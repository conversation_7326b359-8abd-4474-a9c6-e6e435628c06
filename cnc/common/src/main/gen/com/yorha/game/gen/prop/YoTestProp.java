package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.YoTest.YoTestEntity;
import com.yorha.proto.Basic;
import com.yorha.proto.YoTest;
import com.yorha.proto.YoTestPB.YoTestEntityPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.YoTestPB;


/**
 * <AUTHOR> auto gen
 */
public class YoTestProp extends AbstractPropNode {

    public static final int FIELD_INDEX_INTFIELD = 0;
    public static final int FIELD_INDEX_LONGFIELD = 1;
    public static final int FIELD_INDEX_STRINGFIELD = 2;
    public static final int FIELD_INDEX_BOOLFIELD = 3;
    public static final int FIELD_INDEX_USELESS1 = 4;
    public static final int FIELD_INDEX_USELESS2 = 5;
    public static final int FIELD_INDEX_USELESS3 = 6;
    public static final int FIELD_INDEX_USELESS4 = 7;
    public static final int FIELD_INDEX_USELESS5 = 8;
    public static final int FIELD_INDEX_USELESS6 = 9;
    public static final int FIELD_INDEX_USELESS7 = 10;
    public static final int FIELD_INDEX_USELESS8 = 11;
    public static final int FIELD_INDEX_USELESS9 = 12;
    public static final int FIELD_INDEX_USELESS10 = 13;
    public static final int FIELD_INDEX_USELESS11 = 14;
    public static final int FIELD_INDEX_USELESS12 = 15;
    public static final int FIELD_INDEX_USELESS13 = 16;
    public static final int FIELD_INDEX_USELESS14 = 17;
    public static final int FIELD_INDEX_USELESS15 = 18;
    public static final int FIELD_INDEX_USELESS16 = 19;
    public static final int FIELD_INDEX_USELESS17 = 20;
    public static final int FIELD_INDEX_USELESS18 = 21;
    public static final int FIELD_INDEX_USELESS19 = 22;
    public static final int FIELD_INDEX_USELESS20 = 23;
    public static final int FIELD_INDEX_USELESS21 = 24;
    public static final int FIELD_INDEX_USELESS22 = 25;
    public static final int FIELD_INDEX_USELESS23 = 26;
    public static final int FIELD_INDEX_USELESS24 = 27;
    public static final int FIELD_INDEX_USELESS25 = 28;
    public static final int FIELD_INDEX_USELESS26 = 29;
    public static final int FIELD_INDEX_USELESS27 = 30;
    public static final int FIELD_INDEX_USELESS28 = 31;
    public static final int FIELD_INDEX_USELESS29 = 32;
    public static final int FIELD_INDEX_USELESS30 = 33;
    public static final int FIELD_INDEX_USELESS31 = 34;
    public static final int FIELD_INDEX_USELESS32 = 35;
    public static final int FIELD_INDEX_USELESS33 = 36;
    public static final int FIELD_INDEX_USELESS34 = 37;
    public static final int FIELD_INDEX_USELESS35 = 38;
    public static final int FIELD_INDEX_USELESS36 = 39;
    public static final int FIELD_INDEX_USELESS37 = 40;
    public static final int FIELD_INDEX_USELESS38 = 41;
    public static final int FIELD_INDEX_USELESS39 = 42;
    public static final int FIELD_INDEX_USELESS40 = 43;
    public static final int FIELD_INDEX_USELESS41 = 44;
    public static final int FIELD_INDEX_USELESS42 = 45;
    public static final int FIELD_INDEX_USELESS43 = 46;
    public static final int FIELD_INDEX_USELESS44 = 47;
    public static final int FIELD_INDEX_USELESS45 = 48;
    public static final int FIELD_INDEX_USELESS46 = 49;
    public static final int FIELD_INDEX_USELESS47 = 50;
    public static final int FIELD_INDEX_USELESS48 = 51;
    public static final int FIELD_INDEX_USELESS49 = 52;
    public static final int FIELD_INDEX_USELESS50 = 53;
    public static final int FIELD_INDEX_USELESS51 = 54;
    public static final int FIELD_INDEX_USELESS52 = 55;
    public static final int FIELD_INDEX_USELESS53 = 56;
    public static final int FIELD_INDEX_USELESS54 = 57;
    public static final int FIELD_INDEX_USELESS55 = 58;
    public static final int FIELD_INDEX_USELESS56 = 59;
    public static final int FIELD_INDEX_USELESS57 = 60;
    public static final int FIELD_INDEX_USELESS58 = 61;
    public static final int FIELD_INDEX_USELESS59 = 62;
    public static final int FIELD_INDEX_USELESS60 = 63;
    public static final int FIELD_INDEX_USELESS61 = 64;
    public static final int FIELD_INDEX_USELESS62 = 65;
    public static final int FIELD_INDEX_USELESS63 = 66;
    public static final int FIELD_INDEX_USELESS64 = 67;
    public static final int FIELD_INDEX_MAPFIELD = 68;
    public static final int FIELD_INDEX_LISTFIELD = 69;
    public static final int FIELD_INDEX_USELESS101 = 70;
    public static final int FIELD_INDEX_USELESS102 = 71;
    public static final int FIELD_INDEX_USELESS103 = 72;
    public static final int FIELD_INDEX_USELESS104 = 73;
    public static final int FIELD_INDEX_USELESS105 = 74;
    public static final int FIELD_INDEX_USELESS106 = 75;
    public static final int FIELD_INDEX_USELESS107 = 76;
    public static final int FIELD_INDEX_USELESS108 = 77;
    public static final int FIELD_INDEX_USELESS109 = 78;
    public static final int FIELD_INDEX_USELESS110 = 79;
    public static final int FIELD_INDEX_USELESS111 = 80;
    public static final int FIELD_INDEX_USELESS112 = 81;
    public static final int FIELD_INDEX_USELESS113 = 82;
    public static final int FIELD_INDEX_USELESS114 = 83;
    public static final int FIELD_INDEX_USELESS115 = 84;
    public static final int FIELD_INDEX_USELESS116 = 85;
    public static final int FIELD_INDEX_USELESS117 = 86;
    public static final int FIELD_INDEX_USELESS118 = 87;
    public static final int FIELD_INDEX_USELESS119 = 88;
    public static final int FIELD_INDEX_USELESS120 = 89;
    public static final int FIELD_INDEX_USELESS121 = 90;
    public static final int FIELD_INDEX_USELESS122 = 91;
    public static final int FIELD_INDEX_USELESS123 = 92;
    public static final int FIELD_INDEX_USELESS124 = 93;
    public static final int FIELD_INDEX_USELESS125 = 94;
    public static final int FIELD_INDEX_USELESS126 = 95;
    public static final int FIELD_INDEX_USELESS127 = 96;
    public static final int FIELD_INDEX_USELESS128 = 97;
    public static final int FIELD_INDEX_USELESS129 = 98;
    public static final int FIELD_INDEX_USELESS130 = 99;
    public static final int FIELD_INDEX_USELESS131 = 100;
    public static final int FIELD_INDEX_USELESS132 = 101;
    public static final int FIELD_INDEX_USELESS133 = 102;
    public static final int FIELD_INDEX_USELESS134 = 103;
    public static final int FIELD_INDEX_USELESS135 = 104;
    public static final int FIELD_INDEX_USELESS136 = 105;
    public static final int FIELD_INDEX_USELESS137 = 106;
    public static final int FIELD_INDEX_USELESS138 = 107;
    public static final int FIELD_INDEX_USELESS139 = 108;
    public static final int FIELD_INDEX_USELESS140 = 109;
    public static final int FIELD_INDEX_USELESS141 = 110;
    public static final int FIELD_INDEX_USELESS142 = 111;
    public static final int FIELD_INDEX_USELESS143 = 112;
    public static final int FIELD_INDEX_USELESS144 = 113;
    public static final int FIELD_INDEX_USELESS145 = 114;
    public static final int FIELD_INDEX_USELESS146 = 115;
    public static final int FIELD_INDEX_USELESS147 = 116;
    public static final int FIELD_INDEX_USELESS148 = 117;
    public static final int FIELD_INDEX_USELESS149 = 118;
    public static final int FIELD_INDEX_USELESS150 = 119;
    public static final int FIELD_INDEX_USELESS151 = 120;
    public static final int FIELD_INDEX_USELESS152 = 121;
    public static final int FIELD_INDEX_USELESS153 = 122;
    public static final int FIELD_INDEX_USELESS154 = 123;
    public static final int FIELD_INDEX_USELESS155 = 124;
    public static final int FIELD_INDEX_USELESS156 = 125;
    public static final int FIELD_INDEX_USELESS157 = 126;
    public static final int FIELD_INDEX_USELESS158 = 127;
    public static final int FIELD_INDEX_USELESS159 = 128;
    public static final int FIELD_INDEX_USELESS160 = 129;
    public static final int FIELD_INDEX_USELESS161 = 130;
    public static final int FIELD_INDEX_USELESS162 = 131;
    public static final int FIELD_INDEX_USELESS163 = 132;
    public static final int FIELD_INDEX_USELESS164 = 133;
    public static final int FIELD_INDEX_YOTESTMODEL = 134;
    public static final int FIELD_INDEX_YOTESTUNIT = 135;
    public static final int FIELD_INDEX_SETFIELD = 136;

    public static final int FIELD_COUNT = 137;

    private long markBits0 = 0L;
    private long markBits1 = 0L;
    private long markBits2 = 0L;
    private PropertyChangeListener listener;

    private int intField = Constant.DEFAULT_INT_VALUE;
    private long longField = Constant.DEFAULT_LONG_VALUE;
    private String stringField = Constant.DEFAULT_STR_VALUE;
    private boolean boolField = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless1 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless2 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless3 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless4 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless5 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless6 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless7 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless8 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless9 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless10 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless11 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless12 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless13 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless14 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless15 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless16 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless17 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless18 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless19 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless20 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless21 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless22 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless23 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless24 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless25 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless26 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless27 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless28 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless29 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless30 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless31 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless32 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless33 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless34 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless35 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless36 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless37 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless38 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless39 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless40 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless41 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless42 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless43 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless44 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless45 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless46 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless47 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless48 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless49 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless50 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless51 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless52 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless53 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless54 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless55 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless56 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless57 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless58 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless59 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless60 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless61 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless62 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless63 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless64 = Constant.DEFAULT_BOOLEAN_VALUE;
    private Int64YoTestMapMapProp mapField = null;
    private YoTestListListProp listField = null;
    private boolean useless101 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless102 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless103 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless104 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless105 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless106 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless107 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless108 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless109 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless110 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless111 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless112 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless113 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless114 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless115 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless116 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless117 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless118 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless119 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless120 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless121 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless122 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless123 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless124 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless125 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless126 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless127 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless128 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless129 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless130 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless131 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless132 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless133 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless134 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless135 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless136 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless137 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless138 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless139 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless140 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless141 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless142 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless143 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless144 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless145 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless146 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless147 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless148 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless149 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless150 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless151 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless152 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless153 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless154 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless155 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless156 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless157 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless158 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless159 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless160 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless161 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless162 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless163 = Constant.DEFAULT_BOOLEAN_VALUE;
    private boolean useless164 = Constant.DEFAULT_BOOLEAN_VALUE;
    private YoTestModelProp yoTestModel = null;
    private YoTestUnitProp yoTestUnit = null;
    private Int32SetProp setField = null;

    public YoTestProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoTestProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get intField
     *
     * @return intField value
     */
    public int getIntField() {
        return this.intField;
    }

    /**
     * set intField && set marked
     *
     * @param intField new value
     * @return current object
     */
    public YoTestProp setIntField(int intField) {
        if (this.intField != intField) {
            this.mark(FIELD_INDEX_INTFIELD);
            this.intField = intField;
        }
        return this;
    }

    /**
     * inner set intField
     *
     * @param intField new value
     */
    private void innerSetIntField(int intField) {
        this.intField = intField;
    }

    /**
     * get longField
     *
     * @return longField value
     */
    public long getLongField() {
        return this.longField;
    }

    /**
     * set longField && set marked
     *
     * @param longField new value
     * @return current object
     */
    public YoTestProp setLongField(long longField) {
        if (this.longField != longField) {
            this.mark(FIELD_INDEX_LONGFIELD);
            this.longField = longField;
        }
        return this;
    }

    /**
     * inner set longField
     *
     * @param longField new value
     */
    private void innerSetLongField(long longField) {
        this.longField = longField;
    }

    /**
     * get stringField
     *
     * @return stringField value
     */
    public String getStringField() {
        return this.stringField;
    }

    /**
     * set stringField && set marked
     *
     * @param stringField new value
     * @return current object
     */
    public YoTestProp setStringField(String stringField) {
        if (stringField == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.stringField, stringField)) {
            this.mark(FIELD_INDEX_STRINGFIELD);
            this.stringField = stringField;
        }
        return this;
    }

    /**
     * inner set stringField
     *
     * @param stringField new value
     */
    private void innerSetStringField(String stringField) {
        this.stringField = stringField;
    }

    /**
     * get boolField
     *
     * @return boolField value
     */
    public boolean getBoolField() {
        return this.boolField;
    }

    /**
     * set boolField && set marked
     *
     * @param boolField new value
     * @return current object
     */
    public YoTestProp setBoolField(boolean boolField) {
        if (this.boolField != boolField) {
            this.mark(FIELD_INDEX_BOOLFIELD);
            this.boolField = boolField;
        }
        return this;
    }

    /**
     * inner set boolField
     *
     * @param boolField new value
     */
    private void innerSetBoolField(boolean boolField) {
        this.boolField = boolField;
    }

    /**
     * get useless1
     *
     * @return useless1 value
     */
    public boolean getUseless1() {
        return this.useless1;
    }

    /**
     * set useless1 && set marked
     *
     * @param useless1 new value
     * @return current object
     */
    public YoTestProp setUseless1(boolean useless1) {
        if (this.useless1 != useless1) {
            this.mark(FIELD_INDEX_USELESS1);
            this.useless1 = useless1;
        }
        return this;
    }

    /**
     * inner set useless1
     *
     * @param useless1 new value
     */
    private void innerSetUseless1(boolean useless1) {
        this.useless1 = useless1;
    }

    /**
     * get useless2
     *
     * @return useless2 value
     */
    public boolean getUseless2() {
        return this.useless2;
    }

    /**
     * set useless2 && set marked
     *
     * @param useless2 new value
     * @return current object
     */
    public YoTestProp setUseless2(boolean useless2) {
        if (this.useless2 != useless2) {
            this.mark(FIELD_INDEX_USELESS2);
            this.useless2 = useless2;
        }
        return this;
    }

    /**
     * inner set useless2
     *
     * @param useless2 new value
     */
    private void innerSetUseless2(boolean useless2) {
        this.useless2 = useless2;
    }

    /**
     * get useless3
     *
     * @return useless3 value
     */
    public boolean getUseless3() {
        return this.useless3;
    }

    /**
     * set useless3 && set marked
     *
     * @param useless3 new value
     * @return current object
     */
    public YoTestProp setUseless3(boolean useless3) {
        if (this.useless3 != useless3) {
            this.mark(FIELD_INDEX_USELESS3);
            this.useless3 = useless3;
        }
        return this;
    }

    /**
     * inner set useless3
     *
     * @param useless3 new value
     */
    private void innerSetUseless3(boolean useless3) {
        this.useless3 = useless3;
    }

    /**
     * get useless4
     *
     * @return useless4 value
     */
    public boolean getUseless4() {
        return this.useless4;
    }

    /**
     * set useless4 && set marked
     *
     * @param useless4 new value
     * @return current object
     */
    public YoTestProp setUseless4(boolean useless4) {
        if (this.useless4 != useless4) {
            this.mark(FIELD_INDEX_USELESS4);
            this.useless4 = useless4;
        }
        return this;
    }

    /**
     * inner set useless4
     *
     * @param useless4 new value
     */
    private void innerSetUseless4(boolean useless4) {
        this.useless4 = useless4;
    }

    /**
     * get useless5
     *
     * @return useless5 value
     */
    public boolean getUseless5() {
        return this.useless5;
    }

    /**
     * set useless5 && set marked
     *
     * @param useless5 new value
     * @return current object
     */
    public YoTestProp setUseless5(boolean useless5) {
        if (this.useless5 != useless5) {
            this.mark(FIELD_INDEX_USELESS5);
            this.useless5 = useless5;
        }
        return this;
    }

    /**
     * inner set useless5
     *
     * @param useless5 new value
     */
    private void innerSetUseless5(boolean useless5) {
        this.useless5 = useless5;
    }

    /**
     * get useless6
     *
     * @return useless6 value
     */
    public boolean getUseless6() {
        return this.useless6;
    }

    /**
     * set useless6 && set marked
     *
     * @param useless6 new value
     * @return current object
     */
    public YoTestProp setUseless6(boolean useless6) {
        if (this.useless6 != useless6) {
            this.mark(FIELD_INDEX_USELESS6);
            this.useless6 = useless6;
        }
        return this;
    }

    /**
     * inner set useless6
     *
     * @param useless6 new value
     */
    private void innerSetUseless6(boolean useless6) {
        this.useless6 = useless6;
    }

    /**
     * get useless7
     *
     * @return useless7 value
     */
    public boolean getUseless7() {
        return this.useless7;
    }

    /**
     * set useless7 && set marked
     *
     * @param useless7 new value
     * @return current object
     */
    public YoTestProp setUseless7(boolean useless7) {
        if (this.useless7 != useless7) {
            this.mark(FIELD_INDEX_USELESS7);
            this.useless7 = useless7;
        }
        return this;
    }

    /**
     * inner set useless7
     *
     * @param useless7 new value
     */
    private void innerSetUseless7(boolean useless7) {
        this.useless7 = useless7;
    }

    /**
     * get useless8
     *
     * @return useless8 value
     */
    public boolean getUseless8() {
        return this.useless8;
    }

    /**
     * set useless8 && set marked
     *
     * @param useless8 new value
     * @return current object
     */
    public YoTestProp setUseless8(boolean useless8) {
        if (this.useless8 != useless8) {
            this.mark(FIELD_INDEX_USELESS8);
            this.useless8 = useless8;
        }
        return this;
    }

    /**
     * inner set useless8
     *
     * @param useless8 new value
     */
    private void innerSetUseless8(boolean useless8) {
        this.useless8 = useless8;
    }

    /**
     * get useless9
     *
     * @return useless9 value
     */
    public boolean getUseless9() {
        return this.useless9;
    }

    /**
     * set useless9 && set marked
     *
     * @param useless9 new value
     * @return current object
     */
    public YoTestProp setUseless9(boolean useless9) {
        if (this.useless9 != useless9) {
            this.mark(FIELD_INDEX_USELESS9);
            this.useless9 = useless9;
        }
        return this;
    }

    /**
     * inner set useless9
     *
     * @param useless9 new value
     */
    private void innerSetUseless9(boolean useless9) {
        this.useless9 = useless9;
    }

    /**
     * get useless10
     *
     * @return useless10 value
     */
    public boolean getUseless10() {
        return this.useless10;
    }

    /**
     * set useless10 && set marked
     *
     * @param useless10 new value
     * @return current object
     */
    public YoTestProp setUseless10(boolean useless10) {
        if (this.useless10 != useless10) {
            this.mark(FIELD_INDEX_USELESS10);
            this.useless10 = useless10;
        }
        return this;
    }

    /**
     * inner set useless10
     *
     * @param useless10 new value
     */
    private void innerSetUseless10(boolean useless10) {
        this.useless10 = useless10;
    }

    /**
     * get useless11
     *
     * @return useless11 value
     */
    public boolean getUseless11() {
        return this.useless11;
    }

    /**
     * set useless11 && set marked
     *
     * @param useless11 new value
     * @return current object
     */
    public YoTestProp setUseless11(boolean useless11) {
        if (this.useless11 != useless11) {
            this.mark(FIELD_INDEX_USELESS11);
            this.useless11 = useless11;
        }
        return this;
    }

    /**
     * inner set useless11
     *
     * @param useless11 new value
     */
    private void innerSetUseless11(boolean useless11) {
        this.useless11 = useless11;
    }

    /**
     * get useless12
     *
     * @return useless12 value
     */
    public boolean getUseless12() {
        return this.useless12;
    }

    /**
     * set useless12 && set marked
     *
     * @param useless12 new value
     * @return current object
     */
    public YoTestProp setUseless12(boolean useless12) {
        if (this.useless12 != useless12) {
            this.mark(FIELD_INDEX_USELESS12);
            this.useless12 = useless12;
        }
        return this;
    }

    /**
     * inner set useless12
     *
     * @param useless12 new value
     */
    private void innerSetUseless12(boolean useless12) {
        this.useless12 = useless12;
    }

    /**
     * get useless13
     *
     * @return useless13 value
     */
    public boolean getUseless13() {
        return this.useless13;
    }

    /**
     * set useless13 && set marked
     *
     * @param useless13 new value
     * @return current object
     */
    public YoTestProp setUseless13(boolean useless13) {
        if (this.useless13 != useless13) {
            this.mark(FIELD_INDEX_USELESS13);
            this.useless13 = useless13;
        }
        return this;
    }

    /**
     * inner set useless13
     *
     * @param useless13 new value
     */
    private void innerSetUseless13(boolean useless13) {
        this.useless13 = useless13;
    }

    /**
     * get useless14
     *
     * @return useless14 value
     */
    public boolean getUseless14() {
        return this.useless14;
    }

    /**
     * set useless14 && set marked
     *
     * @param useless14 new value
     * @return current object
     */
    public YoTestProp setUseless14(boolean useless14) {
        if (this.useless14 != useless14) {
            this.mark(FIELD_INDEX_USELESS14);
            this.useless14 = useless14;
        }
        return this;
    }

    /**
     * inner set useless14
     *
     * @param useless14 new value
     */
    private void innerSetUseless14(boolean useless14) {
        this.useless14 = useless14;
    }

    /**
     * get useless15
     *
     * @return useless15 value
     */
    public boolean getUseless15() {
        return this.useless15;
    }

    /**
     * set useless15 && set marked
     *
     * @param useless15 new value
     * @return current object
     */
    public YoTestProp setUseless15(boolean useless15) {
        if (this.useless15 != useless15) {
            this.mark(FIELD_INDEX_USELESS15);
            this.useless15 = useless15;
        }
        return this;
    }

    /**
     * inner set useless15
     *
     * @param useless15 new value
     */
    private void innerSetUseless15(boolean useless15) {
        this.useless15 = useless15;
    }

    /**
     * get useless16
     *
     * @return useless16 value
     */
    public boolean getUseless16() {
        return this.useless16;
    }

    /**
     * set useless16 && set marked
     *
     * @param useless16 new value
     * @return current object
     */
    public YoTestProp setUseless16(boolean useless16) {
        if (this.useless16 != useless16) {
            this.mark(FIELD_INDEX_USELESS16);
            this.useless16 = useless16;
        }
        return this;
    }

    /**
     * inner set useless16
     *
     * @param useless16 new value
     */
    private void innerSetUseless16(boolean useless16) {
        this.useless16 = useless16;
    }

    /**
     * get useless17
     *
     * @return useless17 value
     */
    public boolean getUseless17() {
        return this.useless17;
    }

    /**
     * set useless17 && set marked
     *
     * @param useless17 new value
     * @return current object
     */
    public YoTestProp setUseless17(boolean useless17) {
        if (this.useless17 != useless17) {
            this.mark(FIELD_INDEX_USELESS17);
            this.useless17 = useless17;
        }
        return this;
    }

    /**
     * inner set useless17
     *
     * @param useless17 new value
     */
    private void innerSetUseless17(boolean useless17) {
        this.useless17 = useless17;
    }

    /**
     * get useless18
     *
     * @return useless18 value
     */
    public boolean getUseless18() {
        return this.useless18;
    }

    /**
     * set useless18 && set marked
     *
     * @param useless18 new value
     * @return current object
     */
    public YoTestProp setUseless18(boolean useless18) {
        if (this.useless18 != useless18) {
            this.mark(FIELD_INDEX_USELESS18);
            this.useless18 = useless18;
        }
        return this;
    }

    /**
     * inner set useless18
     *
     * @param useless18 new value
     */
    private void innerSetUseless18(boolean useless18) {
        this.useless18 = useless18;
    }

    /**
     * get useless19
     *
     * @return useless19 value
     */
    public boolean getUseless19() {
        return this.useless19;
    }

    /**
     * set useless19 && set marked
     *
     * @param useless19 new value
     * @return current object
     */
    public YoTestProp setUseless19(boolean useless19) {
        if (this.useless19 != useless19) {
            this.mark(FIELD_INDEX_USELESS19);
            this.useless19 = useless19;
        }
        return this;
    }

    /**
     * inner set useless19
     *
     * @param useless19 new value
     */
    private void innerSetUseless19(boolean useless19) {
        this.useless19 = useless19;
    }

    /**
     * get useless20
     *
     * @return useless20 value
     */
    public boolean getUseless20() {
        return this.useless20;
    }

    /**
     * set useless20 && set marked
     *
     * @param useless20 new value
     * @return current object
     */
    public YoTestProp setUseless20(boolean useless20) {
        if (this.useless20 != useless20) {
            this.mark(FIELD_INDEX_USELESS20);
            this.useless20 = useless20;
        }
        return this;
    }

    /**
     * inner set useless20
     *
     * @param useless20 new value
     */
    private void innerSetUseless20(boolean useless20) {
        this.useless20 = useless20;
    }

    /**
     * get useless21
     *
     * @return useless21 value
     */
    public boolean getUseless21() {
        return this.useless21;
    }

    /**
     * set useless21 && set marked
     *
     * @param useless21 new value
     * @return current object
     */
    public YoTestProp setUseless21(boolean useless21) {
        if (this.useless21 != useless21) {
            this.mark(FIELD_INDEX_USELESS21);
            this.useless21 = useless21;
        }
        return this;
    }

    /**
     * inner set useless21
     *
     * @param useless21 new value
     */
    private void innerSetUseless21(boolean useless21) {
        this.useless21 = useless21;
    }

    /**
     * get useless22
     *
     * @return useless22 value
     */
    public boolean getUseless22() {
        return this.useless22;
    }

    /**
     * set useless22 && set marked
     *
     * @param useless22 new value
     * @return current object
     */
    public YoTestProp setUseless22(boolean useless22) {
        if (this.useless22 != useless22) {
            this.mark(FIELD_INDEX_USELESS22);
            this.useless22 = useless22;
        }
        return this;
    }

    /**
     * inner set useless22
     *
     * @param useless22 new value
     */
    private void innerSetUseless22(boolean useless22) {
        this.useless22 = useless22;
    }

    /**
     * get useless23
     *
     * @return useless23 value
     */
    public boolean getUseless23() {
        return this.useless23;
    }

    /**
     * set useless23 && set marked
     *
     * @param useless23 new value
     * @return current object
     */
    public YoTestProp setUseless23(boolean useless23) {
        if (this.useless23 != useless23) {
            this.mark(FIELD_INDEX_USELESS23);
            this.useless23 = useless23;
        }
        return this;
    }

    /**
     * inner set useless23
     *
     * @param useless23 new value
     */
    private void innerSetUseless23(boolean useless23) {
        this.useless23 = useless23;
    }

    /**
     * get useless24
     *
     * @return useless24 value
     */
    public boolean getUseless24() {
        return this.useless24;
    }

    /**
     * set useless24 && set marked
     *
     * @param useless24 new value
     * @return current object
     */
    public YoTestProp setUseless24(boolean useless24) {
        if (this.useless24 != useless24) {
            this.mark(FIELD_INDEX_USELESS24);
            this.useless24 = useless24;
        }
        return this;
    }

    /**
     * inner set useless24
     *
     * @param useless24 new value
     */
    private void innerSetUseless24(boolean useless24) {
        this.useless24 = useless24;
    }

    /**
     * get useless25
     *
     * @return useless25 value
     */
    public boolean getUseless25() {
        return this.useless25;
    }

    /**
     * set useless25 && set marked
     *
     * @param useless25 new value
     * @return current object
     */
    public YoTestProp setUseless25(boolean useless25) {
        if (this.useless25 != useless25) {
            this.mark(FIELD_INDEX_USELESS25);
            this.useless25 = useless25;
        }
        return this;
    }

    /**
     * inner set useless25
     *
     * @param useless25 new value
     */
    private void innerSetUseless25(boolean useless25) {
        this.useless25 = useless25;
    }

    /**
     * get useless26
     *
     * @return useless26 value
     */
    public boolean getUseless26() {
        return this.useless26;
    }

    /**
     * set useless26 && set marked
     *
     * @param useless26 new value
     * @return current object
     */
    public YoTestProp setUseless26(boolean useless26) {
        if (this.useless26 != useless26) {
            this.mark(FIELD_INDEX_USELESS26);
            this.useless26 = useless26;
        }
        return this;
    }

    /**
     * inner set useless26
     *
     * @param useless26 new value
     */
    private void innerSetUseless26(boolean useless26) {
        this.useless26 = useless26;
    }

    /**
     * get useless27
     *
     * @return useless27 value
     */
    public boolean getUseless27() {
        return this.useless27;
    }

    /**
     * set useless27 && set marked
     *
     * @param useless27 new value
     * @return current object
     */
    public YoTestProp setUseless27(boolean useless27) {
        if (this.useless27 != useless27) {
            this.mark(FIELD_INDEX_USELESS27);
            this.useless27 = useless27;
        }
        return this;
    }

    /**
     * inner set useless27
     *
     * @param useless27 new value
     */
    private void innerSetUseless27(boolean useless27) {
        this.useless27 = useless27;
    }

    /**
     * get useless28
     *
     * @return useless28 value
     */
    public boolean getUseless28() {
        return this.useless28;
    }

    /**
     * set useless28 && set marked
     *
     * @param useless28 new value
     * @return current object
     */
    public YoTestProp setUseless28(boolean useless28) {
        if (this.useless28 != useless28) {
            this.mark(FIELD_INDEX_USELESS28);
            this.useless28 = useless28;
        }
        return this;
    }

    /**
     * inner set useless28
     *
     * @param useless28 new value
     */
    private void innerSetUseless28(boolean useless28) {
        this.useless28 = useless28;
    }

    /**
     * get useless29
     *
     * @return useless29 value
     */
    public boolean getUseless29() {
        return this.useless29;
    }

    /**
     * set useless29 && set marked
     *
     * @param useless29 new value
     * @return current object
     */
    public YoTestProp setUseless29(boolean useless29) {
        if (this.useless29 != useless29) {
            this.mark(FIELD_INDEX_USELESS29);
            this.useless29 = useless29;
        }
        return this;
    }

    /**
     * inner set useless29
     *
     * @param useless29 new value
     */
    private void innerSetUseless29(boolean useless29) {
        this.useless29 = useless29;
    }

    /**
     * get useless30
     *
     * @return useless30 value
     */
    public boolean getUseless30() {
        return this.useless30;
    }

    /**
     * set useless30 && set marked
     *
     * @param useless30 new value
     * @return current object
     */
    public YoTestProp setUseless30(boolean useless30) {
        if (this.useless30 != useless30) {
            this.mark(FIELD_INDEX_USELESS30);
            this.useless30 = useless30;
        }
        return this;
    }

    /**
     * inner set useless30
     *
     * @param useless30 new value
     */
    private void innerSetUseless30(boolean useless30) {
        this.useless30 = useless30;
    }

    /**
     * get useless31
     *
     * @return useless31 value
     */
    public boolean getUseless31() {
        return this.useless31;
    }

    /**
     * set useless31 && set marked
     *
     * @param useless31 new value
     * @return current object
     */
    public YoTestProp setUseless31(boolean useless31) {
        if (this.useless31 != useless31) {
            this.mark(FIELD_INDEX_USELESS31);
            this.useless31 = useless31;
        }
        return this;
    }

    /**
     * inner set useless31
     *
     * @param useless31 new value
     */
    private void innerSetUseless31(boolean useless31) {
        this.useless31 = useless31;
    }

    /**
     * get useless32
     *
     * @return useless32 value
     */
    public boolean getUseless32() {
        return this.useless32;
    }

    /**
     * set useless32 && set marked
     *
     * @param useless32 new value
     * @return current object
     */
    public YoTestProp setUseless32(boolean useless32) {
        if (this.useless32 != useless32) {
            this.mark(FIELD_INDEX_USELESS32);
            this.useless32 = useless32;
        }
        return this;
    }

    /**
     * inner set useless32
     *
     * @param useless32 new value
     */
    private void innerSetUseless32(boolean useless32) {
        this.useless32 = useless32;
    }

    /**
     * get useless33
     *
     * @return useless33 value
     */
    public boolean getUseless33() {
        return this.useless33;
    }

    /**
     * set useless33 && set marked
     *
     * @param useless33 new value
     * @return current object
     */
    public YoTestProp setUseless33(boolean useless33) {
        if (this.useless33 != useless33) {
            this.mark(FIELD_INDEX_USELESS33);
            this.useless33 = useless33;
        }
        return this;
    }

    /**
     * inner set useless33
     *
     * @param useless33 new value
     */
    private void innerSetUseless33(boolean useless33) {
        this.useless33 = useless33;
    }

    /**
     * get useless34
     *
     * @return useless34 value
     */
    public boolean getUseless34() {
        return this.useless34;
    }

    /**
     * set useless34 && set marked
     *
     * @param useless34 new value
     * @return current object
     */
    public YoTestProp setUseless34(boolean useless34) {
        if (this.useless34 != useless34) {
            this.mark(FIELD_INDEX_USELESS34);
            this.useless34 = useless34;
        }
        return this;
    }

    /**
     * inner set useless34
     *
     * @param useless34 new value
     */
    private void innerSetUseless34(boolean useless34) {
        this.useless34 = useless34;
    }

    /**
     * get useless35
     *
     * @return useless35 value
     */
    public boolean getUseless35() {
        return this.useless35;
    }

    /**
     * set useless35 && set marked
     *
     * @param useless35 new value
     * @return current object
     */
    public YoTestProp setUseless35(boolean useless35) {
        if (this.useless35 != useless35) {
            this.mark(FIELD_INDEX_USELESS35);
            this.useless35 = useless35;
        }
        return this;
    }

    /**
     * inner set useless35
     *
     * @param useless35 new value
     */
    private void innerSetUseless35(boolean useless35) {
        this.useless35 = useless35;
    }

    /**
     * get useless36
     *
     * @return useless36 value
     */
    public boolean getUseless36() {
        return this.useless36;
    }

    /**
     * set useless36 && set marked
     *
     * @param useless36 new value
     * @return current object
     */
    public YoTestProp setUseless36(boolean useless36) {
        if (this.useless36 != useless36) {
            this.mark(FIELD_INDEX_USELESS36);
            this.useless36 = useless36;
        }
        return this;
    }

    /**
     * inner set useless36
     *
     * @param useless36 new value
     */
    private void innerSetUseless36(boolean useless36) {
        this.useless36 = useless36;
    }

    /**
     * get useless37
     *
     * @return useless37 value
     */
    public boolean getUseless37() {
        return this.useless37;
    }

    /**
     * set useless37 && set marked
     *
     * @param useless37 new value
     * @return current object
     */
    public YoTestProp setUseless37(boolean useless37) {
        if (this.useless37 != useless37) {
            this.mark(FIELD_INDEX_USELESS37);
            this.useless37 = useless37;
        }
        return this;
    }

    /**
     * inner set useless37
     *
     * @param useless37 new value
     */
    private void innerSetUseless37(boolean useless37) {
        this.useless37 = useless37;
    }

    /**
     * get useless38
     *
     * @return useless38 value
     */
    public boolean getUseless38() {
        return this.useless38;
    }

    /**
     * set useless38 && set marked
     *
     * @param useless38 new value
     * @return current object
     */
    public YoTestProp setUseless38(boolean useless38) {
        if (this.useless38 != useless38) {
            this.mark(FIELD_INDEX_USELESS38);
            this.useless38 = useless38;
        }
        return this;
    }

    /**
     * inner set useless38
     *
     * @param useless38 new value
     */
    private void innerSetUseless38(boolean useless38) {
        this.useless38 = useless38;
    }

    /**
     * get useless39
     *
     * @return useless39 value
     */
    public boolean getUseless39() {
        return this.useless39;
    }

    /**
     * set useless39 && set marked
     *
     * @param useless39 new value
     * @return current object
     */
    public YoTestProp setUseless39(boolean useless39) {
        if (this.useless39 != useless39) {
            this.mark(FIELD_INDEX_USELESS39);
            this.useless39 = useless39;
        }
        return this;
    }

    /**
     * inner set useless39
     *
     * @param useless39 new value
     */
    private void innerSetUseless39(boolean useless39) {
        this.useless39 = useless39;
    }

    /**
     * get useless40
     *
     * @return useless40 value
     */
    public boolean getUseless40() {
        return this.useless40;
    }

    /**
     * set useless40 && set marked
     *
     * @param useless40 new value
     * @return current object
     */
    public YoTestProp setUseless40(boolean useless40) {
        if (this.useless40 != useless40) {
            this.mark(FIELD_INDEX_USELESS40);
            this.useless40 = useless40;
        }
        return this;
    }

    /**
     * inner set useless40
     *
     * @param useless40 new value
     */
    private void innerSetUseless40(boolean useless40) {
        this.useless40 = useless40;
    }

    /**
     * get useless41
     *
     * @return useless41 value
     */
    public boolean getUseless41() {
        return this.useless41;
    }

    /**
     * set useless41 && set marked
     *
     * @param useless41 new value
     * @return current object
     */
    public YoTestProp setUseless41(boolean useless41) {
        if (this.useless41 != useless41) {
            this.mark(FIELD_INDEX_USELESS41);
            this.useless41 = useless41;
        }
        return this;
    }

    /**
     * inner set useless41
     *
     * @param useless41 new value
     */
    private void innerSetUseless41(boolean useless41) {
        this.useless41 = useless41;
    }

    /**
     * get useless42
     *
     * @return useless42 value
     */
    public boolean getUseless42() {
        return this.useless42;
    }

    /**
     * set useless42 && set marked
     *
     * @param useless42 new value
     * @return current object
     */
    public YoTestProp setUseless42(boolean useless42) {
        if (this.useless42 != useless42) {
            this.mark(FIELD_INDEX_USELESS42);
            this.useless42 = useless42;
        }
        return this;
    }

    /**
     * inner set useless42
     *
     * @param useless42 new value
     */
    private void innerSetUseless42(boolean useless42) {
        this.useless42 = useless42;
    }

    /**
     * get useless43
     *
     * @return useless43 value
     */
    public boolean getUseless43() {
        return this.useless43;
    }

    /**
     * set useless43 && set marked
     *
     * @param useless43 new value
     * @return current object
     */
    public YoTestProp setUseless43(boolean useless43) {
        if (this.useless43 != useless43) {
            this.mark(FIELD_INDEX_USELESS43);
            this.useless43 = useless43;
        }
        return this;
    }

    /**
     * inner set useless43
     *
     * @param useless43 new value
     */
    private void innerSetUseless43(boolean useless43) {
        this.useless43 = useless43;
    }

    /**
     * get useless44
     *
     * @return useless44 value
     */
    public boolean getUseless44() {
        return this.useless44;
    }

    /**
     * set useless44 && set marked
     *
     * @param useless44 new value
     * @return current object
     */
    public YoTestProp setUseless44(boolean useless44) {
        if (this.useless44 != useless44) {
            this.mark(FIELD_INDEX_USELESS44);
            this.useless44 = useless44;
        }
        return this;
    }

    /**
     * inner set useless44
     *
     * @param useless44 new value
     */
    private void innerSetUseless44(boolean useless44) {
        this.useless44 = useless44;
    }

    /**
     * get useless45
     *
     * @return useless45 value
     */
    public boolean getUseless45() {
        return this.useless45;
    }

    /**
     * set useless45 && set marked
     *
     * @param useless45 new value
     * @return current object
     */
    public YoTestProp setUseless45(boolean useless45) {
        if (this.useless45 != useless45) {
            this.mark(FIELD_INDEX_USELESS45);
            this.useless45 = useless45;
        }
        return this;
    }

    /**
     * inner set useless45
     *
     * @param useless45 new value
     */
    private void innerSetUseless45(boolean useless45) {
        this.useless45 = useless45;
    }

    /**
     * get useless46
     *
     * @return useless46 value
     */
    public boolean getUseless46() {
        return this.useless46;
    }

    /**
     * set useless46 && set marked
     *
     * @param useless46 new value
     * @return current object
     */
    public YoTestProp setUseless46(boolean useless46) {
        if (this.useless46 != useless46) {
            this.mark(FIELD_INDEX_USELESS46);
            this.useless46 = useless46;
        }
        return this;
    }

    /**
     * inner set useless46
     *
     * @param useless46 new value
     */
    private void innerSetUseless46(boolean useless46) {
        this.useless46 = useless46;
    }

    /**
     * get useless47
     *
     * @return useless47 value
     */
    public boolean getUseless47() {
        return this.useless47;
    }

    /**
     * set useless47 && set marked
     *
     * @param useless47 new value
     * @return current object
     */
    public YoTestProp setUseless47(boolean useless47) {
        if (this.useless47 != useless47) {
            this.mark(FIELD_INDEX_USELESS47);
            this.useless47 = useless47;
        }
        return this;
    }

    /**
     * inner set useless47
     *
     * @param useless47 new value
     */
    private void innerSetUseless47(boolean useless47) {
        this.useless47 = useless47;
    }

    /**
     * get useless48
     *
     * @return useless48 value
     */
    public boolean getUseless48() {
        return this.useless48;
    }

    /**
     * set useless48 && set marked
     *
     * @param useless48 new value
     * @return current object
     */
    public YoTestProp setUseless48(boolean useless48) {
        if (this.useless48 != useless48) {
            this.mark(FIELD_INDEX_USELESS48);
            this.useless48 = useless48;
        }
        return this;
    }

    /**
     * inner set useless48
     *
     * @param useless48 new value
     */
    private void innerSetUseless48(boolean useless48) {
        this.useless48 = useless48;
    }

    /**
     * get useless49
     *
     * @return useless49 value
     */
    public boolean getUseless49() {
        return this.useless49;
    }

    /**
     * set useless49 && set marked
     *
     * @param useless49 new value
     * @return current object
     */
    public YoTestProp setUseless49(boolean useless49) {
        if (this.useless49 != useless49) {
            this.mark(FIELD_INDEX_USELESS49);
            this.useless49 = useless49;
        }
        return this;
    }

    /**
     * inner set useless49
     *
     * @param useless49 new value
     */
    private void innerSetUseless49(boolean useless49) {
        this.useless49 = useless49;
    }

    /**
     * get useless50
     *
     * @return useless50 value
     */
    public boolean getUseless50() {
        return this.useless50;
    }

    /**
     * set useless50 && set marked
     *
     * @param useless50 new value
     * @return current object
     */
    public YoTestProp setUseless50(boolean useless50) {
        if (this.useless50 != useless50) {
            this.mark(FIELD_INDEX_USELESS50);
            this.useless50 = useless50;
        }
        return this;
    }

    /**
     * inner set useless50
     *
     * @param useless50 new value
     */
    private void innerSetUseless50(boolean useless50) {
        this.useless50 = useless50;
    }

    /**
     * get useless51
     *
     * @return useless51 value
     */
    public boolean getUseless51() {
        return this.useless51;
    }

    /**
     * set useless51 && set marked
     *
     * @param useless51 new value
     * @return current object
     */
    public YoTestProp setUseless51(boolean useless51) {
        if (this.useless51 != useless51) {
            this.mark(FIELD_INDEX_USELESS51);
            this.useless51 = useless51;
        }
        return this;
    }

    /**
     * inner set useless51
     *
     * @param useless51 new value
     */
    private void innerSetUseless51(boolean useless51) {
        this.useless51 = useless51;
    }

    /**
     * get useless52
     *
     * @return useless52 value
     */
    public boolean getUseless52() {
        return this.useless52;
    }

    /**
     * set useless52 && set marked
     *
     * @param useless52 new value
     * @return current object
     */
    public YoTestProp setUseless52(boolean useless52) {
        if (this.useless52 != useless52) {
            this.mark(FIELD_INDEX_USELESS52);
            this.useless52 = useless52;
        }
        return this;
    }

    /**
     * inner set useless52
     *
     * @param useless52 new value
     */
    private void innerSetUseless52(boolean useless52) {
        this.useless52 = useless52;
    }

    /**
     * get useless53
     *
     * @return useless53 value
     */
    public boolean getUseless53() {
        return this.useless53;
    }

    /**
     * set useless53 && set marked
     *
     * @param useless53 new value
     * @return current object
     */
    public YoTestProp setUseless53(boolean useless53) {
        if (this.useless53 != useless53) {
            this.mark(FIELD_INDEX_USELESS53);
            this.useless53 = useless53;
        }
        return this;
    }

    /**
     * inner set useless53
     *
     * @param useless53 new value
     */
    private void innerSetUseless53(boolean useless53) {
        this.useless53 = useless53;
    }

    /**
     * get useless54
     *
     * @return useless54 value
     */
    public boolean getUseless54() {
        return this.useless54;
    }

    /**
     * set useless54 && set marked
     *
     * @param useless54 new value
     * @return current object
     */
    public YoTestProp setUseless54(boolean useless54) {
        if (this.useless54 != useless54) {
            this.mark(FIELD_INDEX_USELESS54);
            this.useless54 = useless54;
        }
        return this;
    }

    /**
     * inner set useless54
     *
     * @param useless54 new value
     */
    private void innerSetUseless54(boolean useless54) {
        this.useless54 = useless54;
    }

    /**
     * get useless55
     *
     * @return useless55 value
     */
    public boolean getUseless55() {
        return this.useless55;
    }

    /**
     * set useless55 && set marked
     *
     * @param useless55 new value
     * @return current object
     */
    public YoTestProp setUseless55(boolean useless55) {
        if (this.useless55 != useless55) {
            this.mark(FIELD_INDEX_USELESS55);
            this.useless55 = useless55;
        }
        return this;
    }

    /**
     * inner set useless55
     *
     * @param useless55 new value
     */
    private void innerSetUseless55(boolean useless55) {
        this.useless55 = useless55;
    }

    /**
     * get useless56
     *
     * @return useless56 value
     */
    public boolean getUseless56() {
        return this.useless56;
    }

    /**
     * set useless56 && set marked
     *
     * @param useless56 new value
     * @return current object
     */
    public YoTestProp setUseless56(boolean useless56) {
        if (this.useless56 != useless56) {
            this.mark(FIELD_INDEX_USELESS56);
            this.useless56 = useless56;
        }
        return this;
    }

    /**
     * inner set useless56
     *
     * @param useless56 new value
     */
    private void innerSetUseless56(boolean useless56) {
        this.useless56 = useless56;
    }

    /**
     * get useless57
     *
     * @return useless57 value
     */
    public boolean getUseless57() {
        return this.useless57;
    }

    /**
     * set useless57 && set marked
     *
     * @param useless57 new value
     * @return current object
     */
    public YoTestProp setUseless57(boolean useless57) {
        if (this.useless57 != useless57) {
            this.mark(FIELD_INDEX_USELESS57);
            this.useless57 = useless57;
        }
        return this;
    }

    /**
     * inner set useless57
     *
     * @param useless57 new value
     */
    private void innerSetUseless57(boolean useless57) {
        this.useless57 = useless57;
    }

    /**
     * get useless58
     *
     * @return useless58 value
     */
    public boolean getUseless58() {
        return this.useless58;
    }

    /**
     * set useless58 && set marked
     *
     * @param useless58 new value
     * @return current object
     */
    public YoTestProp setUseless58(boolean useless58) {
        if (this.useless58 != useless58) {
            this.mark(FIELD_INDEX_USELESS58);
            this.useless58 = useless58;
        }
        return this;
    }

    /**
     * inner set useless58
     *
     * @param useless58 new value
     */
    private void innerSetUseless58(boolean useless58) {
        this.useless58 = useless58;
    }

    /**
     * get useless59
     *
     * @return useless59 value
     */
    public boolean getUseless59() {
        return this.useless59;
    }

    /**
     * set useless59 && set marked
     *
     * @param useless59 new value
     * @return current object
     */
    public YoTestProp setUseless59(boolean useless59) {
        if (this.useless59 != useless59) {
            this.mark(FIELD_INDEX_USELESS59);
            this.useless59 = useless59;
        }
        return this;
    }

    /**
     * inner set useless59
     *
     * @param useless59 new value
     */
    private void innerSetUseless59(boolean useless59) {
        this.useless59 = useless59;
    }

    /**
     * get useless60
     *
     * @return useless60 value
     */
    public boolean getUseless60() {
        return this.useless60;
    }

    /**
     * set useless60 && set marked
     *
     * @param useless60 new value
     * @return current object
     */
    public YoTestProp setUseless60(boolean useless60) {
        if (this.useless60 != useless60) {
            this.mark(FIELD_INDEX_USELESS60);
            this.useless60 = useless60;
        }
        return this;
    }

    /**
     * inner set useless60
     *
     * @param useless60 new value
     */
    private void innerSetUseless60(boolean useless60) {
        this.useless60 = useless60;
    }

    /**
     * get useless61
     *
     * @return useless61 value
     */
    public boolean getUseless61() {
        return this.useless61;
    }

    /**
     * set useless61 && set marked
     *
     * @param useless61 new value
     * @return current object
     */
    public YoTestProp setUseless61(boolean useless61) {
        if (this.useless61 != useless61) {
            this.mark(FIELD_INDEX_USELESS61);
            this.useless61 = useless61;
        }
        return this;
    }

    /**
     * inner set useless61
     *
     * @param useless61 new value
     */
    private void innerSetUseless61(boolean useless61) {
        this.useless61 = useless61;
    }

    /**
     * get useless62
     *
     * @return useless62 value
     */
    public boolean getUseless62() {
        return this.useless62;
    }

    /**
     * set useless62 && set marked
     *
     * @param useless62 new value
     * @return current object
     */
    public YoTestProp setUseless62(boolean useless62) {
        if (this.useless62 != useless62) {
            this.mark(FIELD_INDEX_USELESS62);
            this.useless62 = useless62;
        }
        return this;
    }

    /**
     * inner set useless62
     *
     * @param useless62 new value
     */
    private void innerSetUseless62(boolean useless62) {
        this.useless62 = useless62;
    }

    /**
     * get useless63
     *
     * @return useless63 value
     */
    public boolean getUseless63() {
        return this.useless63;
    }

    /**
     * set useless63 && set marked
     *
     * @param useless63 new value
     * @return current object
     */
    public YoTestProp setUseless63(boolean useless63) {
        if (this.useless63 != useless63) {
            this.mark(FIELD_INDEX_USELESS63);
            this.useless63 = useless63;
        }
        return this;
    }

    /**
     * inner set useless63
     *
     * @param useless63 new value
     */
    private void innerSetUseless63(boolean useless63) {
        this.useless63 = useless63;
    }

    /**
     * get useless64
     *
     * @return useless64 value
     */
    public boolean getUseless64() {
        return this.useless64;
    }

    /**
     * set useless64 && set marked
     *
     * @param useless64 new value
     * @return current object
     */
    public YoTestProp setUseless64(boolean useless64) {
        if (this.useless64 != useless64) {
            this.mark(FIELD_INDEX_USELESS64);
            this.useless64 = useless64;
        }
        return this;
    }

    /**
     * inner set useless64
     *
     * @param useless64 new value
     */
    private void innerSetUseless64(boolean useless64) {
        this.useless64 = useless64;
    }

    /**
     * get mapField
     *
     * @return mapField value
     */
    public Int64YoTestMapMapProp getMapField() {
        if (this.mapField == null) {
            this.mapField = new Int64YoTestMapMapProp(this, FIELD_INDEX_MAPFIELD);
        }
        return this.mapField;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putMapFieldV(YoTestMapProp v) {
        this.getMapField().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public YoTestMapProp addEmptyMapField(Long k) {
        return this.getMapField().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getMapFieldSize() {
        if (this.mapField == null) {
            return 0;
        }
        return this.mapField.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isMapFieldEmpty() {
        if (this.mapField == null) {
            return true;
        }
        return this.mapField.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public YoTestMapProp getMapFieldV(Long k) {
        if (this.mapField == null || !this.mapField.containsKey(k)) {
            return null;
        }
        return this.mapField.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearMapField() {
        if (this.mapField != null) {
            this.mapField.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeMapFieldV(Long k) {
        if (this.mapField != null) {
            this.mapField.remove(k);
        }
    }
    /**
     * get listField
     *
     * @return listField value
     */
    public YoTestListListProp getListField() {
        if (this.listField == null) {
            this.listField = new YoTestListListProp(this, FIELD_INDEX_LISTFIELD);
        }
        return this.listField;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addListField(YoTestListProp v) {
        this.getListField().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public YoTestListProp getListFieldIndex(int index) {
        return this.getListField().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public YoTestListProp removeListField(YoTestListProp v) {
        if (this.listField == null) {
            return null;
        }
        if(this.listField.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getListFieldSize() {
        if (this.listField == null) {
            return 0;
        }
        return this.listField.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isListFieldEmpty() {
        if (this.listField == null) {
            return true;
        }
        return this.getListField().isEmpty();
    }

    /**
     * clear list
     */
    public void clearListField() {
        this.getListField().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public YoTestListProp removeListFieldIndex(int index) {
        return this.getListField().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public YoTestListProp setListFieldIndex(int index, YoTestListProp v) {
        return this.getListField().set(index, v);
    }
    /**
     * get useless101
     *
     * @return useless101 value
     */
    public boolean getUseless101() {
        return this.useless101;
    }

    /**
     * set useless101 && set marked
     *
     * @param useless101 new value
     * @return current object
     */
    public YoTestProp setUseless101(boolean useless101) {
        if (this.useless101 != useless101) {
            this.mark(FIELD_INDEX_USELESS101);
            this.useless101 = useless101;
        }
        return this;
    }

    /**
     * inner set useless101
     *
     * @param useless101 new value
     */
    private void innerSetUseless101(boolean useless101) {
        this.useless101 = useless101;
    }

    /**
     * get useless102
     *
     * @return useless102 value
     */
    public boolean getUseless102() {
        return this.useless102;
    }

    /**
     * set useless102 && set marked
     *
     * @param useless102 new value
     * @return current object
     */
    public YoTestProp setUseless102(boolean useless102) {
        if (this.useless102 != useless102) {
            this.mark(FIELD_INDEX_USELESS102);
            this.useless102 = useless102;
        }
        return this;
    }

    /**
     * inner set useless102
     *
     * @param useless102 new value
     */
    private void innerSetUseless102(boolean useless102) {
        this.useless102 = useless102;
    }

    /**
     * get useless103
     *
     * @return useless103 value
     */
    public boolean getUseless103() {
        return this.useless103;
    }

    /**
     * set useless103 && set marked
     *
     * @param useless103 new value
     * @return current object
     */
    public YoTestProp setUseless103(boolean useless103) {
        if (this.useless103 != useless103) {
            this.mark(FIELD_INDEX_USELESS103);
            this.useless103 = useless103;
        }
        return this;
    }

    /**
     * inner set useless103
     *
     * @param useless103 new value
     */
    private void innerSetUseless103(boolean useless103) {
        this.useless103 = useless103;
    }

    /**
     * get useless104
     *
     * @return useless104 value
     */
    public boolean getUseless104() {
        return this.useless104;
    }

    /**
     * set useless104 && set marked
     *
     * @param useless104 new value
     * @return current object
     */
    public YoTestProp setUseless104(boolean useless104) {
        if (this.useless104 != useless104) {
            this.mark(FIELD_INDEX_USELESS104);
            this.useless104 = useless104;
        }
        return this;
    }

    /**
     * inner set useless104
     *
     * @param useless104 new value
     */
    private void innerSetUseless104(boolean useless104) {
        this.useless104 = useless104;
    }

    /**
     * get useless105
     *
     * @return useless105 value
     */
    public boolean getUseless105() {
        return this.useless105;
    }

    /**
     * set useless105 && set marked
     *
     * @param useless105 new value
     * @return current object
     */
    public YoTestProp setUseless105(boolean useless105) {
        if (this.useless105 != useless105) {
            this.mark(FIELD_INDEX_USELESS105);
            this.useless105 = useless105;
        }
        return this;
    }

    /**
     * inner set useless105
     *
     * @param useless105 new value
     */
    private void innerSetUseless105(boolean useless105) {
        this.useless105 = useless105;
    }

    /**
     * get useless106
     *
     * @return useless106 value
     */
    public boolean getUseless106() {
        return this.useless106;
    }

    /**
     * set useless106 && set marked
     *
     * @param useless106 new value
     * @return current object
     */
    public YoTestProp setUseless106(boolean useless106) {
        if (this.useless106 != useless106) {
            this.mark(FIELD_INDEX_USELESS106);
            this.useless106 = useless106;
        }
        return this;
    }

    /**
     * inner set useless106
     *
     * @param useless106 new value
     */
    private void innerSetUseless106(boolean useless106) {
        this.useless106 = useless106;
    }

    /**
     * get useless107
     *
     * @return useless107 value
     */
    public boolean getUseless107() {
        return this.useless107;
    }

    /**
     * set useless107 && set marked
     *
     * @param useless107 new value
     * @return current object
     */
    public YoTestProp setUseless107(boolean useless107) {
        if (this.useless107 != useless107) {
            this.mark(FIELD_INDEX_USELESS107);
            this.useless107 = useless107;
        }
        return this;
    }

    /**
     * inner set useless107
     *
     * @param useless107 new value
     */
    private void innerSetUseless107(boolean useless107) {
        this.useless107 = useless107;
    }

    /**
     * get useless108
     *
     * @return useless108 value
     */
    public boolean getUseless108() {
        return this.useless108;
    }

    /**
     * set useless108 && set marked
     *
     * @param useless108 new value
     * @return current object
     */
    public YoTestProp setUseless108(boolean useless108) {
        if (this.useless108 != useless108) {
            this.mark(FIELD_INDEX_USELESS108);
            this.useless108 = useless108;
        }
        return this;
    }

    /**
     * inner set useless108
     *
     * @param useless108 new value
     */
    private void innerSetUseless108(boolean useless108) {
        this.useless108 = useless108;
    }

    /**
     * get useless109
     *
     * @return useless109 value
     */
    public boolean getUseless109() {
        return this.useless109;
    }

    /**
     * set useless109 && set marked
     *
     * @param useless109 new value
     * @return current object
     */
    public YoTestProp setUseless109(boolean useless109) {
        if (this.useless109 != useless109) {
            this.mark(FIELD_INDEX_USELESS109);
            this.useless109 = useless109;
        }
        return this;
    }

    /**
     * inner set useless109
     *
     * @param useless109 new value
     */
    private void innerSetUseless109(boolean useless109) {
        this.useless109 = useless109;
    }

    /**
     * get useless110
     *
     * @return useless110 value
     */
    public boolean getUseless110() {
        return this.useless110;
    }

    /**
     * set useless110 && set marked
     *
     * @param useless110 new value
     * @return current object
     */
    public YoTestProp setUseless110(boolean useless110) {
        if (this.useless110 != useless110) {
            this.mark(FIELD_INDEX_USELESS110);
            this.useless110 = useless110;
        }
        return this;
    }

    /**
     * inner set useless110
     *
     * @param useless110 new value
     */
    private void innerSetUseless110(boolean useless110) {
        this.useless110 = useless110;
    }

    /**
     * get useless111
     *
     * @return useless111 value
     */
    public boolean getUseless111() {
        return this.useless111;
    }

    /**
     * set useless111 && set marked
     *
     * @param useless111 new value
     * @return current object
     */
    public YoTestProp setUseless111(boolean useless111) {
        if (this.useless111 != useless111) {
            this.mark(FIELD_INDEX_USELESS111);
            this.useless111 = useless111;
        }
        return this;
    }

    /**
     * inner set useless111
     *
     * @param useless111 new value
     */
    private void innerSetUseless111(boolean useless111) {
        this.useless111 = useless111;
    }

    /**
     * get useless112
     *
     * @return useless112 value
     */
    public boolean getUseless112() {
        return this.useless112;
    }

    /**
     * set useless112 && set marked
     *
     * @param useless112 new value
     * @return current object
     */
    public YoTestProp setUseless112(boolean useless112) {
        if (this.useless112 != useless112) {
            this.mark(FIELD_INDEX_USELESS112);
            this.useless112 = useless112;
        }
        return this;
    }

    /**
     * inner set useless112
     *
     * @param useless112 new value
     */
    private void innerSetUseless112(boolean useless112) {
        this.useless112 = useless112;
    }

    /**
     * get useless113
     *
     * @return useless113 value
     */
    public boolean getUseless113() {
        return this.useless113;
    }

    /**
     * set useless113 && set marked
     *
     * @param useless113 new value
     * @return current object
     */
    public YoTestProp setUseless113(boolean useless113) {
        if (this.useless113 != useless113) {
            this.mark(FIELD_INDEX_USELESS113);
            this.useless113 = useless113;
        }
        return this;
    }

    /**
     * inner set useless113
     *
     * @param useless113 new value
     */
    private void innerSetUseless113(boolean useless113) {
        this.useless113 = useless113;
    }

    /**
     * get useless114
     *
     * @return useless114 value
     */
    public boolean getUseless114() {
        return this.useless114;
    }

    /**
     * set useless114 && set marked
     *
     * @param useless114 new value
     * @return current object
     */
    public YoTestProp setUseless114(boolean useless114) {
        if (this.useless114 != useless114) {
            this.mark(FIELD_INDEX_USELESS114);
            this.useless114 = useless114;
        }
        return this;
    }

    /**
     * inner set useless114
     *
     * @param useless114 new value
     */
    private void innerSetUseless114(boolean useless114) {
        this.useless114 = useless114;
    }

    /**
     * get useless115
     *
     * @return useless115 value
     */
    public boolean getUseless115() {
        return this.useless115;
    }

    /**
     * set useless115 && set marked
     *
     * @param useless115 new value
     * @return current object
     */
    public YoTestProp setUseless115(boolean useless115) {
        if (this.useless115 != useless115) {
            this.mark(FIELD_INDEX_USELESS115);
            this.useless115 = useless115;
        }
        return this;
    }

    /**
     * inner set useless115
     *
     * @param useless115 new value
     */
    private void innerSetUseless115(boolean useless115) {
        this.useless115 = useless115;
    }

    /**
     * get useless116
     *
     * @return useless116 value
     */
    public boolean getUseless116() {
        return this.useless116;
    }

    /**
     * set useless116 && set marked
     *
     * @param useless116 new value
     * @return current object
     */
    public YoTestProp setUseless116(boolean useless116) {
        if (this.useless116 != useless116) {
            this.mark(FIELD_INDEX_USELESS116);
            this.useless116 = useless116;
        }
        return this;
    }

    /**
     * inner set useless116
     *
     * @param useless116 new value
     */
    private void innerSetUseless116(boolean useless116) {
        this.useless116 = useless116;
    }

    /**
     * get useless117
     *
     * @return useless117 value
     */
    public boolean getUseless117() {
        return this.useless117;
    }

    /**
     * set useless117 && set marked
     *
     * @param useless117 new value
     * @return current object
     */
    public YoTestProp setUseless117(boolean useless117) {
        if (this.useless117 != useless117) {
            this.mark(FIELD_INDEX_USELESS117);
            this.useless117 = useless117;
        }
        return this;
    }

    /**
     * inner set useless117
     *
     * @param useless117 new value
     */
    private void innerSetUseless117(boolean useless117) {
        this.useless117 = useless117;
    }

    /**
     * get useless118
     *
     * @return useless118 value
     */
    public boolean getUseless118() {
        return this.useless118;
    }

    /**
     * set useless118 && set marked
     *
     * @param useless118 new value
     * @return current object
     */
    public YoTestProp setUseless118(boolean useless118) {
        if (this.useless118 != useless118) {
            this.mark(FIELD_INDEX_USELESS118);
            this.useless118 = useless118;
        }
        return this;
    }

    /**
     * inner set useless118
     *
     * @param useless118 new value
     */
    private void innerSetUseless118(boolean useless118) {
        this.useless118 = useless118;
    }

    /**
     * get useless119
     *
     * @return useless119 value
     */
    public boolean getUseless119() {
        return this.useless119;
    }

    /**
     * set useless119 && set marked
     *
     * @param useless119 new value
     * @return current object
     */
    public YoTestProp setUseless119(boolean useless119) {
        if (this.useless119 != useless119) {
            this.mark(FIELD_INDEX_USELESS119);
            this.useless119 = useless119;
        }
        return this;
    }

    /**
     * inner set useless119
     *
     * @param useless119 new value
     */
    private void innerSetUseless119(boolean useless119) {
        this.useless119 = useless119;
    }

    /**
     * get useless120
     *
     * @return useless120 value
     */
    public boolean getUseless120() {
        return this.useless120;
    }

    /**
     * set useless120 && set marked
     *
     * @param useless120 new value
     * @return current object
     */
    public YoTestProp setUseless120(boolean useless120) {
        if (this.useless120 != useless120) {
            this.mark(FIELD_INDEX_USELESS120);
            this.useless120 = useless120;
        }
        return this;
    }

    /**
     * inner set useless120
     *
     * @param useless120 new value
     */
    private void innerSetUseless120(boolean useless120) {
        this.useless120 = useless120;
    }

    /**
     * get useless121
     *
     * @return useless121 value
     */
    public boolean getUseless121() {
        return this.useless121;
    }

    /**
     * set useless121 && set marked
     *
     * @param useless121 new value
     * @return current object
     */
    public YoTestProp setUseless121(boolean useless121) {
        if (this.useless121 != useless121) {
            this.mark(FIELD_INDEX_USELESS121);
            this.useless121 = useless121;
        }
        return this;
    }

    /**
     * inner set useless121
     *
     * @param useless121 new value
     */
    private void innerSetUseless121(boolean useless121) {
        this.useless121 = useless121;
    }

    /**
     * get useless122
     *
     * @return useless122 value
     */
    public boolean getUseless122() {
        return this.useless122;
    }

    /**
     * set useless122 && set marked
     *
     * @param useless122 new value
     * @return current object
     */
    public YoTestProp setUseless122(boolean useless122) {
        if (this.useless122 != useless122) {
            this.mark(FIELD_INDEX_USELESS122);
            this.useless122 = useless122;
        }
        return this;
    }

    /**
     * inner set useless122
     *
     * @param useless122 new value
     */
    private void innerSetUseless122(boolean useless122) {
        this.useless122 = useless122;
    }

    /**
     * get useless123
     *
     * @return useless123 value
     */
    public boolean getUseless123() {
        return this.useless123;
    }

    /**
     * set useless123 && set marked
     *
     * @param useless123 new value
     * @return current object
     */
    public YoTestProp setUseless123(boolean useless123) {
        if (this.useless123 != useless123) {
            this.mark(FIELD_INDEX_USELESS123);
            this.useless123 = useless123;
        }
        return this;
    }

    /**
     * inner set useless123
     *
     * @param useless123 new value
     */
    private void innerSetUseless123(boolean useless123) {
        this.useless123 = useless123;
    }

    /**
     * get useless124
     *
     * @return useless124 value
     */
    public boolean getUseless124() {
        return this.useless124;
    }

    /**
     * set useless124 && set marked
     *
     * @param useless124 new value
     * @return current object
     */
    public YoTestProp setUseless124(boolean useless124) {
        if (this.useless124 != useless124) {
            this.mark(FIELD_INDEX_USELESS124);
            this.useless124 = useless124;
        }
        return this;
    }

    /**
     * inner set useless124
     *
     * @param useless124 new value
     */
    private void innerSetUseless124(boolean useless124) {
        this.useless124 = useless124;
    }

    /**
     * get useless125
     *
     * @return useless125 value
     */
    public boolean getUseless125() {
        return this.useless125;
    }

    /**
     * set useless125 && set marked
     *
     * @param useless125 new value
     * @return current object
     */
    public YoTestProp setUseless125(boolean useless125) {
        if (this.useless125 != useless125) {
            this.mark(FIELD_INDEX_USELESS125);
            this.useless125 = useless125;
        }
        return this;
    }

    /**
     * inner set useless125
     *
     * @param useless125 new value
     */
    private void innerSetUseless125(boolean useless125) {
        this.useless125 = useless125;
    }

    /**
     * get useless126
     *
     * @return useless126 value
     */
    public boolean getUseless126() {
        return this.useless126;
    }

    /**
     * set useless126 && set marked
     *
     * @param useless126 new value
     * @return current object
     */
    public YoTestProp setUseless126(boolean useless126) {
        if (this.useless126 != useless126) {
            this.mark(FIELD_INDEX_USELESS126);
            this.useless126 = useless126;
        }
        return this;
    }

    /**
     * inner set useless126
     *
     * @param useless126 new value
     */
    private void innerSetUseless126(boolean useless126) {
        this.useless126 = useless126;
    }

    /**
     * get useless127
     *
     * @return useless127 value
     */
    public boolean getUseless127() {
        return this.useless127;
    }

    /**
     * set useless127 && set marked
     *
     * @param useless127 new value
     * @return current object
     */
    public YoTestProp setUseless127(boolean useless127) {
        if (this.useless127 != useless127) {
            this.mark(FIELD_INDEX_USELESS127);
            this.useless127 = useless127;
        }
        return this;
    }

    /**
     * inner set useless127
     *
     * @param useless127 new value
     */
    private void innerSetUseless127(boolean useless127) {
        this.useless127 = useless127;
    }

    /**
     * get useless128
     *
     * @return useless128 value
     */
    public boolean getUseless128() {
        return this.useless128;
    }

    /**
     * set useless128 && set marked
     *
     * @param useless128 new value
     * @return current object
     */
    public YoTestProp setUseless128(boolean useless128) {
        if (this.useless128 != useless128) {
            this.mark(FIELD_INDEX_USELESS128);
            this.useless128 = useless128;
        }
        return this;
    }

    /**
     * inner set useless128
     *
     * @param useless128 new value
     */
    private void innerSetUseless128(boolean useless128) {
        this.useless128 = useless128;
    }

    /**
     * get useless129
     *
     * @return useless129 value
     */
    public boolean getUseless129() {
        return this.useless129;
    }

    /**
     * set useless129 && set marked
     *
     * @param useless129 new value
     * @return current object
     */
    public YoTestProp setUseless129(boolean useless129) {
        if (this.useless129 != useless129) {
            this.mark(FIELD_INDEX_USELESS129);
            this.useless129 = useless129;
        }
        return this;
    }

    /**
     * inner set useless129
     *
     * @param useless129 new value
     */
    private void innerSetUseless129(boolean useless129) {
        this.useless129 = useless129;
    }

    /**
     * get useless130
     *
     * @return useless130 value
     */
    public boolean getUseless130() {
        return this.useless130;
    }

    /**
     * set useless130 && set marked
     *
     * @param useless130 new value
     * @return current object
     */
    public YoTestProp setUseless130(boolean useless130) {
        if (this.useless130 != useless130) {
            this.mark(FIELD_INDEX_USELESS130);
            this.useless130 = useless130;
        }
        return this;
    }

    /**
     * inner set useless130
     *
     * @param useless130 new value
     */
    private void innerSetUseless130(boolean useless130) {
        this.useless130 = useless130;
    }

    /**
     * get useless131
     *
     * @return useless131 value
     */
    public boolean getUseless131() {
        return this.useless131;
    }

    /**
     * set useless131 && set marked
     *
     * @param useless131 new value
     * @return current object
     */
    public YoTestProp setUseless131(boolean useless131) {
        if (this.useless131 != useless131) {
            this.mark(FIELD_INDEX_USELESS131);
            this.useless131 = useless131;
        }
        return this;
    }

    /**
     * inner set useless131
     *
     * @param useless131 new value
     */
    private void innerSetUseless131(boolean useless131) {
        this.useless131 = useless131;
    }

    /**
     * get useless132
     *
     * @return useless132 value
     */
    public boolean getUseless132() {
        return this.useless132;
    }

    /**
     * set useless132 && set marked
     *
     * @param useless132 new value
     * @return current object
     */
    public YoTestProp setUseless132(boolean useless132) {
        if (this.useless132 != useless132) {
            this.mark(FIELD_INDEX_USELESS132);
            this.useless132 = useless132;
        }
        return this;
    }

    /**
     * inner set useless132
     *
     * @param useless132 new value
     */
    private void innerSetUseless132(boolean useless132) {
        this.useless132 = useless132;
    }

    /**
     * get useless133
     *
     * @return useless133 value
     */
    public boolean getUseless133() {
        return this.useless133;
    }

    /**
     * set useless133 && set marked
     *
     * @param useless133 new value
     * @return current object
     */
    public YoTestProp setUseless133(boolean useless133) {
        if (this.useless133 != useless133) {
            this.mark(FIELD_INDEX_USELESS133);
            this.useless133 = useless133;
        }
        return this;
    }

    /**
     * inner set useless133
     *
     * @param useless133 new value
     */
    private void innerSetUseless133(boolean useless133) {
        this.useless133 = useless133;
    }

    /**
     * get useless134
     *
     * @return useless134 value
     */
    public boolean getUseless134() {
        return this.useless134;
    }

    /**
     * set useless134 && set marked
     *
     * @param useless134 new value
     * @return current object
     */
    public YoTestProp setUseless134(boolean useless134) {
        if (this.useless134 != useless134) {
            this.mark(FIELD_INDEX_USELESS134);
            this.useless134 = useless134;
        }
        return this;
    }

    /**
     * inner set useless134
     *
     * @param useless134 new value
     */
    private void innerSetUseless134(boolean useless134) {
        this.useless134 = useless134;
    }

    /**
     * get useless135
     *
     * @return useless135 value
     */
    public boolean getUseless135() {
        return this.useless135;
    }

    /**
     * set useless135 && set marked
     *
     * @param useless135 new value
     * @return current object
     */
    public YoTestProp setUseless135(boolean useless135) {
        if (this.useless135 != useless135) {
            this.mark(FIELD_INDEX_USELESS135);
            this.useless135 = useless135;
        }
        return this;
    }

    /**
     * inner set useless135
     *
     * @param useless135 new value
     */
    private void innerSetUseless135(boolean useless135) {
        this.useless135 = useless135;
    }

    /**
     * get useless136
     *
     * @return useless136 value
     */
    public boolean getUseless136() {
        return this.useless136;
    }

    /**
     * set useless136 && set marked
     *
     * @param useless136 new value
     * @return current object
     */
    public YoTestProp setUseless136(boolean useless136) {
        if (this.useless136 != useless136) {
            this.mark(FIELD_INDEX_USELESS136);
            this.useless136 = useless136;
        }
        return this;
    }

    /**
     * inner set useless136
     *
     * @param useless136 new value
     */
    private void innerSetUseless136(boolean useless136) {
        this.useless136 = useless136;
    }

    /**
     * get useless137
     *
     * @return useless137 value
     */
    public boolean getUseless137() {
        return this.useless137;
    }

    /**
     * set useless137 && set marked
     *
     * @param useless137 new value
     * @return current object
     */
    public YoTestProp setUseless137(boolean useless137) {
        if (this.useless137 != useless137) {
            this.mark(FIELD_INDEX_USELESS137);
            this.useless137 = useless137;
        }
        return this;
    }

    /**
     * inner set useless137
     *
     * @param useless137 new value
     */
    private void innerSetUseless137(boolean useless137) {
        this.useless137 = useless137;
    }

    /**
     * get useless138
     *
     * @return useless138 value
     */
    public boolean getUseless138() {
        return this.useless138;
    }

    /**
     * set useless138 && set marked
     *
     * @param useless138 new value
     * @return current object
     */
    public YoTestProp setUseless138(boolean useless138) {
        if (this.useless138 != useless138) {
            this.mark(FIELD_INDEX_USELESS138);
            this.useless138 = useless138;
        }
        return this;
    }

    /**
     * inner set useless138
     *
     * @param useless138 new value
     */
    private void innerSetUseless138(boolean useless138) {
        this.useless138 = useless138;
    }

    /**
     * get useless139
     *
     * @return useless139 value
     */
    public boolean getUseless139() {
        return this.useless139;
    }

    /**
     * set useless139 && set marked
     *
     * @param useless139 new value
     * @return current object
     */
    public YoTestProp setUseless139(boolean useless139) {
        if (this.useless139 != useless139) {
            this.mark(FIELD_INDEX_USELESS139);
            this.useless139 = useless139;
        }
        return this;
    }

    /**
     * inner set useless139
     *
     * @param useless139 new value
     */
    private void innerSetUseless139(boolean useless139) {
        this.useless139 = useless139;
    }

    /**
     * get useless140
     *
     * @return useless140 value
     */
    public boolean getUseless140() {
        return this.useless140;
    }

    /**
     * set useless140 && set marked
     *
     * @param useless140 new value
     * @return current object
     */
    public YoTestProp setUseless140(boolean useless140) {
        if (this.useless140 != useless140) {
            this.mark(FIELD_INDEX_USELESS140);
            this.useless140 = useless140;
        }
        return this;
    }

    /**
     * inner set useless140
     *
     * @param useless140 new value
     */
    private void innerSetUseless140(boolean useless140) {
        this.useless140 = useless140;
    }

    /**
     * get useless141
     *
     * @return useless141 value
     */
    public boolean getUseless141() {
        return this.useless141;
    }

    /**
     * set useless141 && set marked
     *
     * @param useless141 new value
     * @return current object
     */
    public YoTestProp setUseless141(boolean useless141) {
        if (this.useless141 != useless141) {
            this.mark(FIELD_INDEX_USELESS141);
            this.useless141 = useless141;
        }
        return this;
    }

    /**
     * inner set useless141
     *
     * @param useless141 new value
     */
    private void innerSetUseless141(boolean useless141) {
        this.useless141 = useless141;
    }

    /**
     * get useless142
     *
     * @return useless142 value
     */
    public boolean getUseless142() {
        return this.useless142;
    }

    /**
     * set useless142 && set marked
     *
     * @param useless142 new value
     * @return current object
     */
    public YoTestProp setUseless142(boolean useless142) {
        if (this.useless142 != useless142) {
            this.mark(FIELD_INDEX_USELESS142);
            this.useless142 = useless142;
        }
        return this;
    }

    /**
     * inner set useless142
     *
     * @param useless142 new value
     */
    private void innerSetUseless142(boolean useless142) {
        this.useless142 = useless142;
    }

    /**
     * get useless143
     *
     * @return useless143 value
     */
    public boolean getUseless143() {
        return this.useless143;
    }

    /**
     * set useless143 && set marked
     *
     * @param useless143 new value
     * @return current object
     */
    public YoTestProp setUseless143(boolean useless143) {
        if (this.useless143 != useless143) {
            this.mark(FIELD_INDEX_USELESS143);
            this.useless143 = useless143;
        }
        return this;
    }

    /**
     * inner set useless143
     *
     * @param useless143 new value
     */
    private void innerSetUseless143(boolean useless143) {
        this.useless143 = useless143;
    }

    /**
     * get useless144
     *
     * @return useless144 value
     */
    public boolean getUseless144() {
        return this.useless144;
    }

    /**
     * set useless144 && set marked
     *
     * @param useless144 new value
     * @return current object
     */
    public YoTestProp setUseless144(boolean useless144) {
        if (this.useless144 != useless144) {
            this.mark(FIELD_INDEX_USELESS144);
            this.useless144 = useless144;
        }
        return this;
    }

    /**
     * inner set useless144
     *
     * @param useless144 new value
     */
    private void innerSetUseless144(boolean useless144) {
        this.useless144 = useless144;
    }

    /**
     * get useless145
     *
     * @return useless145 value
     */
    public boolean getUseless145() {
        return this.useless145;
    }

    /**
     * set useless145 && set marked
     *
     * @param useless145 new value
     * @return current object
     */
    public YoTestProp setUseless145(boolean useless145) {
        if (this.useless145 != useless145) {
            this.mark(FIELD_INDEX_USELESS145);
            this.useless145 = useless145;
        }
        return this;
    }

    /**
     * inner set useless145
     *
     * @param useless145 new value
     */
    private void innerSetUseless145(boolean useless145) {
        this.useless145 = useless145;
    }

    /**
     * get useless146
     *
     * @return useless146 value
     */
    public boolean getUseless146() {
        return this.useless146;
    }

    /**
     * set useless146 && set marked
     *
     * @param useless146 new value
     * @return current object
     */
    public YoTestProp setUseless146(boolean useless146) {
        if (this.useless146 != useless146) {
            this.mark(FIELD_INDEX_USELESS146);
            this.useless146 = useless146;
        }
        return this;
    }

    /**
     * inner set useless146
     *
     * @param useless146 new value
     */
    private void innerSetUseless146(boolean useless146) {
        this.useless146 = useless146;
    }

    /**
     * get useless147
     *
     * @return useless147 value
     */
    public boolean getUseless147() {
        return this.useless147;
    }

    /**
     * set useless147 && set marked
     *
     * @param useless147 new value
     * @return current object
     */
    public YoTestProp setUseless147(boolean useless147) {
        if (this.useless147 != useless147) {
            this.mark(FIELD_INDEX_USELESS147);
            this.useless147 = useless147;
        }
        return this;
    }

    /**
     * inner set useless147
     *
     * @param useless147 new value
     */
    private void innerSetUseless147(boolean useless147) {
        this.useless147 = useless147;
    }

    /**
     * get useless148
     *
     * @return useless148 value
     */
    public boolean getUseless148() {
        return this.useless148;
    }

    /**
     * set useless148 && set marked
     *
     * @param useless148 new value
     * @return current object
     */
    public YoTestProp setUseless148(boolean useless148) {
        if (this.useless148 != useless148) {
            this.mark(FIELD_INDEX_USELESS148);
            this.useless148 = useless148;
        }
        return this;
    }

    /**
     * inner set useless148
     *
     * @param useless148 new value
     */
    private void innerSetUseless148(boolean useless148) {
        this.useless148 = useless148;
    }

    /**
     * get useless149
     *
     * @return useless149 value
     */
    public boolean getUseless149() {
        return this.useless149;
    }

    /**
     * set useless149 && set marked
     *
     * @param useless149 new value
     * @return current object
     */
    public YoTestProp setUseless149(boolean useless149) {
        if (this.useless149 != useless149) {
            this.mark(FIELD_INDEX_USELESS149);
            this.useless149 = useless149;
        }
        return this;
    }

    /**
     * inner set useless149
     *
     * @param useless149 new value
     */
    private void innerSetUseless149(boolean useless149) {
        this.useless149 = useless149;
    }

    /**
     * get useless150
     *
     * @return useless150 value
     */
    public boolean getUseless150() {
        return this.useless150;
    }

    /**
     * set useless150 && set marked
     *
     * @param useless150 new value
     * @return current object
     */
    public YoTestProp setUseless150(boolean useless150) {
        if (this.useless150 != useless150) {
            this.mark(FIELD_INDEX_USELESS150);
            this.useless150 = useless150;
        }
        return this;
    }

    /**
     * inner set useless150
     *
     * @param useless150 new value
     */
    private void innerSetUseless150(boolean useless150) {
        this.useless150 = useless150;
    }

    /**
     * get useless151
     *
     * @return useless151 value
     */
    public boolean getUseless151() {
        return this.useless151;
    }

    /**
     * set useless151 && set marked
     *
     * @param useless151 new value
     * @return current object
     */
    public YoTestProp setUseless151(boolean useless151) {
        if (this.useless151 != useless151) {
            this.mark(FIELD_INDEX_USELESS151);
            this.useless151 = useless151;
        }
        return this;
    }

    /**
     * inner set useless151
     *
     * @param useless151 new value
     */
    private void innerSetUseless151(boolean useless151) {
        this.useless151 = useless151;
    }

    /**
     * get useless152
     *
     * @return useless152 value
     */
    public boolean getUseless152() {
        return this.useless152;
    }

    /**
     * set useless152 && set marked
     *
     * @param useless152 new value
     * @return current object
     */
    public YoTestProp setUseless152(boolean useless152) {
        if (this.useless152 != useless152) {
            this.mark(FIELD_INDEX_USELESS152);
            this.useless152 = useless152;
        }
        return this;
    }

    /**
     * inner set useless152
     *
     * @param useless152 new value
     */
    private void innerSetUseless152(boolean useless152) {
        this.useless152 = useless152;
    }

    /**
     * get useless153
     *
     * @return useless153 value
     */
    public boolean getUseless153() {
        return this.useless153;
    }

    /**
     * set useless153 && set marked
     *
     * @param useless153 new value
     * @return current object
     */
    public YoTestProp setUseless153(boolean useless153) {
        if (this.useless153 != useless153) {
            this.mark(FIELD_INDEX_USELESS153);
            this.useless153 = useless153;
        }
        return this;
    }

    /**
     * inner set useless153
     *
     * @param useless153 new value
     */
    private void innerSetUseless153(boolean useless153) {
        this.useless153 = useless153;
    }

    /**
     * get useless154
     *
     * @return useless154 value
     */
    public boolean getUseless154() {
        return this.useless154;
    }

    /**
     * set useless154 && set marked
     *
     * @param useless154 new value
     * @return current object
     */
    public YoTestProp setUseless154(boolean useless154) {
        if (this.useless154 != useless154) {
            this.mark(FIELD_INDEX_USELESS154);
            this.useless154 = useless154;
        }
        return this;
    }

    /**
     * inner set useless154
     *
     * @param useless154 new value
     */
    private void innerSetUseless154(boolean useless154) {
        this.useless154 = useless154;
    }

    /**
     * get useless155
     *
     * @return useless155 value
     */
    public boolean getUseless155() {
        return this.useless155;
    }

    /**
     * set useless155 && set marked
     *
     * @param useless155 new value
     * @return current object
     */
    public YoTestProp setUseless155(boolean useless155) {
        if (this.useless155 != useless155) {
            this.mark(FIELD_INDEX_USELESS155);
            this.useless155 = useless155;
        }
        return this;
    }

    /**
     * inner set useless155
     *
     * @param useless155 new value
     */
    private void innerSetUseless155(boolean useless155) {
        this.useless155 = useless155;
    }

    /**
     * get useless156
     *
     * @return useless156 value
     */
    public boolean getUseless156() {
        return this.useless156;
    }

    /**
     * set useless156 && set marked
     *
     * @param useless156 new value
     * @return current object
     */
    public YoTestProp setUseless156(boolean useless156) {
        if (this.useless156 != useless156) {
            this.mark(FIELD_INDEX_USELESS156);
            this.useless156 = useless156;
        }
        return this;
    }

    /**
     * inner set useless156
     *
     * @param useless156 new value
     */
    private void innerSetUseless156(boolean useless156) {
        this.useless156 = useless156;
    }

    /**
     * get useless157
     *
     * @return useless157 value
     */
    public boolean getUseless157() {
        return this.useless157;
    }

    /**
     * set useless157 && set marked
     *
     * @param useless157 new value
     * @return current object
     */
    public YoTestProp setUseless157(boolean useless157) {
        if (this.useless157 != useless157) {
            this.mark(FIELD_INDEX_USELESS157);
            this.useless157 = useless157;
        }
        return this;
    }

    /**
     * inner set useless157
     *
     * @param useless157 new value
     */
    private void innerSetUseless157(boolean useless157) {
        this.useless157 = useless157;
    }

    /**
     * get useless158
     *
     * @return useless158 value
     */
    public boolean getUseless158() {
        return this.useless158;
    }

    /**
     * set useless158 && set marked
     *
     * @param useless158 new value
     * @return current object
     */
    public YoTestProp setUseless158(boolean useless158) {
        if (this.useless158 != useless158) {
            this.mark(FIELD_INDEX_USELESS158);
            this.useless158 = useless158;
        }
        return this;
    }

    /**
     * inner set useless158
     *
     * @param useless158 new value
     */
    private void innerSetUseless158(boolean useless158) {
        this.useless158 = useless158;
    }

    /**
     * get useless159
     *
     * @return useless159 value
     */
    public boolean getUseless159() {
        return this.useless159;
    }

    /**
     * set useless159 && set marked
     *
     * @param useless159 new value
     * @return current object
     */
    public YoTestProp setUseless159(boolean useless159) {
        if (this.useless159 != useless159) {
            this.mark(FIELD_INDEX_USELESS159);
            this.useless159 = useless159;
        }
        return this;
    }

    /**
     * inner set useless159
     *
     * @param useless159 new value
     */
    private void innerSetUseless159(boolean useless159) {
        this.useless159 = useless159;
    }

    /**
     * get useless160
     *
     * @return useless160 value
     */
    public boolean getUseless160() {
        return this.useless160;
    }

    /**
     * set useless160 && set marked
     *
     * @param useless160 new value
     * @return current object
     */
    public YoTestProp setUseless160(boolean useless160) {
        if (this.useless160 != useless160) {
            this.mark(FIELD_INDEX_USELESS160);
            this.useless160 = useless160;
        }
        return this;
    }

    /**
     * inner set useless160
     *
     * @param useless160 new value
     */
    private void innerSetUseless160(boolean useless160) {
        this.useless160 = useless160;
    }

    /**
     * get useless161
     *
     * @return useless161 value
     */
    public boolean getUseless161() {
        return this.useless161;
    }

    /**
     * set useless161 && set marked
     *
     * @param useless161 new value
     * @return current object
     */
    public YoTestProp setUseless161(boolean useless161) {
        if (this.useless161 != useless161) {
            this.mark(FIELD_INDEX_USELESS161);
            this.useless161 = useless161;
        }
        return this;
    }

    /**
     * inner set useless161
     *
     * @param useless161 new value
     */
    private void innerSetUseless161(boolean useless161) {
        this.useless161 = useless161;
    }

    /**
     * get useless162
     *
     * @return useless162 value
     */
    public boolean getUseless162() {
        return this.useless162;
    }

    /**
     * set useless162 && set marked
     *
     * @param useless162 new value
     * @return current object
     */
    public YoTestProp setUseless162(boolean useless162) {
        if (this.useless162 != useless162) {
            this.mark(FIELD_INDEX_USELESS162);
            this.useless162 = useless162;
        }
        return this;
    }

    /**
     * inner set useless162
     *
     * @param useless162 new value
     */
    private void innerSetUseless162(boolean useless162) {
        this.useless162 = useless162;
    }

    /**
     * get useless163
     *
     * @return useless163 value
     */
    public boolean getUseless163() {
        return this.useless163;
    }

    /**
     * set useless163 && set marked
     *
     * @param useless163 new value
     * @return current object
     */
    public YoTestProp setUseless163(boolean useless163) {
        if (this.useless163 != useless163) {
            this.mark(FIELD_INDEX_USELESS163);
            this.useless163 = useless163;
        }
        return this;
    }

    /**
     * inner set useless163
     *
     * @param useless163 new value
     */
    private void innerSetUseless163(boolean useless163) {
        this.useless163 = useless163;
    }

    /**
     * get useless164
     *
     * @return useless164 value
     */
    public boolean getUseless164() {
        return this.useless164;
    }

    /**
     * set useless164 && set marked
     *
     * @param useless164 new value
     * @return current object
     */
    public YoTestProp setUseless164(boolean useless164) {
        if (this.useless164 != useless164) {
            this.mark(FIELD_INDEX_USELESS164);
            this.useless164 = useless164;
        }
        return this;
    }

    /**
     * inner set useless164
     *
     * @param useless164 new value
     */
    private void innerSetUseless164(boolean useless164) {
        this.useless164 = useless164;
    }

    /**
     * get yoTestModel
     *
     * @return yoTestModel value
     */
    public YoTestModelProp getYoTestModel() {
        if (this.yoTestModel == null) {
            this.yoTestModel = new YoTestModelProp(this, FIELD_INDEX_YOTESTMODEL);
        }
        return this.yoTestModel;
    }

    /**
     * get yoTestUnit
     *
     * @return yoTestUnit value
     */
    public YoTestUnitProp getYoTestUnit() {
        if (this.yoTestUnit == null) {
            this.yoTestUnit = new YoTestUnitProp(this, FIELD_INDEX_YOTESTUNIT);
        }
        return this.yoTestUnit;
    }

    /**
     * get setField
     *
     * @return setField value
     */
    public Int32SetProp getSetField() {
        if (this.setField == null) {
            this.setField = new Int32SetProp(this, FIELD_INDEX_SETFIELD);
        }
        return this.setField;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addSetField(Integer e) {
        this.getSetField().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeSetField(Integer e) {
        if (this.setField == null) {
            return null;
        }
        if(this.setField.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getSetFieldSize() {
        if (this.setField == null) {
            return 0;
        }
        return this.setField.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isSetFieldEmpty() {
        if (this.setField == null) {
            return true;
        }
        return this.getSetField().isEmpty();
    }

    /**
     * clear set
     */
    public void clearSetField() {
        this.getSetField().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isSetFieldContains(Integer e) {
        return this.setField != null && this.setField.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestEntityPB.Builder getCopyCsBuilder() {
        final YoTestEntityPB.Builder builder = YoTestEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoTestEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntField() != 0) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }  else if (builder.hasIntField()) {
            // 清理IntField
            builder.clearIntField();
            fieldCnt++;
        }
        if (this.getLongField() != 0L) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }  else if (builder.hasLongField()) {
            // 清理LongField
            builder.clearLongField();
            fieldCnt++;
        }
        if (!this.getStringField().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }  else if (builder.hasStringField()) {
            // 清理StringField
            builder.clearStringField();
            fieldCnt++;
        }
        if (this.getBoolField()) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }  else if (builder.hasBoolField()) {
            // 清理BoolField
            builder.clearBoolField();
            fieldCnt++;
        }
        if (this.getUseless1()) {
            builder.setUseless1(this.getUseless1());
            fieldCnt++;
        }  else if (builder.hasUseless1()) {
            // 清理Useless1
            builder.clearUseless1();
            fieldCnt++;
        }
        if (this.getUseless2()) {
            builder.setUseless2(this.getUseless2());
            fieldCnt++;
        }  else if (builder.hasUseless2()) {
            // 清理Useless2
            builder.clearUseless2();
            fieldCnt++;
        }
        if (this.getUseless3()) {
            builder.setUseless3(this.getUseless3());
            fieldCnt++;
        }  else if (builder.hasUseless3()) {
            // 清理Useless3
            builder.clearUseless3();
            fieldCnt++;
        }
        if (this.getUseless4()) {
            builder.setUseless4(this.getUseless4());
            fieldCnt++;
        }  else if (builder.hasUseless4()) {
            // 清理Useless4
            builder.clearUseless4();
            fieldCnt++;
        }
        if (this.getUseless5()) {
            builder.setUseless5(this.getUseless5());
            fieldCnt++;
        }  else if (builder.hasUseless5()) {
            // 清理Useless5
            builder.clearUseless5();
            fieldCnt++;
        }
        if (this.getUseless6()) {
            builder.setUseless6(this.getUseless6());
            fieldCnt++;
        }  else if (builder.hasUseless6()) {
            // 清理Useless6
            builder.clearUseless6();
            fieldCnt++;
        }
        if (this.getUseless7()) {
            builder.setUseless7(this.getUseless7());
            fieldCnt++;
        }  else if (builder.hasUseless7()) {
            // 清理Useless7
            builder.clearUseless7();
            fieldCnt++;
        }
        if (this.getUseless8()) {
            builder.setUseless8(this.getUseless8());
            fieldCnt++;
        }  else if (builder.hasUseless8()) {
            // 清理Useless8
            builder.clearUseless8();
            fieldCnt++;
        }
        if (this.getUseless9()) {
            builder.setUseless9(this.getUseless9());
            fieldCnt++;
        }  else if (builder.hasUseless9()) {
            // 清理Useless9
            builder.clearUseless9();
            fieldCnt++;
        }
        if (this.getUseless10()) {
            builder.setUseless10(this.getUseless10());
            fieldCnt++;
        }  else if (builder.hasUseless10()) {
            // 清理Useless10
            builder.clearUseless10();
            fieldCnt++;
        }
        if (this.getUseless11()) {
            builder.setUseless11(this.getUseless11());
            fieldCnt++;
        }  else if (builder.hasUseless11()) {
            // 清理Useless11
            builder.clearUseless11();
            fieldCnt++;
        }
        if (this.getUseless12()) {
            builder.setUseless12(this.getUseless12());
            fieldCnt++;
        }  else if (builder.hasUseless12()) {
            // 清理Useless12
            builder.clearUseless12();
            fieldCnt++;
        }
        if (this.getUseless13()) {
            builder.setUseless13(this.getUseless13());
            fieldCnt++;
        }  else if (builder.hasUseless13()) {
            // 清理Useless13
            builder.clearUseless13();
            fieldCnt++;
        }
        if (this.getUseless14()) {
            builder.setUseless14(this.getUseless14());
            fieldCnt++;
        }  else if (builder.hasUseless14()) {
            // 清理Useless14
            builder.clearUseless14();
            fieldCnt++;
        }
        if (this.getUseless15()) {
            builder.setUseless15(this.getUseless15());
            fieldCnt++;
        }  else if (builder.hasUseless15()) {
            // 清理Useless15
            builder.clearUseless15();
            fieldCnt++;
        }
        if (this.getUseless16()) {
            builder.setUseless16(this.getUseless16());
            fieldCnt++;
        }  else if (builder.hasUseless16()) {
            // 清理Useless16
            builder.clearUseless16();
            fieldCnt++;
        }
        if (this.getUseless17()) {
            builder.setUseless17(this.getUseless17());
            fieldCnt++;
        }  else if (builder.hasUseless17()) {
            // 清理Useless17
            builder.clearUseless17();
            fieldCnt++;
        }
        if (this.getUseless18()) {
            builder.setUseless18(this.getUseless18());
            fieldCnt++;
        }  else if (builder.hasUseless18()) {
            // 清理Useless18
            builder.clearUseless18();
            fieldCnt++;
        }
        if (this.getUseless19()) {
            builder.setUseless19(this.getUseless19());
            fieldCnt++;
        }  else if (builder.hasUseless19()) {
            // 清理Useless19
            builder.clearUseless19();
            fieldCnt++;
        }
        if (this.getUseless20()) {
            builder.setUseless20(this.getUseless20());
            fieldCnt++;
        }  else if (builder.hasUseless20()) {
            // 清理Useless20
            builder.clearUseless20();
            fieldCnt++;
        }
        if (this.getUseless21()) {
            builder.setUseless21(this.getUseless21());
            fieldCnt++;
        }  else if (builder.hasUseless21()) {
            // 清理Useless21
            builder.clearUseless21();
            fieldCnt++;
        }
        if (this.getUseless22()) {
            builder.setUseless22(this.getUseless22());
            fieldCnt++;
        }  else if (builder.hasUseless22()) {
            // 清理Useless22
            builder.clearUseless22();
            fieldCnt++;
        }
        if (this.getUseless23()) {
            builder.setUseless23(this.getUseless23());
            fieldCnt++;
        }  else if (builder.hasUseless23()) {
            // 清理Useless23
            builder.clearUseless23();
            fieldCnt++;
        }
        if (this.getUseless24()) {
            builder.setUseless24(this.getUseless24());
            fieldCnt++;
        }  else if (builder.hasUseless24()) {
            // 清理Useless24
            builder.clearUseless24();
            fieldCnt++;
        }
        if (this.getUseless25()) {
            builder.setUseless25(this.getUseless25());
            fieldCnt++;
        }  else if (builder.hasUseless25()) {
            // 清理Useless25
            builder.clearUseless25();
            fieldCnt++;
        }
        if (this.getUseless26()) {
            builder.setUseless26(this.getUseless26());
            fieldCnt++;
        }  else if (builder.hasUseless26()) {
            // 清理Useless26
            builder.clearUseless26();
            fieldCnt++;
        }
        if (this.getUseless27()) {
            builder.setUseless27(this.getUseless27());
            fieldCnt++;
        }  else if (builder.hasUseless27()) {
            // 清理Useless27
            builder.clearUseless27();
            fieldCnt++;
        }
        if (this.getUseless28()) {
            builder.setUseless28(this.getUseless28());
            fieldCnt++;
        }  else if (builder.hasUseless28()) {
            // 清理Useless28
            builder.clearUseless28();
            fieldCnt++;
        }
        if (this.getUseless29()) {
            builder.setUseless29(this.getUseless29());
            fieldCnt++;
        }  else if (builder.hasUseless29()) {
            // 清理Useless29
            builder.clearUseless29();
            fieldCnt++;
        }
        if (this.getUseless30()) {
            builder.setUseless30(this.getUseless30());
            fieldCnt++;
        }  else if (builder.hasUseless30()) {
            // 清理Useless30
            builder.clearUseless30();
            fieldCnt++;
        }
        if (this.getUseless31()) {
            builder.setUseless31(this.getUseless31());
            fieldCnt++;
        }  else if (builder.hasUseless31()) {
            // 清理Useless31
            builder.clearUseless31();
            fieldCnt++;
        }
        if (this.getUseless32()) {
            builder.setUseless32(this.getUseless32());
            fieldCnt++;
        }  else if (builder.hasUseless32()) {
            // 清理Useless32
            builder.clearUseless32();
            fieldCnt++;
        }
        if (this.getUseless33()) {
            builder.setUseless33(this.getUseless33());
            fieldCnt++;
        }  else if (builder.hasUseless33()) {
            // 清理Useless33
            builder.clearUseless33();
            fieldCnt++;
        }
        if (this.getUseless34()) {
            builder.setUseless34(this.getUseless34());
            fieldCnt++;
        }  else if (builder.hasUseless34()) {
            // 清理Useless34
            builder.clearUseless34();
            fieldCnt++;
        }
        if (this.getUseless35()) {
            builder.setUseless35(this.getUseless35());
            fieldCnt++;
        }  else if (builder.hasUseless35()) {
            // 清理Useless35
            builder.clearUseless35();
            fieldCnt++;
        }
        if (this.getUseless36()) {
            builder.setUseless36(this.getUseless36());
            fieldCnt++;
        }  else if (builder.hasUseless36()) {
            // 清理Useless36
            builder.clearUseless36();
            fieldCnt++;
        }
        if (this.getUseless37()) {
            builder.setUseless37(this.getUseless37());
            fieldCnt++;
        }  else if (builder.hasUseless37()) {
            // 清理Useless37
            builder.clearUseless37();
            fieldCnt++;
        }
        if (this.getUseless38()) {
            builder.setUseless38(this.getUseless38());
            fieldCnt++;
        }  else if (builder.hasUseless38()) {
            // 清理Useless38
            builder.clearUseless38();
            fieldCnt++;
        }
        if (this.getUseless39()) {
            builder.setUseless39(this.getUseless39());
            fieldCnt++;
        }  else if (builder.hasUseless39()) {
            // 清理Useless39
            builder.clearUseless39();
            fieldCnt++;
        }
        if (this.getUseless40()) {
            builder.setUseless40(this.getUseless40());
            fieldCnt++;
        }  else if (builder.hasUseless40()) {
            // 清理Useless40
            builder.clearUseless40();
            fieldCnt++;
        }
        if (this.getUseless41()) {
            builder.setUseless41(this.getUseless41());
            fieldCnt++;
        }  else if (builder.hasUseless41()) {
            // 清理Useless41
            builder.clearUseless41();
            fieldCnt++;
        }
        if (this.getUseless42()) {
            builder.setUseless42(this.getUseless42());
            fieldCnt++;
        }  else if (builder.hasUseless42()) {
            // 清理Useless42
            builder.clearUseless42();
            fieldCnt++;
        }
        if (this.getUseless43()) {
            builder.setUseless43(this.getUseless43());
            fieldCnt++;
        }  else if (builder.hasUseless43()) {
            // 清理Useless43
            builder.clearUseless43();
            fieldCnt++;
        }
        if (this.getUseless44()) {
            builder.setUseless44(this.getUseless44());
            fieldCnt++;
        }  else if (builder.hasUseless44()) {
            // 清理Useless44
            builder.clearUseless44();
            fieldCnt++;
        }
        if (this.getUseless45()) {
            builder.setUseless45(this.getUseless45());
            fieldCnt++;
        }  else if (builder.hasUseless45()) {
            // 清理Useless45
            builder.clearUseless45();
            fieldCnt++;
        }
        if (this.getUseless46()) {
            builder.setUseless46(this.getUseless46());
            fieldCnt++;
        }  else if (builder.hasUseless46()) {
            // 清理Useless46
            builder.clearUseless46();
            fieldCnt++;
        }
        if (this.getUseless47()) {
            builder.setUseless47(this.getUseless47());
            fieldCnt++;
        }  else if (builder.hasUseless47()) {
            // 清理Useless47
            builder.clearUseless47();
            fieldCnt++;
        }
        if (this.getUseless48()) {
            builder.setUseless48(this.getUseless48());
            fieldCnt++;
        }  else if (builder.hasUseless48()) {
            // 清理Useless48
            builder.clearUseless48();
            fieldCnt++;
        }
        if (this.getUseless49()) {
            builder.setUseless49(this.getUseless49());
            fieldCnt++;
        }  else if (builder.hasUseless49()) {
            // 清理Useless49
            builder.clearUseless49();
            fieldCnt++;
        }
        if (this.getUseless50()) {
            builder.setUseless50(this.getUseless50());
            fieldCnt++;
        }  else if (builder.hasUseless50()) {
            // 清理Useless50
            builder.clearUseless50();
            fieldCnt++;
        }
        if (this.getUseless51()) {
            builder.setUseless51(this.getUseless51());
            fieldCnt++;
        }  else if (builder.hasUseless51()) {
            // 清理Useless51
            builder.clearUseless51();
            fieldCnt++;
        }
        if (this.getUseless52()) {
            builder.setUseless52(this.getUseless52());
            fieldCnt++;
        }  else if (builder.hasUseless52()) {
            // 清理Useless52
            builder.clearUseless52();
            fieldCnt++;
        }
        if (this.getUseless53()) {
            builder.setUseless53(this.getUseless53());
            fieldCnt++;
        }  else if (builder.hasUseless53()) {
            // 清理Useless53
            builder.clearUseless53();
            fieldCnt++;
        }
        if (this.getUseless54()) {
            builder.setUseless54(this.getUseless54());
            fieldCnt++;
        }  else if (builder.hasUseless54()) {
            // 清理Useless54
            builder.clearUseless54();
            fieldCnt++;
        }
        if (this.getUseless55()) {
            builder.setUseless55(this.getUseless55());
            fieldCnt++;
        }  else if (builder.hasUseless55()) {
            // 清理Useless55
            builder.clearUseless55();
            fieldCnt++;
        }
        if (this.getUseless56()) {
            builder.setUseless56(this.getUseless56());
            fieldCnt++;
        }  else if (builder.hasUseless56()) {
            // 清理Useless56
            builder.clearUseless56();
            fieldCnt++;
        }
        if (this.getUseless57()) {
            builder.setUseless57(this.getUseless57());
            fieldCnt++;
        }  else if (builder.hasUseless57()) {
            // 清理Useless57
            builder.clearUseless57();
            fieldCnt++;
        }
        if (this.getUseless58()) {
            builder.setUseless58(this.getUseless58());
            fieldCnt++;
        }  else if (builder.hasUseless58()) {
            // 清理Useless58
            builder.clearUseless58();
            fieldCnt++;
        }
        if (this.getUseless59()) {
            builder.setUseless59(this.getUseless59());
            fieldCnt++;
        }  else if (builder.hasUseless59()) {
            // 清理Useless59
            builder.clearUseless59();
            fieldCnt++;
        }
        if (this.getUseless60()) {
            builder.setUseless60(this.getUseless60());
            fieldCnt++;
        }  else if (builder.hasUseless60()) {
            // 清理Useless60
            builder.clearUseless60();
            fieldCnt++;
        }
        if (this.getUseless61()) {
            builder.setUseless61(this.getUseless61());
            fieldCnt++;
        }  else if (builder.hasUseless61()) {
            // 清理Useless61
            builder.clearUseless61();
            fieldCnt++;
        }
        if (this.getUseless62()) {
            builder.setUseless62(this.getUseless62());
            fieldCnt++;
        }  else if (builder.hasUseless62()) {
            // 清理Useless62
            builder.clearUseless62();
            fieldCnt++;
        }
        if (this.getUseless63()) {
            builder.setUseless63(this.getUseless63());
            fieldCnt++;
        }  else if (builder.hasUseless63()) {
            // 清理Useless63
            builder.clearUseless63();
            fieldCnt++;
        }
        if (this.getUseless64()) {
            builder.setUseless64(this.getUseless64());
            fieldCnt++;
        }  else if (builder.hasUseless64()) {
            // 清理Useless64
            builder.clearUseless64();
            fieldCnt++;
        }
        if (this.mapField != null) {
            YoTestPB.Int64YoTestMapMapPB.Builder tmpBuilder = YoTestPB.Int64YoTestMapMapPB.newBuilder();
            final int tmpFieldCnt = this.mapField.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMapField();
            }
        }  else if (builder.hasMapField()) {
            // 清理MapField
            builder.clearMapField();
            fieldCnt++;
        }
        if (this.listField != null) {
            YoTestPB.YoTestListListPB.Builder tmpBuilder = YoTestPB.YoTestListListPB.newBuilder();
            final int tmpFieldCnt = this.listField.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setListField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearListField();
            }
        }  else if (builder.hasListField()) {
            // 清理ListField
            builder.clearListField();
            fieldCnt++;
        }
        if (this.getUseless101()) {
            builder.setUseless101(this.getUseless101());
            fieldCnt++;
        }  else if (builder.hasUseless101()) {
            // 清理Useless101
            builder.clearUseless101();
            fieldCnt++;
        }
        if (this.getUseless102()) {
            builder.setUseless102(this.getUseless102());
            fieldCnt++;
        }  else if (builder.hasUseless102()) {
            // 清理Useless102
            builder.clearUseless102();
            fieldCnt++;
        }
        if (this.getUseless103()) {
            builder.setUseless103(this.getUseless103());
            fieldCnt++;
        }  else if (builder.hasUseless103()) {
            // 清理Useless103
            builder.clearUseless103();
            fieldCnt++;
        }
        if (this.getUseless104()) {
            builder.setUseless104(this.getUseless104());
            fieldCnt++;
        }  else if (builder.hasUseless104()) {
            // 清理Useless104
            builder.clearUseless104();
            fieldCnt++;
        }
        if (this.getUseless105()) {
            builder.setUseless105(this.getUseless105());
            fieldCnt++;
        }  else if (builder.hasUseless105()) {
            // 清理Useless105
            builder.clearUseless105();
            fieldCnt++;
        }
        if (this.getUseless106()) {
            builder.setUseless106(this.getUseless106());
            fieldCnt++;
        }  else if (builder.hasUseless106()) {
            // 清理Useless106
            builder.clearUseless106();
            fieldCnt++;
        }
        if (this.getUseless107()) {
            builder.setUseless107(this.getUseless107());
            fieldCnt++;
        }  else if (builder.hasUseless107()) {
            // 清理Useless107
            builder.clearUseless107();
            fieldCnt++;
        }
        if (this.getUseless108()) {
            builder.setUseless108(this.getUseless108());
            fieldCnt++;
        }  else if (builder.hasUseless108()) {
            // 清理Useless108
            builder.clearUseless108();
            fieldCnt++;
        }
        if (this.getUseless109()) {
            builder.setUseless109(this.getUseless109());
            fieldCnt++;
        }  else if (builder.hasUseless109()) {
            // 清理Useless109
            builder.clearUseless109();
            fieldCnt++;
        }
        if (this.getUseless110()) {
            builder.setUseless110(this.getUseless110());
            fieldCnt++;
        }  else if (builder.hasUseless110()) {
            // 清理Useless110
            builder.clearUseless110();
            fieldCnt++;
        }
        if (this.getUseless111()) {
            builder.setUseless111(this.getUseless111());
            fieldCnt++;
        }  else if (builder.hasUseless111()) {
            // 清理Useless111
            builder.clearUseless111();
            fieldCnt++;
        }
        if (this.getUseless112()) {
            builder.setUseless112(this.getUseless112());
            fieldCnt++;
        }  else if (builder.hasUseless112()) {
            // 清理Useless112
            builder.clearUseless112();
            fieldCnt++;
        }
        if (this.getUseless113()) {
            builder.setUseless113(this.getUseless113());
            fieldCnt++;
        }  else if (builder.hasUseless113()) {
            // 清理Useless113
            builder.clearUseless113();
            fieldCnt++;
        }
        if (this.getUseless114()) {
            builder.setUseless114(this.getUseless114());
            fieldCnt++;
        }  else if (builder.hasUseless114()) {
            // 清理Useless114
            builder.clearUseless114();
            fieldCnt++;
        }
        if (this.getUseless115()) {
            builder.setUseless115(this.getUseless115());
            fieldCnt++;
        }  else if (builder.hasUseless115()) {
            // 清理Useless115
            builder.clearUseless115();
            fieldCnt++;
        }
        if (this.getUseless116()) {
            builder.setUseless116(this.getUseless116());
            fieldCnt++;
        }  else if (builder.hasUseless116()) {
            // 清理Useless116
            builder.clearUseless116();
            fieldCnt++;
        }
        if (this.getUseless117()) {
            builder.setUseless117(this.getUseless117());
            fieldCnt++;
        }  else if (builder.hasUseless117()) {
            // 清理Useless117
            builder.clearUseless117();
            fieldCnt++;
        }
        if (this.getUseless118()) {
            builder.setUseless118(this.getUseless118());
            fieldCnt++;
        }  else if (builder.hasUseless118()) {
            // 清理Useless118
            builder.clearUseless118();
            fieldCnt++;
        }
        if (this.getUseless119()) {
            builder.setUseless119(this.getUseless119());
            fieldCnt++;
        }  else if (builder.hasUseless119()) {
            // 清理Useless119
            builder.clearUseless119();
            fieldCnt++;
        }
        if (this.getUseless120()) {
            builder.setUseless120(this.getUseless120());
            fieldCnt++;
        }  else if (builder.hasUseless120()) {
            // 清理Useless120
            builder.clearUseless120();
            fieldCnt++;
        }
        if (this.getUseless121()) {
            builder.setUseless121(this.getUseless121());
            fieldCnt++;
        }  else if (builder.hasUseless121()) {
            // 清理Useless121
            builder.clearUseless121();
            fieldCnt++;
        }
        if (this.getUseless122()) {
            builder.setUseless122(this.getUseless122());
            fieldCnt++;
        }  else if (builder.hasUseless122()) {
            // 清理Useless122
            builder.clearUseless122();
            fieldCnt++;
        }
        if (this.getUseless123()) {
            builder.setUseless123(this.getUseless123());
            fieldCnt++;
        }  else if (builder.hasUseless123()) {
            // 清理Useless123
            builder.clearUseless123();
            fieldCnt++;
        }
        if (this.getUseless124()) {
            builder.setUseless124(this.getUseless124());
            fieldCnt++;
        }  else if (builder.hasUseless124()) {
            // 清理Useless124
            builder.clearUseless124();
            fieldCnt++;
        }
        if (this.getUseless125()) {
            builder.setUseless125(this.getUseless125());
            fieldCnt++;
        }  else if (builder.hasUseless125()) {
            // 清理Useless125
            builder.clearUseless125();
            fieldCnt++;
        }
        if (this.getUseless126()) {
            builder.setUseless126(this.getUseless126());
            fieldCnt++;
        }  else if (builder.hasUseless126()) {
            // 清理Useless126
            builder.clearUseless126();
            fieldCnt++;
        }
        if (this.getUseless127()) {
            builder.setUseless127(this.getUseless127());
            fieldCnt++;
        }  else if (builder.hasUseless127()) {
            // 清理Useless127
            builder.clearUseless127();
            fieldCnt++;
        }
        if (this.getUseless128()) {
            builder.setUseless128(this.getUseless128());
            fieldCnt++;
        }  else if (builder.hasUseless128()) {
            // 清理Useless128
            builder.clearUseless128();
            fieldCnt++;
        }
        if (this.getUseless129()) {
            builder.setUseless129(this.getUseless129());
            fieldCnt++;
        }  else if (builder.hasUseless129()) {
            // 清理Useless129
            builder.clearUseless129();
            fieldCnt++;
        }
        if (this.getUseless130()) {
            builder.setUseless130(this.getUseless130());
            fieldCnt++;
        }  else if (builder.hasUseless130()) {
            // 清理Useless130
            builder.clearUseless130();
            fieldCnt++;
        }
        if (this.getUseless131()) {
            builder.setUseless131(this.getUseless131());
            fieldCnt++;
        }  else if (builder.hasUseless131()) {
            // 清理Useless131
            builder.clearUseless131();
            fieldCnt++;
        }
        if (this.getUseless132()) {
            builder.setUseless132(this.getUseless132());
            fieldCnt++;
        }  else if (builder.hasUseless132()) {
            // 清理Useless132
            builder.clearUseless132();
            fieldCnt++;
        }
        if (this.getUseless133()) {
            builder.setUseless133(this.getUseless133());
            fieldCnt++;
        }  else if (builder.hasUseless133()) {
            // 清理Useless133
            builder.clearUseless133();
            fieldCnt++;
        }
        if (this.getUseless134()) {
            builder.setUseless134(this.getUseless134());
            fieldCnt++;
        }  else if (builder.hasUseless134()) {
            // 清理Useless134
            builder.clearUseless134();
            fieldCnt++;
        }
        if (this.getUseless135()) {
            builder.setUseless135(this.getUseless135());
            fieldCnt++;
        }  else if (builder.hasUseless135()) {
            // 清理Useless135
            builder.clearUseless135();
            fieldCnt++;
        }
        if (this.getUseless136()) {
            builder.setUseless136(this.getUseless136());
            fieldCnt++;
        }  else if (builder.hasUseless136()) {
            // 清理Useless136
            builder.clearUseless136();
            fieldCnt++;
        }
        if (this.getUseless137()) {
            builder.setUseless137(this.getUseless137());
            fieldCnt++;
        }  else if (builder.hasUseless137()) {
            // 清理Useless137
            builder.clearUseless137();
            fieldCnt++;
        }
        if (this.getUseless138()) {
            builder.setUseless138(this.getUseless138());
            fieldCnt++;
        }  else if (builder.hasUseless138()) {
            // 清理Useless138
            builder.clearUseless138();
            fieldCnt++;
        }
        if (this.getUseless139()) {
            builder.setUseless139(this.getUseless139());
            fieldCnt++;
        }  else if (builder.hasUseless139()) {
            // 清理Useless139
            builder.clearUseless139();
            fieldCnt++;
        }
        if (this.getUseless140()) {
            builder.setUseless140(this.getUseless140());
            fieldCnt++;
        }  else if (builder.hasUseless140()) {
            // 清理Useless140
            builder.clearUseless140();
            fieldCnt++;
        }
        if (this.getUseless141()) {
            builder.setUseless141(this.getUseless141());
            fieldCnt++;
        }  else if (builder.hasUseless141()) {
            // 清理Useless141
            builder.clearUseless141();
            fieldCnt++;
        }
        if (this.getUseless142()) {
            builder.setUseless142(this.getUseless142());
            fieldCnt++;
        }  else if (builder.hasUseless142()) {
            // 清理Useless142
            builder.clearUseless142();
            fieldCnt++;
        }
        if (this.getUseless143()) {
            builder.setUseless143(this.getUseless143());
            fieldCnt++;
        }  else if (builder.hasUseless143()) {
            // 清理Useless143
            builder.clearUseless143();
            fieldCnt++;
        }
        if (this.getUseless144()) {
            builder.setUseless144(this.getUseless144());
            fieldCnt++;
        }  else if (builder.hasUseless144()) {
            // 清理Useless144
            builder.clearUseless144();
            fieldCnt++;
        }
        if (this.getUseless145()) {
            builder.setUseless145(this.getUseless145());
            fieldCnt++;
        }  else if (builder.hasUseless145()) {
            // 清理Useless145
            builder.clearUseless145();
            fieldCnt++;
        }
        if (this.getUseless146()) {
            builder.setUseless146(this.getUseless146());
            fieldCnt++;
        }  else if (builder.hasUseless146()) {
            // 清理Useless146
            builder.clearUseless146();
            fieldCnt++;
        }
        if (this.getUseless147()) {
            builder.setUseless147(this.getUseless147());
            fieldCnt++;
        }  else if (builder.hasUseless147()) {
            // 清理Useless147
            builder.clearUseless147();
            fieldCnt++;
        }
        if (this.getUseless148()) {
            builder.setUseless148(this.getUseless148());
            fieldCnt++;
        }  else if (builder.hasUseless148()) {
            // 清理Useless148
            builder.clearUseless148();
            fieldCnt++;
        }
        if (this.getUseless149()) {
            builder.setUseless149(this.getUseless149());
            fieldCnt++;
        }  else if (builder.hasUseless149()) {
            // 清理Useless149
            builder.clearUseless149();
            fieldCnt++;
        }
        if (this.getUseless150()) {
            builder.setUseless150(this.getUseless150());
            fieldCnt++;
        }  else if (builder.hasUseless150()) {
            // 清理Useless150
            builder.clearUseless150();
            fieldCnt++;
        }
        if (this.getUseless151()) {
            builder.setUseless151(this.getUseless151());
            fieldCnt++;
        }  else if (builder.hasUseless151()) {
            // 清理Useless151
            builder.clearUseless151();
            fieldCnt++;
        }
        if (this.getUseless152()) {
            builder.setUseless152(this.getUseless152());
            fieldCnt++;
        }  else if (builder.hasUseless152()) {
            // 清理Useless152
            builder.clearUseless152();
            fieldCnt++;
        }
        if (this.getUseless153()) {
            builder.setUseless153(this.getUseless153());
            fieldCnt++;
        }  else if (builder.hasUseless153()) {
            // 清理Useless153
            builder.clearUseless153();
            fieldCnt++;
        }
        if (this.getUseless154()) {
            builder.setUseless154(this.getUseless154());
            fieldCnt++;
        }  else if (builder.hasUseless154()) {
            // 清理Useless154
            builder.clearUseless154();
            fieldCnt++;
        }
        if (this.getUseless155()) {
            builder.setUseless155(this.getUseless155());
            fieldCnt++;
        }  else if (builder.hasUseless155()) {
            // 清理Useless155
            builder.clearUseless155();
            fieldCnt++;
        }
        if (this.getUseless156()) {
            builder.setUseless156(this.getUseless156());
            fieldCnt++;
        }  else if (builder.hasUseless156()) {
            // 清理Useless156
            builder.clearUseless156();
            fieldCnt++;
        }
        if (this.getUseless157()) {
            builder.setUseless157(this.getUseless157());
            fieldCnt++;
        }  else if (builder.hasUseless157()) {
            // 清理Useless157
            builder.clearUseless157();
            fieldCnt++;
        }
        if (this.getUseless158()) {
            builder.setUseless158(this.getUseless158());
            fieldCnt++;
        }  else if (builder.hasUseless158()) {
            // 清理Useless158
            builder.clearUseless158();
            fieldCnt++;
        }
        if (this.getUseless159()) {
            builder.setUseless159(this.getUseless159());
            fieldCnt++;
        }  else if (builder.hasUseless159()) {
            // 清理Useless159
            builder.clearUseless159();
            fieldCnt++;
        }
        if (this.getUseless160()) {
            builder.setUseless160(this.getUseless160());
            fieldCnt++;
        }  else if (builder.hasUseless160()) {
            // 清理Useless160
            builder.clearUseless160();
            fieldCnt++;
        }
        if (this.getUseless161()) {
            builder.setUseless161(this.getUseless161());
            fieldCnt++;
        }  else if (builder.hasUseless161()) {
            // 清理Useless161
            builder.clearUseless161();
            fieldCnt++;
        }
        if (this.getUseless162()) {
            builder.setUseless162(this.getUseless162());
            fieldCnt++;
        }  else if (builder.hasUseless162()) {
            // 清理Useless162
            builder.clearUseless162();
            fieldCnt++;
        }
        if (this.getUseless163()) {
            builder.setUseless163(this.getUseless163());
            fieldCnt++;
        }  else if (builder.hasUseless163()) {
            // 清理Useless163
            builder.clearUseless163();
            fieldCnt++;
        }
        if (this.getUseless164()) {
            builder.setUseless164(this.getUseless164());
            fieldCnt++;
        }  else if (builder.hasUseless164()) {
            // 清理Useless164
            builder.clearUseless164();
            fieldCnt++;
        }
        if (this.yoTestModel != null) {
            YoTestPB.YoTestModelPB.Builder tmpBuilder = YoTestPB.YoTestModelPB.newBuilder();
            final int tmpFieldCnt = this.yoTestModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYoTestModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYoTestModel();
            }
        }  else if (builder.hasYoTestModel()) {
            // 清理YoTestModel
            builder.clearYoTestModel();
            fieldCnt++;
        }
        if (this.yoTestUnit != null) {
            YoTestPB.YoTestUnitPB.Builder tmpBuilder = YoTestPB.YoTestUnitPB.newBuilder();
            final int tmpFieldCnt = this.yoTestUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYoTestUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYoTestUnit();
            }
        }  else if (builder.hasYoTestUnit()) {
            // 清理YoTestUnit
            builder.clearYoTestUnit();
            fieldCnt++;
        }
        if (this.setField != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.setField.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSetField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSetField();
            }
        }  else if (builder.hasSetField()) {
            // 清理SetField
            builder.clearSetField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoTestEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS1)) {
            builder.setUseless1(this.getUseless1());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS2)) {
            builder.setUseless2(this.getUseless2());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS3)) {
            builder.setUseless3(this.getUseless3());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS4)) {
            builder.setUseless4(this.getUseless4());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS5)) {
            builder.setUseless5(this.getUseless5());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS6)) {
            builder.setUseless6(this.getUseless6());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS7)) {
            builder.setUseless7(this.getUseless7());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS8)) {
            builder.setUseless8(this.getUseless8());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS9)) {
            builder.setUseless9(this.getUseless9());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS10)) {
            builder.setUseless10(this.getUseless10());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS11)) {
            builder.setUseless11(this.getUseless11());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS12)) {
            builder.setUseless12(this.getUseless12());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS13)) {
            builder.setUseless13(this.getUseless13());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS14)) {
            builder.setUseless14(this.getUseless14());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS15)) {
            builder.setUseless15(this.getUseless15());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS16)) {
            builder.setUseless16(this.getUseless16());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS17)) {
            builder.setUseless17(this.getUseless17());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS18)) {
            builder.setUseless18(this.getUseless18());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS19)) {
            builder.setUseless19(this.getUseless19());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS20)) {
            builder.setUseless20(this.getUseless20());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS21)) {
            builder.setUseless21(this.getUseless21());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS22)) {
            builder.setUseless22(this.getUseless22());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS23)) {
            builder.setUseless23(this.getUseless23());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS24)) {
            builder.setUseless24(this.getUseless24());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS25)) {
            builder.setUseless25(this.getUseless25());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS26)) {
            builder.setUseless26(this.getUseless26());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS27)) {
            builder.setUseless27(this.getUseless27());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS28)) {
            builder.setUseless28(this.getUseless28());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS29)) {
            builder.setUseless29(this.getUseless29());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS30)) {
            builder.setUseless30(this.getUseless30());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS31)) {
            builder.setUseless31(this.getUseless31());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS32)) {
            builder.setUseless32(this.getUseless32());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS33)) {
            builder.setUseless33(this.getUseless33());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS34)) {
            builder.setUseless34(this.getUseless34());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS35)) {
            builder.setUseless35(this.getUseless35());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS36)) {
            builder.setUseless36(this.getUseless36());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS37)) {
            builder.setUseless37(this.getUseless37());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS38)) {
            builder.setUseless38(this.getUseless38());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS39)) {
            builder.setUseless39(this.getUseless39());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS40)) {
            builder.setUseless40(this.getUseless40());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS41)) {
            builder.setUseless41(this.getUseless41());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS42)) {
            builder.setUseless42(this.getUseless42());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS43)) {
            builder.setUseless43(this.getUseless43());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS44)) {
            builder.setUseless44(this.getUseless44());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS45)) {
            builder.setUseless45(this.getUseless45());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS46)) {
            builder.setUseless46(this.getUseless46());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS47)) {
            builder.setUseless47(this.getUseless47());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS48)) {
            builder.setUseless48(this.getUseless48());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS49)) {
            builder.setUseless49(this.getUseless49());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS50)) {
            builder.setUseless50(this.getUseless50());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS51)) {
            builder.setUseless51(this.getUseless51());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS52)) {
            builder.setUseless52(this.getUseless52());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS53)) {
            builder.setUseless53(this.getUseless53());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS54)) {
            builder.setUseless54(this.getUseless54());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS55)) {
            builder.setUseless55(this.getUseless55());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS56)) {
            builder.setUseless56(this.getUseless56());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS57)) {
            builder.setUseless57(this.getUseless57());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS58)) {
            builder.setUseless58(this.getUseless58());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS59)) {
            builder.setUseless59(this.getUseless59());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS60)) {
            builder.setUseless60(this.getUseless60());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS61)) {
            builder.setUseless61(this.getUseless61());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS62)) {
            builder.setUseless62(this.getUseless62());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS63)) {
            builder.setUseless63(this.getUseless63());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS64)) {
            builder.setUseless64(this.getUseless64());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPFIELD) && this.mapField != null) {
            final boolean needClear = !builder.hasMapField();
            final int tmpFieldCnt = this.mapField.copyChangeToCs(builder.getMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMapField();
            }
        }
        if (this.hasMark(FIELD_INDEX_LISTFIELD) && this.listField != null) {
            final boolean needClear = !builder.hasListField();
            final int tmpFieldCnt = this.listField.copyChangeToCs(builder.getListFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearListField();
            }
        }
        if (this.hasMark(FIELD_INDEX_USELESS101)) {
            builder.setUseless101(this.getUseless101());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS102)) {
            builder.setUseless102(this.getUseless102());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS103)) {
            builder.setUseless103(this.getUseless103());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS104)) {
            builder.setUseless104(this.getUseless104());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS105)) {
            builder.setUseless105(this.getUseless105());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS106)) {
            builder.setUseless106(this.getUseless106());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS107)) {
            builder.setUseless107(this.getUseless107());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS108)) {
            builder.setUseless108(this.getUseless108());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS109)) {
            builder.setUseless109(this.getUseless109());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS110)) {
            builder.setUseless110(this.getUseless110());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS111)) {
            builder.setUseless111(this.getUseless111());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS112)) {
            builder.setUseless112(this.getUseless112());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS113)) {
            builder.setUseless113(this.getUseless113());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS114)) {
            builder.setUseless114(this.getUseless114());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS115)) {
            builder.setUseless115(this.getUseless115());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS116)) {
            builder.setUseless116(this.getUseless116());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS117)) {
            builder.setUseless117(this.getUseless117());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS118)) {
            builder.setUseless118(this.getUseless118());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS119)) {
            builder.setUseless119(this.getUseless119());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS120)) {
            builder.setUseless120(this.getUseless120());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS121)) {
            builder.setUseless121(this.getUseless121());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS122)) {
            builder.setUseless122(this.getUseless122());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS123)) {
            builder.setUseless123(this.getUseless123());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS124)) {
            builder.setUseless124(this.getUseless124());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS125)) {
            builder.setUseless125(this.getUseless125());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS126)) {
            builder.setUseless126(this.getUseless126());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS127)) {
            builder.setUseless127(this.getUseless127());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS128)) {
            builder.setUseless128(this.getUseless128());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS129)) {
            builder.setUseless129(this.getUseless129());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS130)) {
            builder.setUseless130(this.getUseless130());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS131)) {
            builder.setUseless131(this.getUseless131());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS132)) {
            builder.setUseless132(this.getUseless132());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS133)) {
            builder.setUseless133(this.getUseless133());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS134)) {
            builder.setUseless134(this.getUseless134());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS135)) {
            builder.setUseless135(this.getUseless135());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS136)) {
            builder.setUseless136(this.getUseless136());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS137)) {
            builder.setUseless137(this.getUseless137());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS138)) {
            builder.setUseless138(this.getUseless138());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS139)) {
            builder.setUseless139(this.getUseless139());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS140)) {
            builder.setUseless140(this.getUseless140());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS141)) {
            builder.setUseless141(this.getUseless141());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS142)) {
            builder.setUseless142(this.getUseless142());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS143)) {
            builder.setUseless143(this.getUseless143());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS144)) {
            builder.setUseless144(this.getUseless144());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS145)) {
            builder.setUseless145(this.getUseless145());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS146)) {
            builder.setUseless146(this.getUseless146());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS147)) {
            builder.setUseless147(this.getUseless147());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS148)) {
            builder.setUseless148(this.getUseless148());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS149)) {
            builder.setUseless149(this.getUseless149());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS150)) {
            builder.setUseless150(this.getUseless150());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS151)) {
            builder.setUseless151(this.getUseless151());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS152)) {
            builder.setUseless152(this.getUseless152());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS153)) {
            builder.setUseless153(this.getUseless153());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS154)) {
            builder.setUseless154(this.getUseless154());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS155)) {
            builder.setUseless155(this.getUseless155());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS156)) {
            builder.setUseless156(this.getUseless156());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS157)) {
            builder.setUseless157(this.getUseless157());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS158)) {
            builder.setUseless158(this.getUseless158());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS159)) {
            builder.setUseless159(this.getUseless159());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS160)) {
            builder.setUseless160(this.getUseless160());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS161)) {
            builder.setUseless161(this.getUseless161());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS162)) {
            builder.setUseless162(this.getUseless162());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS163)) {
            builder.setUseless163(this.getUseless163());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS164)) {
            builder.setUseless164(this.getUseless164());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YOTESTMODEL) && this.yoTestModel != null) {
            final boolean needClear = !builder.hasYoTestModel();
            final int tmpFieldCnt = this.yoTestModel.copyChangeToCs(builder.getYoTestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_YOTESTUNIT) && this.yoTestUnit != null) {
            final boolean needClear = !builder.hasYoTestUnit();
            final int tmpFieldCnt = this.yoTestUnit.copyChangeToCs(builder.getYoTestUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETFIELD) && this.setField != null) {
            final boolean needClear = !builder.hasSetField();
            final int tmpFieldCnt = this.setField.copyChangeToCs(builder.getSetFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSetField();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoTestEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS1)) {
            builder.setUseless1(this.getUseless1());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS2)) {
            builder.setUseless2(this.getUseless2());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS3)) {
            builder.setUseless3(this.getUseless3());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS4)) {
            builder.setUseless4(this.getUseless4());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS5)) {
            builder.setUseless5(this.getUseless5());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS6)) {
            builder.setUseless6(this.getUseless6());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS7)) {
            builder.setUseless7(this.getUseless7());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS8)) {
            builder.setUseless8(this.getUseless8());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS9)) {
            builder.setUseless9(this.getUseless9());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS10)) {
            builder.setUseless10(this.getUseless10());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS11)) {
            builder.setUseless11(this.getUseless11());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS12)) {
            builder.setUseless12(this.getUseless12());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS13)) {
            builder.setUseless13(this.getUseless13());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS14)) {
            builder.setUseless14(this.getUseless14());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS15)) {
            builder.setUseless15(this.getUseless15());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS16)) {
            builder.setUseless16(this.getUseless16());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS17)) {
            builder.setUseless17(this.getUseless17());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS18)) {
            builder.setUseless18(this.getUseless18());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS19)) {
            builder.setUseless19(this.getUseless19());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS20)) {
            builder.setUseless20(this.getUseless20());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS21)) {
            builder.setUseless21(this.getUseless21());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS22)) {
            builder.setUseless22(this.getUseless22());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS23)) {
            builder.setUseless23(this.getUseless23());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS24)) {
            builder.setUseless24(this.getUseless24());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS25)) {
            builder.setUseless25(this.getUseless25());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS26)) {
            builder.setUseless26(this.getUseless26());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS27)) {
            builder.setUseless27(this.getUseless27());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS28)) {
            builder.setUseless28(this.getUseless28());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS29)) {
            builder.setUseless29(this.getUseless29());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS30)) {
            builder.setUseless30(this.getUseless30());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS31)) {
            builder.setUseless31(this.getUseless31());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS32)) {
            builder.setUseless32(this.getUseless32());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS33)) {
            builder.setUseless33(this.getUseless33());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS34)) {
            builder.setUseless34(this.getUseless34());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS35)) {
            builder.setUseless35(this.getUseless35());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS36)) {
            builder.setUseless36(this.getUseless36());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS37)) {
            builder.setUseless37(this.getUseless37());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS38)) {
            builder.setUseless38(this.getUseless38());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS39)) {
            builder.setUseless39(this.getUseless39());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS40)) {
            builder.setUseless40(this.getUseless40());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS41)) {
            builder.setUseless41(this.getUseless41());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS42)) {
            builder.setUseless42(this.getUseless42());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS43)) {
            builder.setUseless43(this.getUseless43());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS44)) {
            builder.setUseless44(this.getUseless44());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS45)) {
            builder.setUseless45(this.getUseless45());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS46)) {
            builder.setUseless46(this.getUseless46());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS47)) {
            builder.setUseless47(this.getUseless47());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS48)) {
            builder.setUseless48(this.getUseless48());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS49)) {
            builder.setUseless49(this.getUseless49());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS50)) {
            builder.setUseless50(this.getUseless50());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS51)) {
            builder.setUseless51(this.getUseless51());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS52)) {
            builder.setUseless52(this.getUseless52());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS53)) {
            builder.setUseless53(this.getUseless53());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS54)) {
            builder.setUseless54(this.getUseless54());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS55)) {
            builder.setUseless55(this.getUseless55());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS56)) {
            builder.setUseless56(this.getUseless56());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS57)) {
            builder.setUseless57(this.getUseless57());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS58)) {
            builder.setUseless58(this.getUseless58());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS59)) {
            builder.setUseless59(this.getUseless59());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS60)) {
            builder.setUseless60(this.getUseless60());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS61)) {
            builder.setUseless61(this.getUseless61());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS62)) {
            builder.setUseless62(this.getUseless62());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS63)) {
            builder.setUseless63(this.getUseless63());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS64)) {
            builder.setUseless64(this.getUseless64());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPFIELD) && this.mapField != null) {
            final boolean needClear = !builder.hasMapField();
            final int tmpFieldCnt = this.mapField.copyChangeToAndClearDeleteKeysCs(builder.getMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMapField();
            }
        }
        if (this.hasMark(FIELD_INDEX_LISTFIELD) && this.listField != null) {
            final boolean needClear = !builder.hasListField();
            final int tmpFieldCnt = this.listField.copyChangeToCs(builder.getListFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearListField();
            }
        }
        if (this.hasMark(FIELD_INDEX_USELESS101)) {
            builder.setUseless101(this.getUseless101());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS102)) {
            builder.setUseless102(this.getUseless102());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS103)) {
            builder.setUseless103(this.getUseless103());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS104)) {
            builder.setUseless104(this.getUseless104());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS105)) {
            builder.setUseless105(this.getUseless105());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS106)) {
            builder.setUseless106(this.getUseless106());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS107)) {
            builder.setUseless107(this.getUseless107());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS108)) {
            builder.setUseless108(this.getUseless108());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS109)) {
            builder.setUseless109(this.getUseless109());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS110)) {
            builder.setUseless110(this.getUseless110());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS111)) {
            builder.setUseless111(this.getUseless111());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS112)) {
            builder.setUseless112(this.getUseless112());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS113)) {
            builder.setUseless113(this.getUseless113());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS114)) {
            builder.setUseless114(this.getUseless114());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS115)) {
            builder.setUseless115(this.getUseless115());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS116)) {
            builder.setUseless116(this.getUseless116());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS117)) {
            builder.setUseless117(this.getUseless117());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS118)) {
            builder.setUseless118(this.getUseless118());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS119)) {
            builder.setUseless119(this.getUseless119());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS120)) {
            builder.setUseless120(this.getUseless120());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS121)) {
            builder.setUseless121(this.getUseless121());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS122)) {
            builder.setUseless122(this.getUseless122());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS123)) {
            builder.setUseless123(this.getUseless123());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS124)) {
            builder.setUseless124(this.getUseless124());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS125)) {
            builder.setUseless125(this.getUseless125());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS126)) {
            builder.setUseless126(this.getUseless126());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS127)) {
            builder.setUseless127(this.getUseless127());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS128)) {
            builder.setUseless128(this.getUseless128());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS129)) {
            builder.setUseless129(this.getUseless129());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS130)) {
            builder.setUseless130(this.getUseless130());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS131)) {
            builder.setUseless131(this.getUseless131());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS132)) {
            builder.setUseless132(this.getUseless132());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS133)) {
            builder.setUseless133(this.getUseless133());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS134)) {
            builder.setUseless134(this.getUseless134());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS135)) {
            builder.setUseless135(this.getUseless135());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS136)) {
            builder.setUseless136(this.getUseless136());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS137)) {
            builder.setUseless137(this.getUseless137());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS138)) {
            builder.setUseless138(this.getUseless138());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS139)) {
            builder.setUseless139(this.getUseless139());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS140)) {
            builder.setUseless140(this.getUseless140());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS141)) {
            builder.setUseless141(this.getUseless141());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS142)) {
            builder.setUseless142(this.getUseless142());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS143)) {
            builder.setUseless143(this.getUseless143());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS144)) {
            builder.setUseless144(this.getUseless144());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS145)) {
            builder.setUseless145(this.getUseless145());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS146)) {
            builder.setUseless146(this.getUseless146());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS147)) {
            builder.setUseless147(this.getUseless147());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS148)) {
            builder.setUseless148(this.getUseless148());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS149)) {
            builder.setUseless149(this.getUseless149());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS150)) {
            builder.setUseless150(this.getUseless150());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS151)) {
            builder.setUseless151(this.getUseless151());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS152)) {
            builder.setUseless152(this.getUseless152());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS153)) {
            builder.setUseless153(this.getUseless153());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS154)) {
            builder.setUseless154(this.getUseless154());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS155)) {
            builder.setUseless155(this.getUseless155());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS156)) {
            builder.setUseless156(this.getUseless156());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS157)) {
            builder.setUseless157(this.getUseless157());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS158)) {
            builder.setUseless158(this.getUseless158());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS159)) {
            builder.setUseless159(this.getUseless159());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS160)) {
            builder.setUseless160(this.getUseless160());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS161)) {
            builder.setUseless161(this.getUseless161());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS162)) {
            builder.setUseless162(this.getUseless162());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS163)) {
            builder.setUseless163(this.getUseless163());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS164)) {
            builder.setUseless164(this.getUseless164());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YOTESTMODEL) && this.yoTestModel != null) {
            final boolean needClear = !builder.hasYoTestModel();
            final int tmpFieldCnt = this.yoTestModel.copyChangeToAndClearDeleteKeysCs(builder.getYoTestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_YOTESTUNIT) && this.yoTestUnit != null) {
            final boolean needClear = !builder.hasYoTestUnit();
            final int tmpFieldCnt = this.yoTestUnit.copyChangeToAndClearDeleteKeysCs(builder.getYoTestUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETFIELD) && this.setField != null) {
            final boolean needClear = !builder.hasSetField();
            final int tmpFieldCnt = this.setField.copyChangeToAndClearDeleteKeysCs(builder.getSetFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSetField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoTestEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntField()) {
            this.innerSetIntField(proto.getIntField());
        } else {
            this.innerSetIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLongField()) {
            this.innerSetLongField(proto.getLongField());
        } else {
            this.innerSetLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStringField()) {
            this.innerSetStringField(proto.getStringField());
        } else {
            this.innerSetStringField(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasBoolField()) {
            this.innerSetBoolField(proto.getBoolField());
        } else {
            this.innerSetBoolField(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless1()) {
            this.innerSetUseless1(proto.getUseless1());
        } else {
            this.innerSetUseless1(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless2()) {
            this.innerSetUseless2(proto.getUseless2());
        } else {
            this.innerSetUseless2(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless3()) {
            this.innerSetUseless3(proto.getUseless3());
        } else {
            this.innerSetUseless3(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless4()) {
            this.innerSetUseless4(proto.getUseless4());
        } else {
            this.innerSetUseless4(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless5()) {
            this.innerSetUseless5(proto.getUseless5());
        } else {
            this.innerSetUseless5(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless6()) {
            this.innerSetUseless6(proto.getUseless6());
        } else {
            this.innerSetUseless6(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless7()) {
            this.innerSetUseless7(proto.getUseless7());
        } else {
            this.innerSetUseless7(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless8()) {
            this.innerSetUseless8(proto.getUseless8());
        } else {
            this.innerSetUseless8(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless9()) {
            this.innerSetUseless9(proto.getUseless9());
        } else {
            this.innerSetUseless9(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless10()) {
            this.innerSetUseless10(proto.getUseless10());
        } else {
            this.innerSetUseless10(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless11()) {
            this.innerSetUseless11(proto.getUseless11());
        } else {
            this.innerSetUseless11(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless12()) {
            this.innerSetUseless12(proto.getUseless12());
        } else {
            this.innerSetUseless12(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless13()) {
            this.innerSetUseless13(proto.getUseless13());
        } else {
            this.innerSetUseless13(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless14()) {
            this.innerSetUseless14(proto.getUseless14());
        } else {
            this.innerSetUseless14(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless15()) {
            this.innerSetUseless15(proto.getUseless15());
        } else {
            this.innerSetUseless15(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless16()) {
            this.innerSetUseless16(proto.getUseless16());
        } else {
            this.innerSetUseless16(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless17()) {
            this.innerSetUseless17(proto.getUseless17());
        } else {
            this.innerSetUseless17(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless18()) {
            this.innerSetUseless18(proto.getUseless18());
        } else {
            this.innerSetUseless18(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless19()) {
            this.innerSetUseless19(proto.getUseless19());
        } else {
            this.innerSetUseless19(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless20()) {
            this.innerSetUseless20(proto.getUseless20());
        } else {
            this.innerSetUseless20(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless21()) {
            this.innerSetUseless21(proto.getUseless21());
        } else {
            this.innerSetUseless21(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless22()) {
            this.innerSetUseless22(proto.getUseless22());
        } else {
            this.innerSetUseless22(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless23()) {
            this.innerSetUseless23(proto.getUseless23());
        } else {
            this.innerSetUseless23(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless24()) {
            this.innerSetUseless24(proto.getUseless24());
        } else {
            this.innerSetUseless24(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless25()) {
            this.innerSetUseless25(proto.getUseless25());
        } else {
            this.innerSetUseless25(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless26()) {
            this.innerSetUseless26(proto.getUseless26());
        } else {
            this.innerSetUseless26(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless27()) {
            this.innerSetUseless27(proto.getUseless27());
        } else {
            this.innerSetUseless27(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless28()) {
            this.innerSetUseless28(proto.getUseless28());
        } else {
            this.innerSetUseless28(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless29()) {
            this.innerSetUseless29(proto.getUseless29());
        } else {
            this.innerSetUseless29(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless30()) {
            this.innerSetUseless30(proto.getUseless30());
        } else {
            this.innerSetUseless30(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless31()) {
            this.innerSetUseless31(proto.getUseless31());
        } else {
            this.innerSetUseless31(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless32()) {
            this.innerSetUseless32(proto.getUseless32());
        } else {
            this.innerSetUseless32(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless33()) {
            this.innerSetUseless33(proto.getUseless33());
        } else {
            this.innerSetUseless33(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless34()) {
            this.innerSetUseless34(proto.getUseless34());
        } else {
            this.innerSetUseless34(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless35()) {
            this.innerSetUseless35(proto.getUseless35());
        } else {
            this.innerSetUseless35(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless36()) {
            this.innerSetUseless36(proto.getUseless36());
        } else {
            this.innerSetUseless36(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless37()) {
            this.innerSetUseless37(proto.getUseless37());
        } else {
            this.innerSetUseless37(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless38()) {
            this.innerSetUseless38(proto.getUseless38());
        } else {
            this.innerSetUseless38(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless39()) {
            this.innerSetUseless39(proto.getUseless39());
        } else {
            this.innerSetUseless39(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless40()) {
            this.innerSetUseless40(proto.getUseless40());
        } else {
            this.innerSetUseless40(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless41()) {
            this.innerSetUseless41(proto.getUseless41());
        } else {
            this.innerSetUseless41(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless42()) {
            this.innerSetUseless42(proto.getUseless42());
        } else {
            this.innerSetUseless42(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless43()) {
            this.innerSetUseless43(proto.getUseless43());
        } else {
            this.innerSetUseless43(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless44()) {
            this.innerSetUseless44(proto.getUseless44());
        } else {
            this.innerSetUseless44(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless45()) {
            this.innerSetUseless45(proto.getUseless45());
        } else {
            this.innerSetUseless45(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless46()) {
            this.innerSetUseless46(proto.getUseless46());
        } else {
            this.innerSetUseless46(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless47()) {
            this.innerSetUseless47(proto.getUseless47());
        } else {
            this.innerSetUseless47(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless48()) {
            this.innerSetUseless48(proto.getUseless48());
        } else {
            this.innerSetUseless48(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless49()) {
            this.innerSetUseless49(proto.getUseless49());
        } else {
            this.innerSetUseless49(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless50()) {
            this.innerSetUseless50(proto.getUseless50());
        } else {
            this.innerSetUseless50(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless51()) {
            this.innerSetUseless51(proto.getUseless51());
        } else {
            this.innerSetUseless51(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless52()) {
            this.innerSetUseless52(proto.getUseless52());
        } else {
            this.innerSetUseless52(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless53()) {
            this.innerSetUseless53(proto.getUseless53());
        } else {
            this.innerSetUseless53(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless54()) {
            this.innerSetUseless54(proto.getUseless54());
        } else {
            this.innerSetUseless54(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless55()) {
            this.innerSetUseless55(proto.getUseless55());
        } else {
            this.innerSetUseless55(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless56()) {
            this.innerSetUseless56(proto.getUseless56());
        } else {
            this.innerSetUseless56(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless57()) {
            this.innerSetUseless57(proto.getUseless57());
        } else {
            this.innerSetUseless57(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless58()) {
            this.innerSetUseless58(proto.getUseless58());
        } else {
            this.innerSetUseless58(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless59()) {
            this.innerSetUseless59(proto.getUseless59());
        } else {
            this.innerSetUseless59(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless60()) {
            this.innerSetUseless60(proto.getUseless60());
        } else {
            this.innerSetUseless60(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless61()) {
            this.innerSetUseless61(proto.getUseless61());
        } else {
            this.innerSetUseless61(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless62()) {
            this.innerSetUseless62(proto.getUseless62());
        } else {
            this.innerSetUseless62(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless63()) {
            this.innerSetUseless63(proto.getUseless63());
        } else {
            this.innerSetUseless63(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless64()) {
            this.innerSetUseless64(proto.getUseless64());
        } else {
            this.innerSetUseless64(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasMapField()) {
            this.getMapField().mergeFromCs(proto.getMapField());
        } else {
            if (this.mapField != null) {
                this.mapField.mergeFromCs(proto.getMapField());
            }
        }
        if (proto.hasListField()) {
            this.getListField().mergeFromCs(proto.getListField());
        } else {
            if (this.listField != null) {
                this.listField.mergeFromCs(proto.getListField());
            }
        }
        if (proto.hasUseless101()) {
            this.innerSetUseless101(proto.getUseless101());
        } else {
            this.innerSetUseless101(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless102()) {
            this.innerSetUseless102(proto.getUseless102());
        } else {
            this.innerSetUseless102(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless103()) {
            this.innerSetUseless103(proto.getUseless103());
        } else {
            this.innerSetUseless103(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless104()) {
            this.innerSetUseless104(proto.getUseless104());
        } else {
            this.innerSetUseless104(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless105()) {
            this.innerSetUseless105(proto.getUseless105());
        } else {
            this.innerSetUseless105(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless106()) {
            this.innerSetUseless106(proto.getUseless106());
        } else {
            this.innerSetUseless106(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless107()) {
            this.innerSetUseless107(proto.getUseless107());
        } else {
            this.innerSetUseless107(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless108()) {
            this.innerSetUseless108(proto.getUseless108());
        } else {
            this.innerSetUseless108(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless109()) {
            this.innerSetUseless109(proto.getUseless109());
        } else {
            this.innerSetUseless109(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless110()) {
            this.innerSetUseless110(proto.getUseless110());
        } else {
            this.innerSetUseless110(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless111()) {
            this.innerSetUseless111(proto.getUseless111());
        } else {
            this.innerSetUseless111(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless112()) {
            this.innerSetUseless112(proto.getUseless112());
        } else {
            this.innerSetUseless112(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless113()) {
            this.innerSetUseless113(proto.getUseless113());
        } else {
            this.innerSetUseless113(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless114()) {
            this.innerSetUseless114(proto.getUseless114());
        } else {
            this.innerSetUseless114(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless115()) {
            this.innerSetUseless115(proto.getUseless115());
        } else {
            this.innerSetUseless115(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless116()) {
            this.innerSetUseless116(proto.getUseless116());
        } else {
            this.innerSetUseless116(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless117()) {
            this.innerSetUseless117(proto.getUseless117());
        } else {
            this.innerSetUseless117(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless118()) {
            this.innerSetUseless118(proto.getUseless118());
        } else {
            this.innerSetUseless118(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless119()) {
            this.innerSetUseless119(proto.getUseless119());
        } else {
            this.innerSetUseless119(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless120()) {
            this.innerSetUseless120(proto.getUseless120());
        } else {
            this.innerSetUseless120(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless121()) {
            this.innerSetUseless121(proto.getUseless121());
        } else {
            this.innerSetUseless121(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless122()) {
            this.innerSetUseless122(proto.getUseless122());
        } else {
            this.innerSetUseless122(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless123()) {
            this.innerSetUseless123(proto.getUseless123());
        } else {
            this.innerSetUseless123(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless124()) {
            this.innerSetUseless124(proto.getUseless124());
        } else {
            this.innerSetUseless124(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless125()) {
            this.innerSetUseless125(proto.getUseless125());
        } else {
            this.innerSetUseless125(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless126()) {
            this.innerSetUseless126(proto.getUseless126());
        } else {
            this.innerSetUseless126(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless127()) {
            this.innerSetUseless127(proto.getUseless127());
        } else {
            this.innerSetUseless127(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless128()) {
            this.innerSetUseless128(proto.getUseless128());
        } else {
            this.innerSetUseless128(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless129()) {
            this.innerSetUseless129(proto.getUseless129());
        } else {
            this.innerSetUseless129(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless130()) {
            this.innerSetUseless130(proto.getUseless130());
        } else {
            this.innerSetUseless130(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless131()) {
            this.innerSetUseless131(proto.getUseless131());
        } else {
            this.innerSetUseless131(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless132()) {
            this.innerSetUseless132(proto.getUseless132());
        } else {
            this.innerSetUseless132(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless133()) {
            this.innerSetUseless133(proto.getUseless133());
        } else {
            this.innerSetUseless133(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless134()) {
            this.innerSetUseless134(proto.getUseless134());
        } else {
            this.innerSetUseless134(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless135()) {
            this.innerSetUseless135(proto.getUseless135());
        } else {
            this.innerSetUseless135(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless136()) {
            this.innerSetUseless136(proto.getUseless136());
        } else {
            this.innerSetUseless136(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless137()) {
            this.innerSetUseless137(proto.getUseless137());
        } else {
            this.innerSetUseless137(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless138()) {
            this.innerSetUseless138(proto.getUseless138());
        } else {
            this.innerSetUseless138(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless139()) {
            this.innerSetUseless139(proto.getUseless139());
        } else {
            this.innerSetUseless139(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless140()) {
            this.innerSetUseless140(proto.getUseless140());
        } else {
            this.innerSetUseless140(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless141()) {
            this.innerSetUseless141(proto.getUseless141());
        } else {
            this.innerSetUseless141(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless142()) {
            this.innerSetUseless142(proto.getUseless142());
        } else {
            this.innerSetUseless142(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless143()) {
            this.innerSetUseless143(proto.getUseless143());
        } else {
            this.innerSetUseless143(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless144()) {
            this.innerSetUseless144(proto.getUseless144());
        } else {
            this.innerSetUseless144(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless145()) {
            this.innerSetUseless145(proto.getUseless145());
        } else {
            this.innerSetUseless145(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless146()) {
            this.innerSetUseless146(proto.getUseless146());
        } else {
            this.innerSetUseless146(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless147()) {
            this.innerSetUseless147(proto.getUseless147());
        } else {
            this.innerSetUseless147(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless148()) {
            this.innerSetUseless148(proto.getUseless148());
        } else {
            this.innerSetUseless148(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless149()) {
            this.innerSetUseless149(proto.getUseless149());
        } else {
            this.innerSetUseless149(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless150()) {
            this.innerSetUseless150(proto.getUseless150());
        } else {
            this.innerSetUseless150(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless151()) {
            this.innerSetUseless151(proto.getUseless151());
        } else {
            this.innerSetUseless151(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless152()) {
            this.innerSetUseless152(proto.getUseless152());
        } else {
            this.innerSetUseless152(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless153()) {
            this.innerSetUseless153(proto.getUseless153());
        } else {
            this.innerSetUseless153(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless154()) {
            this.innerSetUseless154(proto.getUseless154());
        } else {
            this.innerSetUseless154(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless155()) {
            this.innerSetUseless155(proto.getUseless155());
        } else {
            this.innerSetUseless155(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless156()) {
            this.innerSetUseless156(proto.getUseless156());
        } else {
            this.innerSetUseless156(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless157()) {
            this.innerSetUseless157(proto.getUseless157());
        } else {
            this.innerSetUseless157(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless158()) {
            this.innerSetUseless158(proto.getUseless158());
        } else {
            this.innerSetUseless158(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless159()) {
            this.innerSetUseless159(proto.getUseless159());
        } else {
            this.innerSetUseless159(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless160()) {
            this.innerSetUseless160(proto.getUseless160());
        } else {
            this.innerSetUseless160(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless161()) {
            this.innerSetUseless161(proto.getUseless161());
        } else {
            this.innerSetUseless161(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless162()) {
            this.innerSetUseless162(proto.getUseless162());
        } else {
            this.innerSetUseless162(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless163()) {
            this.innerSetUseless163(proto.getUseless163());
        } else {
            this.innerSetUseless163(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless164()) {
            this.innerSetUseless164(proto.getUseless164());
        } else {
            this.innerSetUseless164(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasYoTestModel()) {
            this.getYoTestModel().mergeFromCs(proto.getYoTestModel());
        } else {
            if (this.yoTestModel != null) {
                this.yoTestModel.mergeFromCs(proto.getYoTestModel());
            }
        }
        if (proto.hasYoTestUnit()) {
            this.getYoTestUnit().mergeFromCs(proto.getYoTestUnit());
        } else {
            if (this.yoTestUnit != null) {
                this.yoTestUnit.mergeFromCs(proto.getYoTestUnit());
            }
        }
        if (proto.hasSetField()) {
            this.getSetField().mergeFromCs(proto.getSetField());
        } else {
            if (this.setField != null) {
                this.setField.mergeFromCs(proto.getSetField());
            }
        }
        this.markAll();
        return YoTestProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoTestEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntField()) {
            this.setIntField(proto.getIntField());
            fieldCnt++;
        }
        if (proto.hasLongField()) {
            this.setLongField(proto.getLongField());
            fieldCnt++;
        }
        if (proto.hasStringField()) {
            this.setStringField(proto.getStringField());
            fieldCnt++;
        }
        if (proto.hasBoolField()) {
            this.setBoolField(proto.getBoolField());
            fieldCnt++;
        }
        if (proto.hasUseless1()) {
            this.setUseless1(proto.getUseless1());
            fieldCnt++;
        }
        if (proto.hasUseless2()) {
            this.setUseless2(proto.getUseless2());
            fieldCnt++;
        }
        if (proto.hasUseless3()) {
            this.setUseless3(proto.getUseless3());
            fieldCnt++;
        }
        if (proto.hasUseless4()) {
            this.setUseless4(proto.getUseless4());
            fieldCnt++;
        }
        if (proto.hasUseless5()) {
            this.setUseless5(proto.getUseless5());
            fieldCnt++;
        }
        if (proto.hasUseless6()) {
            this.setUseless6(proto.getUseless6());
            fieldCnt++;
        }
        if (proto.hasUseless7()) {
            this.setUseless7(proto.getUseless7());
            fieldCnt++;
        }
        if (proto.hasUseless8()) {
            this.setUseless8(proto.getUseless8());
            fieldCnt++;
        }
        if (proto.hasUseless9()) {
            this.setUseless9(proto.getUseless9());
            fieldCnt++;
        }
        if (proto.hasUseless10()) {
            this.setUseless10(proto.getUseless10());
            fieldCnt++;
        }
        if (proto.hasUseless11()) {
            this.setUseless11(proto.getUseless11());
            fieldCnt++;
        }
        if (proto.hasUseless12()) {
            this.setUseless12(proto.getUseless12());
            fieldCnt++;
        }
        if (proto.hasUseless13()) {
            this.setUseless13(proto.getUseless13());
            fieldCnt++;
        }
        if (proto.hasUseless14()) {
            this.setUseless14(proto.getUseless14());
            fieldCnt++;
        }
        if (proto.hasUseless15()) {
            this.setUseless15(proto.getUseless15());
            fieldCnt++;
        }
        if (proto.hasUseless16()) {
            this.setUseless16(proto.getUseless16());
            fieldCnt++;
        }
        if (proto.hasUseless17()) {
            this.setUseless17(proto.getUseless17());
            fieldCnt++;
        }
        if (proto.hasUseless18()) {
            this.setUseless18(proto.getUseless18());
            fieldCnt++;
        }
        if (proto.hasUseless19()) {
            this.setUseless19(proto.getUseless19());
            fieldCnt++;
        }
        if (proto.hasUseless20()) {
            this.setUseless20(proto.getUseless20());
            fieldCnt++;
        }
        if (proto.hasUseless21()) {
            this.setUseless21(proto.getUseless21());
            fieldCnt++;
        }
        if (proto.hasUseless22()) {
            this.setUseless22(proto.getUseless22());
            fieldCnt++;
        }
        if (proto.hasUseless23()) {
            this.setUseless23(proto.getUseless23());
            fieldCnt++;
        }
        if (proto.hasUseless24()) {
            this.setUseless24(proto.getUseless24());
            fieldCnt++;
        }
        if (proto.hasUseless25()) {
            this.setUseless25(proto.getUseless25());
            fieldCnt++;
        }
        if (proto.hasUseless26()) {
            this.setUseless26(proto.getUseless26());
            fieldCnt++;
        }
        if (proto.hasUseless27()) {
            this.setUseless27(proto.getUseless27());
            fieldCnt++;
        }
        if (proto.hasUseless28()) {
            this.setUseless28(proto.getUseless28());
            fieldCnt++;
        }
        if (proto.hasUseless29()) {
            this.setUseless29(proto.getUseless29());
            fieldCnt++;
        }
        if (proto.hasUseless30()) {
            this.setUseless30(proto.getUseless30());
            fieldCnt++;
        }
        if (proto.hasUseless31()) {
            this.setUseless31(proto.getUseless31());
            fieldCnt++;
        }
        if (proto.hasUseless32()) {
            this.setUseless32(proto.getUseless32());
            fieldCnt++;
        }
        if (proto.hasUseless33()) {
            this.setUseless33(proto.getUseless33());
            fieldCnt++;
        }
        if (proto.hasUseless34()) {
            this.setUseless34(proto.getUseless34());
            fieldCnt++;
        }
        if (proto.hasUseless35()) {
            this.setUseless35(proto.getUseless35());
            fieldCnt++;
        }
        if (proto.hasUseless36()) {
            this.setUseless36(proto.getUseless36());
            fieldCnt++;
        }
        if (proto.hasUseless37()) {
            this.setUseless37(proto.getUseless37());
            fieldCnt++;
        }
        if (proto.hasUseless38()) {
            this.setUseless38(proto.getUseless38());
            fieldCnt++;
        }
        if (proto.hasUseless39()) {
            this.setUseless39(proto.getUseless39());
            fieldCnt++;
        }
        if (proto.hasUseless40()) {
            this.setUseless40(proto.getUseless40());
            fieldCnt++;
        }
        if (proto.hasUseless41()) {
            this.setUseless41(proto.getUseless41());
            fieldCnt++;
        }
        if (proto.hasUseless42()) {
            this.setUseless42(proto.getUseless42());
            fieldCnt++;
        }
        if (proto.hasUseless43()) {
            this.setUseless43(proto.getUseless43());
            fieldCnt++;
        }
        if (proto.hasUseless44()) {
            this.setUseless44(proto.getUseless44());
            fieldCnt++;
        }
        if (proto.hasUseless45()) {
            this.setUseless45(proto.getUseless45());
            fieldCnt++;
        }
        if (proto.hasUseless46()) {
            this.setUseless46(proto.getUseless46());
            fieldCnt++;
        }
        if (proto.hasUseless47()) {
            this.setUseless47(proto.getUseless47());
            fieldCnt++;
        }
        if (proto.hasUseless48()) {
            this.setUseless48(proto.getUseless48());
            fieldCnt++;
        }
        if (proto.hasUseless49()) {
            this.setUseless49(proto.getUseless49());
            fieldCnt++;
        }
        if (proto.hasUseless50()) {
            this.setUseless50(proto.getUseless50());
            fieldCnt++;
        }
        if (proto.hasUseless51()) {
            this.setUseless51(proto.getUseless51());
            fieldCnt++;
        }
        if (proto.hasUseless52()) {
            this.setUseless52(proto.getUseless52());
            fieldCnt++;
        }
        if (proto.hasUseless53()) {
            this.setUseless53(proto.getUseless53());
            fieldCnt++;
        }
        if (proto.hasUseless54()) {
            this.setUseless54(proto.getUseless54());
            fieldCnt++;
        }
        if (proto.hasUseless55()) {
            this.setUseless55(proto.getUseless55());
            fieldCnt++;
        }
        if (proto.hasUseless56()) {
            this.setUseless56(proto.getUseless56());
            fieldCnt++;
        }
        if (proto.hasUseless57()) {
            this.setUseless57(proto.getUseless57());
            fieldCnt++;
        }
        if (proto.hasUseless58()) {
            this.setUseless58(proto.getUseless58());
            fieldCnt++;
        }
        if (proto.hasUseless59()) {
            this.setUseless59(proto.getUseless59());
            fieldCnt++;
        }
        if (proto.hasUseless60()) {
            this.setUseless60(proto.getUseless60());
            fieldCnt++;
        }
        if (proto.hasUseless61()) {
            this.setUseless61(proto.getUseless61());
            fieldCnt++;
        }
        if (proto.hasUseless62()) {
            this.setUseless62(proto.getUseless62());
            fieldCnt++;
        }
        if (proto.hasUseless63()) {
            this.setUseless63(proto.getUseless63());
            fieldCnt++;
        }
        if (proto.hasUseless64()) {
            this.setUseless64(proto.getUseless64());
            fieldCnt++;
        }
        if (proto.hasMapField()) {
            this.getMapField().mergeChangeFromCs(proto.getMapField());
            fieldCnt++;
        }
        if (proto.hasListField()) {
            this.getListField().mergeChangeFromCs(proto.getListField());
            fieldCnt++;
        }
        if (proto.hasUseless101()) {
            this.setUseless101(proto.getUseless101());
            fieldCnt++;
        }
        if (proto.hasUseless102()) {
            this.setUseless102(proto.getUseless102());
            fieldCnt++;
        }
        if (proto.hasUseless103()) {
            this.setUseless103(proto.getUseless103());
            fieldCnt++;
        }
        if (proto.hasUseless104()) {
            this.setUseless104(proto.getUseless104());
            fieldCnt++;
        }
        if (proto.hasUseless105()) {
            this.setUseless105(proto.getUseless105());
            fieldCnt++;
        }
        if (proto.hasUseless106()) {
            this.setUseless106(proto.getUseless106());
            fieldCnt++;
        }
        if (proto.hasUseless107()) {
            this.setUseless107(proto.getUseless107());
            fieldCnt++;
        }
        if (proto.hasUseless108()) {
            this.setUseless108(proto.getUseless108());
            fieldCnt++;
        }
        if (proto.hasUseless109()) {
            this.setUseless109(proto.getUseless109());
            fieldCnt++;
        }
        if (proto.hasUseless110()) {
            this.setUseless110(proto.getUseless110());
            fieldCnt++;
        }
        if (proto.hasUseless111()) {
            this.setUseless111(proto.getUseless111());
            fieldCnt++;
        }
        if (proto.hasUseless112()) {
            this.setUseless112(proto.getUseless112());
            fieldCnt++;
        }
        if (proto.hasUseless113()) {
            this.setUseless113(proto.getUseless113());
            fieldCnt++;
        }
        if (proto.hasUseless114()) {
            this.setUseless114(proto.getUseless114());
            fieldCnt++;
        }
        if (proto.hasUseless115()) {
            this.setUseless115(proto.getUseless115());
            fieldCnt++;
        }
        if (proto.hasUseless116()) {
            this.setUseless116(proto.getUseless116());
            fieldCnt++;
        }
        if (proto.hasUseless117()) {
            this.setUseless117(proto.getUseless117());
            fieldCnt++;
        }
        if (proto.hasUseless118()) {
            this.setUseless118(proto.getUseless118());
            fieldCnt++;
        }
        if (proto.hasUseless119()) {
            this.setUseless119(proto.getUseless119());
            fieldCnt++;
        }
        if (proto.hasUseless120()) {
            this.setUseless120(proto.getUseless120());
            fieldCnt++;
        }
        if (proto.hasUseless121()) {
            this.setUseless121(proto.getUseless121());
            fieldCnt++;
        }
        if (proto.hasUseless122()) {
            this.setUseless122(proto.getUseless122());
            fieldCnt++;
        }
        if (proto.hasUseless123()) {
            this.setUseless123(proto.getUseless123());
            fieldCnt++;
        }
        if (proto.hasUseless124()) {
            this.setUseless124(proto.getUseless124());
            fieldCnt++;
        }
        if (proto.hasUseless125()) {
            this.setUseless125(proto.getUseless125());
            fieldCnt++;
        }
        if (proto.hasUseless126()) {
            this.setUseless126(proto.getUseless126());
            fieldCnt++;
        }
        if (proto.hasUseless127()) {
            this.setUseless127(proto.getUseless127());
            fieldCnt++;
        }
        if (proto.hasUseless128()) {
            this.setUseless128(proto.getUseless128());
            fieldCnt++;
        }
        if (proto.hasUseless129()) {
            this.setUseless129(proto.getUseless129());
            fieldCnt++;
        }
        if (proto.hasUseless130()) {
            this.setUseless130(proto.getUseless130());
            fieldCnt++;
        }
        if (proto.hasUseless131()) {
            this.setUseless131(proto.getUseless131());
            fieldCnt++;
        }
        if (proto.hasUseless132()) {
            this.setUseless132(proto.getUseless132());
            fieldCnt++;
        }
        if (proto.hasUseless133()) {
            this.setUseless133(proto.getUseless133());
            fieldCnt++;
        }
        if (proto.hasUseless134()) {
            this.setUseless134(proto.getUseless134());
            fieldCnt++;
        }
        if (proto.hasUseless135()) {
            this.setUseless135(proto.getUseless135());
            fieldCnt++;
        }
        if (proto.hasUseless136()) {
            this.setUseless136(proto.getUseless136());
            fieldCnt++;
        }
        if (proto.hasUseless137()) {
            this.setUseless137(proto.getUseless137());
            fieldCnt++;
        }
        if (proto.hasUseless138()) {
            this.setUseless138(proto.getUseless138());
            fieldCnt++;
        }
        if (proto.hasUseless139()) {
            this.setUseless139(proto.getUseless139());
            fieldCnt++;
        }
        if (proto.hasUseless140()) {
            this.setUseless140(proto.getUseless140());
            fieldCnt++;
        }
        if (proto.hasUseless141()) {
            this.setUseless141(proto.getUseless141());
            fieldCnt++;
        }
        if (proto.hasUseless142()) {
            this.setUseless142(proto.getUseless142());
            fieldCnt++;
        }
        if (proto.hasUseless143()) {
            this.setUseless143(proto.getUseless143());
            fieldCnt++;
        }
        if (proto.hasUseless144()) {
            this.setUseless144(proto.getUseless144());
            fieldCnt++;
        }
        if (proto.hasUseless145()) {
            this.setUseless145(proto.getUseless145());
            fieldCnt++;
        }
        if (proto.hasUseless146()) {
            this.setUseless146(proto.getUseless146());
            fieldCnt++;
        }
        if (proto.hasUseless147()) {
            this.setUseless147(proto.getUseless147());
            fieldCnt++;
        }
        if (proto.hasUseless148()) {
            this.setUseless148(proto.getUseless148());
            fieldCnt++;
        }
        if (proto.hasUseless149()) {
            this.setUseless149(proto.getUseless149());
            fieldCnt++;
        }
        if (proto.hasUseless150()) {
            this.setUseless150(proto.getUseless150());
            fieldCnt++;
        }
        if (proto.hasUseless151()) {
            this.setUseless151(proto.getUseless151());
            fieldCnt++;
        }
        if (proto.hasUseless152()) {
            this.setUseless152(proto.getUseless152());
            fieldCnt++;
        }
        if (proto.hasUseless153()) {
            this.setUseless153(proto.getUseless153());
            fieldCnt++;
        }
        if (proto.hasUseless154()) {
            this.setUseless154(proto.getUseless154());
            fieldCnt++;
        }
        if (proto.hasUseless155()) {
            this.setUseless155(proto.getUseless155());
            fieldCnt++;
        }
        if (proto.hasUseless156()) {
            this.setUseless156(proto.getUseless156());
            fieldCnt++;
        }
        if (proto.hasUseless157()) {
            this.setUseless157(proto.getUseless157());
            fieldCnt++;
        }
        if (proto.hasUseless158()) {
            this.setUseless158(proto.getUseless158());
            fieldCnt++;
        }
        if (proto.hasUseless159()) {
            this.setUseless159(proto.getUseless159());
            fieldCnt++;
        }
        if (proto.hasUseless160()) {
            this.setUseless160(proto.getUseless160());
            fieldCnt++;
        }
        if (proto.hasUseless161()) {
            this.setUseless161(proto.getUseless161());
            fieldCnt++;
        }
        if (proto.hasUseless162()) {
            this.setUseless162(proto.getUseless162());
            fieldCnt++;
        }
        if (proto.hasUseless163()) {
            this.setUseless163(proto.getUseless163());
            fieldCnt++;
        }
        if (proto.hasUseless164()) {
            this.setUseless164(proto.getUseless164());
            fieldCnt++;
        }
        if (proto.hasYoTestModel()) {
            this.getYoTestModel().mergeChangeFromCs(proto.getYoTestModel());
            fieldCnt++;
        }
        if (proto.hasYoTestUnit()) {
            this.getYoTestUnit().mergeChangeFromCs(proto.getYoTestUnit());
            fieldCnt++;
        }
        if (proto.hasSetField()) {
            this.getSetField().mergeChangeFromCs(proto.getSetField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestEntity.Builder getCopyDbBuilder() {
        final YoTestEntity.Builder builder = YoTestEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(YoTestEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntField() != 0) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }  else if (builder.hasIntField()) {
            // 清理IntField
            builder.clearIntField();
            fieldCnt++;
        }
        if (this.getLongField() != 0L) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }  else if (builder.hasLongField()) {
            // 清理LongField
            builder.clearLongField();
            fieldCnt++;
        }
        if (!this.getStringField().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }  else if (builder.hasStringField()) {
            // 清理StringField
            builder.clearStringField();
            fieldCnt++;
        }
        if (this.getBoolField()) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }  else if (builder.hasBoolField()) {
            // 清理BoolField
            builder.clearBoolField();
            fieldCnt++;
        }
        if (this.getUseless1()) {
            builder.setUseless1(this.getUseless1());
            fieldCnt++;
        }  else if (builder.hasUseless1()) {
            // 清理Useless1
            builder.clearUseless1();
            fieldCnt++;
        }
        if (this.getUseless2()) {
            builder.setUseless2(this.getUseless2());
            fieldCnt++;
        }  else if (builder.hasUseless2()) {
            // 清理Useless2
            builder.clearUseless2();
            fieldCnt++;
        }
        if (this.getUseless3()) {
            builder.setUseless3(this.getUseless3());
            fieldCnt++;
        }  else if (builder.hasUseless3()) {
            // 清理Useless3
            builder.clearUseless3();
            fieldCnt++;
        }
        if (this.getUseless4()) {
            builder.setUseless4(this.getUseless4());
            fieldCnt++;
        }  else if (builder.hasUseless4()) {
            // 清理Useless4
            builder.clearUseless4();
            fieldCnt++;
        }
        if (this.getUseless5()) {
            builder.setUseless5(this.getUseless5());
            fieldCnt++;
        }  else if (builder.hasUseless5()) {
            // 清理Useless5
            builder.clearUseless5();
            fieldCnt++;
        }
        if (this.getUseless6()) {
            builder.setUseless6(this.getUseless6());
            fieldCnt++;
        }  else if (builder.hasUseless6()) {
            // 清理Useless6
            builder.clearUseless6();
            fieldCnt++;
        }
        if (this.getUseless7()) {
            builder.setUseless7(this.getUseless7());
            fieldCnt++;
        }  else if (builder.hasUseless7()) {
            // 清理Useless7
            builder.clearUseless7();
            fieldCnt++;
        }
        if (this.getUseless8()) {
            builder.setUseless8(this.getUseless8());
            fieldCnt++;
        }  else if (builder.hasUseless8()) {
            // 清理Useless8
            builder.clearUseless8();
            fieldCnt++;
        }
        if (this.getUseless9()) {
            builder.setUseless9(this.getUseless9());
            fieldCnt++;
        }  else if (builder.hasUseless9()) {
            // 清理Useless9
            builder.clearUseless9();
            fieldCnt++;
        }
        if (this.getUseless10()) {
            builder.setUseless10(this.getUseless10());
            fieldCnt++;
        }  else if (builder.hasUseless10()) {
            // 清理Useless10
            builder.clearUseless10();
            fieldCnt++;
        }
        if (this.getUseless11()) {
            builder.setUseless11(this.getUseless11());
            fieldCnt++;
        }  else if (builder.hasUseless11()) {
            // 清理Useless11
            builder.clearUseless11();
            fieldCnt++;
        }
        if (this.getUseless12()) {
            builder.setUseless12(this.getUseless12());
            fieldCnt++;
        }  else if (builder.hasUseless12()) {
            // 清理Useless12
            builder.clearUseless12();
            fieldCnt++;
        }
        if (this.getUseless13()) {
            builder.setUseless13(this.getUseless13());
            fieldCnt++;
        }  else if (builder.hasUseless13()) {
            // 清理Useless13
            builder.clearUseless13();
            fieldCnt++;
        }
        if (this.getUseless14()) {
            builder.setUseless14(this.getUseless14());
            fieldCnt++;
        }  else if (builder.hasUseless14()) {
            // 清理Useless14
            builder.clearUseless14();
            fieldCnt++;
        }
        if (this.getUseless15()) {
            builder.setUseless15(this.getUseless15());
            fieldCnt++;
        }  else if (builder.hasUseless15()) {
            // 清理Useless15
            builder.clearUseless15();
            fieldCnt++;
        }
        if (this.getUseless16()) {
            builder.setUseless16(this.getUseless16());
            fieldCnt++;
        }  else if (builder.hasUseless16()) {
            // 清理Useless16
            builder.clearUseless16();
            fieldCnt++;
        }
        if (this.getUseless17()) {
            builder.setUseless17(this.getUseless17());
            fieldCnt++;
        }  else if (builder.hasUseless17()) {
            // 清理Useless17
            builder.clearUseless17();
            fieldCnt++;
        }
        if (this.getUseless18()) {
            builder.setUseless18(this.getUseless18());
            fieldCnt++;
        }  else if (builder.hasUseless18()) {
            // 清理Useless18
            builder.clearUseless18();
            fieldCnt++;
        }
        if (this.getUseless19()) {
            builder.setUseless19(this.getUseless19());
            fieldCnt++;
        }  else if (builder.hasUseless19()) {
            // 清理Useless19
            builder.clearUseless19();
            fieldCnt++;
        }
        if (this.getUseless20()) {
            builder.setUseless20(this.getUseless20());
            fieldCnt++;
        }  else if (builder.hasUseless20()) {
            // 清理Useless20
            builder.clearUseless20();
            fieldCnt++;
        }
        if (this.getUseless21()) {
            builder.setUseless21(this.getUseless21());
            fieldCnt++;
        }  else if (builder.hasUseless21()) {
            // 清理Useless21
            builder.clearUseless21();
            fieldCnt++;
        }
        if (this.getUseless22()) {
            builder.setUseless22(this.getUseless22());
            fieldCnt++;
        }  else if (builder.hasUseless22()) {
            // 清理Useless22
            builder.clearUseless22();
            fieldCnt++;
        }
        if (this.getUseless23()) {
            builder.setUseless23(this.getUseless23());
            fieldCnt++;
        }  else if (builder.hasUseless23()) {
            // 清理Useless23
            builder.clearUseless23();
            fieldCnt++;
        }
        if (this.getUseless24()) {
            builder.setUseless24(this.getUseless24());
            fieldCnt++;
        }  else if (builder.hasUseless24()) {
            // 清理Useless24
            builder.clearUseless24();
            fieldCnt++;
        }
        if (this.getUseless25()) {
            builder.setUseless25(this.getUseless25());
            fieldCnt++;
        }  else if (builder.hasUseless25()) {
            // 清理Useless25
            builder.clearUseless25();
            fieldCnt++;
        }
        if (this.getUseless26()) {
            builder.setUseless26(this.getUseless26());
            fieldCnt++;
        }  else if (builder.hasUseless26()) {
            // 清理Useless26
            builder.clearUseless26();
            fieldCnt++;
        }
        if (this.getUseless27()) {
            builder.setUseless27(this.getUseless27());
            fieldCnt++;
        }  else if (builder.hasUseless27()) {
            // 清理Useless27
            builder.clearUseless27();
            fieldCnt++;
        }
        if (this.getUseless28()) {
            builder.setUseless28(this.getUseless28());
            fieldCnt++;
        }  else if (builder.hasUseless28()) {
            // 清理Useless28
            builder.clearUseless28();
            fieldCnt++;
        }
        if (this.getUseless29()) {
            builder.setUseless29(this.getUseless29());
            fieldCnt++;
        }  else if (builder.hasUseless29()) {
            // 清理Useless29
            builder.clearUseless29();
            fieldCnt++;
        }
        if (this.getUseless30()) {
            builder.setUseless30(this.getUseless30());
            fieldCnt++;
        }  else if (builder.hasUseless30()) {
            // 清理Useless30
            builder.clearUseless30();
            fieldCnt++;
        }
        if (this.getUseless31()) {
            builder.setUseless31(this.getUseless31());
            fieldCnt++;
        }  else if (builder.hasUseless31()) {
            // 清理Useless31
            builder.clearUseless31();
            fieldCnt++;
        }
        if (this.getUseless32()) {
            builder.setUseless32(this.getUseless32());
            fieldCnt++;
        }  else if (builder.hasUseless32()) {
            // 清理Useless32
            builder.clearUseless32();
            fieldCnt++;
        }
        if (this.getUseless33()) {
            builder.setUseless33(this.getUseless33());
            fieldCnt++;
        }  else if (builder.hasUseless33()) {
            // 清理Useless33
            builder.clearUseless33();
            fieldCnt++;
        }
        if (this.getUseless34()) {
            builder.setUseless34(this.getUseless34());
            fieldCnt++;
        }  else if (builder.hasUseless34()) {
            // 清理Useless34
            builder.clearUseless34();
            fieldCnt++;
        }
        if (this.getUseless35()) {
            builder.setUseless35(this.getUseless35());
            fieldCnt++;
        }  else if (builder.hasUseless35()) {
            // 清理Useless35
            builder.clearUseless35();
            fieldCnt++;
        }
        if (this.getUseless36()) {
            builder.setUseless36(this.getUseless36());
            fieldCnt++;
        }  else if (builder.hasUseless36()) {
            // 清理Useless36
            builder.clearUseless36();
            fieldCnt++;
        }
        if (this.getUseless37()) {
            builder.setUseless37(this.getUseless37());
            fieldCnt++;
        }  else if (builder.hasUseless37()) {
            // 清理Useless37
            builder.clearUseless37();
            fieldCnt++;
        }
        if (this.getUseless38()) {
            builder.setUseless38(this.getUseless38());
            fieldCnt++;
        }  else if (builder.hasUseless38()) {
            // 清理Useless38
            builder.clearUseless38();
            fieldCnt++;
        }
        if (this.getUseless39()) {
            builder.setUseless39(this.getUseless39());
            fieldCnt++;
        }  else if (builder.hasUseless39()) {
            // 清理Useless39
            builder.clearUseless39();
            fieldCnt++;
        }
        if (this.getUseless40()) {
            builder.setUseless40(this.getUseless40());
            fieldCnt++;
        }  else if (builder.hasUseless40()) {
            // 清理Useless40
            builder.clearUseless40();
            fieldCnt++;
        }
        if (this.getUseless41()) {
            builder.setUseless41(this.getUseless41());
            fieldCnt++;
        }  else if (builder.hasUseless41()) {
            // 清理Useless41
            builder.clearUseless41();
            fieldCnt++;
        }
        if (this.getUseless42()) {
            builder.setUseless42(this.getUseless42());
            fieldCnt++;
        }  else if (builder.hasUseless42()) {
            // 清理Useless42
            builder.clearUseless42();
            fieldCnt++;
        }
        if (this.getUseless43()) {
            builder.setUseless43(this.getUseless43());
            fieldCnt++;
        }  else if (builder.hasUseless43()) {
            // 清理Useless43
            builder.clearUseless43();
            fieldCnt++;
        }
        if (this.getUseless44()) {
            builder.setUseless44(this.getUseless44());
            fieldCnt++;
        }  else if (builder.hasUseless44()) {
            // 清理Useless44
            builder.clearUseless44();
            fieldCnt++;
        }
        if (this.getUseless45()) {
            builder.setUseless45(this.getUseless45());
            fieldCnt++;
        }  else if (builder.hasUseless45()) {
            // 清理Useless45
            builder.clearUseless45();
            fieldCnt++;
        }
        if (this.getUseless46()) {
            builder.setUseless46(this.getUseless46());
            fieldCnt++;
        }  else if (builder.hasUseless46()) {
            // 清理Useless46
            builder.clearUseless46();
            fieldCnt++;
        }
        if (this.getUseless47()) {
            builder.setUseless47(this.getUseless47());
            fieldCnt++;
        }  else if (builder.hasUseless47()) {
            // 清理Useless47
            builder.clearUseless47();
            fieldCnt++;
        }
        if (this.getUseless48()) {
            builder.setUseless48(this.getUseless48());
            fieldCnt++;
        }  else if (builder.hasUseless48()) {
            // 清理Useless48
            builder.clearUseless48();
            fieldCnt++;
        }
        if (this.getUseless49()) {
            builder.setUseless49(this.getUseless49());
            fieldCnt++;
        }  else if (builder.hasUseless49()) {
            // 清理Useless49
            builder.clearUseless49();
            fieldCnt++;
        }
        if (this.getUseless50()) {
            builder.setUseless50(this.getUseless50());
            fieldCnt++;
        }  else if (builder.hasUseless50()) {
            // 清理Useless50
            builder.clearUseless50();
            fieldCnt++;
        }
        if (this.getUseless51()) {
            builder.setUseless51(this.getUseless51());
            fieldCnt++;
        }  else if (builder.hasUseless51()) {
            // 清理Useless51
            builder.clearUseless51();
            fieldCnt++;
        }
        if (this.getUseless52()) {
            builder.setUseless52(this.getUseless52());
            fieldCnt++;
        }  else if (builder.hasUseless52()) {
            // 清理Useless52
            builder.clearUseless52();
            fieldCnt++;
        }
        if (this.getUseless53()) {
            builder.setUseless53(this.getUseless53());
            fieldCnt++;
        }  else if (builder.hasUseless53()) {
            // 清理Useless53
            builder.clearUseless53();
            fieldCnt++;
        }
        if (this.getUseless54()) {
            builder.setUseless54(this.getUseless54());
            fieldCnt++;
        }  else if (builder.hasUseless54()) {
            // 清理Useless54
            builder.clearUseless54();
            fieldCnt++;
        }
        if (this.getUseless55()) {
            builder.setUseless55(this.getUseless55());
            fieldCnt++;
        }  else if (builder.hasUseless55()) {
            // 清理Useless55
            builder.clearUseless55();
            fieldCnt++;
        }
        if (this.getUseless56()) {
            builder.setUseless56(this.getUseless56());
            fieldCnt++;
        }  else if (builder.hasUseless56()) {
            // 清理Useless56
            builder.clearUseless56();
            fieldCnt++;
        }
        if (this.getUseless57()) {
            builder.setUseless57(this.getUseless57());
            fieldCnt++;
        }  else if (builder.hasUseless57()) {
            // 清理Useless57
            builder.clearUseless57();
            fieldCnt++;
        }
        if (this.getUseless58()) {
            builder.setUseless58(this.getUseless58());
            fieldCnt++;
        }  else if (builder.hasUseless58()) {
            // 清理Useless58
            builder.clearUseless58();
            fieldCnt++;
        }
        if (this.getUseless59()) {
            builder.setUseless59(this.getUseless59());
            fieldCnt++;
        }  else if (builder.hasUseless59()) {
            // 清理Useless59
            builder.clearUseless59();
            fieldCnt++;
        }
        if (this.getUseless60()) {
            builder.setUseless60(this.getUseless60());
            fieldCnt++;
        }  else if (builder.hasUseless60()) {
            // 清理Useless60
            builder.clearUseless60();
            fieldCnt++;
        }
        if (this.getUseless61()) {
            builder.setUseless61(this.getUseless61());
            fieldCnt++;
        }  else if (builder.hasUseless61()) {
            // 清理Useless61
            builder.clearUseless61();
            fieldCnt++;
        }
        if (this.getUseless62()) {
            builder.setUseless62(this.getUseless62());
            fieldCnt++;
        }  else if (builder.hasUseless62()) {
            // 清理Useless62
            builder.clearUseless62();
            fieldCnt++;
        }
        if (this.getUseless63()) {
            builder.setUseless63(this.getUseless63());
            fieldCnt++;
        }  else if (builder.hasUseless63()) {
            // 清理Useless63
            builder.clearUseless63();
            fieldCnt++;
        }
        if (this.getUseless64()) {
            builder.setUseless64(this.getUseless64());
            fieldCnt++;
        }  else if (builder.hasUseless64()) {
            // 清理Useless64
            builder.clearUseless64();
            fieldCnt++;
        }
        if (this.mapField != null) {
            YoTest.Int64YoTestMapMap.Builder tmpBuilder = YoTest.Int64YoTestMapMap.newBuilder();
            final int tmpFieldCnt = this.mapField.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMapField();
            }
        }  else if (builder.hasMapField()) {
            // 清理MapField
            builder.clearMapField();
            fieldCnt++;
        }
        if (this.listField != null) {
            YoTest.YoTestListList.Builder tmpBuilder = YoTest.YoTestListList.newBuilder();
            final int tmpFieldCnt = this.listField.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setListField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearListField();
            }
        }  else if (builder.hasListField()) {
            // 清理ListField
            builder.clearListField();
            fieldCnt++;
        }
        if (this.getUseless101()) {
            builder.setUseless101(this.getUseless101());
            fieldCnt++;
        }  else if (builder.hasUseless101()) {
            // 清理Useless101
            builder.clearUseless101();
            fieldCnt++;
        }
        if (this.getUseless102()) {
            builder.setUseless102(this.getUseless102());
            fieldCnt++;
        }  else if (builder.hasUseless102()) {
            // 清理Useless102
            builder.clearUseless102();
            fieldCnt++;
        }
        if (this.getUseless103()) {
            builder.setUseless103(this.getUseless103());
            fieldCnt++;
        }  else if (builder.hasUseless103()) {
            // 清理Useless103
            builder.clearUseless103();
            fieldCnt++;
        }
        if (this.getUseless104()) {
            builder.setUseless104(this.getUseless104());
            fieldCnt++;
        }  else if (builder.hasUseless104()) {
            // 清理Useless104
            builder.clearUseless104();
            fieldCnt++;
        }
        if (this.getUseless105()) {
            builder.setUseless105(this.getUseless105());
            fieldCnt++;
        }  else if (builder.hasUseless105()) {
            // 清理Useless105
            builder.clearUseless105();
            fieldCnt++;
        }
        if (this.getUseless106()) {
            builder.setUseless106(this.getUseless106());
            fieldCnt++;
        }  else if (builder.hasUseless106()) {
            // 清理Useless106
            builder.clearUseless106();
            fieldCnt++;
        }
        if (this.getUseless107()) {
            builder.setUseless107(this.getUseless107());
            fieldCnt++;
        }  else if (builder.hasUseless107()) {
            // 清理Useless107
            builder.clearUseless107();
            fieldCnt++;
        }
        if (this.getUseless108()) {
            builder.setUseless108(this.getUseless108());
            fieldCnt++;
        }  else if (builder.hasUseless108()) {
            // 清理Useless108
            builder.clearUseless108();
            fieldCnt++;
        }
        if (this.getUseless109()) {
            builder.setUseless109(this.getUseless109());
            fieldCnt++;
        }  else if (builder.hasUseless109()) {
            // 清理Useless109
            builder.clearUseless109();
            fieldCnt++;
        }
        if (this.getUseless110()) {
            builder.setUseless110(this.getUseless110());
            fieldCnt++;
        }  else if (builder.hasUseless110()) {
            // 清理Useless110
            builder.clearUseless110();
            fieldCnt++;
        }
        if (this.getUseless111()) {
            builder.setUseless111(this.getUseless111());
            fieldCnt++;
        }  else if (builder.hasUseless111()) {
            // 清理Useless111
            builder.clearUseless111();
            fieldCnt++;
        }
        if (this.getUseless112()) {
            builder.setUseless112(this.getUseless112());
            fieldCnt++;
        }  else if (builder.hasUseless112()) {
            // 清理Useless112
            builder.clearUseless112();
            fieldCnt++;
        }
        if (this.getUseless113()) {
            builder.setUseless113(this.getUseless113());
            fieldCnt++;
        }  else if (builder.hasUseless113()) {
            // 清理Useless113
            builder.clearUseless113();
            fieldCnt++;
        }
        if (this.getUseless114()) {
            builder.setUseless114(this.getUseless114());
            fieldCnt++;
        }  else if (builder.hasUseless114()) {
            // 清理Useless114
            builder.clearUseless114();
            fieldCnt++;
        }
        if (this.getUseless115()) {
            builder.setUseless115(this.getUseless115());
            fieldCnt++;
        }  else if (builder.hasUseless115()) {
            // 清理Useless115
            builder.clearUseless115();
            fieldCnt++;
        }
        if (this.getUseless116()) {
            builder.setUseless116(this.getUseless116());
            fieldCnt++;
        }  else if (builder.hasUseless116()) {
            // 清理Useless116
            builder.clearUseless116();
            fieldCnt++;
        }
        if (this.getUseless117()) {
            builder.setUseless117(this.getUseless117());
            fieldCnt++;
        }  else if (builder.hasUseless117()) {
            // 清理Useless117
            builder.clearUseless117();
            fieldCnt++;
        }
        if (this.getUseless118()) {
            builder.setUseless118(this.getUseless118());
            fieldCnt++;
        }  else if (builder.hasUseless118()) {
            // 清理Useless118
            builder.clearUseless118();
            fieldCnt++;
        }
        if (this.getUseless119()) {
            builder.setUseless119(this.getUseless119());
            fieldCnt++;
        }  else if (builder.hasUseless119()) {
            // 清理Useless119
            builder.clearUseless119();
            fieldCnt++;
        }
        if (this.getUseless120()) {
            builder.setUseless120(this.getUseless120());
            fieldCnt++;
        }  else if (builder.hasUseless120()) {
            // 清理Useless120
            builder.clearUseless120();
            fieldCnt++;
        }
        if (this.getUseless121()) {
            builder.setUseless121(this.getUseless121());
            fieldCnt++;
        }  else if (builder.hasUseless121()) {
            // 清理Useless121
            builder.clearUseless121();
            fieldCnt++;
        }
        if (this.getUseless122()) {
            builder.setUseless122(this.getUseless122());
            fieldCnt++;
        }  else if (builder.hasUseless122()) {
            // 清理Useless122
            builder.clearUseless122();
            fieldCnt++;
        }
        if (this.getUseless123()) {
            builder.setUseless123(this.getUseless123());
            fieldCnt++;
        }  else if (builder.hasUseless123()) {
            // 清理Useless123
            builder.clearUseless123();
            fieldCnt++;
        }
        if (this.getUseless124()) {
            builder.setUseless124(this.getUseless124());
            fieldCnt++;
        }  else if (builder.hasUseless124()) {
            // 清理Useless124
            builder.clearUseless124();
            fieldCnt++;
        }
        if (this.getUseless125()) {
            builder.setUseless125(this.getUseless125());
            fieldCnt++;
        }  else if (builder.hasUseless125()) {
            // 清理Useless125
            builder.clearUseless125();
            fieldCnt++;
        }
        if (this.getUseless126()) {
            builder.setUseless126(this.getUseless126());
            fieldCnt++;
        }  else if (builder.hasUseless126()) {
            // 清理Useless126
            builder.clearUseless126();
            fieldCnt++;
        }
        if (this.getUseless127()) {
            builder.setUseless127(this.getUseless127());
            fieldCnt++;
        }  else if (builder.hasUseless127()) {
            // 清理Useless127
            builder.clearUseless127();
            fieldCnt++;
        }
        if (this.getUseless128()) {
            builder.setUseless128(this.getUseless128());
            fieldCnt++;
        }  else if (builder.hasUseless128()) {
            // 清理Useless128
            builder.clearUseless128();
            fieldCnt++;
        }
        if (this.getUseless129()) {
            builder.setUseless129(this.getUseless129());
            fieldCnt++;
        }  else if (builder.hasUseless129()) {
            // 清理Useless129
            builder.clearUseless129();
            fieldCnt++;
        }
        if (this.getUseless130()) {
            builder.setUseless130(this.getUseless130());
            fieldCnt++;
        }  else if (builder.hasUseless130()) {
            // 清理Useless130
            builder.clearUseless130();
            fieldCnt++;
        }
        if (this.getUseless131()) {
            builder.setUseless131(this.getUseless131());
            fieldCnt++;
        }  else if (builder.hasUseless131()) {
            // 清理Useless131
            builder.clearUseless131();
            fieldCnt++;
        }
        if (this.getUseless132()) {
            builder.setUseless132(this.getUseless132());
            fieldCnt++;
        }  else if (builder.hasUseless132()) {
            // 清理Useless132
            builder.clearUseless132();
            fieldCnt++;
        }
        if (this.getUseless133()) {
            builder.setUseless133(this.getUseless133());
            fieldCnt++;
        }  else if (builder.hasUseless133()) {
            // 清理Useless133
            builder.clearUseless133();
            fieldCnt++;
        }
        if (this.getUseless134()) {
            builder.setUseless134(this.getUseless134());
            fieldCnt++;
        }  else if (builder.hasUseless134()) {
            // 清理Useless134
            builder.clearUseless134();
            fieldCnt++;
        }
        if (this.getUseless135()) {
            builder.setUseless135(this.getUseless135());
            fieldCnt++;
        }  else if (builder.hasUseless135()) {
            // 清理Useless135
            builder.clearUseless135();
            fieldCnt++;
        }
        if (this.getUseless136()) {
            builder.setUseless136(this.getUseless136());
            fieldCnt++;
        }  else if (builder.hasUseless136()) {
            // 清理Useless136
            builder.clearUseless136();
            fieldCnt++;
        }
        if (this.getUseless137()) {
            builder.setUseless137(this.getUseless137());
            fieldCnt++;
        }  else if (builder.hasUseless137()) {
            // 清理Useless137
            builder.clearUseless137();
            fieldCnt++;
        }
        if (this.getUseless138()) {
            builder.setUseless138(this.getUseless138());
            fieldCnt++;
        }  else if (builder.hasUseless138()) {
            // 清理Useless138
            builder.clearUseless138();
            fieldCnt++;
        }
        if (this.getUseless139()) {
            builder.setUseless139(this.getUseless139());
            fieldCnt++;
        }  else if (builder.hasUseless139()) {
            // 清理Useless139
            builder.clearUseless139();
            fieldCnt++;
        }
        if (this.getUseless140()) {
            builder.setUseless140(this.getUseless140());
            fieldCnt++;
        }  else if (builder.hasUseless140()) {
            // 清理Useless140
            builder.clearUseless140();
            fieldCnt++;
        }
        if (this.getUseless141()) {
            builder.setUseless141(this.getUseless141());
            fieldCnt++;
        }  else if (builder.hasUseless141()) {
            // 清理Useless141
            builder.clearUseless141();
            fieldCnt++;
        }
        if (this.getUseless142()) {
            builder.setUseless142(this.getUseless142());
            fieldCnt++;
        }  else if (builder.hasUseless142()) {
            // 清理Useless142
            builder.clearUseless142();
            fieldCnt++;
        }
        if (this.getUseless143()) {
            builder.setUseless143(this.getUseless143());
            fieldCnt++;
        }  else if (builder.hasUseless143()) {
            // 清理Useless143
            builder.clearUseless143();
            fieldCnt++;
        }
        if (this.getUseless144()) {
            builder.setUseless144(this.getUseless144());
            fieldCnt++;
        }  else if (builder.hasUseless144()) {
            // 清理Useless144
            builder.clearUseless144();
            fieldCnt++;
        }
        if (this.getUseless145()) {
            builder.setUseless145(this.getUseless145());
            fieldCnt++;
        }  else if (builder.hasUseless145()) {
            // 清理Useless145
            builder.clearUseless145();
            fieldCnt++;
        }
        if (this.getUseless146()) {
            builder.setUseless146(this.getUseless146());
            fieldCnt++;
        }  else if (builder.hasUseless146()) {
            // 清理Useless146
            builder.clearUseless146();
            fieldCnt++;
        }
        if (this.getUseless147()) {
            builder.setUseless147(this.getUseless147());
            fieldCnt++;
        }  else if (builder.hasUseless147()) {
            // 清理Useless147
            builder.clearUseless147();
            fieldCnt++;
        }
        if (this.getUseless148()) {
            builder.setUseless148(this.getUseless148());
            fieldCnt++;
        }  else if (builder.hasUseless148()) {
            // 清理Useless148
            builder.clearUseless148();
            fieldCnt++;
        }
        if (this.getUseless149()) {
            builder.setUseless149(this.getUseless149());
            fieldCnt++;
        }  else if (builder.hasUseless149()) {
            // 清理Useless149
            builder.clearUseless149();
            fieldCnt++;
        }
        if (this.getUseless150()) {
            builder.setUseless150(this.getUseless150());
            fieldCnt++;
        }  else if (builder.hasUseless150()) {
            // 清理Useless150
            builder.clearUseless150();
            fieldCnt++;
        }
        if (this.getUseless151()) {
            builder.setUseless151(this.getUseless151());
            fieldCnt++;
        }  else if (builder.hasUseless151()) {
            // 清理Useless151
            builder.clearUseless151();
            fieldCnt++;
        }
        if (this.getUseless152()) {
            builder.setUseless152(this.getUseless152());
            fieldCnt++;
        }  else if (builder.hasUseless152()) {
            // 清理Useless152
            builder.clearUseless152();
            fieldCnt++;
        }
        if (this.getUseless153()) {
            builder.setUseless153(this.getUseless153());
            fieldCnt++;
        }  else if (builder.hasUseless153()) {
            // 清理Useless153
            builder.clearUseless153();
            fieldCnt++;
        }
        if (this.getUseless154()) {
            builder.setUseless154(this.getUseless154());
            fieldCnt++;
        }  else if (builder.hasUseless154()) {
            // 清理Useless154
            builder.clearUseless154();
            fieldCnt++;
        }
        if (this.getUseless155()) {
            builder.setUseless155(this.getUseless155());
            fieldCnt++;
        }  else if (builder.hasUseless155()) {
            // 清理Useless155
            builder.clearUseless155();
            fieldCnt++;
        }
        if (this.getUseless156()) {
            builder.setUseless156(this.getUseless156());
            fieldCnt++;
        }  else if (builder.hasUseless156()) {
            // 清理Useless156
            builder.clearUseless156();
            fieldCnt++;
        }
        if (this.getUseless157()) {
            builder.setUseless157(this.getUseless157());
            fieldCnt++;
        }  else if (builder.hasUseless157()) {
            // 清理Useless157
            builder.clearUseless157();
            fieldCnt++;
        }
        if (this.getUseless158()) {
            builder.setUseless158(this.getUseless158());
            fieldCnt++;
        }  else if (builder.hasUseless158()) {
            // 清理Useless158
            builder.clearUseless158();
            fieldCnt++;
        }
        if (this.getUseless159()) {
            builder.setUseless159(this.getUseless159());
            fieldCnt++;
        }  else if (builder.hasUseless159()) {
            // 清理Useless159
            builder.clearUseless159();
            fieldCnt++;
        }
        if (this.getUseless160()) {
            builder.setUseless160(this.getUseless160());
            fieldCnt++;
        }  else if (builder.hasUseless160()) {
            // 清理Useless160
            builder.clearUseless160();
            fieldCnt++;
        }
        if (this.getUseless161()) {
            builder.setUseless161(this.getUseless161());
            fieldCnt++;
        }  else if (builder.hasUseless161()) {
            // 清理Useless161
            builder.clearUseless161();
            fieldCnt++;
        }
        if (this.getUseless162()) {
            builder.setUseless162(this.getUseless162());
            fieldCnt++;
        }  else if (builder.hasUseless162()) {
            // 清理Useless162
            builder.clearUseless162();
            fieldCnt++;
        }
        if (this.getUseless163()) {
            builder.setUseless163(this.getUseless163());
            fieldCnt++;
        }  else if (builder.hasUseless163()) {
            // 清理Useless163
            builder.clearUseless163();
            fieldCnt++;
        }
        if (this.getUseless164()) {
            builder.setUseless164(this.getUseless164());
            fieldCnt++;
        }  else if (builder.hasUseless164()) {
            // 清理Useless164
            builder.clearUseless164();
            fieldCnt++;
        }
        if (this.yoTestModel != null) {
            YoTest.YoTestModel.Builder tmpBuilder = YoTest.YoTestModel.newBuilder();
            final int tmpFieldCnt = this.yoTestModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYoTestModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYoTestModel();
            }
        }  else if (builder.hasYoTestModel()) {
            // 清理YoTestModel
            builder.clearYoTestModel();
            fieldCnt++;
        }
        if (this.yoTestUnit != null) {
            YoTest.YoTestUnit.Builder tmpBuilder = YoTest.YoTestUnit.newBuilder();
            final int tmpFieldCnt = this.yoTestUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYoTestUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYoTestUnit();
            }
        }  else if (builder.hasYoTestUnit()) {
            // 清理YoTestUnit
            builder.clearYoTestUnit();
            fieldCnt++;
        }
        if (this.setField != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.setField.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSetField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSetField();
            }
        }  else if (builder.hasSetField()) {
            // 清理SetField
            builder.clearSetField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(YoTestEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS1)) {
            builder.setUseless1(this.getUseless1());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS2)) {
            builder.setUseless2(this.getUseless2());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS3)) {
            builder.setUseless3(this.getUseless3());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS4)) {
            builder.setUseless4(this.getUseless4());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS5)) {
            builder.setUseless5(this.getUseless5());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS6)) {
            builder.setUseless6(this.getUseless6());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS7)) {
            builder.setUseless7(this.getUseless7());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS8)) {
            builder.setUseless8(this.getUseless8());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS9)) {
            builder.setUseless9(this.getUseless9());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS10)) {
            builder.setUseless10(this.getUseless10());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS11)) {
            builder.setUseless11(this.getUseless11());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS12)) {
            builder.setUseless12(this.getUseless12());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS13)) {
            builder.setUseless13(this.getUseless13());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS14)) {
            builder.setUseless14(this.getUseless14());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS15)) {
            builder.setUseless15(this.getUseless15());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS16)) {
            builder.setUseless16(this.getUseless16());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS17)) {
            builder.setUseless17(this.getUseless17());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS18)) {
            builder.setUseless18(this.getUseless18());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS19)) {
            builder.setUseless19(this.getUseless19());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS20)) {
            builder.setUseless20(this.getUseless20());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS21)) {
            builder.setUseless21(this.getUseless21());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS22)) {
            builder.setUseless22(this.getUseless22());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS23)) {
            builder.setUseless23(this.getUseless23());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS24)) {
            builder.setUseless24(this.getUseless24());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS25)) {
            builder.setUseless25(this.getUseless25());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS26)) {
            builder.setUseless26(this.getUseless26());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS27)) {
            builder.setUseless27(this.getUseless27());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS28)) {
            builder.setUseless28(this.getUseless28());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS29)) {
            builder.setUseless29(this.getUseless29());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS30)) {
            builder.setUseless30(this.getUseless30());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS31)) {
            builder.setUseless31(this.getUseless31());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS32)) {
            builder.setUseless32(this.getUseless32());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS33)) {
            builder.setUseless33(this.getUseless33());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS34)) {
            builder.setUseless34(this.getUseless34());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS35)) {
            builder.setUseless35(this.getUseless35());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS36)) {
            builder.setUseless36(this.getUseless36());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS37)) {
            builder.setUseless37(this.getUseless37());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS38)) {
            builder.setUseless38(this.getUseless38());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS39)) {
            builder.setUseless39(this.getUseless39());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS40)) {
            builder.setUseless40(this.getUseless40());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS41)) {
            builder.setUseless41(this.getUseless41());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS42)) {
            builder.setUseless42(this.getUseless42());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS43)) {
            builder.setUseless43(this.getUseless43());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS44)) {
            builder.setUseless44(this.getUseless44());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS45)) {
            builder.setUseless45(this.getUseless45());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS46)) {
            builder.setUseless46(this.getUseless46());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS47)) {
            builder.setUseless47(this.getUseless47());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS48)) {
            builder.setUseless48(this.getUseless48());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS49)) {
            builder.setUseless49(this.getUseless49());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS50)) {
            builder.setUseless50(this.getUseless50());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS51)) {
            builder.setUseless51(this.getUseless51());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS52)) {
            builder.setUseless52(this.getUseless52());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS53)) {
            builder.setUseless53(this.getUseless53());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS54)) {
            builder.setUseless54(this.getUseless54());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS55)) {
            builder.setUseless55(this.getUseless55());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS56)) {
            builder.setUseless56(this.getUseless56());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS57)) {
            builder.setUseless57(this.getUseless57());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS58)) {
            builder.setUseless58(this.getUseless58());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS59)) {
            builder.setUseless59(this.getUseless59());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS60)) {
            builder.setUseless60(this.getUseless60());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS61)) {
            builder.setUseless61(this.getUseless61());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS62)) {
            builder.setUseless62(this.getUseless62());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS63)) {
            builder.setUseless63(this.getUseless63());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS64)) {
            builder.setUseless64(this.getUseless64());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPFIELD) && this.mapField != null) {
            final boolean needClear = !builder.hasMapField();
            final int tmpFieldCnt = this.mapField.copyChangeToDb(builder.getMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMapField();
            }
        }
        if (this.hasMark(FIELD_INDEX_LISTFIELD) && this.listField != null) {
            final boolean needClear = !builder.hasListField();
            final int tmpFieldCnt = this.listField.copyChangeToDb(builder.getListFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearListField();
            }
        }
        if (this.hasMark(FIELD_INDEX_USELESS101)) {
            builder.setUseless101(this.getUseless101());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS102)) {
            builder.setUseless102(this.getUseless102());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS103)) {
            builder.setUseless103(this.getUseless103());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS104)) {
            builder.setUseless104(this.getUseless104());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS105)) {
            builder.setUseless105(this.getUseless105());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS106)) {
            builder.setUseless106(this.getUseless106());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS107)) {
            builder.setUseless107(this.getUseless107());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS108)) {
            builder.setUseless108(this.getUseless108());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS109)) {
            builder.setUseless109(this.getUseless109());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS110)) {
            builder.setUseless110(this.getUseless110());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS111)) {
            builder.setUseless111(this.getUseless111());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS112)) {
            builder.setUseless112(this.getUseless112());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS113)) {
            builder.setUseless113(this.getUseless113());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS114)) {
            builder.setUseless114(this.getUseless114());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS115)) {
            builder.setUseless115(this.getUseless115());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS116)) {
            builder.setUseless116(this.getUseless116());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS117)) {
            builder.setUseless117(this.getUseless117());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS118)) {
            builder.setUseless118(this.getUseless118());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS119)) {
            builder.setUseless119(this.getUseless119());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS120)) {
            builder.setUseless120(this.getUseless120());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS121)) {
            builder.setUseless121(this.getUseless121());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS122)) {
            builder.setUseless122(this.getUseless122());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS123)) {
            builder.setUseless123(this.getUseless123());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS124)) {
            builder.setUseless124(this.getUseless124());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS125)) {
            builder.setUseless125(this.getUseless125());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS126)) {
            builder.setUseless126(this.getUseless126());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS127)) {
            builder.setUseless127(this.getUseless127());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS128)) {
            builder.setUseless128(this.getUseless128());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS129)) {
            builder.setUseless129(this.getUseless129());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS130)) {
            builder.setUseless130(this.getUseless130());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS131)) {
            builder.setUseless131(this.getUseless131());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS132)) {
            builder.setUseless132(this.getUseless132());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS133)) {
            builder.setUseless133(this.getUseless133());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS134)) {
            builder.setUseless134(this.getUseless134());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS135)) {
            builder.setUseless135(this.getUseless135());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS136)) {
            builder.setUseless136(this.getUseless136());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS137)) {
            builder.setUseless137(this.getUseless137());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS138)) {
            builder.setUseless138(this.getUseless138());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS139)) {
            builder.setUseless139(this.getUseless139());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS140)) {
            builder.setUseless140(this.getUseless140());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS141)) {
            builder.setUseless141(this.getUseless141());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS142)) {
            builder.setUseless142(this.getUseless142());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS143)) {
            builder.setUseless143(this.getUseless143());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS144)) {
            builder.setUseless144(this.getUseless144());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS145)) {
            builder.setUseless145(this.getUseless145());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS146)) {
            builder.setUseless146(this.getUseless146());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS147)) {
            builder.setUseless147(this.getUseless147());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS148)) {
            builder.setUseless148(this.getUseless148());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS149)) {
            builder.setUseless149(this.getUseless149());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS150)) {
            builder.setUseless150(this.getUseless150());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS151)) {
            builder.setUseless151(this.getUseless151());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS152)) {
            builder.setUseless152(this.getUseless152());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS153)) {
            builder.setUseless153(this.getUseless153());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS154)) {
            builder.setUseless154(this.getUseless154());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS155)) {
            builder.setUseless155(this.getUseless155());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS156)) {
            builder.setUseless156(this.getUseless156());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS157)) {
            builder.setUseless157(this.getUseless157());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS158)) {
            builder.setUseless158(this.getUseless158());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS159)) {
            builder.setUseless159(this.getUseless159());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS160)) {
            builder.setUseless160(this.getUseless160());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS161)) {
            builder.setUseless161(this.getUseless161());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS162)) {
            builder.setUseless162(this.getUseless162());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS163)) {
            builder.setUseless163(this.getUseless163());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS164)) {
            builder.setUseless164(this.getUseless164());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YOTESTMODEL) && this.yoTestModel != null) {
            final boolean needClear = !builder.hasYoTestModel();
            final int tmpFieldCnt = this.yoTestModel.copyChangeToDb(builder.getYoTestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_YOTESTUNIT) && this.yoTestUnit != null) {
            final boolean needClear = !builder.hasYoTestUnit();
            final int tmpFieldCnt = this.yoTestUnit.copyChangeToDb(builder.getYoTestUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETFIELD) && this.setField != null) {
            final boolean needClear = !builder.hasSetField();
            final int tmpFieldCnt = this.setField.copyChangeToDb(builder.getSetFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSetField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(YoTestEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntField()) {
            this.innerSetIntField(proto.getIntField());
        } else {
            this.innerSetIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLongField()) {
            this.innerSetLongField(proto.getLongField());
        } else {
            this.innerSetLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStringField()) {
            this.innerSetStringField(proto.getStringField());
        } else {
            this.innerSetStringField(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasBoolField()) {
            this.innerSetBoolField(proto.getBoolField());
        } else {
            this.innerSetBoolField(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless1()) {
            this.innerSetUseless1(proto.getUseless1());
        } else {
            this.innerSetUseless1(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless2()) {
            this.innerSetUseless2(proto.getUseless2());
        } else {
            this.innerSetUseless2(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless3()) {
            this.innerSetUseless3(proto.getUseless3());
        } else {
            this.innerSetUseless3(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless4()) {
            this.innerSetUseless4(proto.getUseless4());
        } else {
            this.innerSetUseless4(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless5()) {
            this.innerSetUseless5(proto.getUseless5());
        } else {
            this.innerSetUseless5(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless6()) {
            this.innerSetUseless6(proto.getUseless6());
        } else {
            this.innerSetUseless6(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless7()) {
            this.innerSetUseless7(proto.getUseless7());
        } else {
            this.innerSetUseless7(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless8()) {
            this.innerSetUseless8(proto.getUseless8());
        } else {
            this.innerSetUseless8(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless9()) {
            this.innerSetUseless9(proto.getUseless9());
        } else {
            this.innerSetUseless9(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless10()) {
            this.innerSetUseless10(proto.getUseless10());
        } else {
            this.innerSetUseless10(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless11()) {
            this.innerSetUseless11(proto.getUseless11());
        } else {
            this.innerSetUseless11(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless12()) {
            this.innerSetUseless12(proto.getUseless12());
        } else {
            this.innerSetUseless12(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless13()) {
            this.innerSetUseless13(proto.getUseless13());
        } else {
            this.innerSetUseless13(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless14()) {
            this.innerSetUseless14(proto.getUseless14());
        } else {
            this.innerSetUseless14(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless15()) {
            this.innerSetUseless15(proto.getUseless15());
        } else {
            this.innerSetUseless15(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless16()) {
            this.innerSetUseless16(proto.getUseless16());
        } else {
            this.innerSetUseless16(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless17()) {
            this.innerSetUseless17(proto.getUseless17());
        } else {
            this.innerSetUseless17(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless18()) {
            this.innerSetUseless18(proto.getUseless18());
        } else {
            this.innerSetUseless18(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless19()) {
            this.innerSetUseless19(proto.getUseless19());
        } else {
            this.innerSetUseless19(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless20()) {
            this.innerSetUseless20(proto.getUseless20());
        } else {
            this.innerSetUseless20(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless21()) {
            this.innerSetUseless21(proto.getUseless21());
        } else {
            this.innerSetUseless21(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless22()) {
            this.innerSetUseless22(proto.getUseless22());
        } else {
            this.innerSetUseless22(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless23()) {
            this.innerSetUseless23(proto.getUseless23());
        } else {
            this.innerSetUseless23(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless24()) {
            this.innerSetUseless24(proto.getUseless24());
        } else {
            this.innerSetUseless24(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless25()) {
            this.innerSetUseless25(proto.getUseless25());
        } else {
            this.innerSetUseless25(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless26()) {
            this.innerSetUseless26(proto.getUseless26());
        } else {
            this.innerSetUseless26(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless27()) {
            this.innerSetUseless27(proto.getUseless27());
        } else {
            this.innerSetUseless27(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless28()) {
            this.innerSetUseless28(proto.getUseless28());
        } else {
            this.innerSetUseless28(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless29()) {
            this.innerSetUseless29(proto.getUseless29());
        } else {
            this.innerSetUseless29(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless30()) {
            this.innerSetUseless30(proto.getUseless30());
        } else {
            this.innerSetUseless30(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless31()) {
            this.innerSetUseless31(proto.getUseless31());
        } else {
            this.innerSetUseless31(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless32()) {
            this.innerSetUseless32(proto.getUseless32());
        } else {
            this.innerSetUseless32(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless33()) {
            this.innerSetUseless33(proto.getUseless33());
        } else {
            this.innerSetUseless33(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless34()) {
            this.innerSetUseless34(proto.getUseless34());
        } else {
            this.innerSetUseless34(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless35()) {
            this.innerSetUseless35(proto.getUseless35());
        } else {
            this.innerSetUseless35(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless36()) {
            this.innerSetUseless36(proto.getUseless36());
        } else {
            this.innerSetUseless36(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless37()) {
            this.innerSetUseless37(proto.getUseless37());
        } else {
            this.innerSetUseless37(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless38()) {
            this.innerSetUseless38(proto.getUseless38());
        } else {
            this.innerSetUseless38(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless39()) {
            this.innerSetUseless39(proto.getUseless39());
        } else {
            this.innerSetUseless39(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless40()) {
            this.innerSetUseless40(proto.getUseless40());
        } else {
            this.innerSetUseless40(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless41()) {
            this.innerSetUseless41(proto.getUseless41());
        } else {
            this.innerSetUseless41(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless42()) {
            this.innerSetUseless42(proto.getUseless42());
        } else {
            this.innerSetUseless42(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless43()) {
            this.innerSetUseless43(proto.getUseless43());
        } else {
            this.innerSetUseless43(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless44()) {
            this.innerSetUseless44(proto.getUseless44());
        } else {
            this.innerSetUseless44(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless45()) {
            this.innerSetUseless45(proto.getUseless45());
        } else {
            this.innerSetUseless45(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless46()) {
            this.innerSetUseless46(proto.getUseless46());
        } else {
            this.innerSetUseless46(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless47()) {
            this.innerSetUseless47(proto.getUseless47());
        } else {
            this.innerSetUseless47(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless48()) {
            this.innerSetUseless48(proto.getUseless48());
        } else {
            this.innerSetUseless48(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless49()) {
            this.innerSetUseless49(proto.getUseless49());
        } else {
            this.innerSetUseless49(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless50()) {
            this.innerSetUseless50(proto.getUseless50());
        } else {
            this.innerSetUseless50(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless51()) {
            this.innerSetUseless51(proto.getUseless51());
        } else {
            this.innerSetUseless51(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless52()) {
            this.innerSetUseless52(proto.getUseless52());
        } else {
            this.innerSetUseless52(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless53()) {
            this.innerSetUseless53(proto.getUseless53());
        } else {
            this.innerSetUseless53(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless54()) {
            this.innerSetUseless54(proto.getUseless54());
        } else {
            this.innerSetUseless54(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless55()) {
            this.innerSetUseless55(proto.getUseless55());
        } else {
            this.innerSetUseless55(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless56()) {
            this.innerSetUseless56(proto.getUseless56());
        } else {
            this.innerSetUseless56(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless57()) {
            this.innerSetUseless57(proto.getUseless57());
        } else {
            this.innerSetUseless57(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless58()) {
            this.innerSetUseless58(proto.getUseless58());
        } else {
            this.innerSetUseless58(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless59()) {
            this.innerSetUseless59(proto.getUseless59());
        } else {
            this.innerSetUseless59(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless60()) {
            this.innerSetUseless60(proto.getUseless60());
        } else {
            this.innerSetUseless60(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless61()) {
            this.innerSetUseless61(proto.getUseless61());
        } else {
            this.innerSetUseless61(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless62()) {
            this.innerSetUseless62(proto.getUseless62());
        } else {
            this.innerSetUseless62(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless63()) {
            this.innerSetUseless63(proto.getUseless63());
        } else {
            this.innerSetUseless63(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless64()) {
            this.innerSetUseless64(proto.getUseless64());
        } else {
            this.innerSetUseless64(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasMapField()) {
            this.getMapField().mergeFromDb(proto.getMapField());
        } else {
            if (this.mapField != null) {
                this.mapField.mergeFromDb(proto.getMapField());
            }
        }
        if (proto.hasListField()) {
            this.getListField().mergeFromDb(proto.getListField());
        } else {
            if (this.listField != null) {
                this.listField.mergeFromDb(proto.getListField());
            }
        }
        if (proto.hasUseless101()) {
            this.innerSetUseless101(proto.getUseless101());
        } else {
            this.innerSetUseless101(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless102()) {
            this.innerSetUseless102(proto.getUseless102());
        } else {
            this.innerSetUseless102(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless103()) {
            this.innerSetUseless103(proto.getUseless103());
        } else {
            this.innerSetUseless103(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless104()) {
            this.innerSetUseless104(proto.getUseless104());
        } else {
            this.innerSetUseless104(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless105()) {
            this.innerSetUseless105(proto.getUseless105());
        } else {
            this.innerSetUseless105(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless106()) {
            this.innerSetUseless106(proto.getUseless106());
        } else {
            this.innerSetUseless106(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless107()) {
            this.innerSetUseless107(proto.getUseless107());
        } else {
            this.innerSetUseless107(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless108()) {
            this.innerSetUseless108(proto.getUseless108());
        } else {
            this.innerSetUseless108(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless109()) {
            this.innerSetUseless109(proto.getUseless109());
        } else {
            this.innerSetUseless109(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless110()) {
            this.innerSetUseless110(proto.getUseless110());
        } else {
            this.innerSetUseless110(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless111()) {
            this.innerSetUseless111(proto.getUseless111());
        } else {
            this.innerSetUseless111(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless112()) {
            this.innerSetUseless112(proto.getUseless112());
        } else {
            this.innerSetUseless112(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless113()) {
            this.innerSetUseless113(proto.getUseless113());
        } else {
            this.innerSetUseless113(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless114()) {
            this.innerSetUseless114(proto.getUseless114());
        } else {
            this.innerSetUseless114(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless115()) {
            this.innerSetUseless115(proto.getUseless115());
        } else {
            this.innerSetUseless115(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless116()) {
            this.innerSetUseless116(proto.getUseless116());
        } else {
            this.innerSetUseless116(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless117()) {
            this.innerSetUseless117(proto.getUseless117());
        } else {
            this.innerSetUseless117(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless118()) {
            this.innerSetUseless118(proto.getUseless118());
        } else {
            this.innerSetUseless118(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless119()) {
            this.innerSetUseless119(proto.getUseless119());
        } else {
            this.innerSetUseless119(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless120()) {
            this.innerSetUseless120(proto.getUseless120());
        } else {
            this.innerSetUseless120(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless121()) {
            this.innerSetUseless121(proto.getUseless121());
        } else {
            this.innerSetUseless121(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless122()) {
            this.innerSetUseless122(proto.getUseless122());
        } else {
            this.innerSetUseless122(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless123()) {
            this.innerSetUseless123(proto.getUseless123());
        } else {
            this.innerSetUseless123(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless124()) {
            this.innerSetUseless124(proto.getUseless124());
        } else {
            this.innerSetUseless124(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless125()) {
            this.innerSetUseless125(proto.getUseless125());
        } else {
            this.innerSetUseless125(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless126()) {
            this.innerSetUseless126(proto.getUseless126());
        } else {
            this.innerSetUseless126(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless127()) {
            this.innerSetUseless127(proto.getUseless127());
        } else {
            this.innerSetUseless127(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless128()) {
            this.innerSetUseless128(proto.getUseless128());
        } else {
            this.innerSetUseless128(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless129()) {
            this.innerSetUseless129(proto.getUseless129());
        } else {
            this.innerSetUseless129(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless130()) {
            this.innerSetUseless130(proto.getUseless130());
        } else {
            this.innerSetUseless130(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless131()) {
            this.innerSetUseless131(proto.getUseless131());
        } else {
            this.innerSetUseless131(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless132()) {
            this.innerSetUseless132(proto.getUseless132());
        } else {
            this.innerSetUseless132(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless133()) {
            this.innerSetUseless133(proto.getUseless133());
        } else {
            this.innerSetUseless133(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless134()) {
            this.innerSetUseless134(proto.getUseless134());
        } else {
            this.innerSetUseless134(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless135()) {
            this.innerSetUseless135(proto.getUseless135());
        } else {
            this.innerSetUseless135(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless136()) {
            this.innerSetUseless136(proto.getUseless136());
        } else {
            this.innerSetUseless136(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless137()) {
            this.innerSetUseless137(proto.getUseless137());
        } else {
            this.innerSetUseless137(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless138()) {
            this.innerSetUseless138(proto.getUseless138());
        } else {
            this.innerSetUseless138(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless139()) {
            this.innerSetUseless139(proto.getUseless139());
        } else {
            this.innerSetUseless139(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless140()) {
            this.innerSetUseless140(proto.getUseless140());
        } else {
            this.innerSetUseless140(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless141()) {
            this.innerSetUseless141(proto.getUseless141());
        } else {
            this.innerSetUseless141(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless142()) {
            this.innerSetUseless142(proto.getUseless142());
        } else {
            this.innerSetUseless142(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless143()) {
            this.innerSetUseless143(proto.getUseless143());
        } else {
            this.innerSetUseless143(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless144()) {
            this.innerSetUseless144(proto.getUseless144());
        } else {
            this.innerSetUseless144(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless145()) {
            this.innerSetUseless145(proto.getUseless145());
        } else {
            this.innerSetUseless145(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless146()) {
            this.innerSetUseless146(proto.getUseless146());
        } else {
            this.innerSetUseless146(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless147()) {
            this.innerSetUseless147(proto.getUseless147());
        } else {
            this.innerSetUseless147(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless148()) {
            this.innerSetUseless148(proto.getUseless148());
        } else {
            this.innerSetUseless148(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless149()) {
            this.innerSetUseless149(proto.getUseless149());
        } else {
            this.innerSetUseless149(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless150()) {
            this.innerSetUseless150(proto.getUseless150());
        } else {
            this.innerSetUseless150(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless151()) {
            this.innerSetUseless151(proto.getUseless151());
        } else {
            this.innerSetUseless151(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless152()) {
            this.innerSetUseless152(proto.getUseless152());
        } else {
            this.innerSetUseless152(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless153()) {
            this.innerSetUseless153(proto.getUseless153());
        } else {
            this.innerSetUseless153(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless154()) {
            this.innerSetUseless154(proto.getUseless154());
        } else {
            this.innerSetUseless154(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless155()) {
            this.innerSetUseless155(proto.getUseless155());
        } else {
            this.innerSetUseless155(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless156()) {
            this.innerSetUseless156(proto.getUseless156());
        } else {
            this.innerSetUseless156(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless157()) {
            this.innerSetUseless157(proto.getUseless157());
        } else {
            this.innerSetUseless157(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless158()) {
            this.innerSetUseless158(proto.getUseless158());
        } else {
            this.innerSetUseless158(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless159()) {
            this.innerSetUseless159(proto.getUseless159());
        } else {
            this.innerSetUseless159(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless160()) {
            this.innerSetUseless160(proto.getUseless160());
        } else {
            this.innerSetUseless160(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless161()) {
            this.innerSetUseless161(proto.getUseless161());
        } else {
            this.innerSetUseless161(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless162()) {
            this.innerSetUseless162(proto.getUseless162());
        } else {
            this.innerSetUseless162(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless163()) {
            this.innerSetUseless163(proto.getUseless163());
        } else {
            this.innerSetUseless163(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless164()) {
            this.innerSetUseless164(proto.getUseless164());
        } else {
            this.innerSetUseless164(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasYoTestModel()) {
            this.getYoTestModel().mergeFromDb(proto.getYoTestModel());
        } else {
            if (this.yoTestModel != null) {
                this.yoTestModel.mergeFromDb(proto.getYoTestModel());
            }
        }
        if (proto.hasYoTestUnit()) {
            this.getYoTestUnit().mergeFromDb(proto.getYoTestUnit());
        } else {
            if (this.yoTestUnit != null) {
                this.yoTestUnit.mergeFromDb(proto.getYoTestUnit());
            }
        }
        if (proto.hasSetField()) {
            this.getSetField().mergeFromDb(proto.getSetField());
        } else {
            if (this.setField != null) {
                this.setField.mergeFromDb(proto.getSetField());
            }
        }
        this.markAll();
        return YoTestProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(YoTestEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntField()) {
            this.setIntField(proto.getIntField());
            fieldCnt++;
        }
        if (proto.hasLongField()) {
            this.setLongField(proto.getLongField());
            fieldCnt++;
        }
        if (proto.hasStringField()) {
            this.setStringField(proto.getStringField());
            fieldCnt++;
        }
        if (proto.hasBoolField()) {
            this.setBoolField(proto.getBoolField());
            fieldCnt++;
        }
        if (proto.hasUseless1()) {
            this.setUseless1(proto.getUseless1());
            fieldCnt++;
        }
        if (proto.hasUseless2()) {
            this.setUseless2(proto.getUseless2());
            fieldCnt++;
        }
        if (proto.hasUseless3()) {
            this.setUseless3(proto.getUseless3());
            fieldCnt++;
        }
        if (proto.hasUseless4()) {
            this.setUseless4(proto.getUseless4());
            fieldCnt++;
        }
        if (proto.hasUseless5()) {
            this.setUseless5(proto.getUseless5());
            fieldCnt++;
        }
        if (proto.hasUseless6()) {
            this.setUseless6(proto.getUseless6());
            fieldCnt++;
        }
        if (proto.hasUseless7()) {
            this.setUseless7(proto.getUseless7());
            fieldCnt++;
        }
        if (proto.hasUseless8()) {
            this.setUseless8(proto.getUseless8());
            fieldCnt++;
        }
        if (proto.hasUseless9()) {
            this.setUseless9(proto.getUseless9());
            fieldCnt++;
        }
        if (proto.hasUseless10()) {
            this.setUseless10(proto.getUseless10());
            fieldCnt++;
        }
        if (proto.hasUseless11()) {
            this.setUseless11(proto.getUseless11());
            fieldCnt++;
        }
        if (proto.hasUseless12()) {
            this.setUseless12(proto.getUseless12());
            fieldCnt++;
        }
        if (proto.hasUseless13()) {
            this.setUseless13(proto.getUseless13());
            fieldCnt++;
        }
        if (proto.hasUseless14()) {
            this.setUseless14(proto.getUseless14());
            fieldCnt++;
        }
        if (proto.hasUseless15()) {
            this.setUseless15(proto.getUseless15());
            fieldCnt++;
        }
        if (proto.hasUseless16()) {
            this.setUseless16(proto.getUseless16());
            fieldCnt++;
        }
        if (proto.hasUseless17()) {
            this.setUseless17(proto.getUseless17());
            fieldCnt++;
        }
        if (proto.hasUseless18()) {
            this.setUseless18(proto.getUseless18());
            fieldCnt++;
        }
        if (proto.hasUseless19()) {
            this.setUseless19(proto.getUseless19());
            fieldCnt++;
        }
        if (proto.hasUseless20()) {
            this.setUseless20(proto.getUseless20());
            fieldCnt++;
        }
        if (proto.hasUseless21()) {
            this.setUseless21(proto.getUseless21());
            fieldCnt++;
        }
        if (proto.hasUseless22()) {
            this.setUseless22(proto.getUseless22());
            fieldCnt++;
        }
        if (proto.hasUseless23()) {
            this.setUseless23(proto.getUseless23());
            fieldCnt++;
        }
        if (proto.hasUseless24()) {
            this.setUseless24(proto.getUseless24());
            fieldCnt++;
        }
        if (proto.hasUseless25()) {
            this.setUseless25(proto.getUseless25());
            fieldCnt++;
        }
        if (proto.hasUseless26()) {
            this.setUseless26(proto.getUseless26());
            fieldCnt++;
        }
        if (proto.hasUseless27()) {
            this.setUseless27(proto.getUseless27());
            fieldCnt++;
        }
        if (proto.hasUseless28()) {
            this.setUseless28(proto.getUseless28());
            fieldCnt++;
        }
        if (proto.hasUseless29()) {
            this.setUseless29(proto.getUseless29());
            fieldCnt++;
        }
        if (proto.hasUseless30()) {
            this.setUseless30(proto.getUseless30());
            fieldCnt++;
        }
        if (proto.hasUseless31()) {
            this.setUseless31(proto.getUseless31());
            fieldCnt++;
        }
        if (proto.hasUseless32()) {
            this.setUseless32(proto.getUseless32());
            fieldCnt++;
        }
        if (proto.hasUseless33()) {
            this.setUseless33(proto.getUseless33());
            fieldCnt++;
        }
        if (proto.hasUseless34()) {
            this.setUseless34(proto.getUseless34());
            fieldCnt++;
        }
        if (proto.hasUseless35()) {
            this.setUseless35(proto.getUseless35());
            fieldCnt++;
        }
        if (proto.hasUseless36()) {
            this.setUseless36(proto.getUseless36());
            fieldCnt++;
        }
        if (proto.hasUseless37()) {
            this.setUseless37(proto.getUseless37());
            fieldCnt++;
        }
        if (proto.hasUseless38()) {
            this.setUseless38(proto.getUseless38());
            fieldCnt++;
        }
        if (proto.hasUseless39()) {
            this.setUseless39(proto.getUseless39());
            fieldCnt++;
        }
        if (proto.hasUseless40()) {
            this.setUseless40(proto.getUseless40());
            fieldCnt++;
        }
        if (proto.hasUseless41()) {
            this.setUseless41(proto.getUseless41());
            fieldCnt++;
        }
        if (proto.hasUseless42()) {
            this.setUseless42(proto.getUseless42());
            fieldCnt++;
        }
        if (proto.hasUseless43()) {
            this.setUseless43(proto.getUseless43());
            fieldCnt++;
        }
        if (proto.hasUseless44()) {
            this.setUseless44(proto.getUseless44());
            fieldCnt++;
        }
        if (proto.hasUseless45()) {
            this.setUseless45(proto.getUseless45());
            fieldCnt++;
        }
        if (proto.hasUseless46()) {
            this.setUseless46(proto.getUseless46());
            fieldCnt++;
        }
        if (proto.hasUseless47()) {
            this.setUseless47(proto.getUseless47());
            fieldCnt++;
        }
        if (proto.hasUseless48()) {
            this.setUseless48(proto.getUseless48());
            fieldCnt++;
        }
        if (proto.hasUseless49()) {
            this.setUseless49(proto.getUseless49());
            fieldCnt++;
        }
        if (proto.hasUseless50()) {
            this.setUseless50(proto.getUseless50());
            fieldCnt++;
        }
        if (proto.hasUseless51()) {
            this.setUseless51(proto.getUseless51());
            fieldCnt++;
        }
        if (proto.hasUseless52()) {
            this.setUseless52(proto.getUseless52());
            fieldCnt++;
        }
        if (proto.hasUseless53()) {
            this.setUseless53(proto.getUseless53());
            fieldCnt++;
        }
        if (proto.hasUseless54()) {
            this.setUseless54(proto.getUseless54());
            fieldCnt++;
        }
        if (proto.hasUseless55()) {
            this.setUseless55(proto.getUseless55());
            fieldCnt++;
        }
        if (proto.hasUseless56()) {
            this.setUseless56(proto.getUseless56());
            fieldCnt++;
        }
        if (proto.hasUseless57()) {
            this.setUseless57(proto.getUseless57());
            fieldCnt++;
        }
        if (proto.hasUseless58()) {
            this.setUseless58(proto.getUseless58());
            fieldCnt++;
        }
        if (proto.hasUseless59()) {
            this.setUseless59(proto.getUseless59());
            fieldCnt++;
        }
        if (proto.hasUseless60()) {
            this.setUseless60(proto.getUseless60());
            fieldCnt++;
        }
        if (proto.hasUseless61()) {
            this.setUseless61(proto.getUseless61());
            fieldCnt++;
        }
        if (proto.hasUseless62()) {
            this.setUseless62(proto.getUseless62());
            fieldCnt++;
        }
        if (proto.hasUseless63()) {
            this.setUseless63(proto.getUseless63());
            fieldCnt++;
        }
        if (proto.hasUseless64()) {
            this.setUseless64(proto.getUseless64());
            fieldCnt++;
        }
        if (proto.hasMapField()) {
            this.getMapField().mergeChangeFromDb(proto.getMapField());
            fieldCnt++;
        }
        if (proto.hasListField()) {
            this.getListField().mergeChangeFromDb(proto.getListField());
            fieldCnt++;
        }
        if (proto.hasUseless101()) {
            this.setUseless101(proto.getUseless101());
            fieldCnt++;
        }
        if (proto.hasUseless102()) {
            this.setUseless102(proto.getUseless102());
            fieldCnt++;
        }
        if (proto.hasUseless103()) {
            this.setUseless103(proto.getUseless103());
            fieldCnt++;
        }
        if (proto.hasUseless104()) {
            this.setUseless104(proto.getUseless104());
            fieldCnt++;
        }
        if (proto.hasUseless105()) {
            this.setUseless105(proto.getUseless105());
            fieldCnt++;
        }
        if (proto.hasUseless106()) {
            this.setUseless106(proto.getUseless106());
            fieldCnt++;
        }
        if (proto.hasUseless107()) {
            this.setUseless107(proto.getUseless107());
            fieldCnt++;
        }
        if (proto.hasUseless108()) {
            this.setUseless108(proto.getUseless108());
            fieldCnt++;
        }
        if (proto.hasUseless109()) {
            this.setUseless109(proto.getUseless109());
            fieldCnt++;
        }
        if (proto.hasUseless110()) {
            this.setUseless110(proto.getUseless110());
            fieldCnt++;
        }
        if (proto.hasUseless111()) {
            this.setUseless111(proto.getUseless111());
            fieldCnt++;
        }
        if (proto.hasUseless112()) {
            this.setUseless112(proto.getUseless112());
            fieldCnt++;
        }
        if (proto.hasUseless113()) {
            this.setUseless113(proto.getUseless113());
            fieldCnt++;
        }
        if (proto.hasUseless114()) {
            this.setUseless114(proto.getUseless114());
            fieldCnt++;
        }
        if (proto.hasUseless115()) {
            this.setUseless115(proto.getUseless115());
            fieldCnt++;
        }
        if (proto.hasUseless116()) {
            this.setUseless116(proto.getUseless116());
            fieldCnt++;
        }
        if (proto.hasUseless117()) {
            this.setUseless117(proto.getUseless117());
            fieldCnt++;
        }
        if (proto.hasUseless118()) {
            this.setUseless118(proto.getUseless118());
            fieldCnt++;
        }
        if (proto.hasUseless119()) {
            this.setUseless119(proto.getUseless119());
            fieldCnt++;
        }
        if (proto.hasUseless120()) {
            this.setUseless120(proto.getUseless120());
            fieldCnt++;
        }
        if (proto.hasUseless121()) {
            this.setUseless121(proto.getUseless121());
            fieldCnt++;
        }
        if (proto.hasUseless122()) {
            this.setUseless122(proto.getUseless122());
            fieldCnt++;
        }
        if (proto.hasUseless123()) {
            this.setUseless123(proto.getUseless123());
            fieldCnt++;
        }
        if (proto.hasUseless124()) {
            this.setUseless124(proto.getUseless124());
            fieldCnt++;
        }
        if (proto.hasUseless125()) {
            this.setUseless125(proto.getUseless125());
            fieldCnt++;
        }
        if (proto.hasUseless126()) {
            this.setUseless126(proto.getUseless126());
            fieldCnt++;
        }
        if (proto.hasUseless127()) {
            this.setUseless127(proto.getUseless127());
            fieldCnt++;
        }
        if (proto.hasUseless128()) {
            this.setUseless128(proto.getUseless128());
            fieldCnt++;
        }
        if (proto.hasUseless129()) {
            this.setUseless129(proto.getUseless129());
            fieldCnt++;
        }
        if (proto.hasUseless130()) {
            this.setUseless130(proto.getUseless130());
            fieldCnt++;
        }
        if (proto.hasUseless131()) {
            this.setUseless131(proto.getUseless131());
            fieldCnt++;
        }
        if (proto.hasUseless132()) {
            this.setUseless132(proto.getUseless132());
            fieldCnt++;
        }
        if (proto.hasUseless133()) {
            this.setUseless133(proto.getUseless133());
            fieldCnt++;
        }
        if (proto.hasUseless134()) {
            this.setUseless134(proto.getUseless134());
            fieldCnt++;
        }
        if (proto.hasUseless135()) {
            this.setUseless135(proto.getUseless135());
            fieldCnt++;
        }
        if (proto.hasUseless136()) {
            this.setUseless136(proto.getUseless136());
            fieldCnt++;
        }
        if (proto.hasUseless137()) {
            this.setUseless137(proto.getUseless137());
            fieldCnt++;
        }
        if (proto.hasUseless138()) {
            this.setUseless138(proto.getUseless138());
            fieldCnt++;
        }
        if (proto.hasUseless139()) {
            this.setUseless139(proto.getUseless139());
            fieldCnt++;
        }
        if (proto.hasUseless140()) {
            this.setUseless140(proto.getUseless140());
            fieldCnt++;
        }
        if (proto.hasUseless141()) {
            this.setUseless141(proto.getUseless141());
            fieldCnt++;
        }
        if (proto.hasUseless142()) {
            this.setUseless142(proto.getUseless142());
            fieldCnt++;
        }
        if (proto.hasUseless143()) {
            this.setUseless143(proto.getUseless143());
            fieldCnt++;
        }
        if (proto.hasUseless144()) {
            this.setUseless144(proto.getUseless144());
            fieldCnt++;
        }
        if (proto.hasUseless145()) {
            this.setUseless145(proto.getUseless145());
            fieldCnt++;
        }
        if (proto.hasUseless146()) {
            this.setUseless146(proto.getUseless146());
            fieldCnt++;
        }
        if (proto.hasUseless147()) {
            this.setUseless147(proto.getUseless147());
            fieldCnt++;
        }
        if (proto.hasUseless148()) {
            this.setUseless148(proto.getUseless148());
            fieldCnt++;
        }
        if (proto.hasUseless149()) {
            this.setUseless149(proto.getUseless149());
            fieldCnt++;
        }
        if (proto.hasUseless150()) {
            this.setUseless150(proto.getUseless150());
            fieldCnt++;
        }
        if (proto.hasUseless151()) {
            this.setUseless151(proto.getUseless151());
            fieldCnt++;
        }
        if (proto.hasUseless152()) {
            this.setUseless152(proto.getUseless152());
            fieldCnt++;
        }
        if (proto.hasUseless153()) {
            this.setUseless153(proto.getUseless153());
            fieldCnt++;
        }
        if (proto.hasUseless154()) {
            this.setUseless154(proto.getUseless154());
            fieldCnt++;
        }
        if (proto.hasUseless155()) {
            this.setUseless155(proto.getUseless155());
            fieldCnt++;
        }
        if (proto.hasUseless156()) {
            this.setUseless156(proto.getUseless156());
            fieldCnt++;
        }
        if (proto.hasUseless157()) {
            this.setUseless157(proto.getUseless157());
            fieldCnt++;
        }
        if (proto.hasUseless158()) {
            this.setUseless158(proto.getUseless158());
            fieldCnt++;
        }
        if (proto.hasUseless159()) {
            this.setUseless159(proto.getUseless159());
            fieldCnt++;
        }
        if (proto.hasUseless160()) {
            this.setUseless160(proto.getUseless160());
            fieldCnt++;
        }
        if (proto.hasUseless161()) {
            this.setUseless161(proto.getUseless161());
            fieldCnt++;
        }
        if (proto.hasUseless162()) {
            this.setUseless162(proto.getUseless162());
            fieldCnt++;
        }
        if (proto.hasUseless163()) {
            this.setUseless163(proto.getUseless163());
            fieldCnt++;
        }
        if (proto.hasUseless164()) {
            this.setUseless164(proto.getUseless164());
            fieldCnt++;
        }
        if (proto.hasYoTestModel()) {
            this.getYoTestModel().mergeChangeFromDb(proto.getYoTestModel());
            fieldCnt++;
        }
        if (proto.hasYoTestUnit()) {
            this.getYoTestUnit().mergeChangeFromDb(proto.getYoTestUnit());
            fieldCnt++;
        }
        if (proto.hasSetField()) {
            this.getSetField().mergeChangeFromDb(proto.getSetField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestEntity.Builder getCopySsBuilder() {
        final YoTestEntity.Builder builder = YoTestEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoTestEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIntField() != 0) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }  else if (builder.hasIntField()) {
            // 清理IntField
            builder.clearIntField();
            fieldCnt++;
        }
        if (this.getLongField() != 0L) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }  else if (builder.hasLongField()) {
            // 清理LongField
            builder.clearLongField();
            fieldCnt++;
        }
        if (!this.getStringField().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }  else if (builder.hasStringField()) {
            // 清理StringField
            builder.clearStringField();
            fieldCnt++;
        }
        if (this.getBoolField()) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }  else if (builder.hasBoolField()) {
            // 清理BoolField
            builder.clearBoolField();
            fieldCnt++;
        }
        if (this.getUseless1()) {
            builder.setUseless1(this.getUseless1());
            fieldCnt++;
        }  else if (builder.hasUseless1()) {
            // 清理Useless1
            builder.clearUseless1();
            fieldCnt++;
        }
        if (this.getUseless2()) {
            builder.setUseless2(this.getUseless2());
            fieldCnt++;
        }  else if (builder.hasUseless2()) {
            // 清理Useless2
            builder.clearUseless2();
            fieldCnt++;
        }
        if (this.getUseless3()) {
            builder.setUseless3(this.getUseless3());
            fieldCnt++;
        }  else if (builder.hasUseless3()) {
            // 清理Useless3
            builder.clearUseless3();
            fieldCnt++;
        }
        if (this.getUseless4()) {
            builder.setUseless4(this.getUseless4());
            fieldCnt++;
        }  else if (builder.hasUseless4()) {
            // 清理Useless4
            builder.clearUseless4();
            fieldCnt++;
        }
        if (this.getUseless5()) {
            builder.setUseless5(this.getUseless5());
            fieldCnt++;
        }  else if (builder.hasUseless5()) {
            // 清理Useless5
            builder.clearUseless5();
            fieldCnt++;
        }
        if (this.getUseless6()) {
            builder.setUseless6(this.getUseless6());
            fieldCnt++;
        }  else if (builder.hasUseless6()) {
            // 清理Useless6
            builder.clearUseless6();
            fieldCnt++;
        }
        if (this.getUseless7()) {
            builder.setUseless7(this.getUseless7());
            fieldCnt++;
        }  else if (builder.hasUseless7()) {
            // 清理Useless7
            builder.clearUseless7();
            fieldCnt++;
        }
        if (this.getUseless8()) {
            builder.setUseless8(this.getUseless8());
            fieldCnt++;
        }  else if (builder.hasUseless8()) {
            // 清理Useless8
            builder.clearUseless8();
            fieldCnt++;
        }
        if (this.getUseless9()) {
            builder.setUseless9(this.getUseless9());
            fieldCnt++;
        }  else if (builder.hasUseless9()) {
            // 清理Useless9
            builder.clearUseless9();
            fieldCnt++;
        }
        if (this.getUseless10()) {
            builder.setUseless10(this.getUseless10());
            fieldCnt++;
        }  else if (builder.hasUseless10()) {
            // 清理Useless10
            builder.clearUseless10();
            fieldCnt++;
        }
        if (this.getUseless11()) {
            builder.setUseless11(this.getUseless11());
            fieldCnt++;
        }  else if (builder.hasUseless11()) {
            // 清理Useless11
            builder.clearUseless11();
            fieldCnt++;
        }
        if (this.getUseless12()) {
            builder.setUseless12(this.getUseless12());
            fieldCnt++;
        }  else if (builder.hasUseless12()) {
            // 清理Useless12
            builder.clearUseless12();
            fieldCnt++;
        }
        if (this.getUseless13()) {
            builder.setUseless13(this.getUseless13());
            fieldCnt++;
        }  else if (builder.hasUseless13()) {
            // 清理Useless13
            builder.clearUseless13();
            fieldCnt++;
        }
        if (this.getUseless14()) {
            builder.setUseless14(this.getUseless14());
            fieldCnt++;
        }  else if (builder.hasUseless14()) {
            // 清理Useless14
            builder.clearUseless14();
            fieldCnt++;
        }
        if (this.getUseless15()) {
            builder.setUseless15(this.getUseless15());
            fieldCnt++;
        }  else if (builder.hasUseless15()) {
            // 清理Useless15
            builder.clearUseless15();
            fieldCnt++;
        }
        if (this.getUseless16()) {
            builder.setUseless16(this.getUseless16());
            fieldCnt++;
        }  else if (builder.hasUseless16()) {
            // 清理Useless16
            builder.clearUseless16();
            fieldCnt++;
        }
        if (this.getUseless17()) {
            builder.setUseless17(this.getUseless17());
            fieldCnt++;
        }  else if (builder.hasUseless17()) {
            // 清理Useless17
            builder.clearUseless17();
            fieldCnt++;
        }
        if (this.getUseless18()) {
            builder.setUseless18(this.getUseless18());
            fieldCnt++;
        }  else if (builder.hasUseless18()) {
            // 清理Useless18
            builder.clearUseless18();
            fieldCnt++;
        }
        if (this.getUseless19()) {
            builder.setUseless19(this.getUseless19());
            fieldCnt++;
        }  else if (builder.hasUseless19()) {
            // 清理Useless19
            builder.clearUseless19();
            fieldCnt++;
        }
        if (this.getUseless20()) {
            builder.setUseless20(this.getUseless20());
            fieldCnt++;
        }  else if (builder.hasUseless20()) {
            // 清理Useless20
            builder.clearUseless20();
            fieldCnt++;
        }
        if (this.getUseless21()) {
            builder.setUseless21(this.getUseless21());
            fieldCnt++;
        }  else if (builder.hasUseless21()) {
            // 清理Useless21
            builder.clearUseless21();
            fieldCnt++;
        }
        if (this.getUseless22()) {
            builder.setUseless22(this.getUseless22());
            fieldCnt++;
        }  else if (builder.hasUseless22()) {
            // 清理Useless22
            builder.clearUseless22();
            fieldCnt++;
        }
        if (this.getUseless23()) {
            builder.setUseless23(this.getUseless23());
            fieldCnt++;
        }  else if (builder.hasUseless23()) {
            // 清理Useless23
            builder.clearUseless23();
            fieldCnt++;
        }
        if (this.getUseless24()) {
            builder.setUseless24(this.getUseless24());
            fieldCnt++;
        }  else if (builder.hasUseless24()) {
            // 清理Useless24
            builder.clearUseless24();
            fieldCnt++;
        }
        if (this.getUseless25()) {
            builder.setUseless25(this.getUseless25());
            fieldCnt++;
        }  else if (builder.hasUseless25()) {
            // 清理Useless25
            builder.clearUseless25();
            fieldCnt++;
        }
        if (this.getUseless26()) {
            builder.setUseless26(this.getUseless26());
            fieldCnt++;
        }  else if (builder.hasUseless26()) {
            // 清理Useless26
            builder.clearUseless26();
            fieldCnt++;
        }
        if (this.getUseless27()) {
            builder.setUseless27(this.getUseless27());
            fieldCnt++;
        }  else if (builder.hasUseless27()) {
            // 清理Useless27
            builder.clearUseless27();
            fieldCnt++;
        }
        if (this.getUseless28()) {
            builder.setUseless28(this.getUseless28());
            fieldCnt++;
        }  else if (builder.hasUseless28()) {
            // 清理Useless28
            builder.clearUseless28();
            fieldCnt++;
        }
        if (this.getUseless29()) {
            builder.setUseless29(this.getUseless29());
            fieldCnt++;
        }  else if (builder.hasUseless29()) {
            // 清理Useless29
            builder.clearUseless29();
            fieldCnt++;
        }
        if (this.getUseless30()) {
            builder.setUseless30(this.getUseless30());
            fieldCnt++;
        }  else if (builder.hasUseless30()) {
            // 清理Useless30
            builder.clearUseless30();
            fieldCnt++;
        }
        if (this.getUseless31()) {
            builder.setUseless31(this.getUseless31());
            fieldCnt++;
        }  else if (builder.hasUseless31()) {
            // 清理Useless31
            builder.clearUseless31();
            fieldCnt++;
        }
        if (this.getUseless32()) {
            builder.setUseless32(this.getUseless32());
            fieldCnt++;
        }  else if (builder.hasUseless32()) {
            // 清理Useless32
            builder.clearUseless32();
            fieldCnt++;
        }
        if (this.getUseless33()) {
            builder.setUseless33(this.getUseless33());
            fieldCnt++;
        }  else if (builder.hasUseless33()) {
            // 清理Useless33
            builder.clearUseless33();
            fieldCnt++;
        }
        if (this.getUseless34()) {
            builder.setUseless34(this.getUseless34());
            fieldCnt++;
        }  else if (builder.hasUseless34()) {
            // 清理Useless34
            builder.clearUseless34();
            fieldCnt++;
        }
        if (this.getUseless35()) {
            builder.setUseless35(this.getUseless35());
            fieldCnt++;
        }  else if (builder.hasUseless35()) {
            // 清理Useless35
            builder.clearUseless35();
            fieldCnt++;
        }
        if (this.getUseless36()) {
            builder.setUseless36(this.getUseless36());
            fieldCnt++;
        }  else if (builder.hasUseless36()) {
            // 清理Useless36
            builder.clearUseless36();
            fieldCnt++;
        }
        if (this.getUseless37()) {
            builder.setUseless37(this.getUseless37());
            fieldCnt++;
        }  else if (builder.hasUseless37()) {
            // 清理Useless37
            builder.clearUseless37();
            fieldCnt++;
        }
        if (this.getUseless38()) {
            builder.setUseless38(this.getUseless38());
            fieldCnt++;
        }  else if (builder.hasUseless38()) {
            // 清理Useless38
            builder.clearUseless38();
            fieldCnt++;
        }
        if (this.getUseless39()) {
            builder.setUseless39(this.getUseless39());
            fieldCnt++;
        }  else if (builder.hasUseless39()) {
            // 清理Useless39
            builder.clearUseless39();
            fieldCnt++;
        }
        if (this.getUseless40()) {
            builder.setUseless40(this.getUseless40());
            fieldCnt++;
        }  else if (builder.hasUseless40()) {
            // 清理Useless40
            builder.clearUseless40();
            fieldCnt++;
        }
        if (this.getUseless41()) {
            builder.setUseless41(this.getUseless41());
            fieldCnt++;
        }  else if (builder.hasUseless41()) {
            // 清理Useless41
            builder.clearUseless41();
            fieldCnt++;
        }
        if (this.getUseless42()) {
            builder.setUseless42(this.getUseless42());
            fieldCnt++;
        }  else if (builder.hasUseless42()) {
            // 清理Useless42
            builder.clearUseless42();
            fieldCnt++;
        }
        if (this.getUseless43()) {
            builder.setUseless43(this.getUseless43());
            fieldCnt++;
        }  else if (builder.hasUseless43()) {
            // 清理Useless43
            builder.clearUseless43();
            fieldCnt++;
        }
        if (this.getUseless44()) {
            builder.setUseless44(this.getUseless44());
            fieldCnt++;
        }  else if (builder.hasUseless44()) {
            // 清理Useless44
            builder.clearUseless44();
            fieldCnt++;
        }
        if (this.getUseless45()) {
            builder.setUseless45(this.getUseless45());
            fieldCnt++;
        }  else if (builder.hasUseless45()) {
            // 清理Useless45
            builder.clearUseless45();
            fieldCnt++;
        }
        if (this.getUseless46()) {
            builder.setUseless46(this.getUseless46());
            fieldCnt++;
        }  else if (builder.hasUseless46()) {
            // 清理Useless46
            builder.clearUseless46();
            fieldCnt++;
        }
        if (this.getUseless47()) {
            builder.setUseless47(this.getUseless47());
            fieldCnt++;
        }  else if (builder.hasUseless47()) {
            // 清理Useless47
            builder.clearUseless47();
            fieldCnt++;
        }
        if (this.getUseless48()) {
            builder.setUseless48(this.getUseless48());
            fieldCnt++;
        }  else if (builder.hasUseless48()) {
            // 清理Useless48
            builder.clearUseless48();
            fieldCnt++;
        }
        if (this.getUseless49()) {
            builder.setUseless49(this.getUseless49());
            fieldCnt++;
        }  else if (builder.hasUseless49()) {
            // 清理Useless49
            builder.clearUseless49();
            fieldCnt++;
        }
        if (this.getUseless50()) {
            builder.setUseless50(this.getUseless50());
            fieldCnt++;
        }  else if (builder.hasUseless50()) {
            // 清理Useless50
            builder.clearUseless50();
            fieldCnt++;
        }
        if (this.getUseless51()) {
            builder.setUseless51(this.getUseless51());
            fieldCnt++;
        }  else if (builder.hasUseless51()) {
            // 清理Useless51
            builder.clearUseless51();
            fieldCnt++;
        }
        if (this.getUseless52()) {
            builder.setUseless52(this.getUseless52());
            fieldCnt++;
        }  else if (builder.hasUseless52()) {
            // 清理Useless52
            builder.clearUseless52();
            fieldCnt++;
        }
        if (this.getUseless53()) {
            builder.setUseless53(this.getUseless53());
            fieldCnt++;
        }  else if (builder.hasUseless53()) {
            // 清理Useless53
            builder.clearUseless53();
            fieldCnt++;
        }
        if (this.getUseless54()) {
            builder.setUseless54(this.getUseless54());
            fieldCnt++;
        }  else if (builder.hasUseless54()) {
            // 清理Useless54
            builder.clearUseless54();
            fieldCnt++;
        }
        if (this.getUseless55()) {
            builder.setUseless55(this.getUseless55());
            fieldCnt++;
        }  else if (builder.hasUseless55()) {
            // 清理Useless55
            builder.clearUseless55();
            fieldCnt++;
        }
        if (this.getUseless56()) {
            builder.setUseless56(this.getUseless56());
            fieldCnt++;
        }  else if (builder.hasUseless56()) {
            // 清理Useless56
            builder.clearUseless56();
            fieldCnt++;
        }
        if (this.getUseless57()) {
            builder.setUseless57(this.getUseless57());
            fieldCnt++;
        }  else if (builder.hasUseless57()) {
            // 清理Useless57
            builder.clearUseless57();
            fieldCnt++;
        }
        if (this.getUseless58()) {
            builder.setUseless58(this.getUseless58());
            fieldCnt++;
        }  else if (builder.hasUseless58()) {
            // 清理Useless58
            builder.clearUseless58();
            fieldCnt++;
        }
        if (this.getUseless59()) {
            builder.setUseless59(this.getUseless59());
            fieldCnt++;
        }  else if (builder.hasUseless59()) {
            // 清理Useless59
            builder.clearUseless59();
            fieldCnt++;
        }
        if (this.getUseless60()) {
            builder.setUseless60(this.getUseless60());
            fieldCnt++;
        }  else if (builder.hasUseless60()) {
            // 清理Useless60
            builder.clearUseless60();
            fieldCnt++;
        }
        if (this.getUseless61()) {
            builder.setUseless61(this.getUseless61());
            fieldCnt++;
        }  else if (builder.hasUseless61()) {
            // 清理Useless61
            builder.clearUseless61();
            fieldCnt++;
        }
        if (this.getUseless62()) {
            builder.setUseless62(this.getUseless62());
            fieldCnt++;
        }  else if (builder.hasUseless62()) {
            // 清理Useless62
            builder.clearUseless62();
            fieldCnt++;
        }
        if (this.getUseless63()) {
            builder.setUseless63(this.getUseless63());
            fieldCnt++;
        }  else if (builder.hasUseless63()) {
            // 清理Useless63
            builder.clearUseless63();
            fieldCnt++;
        }
        if (this.getUseless64()) {
            builder.setUseless64(this.getUseless64());
            fieldCnt++;
        }  else if (builder.hasUseless64()) {
            // 清理Useless64
            builder.clearUseless64();
            fieldCnt++;
        }
        if (this.mapField != null) {
            YoTest.Int64YoTestMapMap.Builder tmpBuilder = YoTest.Int64YoTestMapMap.newBuilder();
            final int tmpFieldCnt = this.mapField.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMapField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMapField();
            }
        }  else if (builder.hasMapField()) {
            // 清理MapField
            builder.clearMapField();
            fieldCnt++;
        }
        if (this.listField != null) {
            YoTest.YoTestListList.Builder tmpBuilder = YoTest.YoTestListList.newBuilder();
            final int tmpFieldCnt = this.listField.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setListField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearListField();
            }
        }  else if (builder.hasListField()) {
            // 清理ListField
            builder.clearListField();
            fieldCnt++;
        }
        if (this.getUseless101()) {
            builder.setUseless101(this.getUseless101());
            fieldCnt++;
        }  else if (builder.hasUseless101()) {
            // 清理Useless101
            builder.clearUseless101();
            fieldCnt++;
        }
        if (this.getUseless102()) {
            builder.setUseless102(this.getUseless102());
            fieldCnt++;
        }  else if (builder.hasUseless102()) {
            // 清理Useless102
            builder.clearUseless102();
            fieldCnt++;
        }
        if (this.getUseless103()) {
            builder.setUseless103(this.getUseless103());
            fieldCnt++;
        }  else if (builder.hasUseless103()) {
            // 清理Useless103
            builder.clearUseless103();
            fieldCnt++;
        }
        if (this.getUseless104()) {
            builder.setUseless104(this.getUseless104());
            fieldCnt++;
        }  else if (builder.hasUseless104()) {
            // 清理Useless104
            builder.clearUseless104();
            fieldCnt++;
        }
        if (this.getUseless105()) {
            builder.setUseless105(this.getUseless105());
            fieldCnt++;
        }  else if (builder.hasUseless105()) {
            // 清理Useless105
            builder.clearUseless105();
            fieldCnt++;
        }
        if (this.getUseless106()) {
            builder.setUseless106(this.getUseless106());
            fieldCnt++;
        }  else if (builder.hasUseless106()) {
            // 清理Useless106
            builder.clearUseless106();
            fieldCnt++;
        }
        if (this.getUseless107()) {
            builder.setUseless107(this.getUseless107());
            fieldCnt++;
        }  else if (builder.hasUseless107()) {
            // 清理Useless107
            builder.clearUseless107();
            fieldCnt++;
        }
        if (this.getUseless108()) {
            builder.setUseless108(this.getUseless108());
            fieldCnt++;
        }  else if (builder.hasUseless108()) {
            // 清理Useless108
            builder.clearUseless108();
            fieldCnt++;
        }
        if (this.getUseless109()) {
            builder.setUseless109(this.getUseless109());
            fieldCnt++;
        }  else if (builder.hasUseless109()) {
            // 清理Useless109
            builder.clearUseless109();
            fieldCnt++;
        }
        if (this.getUseless110()) {
            builder.setUseless110(this.getUseless110());
            fieldCnt++;
        }  else if (builder.hasUseless110()) {
            // 清理Useless110
            builder.clearUseless110();
            fieldCnt++;
        }
        if (this.getUseless111()) {
            builder.setUseless111(this.getUseless111());
            fieldCnt++;
        }  else if (builder.hasUseless111()) {
            // 清理Useless111
            builder.clearUseless111();
            fieldCnt++;
        }
        if (this.getUseless112()) {
            builder.setUseless112(this.getUseless112());
            fieldCnt++;
        }  else if (builder.hasUseless112()) {
            // 清理Useless112
            builder.clearUseless112();
            fieldCnt++;
        }
        if (this.getUseless113()) {
            builder.setUseless113(this.getUseless113());
            fieldCnt++;
        }  else if (builder.hasUseless113()) {
            // 清理Useless113
            builder.clearUseless113();
            fieldCnt++;
        }
        if (this.getUseless114()) {
            builder.setUseless114(this.getUseless114());
            fieldCnt++;
        }  else if (builder.hasUseless114()) {
            // 清理Useless114
            builder.clearUseless114();
            fieldCnt++;
        }
        if (this.getUseless115()) {
            builder.setUseless115(this.getUseless115());
            fieldCnt++;
        }  else if (builder.hasUseless115()) {
            // 清理Useless115
            builder.clearUseless115();
            fieldCnt++;
        }
        if (this.getUseless116()) {
            builder.setUseless116(this.getUseless116());
            fieldCnt++;
        }  else if (builder.hasUseless116()) {
            // 清理Useless116
            builder.clearUseless116();
            fieldCnt++;
        }
        if (this.getUseless117()) {
            builder.setUseless117(this.getUseless117());
            fieldCnt++;
        }  else if (builder.hasUseless117()) {
            // 清理Useless117
            builder.clearUseless117();
            fieldCnt++;
        }
        if (this.getUseless118()) {
            builder.setUseless118(this.getUseless118());
            fieldCnt++;
        }  else if (builder.hasUseless118()) {
            // 清理Useless118
            builder.clearUseless118();
            fieldCnt++;
        }
        if (this.getUseless119()) {
            builder.setUseless119(this.getUseless119());
            fieldCnt++;
        }  else if (builder.hasUseless119()) {
            // 清理Useless119
            builder.clearUseless119();
            fieldCnt++;
        }
        if (this.getUseless120()) {
            builder.setUseless120(this.getUseless120());
            fieldCnt++;
        }  else if (builder.hasUseless120()) {
            // 清理Useless120
            builder.clearUseless120();
            fieldCnt++;
        }
        if (this.getUseless121()) {
            builder.setUseless121(this.getUseless121());
            fieldCnt++;
        }  else if (builder.hasUseless121()) {
            // 清理Useless121
            builder.clearUseless121();
            fieldCnt++;
        }
        if (this.getUseless122()) {
            builder.setUseless122(this.getUseless122());
            fieldCnt++;
        }  else if (builder.hasUseless122()) {
            // 清理Useless122
            builder.clearUseless122();
            fieldCnt++;
        }
        if (this.getUseless123()) {
            builder.setUseless123(this.getUseless123());
            fieldCnt++;
        }  else if (builder.hasUseless123()) {
            // 清理Useless123
            builder.clearUseless123();
            fieldCnt++;
        }
        if (this.getUseless124()) {
            builder.setUseless124(this.getUseless124());
            fieldCnt++;
        }  else if (builder.hasUseless124()) {
            // 清理Useless124
            builder.clearUseless124();
            fieldCnt++;
        }
        if (this.getUseless125()) {
            builder.setUseless125(this.getUseless125());
            fieldCnt++;
        }  else if (builder.hasUseless125()) {
            // 清理Useless125
            builder.clearUseless125();
            fieldCnt++;
        }
        if (this.getUseless126()) {
            builder.setUseless126(this.getUseless126());
            fieldCnt++;
        }  else if (builder.hasUseless126()) {
            // 清理Useless126
            builder.clearUseless126();
            fieldCnt++;
        }
        if (this.getUseless127()) {
            builder.setUseless127(this.getUseless127());
            fieldCnt++;
        }  else if (builder.hasUseless127()) {
            // 清理Useless127
            builder.clearUseless127();
            fieldCnt++;
        }
        if (this.getUseless128()) {
            builder.setUseless128(this.getUseless128());
            fieldCnt++;
        }  else if (builder.hasUseless128()) {
            // 清理Useless128
            builder.clearUseless128();
            fieldCnt++;
        }
        if (this.getUseless129()) {
            builder.setUseless129(this.getUseless129());
            fieldCnt++;
        }  else if (builder.hasUseless129()) {
            // 清理Useless129
            builder.clearUseless129();
            fieldCnt++;
        }
        if (this.getUseless130()) {
            builder.setUseless130(this.getUseless130());
            fieldCnt++;
        }  else if (builder.hasUseless130()) {
            // 清理Useless130
            builder.clearUseless130();
            fieldCnt++;
        }
        if (this.getUseless131()) {
            builder.setUseless131(this.getUseless131());
            fieldCnt++;
        }  else if (builder.hasUseless131()) {
            // 清理Useless131
            builder.clearUseless131();
            fieldCnt++;
        }
        if (this.getUseless132()) {
            builder.setUseless132(this.getUseless132());
            fieldCnt++;
        }  else if (builder.hasUseless132()) {
            // 清理Useless132
            builder.clearUseless132();
            fieldCnt++;
        }
        if (this.getUseless133()) {
            builder.setUseless133(this.getUseless133());
            fieldCnt++;
        }  else if (builder.hasUseless133()) {
            // 清理Useless133
            builder.clearUseless133();
            fieldCnt++;
        }
        if (this.getUseless134()) {
            builder.setUseless134(this.getUseless134());
            fieldCnt++;
        }  else if (builder.hasUseless134()) {
            // 清理Useless134
            builder.clearUseless134();
            fieldCnt++;
        }
        if (this.getUseless135()) {
            builder.setUseless135(this.getUseless135());
            fieldCnt++;
        }  else if (builder.hasUseless135()) {
            // 清理Useless135
            builder.clearUseless135();
            fieldCnt++;
        }
        if (this.getUseless136()) {
            builder.setUseless136(this.getUseless136());
            fieldCnt++;
        }  else if (builder.hasUseless136()) {
            // 清理Useless136
            builder.clearUseless136();
            fieldCnt++;
        }
        if (this.getUseless137()) {
            builder.setUseless137(this.getUseless137());
            fieldCnt++;
        }  else if (builder.hasUseless137()) {
            // 清理Useless137
            builder.clearUseless137();
            fieldCnt++;
        }
        if (this.getUseless138()) {
            builder.setUseless138(this.getUseless138());
            fieldCnt++;
        }  else if (builder.hasUseless138()) {
            // 清理Useless138
            builder.clearUseless138();
            fieldCnt++;
        }
        if (this.getUseless139()) {
            builder.setUseless139(this.getUseless139());
            fieldCnt++;
        }  else if (builder.hasUseless139()) {
            // 清理Useless139
            builder.clearUseless139();
            fieldCnt++;
        }
        if (this.getUseless140()) {
            builder.setUseless140(this.getUseless140());
            fieldCnt++;
        }  else if (builder.hasUseless140()) {
            // 清理Useless140
            builder.clearUseless140();
            fieldCnt++;
        }
        if (this.getUseless141()) {
            builder.setUseless141(this.getUseless141());
            fieldCnt++;
        }  else if (builder.hasUseless141()) {
            // 清理Useless141
            builder.clearUseless141();
            fieldCnt++;
        }
        if (this.getUseless142()) {
            builder.setUseless142(this.getUseless142());
            fieldCnt++;
        }  else if (builder.hasUseless142()) {
            // 清理Useless142
            builder.clearUseless142();
            fieldCnt++;
        }
        if (this.getUseless143()) {
            builder.setUseless143(this.getUseless143());
            fieldCnt++;
        }  else if (builder.hasUseless143()) {
            // 清理Useless143
            builder.clearUseless143();
            fieldCnt++;
        }
        if (this.getUseless144()) {
            builder.setUseless144(this.getUseless144());
            fieldCnt++;
        }  else if (builder.hasUseless144()) {
            // 清理Useless144
            builder.clearUseless144();
            fieldCnt++;
        }
        if (this.getUseless145()) {
            builder.setUseless145(this.getUseless145());
            fieldCnt++;
        }  else if (builder.hasUseless145()) {
            // 清理Useless145
            builder.clearUseless145();
            fieldCnt++;
        }
        if (this.getUseless146()) {
            builder.setUseless146(this.getUseless146());
            fieldCnt++;
        }  else if (builder.hasUseless146()) {
            // 清理Useless146
            builder.clearUseless146();
            fieldCnt++;
        }
        if (this.getUseless147()) {
            builder.setUseless147(this.getUseless147());
            fieldCnt++;
        }  else if (builder.hasUseless147()) {
            // 清理Useless147
            builder.clearUseless147();
            fieldCnt++;
        }
        if (this.getUseless148()) {
            builder.setUseless148(this.getUseless148());
            fieldCnt++;
        }  else if (builder.hasUseless148()) {
            // 清理Useless148
            builder.clearUseless148();
            fieldCnt++;
        }
        if (this.getUseless149()) {
            builder.setUseless149(this.getUseless149());
            fieldCnt++;
        }  else if (builder.hasUseless149()) {
            // 清理Useless149
            builder.clearUseless149();
            fieldCnt++;
        }
        if (this.getUseless150()) {
            builder.setUseless150(this.getUseless150());
            fieldCnt++;
        }  else if (builder.hasUseless150()) {
            // 清理Useless150
            builder.clearUseless150();
            fieldCnt++;
        }
        if (this.getUseless151()) {
            builder.setUseless151(this.getUseless151());
            fieldCnt++;
        }  else if (builder.hasUseless151()) {
            // 清理Useless151
            builder.clearUseless151();
            fieldCnt++;
        }
        if (this.getUseless152()) {
            builder.setUseless152(this.getUseless152());
            fieldCnt++;
        }  else if (builder.hasUseless152()) {
            // 清理Useless152
            builder.clearUseless152();
            fieldCnt++;
        }
        if (this.getUseless153()) {
            builder.setUseless153(this.getUseless153());
            fieldCnt++;
        }  else if (builder.hasUseless153()) {
            // 清理Useless153
            builder.clearUseless153();
            fieldCnt++;
        }
        if (this.getUseless154()) {
            builder.setUseless154(this.getUseless154());
            fieldCnt++;
        }  else if (builder.hasUseless154()) {
            // 清理Useless154
            builder.clearUseless154();
            fieldCnt++;
        }
        if (this.getUseless155()) {
            builder.setUseless155(this.getUseless155());
            fieldCnt++;
        }  else if (builder.hasUseless155()) {
            // 清理Useless155
            builder.clearUseless155();
            fieldCnt++;
        }
        if (this.getUseless156()) {
            builder.setUseless156(this.getUseless156());
            fieldCnt++;
        }  else if (builder.hasUseless156()) {
            // 清理Useless156
            builder.clearUseless156();
            fieldCnt++;
        }
        if (this.getUseless157()) {
            builder.setUseless157(this.getUseless157());
            fieldCnt++;
        }  else if (builder.hasUseless157()) {
            // 清理Useless157
            builder.clearUseless157();
            fieldCnt++;
        }
        if (this.getUseless158()) {
            builder.setUseless158(this.getUseless158());
            fieldCnt++;
        }  else if (builder.hasUseless158()) {
            // 清理Useless158
            builder.clearUseless158();
            fieldCnt++;
        }
        if (this.getUseless159()) {
            builder.setUseless159(this.getUseless159());
            fieldCnt++;
        }  else if (builder.hasUseless159()) {
            // 清理Useless159
            builder.clearUseless159();
            fieldCnt++;
        }
        if (this.getUseless160()) {
            builder.setUseless160(this.getUseless160());
            fieldCnt++;
        }  else if (builder.hasUseless160()) {
            // 清理Useless160
            builder.clearUseless160();
            fieldCnt++;
        }
        if (this.getUseless161()) {
            builder.setUseless161(this.getUseless161());
            fieldCnt++;
        }  else if (builder.hasUseless161()) {
            // 清理Useless161
            builder.clearUseless161();
            fieldCnt++;
        }
        if (this.getUseless162()) {
            builder.setUseless162(this.getUseless162());
            fieldCnt++;
        }  else if (builder.hasUseless162()) {
            // 清理Useless162
            builder.clearUseless162();
            fieldCnt++;
        }
        if (this.getUseless163()) {
            builder.setUseless163(this.getUseless163());
            fieldCnt++;
        }  else if (builder.hasUseless163()) {
            // 清理Useless163
            builder.clearUseless163();
            fieldCnt++;
        }
        if (this.getUseless164()) {
            builder.setUseless164(this.getUseless164());
            fieldCnt++;
        }  else if (builder.hasUseless164()) {
            // 清理Useless164
            builder.clearUseless164();
            fieldCnt++;
        }
        if (this.yoTestModel != null) {
            YoTest.YoTestModel.Builder tmpBuilder = YoTest.YoTestModel.newBuilder();
            final int tmpFieldCnt = this.yoTestModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYoTestModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYoTestModel();
            }
        }  else if (builder.hasYoTestModel()) {
            // 清理YoTestModel
            builder.clearYoTestModel();
            fieldCnt++;
        }
        if (this.yoTestUnit != null) {
            YoTest.YoTestUnit.Builder tmpBuilder = YoTest.YoTestUnit.newBuilder();
            final int tmpFieldCnt = this.yoTestUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setYoTestUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearYoTestUnit();
            }
        }  else if (builder.hasYoTestUnit()) {
            // 清理YoTestUnit
            builder.clearYoTestUnit();
            fieldCnt++;
        }
        if (this.setField != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.setField.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSetField(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSetField();
            }
        }  else if (builder.hasSetField()) {
            // 清理SetField
            builder.clearSetField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoTestEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_INTFIELD)) {
            builder.setIntField(this.getIntField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LONGFIELD)) {
            builder.setLongField(this.getLongField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STRINGFIELD)) {
            builder.setStringField(this.getStringField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOOLFIELD)) {
            builder.setBoolField(this.getBoolField());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS1)) {
            builder.setUseless1(this.getUseless1());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS2)) {
            builder.setUseless2(this.getUseless2());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS3)) {
            builder.setUseless3(this.getUseless3());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS4)) {
            builder.setUseless4(this.getUseless4());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS5)) {
            builder.setUseless5(this.getUseless5());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS6)) {
            builder.setUseless6(this.getUseless6());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS7)) {
            builder.setUseless7(this.getUseless7());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS8)) {
            builder.setUseless8(this.getUseless8());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS9)) {
            builder.setUseless9(this.getUseless9());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS10)) {
            builder.setUseless10(this.getUseless10());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS11)) {
            builder.setUseless11(this.getUseless11());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS12)) {
            builder.setUseless12(this.getUseless12());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS13)) {
            builder.setUseless13(this.getUseless13());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS14)) {
            builder.setUseless14(this.getUseless14());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS15)) {
            builder.setUseless15(this.getUseless15());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS16)) {
            builder.setUseless16(this.getUseless16());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS17)) {
            builder.setUseless17(this.getUseless17());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS18)) {
            builder.setUseless18(this.getUseless18());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS19)) {
            builder.setUseless19(this.getUseless19());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS20)) {
            builder.setUseless20(this.getUseless20());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS21)) {
            builder.setUseless21(this.getUseless21());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS22)) {
            builder.setUseless22(this.getUseless22());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS23)) {
            builder.setUseless23(this.getUseless23());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS24)) {
            builder.setUseless24(this.getUseless24());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS25)) {
            builder.setUseless25(this.getUseless25());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS26)) {
            builder.setUseless26(this.getUseless26());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS27)) {
            builder.setUseless27(this.getUseless27());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS28)) {
            builder.setUseless28(this.getUseless28());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS29)) {
            builder.setUseless29(this.getUseless29());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS30)) {
            builder.setUseless30(this.getUseless30());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS31)) {
            builder.setUseless31(this.getUseless31());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS32)) {
            builder.setUseless32(this.getUseless32());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS33)) {
            builder.setUseless33(this.getUseless33());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS34)) {
            builder.setUseless34(this.getUseless34());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS35)) {
            builder.setUseless35(this.getUseless35());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS36)) {
            builder.setUseless36(this.getUseless36());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS37)) {
            builder.setUseless37(this.getUseless37());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS38)) {
            builder.setUseless38(this.getUseless38());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS39)) {
            builder.setUseless39(this.getUseless39());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS40)) {
            builder.setUseless40(this.getUseless40());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS41)) {
            builder.setUseless41(this.getUseless41());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS42)) {
            builder.setUseless42(this.getUseless42());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS43)) {
            builder.setUseless43(this.getUseless43());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS44)) {
            builder.setUseless44(this.getUseless44());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS45)) {
            builder.setUseless45(this.getUseless45());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS46)) {
            builder.setUseless46(this.getUseless46());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS47)) {
            builder.setUseless47(this.getUseless47());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS48)) {
            builder.setUseless48(this.getUseless48());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS49)) {
            builder.setUseless49(this.getUseless49());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS50)) {
            builder.setUseless50(this.getUseless50());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS51)) {
            builder.setUseless51(this.getUseless51());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS52)) {
            builder.setUseless52(this.getUseless52());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS53)) {
            builder.setUseless53(this.getUseless53());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS54)) {
            builder.setUseless54(this.getUseless54());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS55)) {
            builder.setUseless55(this.getUseless55());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS56)) {
            builder.setUseless56(this.getUseless56());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS57)) {
            builder.setUseless57(this.getUseless57());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS58)) {
            builder.setUseless58(this.getUseless58());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS59)) {
            builder.setUseless59(this.getUseless59());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS60)) {
            builder.setUseless60(this.getUseless60());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS61)) {
            builder.setUseless61(this.getUseless61());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS62)) {
            builder.setUseless62(this.getUseless62());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS63)) {
            builder.setUseless63(this.getUseless63());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS64)) {
            builder.setUseless64(this.getUseless64());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPFIELD) && this.mapField != null) {
            final boolean needClear = !builder.hasMapField();
            final int tmpFieldCnt = this.mapField.copyChangeToSs(builder.getMapFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMapField();
            }
        }
        if (this.hasMark(FIELD_INDEX_LISTFIELD) && this.listField != null) {
            final boolean needClear = !builder.hasListField();
            final int tmpFieldCnt = this.listField.copyChangeToSs(builder.getListFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearListField();
            }
        }
        if (this.hasMark(FIELD_INDEX_USELESS101)) {
            builder.setUseless101(this.getUseless101());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS102)) {
            builder.setUseless102(this.getUseless102());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS103)) {
            builder.setUseless103(this.getUseless103());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS104)) {
            builder.setUseless104(this.getUseless104());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS105)) {
            builder.setUseless105(this.getUseless105());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS106)) {
            builder.setUseless106(this.getUseless106());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS107)) {
            builder.setUseless107(this.getUseless107());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS108)) {
            builder.setUseless108(this.getUseless108());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS109)) {
            builder.setUseless109(this.getUseless109());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS110)) {
            builder.setUseless110(this.getUseless110());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS111)) {
            builder.setUseless111(this.getUseless111());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS112)) {
            builder.setUseless112(this.getUseless112());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS113)) {
            builder.setUseless113(this.getUseless113());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS114)) {
            builder.setUseless114(this.getUseless114());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS115)) {
            builder.setUseless115(this.getUseless115());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS116)) {
            builder.setUseless116(this.getUseless116());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS117)) {
            builder.setUseless117(this.getUseless117());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS118)) {
            builder.setUseless118(this.getUseless118());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS119)) {
            builder.setUseless119(this.getUseless119());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS120)) {
            builder.setUseless120(this.getUseless120());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS121)) {
            builder.setUseless121(this.getUseless121());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS122)) {
            builder.setUseless122(this.getUseless122());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS123)) {
            builder.setUseless123(this.getUseless123());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS124)) {
            builder.setUseless124(this.getUseless124());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS125)) {
            builder.setUseless125(this.getUseless125());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS126)) {
            builder.setUseless126(this.getUseless126());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS127)) {
            builder.setUseless127(this.getUseless127());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS128)) {
            builder.setUseless128(this.getUseless128());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS129)) {
            builder.setUseless129(this.getUseless129());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS130)) {
            builder.setUseless130(this.getUseless130());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS131)) {
            builder.setUseless131(this.getUseless131());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS132)) {
            builder.setUseless132(this.getUseless132());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS133)) {
            builder.setUseless133(this.getUseless133());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS134)) {
            builder.setUseless134(this.getUseless134());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS135)) {
            builder.setUseless135(this.getUseless135());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS136)) {
            builder.setUseless136(this.getUseless136());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS137)) {
            builder.setUseless137(this.getUseless137());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS138)) {
            builder.setUseless138(this.getUseless138());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS139)) {
            builder.setUseless139(this.getUseless139());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS140)) {
            builder.setUseless140(this.getUseless140());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS141)) {
            builder.setUseless141(this.getUseless141());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS142)) {
            builder.setUseless142(this.getUseless142());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS143)) {
            builder.setUseless143(this.getUseless143());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS144)) {
            builder.setUseless144(this.getUseless144());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS145)) {
            builder.setUseless145(this.getUseless145());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS146)) {
            builder.setUseless146(this.getUseless146());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS147)) {
            builder.setUseless147(this.getUseless147());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS148)) {
            builder.setUseless148(this.getUseless148());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS149)) {
            builder.setUseless149(this.getUseless149());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS150)) {
            builder.setUseless150(this.getUseless150());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS151)) {
            builder.setUseless151(this.getUseless151());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS152)) {
            builder.setUseless152(this.getUseless152());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS153)) {
            builder.setUseless153(this.getUseless153());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS154)) {
            builder.setUseless154(this.getUseless154());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS155)) {
            builder.setUseless155(this.getUseless155());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS156)) {
            builder.setUseless156(this.getUseless156());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS157)) {
            builder.setUseless157(this.getUseless157());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS158)) {
            builder.setUseless158(this.getUseless158());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS159)) {
            builder.setUseless159(this.getUseless159());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS160)) {
            builder.setUseless160(this.getUseless160());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS161)) {
            builder.setUseless161(this.getUseless161());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS162)) {
            builder.setUseless162(this.getUseless162());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS163)) {
            builder.setUseless163(this.getUseless163());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USELESS164)) {
            builder.setUseless164(this.getUseless164());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_YOTESTMODEL) && this.yoTestModel != null) {
            final boolean needClear = !builder.hasYoTestModel();
            final int tmpFieldCnt = this.yoTestModel.copyChangeToSs(builder.getYoTestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_YOTESTUNIT) && this.yoTestUnit != null) {
            final boolean needClear = !builder.hasYoTestUnit();
            final int tmpFieldCnt = this.yoTestUnit.copyChangeToSs(builder.getYoTestUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearYoTestUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETFIELD) && this.setField != null) {
            final boolean needClear = !builder.hasSetField();
            final int tmpFieldCnt = this.setField.copyChangeToSs(builder.getSetFieldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSetField();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoTestEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIntField()) {
            this.innerSetIntField(proto.getIntField());
        } else {
            this.innerSetIntField(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLongField()) {
            this.innerSetLongField(proto.getLongField());
        } else {
            this.innerSetLongField(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStringField()) {
            this.innerSetStringField(proto.getStringField());
        } else {
            this.innerSetStringField(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasBoolField()) {
            this.innerSetBoolField(proto.getBoolField());
        } else {
            this.innerSetBoolField(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless1()) {
            this.innerSetUseless1(proto.getUseless1());
        } else {
            this.innerSetUseless1(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless2()) {
            this.innerSetUseless2(proto.getUseless2());
        } else {
            this.innerSetUseless2(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless3()) {
            this.innerSetUseless3(proto.getUseless3());
        } else {
            this.innerSetUseless3(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless4()) {
            this.innerSetUseless4(proto.getUseless4());
        } else {
            this.innerSetUseless4(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless5()) {
            this.innerSetUseless5(proto.getUseless5());
        } else {
            this.innerSetUseless5(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless6()) {
            this.innerSetUseless6(proto.getUseless6());
        } else {
            this.innerSetUseless6(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless7()) {
            this.innerSetUseless7(proto.getUseless7());
        } else {
            this.innerSetUseless7(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless8()) {
            this.innerSetUseless8(proto.getUseless8());
        } else {
            this.innerSetUseless8(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless9()) {
            this.innerSetUseless9(proto.getUseless9());
        } else {
            this.innerSetUseless9(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless10()) {
            this.innerSetUseless10(proto.getUseless10());
        } else {
            this.innerSetUseless10(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless11()) {
            this.innerSetUseless11(proto.getUseless11());
        } else {
            this.innerSetUseless11(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless12()) {
            this.innerSetUseless12(proto.getUseless12());
        } else {
            this.innerSetUseless12(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless13()) {
            this.innerSetUseless13(proto.getUseless13());
        } else {
            this.innerSetUseless13(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless14()) {
            this.innerSetUseless14(proto.getUseless14());
        } else {
            this.innerSetUseless14(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless15()) {
            this.innerSetUseless15(proto.getUseless15());
        } else {
            this.innerSetUseless15(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless16()) {
            this.innerSetUseless16(proto.getUseless16());
        } else {
            this.innerSetUseless16(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless17()) {
            this.innerSetUseless17(proto.getUseless17());
        } else {
            this.innerSetUseless17(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless18()) {
            this.innerSetUseless18(proto.getUseless18());
        } else {
            this.innerSetUseless18(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless19()) {
            this.innerSetUseless19(proto.getUseless19());
        } else {
            this.innerSetUseless19(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless20()) {
            this.innerSetUseless20(proto.getUseless20());
        } else {
            this.innerSetUseless20(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless21()) {
            this.innerSetUseless21(proto.getUseless21());
        } else {
            this.innerSetUseless21(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless22()) {
            this.innerSetUseless22(proto.getUseless22());
        } else {
            this.innerSetUseless22(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless23()) {
            this.innerSetUseless23(proto.getUseless23());
        } else {
            this.innerSetUseless23(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless24()) {
            this.innerSetUseless24(proto.getUseless24());
        } else {
            this.innerSetUseless24(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless25()) {
            this.innerSetUseless25(proto.getUseless25());
        } else {
            this.innerSetUseless25(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless26()) {
            this.innerSetUseless26(proto.getUseless26());
        } else {
            this.innerSetUseless26(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless27()) {
            this.innerSetUseless27(proto.getUseless27());
        } else {
            this.innerSetUseless27(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless28()) {
            this.innerSetUseless28(proto.getUseless28());
        } else {
            this.innerSetUseless28(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless29()) {
            this.innerSetUseless29(proto.getUseless29());
        } else {
            this.innerSetUseless29(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless30()) {
            this.innerSetUseless30(proto.getUseless30());
        } else {
            this.innerSetUseless30(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless31()) {
            this.innerSetUseless31(proto.getUseless31());
        } else {
            this.innerSetUseless31(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless32()) {
            this.innerSetUseless32(proto.getUseless32());
        } else {
            this.innerSetUseless32(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless33()) {
            this.innerSetUseless33(proto.getUseless33());
        } else {
            this.innerSetUseless33(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless34()) {
            this.innerSetUseless34(proto.getUseless34());
        } else {
            this.innerSetUseless34(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless35()) {
            this.innerSetUseless35(proto.getUseless35());
        } else {
            this.innerSetUseless35(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless36()) {
            this.innerSetUseless36(proto.getUseless36());
        } else {
            this.innerSetUseless36(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless37()) {
            this.innerSetUseless37(proto.getUseless37());
        } else {
            this.innerSetUseless37(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless38()) {
            this.innerSetUseless38(proto.getUseless38());
        } else {
            this.innerSetUseless38(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless39()) {
            this.innerSetUseless39(proto.getUseless39());
        } else {
            this.innerSetUseless39(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless40()) {
            this.innerSetUseless40(proto.getUseless40());
        } else {
            this.innerSetUseless40(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless41()) {
            this.innerSetUseless41(proto.getUseless41());
        } else {
            this.innerSetUseless41(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless42()) {
            this.innerSetUseless42(proto.getUseless42());
        } else {
            this.innerSetUseless42(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless43()) {
            this.innerSetUseless43(proto.getUseless43());
        } else {
            this.innerSetUseless43(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless44()) {
            this.innerSetUseless44(proto.getUseless44());
        } else {
            this.innerSetUseless44(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless45()) {
            this.innerSetUseless45(proto.getUseless45());
        } else {
            this.innerSetUseless45(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless46()) {
            this.innerSetUseless46(proto.getUseless46());
        } else {
            this.innerSetUseless46(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless47()) {
            this.innerSetUseless47(proto.getUseless47());
        } else {
            this.innerSetUseless47(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless48()) {
            this.innerSetUseless48(proto.getUseless48());
        } else {
            this.innerSetUseless48(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless49()) {
            this.innerSetUseless49(proto.getUseless49());
        } else {
            this.innerSetUseless49(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless50()) {
            this.innerSetUseless50(proto.getUseless50());
        } else {
            this.innerSetUseless50(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless51()) {
            this.innerSetUseless51(proto.getUseless51());
        } else {
            this.innerSetUseless51(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless52()) {
            this.innerSetUseless52(proto.getUseless52());
        } else {
            this.innerSetUseless52(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless53()) {
            this.innerSetUseless53(proto.getUseless53());
        } else {
            this.innerSetUseless53(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless54()) {
            this.innerSetUseless54(proto.getUseless54());
        } else {
            this.innerSetUseless54(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless55()) {
            this.innerSetUseless55(proto.getUseless55());
        } else {
            this.innerSetUseless55(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless56()) {
            this.innerSetUseless56(proto.getUseless56());
        } else {
            this.innerSetUseless56(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless57()) {
            this.innerSetUseless57(proto.getUseless57());
        } else {
            this.innerSetUseless57(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless58()) {
            this.innerSetUseless58(proto.getUseless58());
        } else {
            this.innerSetUseless58(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless59()) {
            this.innerSetUseless59(proto.getUseless59());
        } else {
            this.innerSetUseless59(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless60()) {
            this.innerSetUseless60(proto.getUseless60());
        } else {
            this.innerSetUseless60(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless61()) {
            this.innerSetUseless61(proto.getUseless61());
        } else {
            this.innerSetUseless61(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless62()) {
            this.innerSetUseless62(proto.getUseless62());
        } else {
            this.innerSetUseless62(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless63()) {
            this.innerSetUseless63(proto.getUseless63());
        } else {
            this.innerSetUseless63(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless64()) {
            this.innerSetUseless64(proto.getUseless64());
        } else {
            this.innerSetUseless64(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasMapField()) {
            this.getMapField().mergeFromSs(proto.getMapField());
        } else {
            if (this.mapField != null) {
                this.mapField.mergeFromSs(proto.getMapField());
            }
        }
        if (proto.hasListField()) {
            this.getListField().mergeFromSs(proto.getListField());
        } else {
            if (this.listField != null) {
                this.listField.mergeFromSs(proto.getListField());
            }
        }
        if (proto.hasUseless101()) {
            this.innerSetUseless101(proto.getUseless101());
        } else {
            this.innerSetUseless101(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless102()) {
            this.innerSetUseless102(proto.getUseless102());
        } else {
            this.innerSetUseless102(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless103()) {
            this.innerSetUseless103(proto.getUseless103());
        } else {
            this.innerSetUseless103(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless104()) {
            this.innerSetUseless104(proto.getUseless104());
        } else {
            this.innerSetUseless104(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless105()) {
            this.innerSetUseless105(proto.getUseless105());
        } else {
            this.innerSetUseless105(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless106()) {
            this.innerSetUseless106(proto.getUseless106());
        } else {
            this.innerSetUseless106(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless107()) {
            this.innerSetUseless107(proto.getUseless107());
        } else {
            this.innerSetUseless107(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless108()) {
            this.innerSetUseless108(proto.getUseless108());
        } else {
            this.innerSetUseless108(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless109()) {
            this.innerSetUseless109(proto.getUseless109());
        } else {
            this.innerSetUseless109(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless110()) {
            this.innerSetUseless110(proto.getUseless110());
        } else {
            this.innerSetUseless110(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless111()) {
            this.innerSetUseless111(proto.getUseless111());
        } else {
            this.innerSetUseless111(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless112()) {
            this.innerSetUseless112(proto.getUseless112());
        } else {
            this.innerSetUseless112(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless113()) {
            this.innerSetUseless113(proto.getUseless113());
        } else {
            this.innerSetUseless113(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless114()) {
            this.innerSetUseless114(proto.getUseless114());
        } else {
            this.innerSetUseless114(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless115()) {
            this.innerSetUseless115(proto.getUseless115());
        } else {
            this.innerSetUseless115(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless116()) {
            this.innerSetUseless116(proto.getUseless116());
        } else {
            this.innerSetUseless116(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless117()) {
            this.innerSetUseless117(proto.getUseless117());
        } else {
            this.innerSetUseless117(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless118()) {
            this.innerSetUseless118(proto.getUseless118());
        } else {
            this.innerSetUseless118(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless119()) {
            this.innerSetUseless119(proto.getUseless119());
        } else {
            this.innerSetUseless119(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless120()) {
            this.innerSetUseless120(proto.getUseless120());
        } else {
            this.innerSetUseless120(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless121()) {
            this.innerSetUseless121(proto.getUseless121());
        } else {
            this.innerSetUseless121(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless122()) {
            this.innerSetUseless122(proto.getUseless122());
        } else {
            this.innerSetUseless122(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless123()) {
            this.innerSetUseless123(proto.getUseless123());
        } else {
            this.innerSetUseless123(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless124()) {
            this.innerSetUseless124(proto.getUseless124());
        } else {
            this.innerSetUseless124(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless125()) {
            this.innerSetUseless125(proto.getUseless125());
        } else {
            this.innerSetUseless125(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless126()) {
            this.innerSetUseless126(proto.getUseless126());
        } else {
            this.innerSetUseless126(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless127()) {
            this.innerSetUseless127(proto.getUseless127());
        } else {
            this.innerSetUseless127(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless128()) {
            this.innerSetUseless128(proto.getUseless128());
        } else {
            this.innerSetUseless128(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless129()) {
            this.innerSetUseless129(proto.getUseless129());
        } else {
            this.innerSetUseless129(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless130()) {
            this.innerSetUseless130(proto.getUseless130());
        } else {
            this.innerSetUseless130(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless131()) {
            this.innerSetUseless131(proto.getUseless131());
        } else {
            this.innerSetUseless131(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless132()) {
            this.innerSetUseless132(proto.getUseless132());
        } else {
            this.innerSetUseless132(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless133()) {
            this.innerSetUseless133(proto.getUseless133());
        } else {
            this.innerSetUseless133(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless134()) {
            this.innerSetUseless134(proto.getUseless134());
        } else {
            this.innerSetUseless134(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless135()) {
            this.innerSetUseless135(proto.getUseless135());
        } else {
            this.innerSetUseless135(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless136()) {
            this.innerSetUseless136(proto.getUseless136());
        } else {
            this.innerSetUseless136(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless137()) {
            this.innerSetUseless137(proto.getUseless137());
        } else {
            this.innerSetUseless137(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless138()) {
            this.innerSetUseless138(proto.getUseless138());
        } else {
            this.innerSetUseless138(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless139()) {
            this.innerSetUseless139(proto.getUseless139());
        } else {
            this.innerSetUseless139(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless140()) {
            this.innerSetUseless140(proto.getUseless140());
        } else {
            this.innerSetUseless140(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless141()) {
            this.innerSetUseless141(proto.getUseless141());
        } else {
            this.innerSetUseless141(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless142()) {
            this.innerSetUseless142(proto.getUseless142());
        } else {
            this.innerSetUseless142(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless143()) {
            this.innerSetUseless143(proto.getUseless143());
        } else {
            this.innerSetUseless143(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless144()) {
            this.innerSetUseless144(proto.getUseless144());
        } else {
            this.innerSetUseless144(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless145()) {
            this.innerSetUseless145(proto.getUseless145());
        } else {
            this.innerSetUseless145(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless146()) {
            this.innerSetUseless146(proto.getUseless146());
        } else {
            this.innerSetUseless146(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless147()) {
            this.innerSetUseless147(proto.getUseless147());
        } else {
            this.innerSetUseless147(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless148()) {
            this.innerSetUseless148(proto.getUseless148());
        } else {
            this.innerSetUseless148(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless149()) {
            this.innerSetUseless149(proto.getUseless149());
        } else {
            this.innerSetUseless149(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless150()) {
            this.innerSetUseless150(proto.getUseless150());
        } else {
            this.innerSetUseless150(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless151()) {
            this.innerSetUseless151(proto.getUseless151());
        } else {
            this.innerSetUseless151(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless152()) {
            this.innerSetUseless152(proto.getUseless152());
        } else {
            this.innerSetUseless152(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless153()) {
            this.innerSetUseless153(proto.getUseless153());
        } else {
            this.innerSetUseless153(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless154()) {
            this.innerSetUseless154(proto.getUseless154());
        } else {
            this.innerSetUseless154(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless155()) {
            this.innerSetUseless155(proto.getUseless155());
        } else {
            this.innerSetUseless155(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless156()) {
            this.innerSetUseless156(proto.getUseless156());
        } else {
            this.innerSetUseless156(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless157()) {
            this.innerSetUseless157(proto.getUseless157());
        } else {
            this.innerSetUseless157(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless158()) {
            this.innerSetUseless158(proto.getUseless158());
        } else {
            this.innerSetUseless158(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless159()) {
            this.innerSetUseless159(proto.getUseless159());
        } else {
            this.innerSetUseless159(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless160()) {
            this.innerSetUseless160(proto.getUseless160());
        } else {
            this.innerSetUseless160(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless161()) {
            this.innerSetUseless161(proto.getUseless161());
        } else {
            this.innerSetUseless161(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless162()) {
            this.innerSetUseless162(proto.getUseless162());
        } else {
            this.innerSetUseless162(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless163()) {
            this.innerSetUseless163(proto.getUseless163());
        } else {
            this.innerSetUseless163(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasUseless164()) {
            this.innerSetUseless164(proto.getUseless164());
        } else {
            this.innerSetUseless164(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasYoTestModel()) {
            this.getYoTestModel().mergeFromSs(proto.getYoTestModel());
        } else {
            if (this.yoTestModel != null) {
                this.yoTestModel.mergeFromSs(proto.getYoTestModel());
            }
        }
        if (proto.hasYoTestUnit()) {
            this.getYoTestUnit().mergeFromSs(proto.getYoTestUnit());
        } else {
            if (this.yoTestUnit != null) {
                this.yoTestUnit.mergeFromSs(proto.getYoTestUnit());
            }
        }
        if (proto.hasSetField()) {
            this.getSetField().mergeFromSs(proto.getSetField());
        } else {
            if (this.setField != null) {
                this.setField.mergeFromSs(proto.getSetField());
            }
        }
        this.markAll();
        return YoTestProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoTestEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIntField()) {
            this.setIntField(proto.getIntField());
            fieldCnt++;
        }
        if (proto.hasLongField()) {
            this.setLongField(proto.getLongField());
            fieldCnt++;
        }
        if (proto.hasStringField()) {
            this.setStringField(proto.getStringField());
            fieldCnt++;
        }
        if (proto.hasBoolField()) {
            this.setBoolField(proto.getBoolField());
            fieldCnt++;
        }
        if (proto.hasUseless1()) {
            this.setUseless1(proto.getUseless1());
            fieldCnt++;
        }
        if (proto.hasUseless2()) {
            this.setUseless2(proto.getUseless2());
            fieldCnt++;
        }
        if (proto.hasUseless3()) {
            this.setUseless3(proto.getUseless3());
            fieldCnt++;
        }
        if (proto.hasUseless4()) {
            this.setUseless4(proto.getUseless4());
            fieldCnt++;
        }
        if (proto.hasUseless5()) {
            this.setUseless5(proto.getUseless5());
            fieldCnt++;
        }
        if (proto.hasUseless6()) {
            this.setUseless6(proto.getUseless6());
            fieldCnt++;
        }
        if (proto.hasUseless7()) {
            this.setUseless7(proto.getUseless7());
            fieldCnt++;
        }
        if (proto.hasUseless8()) {
            this.setUseless8(proto.getUseless8());
            fieldCnt++;
        }
        if (proto.hasUseless9()) {
            this.setUseless9(proto.getUseless9());
            fieldCnt++;
        }
        if (proto.hasUseless10()) {
            this.setUseless10(proto.getUseless10());
            fieldCnt++;
        }
        if (proto.hasUseless11()) {
            this.setUseless11(proto.getUseless11());
            fieldCnt++;
        }
        if (proto.hasUseless12()) {
            this.setUseless12(proto.getUseless12());
            fieldCnt++;
        }
        if (proto.hasUseless13()) {
            this.setUseless13(proto.getUseless13());
            fieldCnt++;
        }
        if (proto.hasUseless14()) {
            this.setUseless14(proto.getUseless14());
            fieldCnt++;
        }
        if (proto.hasUseless15()) {
            this.setUseless15(proto.getUseless15());
            fieldCnt++;
        }
        if (proto.hasUseless16()) {
            this.setUseless16(proto.getUseless16());
            fieldCnt++;
        }
        if (proto.hasUseless17()) {
            this.setUseless17(proto.getUseless17());
            fieldCnt++;
        }
        if (proto.hasUseless18()) {
            this.setUseless18(proto.getUseless18());
            fieldCnt++;
        }
        if (proto.hasUseless19()) {
            this.setUseless19(proto.getUseless19());
            fieldCnt++;
        }
        if (proto.hasUseless20()) {
            this.setUseless20(proto.getUseless20());
            fieldCnt++;
        }
        if (proto.hasUseless21()) {
            this.setUseless21(proto.getUseless21());
            fieldCnt++;
        }
        if (proto.hasUseless22()) {
            this.setUseless22(proto.getUseless22());
            fieldCnt++;
        }
        if (proto.hasUseless23()) {
            this.setUseless23(proto.getUseless23());
            fieldCnt++;
        }
        if (proto.hasUseless24()) {
            this.setUseless24(proto.getUseless24());
            fieldCnt++;
        }
        if (proto.hasUseless25()) {
            this.setUseless25(proto.getUseless25());
            fieldCnt++;
        }
        if (proto.hasUseless26()) {
            this.setUseless26(proto.getUseless26());
            fieldCnt++;
        }
        if (proto.hasUseless27()) {
            this.setUseless27(proto.getUseless27());
            fieldCnt++;
        }
        if (proto.hasUseless28()) {
            this.setUseless28(proto.getUseless28());
            fieldCnt++;
        }
        if (proto.hasUseless29()) {
            this.setUseless29(proto.getUseless29());
            fieldCnt++;
        }
        if (proto.hasUseless30()) {
            this.setUseless30(proto.getUseless30());
            fieldCnt++;
        }
        if (proto.hasUseless31()) {
            this.setUseless31(proto.getUseless31());
            fieldCnt++;
        }
        if (proto.hasUseless32()) {
            this.setUseless32(proto.getUseless32());
            fieldCnt++;
        }
        if (proto.hasUseless33()) {
            this.setUseless33(proto.getUseless33());
            fieldCnt++;
        }
        if (proto.hasUseless34()) {
            this.setUseless34(proto.getUseless34());
            fieldCnt++;
        }
        if (proto.hasUseless35()) {
            this.setUseless35(proto.getUseless35());
            fieldCnt++;
        }
        if (proto.hasUseless36()) {
            this.setUseless36(proto.getUseless36());
            fieldCnt++;
        }
        if (proto.hasUseless37()) {
            this.setUseless37(proto.getUseless37());
            fieldCnt++;
        }
        if (proto.hasUseless38()) {
            this.setUseless38(proto.getUseless38());
            fieldCnt++;
        }
        if (proto.hasUseless39()) {
            this.setUseless39(proto.getUseless39());
            fieldCnt++;
        }
        if (proto.hasUseless40()) {
            this.setUseless40(proto.getUseless40());
            fieldCnt++;
        }
        if (proto.hasUseless41()) {
            this.setUseless41(proto.getUseless41());
            fieldCnt++;
        }
        if (proto.hasUseless42()) {
            this.setUseless42(proto.getUseless42());
            fieldCnt++;
        }
        if (proto.hasUseless43()) {
            this.setUseless43(proto.getUseless43());
            fieldCnt++;
        }
        if (proto.hasUseless44()) {
            this.setUseless44(proto.getUseless44());
            fieldCnt++;
        }
        if (proto.hasUseless45()) {
            this.setUseless45(proto.getUseless45());
            fieldCnt++;
        }
        if (proto.hasUseless46()) {
            this.setUseless46(proto.getUseless46());
            fieldCnt++;
        }
        if (proto.hasUseless47()) {
            this.setUseless47(proto.getUseless47());
            fieldCnt++;
        }
        if (proto.hasUseless48()) {
            this.setUseless48(proto.getUseless48());
            fieldCnt++;
        }
        if (proto.hasUseless49()) {
            this.setUseless49(proto.getUseless49());
            fieldCnt++;
        }
        if (proto.hasUseless50()) {
            this.setUseless50(proto.getUseless50());
            fieldCnt++;
        }
        if (proto.hasUseless51()) {
            this.setUseless51(proto.getUseless51());
            fieldCnt++;
        }
        if (proto.hasUseless52()) {
            this.setUseless52(proto.getUseless52());
            fieldCnt++;
        }
        if (proto.hasUseless53()) {
            this.setUseless53(proto.getUseless53());
            fieldCnt++;
        }
        if (proto.hasUseless54()) {
            this.setUseless54(proto.getUseless54());
            fieldCnt++;
        }
        if (proto.hasUseless55()) {
            this.setUseless55(proto.getUseless55());
            fieldCnt++;
        }
        if (proto.hasUseless56()) {
            this.setUseless56(proto.getUseless56());
            fieldCnt++;
        }
        if (proto.hasUseless57()) {
            this.setUseless57(proto.getUseless57());
            fieldCnt++;
        }
        if (proto.hasUseless58()) {
            this.setUseless58(proto.getUseless58());
            fieldCnt++;
        }
        if (proto.hasUseless59()) {
            this.setUseless59(proto.getUseless59());
            fieldCnt++;
        }
        if (proto.hasUseless60()) {
            this.setUseless60(proto.getUseless60());
            fieldCnt++;
        }
        if (proto.hasUseless61()) {
            this.setUseless61(proto.getUseless61());
            fieldCnt++;
        }
        if (proto.hasUseless62()) {
            this.setUseless62(proto.getUseless62());
            fieldCnt++;
        }
        if (proto.hasUseless63()) {
            this.setUseless63(proto.getUseless63());
            fieldCnt++;
        }
        if (proto.hasUseless64()) {
            this.setUseless64(proto.getUseless64());
            fieldCnt++;
        }
        if (proto.hasMapField()) {
            this.getMapField().mergeChangeFromSs(proto.getMapField());
            fieldCnt++;
        }
        if (proto.hasListField()) {
            this.getListField().mergeChangeFromSs(proto.getListField());
            fieldCnt++;
        }
        if (proto.hasUseless101()) {
            this.setUseless101(proto.getUseless101());
            fieldCnt++;
        }
        if (proto.hasUseless102()) {
            this.setUseless102(proto.getUseless102());
            fieldCnt++;
        }
        if (proto.hasUseless103()) {
            this.setUseless103(proto.getUseless103());
            fieldCnt++;
        }
        if (proto.hasUseless104()) {
            this.setUseless104(proto.getUseless104());
            fieldCnt++;
        }
        if (proto.hasUseless105()) {
            this.setUseless105(proto.getUseless105());
            fieldCnt++;
        }
        if (proto.hasUseless106()) {
            this.setUseless106(proto.getUseless106());
            fieldCnt++;
        }
        if (proto.hasUseless107()) {
            this.setUseless107(proto.getUseless107());
            fieldCnt++;
        }
        if (proto.hasUseless108()) {
            this.setUseless108(proto.getUseless108());
            fieldCnt++;
        }
        if (proto.hasUseless109()) {
            this.setUseless109(proto.getUseless109());
            fieldCnt++;
        }
        if (proto.hasUseless110()) {
            this.setUseless110(proto.getUseless110());
            fieldCnt++;
        }
        if (proto.hasUseless111()) {
            this.setUseless111(proto.getUseless111());
            fieldCnt++;
        }
        if (proto.hasUseless112()) {
            this.setUseless112(proto.getUseless112());
            fieldCnt++;
        }
        if (proto.hasUseless113()) {
            this.setUseless113(proto.getUseless113());
            fieldCnt++;
        }
        if (proto.hasUseless114()) {
            this.setUseless114(proto.getUseless114());
            fieldCnt++;
        }
        if (proto.hasUseless115()) {
            this.setUseless115(proto.getUseless115());
            fieldCnt++;
        }
        if (proto.hasUseless116()) {
            this.setUseless116(proto.getUseless116());
            fieldCnt++;
        }
        if (proto.hasUseless117()) {
            this.setUseless117(proto.getUseless117());
            fieldCnt++;
        }
        if (proto.hasUseless118()) {
            this.setUseless118(proto.getUseless118());
            fieldCnt++;
        }
        if (proto.hasUseless119()) {
            this.setUseless119(proto.getUseless119());
            fieldCnt++;
        }
        if (proto.hasUseless120()) {
            this.setUseless120(proto.getUseless120());
            fieldCnt++;
        }
        if (proto.hasUseless121()) {
            this.setUseless121(proto.getUseless121());
            fieldCnt++;
        }
        if (proto.hasUseless122()) {
            this.setUseless122(proto.getUseless122());
            fieldCnt++;
        }
        if (proto.hasUseless123()) {
            this.setUseless123(proto.getUseless123());
            fieldCnt++;
        }
        if (proto.hasUseless124()) {
            this.setUseless124(proto.getUseless124());
            fieldCnt++;
        }
        if (proto.hasUseless125()) {
            this.setUseless125(proto.getUseless125());
            fieldCnt++;
        }
        if (proto.hasUseless126()) {
            this.setUseless126(proto.getUseless126());
            fieldCnt++;
        }
        if (proto.hasUseless127()) {
            this.setUseless127(proto.getUseless127());
            fieldCnt++;
        }
        if (proto.hasUseless128()) {
            this.setUseless128(proto.getUseless128());
            fieldCnt++;
        }
        if (proto.hasUseless129()) {
            this.setUseless129(proto.getUseless129());
            fieldCnt++;
        }
        if (proto.hasUseless130()) {
            this.setUseless130(proto.getUseless130());
            fieldCnt++;
        }
        if (proto.hasUseless131()) {
            this.setUseless131(proto.getUseless131());
            fieldCnt++;
        }
        if (proto.hasUseless132()) {
            this.setUseless132(proto.getUseless132());
            fieldCnt++;
        }
        if (proto.hasUseless133()) {
            this.setUseless133(proto.getUseless133());
            fieldCnt++;
        }
        if (proto.hasUseless134()) {
            this.setUseless134(proto.getUseless134());
            fieldCnt++;
        }
        if (proto.hasUseless135()) {
            this.setUseless135(proto.getUseless135());
            fieldCnt++;
        }
        if (proto.hasUseless136()) {
            this.setUseless136(proto.getUseless136());
            fieldCnt++;
        }
        if (proto.hasUseless137()) {
            this.setUseless137(proto.getUseless137());
            fieldCnt++;
        }
        if (proto.hasUseless138()) {
            this.setUseless138(proto.getUseless138());
            fieldCnt++;
        }
        if (proto.hasUseless139()) {
            this.setUseless139(proto.getUseless139());
            fieldCnt++;
        }
        if (proto.hasUseless140()) {
            this.setUseless140(proto.getUseless140());
            fieldCnt++;
        }
        if (proto.hasUseless141()) {
            this.setUseless141(proto.getUseless141());
            fieldCnt++;
        }
        if (proto.hasUseless142()) {
            this.setUseless142(proto.getUseless142());
            fieldCnt++;
        }
        if (proto.hasUseless143()) {
            this.setUseless143(proto.getUseless143());
            fieldCnt++;
        }
        if (proto.hasUseless144()) {
            this.setUseless144(proto.getUseless144());
            fieldCnt++;
        }
        if (proto.hasUseless145()) {
            this.setUseless145(proto.getUseless145());
            fieldCnt++;
        }
        if (proto.hasUseless146()) {
            this.setUseless146(proto.getUseless146());
            fieldCnt++;
        }
        if (proto.hasUseless147()) {
            this.setUseless147(proto.getUseless147());
            fieldCnt++;
        }
        if (proto.hasUseless148()) {
            this.setUseless148(proto.getUseless148());
            fieldCnt++;
        }
        if (proto.hasUseless149()) {
            this.setUseless149(proto.getUseless149());
            fieldCnt++;
        }
        if (proto.hasUseless150()) {
            this.setUseless150(proto.getUseless150());
            fieldCnt++;
        }
        if (proto.hasUseless151()) {
            this.setUseless151(proto.getUseless151());
            fieldCnt++;
        }
        if (proto.hasUseless152()) {
            this.setUseless152(proto.getUseless152());
            fieldCnt++;
        }
        if (proto.hasUseless153()) {
            this.setUseless153(proto.getUseless153());
            fieldCnt++;
        }
        if (proto.hasUseless154()) {
            this.setUseless154(proto.getUseless154());
            fieldCnt++;
        }
        if (proto.hasUseless155()) {
            this.setUseless155(proto.getUseless155());
            fieldCnt++;
        }
        if (proto.hasUseless156()) {
            this.setUseless156(proto.getUseless156());
            fieldCnt++;
        }
        if (proto.hasUseless157()) {
            this.setUseless157(proto.getUseless157());
            fieldCnt++;
        }
        if (proto.hasUseless158()) {
            this.setUseless158(proto.getUseless158());
            fieldCnt++;
        }
        if (proto.hasUseless159()) {
            this.setUseless159(proto.getUseless159());
            fieldCnt++;
        }
        if (proto.hasUseless160()) {
            this.setUseless160(proto.getUseless160());
            fieldCnt++;
        }
        if (proto.hasUseless161()) {
            this.setUseless161(proto.getUseless161());
            fieldCnt++;
        }
        if (proto.hasUseless162()) {
            this.setUseless162(proto.getUseless162());
            fieldCnt++;
        }
        if (proto.hasUseless163()) {
            this.setUseless163(proto.getUseless163());
            fieldCnt++;
        }
        if (proto.hasUseless164()) {
            this.setUseless164(proto.getUseless164());
            fieldCnt++;
        }
        if (proto.hasYoTestModel()) {
            this.getYoTestModel().mergeChangeFromSs(proto.getYoTestModel());
            fieldCnt++;
        }
        if (proto.hasYoTestUnit()) {
            this.getYoTestUnit().mergeChangeFromSs(proto.getYoTestUnit());
            fieldCnt++;
        }
        if (proto.hasSetField()) {
            this.getSetField().mergeChangeFromSs(proto.getSetField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoTestEntity.Builder builder = YoTestEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MAPFIELD) && this.mapField != null) {
            this.mapField.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LISTFIELD) && this.listField != null) {
            this.listField.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_YOTESTMODEL) && this.yoTestModel != null) {
            this.yoTestModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_YOTESTUNIT) && this.yoTestUnit != null) {
            this.yoTestUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SETFIELD) && this.setField != null) {
            this.setField.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.mapField != null) {
            this.mapField.markAll();
        }
        if (this.listField != null) {
            this.listField.markAll();
        }
        if (this.yoTestModel != null) {
            this.yoTestModel.markAll();
        }
        if (this.yoTestUnit != null) {
            this.yoTestUnit.markAll();
        }
        if (this.setField != null) {
            this.setField.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("YoTestProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("YoTestProp");
            }
            this.markParentNode();
            return;
        }
        if (inWhichMarkBits == 1) {
            this.markBits1 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("YoTestProp");
            }
            this.markParentNode();
            return;
        }
        if (inWhichMarkBits == 2) {
            this.markBits2 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("YoTestProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        if (inWhichMarkBits == 1) {
            this.markBits1 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        if (inWhichMarkBits == 2) {
            this.markBits2 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        if (inWhichMarkBits == 1) {
            return (this.markBits1 & (1L << inWhichBit)) != 0;
        }
        if (inWhichMarkBits == 2) {
            return (this.markBits2 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        mark |= this.markBits1;
        mark |= this.markBits2;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoTestProp)) {
            return false;
        }
        final YoTestProp otherNode = (YoTestProp) node;
        if (this.intField != otherNode.intField) {
            return false;
        }
        if (this.longField != otherNode.longField) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.stringField, otherNode.stringField)) {
            return false;
        }
        if (this.boolField != otherNode.boolField) {
            return false;
        }
        if (this.useless1 != otherNode.useless1) {
            return false;
        }
        if (this.useless2 != otherNode.useless2) {
            return false;
        }
        if (this.useless3 != otherNode.useless3) {
            return false;
        }
        if (this.useless4 != otherNode.useless4) {
            return false;
        }
        if (this.useless5 != otherNode.useless5) {
            return false;
        }
        if (this.useless6 != otherNode.useless6) {
            return false;
        }
        if (this.useless7 != otherNode.useless7) {
            return false;
        }
        if (this.useless8 != otherNode.useless8) {
            return false;
        }
        if (this.useless9 != otherNode.useless9) {
            return false;
        }
        if (this.useless10 != otherNode.useless10) {
            return false;
        }
        if (this.useless11 != otherNode.useless11) {
            return false;
        }
        if (this.useless12 != otherNode.useless12) {
            return false;
        }
        if (this.useless13 != otherNode.useless13) {
            return false;
        }
        if (this.useless14 != otherNode.useless14) {
            return false;
        }
        if (this.useless15 != otherNode.useless15) {
            return false;
        }
        if (this.useless16 != otherNode.useless16) {
            return false;
        }
        if (this.useless17 != otherNode.useless17) {
            return false;
        }
        if (this.useless18 != otherNode.useless18) {
            return false;
        }
        if (this.useless19 != otherNode.useless19) {
            return false;
        }
        if (this.useless20 != otherNode.useless20) {
            return false;
        }
        if (this.useless21 != otherNode.useless21) {
            return false;
        }
        if (this.useless22 != otherNode.useless22) {
            return false;
        }
        if (this.useless23 != otherNode.useless23) {
            return false;
        }
        if (this.useless24 != otherNode.useless24) {
            return false;
        }
        if (this.useless25 != otherNode.useless25) {
            return false;
        }
        if (this.useless26 != otherNode.useless26) {
            return false;
        }
        if (this.useless27 != otherNode.useless27) {
            return false;
        }
        if (this.useless28 != otherNode.useless28) {
            return false;
        }
        if (this.useless29 != otherNode.useless29) {
            return false;
        }
        if (this.useless30 != otherNode.useless30) {
            return false;
        }
        if (this.useless31 != otherNode.useless31) {
            return false;
        }
        if (this.useless32 != otherNode.useless32) {
            return false;
        }
        if (this.useless33 != otherNode.useless33) {
            return false;
        }
        if (this.useless34 != otherNode.useless34) {
            return false;
        }
        if (this.useless35 != otherNode.useless35) {
            return false;
        }
        if (this.useless36 != otherNode.useless36) {
            return false;
        }
        if (this.useless37 != otherNode.useless37) {
            return false;
        }
        if (this.useless38 != otherNode.useless38) {
            return false;
        }
        if (this.useless39 != otherNode.useless39) {
            return false;
        }
        if (this.useless40 != otherNode.useless40) {
            return false;
        }
        if (this.useless41 != otherNode.useless41) {
            return false;
        }
        if (this.useless42 != otherNode.useless42) {
            return false;
        }
        if (this.useless43 != otherNode.useless43) {
            return false;
        }
        if (this.useless44 != otherNode.useless44) {
            return false;
        }
        if (this.useless45 != otherNode.useless45) {
            return false;
        }
        if (this.useless46 != otherNode.useless46) {
            return false;
        }
        if (this.useless47 != otherNode.useless47) {
            return false;
        }
        if (this.useless48 != otherNode.useless48) {
            return false;
        }
        if (this.useless49 != otherNode.useless49) {
            return false;
        }
        if (this.useless50 != otherNode.useless50) {
            return false;
        }
        if (this.useless51 != otherNode.useless51) {
            return false;
        }
        if (this.useless52 != otherNode.useless52) {
            return false;
        }
        if (this.useless53 != otherNode.useless53) {
            return false;
        }
        if (this.useless54 != otherNode.useless54) {
            return false;
        }
        if (this.useless55 != otherNode.useless55) {
            return false;
        }
        if (this.useless56 != otherNode.useless56) {
            return false;
        }
        if (this.useless57 != otherNode.useless57) {
            return false;
        }
        if (this.useless58 != otherNode.useless58) {
            return false;
        }
        if (this.useless59 != otherNode.useless59) {
            return false;
        }
        if (this.useless60 != otherNode.useless60) {
            return false;
        }
        if (this.useless61 != otherNode.useless61) {
            return false;
        }
        if (this.useless62 != otherNode.useless62) {
            return false;
        }
        if (this.useless63 != otherNode.useless63) {
            return false;
        }
        if (this.useless64 != otherNode.useless64) {
            return false;
        }
        if (!this.getMapField().compareDataTo(otherNode.getMapField())) {
            return false;
        }
        if (!this.getListField().compareDataTo(otherNode.getListField())) {
            return false;
        }
        if (this.useless101 != otherNode.useless101) {
            return false;
        }
        if (this.useless102 != otherNode.useless102) {
            return false;
        }
        if (this.useless103 != otherNode.useless103) {
            return false;
        }
        if (this.useless104 != otherNode.useless104) {
            return false;
        }
        if (this.useless105 != otherNode.useless105) {
            return false;
        }
        if (this.useless106 != otherNode.useless106) {
            return false;
        }
        if (this.useless107 != otherNode.useless107) {
            return false;
        }
        if (this.useless108 != otherNode.useless108) {
            return false;
        }
        if (this.useless109 != otherNode.useless109) {
            return false;
        }
        if (this.useless110 != otherNode.useless110) {
            return false;
        }
        if (this.useless111 != otherNode.useless111) {
            return false;
        }
        if (this.useless112 != otherNode.useless112) {
            return false;
        }
        if (this.useless113 != otherNode.useless113) {
            return false;
        }
        if (this.useless114 != otherNode.useless114) {
            return false;
        }
        if (this.useless115 != otherNode.useless115) {
            return false;
        }
        if (this.useless116 != otherNode.useless116) {
            return false;
        }
        if (this.useless117 != otherNode.useless117) {
            return false;
        }
        if (this.useless118 != otherNode.useless118) {
            return false;
        }
        if (this.useless119 != otherNode.useless119) {
            return false;
        }
        if (this.useless120 != otherNode.useless120) {
            return false;
        }
        if (this.useless121 != otherNode.useless121) {
            return false;
        }
        if (this.useless122 != otherNode.useless122) {
            return false;
        }
        if (this.useless123 != otherNode.useless123) {
            return false;
        }
        if (this.useless124 != otherNode.useless124) {
            return false;
        }
        if (this.useless125 != otherNode.useless125) {
            return false;
        }
        if (this.useless126 != otherNode.useless126) {
            return false;
        }
        if (this.useless127 != otherNode.useless127) {
            return false;
        }
        if (this.useless128 != otherNode.useless128) {
            return false;
        }
        if (this.useless129 != otherNode.useless129) {
            return false;
        }
        if (this.useless130 != otherNode.useless130) {
            return false;
        }
        if (this.useless131 != otherNode.useless131) {
            return false;
        }
        if (this.useless132 != otherNode.useless132) {
            return false;
        }
        if (this.useless133 != otherNode.useless133) {
            return false;
        }
        if (this.useless134 != otherNode.useless134) {
            return false;
        }
        if (this.useless135 != otherNode.useless135) {
            return false;
        }
        if (this.useless136 != otherNode.useless136) {
            return false;
        }
        if (this.useless137 != otherNode.useless137) {
            return false;
        }
        if (this.useless138 != otherNode.useless138) {
            return false;
        }
        if (this.useless139 != otherNode.useless139) {
            return false;
        }
        if (this.useless140 != otherNode.useless140) {
            return false;
        }
        if (this.useless141 != otherNode.useless141) {
            return false;
        }
        if (this.useless142 != otherNode.useless142) {
            return false;
        }
        if (this.useless143 != otherNode.useless143) {
            return false;
        }
        if (this.useless144 != otherNode.useless144) {
            return false;
        }
        if (this.useless145 != otherNode.useless145) {
            return false;
        }
        if (this.useless146 != otherNode.useless146) {
            return false;
        }
        if (this.useless147 != otherNode.useless147) {
            return false;
        }
        if (this.useless148 != otherNode.useless148) {
            return false;
        }
        if (this.useless149 != otherNode.useless149) {
            return false;
        }
        if (this.useless150 != otherNode.useless150) {
            return false;
        }
        if (this.useless151 != otherNode.useless151) {
            return false;
        }
        if (this.useless152 != otherNode.useless152) {
            return false;
        }
        if (this.useless153 != otherNode.useless153) {
            return false;
        }
        if (this.useless154 != otherNode.useless154) {
            return false;
        }
        if (this.useless155 != otherNode.useless155) {
            return false;
        }
        if (this.useless156 != otherNode.useless156) {
            return false;
        }
        if (this.useless157 != otherNode.useless157) {
            return false;
        }
        if (this.useless158 != otherNode.useless158) {
            return false;
        }
        if (this.useless159 != otherNode.useless159) {
            return false;
        }
        if (this.useless160 != otherNode.useless160) {
            return false;
        }
        if (this.useless161 != otherNode.useless161) {
            return false;
        }
        if (this.useless162 != otherNode.useless162) {
            return false;
        }
        if (this.useless163 != otherNode.useless163) {
            return false;
        }
        if (this.useless164 != otherNode.useless164) {
            return false;
        }
        if (!this.getYoTestModel().compareDataTo(otherNode.getYoTestModel())) {
            return false;
        }
        if (!this.getYoTestUnit().compareDataTo(otherNode.getYoTestUnit())) {
            return false;
        }
        if (!this.getSetField().compareDataTo(otherNode.getSetField())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static YoTestProp of(YoTestEntity fullAttrDb, YoTestEntity changeAttrDb) {
        YoTestProp prop = new YoTestProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L);
        this.markBits1 = ~(0L);
        this.markBits2 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
        this.markBits1 = 0L;
        this.markBits2 = 0L;
    }
}
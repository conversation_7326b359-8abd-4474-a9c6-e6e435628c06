package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerHero;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerHeroPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerHeroProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_HEROID = 0;
    public static final int FIELD_INDEX_LEVEL = 1;
    public static final int FIELD_INDEX_STAR = 2;
    public static final int FIELD_INDEX_EXP = 3;
    public static final int FIELD_INDEX_STATE = 4;
    public static final int FIELD_INDEX_SKILLSTORE = 5;
    public static final int FIELD_INDEX_TALENTPAGESTORE = 6;
    public static final int FIELD_INDEX_HEROSTATISTICS = 7;
    public static final int FIELD_INDEX_SKILLSLOTLIMIT = 8;

    public static final int FIELD_COUNT = 9;

    private long markBits0 = 0L;

    private int heroId = Constant.DEFAULT_INT_VALUE;
    private int level = Constant.DEFAULT_INT_VALUE;
    private int star = Constant.DEFAULT_INT_VALUE;
    private long exp = Constant.DEFAULT_LONG_VALUE;
    private HeroState state = HeroState.forNumber(0);
    private Int32PlayerHeroSkillMapProp skillStore = null;
    private Int32PlayerHeroTalentPageMapProp talentPageStore = null;
    private HeroStatisticsProp heroStatistics = null;
    private int skillSlotLimit = Constant.DEFAULT_INT_VALUE;

    public PlayerHeroProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerHeroProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public PlayerHeroProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public PlayerHeroProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get star
     *
     * @return star value
     */
    public int getStar() {
        return this.star;
    }

    /**
     * set star && set marked
     *
     * @param star new value
     * @return current object
     */
    public PlayerHeroProp setStar(int star) {
        if (this.star != star) {
            this.mark(FIELD_INDEX_STAR);
            this.star = star;
        }
        return this;
    }

    /**
     * inner set star
     *
     * @param star new value
     */
    private void innerSetStar(int star) {
        this.star = star;
    }

    /**
     * get exp
     *
     * @return exp value
     */
    public long getExp() {
        return this.exp;
    }

    /**
     * set exp && set marked
     *
     * @param exp new value
     * @return current object
     */
    public PlayerHeroProp setExp(long exp) {
        if (this.exp != exp) {
            this.mark(FIELD_INDEX_EXP);
            this.exp = exp;
        }
        return this;
    }

    /**
     * inner set exp
     *
     * @param exp new value
     */
    private void innerSetExp(long exp) {
        this.exp = exp;
    }

    /**
     * get state
     *
     * @return state value
     */
    public HeroState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public PlayerHeroProp setState(HeroState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(HeroState state) {
        this.state = state;
    }

    /**
     * get skillStore
     *
     * @return skillStore value
     */
    public Int32PlayerHeroSkillMapProp getSkillStore() {
        if (this.skillStore == null) {
            this.skillStore = new Int32PlayerHeroSkillMapProp(this, FIELD_INDEX_SKILLSTORE);
        }
        return this.skillStore;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSkillStoreV(PlayerHeroSkillProp v) {
        this.getSkillStore().put(v.getSkillGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerHeroSkillProp addEmptySkillStore(Integer k) {
        return this.getSkillStore().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSkillStoreSize() {
        if (this.skillStore == null) {
            return 0;
        }
        return this.skillStore.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSkillStoreEmpty() {
        if (this.skillStore == null) {
            return true;
        }
        return this.skillStore.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerHeroSkillProp getSkillStoreV(Integer k) {
        if (this.skillStore == null || !this.skillStore.containsKey(k)) {
            return null;
        }
        return this.skillStore.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSkillStore() {
        if (this.skillStore != null) {
            this.skillStore.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSkillStoreV(Integer k) {
        if (this.skillStore != null) {
            this.skillStore.remove(k);
        }
    }
    /**
     * get talentPageStore
     *
     * @return talentPageStore value
     */
    public Int32PlayerHeroTalentPageMapProp getTalentPageStore() {
        if (this.talentPageStore == null) {
            this.talentPageStore = new Int32PlayerHeroTalentPageMapProp(this, FIELD_INDEX_TALENTPAGESTORE);
        }
        return this.talentPageStore;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTalentPageStoreV(PlayerHeroTalentPageProp v) {
        this.getTalentPageStore().put(v.getSlotNum(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerHeroTalentPageProp addEmptyTalentPageStore(Integer k) {
        return this.getTalentPageStore().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTalentPageStoreSize() {
        if (this.talentPageStore == null) {
            return 0;
        }
        return this.talentPageStore.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTalentPageStoreEmpty() {
        if (this.talentPageStore == null) {
            return true;
        }
        return this.talentPageStore.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerHeroTalentPageProp getTalentPageStoreV(Integer k) {
        if (this.talentPageStore == null || !this.talentPageStore.containsKey(k)) {
            return null;
        }
        return this.talentPageStore.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTalentPageStore() {
        if (this.talentPageStore != null) {
            this.talentPageStore.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTalentPageStoreV(Integer k) {
        if (this.talentPageStore != null) {
            this.talentPageStore.remove(k);
        }
    }
    /**
     * get heroStatistics
     *
     * @return heroStatistics value
     */
    public HeroStatisticsProp getHeroStatistics() {
        if (this.heroStatistics == null) {
            this.heroStatistics = new HeroStatisticsProp(this, FIELD_INDEX_HEROSTATISTICS);
        }
        return this.heroStatistics;
    }

    /**
     * get skillSlotLimit
     *
     * @return skillSlotLimit value
     */
    public int getSkillSlotLimit() {
        return this.skillSlotLimit;
    }

    /**
     * set skillSlotLimit && set marked
     *
     * @param skillSlotLimit new value
     * @return current object
     */
    public PlayerHeroProp setSkillSlotLimit(int skillSlotLimit) {
        if (this.skillSlotLimit != skillSlotLimit) {
            this.mark(FIELD_INDEX_SKILLSLOTLIMIT);
            this.skillSlotLimit = skillSlotLimit;
        }
        return this;
    }

    /**
     * inner set skillSlotLimit
     *
     * @param skillSlotLimit new value
     */
    private void innerSetSkillSlotLimit(int skillSlotLimit) {
        this.skillSlotLimit = skillSlotLimit;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroPB.Builder getCopyCsBuilder() {
        final PlayerHeroPB.Builder builder = PlayerHeroPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerHeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getExp() != 0L) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }  else if (builder.hasExp()) {
            // 清理Exp
            builder.clearExp();
            fieldCnt++;
        }
        if (this.getState() != HeroState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.skillStore != null) {
            PlayerPB.Int32PlayerHeroSkillMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerHeroSkillMapPB.newBuilder();
            final int tmpFieldCnt = this.skillStore.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillStore();
            }
        }  else if (builder.hasSkillStore()) {
            // 清理SkillStore
            builder.clearSkillStore();
            fieldCnt++;
        }
        if (this.talentPageStore != null) {
            PlayerPB.Int32PlayerHeroTalentPageMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerHeroTalentPageMapPB.newBuilder();
            final int tmpFieldCnt = this.talentPageStore.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentPageStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentPageStore();
            }
        }  else if (builder.hasTalentPageStore()) {
            // 清理TalentPageStore
            builder.clearTalentPageStore();
            fieldCnt++;
        }
        if (this.heroStatistics != null) {
            PlayerPB.HeroStatisticsPB.Builder tmpBuilder = PlayerPB.HeroStatisticsPB.newBuilder();
            final int tmpFieldCnt = this.heroStatistics.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroStatistics(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroStatistics();
            }
        }  else if (builder.hasHeroStatistics()) {
            // 清理HeroStatistics
            builder.clearHeroStatistics();
            fieldCnt++;
        }
        if (this.getSkillSlotLimit() != 0) {
            builder.setSkillSlotLimit(this.getSkillSlotLimit());
            fieldCnt++;
        }  else if (builder.hasSkillSlotLimit()) {
            // 清理SkillSlotLimit
            builder.clearSkillSlotLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerHeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTORE) && this.skillStore != null) {
            final boolean needClear = !builder.hasSkillStore();
            final int tmpFieldCnt = this.skillStore.copyChangeToCs(builder.getSkillStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTORE) && this.talentPageStore != null) {
            final boolean needClear = !builder.hasTalentPageStore();
            final int tmpFieldCnt = this.talentPageStore.copyChangeToCs(builder.getTalentPageStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentPageStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROSTATISTICS) && this.heroStatistics != null) {
            final boolean needClear = !builder.hasHeroStatistics();
            final int tmpFieldCnt = this.heroStatistics.copyChangeToCs(builder.getHeroStatisticsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroStatistics();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOTLIMIT)) {
            builder.setSkillSlotLimit(this.getSkillSlotLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerHeroPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTORE) && this.skillStore != null) {
            final boolean needClear = !builder.hasSkillStore();
            final int tmpFieldCnt = this.skillStore.copyChangeToAndClearDeleteKeysCs(builder.getSkillStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTORE) && this.talentPageStore != null) {
            final boolean needClear = !builder.hasTalentPageStore();
            final int tmpFieldCnt = this.talentPageStore.copyChangeToAndClearDeleteKeysCs(builder.getTalentPageStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentPageStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROSTATISTICS) && this.heroStatistics != null) {
            final boolean needClear = !builder.hasHeroStatistics();
            final int tmpFieldCnt = this.heroStatistics.copyChangeToAndClearDeleteKeysCs(builder.getHeroStatisticsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroStatistics();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOTLIMIT)) {
            builder.setSkillSlotLimit(this.getSkillSlotLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerHeroPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExp()) {
            this.innerSetExp(proto.getExp());
        } else {
            this.innerSetExp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(HeroState.forNumber(0));
        }
        if (proto.hasSkillStore()) {
            this.getSkillStore().mergeFromCs(proto.getSkillStore());
        } else {
            if (this.skillStore != null) {
                this.skillStore.mergeFromCs(proto.getSkillStore());
            }
        }
        if (proto.hasTalentPageStore()) {
            this.getTalentPageStore().mergeFromCs(proto.getTalentPageStore());
        } else {
            if (this.talentPageStore != null) {
                this.talentPageStore.mergeFromCs(proto.getTalentPageStore());
            }
        }
        if (proto.hasHeroStatistics()) {
            this.getHeroStatistics().mergeFromCs(proto.getHeroStatistics());
        } else {
            if (this.heroStatistics != null) {
                this.heroStatistics.mergeFromCs(proto.getHeroStatistics());
            }
        }
        if (proto.hasSkillSlotLimit()) {
            this.innerSetSkillSlotLimit(proto.getSkillSlotLimit());
        } else {
            this.innerSetSkillSlotLimit(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerHeroPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasExp()) {
            this.setExp(proto.getExp());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasSkillStore()) {
            this.getSkillStore().mergeChangeFromCs(proto.getSkillStore());
            fieldCnt++;
        }
        if (proto.hasTalentPageStore()) {
            this.getTalentPageStore().mergeChangeFromCs(proto.getTalentPageStore());
            fieldCnt++;
        }
        if (proto.hasHeroStatistics()) {
            this.getHeroStatistics().mergeChangeFromCs(proto.getHeroStatistics());
            fieldCnt++;
        }
        if (proto.hasSkillSlotLimit()) {
            this.setSkillSlotLimit(proto.getSkillSlotLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHero.Builder getCopyDbBuilder() {
        final PlayerHero.Builder builder = PlayerHero.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getExp() != 0L) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }  else if (builder.hasExp()) {
            // 清理Exp
            builder.clearExp();
            fieldCnt++;
        }
        if (this.getState() != HeroState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.skillStore != null) {
            Player.Int32PlayerHeroSkillMap.Builder tmpBuilder = Player.Int32PlayerHeroSkillMap.newBuilder();
            final int tmpFieldCnt = this.skillStore.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillStore();
            }
        }  else if (builder.hasSkillStore()) {
            // 清理SkillStore
            builder.clearSkillStore();
            fieldCnt++;
        }
        if (this.talentPageStore != null) {
            Player.Int32PlayerHeroTalentPageMap.Builder tmpBuilder = Player.Int32PlayerHeroTalentPageMap.newBuilder();
            final int tmpFieldCnt = this.talentPageStore.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentPageStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentPageStore();
            }
        }  else if (builder.hasTalentPageStore()) {
            // 清理TalentPageStore
            builder.clearTalentPageStore();
            fieldCnt++;
        }
        if (this.heroStatistics != null) {
            Player.HeroStatistics.Builder tmpBuilder = Player.HeroStatistics.newBuilder();
            final int tmpFieldCnt = this.heroStatistics.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroStatistics(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroStatistics();
            }
        }  else if (builder.hasHeroStatistics()) {
            // 清理HeroStatistics
            builder.clearHeroStatistics();
            fieldCnt++;
        }
        if (this.getSkillSlotLimit() != 0) {
            builder.setSkillSlotLimit(this.getSkillSlotLimit());
            fieldCnt++;
        }  else if (builder.hasSkillSlotLimit()) {
            // 清理SkillSlotLimit
            builder.clearSkillSlotLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTORE) && this.skillStore != null) {
            final boolean needClear = !builder.hasSkillStore();
            final int tmpFieldCnt = this.skillStore.copyChangeToDb(builder.getSkillStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTORE) && this.talentPageStore != null) {
            final boolean needClear = !builder.hasTalentPageStore();
            final int tmpFieldCnt = this.talentPageStore.copyChangeToDb(builder.getTalentPageStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentPageStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROSTATISTICS) && this.heroStatistics != null) {
            final boolean needClear = !builder.hasHeroStatistics();
            final int tmpFieldCnt = this.heroStatistics.copyChangeToDb(builder.getHeroStatisticsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroStatistics();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOTLIMIT)) {
            builder.setSkillSlotLimit(this.getSkillSlotLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerHero proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExp()) {
            this.innerSetExp(proto.getExp());
        } else {
            this.innerSetExp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(HeroState.forNumber(0));
        }
        if (proto.hasSkillStore()) {
            this.getSkillStore().mergeFromDb(proto.getSkillStore());
        } else {
            if (this.skillStore != null) {
                this.skillStore.mergeFromDb(proto.getSkillStore());
            }
        }
        if (proto.hasTalentPageStore()) {
            this.getTalentPageStore().mergeFromDb(proto.getTalentPageStore());
        } else {
            if (this.talentPageStore != null) {
                this.talentPageStore.mergeFromDb(proto.getTalentPageStore());
            }
        }
        if (proto.hasHeroStatistics()) {
            this.getHeroStatistics().mergeFromDb(proto.getHeroStatistics());
        } else {
            if (this.heroStatistics != null) {
                this.heroStatistics.mergeFromDb(proto.getHeroStatistics());
            }
        }
        if (proto.hasSkillSlotLimit()) {
            this.innerSetSkillSlotLimit(proto.getSkillSlotLimit());
        } else {
            this.innerSetSkillSlotLimit(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerHero proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasExp()) {
            this.setExp(proto.getExp());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasSkillStore()) {
            this.getSkillStore().mergeChangeFromDb(proto.getSkillStore());
            fieldCnt++;
        }
        if (proto.hasTalentPageStore()) {
            this.getTalentPageStore().mergeChangeFromDb(proto.getTalentPageStore());
            fieldCnt++;
        }
        if (proto.hasHeroStatistics()) {
            this.getHeroStatistics().mergeChangeFromDb(proto.getHeroStatistics());
            fieldCnt++;
        }
        if (proto.hasSkillSlotLimit()) {
            this.setSkillSlotLimit(proto.getSkillSlotLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHero.Builder getCopySsBuilder() {
        final PlayerHero.Builder builder = PlayerHero.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getStar() != 0) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }  else if (builder.hasStar()) {
            // 清理Star
            builder.clearStar();
            fieldCnt++;
        }
        if (this.getExp() != 0L) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }  else if (builder.hasExp()) {
            // 清理Exp
            builder.clearExp();
            fieldCnt++;
        }
        if (this.getState() != HeroState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.skillStore != null) {
            Player.Int32PlayerHeroSkillMap.Builder tmpBuilder = Player.Int32PlayerHeroSkillMap.newBuilder();
            final int tmpFieldCnt = this.skillStore.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillStore();
            }
        }  else if (builder.hasSkillStore()) {
            // 清理SkillStore
            builder.clearSkillStore();
            fieldCnt++;
        }
        if (this.talentPageStore != null) {
            Player.Int32PlayerHeroTalentPageMap.Builder tmpBuilder = Player.Int32PlayerHeroTalentPageMap.newBuilder();
            final int tmpFieldCnt = this.talentPageStore.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTalentPageStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTalentPageStore();
            }
        }  else if (builder.hasTalentPageStore()) {
            // 清理TalentPageStore
            builder.clearTalentPageStore();
            fieldCnt++;
        }
        if (this.heroStatistics != null) {
            Player.HeroStatistics.Builder tmpBuilder = Player.HeroStatistics.newBuilder();
            final int tmpFieldCnt = this.heroStatistics.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroStatistics(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroStatistics();
            }
        }  else if (builder.hasHeroStatistics()) {
            // 清理HeroStatistics
            builder.clearHeroStatistics();
            fieldCnt++;
        }
        if (this.getSkillSlotLimit() != 0) {
            builder.setSkillSlotLimit(this.getSkillSlotLimit());
            fieldCnt++;
        }  else if (builder.hasSkillSlotLimit()) {
            // 清理SkillSlotLimit
            builder.clearSkillSlotLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerHero.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAR)) {
            builder.setStar(this.getStar());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTORE) && this.skillStore != null) {
            final boolean needClear = !builder.hasSkillStore();
            final int tmpFieldCnt = this.skillStore.copyChangeToSs(builder.getSkillStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTORE) && this.talentPageStore != null) {
            final boolean needClear = !builder.hasTalentPageStore();
            final int tmpFieldCnt = this.talentPageStore.copyChangeToSs(builder.getTalentPageStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTalentPageStore();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROSTATISTICS) && this.heroStatistics != null) {
            final boolean needClear = !builder.hasHeroStatistics();
            final int tmpFieldCnt = this.heroStatistics.copyChangeToSs(builder.getHeroStatisticsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroStatistics();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKILLSLOTLIMIT)) {
            builder.setSkillSlotLimit(this.getSkillSlotLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerHero proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStar()) {
            this.innerSetStar(proto.getStar());
        } else {
            this.innerSetStar(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExp()) {
            this.innerSetExp(proto.getExp());
        } else {
            this.innerSetExp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(HeroState.forNumber(0));
        }
        if (proto.hasSkillStore()) {
            this.getSkillStore().mergeFromSs(proto.getSkillStore());
        } else {
            if (this.skillStore != null) {
                this.skillStore.mergeFromSs(proto.getSkillStore());
            }
        }
        if (proto.hasTalentPageStore()) {
            this.getTalentPageStore().mergeFromSs(proto.getTalentPageStore());
        } else {
            if (this.talentPageStore != null) {
                this.talentPageStore.mergeFromSs(proto.getTalentPageStore());
            }
        }
        if (proto.hasHeroStatistics()) {
            this.getHeroStatistics().mergeFromSs(proto.getHeroStatistics());
        } else {
            if (this.heroStatistics != null) {
                this.heroStatistics.mergeFromSs(proto.getHeroStatistics());
            }
        }
        if (proto.hasSkillSlotLimit()) {
            this.innerSetSkillSlotLimit(proto.getSkillSlotLimit());
        } else {
            this.innerSetSkillSlotLimit(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerHero proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasStar()) {
            this.setStar(proto.getStar());
            fieldCnt++;
        }
        if (proto.hasExp()) {
            this.setExp(proto.getExp());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasSkillStore()) {
            this.getSkillStore().mergeChangeFromSs(proto.getSkillStore());
            fieldCnt++;
        }
        if (proto.hasTalentPageStore()) {
            this.getTalentPageStore().mergeChangeFromSs(proto.getTalentPageStore());
            fieldCnt++;
        }
        if (proto.hasHeroStatistics()) {
            this.getHeroStatistics().mergeChangeFromSs(proto.getHeroStatistics());
            fieldCnt++;
        }
        if (proto.hasSkillSlotLimit()) {
            this.setSkillSlotLimit(proto.getSkillSlotLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerHero.Builder builder = PlayerHero.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SKILLSTORE) && this.skillStore != null) {
            this.skillStore.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TALENTPAGESTORE) && this.talentPageStore != null) {
            this.talentPageStore.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_HEROSTATISTICS) && this.heroStatistics != null) {
            this.heroStatistics.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.skillStore != null) {
            this.skillStore.markAll();
        }
        if (this.talentPageStore != null) {
            this.talentPageStore.markAll();
        }
        if (this.heroStatistics != null) {
            this.heroStatistics.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.heroId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerHeroProp)) {
            return false;
        }
        final PlayerHeroProp otherNode = (PlayerHeroProp) node;
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.star != otherNode.star) {
            return false;
        }
        if (this.exp != otherNode.exp) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (!this.getSkillStore().compareDataTo(otherNode.getSkillStore())) {
            return false;
        }
        if (!this.getTalentPageStore().compareDataTo(otherNode.getTalentPageStore())) {
            return false;
        }
        if (!this.getHeroStatistics().compareDataTo(otherNode.getHeroStatistics())) {
            return false;
        }
        if (this.skillSlotLimit != otherNode.skillSlotLimit) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 55;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
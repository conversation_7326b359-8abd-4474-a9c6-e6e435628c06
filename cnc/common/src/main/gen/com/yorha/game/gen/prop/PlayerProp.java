package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.Player.PlayerEntity;
import com.yorha.proto.Player;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.PlayerPB.PlayerEntityPB;
import com.yorha.proto.PlayerPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_OPENID = 1;
    public static final int FIELD_INDEX_SCENE = 2;
    public static final int FIELD_INDEX_SCENEPLAYER = 3;
    public static final int FIELD_INDEX_CREATETIME = 4;
    public static final int FIELD_INDEX_CLAN = 5;
    public static final int FIELD_INDEX_FRAGMENTS = 6;
    public static final int FIELD_INDEX_ITEMS = 7;
    public static final int FIELD_INDEX_PURSE = 8;
    public static final int FIELD_INDEX_QUEUETASKMAP = 9;
    public static final int FIELD_INDEX_PLAYERSOLDIERINBUILDING = 10;
    public static final int FIELD_INDEX_PLAYERPOWERINFO = 11;
    public static final int FIELD_INDEX_PLAYERDATARECORD = 12;
    public static final int FIELD_INDEX_BASICINFO = 13;
    public static final int FIELD_INDEX_FORMATION = 14;
    public static final int FIELD_INDEX_RESOURCEPRODUCE = 15;
    public static final int FIELD_INDEX_ADDITIONSYS = 16;
    public static final int FIELD_INDEX_DEVBUFFSYS = 17;
    public static final int FIELD_INDEX_REFRESHSYS = 18;
    public static final int FIELD_INDEX_TASKSYSTEM = 19;
    public static final int FIELD_INDEX_DISCOUNTINFO = 20;
    public static final int FIELD_INDEX_PLAYERSTOREMODEL = 21;
    public static final int FIELD_INDEX_PLAYERGUIDANCEMODEL = 22;
    public static final int FIELD_INDEX_PLAYERTECHMODEL = 23;
    public static final int FIELD_INDEX_PLAYERPLANEMODEL = 24;
    public static final int FIELD_INDEX_CHATPLAYER = 25;
    public static final int FIELD_INDEX_NEWBIEMODEL = 26;
    public static final int FIELD_INDEX_STATISTICMODEL = 27;
    public static final int FIELD_INDEX_REDDOTMODEL = 28;
    public static final int FIELD_INDEX_PLAYERHEROMODEL = 29;
    public static final int FIELD_INDEX_AVATARMODEL = 30;
    public static final int FIELD_INDEX_ZONEMODEL = 31;
    public static final int FIELD_INDEX_ENERGYMODEL = 32;
    public static final int FIELD_INDEX_PLAYERRECRUITMODEL = 33;
    public static final int FIELD_INDEX_ACTIVITYMODEL = 34;
    public static final int FIELD_INDEX_MAILMODEL = 35;
    public static final int FIELD_INDEX_DUNGEONPLAYER = 36;
    public static final int FIELD_INDEX_KILLMONSTERMODEL = 37;
    public static final int FIELD_INDEX_MILESTONEMODEL = 38;
    public static final int FIELD_INDEX_DUNGEONMODEL = 39;
    public static final int FIELD_INDEX_RESETINFO = 40;
    public static final int FIELD_INDEX_SETTINGMODEL = 41;
    public static final int FIELD_INDEX_PAYMENTMODEL = 42;
    public static final int FIELD_INDEX_FRIENDPLAYER = 43;
    public static final int FIELD_INDEX_VIPMODEL = 44;
    public static final int FIELD_INDEX_QUESTMODEL = 45;
    public static final int FIELD_INDEX_CITYMODEL = 46;
    public static final int FIELD_INDEX_DAILYGOODSMODEL = 47;
    public static final int FIELD_INDEX_DRAWINFO = 48;
    public static final int FIELD_INDEX_DEVBUFFSYSNEW = 49;
    public static final int FIELD_INDEX_KINGDOMMODEL = 50;
    public static final int FIELD_INDEX_ZONEADDITIONSYS = 51;
    public static final int FIELD_INDEX_TRIGGERBUNDLEMODEL = 52;
    public static final int FIELD_INDEX_FEATURELOCKMODEL = 53;
    public static final int FIELD_INDEX_BATTLEPASSMODEL = 54;
    public static final int FIELD_INDEX_DATAPATCHVERSION = 55;
    public static final int FIELD_INDEX_ADDITIONSYSNEW = 56;
    public static final int FIELD_INDEX_CONTACTSMODEL = 57;
    public static final int FIELD_INDEX_RATINGMODEL = 58;
    public static final int FIELD_INDEX_SKYNETMODEL = 59;
    public static final int FIELD_INDEX_ACHIEVEMENTMODEL = 60;
    public static final int FIELD_INDEX_SEASONBATTLEPASSMODEL = 61;
    public static final int FIELD_INDEX_PLAYERINNERBUILDRHMODEL = 62;
    public static final int FIELD_INDEX_PLAYERCAMPAIGNMODEL = 63;
    public static final int FIELD_INDEX_PLAYERUNITMODEL = 64;
    public static final int FIELD_INDEX_PLAYERMISSIONMODEL = 65;

    public static final int FIELD_COUNT = 66;

    private long markBits0 = 0L;
    private long markBits1 = 0L;
    private PropertyChangeListener listener;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private String openId = Constant.DEFAULT_STR_VALUE;
    private PlayerSceneProp scene = null;
    private ScenePlayerProp scenePlayer = null;
    private long createTime = Constant.DEFAULT_LONG_VALUE;
    private ClanInfoProp clan = null;
    private HistoryFragmentsProp fragments = null;
    private Int64ItemMapProp items = null;
    private Int32CurrencyMapProp purse = null;
    private Int32QueueTasksMapProp queueTaskMap = null;
    private PlayerSoldierInBuildingProp playerSoldierInBuilding = null;
    private PlayerPowerInfoProp playerPowerInfo = null;
    private PlayerDataRecordProp playerDataRecord = null;
    private PlayerBasicInfoProp basicInfo = null;
    private PlayerFormationProp formation = null;
    private PlayerResourceProduceMapProp resourceProduce = null;
    private AdditionSysProp additionSys = null;
    private DevBuffSysProp devBuffSys = null;
    private RefreshInfoProp refreshSys = null;
    private TaskSystemProp taskSystem = null;
    private DiscountInfoProp discountInfo = null;
    private PlayerStoreModelProp playerStoreModel = null;
    private PlayerGuidanceModelProp PlayerGuidanceModel = null;
    private PlayerTechnologyModelProp PlayerTechModel = null;
    private PlayerPlaneModelProp playerPlaneModel = null;
    private ChatPlayerProp ChatPlayer = null;
    private PlayerNewbieModelProp newbieModel = null;
    private PlayerStatisticModelProp statisticModel = null;
    private PlayerRedDotModelProp redDotModel = null;
    private PlayerHeroModelProp playerHeroModel = null;
    private PlayerAvatarModelProp avatarModel = null;
    private PlayerZoneModelProp zoneModel = null;
    private PlayerEnergyModelProp energyModel = null;
    private PlayerRecruitModelProp playerRecruitModel = null;
    private PlayerActivityModelProp activityModel = null;
    private PlayerMailModelProp mailModel = null;
    private DungeonPlayerProp dungeonPlayer = null;
    private PlayerKillMonsterModelProp killMonsterModel = null;
    private PlayerMileStoneModelProp mileStoneModel = null;
    private PlayerDungeonModelProp dungeonModel = null;
    private PlayerResetInfoProp resetInfo = null;
    private PlayerSettingModelProp settingModel = null;
    private PlayerPaymentModelProp paymentModel = null;
    private FriendPlayerProp friendPlayer = null;
    private PlayerVipModelProp vipModel = null;
    private PlayerInnerQuestModelProp questModel = null;
    private PlayerCityModelProp cityModel = null;
    private PlayerDailyGoodsModelProp dailyGoodsModel = null;
    private Int32DrawInfoMapProp drawInfo = null;
    private DevBuffSysProp devBuffSysNew = null;
    private PlayerKingdomModelProp kingdomModel = null;
    private AdditionSysProp zoneAdditionSys = null;
    private PlayerTriggerBundleModelProp triggerBundleModel = null;
    private PlayerFeatureLockModelProp featureLockModel = null;
    private PlayerBattlePassModelProp battlePassModel = null;
    private int dataPatchVersion = Constant.DEFAULT_INT_VALUE;
    private AdditionSysProp additionSysNew = null;
    private PlayerContactsModelProp contactsModel = null;
    private PlayerStoreRatingModelProp ratingModel = null;
    private PlayerSkynetModelProp skynetModel = null;
    private PlayerAchievementModelProp achievementModel = null;
    private PlayerBattlePassModelProp seasonBattlePassModel = null;
    private PlayerInnerBuildRHModelProp playerInnerBuildRHModel = null;
    private PlayerCampaignModelProp playerCampaignModel = null;
    private PlayerUnitModelProp playerUnitModel = null;
    private PlayerMissionModelProp playerMissionModel = null;

    public PlayerProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public PlayerProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get openId
     *
     * @return openId value
     */
    public String getOpenId() {
        return this.openId;
    }

    /**
     * set openId && set marked
     *
     * @param openId new value
     * @return current object
     */
    public PlayerProp setOpenId(String openId) {
        if (openId == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.openId, openId)) {
            this.mark(FIELD_INDEX_OPENID);
            this.openId = openId;
        }
        return this;
    }

    /**
     * inner set openId
     *
     * @param openId new value
     */
    private void innerSetOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * get scene
     *
     * @return scene value
     */
    public PlayerSceneProp getScene() {
        if (this.scene == null) {
            this.scene = new PlayerSceneProp(this, FIELD_INDEX_SCENE);
        }
        return this.scene;
    }

    /**
     * get scenePlayer
     *
     * @return scenePlayer value
     */
    public ScenePlayerProp getScenePlayer() {
        if (this.scenePlayer == null) {
            this.scenePlayer = new ScenePlayerProp(this, FIELD_INDEX_SCENEPLAYER);
        }
        return this.scenePlayer;
    }

    /**
     * get createTime
     *
     * @return createTime value
     */
    public long getCreateTime() {
        return this.createTime;
    }

    /**
     * set createTime && set marked
     *
     * @param createTime new value
     * @return current object
     */
    public PlayerProp setCreateTime(long createTime) {
        if (this.createTime != createTime) {
            this.mark(FIELD_INDEX_CREATETIME);
            this.createTime = createTime;
        }
        return this;
    }

    /**
     * inner set createTime
     *
     * @param createTime new value
     */
    private void innerSetCreateTime(long createTime) {
        this.createTime = createTime;
    }

    /**
     * get clan
     *
     * @return clan value
     */
    public ClanInfoProp getClan() {
        if (this.clan == null) {
            this.clan = new ClanInfoProp(this, FIELD_INDEX_CLAN);
        }
        return this.clan;
    }

    /**
     * get fragments
     *
     * @return fragments value
     */
    public HistoryFragmentsProp getFragments() {
        if (this.fragments == null) {
            this.fragments = new HistoryFragmentsProp(this, FIELD_INDEX_FRAGMENTS);
        }
        return this.fragments;
    }

    /**
     * get items
     *
     * @return items value
     */
    public Int64ItemMapProp getItems() {
        if (this.items == null) {
            this.items = new Int64ItemMapProp(this, FIELD_INDEX_ITEMS);
        }
        return this.items;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putItemsV(ItemProp v) {
        this.getItems().put(v.getKey(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ItemProp addEmptyItems(Long k) {
        return this.getItems().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getItemsSize() {
        if (this.items == null) {
            return 0;
        }
        return this.items.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isItemsEmpty() {
        if (this.items == null) {
            return true;
        }
        return this.items.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ItemProp getItemsV(Long k) {
        if (this.items == null || !this.items.containsKey(k)) {
            return null;
        }
        return this.items.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearItems() {
        if (this.items != null) {
            this.items.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeItemsV(Long k) {
        if (this.items != null) {
            this.items.remove(k);
        }
    }
    /**
     * get purse
     *
     * @return purse value
     */
    public Int32CurrencyMapProp getPurse() {
        if (this.purse == null) {
            this.purse = new Int32CurrencyMapProp(this, FIELD_INDEX_PURSE);
        }
        return this.purse;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPurseV(CurrencyProp v) {
        this.getPurse().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyPurse(Integer k) {
        return this.getPurse().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPurseSize() {
        if (this.purse == null) {
            return 0;
        }
        return this.purse.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPurseEmpty() {
        if (this.purse == null) {
            return true;
        }
        return this.purse.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getPurseV(Integer k) {
        if (this.purse == null || !this.purse.containsKey(k)) {
            return null;
        }
        return this.purse.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPurse() {
        if (this.purse != null) {
            this.purse.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePurseV(Integer k) {
        if (this.purse != null) {
            this.purse.remove(k);
        }
    }
    /**
     * get queueTaskMap
     *
     * @return queueTaskMap value
     */
    public Int32QueueTasksMapProp getQueueTaskMap() {
        if (this.queueTaskMap == null) {
            this.queueTaskMap = new Int32QueueTasksMapProp(this, FIELD_INDEX_QUEUETASKMAP);
        }
        return this.queueTaskMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putQueueTaskMapV(QueueTasksProp v) {
        this.getQueueTaskMap().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public QueueTasksProp addEmptyQueueTaskMap(Integer k) {
        return this.getQueueTaskMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getQueueTaskMapSize() {
        if (this.queueTaskMap == null) {
            return 0;
        }
        return this.queueTaskMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isQueueTaskMapEmpty() {
        if (this.queueTaskMap == null) {
            return true;
        }
        return this.queueTaskMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public QueueTasksProp getQueueTaskMapV(Integer k) {
        if (this.queueTaskMap == null || !this.queueTaskMap.containsKey(k)) {
            return null;
        }
        return this.queueTaskMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearQueueTaskMap() {
        if (this.queueTaskMap != null) {
            this.queueTaskMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeQueueTaskMapV(Integer k) {
        if (this.queueTaskMap != null) {
            this.queueTaskMap.remove(k);
        }
    }
    /**
     * get playerSoldierInBuilding
     *
     * @return playerSoldierInBuilding value
     */
    public PlayerSoldierInBuildingProp getPlayerSoldierInBuilding() {
        if (this.playerSoldierInBuilding == null) {
            this.playerSoldierInBuilding = new PlayerSoldierInBuildingProp(this, FIELD_INDEX_PLAYERSOLDIERINBUILDING);
        }
        return this.playerSoldierInBuilding;
    }

    /**
     * get playerPowerInfo
     *
     * @return playerPowerInfo value
     */
    public PlayerPowerInfoProp getPlayerPowerInfo() {
        if (this.playerPowerInfo == null) {
            this.playerPowerInfo = new PlayerPowerInfoProp(this, FIELD_INDEX_PLAYERPOWERINFO);
        }
        return this.playerPowerInfo;
    }

    /**
     * get playerDataRecord
     *
     * @return playerDataRecord value
     */
    public PlayerDataRecordProp getPlayerDataRecord() {
        if (this.playerDataRecord == null) {
            this.playerDataRecord = new PlayerDataRecordProp(this, FIELD_INDEX_PLAYERDATARECORD);
        }
        return this.playerDataRecord;
    }

    /**
     * get basicInfo
     *
     * @return basicInfo value
     */
    public PlayerBasicInfoProp getBasicInfo() {
        if (this.basicInfo == null) {
            this.basicInfo = new PlayerBasicInfoProp(this, FIELD_INDEX_BASICINFO);
        }
        return this.basicInfo;
    }

    /**
     * get formation
     *
     * @return formation value
     */
    public PlayerFormationProp getFormation() {
        if (this.formation == null) {
            this.formation = new PlayerFormationProp(this, FIELD_INDEX_FORMATION);
        }
        return this.formation;
    }

    /**
     * get resourceProduce
     *
     * @return resourceProduce value
     */
    public PlayerResourceProduceMapProp getResourceProduce() {
        if (this.resourceProduce == null) {
            this.resourceProduce = new PlayerResourceProduceMapProp(this, FIELD_INDEX_RESOURCEPRODUCE);
        }
        return this.resourceProduce;
    }

    /**
     * get additionSys
     *
     * @return additionSys value
     */
    public AdditionSysProp getAdditionSys() {
        if (this.additionSys == null) {
            this.additionSys = new AdditionSysProp(this, FIELD_INDEX_ADDITIONSYS);
        }
        return this.additionSys;
    }

    /**
     * get devBuffSys
     *
     * @return devBuffSys value
     */
    public DevBuffSysProp getDevBuffSys() {
        if (this.devBuffSys == null) {
            this.devBuffSys = new DevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYS);
        }
        return this.devBuffSys;
    }

    /**
     * get refreshSys
     *
     * @return refreshSys value
     */
    public RefreshInfoProp getRefreshSys() {
        if (this.refreshSys == null) {
            this.refreshSys = new RefreshInfoProp(this, FIELD_INDEX_REFRESHSYS);
        }
        return this.refreshSys;
    }

    /**
     * get taskSystem
     *
     * @return taskSystem value
     */
    public TaskSystemProp getTaskSystem() {
        if (this.taskSystem == null) {
            this.taskSystem = new TaskSystemProp(this, FIELD_INDEX_TASKSYSTEM);
        }
        return this.taskSystem;
    }

    /**
     * get discountInfo
     *
     * @return discountInfo value
     */
    public DiscountInfoProp getDiscountInfo() {
        if (this.discountInfo == null) {
            this.discountInfo = new DiscountInfoProp(this, FIELD_INDEX_DISCOUNTINFO);
        }
        return this.discountInfo;
    }

    /**
     * get playerStoreModel
     *
     * @return playerStoreModel value
     */
    public PlayerStoreModelProp getPlayerStoreModel() {
        if (this.playerStoreModel == null) {
            this.playerStoreModel = new PlayerStoreModelProp(this, FIELD_INDEX_PLAYERSTOREMODEL);
        }
        return this.playerStoreModel;
    }

    /**
     * get PlayerGuidanceModel
     *
     * @return PlayerGuidanceModel value
     */
    public PlayerGuidanceModelProp getPlayerGuidanceModel() {
        if (this.PlayerGuidanceModel == null) {
            this.PlayerGuidanceModel = new PlayerGuidanceModelProp(this, FIELD_INDEX_PLAYERGUIDANCEMODEL);
        }
        return this.PlayerGuidanceModel;
    }

    /**
     * get PlayerTechModel
     *
     * @return PlayerTechModel value
     */
    public PlayerTechnologyModelProp getPlayerTechModel() {
        if (this.PlayerTechModel == null) {
            this.PlayerTechModel = new PlayerTechnologyModelProp(this, FIELD_INDEX_PLAYERTECHMODEL);
        }
        return this.PlayerTechModel;
    }

    /**
     * get playerPlaneModel
     *
     * @return playerPlaneModel value
     */
    public PlayerPlaneModelProp getPlayerPlaneModel() {
        if (this.playerPlaneModel == null) {
            this.playerPlaneModel = new PlayerPlaneModelProp(this, FIELD_INDEX_PLAYERPLANEMODEL);
        }
        return this.playerPlaneModel;
    }

    /**
     * get ChatPlayer
     *
     * @return ChatPlayer value
     */
    public ChatPlayerProp getChatPlayer() {
        if (this.ChatPlayer == null) {
            this.ChatPlayer = new ChatPlayerProp(this, FIELD_INDEX_CHATPLAYER);
        }
        return this.ChatPlayer;
    }

    /**
     * get newbieModel
     *
     * @return newbieModel value
     */
    public PlayerNewbieModelProp getNewbieModel() {
        if (this.newbieModel == null) {
            this.newbieModel = new PlayerNewbieModelProp(this, FIELD_INDEX_NEWBIEMODEL);
        }
        return this.newbieModel;
    }

    /**
     * get statisticModel
     *
     * @return statisticModel value
     */
    public PlayerStatisticModelProp getStatisticModel() {
        if (this.statisticModel == null) {
            this.statisticModel = new PlayerStatisticModelProp(this, FIELD_INDEX_STATISTICMODEL);
        }
        return this.statisticModel;
    }

    /**
     * get redDotModel
     *
     * @return redDotModel value
     */
    public PlayerRedDotModelProp getRedDotModel() {
        if (this.redDotModel == null) {
            this.redDotModel = new PlayerRedDotModelProp(this, FIELD_INDEX_REDDOTMODEL);
        }
        return this.redDotModel;
    }

    /**
     * get playerHeroModel
     *
     * @return playerHeroModel value
     */
    public PlayerHeroModelProp getPlayerHeroModel() {
        if (this.playerHeroModel == null) {
            this.playerHeroModel = new PlayerHeroModelProp(this, FIELD_INDEX_PLAYERHEROMODEL);
        }
        return this.playerHeroModel;
    }

    /**
     * get avatarModel
     *
     * @return avatarModel value
     */
    public PlayerAvatarModelProp getAvatarModel() {
        if (this.avatarModel == null) {
            this.avatarModel = new PlayerAvatarModelProp(this, FIELD_INDEX_AVATARMODEL);
        }
        return this.avatarModel;
    }

    /**
     * get zoneModel
     *
     * @return zoneModel value
     */
    public PlayerZoneModelProp getZoneModel() {
        if (this.zoneModel == null) {
            this.zoneModel = new PlayerZoneModelProp(this, FIELD_INDEX_ZONEMODEL);
        }
        return this.zoneModel;
    }

    /**
     * get energyModel
     *
     * @return energyModel value
     */
    public PlayerEnergyModelProp getEnergyModel() {
        if (this.energyModel == null) {
            this.energyModel = new PlayerEnergyModelProp(this, FIELD_INDEX_ENERGYMODEL);
        }
        return this.energyModel;
    }

    /**
     * get playerRecruitModel
     *
     * @return playerRecruitModel value
     */
    public PlayerRecruitModelProp getPlayerRecruitModel() {
        if (this.playerRecruitModel == null) {
            this.playerRecruitModel = new PlayerRecruitModelProp(this, FIELD_INDEX_PLAYERRECRUITMODEL);
        }
        return this.playerRecruitModel;
    }

    /**
     * get activityModel
     *
     * @return activityModel value
     */
    public PlayerActivityModelProp getActivityModel() {
        if (this.activityModel == null) {
            this.activityModel = new PlayerActivityModelProp(this, FIELD_INDEX_ACTIVITYMODEL);
        }
        return this.activityModel;
    }

    /**
     * get mailModel
     *
     * @return mailModel value
     */
    public PlayerMailModelProp getMailModel() {
        if (this.mailModel == null) {
            this.mailModel = new PlayerMailModelProp(this, FIELD_INDEX_MAILMODEL);
        }
        return this.mailModel;
    }

    /**
     * get dungeonPlayer
     *
     * @return dungeonPlayer value
     */
    public DungeonPlayerProp getDungeonPlayer() {
        if (this.dungeonPlayer == null) {
            this.dungeonPlayer = new DungeonPlayerProp(this, FIELD_INDEX_DUNGEONPLAYER);
        }
        return this.dungeonPlayer;
    }

    /**
     * get killMonsterModel
     *
     * @return killMonsterModel value
     */
    public PlayerKillMonsterModelProp getKillMonsterModel() {
        if (this.killMonsterModel == null) {
            this.killMonsterModel = new PlayerKillMonsterModelProp(this, FIELD_INDEX_KILLMONSTERMODEL);
        }
        return this.killMonsterModel;
    }

    /**
     * get mileStoneModel
     *
     * @return mileStoneModel value
     */
    public PlayerMileStoneModelProp getMileStoneModel() {
        if (this.mileStoneModel == null) {
            this.mileStoneModel = new PlayerMileStoneModelProp(this, FIELD_INDEX_MILESTONEMODEL);
        }
        return this.mileStoneModel;
    }

    /**
     * get dungeonModel
     *
     * @return dungeonModel value
     */
    public PlayerDungeonModelProp getDungeonModel() {
        if (this.dungeonModel == null) {
            this.dungeonModel = new PlayerDungeonModelProp(this, FIELD_INDEX_DUNGEONMODEL);
        }
        return this.dungeonModel;
    }

    /**
     * get resetInfo
     *
     * @return resetInfo value
     */
    public PlayerResetInfoProp getResetInfo() {
        if (this.resetInfo == null) {
            this.resetInfo = new PlayerResetInfoProp(this, FIELD_INDEX_RESETINFO);
        }
        return this.resetInfo;
    }

    /**
     * get settingModel
     *
     * @return settingModel value
     */
    public PlayerSettingModelProp getSettingModel() {
        if (this.settingModel == null) {
            this.settingModel = new PlayerSettingModelProp(this, FIELD_INDEX_SETTINGMODEL);
        }
        return this.settingModel;
    }

    /**
     * get paymentModel
     *
     * @return paymentModel value
     */
    public PlayerPaymentModelProp getPaymentModel() {
        if (this.paymentModel == null) {
            this.paymentModel = new PlayerPaymentModelProp(this, FIELD_INDEX_PAYMENTMODEL);
        }
        return this.paymentModel;
    }

    /**
     * get friendPlayer
     *
     * @return friendPlayer value
     */
    public FriendPlayerProp getFriendPlayer() {
        if (this.friendPlayer == null) {
            this.friendPlayer = new FriendPlayerProp(this, FIELD_INDEX_FRIENDPLAYER);
        }
        return this.friendPlayer;
    }

    /**
     * get vipModel
     *
     * @return vipModel value
     */
    public PlayerVipModelProp getVipModel() {
        if (this.vipModel == null) {
            this.vipModel = new PlayerVipModelProp(this, FIELD_INDEX_VIPMODEL);
        }
        return this.vipModel;
    }

    /**
     * get questModel
     *
     * @return questModel value
     */
    public PlayerInnerQuestModelProp getQuestModel() {
        if (this.questModel == null) {
            this.questModel = new PlayerInnerQuestModelProp(this, FIELD_INDEX_QUESTMODEL);
        }
        return this.questModel;
    }

    /**
     * get cityModel
     *
     * @return cityModel value
     */
    public PlayerCityModelProp getCityModel() {
        if (this.cityModel == null) {
            this.cityModel = new PlayerCityModelProp(this, FIELD_INDEX_CITYMODEL);
        }
        return this.cityModel;
    }

    /**
     * get dailyGoodsModel
     *
     * @return dailyGoodsModel value
     */
    public PlayerDailyGoodsModelProp getDailyGoodsModel() {
        if (this.dailyGoodsModel == null) {
            this.dailyGoodsModel = new PlayerDailyGoodsModelProp(this, FIELD_INDEX_DAILYGOODSMODEL);
        }
        return this.dailyGoodsModel;
    }

    /**
     * get drawInfo
     *
     * @return drawInfo value
     */
    public Int32DrawInfoMapProp getDrawInfo() {
        if (this.drawInfo == null) {
            this.drawInfo = new Int32DrawInfoMapProp(this, FIELD_INDEX_DRAWINFO);
        }
        return this.drawInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDrawInfoV(DrawInfoProp v) {
        this.getDrawInfo().put(v.getItemId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public DrawInfoProp addEmptyDrawInfo(Integer k) {
        return this.getDrawInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDrawInfoSize() {
        if (this.drawInfo == null) {
            return 0;
        }
        return this.drawInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDrawInfoEmpty() {
        if (this.drawInfo == null) {
            return true;
        }
        return this.drawInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public DrawInfoProp getDrawInfoV(Integer k) {
        if (this.drawInfo == null || !this.drawInfo.containsKey(k)) {
            return null;
        }
        return this.drawInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDrawInfo() {
        if (this.drawInfo != null) {
            this.drawInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDrawInfoV(Integer k) {
        if (this.drawInfo != null) {
            this.drawInfo.remove(k);
        }
    }
    /**
     * get devBuffSysNew
     *
     * @return devBuffSysNew value
     */
    public DevBuffSysProp getDevBuffSysNew() {
        if (this.devBuffSysNew == null) {
            this.devBuffSysNew = new DevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYSNEW);
        }
        return this.devBuffSysNew;
    }

    /**
     * get kingdomModel
     *
     * @return kingdomModel value
     */
    public PlayerKingdomModelProp getKingdomModel() {
        if (this.kingdomModel == null) {
            this.kingdomModel = new PlayerKingdomModelProp(this, FIELD_INDEX_KINGDOMMODEL);
        }
        return this.kingdomModel;
    }

    /**
     * get zoneAdditionSys
     *
     * @return zoneAdditionSys value
     */
    public AdditionSysProp getZoneAdditionSys() {
        if (this.zoneAdditionSys == null) {
            this.zoneAdditionSys = new AdditionSysProp(this, FIELD_INDEX_ZONEADDITIONSYS);
        }
        return this.zoneAdditionSys;
    }

    /**
     * get triggerBundleModel
     *
     * @return triggerBundleModel value
     */
    public PlayerTriggerBundleModelProp getTriggerBundleModel() {
        if (this.triggerBundleModel == null) {
            this.triggerBundleModel = new PlayerTriggerBundleModelProp(this, FIELD_INDEX_TRIGGERBUNDLEMODEL);
        }
        return this.triggerBundleModel;
    }

    /**
     * get featureLockModel
     *
     * @return featureLockModel value
     */
    public PlayerFeatureLockModelProp getFeatureLockModel() {
        if (this.featureLockModel == null) {
            this.featureLockModel = new PlayerFeatureLockModelProp(this, FIELD_INDEX_FEATURELOCKMODEL);
        }
        return this.featureLockModel;
    }

    /**
     * get battlePassModel
     *
     * @return battlePassModel value
     */
    public PlayerBattlePassModelProp getBattlePassModel() {
        if (this.battlePassModel == null) {
            this.battlePassModel = new PlayerBattlePassModelProp(this, FIELD_INDEX_BATTLEPASSMODEL);
        }
        return this.battlePassModel;
    }

    /**
     * get dataPatchVersion
     *
     * @return dataPatchVersion value
     */
    public int getDataPatchVersion() {
        return this.dataPatchVersion;
    }

    /**
     * set dataPatchVersion && set marked
     *
     * @param dataPatchVersion new value
     * @return current object
     */
    public PlayerProp setDataPatchVersion(int dataPatchVersion) {
        if (this.dataPatchVersion != dataPatchVersion) {
            this.mark(FIELD_INDEX_DATAPATCHVERSION);
            this.dataPatchVersion = dataPatchVersion;
        }
        return this;
    }

    /**
     * inner set dataPatchVersion
     *
     * @param dataPatchVersion new value
     */
    private void innerSetDataPatchVersion(int dataPatchVersion) {
        this.dataPatchVersion = dataPatchVersion;
    }

    /**
     * get additionSysNew
     *
     * @return additionSysNew value
     */
    public AdditionSysProp getAdditionSysNew() {
        if (this.additionSysNew == null) {
            this.additionSysNew = new AdditionSysProp(this, FIELD_INDEX_ADDITIONSYSNEW);
        }
        return this.additionSysNew;
    }

    /**
     * get contactsModel
     *
     * @return contactsModel value
     */
    public PlayerContactsModelProp getContactsModel() {
        if (this.contactsModel == null) {
            this.contactsModel = new PlayerContactsModelProp(this, FIELD_INDEX_CONTACTSMODEL);
        }
        return this.contactsModel;
    }

    /**
     * get ratingModel
     *
     * @return ratingModel value
     */
    public PlayerStoreRatingModelProp getRatingModel() {
        if (this.ratingModel == null) {
            this.ratingModel = new PlayerStoreRatingModelProp(this, FIELD_INDEX_RATINGMODEL);
        }
        return this.ratingModel;
    }

    /**
     * get skynetModel
     *
     * @return skynetModel value
     */
    public PlayerSkynetModelProp getSkynetModel() {
        if (this.skynetModel == null) {
            this.skynetModel = new PlayerSkynetModelProp(this, FIELD_INDEX_SKYNETMODEL);
        }
        return this.skynetModel;
    }

    /**
     * get achievementModel
     *
     * @return achievementModel value
     */
    public PlayerAchievementModelProp getAchievementModel() {
        if (this.achievementModel == null) {
            this.achievementModel = new PlayerAchievementModelProp(this, FIELD_INDEX_ACHIEVEMENTMODEL);
        }
        return this.achievementModel;
    }

    /**
     * get seasonBattlePassModel
     *
     * @return seasonBattlePassModel value
     */
    public PlayerBattlePassModelProp getSeasonBattlePassModel() {
        if (this.seasonBattlePassModel == null) {
            this.seasonBattlePassModel = new PlayerBattlePassModelProp(this, FIELD_INDEX_SEASONBATTLEPASSMODEL);
        }
        return this.seasonBattlePassModel;
    }

    /**
     * get playerInnerBuildRHModel
     *
     * @return playerInnerBuildRHModel value
     */
    public PlayerInnerBuildRHModelProp getPlayerInnerBuildRHModel() {
        if (this.playerInnerBuildRHModel == null) {
            this.playerInnerBuildRHModel = new PlayerInnerBuildRHModelProp(this, FIELD_INDEX_PLAYERINNERBUILDRHMODEL);
        }
        return this.playerInnerBuildRHModel;
    }

    /**
     * get playerCampaignModel
     *
     * @return playerCampaignModel value
     */
    public PlayerCampaignModelProp getPlayerCampaignModel() {
        if (this.playerCampaignModel == null) {
            this.playerCampaignModel = new PlayerCampaignModelProp(this, FIELD_INDEX_PLAYERCAMPAIGNMODEL);
        }
        return this.playerCampaignModel;
    }

    /**
     * get playerUnitModel
     *
     * @return playerUnitModel value
     */
    public PlayerUnitModelProp getPlayerUnitModel() {
        if (this.playerUnitModel == null) {
            this.playerUnitModel = new PlayerUnitModelProp(this, FIELD_INDEX_PLAYERUNITMODEL);
        }
        return this.playerUnitModel;
    }

    /**
     * get playerMissionModel
     *
     * @return playerMissionModel value
     */
    public PlayerMissionModelProp getPlayerMissionModel() {
        if (this.playerMissionModel == null) {
            this.playerMissionModel = new PlayerMissionModelProp(this, FIELD_INDEX_PLAYERMISSIONMODEL);
        }
        return this.playerMissionModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerEntityPB.Builder getCopyCsBuilder() {
        final PlayerEntityPB.Builder builder = PlayerEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (!this.getOpenId().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOpenId(this.getOpenId());
            fieldCnt++;
        }  else if (builder.hasOpenId()) {
            // 清理OpenId
            builder.clearOpenId();
            fieldCnt++;
        }
        if (this.scene != null) {
            PlayerPB.PlayerScenePB.Builder tmpBuilder = PlayerPB.PlayerScenePB.newBuilder();
            final int tmpFieldCnt = this.scene.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScene(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScene();
            }
        }  else if (builder.hasScene()) {
            // 清理Scene
            builder.clearScene();
            fieldCnt++;
        }
        if (this.scenePlayer != null) {
            PlayerPB.ScenePlayerPB.Builder tmpBuilder = PlayerPB.ScenePlayerPB.newBuilder();
            final int tmpFieldCnt = this.scenePlayer.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScenePlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScenePlayer();
            }
        }  else if (builder.hasScenePlayer()) {
            // 清理ScenePlayer
            builder.clearScenePlayer();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.clan != null) {
            PlayerPB.ClanInfoPB.Builder tmpBuilder = PlayerPB.ClanInfoPB.newBuilder();
            final int tmpFieldCnt = this.clan.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClan(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClan();
            }
        }  else if (builder.hasClan()) {
            // 清理Clan
            builder.clearClan();
            fieldCnt++;
        }
        if (this.fragments != null) {
            PlayerPB.HistoryFragmentsPB.Builder tmpBuilder = PlayerPB.HistoryFragmentsPB.newBuilder();
            final int tmpFieldCnt = this.fragments.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFragments(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFragments();
            }
        }  else if (builder.hasFragments()) {
            // 清理Fragments
            builder.clearFragments();
            fieldCnt++;
        }
        if (this.items != null) {
            StructPB.Int64ItemMapPB.Builder tmpBuilder = StructPB.Int64ItemMapPB.newBuilder();
            final int tmpFieldCnt = this.items.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.purse != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.purse.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPurse(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPurse();
            }
        }  else if (builder.hasPurse()) {
            // 清理Purse
            builder.clearPurse();
            fieldCnt++;
        }
        if (this.queueTaskMap != null) {
            StructPlayerPB.Int32QueueTasksMapPB.Builder tmpBuilder = StructPlayerPB.Int32QueueTasksMapPB.newBuilder();
            final int tmpFieldCnt = this.queueTaskMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQueueTaskMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQueueTaskMap();
            }
        }  else if (builder.hasQueueTaskMap()) {
            // 清理QueueTaskMap
            builder.clearQueueTaskMap();
            fieldCnt++;
        }
        if (this.playerSoldierInBuilding != null) {
            StructPB.PlayerSoldierInBuildingPB.Builder tmpBuilder = StructPB.PlayerSoldierInBuildingPB.newBuilder();
            final int tmpFieldCnt = this.playerSoldierInBuilding.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerSoldierInBuilding(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerSoldierInBuilding();
            }
        }  else if (builder.hasPlayerSoldierInBuilding()) {
            // 清理PlayerSoldierInBuilding
            builder.clearPlayerSoldierInBuilding();
            fieldCnt++;
        }
        if (this.playerPowerInfo != null) {
            StructPB.PlayerPowerInfoPB.Builder tmpBuilder = StructPB.PlayerPowerInfoPB.newBuilder();
            final int tmpFieldCnt = this.playerPowerInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerPowerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerPowerInfo();
            }
        }  else if (builder.hasPlayerPowerInfo()) {
            // 清理PlayerPowerInfo
            builder.clearPlayerPowerInfo();
            fieldCnt++;
        }
        if (this.playerDataRecord != null) {
            StructPB.PlayerDataRecordPB.Builder tmpBuilder = StructPB.PlayerDataRecordPB.newBuilder();
            final int tmpFieldCnt = this.playerDataRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerDataRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerDataRecord();
            }
        }  else if (builder.hasPlayerDataRecord()) {
            // 清理PlayerDataRecord
            builder.clearPlayerDataRecord();
            fieldCnt++;
        }
        if (this.basicInfo != null) {
            PlayerPB.PlayerBasicInfoPB.Builder tmpBuilder = PlayerPB.PlayerBasicInfoPB.newBuilder();
            final int tmpFieldCnt = this.basicInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBasicInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBasicInfo();
            }
        }  else if (builder.hasBasicInfo()) {
            // 清理BasicInfo
            builder.clearBasicInfo();
            fieldCnt++;
        }
        if (this.formation != null) {
            StructPlayerPB.PlayerFormationPB.Builder tmpBuilder = StructPlayerPB.PlayerFormationPB.newBuilder();
            final int tmpFieldCnt = this.formation.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormation();
            }
        }  else if (builder.hasFormation()) {
            // 清理Formation
            builder.clearFormation();
            fieldCnt++;
        }
        if (this.resourceProduce != null) {
            PlayerPB.PlayerResourceProduceMapPB.Builder tmpBuilder = PlayerPB.PlayerResourceProduceMapPB.newBuilder();
            final int tmpFieldCnt = this.resourceProduce.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResourceProduce(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResourceProduce();
            }
        }  else if (builder.hasResourceProduce()) {
            // 清理ResourceProduce
            builder.clearResourceProduce();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            StructPB.AdditionSysPB.Builder tmpBuilder = StructPB.AdditionSysPB.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattlePB.DevBuffSysPB.Builder tmpBuilder = StructBattlePB.DevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.refreshSys != null) {
            PlayerPB.RefreshInfoPB.Builder tmpBuilder = PlayerPB.RefreshInfoPB.newBuilder();
            final int tmpFieldCnt = this.refreshSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshSys();
            }
        }  else if (builder.hasRefreshSys()) {
            // 清理RefreshSys
            builder.clearRefreshSys();
            fieldCnt++;
        }
        if (this.taskSystem != null) {
            PlayerPB.TaskSystemPB.Builder tmpBuilder = PlayerPB.TaskSystemPB.newBuilder();
            final int tmpFieldCnt = this.taskSystem.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskSystem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskSystem();
            }
        }  else if (builder.hasTaskSystem()) {
            // 清理TaskSystem
            builder.clearTaskSystem();
            fieldCnt++;
        }
        if (this.discountInfo != null) {
            PlayerPB.DiscountInfoPB.Builder tmpBuilder = PlayerPB.DiscountInfoPB.newBuilder();
            final int tmpFieldCnt = this.discountInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDiscountInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDiscountInfo();
            }
        }  else if (builder.hasDiscountInfo()) {
            // 清理DiscountInfo
            builder.clearDiscountInfo();
            fieldCnt++;
        }
        if (this.PlayerGuidanceModel != null) {
            PlayerPB.PlayerGuidanceModelPB.Builder tmpBuilder = PlayerPB.PlayerGuidanceModelPB.newBuilder();
            final int tmpFieldCnt = this.PlayerGuidanceModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerGuidanceModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerGuidanceModel();
            }
        }  else if (builder.hasPlayerGuidanceModel()) {
            // 清理PlayerGuidanceModel
            builder.clearPlayerGuidanceModel();
            fieldCnt++;
        }
        if (this.PlayerTechModel != null) {
            PlayerPB.PlayerTechnologyModelPB.Builder tmpBuilder = PlayerPB.PlayerTechnologyModelPB.newBuilder();
            final int tmpFieldCnt = this.PlayerTechModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerTechModel();
            }
        }  else if (builder.hasPlayerTechModel()) {
            // 清理PlayerTechModel
            builder.clearPlayerTechModel();
            fieldCnt++;
        }
        if (this.playerPlaneModel != null) {
            PlayerPB.PlayerPlaneModelPB.Builder tmpBuilder = PlayerPB.PlayerPlaneModelPB.newBuilder();
            final int tmpFieldCnt = this.playerPlaneModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerPlaneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerPlaneModel();
            }
        }  else if (builder.hasPlayerPlaneModel()) {
            // 清理PlayerPlaneModel
            builder.clearPlayerPlaneModel();
            fieldCnt++;
        }
        if (this.ChatPlayer != null) {
            PlayerPB.ChatPlayerPB.Builder tmpBuilder = PlayerPB.ChatPlayerPB.newBuilder();
            final int tmpFieldCnt = this.ChatPlayer.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChatPlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChatPlayer();
            }
        }  else if (builder.hasChatPlayer()) {
            // 清理ChatPlayer
            builder.clearChatPlayer();
            fieldCnt++;
        }
        if (this.newbieModel != null) {
            PlayerPB.PlayerNewbieModelPB.Builder tmpBuilder = PlayerPB.PlayerNewbieModelPB.newBuilder();
            final int tmpFieldCnt = this.newbieModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNewbieModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNewbieModel();
            }
        }  else if (builder.hasNewbieModel()) {
            // 清理NewbieModel
            builder.clearNewbieModel();
            fieldCnt++;
        }
        if (this.redDotModel != null) {
            PlayerPB.PlayerRedDotModelPB.Builder tmpBuilder = PlayerPB.PlayerRedDotModelPB.newBuilder();
            final int tmpFieldCnt = this.redDotModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotModel();
            }
        }  else if (builder.hasRedDotModel()) {
            // 清理RedDotModel
            builder.clearRedDotModel();
            fieldCnt++;
        }
        if (this.playerHeroModel != null) {
            PlayerPB.PlayerHeroModelPB.Builder tmpBuilder = PlayerPB.PlayerHeroModelPB.newBuilder();
            final int tmpFieldCnt = this.playerHeroModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerHeroModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerHeroModel();
            }
        }  else if (builder.hasPlayerHeroModel()) {
            // 清理PlayerHeroModel
            builder.clearPlayerHeroModel();
            fieldCnt++;
        }
        if (this.avatarModel != null) {
            PlayerPB.PlayerAvatarModelPB.Builder tmpBuilder = PlayerPB.PlayerAvatarModelPB.newBuilder();
            final int tmpFieldCnt = this.avatarModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAvatarModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAvatarModel();
            }
        }  else if (builder.hasAvatarModel()) {
            // 清理AvatarModel
            builder.clearAvatarModel();
            fieldCnt++;
        }
        if (this.zoneModel != null) {
            PlayerPB.PlayerZoneModelPB.Builder tmpBuilder = PlayerPB.PlayerZoneModelPB.newBuilder();
            final int tmpFieldCnt = this.zoneModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneModel();
            }
        }  else if (builder.hasZoneModel()) {
            // 清理ZoneModel
            builder.clearZoneModel();
            fieldCnt++;
        }
        if (this.energyModel != null) {
            PlayerPB.PlayerEnergyModelPB.Builder tmpBuilder = PlayerPB.PlayerEnergyModelPB.newBuilder();
            final int tmpFieldCnt = this.energyModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEnergyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEnergyModel();
            }
        }  else if (builder.hasEnergyModel()) {
            // 清理EnergyModel
            builder.clearEnergyModel();
            fieldCnt++;
        }
        if (this.playerRecruitModel != null) {
            PlayerPB.PlayerRecruitModelPB.Builder tmpBuilder = PlayerPB.PlayerRecruitModelPB.newBuilder();
            final int tmpFieldCnt = this.playerRecruitModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerRecruitModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerRecruitModel();
            }
        }  else if (builder.hasPlayerRecruitModel()) {
            // 清理PlayerRecruitModel
            builder.clearPlayerRecruitModel();
            fieldCnt++;
        }
        if (this.activityModel != null) {
            PlayerPB.PlayerActivityModelPB.Builder tmpBuilder = PlayerPB.PlayerActivityModelPB.newBuilder();
            final int tmpFieldCnt = this.activityModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityModel();
            }
        }  else if (builder.hasActivityModel()) {
            // 清理ActivityModel
            builder.clearActivityModel();
            fieldCnt++;
        }
        if (this.mailModel != null) {
            PlayerPB.PlayerMailModelPB.Builder tmpBuilder = PlayerPB.PlayerMailModelPB.newBuilder();
            final int tmpFieldCnt = this.mailModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMailModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMailModel();
            }
        }  else if (builder.hasMailModel()) {
            // 清理MailModel
            builder.clearMailModel();
            fieldCnt++;
        }
        if (this.dungeonPlayer != null) {
            PlayerPB.DungeonPlayerPB.Builder tmpBuilder = PlayerPB.DungeonPlayerPB.newBuilder();
            final int tmpFieldCnt = this.dungeonPlayer.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonPlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonPlayer();
            }
        }  else if (builder.hasDungeonPlayer()) {
            // 清理DungeonPlayer
            builder.clearDungeonPlayer();
            fieldCnt++;
        }
        if (this.killMonsterModel != null) {
            PlayerPB.PlayerKillMonsterModelPB.Builder tmpBuilder = PlayerPB.PlayerKillMonsterModelPB.newBuilder();
            final int tmpFieldCnt = this.killMonsterModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKillMonsterModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKillMonsterModel();
            }
        }  else if (builder.hasKillMonsterModel()) {
            // 清理KillMonsterModel
            builder.clearKillMonsterModel();
            fieldCnt++;
        }
        if (this.mileStoneModel != null) {
            PlayerPB.PlayerMileStoneModelPB.Builder tmpBuilder = PlayerPB.PlayerMileStoneModelPB.newBuilder();
            final int tmpFieldCnt = this.mileStoneModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneModel();
            }
        }  else if (builder.hasMileStoneModel()) {
            // 清理MileStoneModel
            builder.clearMileStoneModel();
            fieldCnt++;
        }
        if (this.dungeonModel != null) {
            PlayerPB.PlayerDungeonModelPB.Builder tmpBuilder = PlayerPB.PlayerDungeonModelPB.newBuilder();
            final int tmpFieldCnt = this.dungeonModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonModel();
            }
        }  else if (builder.hasDungeonModel()) {
            // 清理DungeonModel
            builder.clearDungeonModel();
            fieldCnt++;
        }
        if (this.resetInfo != null) {
            PlayerPB.PlayerResetInfoPB.Builder tmpBuilder = PlayerPB.PlayerResetInfoPB.newBuilder();
            final int tmpFieldCnt = this.resetInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResetInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResetInfo();
            }
        }  else if (builder.hasResetInfo()) {
            // 清理ResetInfo
            builder.clearResetInfo();
            fieldCnt++;
        }
        if (this.settingModel != null) {
            PlayerPB.PlayerSettingModelPB.Builder tmpBuilder = PlayerPB.PlayerSettingModelPB.newBuilder();
            final int tmpFieldCnt = this.settingModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSettingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSettingModel();
            }
        }  else if (builder.hasSettingModel()) {
            // 清理SettingModel
            builder.clearSettingModel();
            fieldCnt++;
        }
        if (this.paymentModel != null) {
            PlayerPB.PlayerPaymentModelPB.Builder tmpBuilder = PlayerPB.PlayerPaymentModelPB.newBuilder();
            final int tmpFieldCnt = this.paymentModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPaymentModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPaymentModel();
            }
        }  else if (builder.hasPaymentModel()) {
            // 清理PaymentModel
            builder.clearPaymentModel();
            fieldCnt++;
        }
        if (this.friendPlayer != null) {
            PlayerPB.FriendPlayerPB.Builder tmpBuilder = PlayerPB.FriendPlayerPB.newBuilder();
            final int tmpFieldCnt = this.friendPlayer.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFriendPlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFriendPlayer();
            }
        }  else if (builder.hasFriendPlayer()) {
            // 清理FriendPlayer
            builder.clearFriendPlayer();
            fieldCnt++;
        }
        if (this.vipModel != null) {
            PlayerPB.PlayerVipModelPB.Builder tmpBuilder = PlayerPB.PlayerVipModelPB.newBuilder();
            final int tmpFieldCnt = this.vipModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipModel();
            }
        }  else if (builder.hasVipModel()) {
            // 清理VipModel
            builder.clearVipModel();
            fieldCnt++;
        }
        if (this.questModel != null) {
            PlayerPB.PlayerInnerQuestModelPB.Builder tmpBuilder = PlayerPB.PlayerInnerQuestModelPB.newBuilder();
            final int tmpFieldCnt = this.questModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQuestModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQuestModel();
            }
        }  else if (builder.hasQuestModel()) {
            // 清理QuestModel
            builder.clearQuestModel();
            fieldCnt++;
        }
        if (this.cityModel != null) {
            PlayerPB.PlayerCityModelPB.Builder tmpBuilder = PlayerPB.PlayerCityModelPB.newBuilder();
            final int tmpFieldCnt = this.cityModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityModel();
            }
        }  else if (builder.hasCityModel()) {
            // 清理CityModel
            builder.clearCityModel();
            fieldCnt++;
        }
        if (this.dailyGoodsModel != null) {
            PlayerPB.PlayerDailyGoodsModelPB.Builder tmpBuilder = PlayerPB.PlayerDailyGoodsModelPB.newBuilder();
            final int tmpFieldCnt = this.dailyGoodsModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyGoodsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyGoodsModel();
            }
        }  else if (builder.hasDailyGoodsModel()) {
            // 清理DailyGoodsModel
            builder.clearDailyGoodsModel();
            fieldCnt++;
        }
        if (this.devBuffSysNew != null) {
            StructBattlePB.DevBuffSysPB.Builder tmpBuilder = StructBattlePB.DevBuffSysPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSysNew.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSysNew();
            }
        }  else if (builder.hasDevBuffSysNew()) {
            // 清理DevBuffSysNew
            builder.clearDevBuffSysNew();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            PlayerPB.PlayerKingdomModelPB.Builder tmpBuilder = PlayerPB.PlayerKingdomModelPB.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.triggerBundleModel != null) {
            PlayerPB.PlayerTriggerBundleModelPB.Builder tmpBuilder = PlayerPB.PlayerTriggerBundleModelPB.newBuilder();
            final int tmpFieldCnt = this.triggerBundleModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerBundleModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerBundleModel();
            }
        }  else if (builder.hasTriggerBundleModel()) {
            // 清理TriggerBundleModel
            builder.clearTriggerBundleModel();
            fieldCnt++;
        }
        if (this.featureLockModel != null) {
            PlayerPB.PlayerFeatureLockModelPB.Builder tmpBuilder = PlayerPB.PlayerFeatureLockModelPB.newBuilder();
            final int tmpFieldCnt = this.featureLockModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFeatureLockModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFeatureLockModel();
            }
        }  else if (builder.hasFeatureLockModel()) {
            // 清理FeatureLockModel
            builder.clearFeatureLockModel();
            fieldCnt++;
        }
        if (this.battlePassModel != null) {
            PlayerPB.PlayerBattlePassModelPB.Builder tmpBuilder = PlayerPB.PlayerBattlePassModelPB.newBuilder();
            final int tmpFieldCnt = this.battlePassModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattlePassModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattlePassModel();
            }
        }  else if (builder.hasBattlePassModel()) {
            // 清理BattlePassModel
            builder.clearBattlePassModel();
            fieldCnt++;
        }
        if (this.additionSysNew != null) {
            StructPB.AdditionSysPB.Builder tmpBuilder = StructPB.AdditionSysPB.newBuilder();
            final int tmpFieldCnt = this.additionSysNew.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSysNew();
            }
        }  else if (builder.hasAdditionSysNew()) {
            // 清理AdditionSysNew
            builder.clearAdditionSysNew();
            fieldCnt++;
        }
        if (this.skynetModel != null) {
            PlayerPB.PlayerSkynetModelPB.Builder tmpBuilder = PlayerPB.PlayerSkynetModelPB.newBuilder();
            final int tmpFieldCnt = this.skynetModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkynetModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkynetModel();
            }
        }  else if (builder.hasSkynetModel()) {
            // 清理SkynetModel
            builder.clearSkynetModel();
            fieldCnt++;
        }
        if (this.seasonBattlePassModel != null) {
            PlayerPB.PlayerBattlePassModelPB.Builder tmpBuilder = PlayerPB.PlayerBattlePassModelPB.newBuilder();
            final int tmpFieldCnt = this.seasonBattlePassModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSeasonBattlePassModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSeasonBattlePassModel();
            }
        }  else if (builder.hasSeasonBattlePassModel()) {
            // 清理SeasonBattlePassModel
            builder.clearSeasonBattlePassModel();
            fieldCnt++;
        }
        if (this.playerInnerBuildRHModel != null) {
            PlayerPB.PlayerInnerBuildRHModelPB.Builder tmpBuilder = PlayerPB.PlayerInnerBuildRHModelPB.newBuilder();
            final int tmpFieldCnt = this.playerInnerBuildRHModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerInnerBuildRHModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerInnerBuildRHModel();
            }
        }  else if (builder.hasPlayerInnerBuildRHModel()) {
            // 清理PlayerInnerBuildRHModel
            builder.clearPlayerInnerBuildRHModel();
            fieldCnt++;
        }
        if (this.playerCampaignModel != null) {
            PlayerPB.PlayerCampaignModelPB.Builder tmpBuilder = PlayerPB.PlayerCampaignModelPB.newBuilder();
            final int tmpFieldCnt = this.playerCampaignModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCampaignModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCampaignModel();
            }
        }  else if (builder.hasPlayerCampaignModel()) {
            // 清理PlayerCampaignModel
            builder.clearPlayerCampaignModel();
            fieldCnt++;
        }
        if (this.playerUnitModel != null) {
            PlayerPB.PlayerUnitModelPB.Builder tmpBuilder = PlayerPB.PlayerUnitModelPB.newBuilder();
            final int tmpFieldCnt = this.playerUnitModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerUnitModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerUnitModel();
            }
        }  else if (builder.hasPlayerUnitModel()) {
            // 清理PlayerUnitModel
            builder.clearPlayerUnitModel();
            fieldCnt++;
        }
        if (this.playerMissionModel != null) {
            PlayerPB.PlayerMissionModelPB.Builder tmpBuilder = PlayerPB.PlayerMissionModelPB.newBuilder();
            final int tmpFieldCnt = this.playerMissionModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerMissionModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerMissionModel();
            }
        }  else if (builder.hasPlayerMissionModel()) {
            // 清理PlayerMissionModel
            builder.clearPlayerMissionModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENID)) {
            builder.setOpenId(this.getOpenId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENE) && this.scene != null) {
            final boolean needClear = !builder.hasScene();
            final int tmpFieldCnt = this.scene.copyChangeToCs(builder.getSceneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScene();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEPLAYER) && this.scenePlayer != null) {
            final boolean needClear = !builder.hasScenePlayer();
            final int tmpFieldCnt = this.scenePlayer.copyChangeToCs(builder.getScenePlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScenePlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAN) && this.clan != null) {
            final boolean needClear = !builder.hasClan();
            final int tmpFieldCnt = this.clan.copyChangeToCs(builder.getClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRAGMENTS) && this.fragments != null) {
            final boolean needClear = !builder.hasFragments();
            final int tmpFieldCnt = this.fragments.copyChangeToCs(builder.getFragmentsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFragments();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_PURSE) && this.purse != null) {
            final boolean needClear = !builder.hasPurse();
            final int tmpFieldCnt = this.purse.copyChangeToCs(builder.getPurseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPurse();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKMAP) && this.queueTaskMap != null) {
            final boolean needClear = !builder.hasQueueTaskMap();
            final int tmpFieldCnt = this.queueTaskMap.copyChangeToCs(builder.getQueueTaskMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSOLDIERINBUILDING) && this.playerSoldierInBuilding != null) {
            final boolean needClear = !builder.hasPlayerSoldierInBuilding();
            final int tmpFieldCnt = this.playerSoldierInBuilding.copyChangeToCs(builder.getPlayerSoldierInBuildingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSoldierInBuilding();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPOWERINFO) && this.playerPowerInfo != null) {
            final boolean needClear = !builder.hasPlayerPowerInfo();
            final int tmpFieldCnt = this.playerPowerInfo.copyChangeToCs(builder.getPlayerPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPowerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERDATARECORD) && this.playerDataRecord != null) {
            final boolean needClear = !builder.hasPlayerDataRecord();
            final int tmpFieldCnt = this.playerDataRecord.copyChangeToCs(builder.getPlayerDataRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerDataRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASICINFO) && this.basicInfo != null) {
            final boolean needClear = !builder.hasBasicInfo();
            final int tmpFieldCnt = this.basicInfo.copyChangeToCs(builder.getBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            final boolean needClear = !builder.hasFormation();
            final int tmpFieldCnt = this.formation.copyChangeToCs(builder.getFormationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormation();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEPRODUCE) && this.resourceProduce != null) {
            final boolean needClear = !builder.hasResourceProduce();
            final int tmpFieldCnt = this.resourceProduce.copyChangeToCs(builder.getResourceProduceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceProduce();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHSYS) && this.refreshSys != null) {
            final boolean needClear = !builder.hasRefreshSys();
            final int tmpFieldCnt = this.refreshSys.copyChangeToCs(builder.getRefreshSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKSYSTEM) && this.taskSystem != null) {
            final boolean needClear = !builder.hasTaskSystem();
            final int tmpFieldCnt = this.taskSystem.copyChangeToCs(builder.getTaskSystemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskSystem();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCOUNTINFO) && this.discountInfo != null) {
            final boolean needClear = !builder.hasDiscountInfo();
            final int tmpFieldCnt = this.discountInfo.copyChangeToCs(builder.getDiscountInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscountInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERGUIDANCEMODEL) && this.PlayerGuidanceModel != null) {
            final boolean needClear = !builder.hasPlayerGuidanceModel();
            final int tmpFieldCnt = this.PlayerGuidanceModel.copyChangeToCs(builder.getPlayerGuidanceModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerGuidanceModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTECHMODEL) && this.PlayerTechModel != null) {
            final boolean needClear = !builder.hasPlayerTechModel();
            final int tmpFieldCnt = this.PlayerTechModel.copyChangeToCs(builder.getPlayerTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEMODEL) && this.playerPlaneModel != null) {
            final boolean needClear = !builder.hasPlayerPlaneModel();
            final int tmpFieldCnt = this.playerPlaneModel.copyChangeToCs(builder.getPlayerPlaneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPlaneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHATPLAYER) && this.ChatPlayer != null) {
            final boolean needClear = !builder.hasChatPlayer();
            final int tmpFieldCnt = this.ChatPlayer.copyChangeToCs(builder.getChatPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChatPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEMODEL) && this.newbieModel != null) {
            final boolean needClear = !builder.hasNewbieModel();
            final int tmpFieldCnt = this.newbieModel.copyChangeToCs(builder.getNewbieModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNewbieModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToCs(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMODEL) && this.playerHeroModel != null) {
            final boolean needClear = !builder.hasPlayerHeroModel();
            final int tmpFieldCnt = this.playerHeroModel.copyChangeToCs(builder.getPlayerHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_AVATARMODEL) && this.avatarModel != null) {
            final boolean needClear = !builder.hasAvatarModel();
            final int tmpFieldCnt = this.avatarModel.copyChangeToCs(builder.getAvatarModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAvatarModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            final boolean needClear = !builder.hasZoneModel();
            final int tmpFieldCnt = this.zoneModel.copyChangeToCs(builder.getZoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENERGYMODEL) && this.energyModel != null) {
            final boolean needClear = !builder.hasEnergyModel();
            final int tmpFieldCnt = this.energyModel.copyChangeToCs(builder.getEnergyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEnergyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERRECRUITMODEL) && this.playerRecruitModel != null) {
            final boolean needClear = !builder.hasPlayerRecruitModel();
            final int tmpFieldCnt = this.playerRecruitModel.copyChangeToCs(builder.getPlayerRecruitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerRecruitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToCs(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAILMODEL) && this.mailModel != null) {
            final boolean needClear = !builder.hasMailModel();
            final int tmpFieldCnt = this.mailModel.copyChangeToCs(builder.getMailModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMailModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONPLAYER) && this.dungeonPlayer != null) {
            final boolean needClear = !builder.hasDungeonPlayer();
            final int tmpFieldCnt = this.dungeonPlayer.copyChangeToCs(builder.getDungeonPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMODEL) && this.killMonsterModel != null) {
            final boolean needClear = !builder.hasKillMonsterModel();
            final int tmpFieldCnt = this.killMonsterModel.copyChangeToCs(builder.getKillMonsterModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKillMonsterModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEMODEL) && this.mileStoneModel != null) {
            final boolean needClear = !builder.hasMileStoneModel();
            final int tmpFieldCnt = this.mileStoneModel.copyChangeToCs(builder.getMileStoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONMODEL) && this.dungeonModel != null) {
            final boolean needClear = !builder.hasDungeonModel();
            final int tmpFieldCnt = this.dungeonModel.copyChangeToCs(builder.getDungeonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETINFO) && this.resetInfo != null) {
            final boolean needClear = !builder.hasResetInfo();
            final int tmpFieldCnt = this.resetInfo.copyChangeToCs(builder.getResetInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETTINGMODEL) && this.settingModel != null) {
            final boolean needClear = !builder.hasSettingModel();
            final int tmpFieldCnt = this.settingModel.copyChangeToCs(builder.getSettingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSettingModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYMENTMODEL) && this.paymentModel != null) {
            final boolean needClear = !builder.hasPaymentModel();
            final int tmpFieldCnt = this.paymentModel.copyChangeToCs(builder.getPaymentModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPaymentModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRIENDPLAYER) && this.friendPlayer != null) {
            final boolean needClear = !builder.hasFriendPlayer();
            final int tmpFieldCnt = this.friendPlayer.copyChangeToCs(builder.getFriendPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFriendPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPMODEL) && this.vipModel != null) {
            final boolean needClear = !builder.hasVipModel();
            final int tmpFieldCnt = this.vipModel.copyChangeToCs(builder.getVipModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUESTMODEL) && this.questModel != null) {
            final boolean needClear = !builder.hasQuestModel();
            final int tmpFieldCnt = this.questModel.copyChangeToCs(builder.getQuestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQuestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYMODEL) && this.cityModel != null) {
            final boolean needClear = !builder.hasCityModel();
            final int tmpFieldCnt = this.cityModel.copyChangeToCs(builder.getCityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DAILYGOODSMODEL) && this.dailyGoodsModel != null) {
            final boolean needClear = !builder.hasDailyGoodsModel();
            final int tmpFieldCnt = this.dailyGoodsModel.copyChangeToCs(builder.getDailyGoodsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyGoodsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToCs(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToCs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEMODEL) && this.triggerBundleModel != null) {
            final boolean needClear = !builder.hasTriggerBundleModel();
            final int tmpFieldCnt = this.triggerBundleModel.copyChangeToCs(builder.getTriggerBundleModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FEATURELOCKMODEL) && this.featureLockModel != null) {
            final boolean needClear = !builder.hasFeatureLockModel();
            final int tmpFieldCnt = this.featureLockModel.copyChangeToCs(builder.getFeatureLockModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFeatureLockModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPASSMODEL) && this.battlePassModel != null) {
            final boolean needClear = !builder.hasBattlePassModel();
            final int tmpFieldCnt = this.battlePassModel.copyChangeToCs(builder.getBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYSNEW) && this.additionSysNew != null) {
            final boolean needClear = !builder.hasAdditionSysNew();
            final int tmpFieldCnt = this.additionSysNew.copyChangeToCs(builder.getAdditionSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKYNETMODEL) && this.skynetModel != null) {
            final boolean needClear = !builder.hasSkynetModel();
            final int tmpFieldCnt = this.skynetModel.copyChangeToCs(builder.getSkynetModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkynetModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONBATTLEPASSMODEL) && this.seasonBattlePassModel != null) {
            final boolean needClear = !builder.hasSeasonBattlePassModel();
            final int tmpFieldCnt = this.seasonBattlePassModel.copyChangeToCs(builder.getSeasonBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINNERBUILDRHMODEL) && this.playerInnerBuildRHModel != null) {
            final boolean needClear = !builder.hasPlayerInnerBuildRHModel();
            final int tmpFieldCnt = this.playerInnerBuildRHModel.copyChangeToCs(builder.getPlayerInnerBuildRHModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInnerBuildRHModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMPAIGNMODEL) && this.playerCampaignModel != null) {
            final boolean needClear = !builder.hasPlayerCampaignModel();
            final int tmpFieldCnt = this.playerCampaignModel.copyChangeToCs(builder.getPlayerCampaignModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCampaignModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMODEL) && this.playerUnitModel != null) {
            final boolean needClear = !builder.hasPlayerUnitModel();
            final int tmpFieldCnt = this.playerUnitModel.copyChangeToCs(builder.getPlayerUnitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERMISSIONMODEL) && this.playerMissionModel != null) {
            final boolean needClear = !builder.hasPlayerMissionModel();
            final int tmpFieldCnt = this.playerMissionModel.copyChangeToCs(builder.getPlayerMissionModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerMissionModel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENID)) {
            builder.setOpenId(this.getOpenId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENE) && this.scene != null) {
            final boolean needClear = !builder.hasScene();
            final int tmpFieldCnt = this.scene.copyChangeToAndClearDeleteKeysCs(builder.getSceneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScene();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEPLAYER) && this.scenePlayer != null) {
            final boolean needClear = !builder.hasScenePlayer();
            final int tmpFieldCnt = this.scenePlayer.copyChangeToAndClearDeleteKeysCs(builder.getScenePlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScenePlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAN) && this.clan != null) {
            final boolean needClear = !builder.hasClan();
            final int tmpFieldCnt = this.clan.copyChangeToAndClearDeleteKeysCs(builder.getClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRAGMENTS) && this.fragments != null) {
            final boolean needClear = !builder.hasFragments();
            final int tmpFieldCnt = this.fragments.copyChangeToAndClearDeleteKeysCs(builder.getFragmentsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFragments();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToAndClearDeleteKeysCs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_PURSE) && this.purse != null) {
            final boolean needClear = !builder.hasPurse();
            final int tmpFieldCnt = this.purse.copyChangeToAndClearDeleteKeysCs(builder.getPurseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPurse();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKMAP) && this.queueTaskMap != null) {
            final boolean needClear = !builder.hasQueueTaskMap();
            final int tmpFieldCnt = this.queueTaskMap.copyChangeToAndClearDeleteKeysCs(builder.getQueueTaskMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSOLDIERINBUILDING) && this.playerSoldierInBuilding != null) {
            final boolean needClear = !builder.hasPlayerSoldierInBuilding();
            final int tmpFieldCnt = this.playerSoldierInBuilding.copyChangeToAndClearDeleteKeysCs(builder.getPlayerSoldierInBuildingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSoldierInBuilding();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPOWERINFO) && this.playerPowerInfo != null) {
            final boolean needClear = !builder.hasPlayerPowerInfo();
            final int tmpFieldCnt = this.playerPowerInfo.copyChangeToAndClearDeleteKeysCs(builder.getPlayerPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPowerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERDATARECORD) && this.playerDataRecord != null) {
            final boolean needClear = !builder.hasPlayerDataRecord();
            final int tmpFieldCnt = this.playerDataRecord.copyChangeToAndClearDeleteKeysCs(builder.getPlayerDataRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerDataRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASICINFO) && this.basicInfo != null) {
            final boolean needClear = !builder.hasBasicInfo();
            final int tmpFieldCnt = this.basicInfo.copyChangeToAndClearDeleteKeysCs(builder.getBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            final boolean needClear = !builder.hasFormation();
            final int tmpFieldCnt = this.formation.copyChangeToAndClearDeleteKeysCs(builder.getFormationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormation();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEPRODUCE) && this.resourceProduce != null) {
            final boolean needClear = !builder.hasResourceProduce();
            final int tmpFieldCnt = this.resourceProduce.copyChangeToAndClearDeleteKeysCs(builder.getResourceProduceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceProduce();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToAndClearDeleteKeysCs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHSYS) && this.refreshSys != null) {
            final boolean needClear = !builder.hasRefreshSys();
            final int tmpFieldCnt = this.refreshSys.copyChangeToAndClearDeleteKeysCs(builder.getRefreshSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKSYSTEM) && this.taskSystem != null) {
            final boolean needClear = !builder.hasTaskSystem();
            final int tmpFieldCnt = this.taskSystem.copyChangeToAndClearDeleteKeysCs(builder.getTaskSystemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskSystem();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCOUNTINFO) && this.discountInfo != null) {
            final boolean needClear = !builder.hasDiscountInfo();
            final int tmpFieldCnt = this.discountInfo.copyChangeToAndClearDeleteKeysCs(builder.getDiscountInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscountInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERGUIDANCEMODEL) && this.PlayerGuidanceModel != null) {
            final boolean needClear = !builder.hasPlayerGuidanceModel();
            final int tmpFieldCnt = this.PlayerGuidanceModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerGuidanceModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerGuidanceModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTECHMODEL) && this.PlayerTechModel != null) {
            final boolean needClear = !builder.hasPlayerTechModel();
            final int tmpFieldCnt = this.PlayerTechModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEMODEL) && this.playerPlaneModel != null) {
            final boolean needClear = !builder.hasPlayerPlaneModel();
            final int tmpFieldCnt = this.playerPlaneModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerPlaneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPlaneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHATPLAYER) && this.ChatPlayer != null) {
            final boolean needClear = !builder.hasChatPlayer();
            final int tmpFieldCnt = this.ChatPlayer.copyChangeToAndClearDeleteKeysCs(builder.getChatPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChatPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEMODEL) && this.newbieModel != null) {
            final boolean needClear = !builder.hasNewbieModel();
            final int tmpFieldCnt = this.newbieModel.copyChangeToAndClearDeleteKeysCs(builder.getNewbieModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNewbieModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToAndClearDeleteKeysCs(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMODEL) && this.playerHeroModel != null) {
            final boolean needClear = !builder.hasPlayerHeroModel();
            final int tmpFieldCnt = this.playerHeroModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_AVATARMODEL) && this.avatarModel != null) {
            final boolean needClear = !builder.hasAvatarModel();
            final int tmpFieldCnt = this.avatarModel.copyChangeToAndClearDeleteKeysCs(builder.getAvatarModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAvatarModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            final boolean needClear = !builder.hasZoneModel();
            final int tmpFieldCnt = this.zoneModel.copyChangeToAndClearDeleteKeysCs(builder.getZoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENERGYMODEL) && this.energyModel != null) {
            final boolean needClear = !builder.hasEnergyModel();
            final int tmpFieldCnt = this.energyModel.copyChangeToAndClearDeleteKeysCs(builder.getEnergyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEnergyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERRECRUITMODEL) && this.playerRecruitModel != null) {
            final boolean needClear = !builder.hasPlayerRecruitModel();
            final int tmpFieldCnt = this.playerRecruitModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerRecruitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerRecruitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToAndClearDeleteKeysCs(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAILMODEL) && this.mailModel != null) {
            final boolean needClear = !builder.hasMailModel();
            final int tmpFieldCnt = this.mailModel.copyChangeToAndClearDeleteKeysCs(builder.getMailModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMailModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONPLAYER) && this.dungeonPlayer != null) {
            final boolean needClear = !builder.hasDungeonPlayer();
            final int tmpFieldCnt = this.dungeonPlayer.copyChangeToAndClearDeleteKeysCs(builder.getDungeonPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMODEL) && this.killMonsterModel != null) {
            final boolean needClear = !builder.hasKillMonsterModel();
            final int tmpFieldCnt = this.killMonsterModel.copyChangeToAndClearDeleteKeysCs(builder.getKillMonsterModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKillMonsterModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEMODEL) && this.mileStoneModel != null) {
            final boolean needClear = !builder.hasMileStoneModel();
            final int tmpFieldCnt = this.mileStoneModel.copyChangeToAndClearDeleteKeysCs(builder.getMileStoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONMODEL) && this.dungeonModel != null) {
            final boolean needClear = !builder.hasDungeonModel();
            final int tmpFieldCnt = this.dungeonModel.copyChangeToAndClearDeleteKeysCs(builder.getDungeonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETINFO) && this.resetInfo != null) {
            final boolean needClear = !builder.hasResetInfo();
            final int tmpFieldCnt = this.resetInfo.copyChangeToAndClearDeleteKeysCs(builder.getResetInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETTINGMODEL) && this.settingModel != null) {
            final boolean needClear = !builder.hasSettingModel();
            final int tmpFieldCnt = this.settingModel.copyChangeToAndClearDeleteKeysCs(builder.getSettingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSettingModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYMENTMODEL) && this.paymentModel != null) {
            final boolean needClear = !builder.hasPaymentModel();
            final int tmpFieldCnt = this.paymentModel.copyChangeToAndClearDeleteKeysCs(builder.getPaymentModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPaymentModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRIENDPLAYER) && this.friendPlayer != null) {
            final boolean needClear = !builder.hasFriendPlayer();
            final int tmpFieldCnt = this.friendPlayer.copyChangeToAndClearDeleteKeysCs(builder.getFriendPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFriendPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPMODEL) && this.vipModel != null) {
            final boolean needClear = !builder.hasVipModel();
            final int tmpFieldCnt = this.vipModel.copyChangeToAndClearDeleteKeysCs(builder.getVipModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUESTMODEL) && this.questModel != null) {
            final boolean needClear = !builder.hasQuestModel();
            final int tmpFieldCnt = this.questModel.copyChangeToAndClearDeleteKeysCs(builder.getQuestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQuestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYMODEL) && this.cityModel != null) {
            final boolean needClear = !builder.hasCityModel();
            final int tmpFieldCnt = this.cityModel.copyChangeToAndClearDeleteKeysCs(builder.getCityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DAILYGOODSMODEL) && this.dailyGoodsModel != null) {
            final boolean needClear = !builder.hasDailyGoodsModel();
            final int tmpFieldCnt = this.dailyGoodsModel.copyChangeToAndClearDeleteKeysCs(builder.getDailyGoodsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyGoodsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToAndClearDeleteKeysCs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEMODEL) && this.triggerBundleModel != null) {
            final boolean needClear = !builder.hasTriggerBundleModel();
            final int tmpFieldCnt = this.triggerBundleModel.copyChangeToAndClearDeleteKeysCs(builder.getTriggerBundleModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FEATURELOCKMODEL) && this.featureLockModel != null) {
            final boolean needClear = !builder.hasFeatureLockModel();
            final int tmpFieldCnt = this.featureLockModel.copyChangeToAndClearDeleteKeysCs(builder.getFeatureLockModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFeatureLockModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPASSMODEL) && this.battlePassModel != null) {
            final boolean needClear = !builder.hasBattlePassModel();
            final int tmpFieldCnt = this.battlePassModel.copyChangeToAndClearDeleteKeysCs(builder.getBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYSNEW) && this.additionSysNew != null) {
            final boolean needClear = !builder.hasAdditionSysNew();
            final int tmpFieldCnt = this.additionSysNew.copyChangeToAndClearDeleteKeysCs(builder.getAdditionSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKYNETMODEL) && this.skynetModel != null) {
            final boolean needClear = !builder.hasSkynetModel();
            final int tmpFieldCnt = this.skynetModel.copyChangeToAndClearDeleteKeysCs(builder.getSkynetModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkynetModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONBATTLEPASSMODEL) && this.seasonBattlePassModel != null) {
            final boolean needClear = !builder.hasSeasonBattlePassModel();
            final int tmpFieldCnt = this.seasonBattlePassModel.copyChangeToAndClearDeleteKeysCs(builder.getSeasonBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINNERBUILDRHMODEL) && this.playerInnerBuildRHModel != null) {
            final boolean needClear = !builder.hasPlayerInnerBuildRHModel();
            final int tmpFieldCnt = this.playerInnerBuildRHModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerInnerBuildRHModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInnerBuildRHModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMPAIGNMODEL) && this.playerCampaignModel != null) {
            final boolean needClear = !builder.hasPlayerCampaignModel();
            final int tmpFieldCnt = this.playerCampaignModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerCampaignModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCampaignModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMODEL) && this.playerUnitModel != null) {
            final boolean needClear = !builder.hasPlayerUnitModel();
            final int tmpFieldCnt = this.playerUnitModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerUnitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERMISSIONMODEL) && this.playerMissionModel != null) {
            final boolean needClear = !builder.hasPlayerMissionModel();
            final int tmpFieldCnt = this.playerMissionModel.copyChangeToAndClearDeleteKeysCs(builder.getPlayerMissionModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerMissionModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOpenId()) {
            this.innerSetOpenId(proto.getOpenId());
        } else {
            this.innerSetOpenId(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasScene()) {
            this.getScene().mergeFromCs(proto.getScene());
        } else {
            if (this.scene != null) {
                this.scene.mergeFromCs(proto.getScene());
            }
        }
        if (proto.hasScenePlayer()) {
            this.getScenePlayer().mergeFromCs(proto.getScenePlayer());
        } else {
            if (this.scenePlayer != null) {
                this.scenePlayer.mergeFromCs(proto.getScenePlayer());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClan()) {
            this.getClan().mergeFromCs(proto.getClan());
        } else {
            if (this.clan != null) {
                this.clan.mergeFromCs(proto.getClan());
            }
        }
        if (proto.hasFragments()) {
            this.getFragments().mergeFromCs(proto.getFragments());
        } else {
            if (this.fragments != null) {
                this.fragments.mergeFromCs(proto.getFragments());
            }
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromCs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromCs(proto.getItems());
            }
        }
        if (proto.hasPurse()) {
            this.getPurse().mergeFromCs(proto.getPurse());
        } else {
            if (this.purse != null) {
                this.purse.mergeFromCs(proto.getPurse());
            }
        }
        if (proto.hasQueueTaskMap()) {
            this.getQueueTaskMap().mergeFromCs(proto.getQueueTaskMap());
        } else {
            if (this.queueTaskMap != null) {
                this.queueTaskMap.mergeFromCs(proto.getQueueTaskMap());
            }
        }
        if (proto.hasPlayerSoldierInBuilding()) {
            this.getPlayerSoldierInBuilding().mergeFromCs(proto.getPlayerSoldierInBuilding());
        } else {
            if (this.playerSoldierInBuilding != null) {
                this.playerSoldierInBuilding.mergeFromCs(proto.getPlayerSoldierInBuilding());
            }
        }
        if (proto.hasPlayerPowerInfo()) {
            this.getPlayerPowerInfo().mergeFromCs(proto.getPlayerPowerInfo());
        } else {
            if (this.playerPowerInfo != null) {
                this.playerPowerInfo.mergeFromCs(proto.getPlayerPowerInfo());
            }
        }
        if (proto.hasPlayerDataRecord()) {
            this.getPlayerDataRecord().mergeFromCs(proto.getPlayerDataRecord());
        } else {
            if (this.playerDataRecord != null) {
                this.playerDataRecord.mergeFromCs(proto.getPlayerDataRecord());
            }
        }
        if (proto.hasBasicInfo()) {
            this.getBasicInfo().mergeFromCs(proto.getBasicInfo());
        } else {
            if (this.basicInfo != null) {
                this.basicInfo.mergeFromCs(proto.getBasicInfo());
            }
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeFromCs(proto.getFormation());
        } else {
            if (this.formation != null) {
                this.formation.mergeFromCs(proto.getFormation());
            }
        }
        if (proto.hasResourceProduce()) {
            this.getResourceProduce().mergeFromCs(proto.getResourceProduce());
        } else {
            if (this.resourceProduce != null) {
                this.resourceProduce.mergeFromCs(proto.getResourceProduce());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromCs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromCs(proto.getAdditionSys());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromCs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromCs(proto.getDevBuffSys());
            }
        }
        if (proto.hasRefreshSys()) {
            this.getRefreshSys().mergeFromCs(proto.getRefreshSys());
        } else {
            if (this.refreshSys != null) {
                this.refreshSys.mergeFromCs(proto.getRefreshSys());
            }
        }
        if (proto.hasTaskSystem()) {
            this.getTaskSystem().mergeFromCs(proto.getTaskSystem());
        } else {
            if (this.taskSystem != null) {
                this.taskSystem.mergeFromCs(proto.getTaskSystem());
            }
        }
        if (proto.hasDiscountInfo()) {
            this.getDiscountInfo().mergeFromCs(proto.getDiscountInfo());
        } else {
            if (this.discountInfo != null) {
                this.discountInfo.mergeFromCs(proto.getDiscountInfo());
            }
        }
        if (proto.hasPlayerGuidanceModel()) {
            this.getPlayerGuidanceModel().mergeFromCs(proto.getPlayerGuidanceModel());
        } else {
            if (this.PlayerGuidanceModel != null) {
                this.PlayerGuidanceModel.mergeFromCs(proto.getPlayerGuidanceModel());
            }
        }
        if (proto.hasPlayerTechModel()) {
            this.getPlayerTechModel().mergeFromCs(proto.getPlayerTechModel());
        } else {
            if (this.PlayerTechModel != null) {
                this.PlayerTechModel.mergeFromCs(proto.getPlayerTechModel());
            }
        }
        if (proto.hasPlayerPlaneModel()) {
            this.getPlayerPlaneModel().mergeFromCs(proto.getPlayerPlaneModel());
        } else {
            if (this.playerPlaneModel != null) {
                this.playerPlaneModel.mergeFromCs(proto.getPlayerPlaneModel());
            }
        }
        if (proto.hasChatPlayer()) {
            this.getChatPlayer().mergeFromCs(proto.getChatPlayer());
        } else {
            if (this.ChatPlayer != null) {
                this.ChatPlayer.mergeFromCs(proto.getChatPlayer());
            }
        }
        if (proto.hasNewbieModel()) {
            this.getNewbieModel().mergeFromCs(proto.getNewbieModel());
        } else {
            if (this.newbieModel != null) {
                this.newbieModel.mergeFromCs(proto.getNewbieModel());
            }
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeFromCs(proto.getRedDotModel());
        } else {
            if (this.redDotModel != null) {
                this.redDotModel.mergeFromCs(proto.getRedDotModel());
            }
        }
        if (proto.hasPlayerHeroModel()) {
            this.getPlayerHeroModel().mergeFromCs(proto.getPlayerHeroModel());
        } else {
            if (this.playerHeroModel != null) {
                this.playerHeroModel.mergeFromCs(proto.getPlayerHeroModel());
            }
        }
        if (proto.hasAvatarModel()) {
            this.getAvatarModel().mergeFromCs(proto.getAvatarModel());
        } else {
            if (this.avatarModel != null) {
                this.avatarModel.mergeFromCs(proto.getAvatarModel());
            }
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeFromCs(proto.getZoneModel());
        } else {
            if (this.zoneModel != null) {
                this.zoneModel.mergeFromCs(proto.getZoneModel());
            }
        }
        if (proto.hasEnergyModel()) {
            this.getEnergyModel().mergeFromCs(proto.getEnergyModel());
        } else {
            if (this.energyModel != null) {
                this.energyModel.mergeFromCs(proto.getEnergyModel());
            }
        }
        if (proto.hasPlayerRecruitModel()) {
            this.getPlayerRecruitModel().mergeFromCs(proto.getPlayerRecruitModel());
        } else {
            if (this.playerRecruitModel != null) {
                this.playerRecruitModel.mergeFromCs(proto.getPlayerRecruitModel());
            }
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeFromCs(proto.getActivityModel());
        } else {
            if (this.activityModel != null) {
                this.activityModel.mergeFromCs(proto.getActivityModel());
            }
        }
        if (proto.hasMailModel()) {
            this.getMailModel().mergeFromCs(proto.getMailModel());
        } else {
            if (this.mailModel != null) {
                this.mailModel.mergeFromCs(proto.getMailModel());
            }
        }
        if (proto.hasDungeonPlayer()) {
            this.getDungeonPlayer().mergeFromCs(proto.getDungeonPlayer());
        } else {
            if (this.dungeonPlayer != null) {
                this.dungeonPlayer.mergeFromCs(proto.getDungeonPlayer());
            }
        }
        if (proto.hasKillMonsterModel()) {
            this.getKillMonsterModel().mergeFromCs(proto.getKillMonsterModel());
        } else {
            if (this.killMonsterModel != null) {
                this.killMonsterModel.mergeFromCs(proto.getKillMonsterModel());
            }
        }
        if (proto.hasMileStoneModel()) {
            this.getMileStoneModel().mergeFromCs(proto.getMileStoneModel());
        } else {
            if (this.mileStoneModel != null) {
                this.mileStoneModel.mergeFromCs(proto.getMileStoneModel());
            }
        }
        if (proto.hasDungeonModel()) {
            this.getDungeonModel().mergeFromCs(proto.getDungeonModel());
        } else {
            if (this.dungeonModel != null) {
                this.dungeonModel.mergeFromCs(proto.getDungeonModel());
            }
        }
        if (proto.hasResetInfo()) {
            this.getResetInfo().mergeFromCs(proto.getResetInfo());
        } else {
            if (this.resetInfo != null) {
                this.resetInfo.mergeFromCs(proto.getResetInfo());
            }
        }
        if (proto.hasSettingModel()) {
            this.getSettingModel().mergeFromCs(proto.getSettingModel());
        } else {
            if (this.settingModel != null) {
                this.settingModel.mergeFromCs(proto.getSettingModel());
            }
        }
        if (proto.hasPaymentModel()) {
            this.getPaymentModel().mergeFromCs(proto.getPaymentModel());
        } else {
            if (this.paymentModel != null) {
                this.paymentModel.mergeFromCs(proto.getPaymentModel());
            }
        }
        if (proto.hasFriendPlayer()) {
            this.getFriendPlayer().mergeFromCs(proto.getFriendPlayer());
        } else {
            if (this.friendPlayer != null) {
                this.friendPlayer.mergeFromCs(proto.getFriendPlayer());
            }
        }
        if (proto.hasVipModel()) {
            this.getVipModel().mergeFromCs(proto.getVipModel());
        } else {
            if (this.vipModel != null) {
                this.vipModel.mergeFromCs(proto.getVipModel());
            }
        }
        if (proto.hasQuestModel()) {
            this.getQuestModel().mergeFromCs(proto.getQuestModel());
        } else {
            if (this.questModel != null) {
                this.questModel.mergeFromCs(proto.getQuestModel());
            }
        }
        if (proto.hasCityModel()) {
            this.getCityModel().mergeFromCs(proto.getCityModel());
        } else {
            if (this.cityModel != null) {
                this.cityModel.mergeFromCs(proto.getCityModel());
            }
        }
        if (proto.hasDailyGoodsModel()) {
            this.getDailyGoodsModel().mergeFromCs(proto.getDailyGoodsModel());
        } else {
            if (this.dailyGoodsModel != null) {
                this.dailyGoodsModel.mergeFromCs(proto.getDailyGoodsModel());
            }
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeFromCs(proto.getDevBuffSysNew());
        } else {
            if (this.devBuffSysNew != null) {
                this.devBuffSysNew.mergeFromCs(proto.getDevBuffSysNew());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromCs(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromCs(proto.getKingdomModel());
            }
        }
        if (proto.hasTriggerBundleModel()) {
            this.getTriggerBundleModel().mergeFromCs(proto.getTriggerBundleModel());
        } else {
            if (this.triggerBundleModel != null) {
                this.triggerBundleModel.mergeFromCs(proto.getTriggerBundleModel());
            }
        }
        if (proto.hasFeatureLockModel()) {
            this.getFeatureLockModel().mergeFromCs(proto.getFeatureLockModel());
        } else {
            if (this.featureLockModel != null) {
                this.featureLockModel.mergeFromCs(proto.getFeatureLockModel());
            }
        }
        if (proto.hasBattlePassModel()) {
            this.getBattlePassModel().mergeFromCs(proto.getBattlePassModel());
        } else {
            if (this.battlePassModel != null) {
                this.battlePassModel.mergeFromCs(proto.getBattlePassModel());
            }
        }
        if (proto.hasAdditionSysNew()) {
            this.getAdditionSysNew().mergeFromCs(proto.getAdditionSysNew());
        } else {
            if (this.additionSysNew != null) {
                this.additionSysNew.mergeFromCs(proto.getAdditionSysNew());
            }
        }
        if (proto.hasSkynetModel()) {
            this.getSkynetModel().mergeFromCs(proto.getSkynetModel());
        } else {
            if (this.skynetModel != null) {
                this.skynetModel.mergeFromCs(proto.getSkynetModel());
            }
        }
        if (proto.hasSeasonBattlePassModel()) {
            this.getSeasonBattlePassModel().mergeFromCs(proto.getSeasonBattlePassModel());
        } else {
            if (this.seasonBattlePassModel != null) {
                this.seasonBattlePassModel.mergeFromCs(proto.getSeasonBattlePassModel());
            }
        }
        if (proto.hasPlayerInnerBuildRHModel()) {
            this.getPlayerInnerBuildRHModel().mergeFromCs(proto.getPlayerInnerBuildRHModel());
        } else {
            if (this.playerInnerBuildRHModel != null) {
                this.playerInnerBuildRHModel.mergeFromCs(proto.getPlayerInnerBuildRHModel());
            }
        }
        if (proto.hasPlayerCampaignModel()) {
            this.getPlayerCampaignModel().mergeFromCs(proto.getPlayerCampaignModel());
        } else {
            if (this.playerCampaignModel != null) {
                this.playerCampaignModel.mergeFromCs(proto.getPlayerCampaignModel());
            }
        }
        if (proto.hasPlayerUnitModel()) {
            this.getPlayerUnitModel().mergeFromCs(proto.getPlayerUnitModel());
        } else {
            if (this.playerUnitModel != null) {
                this.playerUnitModel.mergeFromCs(proto.getPlayerUnitModel());
            }
        }
        if (proto.hasPlayerMissionModel()) {
            this.getPlayerMissionModel().mergeFromCs(proto.getPlayerMissionModel());
        } else {
            if (this.playerMissionModel != null) {
                this.playerMissionModel.mergeFromCs(proto.getPlayerMissionModel());
            }
        }
        this.markAll();
        return PlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasOpenId()) {
            this.setOpenId(proto.getOpenId());
            fieldCnt++;
        }
        if (proto.hasScene()) {
            this.getScene().mergeChangeFromCs(proto.getScene());
            fieldCnt++;
        }
        if (proto.hasScenePlayer()) {
            this.getScenePlayer().mergeChangeFromCs(proto.getScenePlayer());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasClan()) {
            this.getClan().mergeChangeFromCs(proto.getClan());
            fieldCnt++;
        }
        if (proto.hasFragments()) {
            this.getFragments().mergeChangeFromCs(proto.getFragments());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromCs(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasPurse()) {
            this.getPurse().mergeChangeFromCs(proto.getPurse());
            fieldCnt++;
        }
        if (proto.hasQueueTaskMap()) {
            this.getQueueTaskMap().mergeChangeFromCs(proto.getQueueTaskMap());
            fieldCnt++;
        }
        if (proto.hasPlayerSoldierInBuilding()) {
            this.getPlayerSoldierInBuilding().mergeChangeFromCs(proto.getPlayerSoldierInBuilding());
            fieldCnt++;
        }
        if (proto.hasPlayerPowerInfo()) {
            this.getPlayerPowerInfo().mergeChangeFromCs(proto.getPlayerPowerInfo());
            fieldCnt++;
        }
        if (proto.hasPlayerDataRecord()) {
            this.getPlayerDataRecord().mergeChangeFromCs(proto.getPlayerDataRecord());
            fieldCnt++;
        }
        if (proto.hasBasicInfo()) {
            this.getBasicInfo().mergeChangeFromCs(proto.getBasicInfo());
            fieldCnt++;
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeChangeFromCs(proto.getFormation());
            fieldCnt++;
        }
        if (proto.hasResourceProduce()) {
            this.getResourceProduce().mergeChangeFromCs(proto.getResourceProduce());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromCs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromCs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasRefreshSys()) {
            this.getRefreshSys().mergeChangeFromCs(proto.getRefreshSys());
            fieldCnt++;
        }
        if (proto.hasTaskSystem()) {
            this.getTaskSystem().mergeChangeFromCs(proto.getTaskSystem());
            fieldCnt++;
        }
        if (proto.hasDiscountInfo()) {
            this.getDiscountInfo().mergeChangeFromCs(proto.getDiscountInfo());
            fieldCnt++;
        }
        if (proto.hasPlayerGuidanceModel()) {
            this.getPlayerGuidanceModel().mergeChangeFromCs(proto.getPlayerGuidanceModel());
            fieldCnt++;
        }
        if (proto.hasPlayerTechModel()) {
            this.getPlayerTechModel().mergeChangeFromCs(proto.getPlayerTechModel());
            fieldCnt++;
        }
        if (proto.hasPlayerPlaneModel()) {
            this.getPlayerPlaneModel().mergeChangeFromCs(proto.getPlayerPlaneModel());
            fieldCnt++;
        }
        if (proto.hasChatPlayer()) {
            this.getChatPlayer().mergeChangeFromCs(proto.getChatPlayer());
            fieldCnt++;
        }
        if (proto.hasNewbieModel()) {
            this.getNewbieModel().mergeChangeFromCs(proto.getNewbieModel());
            fieldCnt++;
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeChangeFromCs(proto.getRedDotModel());
            fieldCnt++;
        }
        if (proto.hasPlayerHeroModel()) {
            this.getPlayerHeroModel().mergeChangeFromCs(proto.getPlayerHeroModel());
            fieldCnt++;
        }
        if (proto.hasAvatarModel()) {
            this.getAvatarModel().mergeChangeFromCs(proto.getAvatarModel());
            fieldCnt++;
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeChangeFromCs(proto.getZoneModel());
            fieldCnt++;
        }
        if (proto.hasEnergyModel()) {
            this.getEnergyModel().mergeChangeFromCs(proto.getEnergyModel());
            fieldCnt++;
        }
        if (proto.hasPlayerRecruitModel()) {
            this.getPlayerRecruitModel().mergeChangeFromCs(proto.getPlayerRecruitModel());
            fieldCnt++;
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeChangeFromCs(proto.getActivityModel());
            fieldCnt++;
        }
        if (proto.hasMailModel()) {
            this.getMailModel().mergeChangeFromCs(proto.getMailModel());
            fieldCnt++;
        }
        if (proto.hasDungeonPlayer()) {
            this.getDungeonPlayer().mergeChangeFromCs(proto.getDungeonPlayer());
            fieldCnt++;
        }
        if (proto.hasKillMonsterModel()) {
            this.getKillMonsterModel().mergeChangeFromCs(proto.getKillMonsterModel());
            fieldCnt++;
        }
        if (proto.hasMileStoneModel()) {
            this.getMileStoneModel().mergeChangeFromCs(proto.getMileStoneModel());
            fieldCnt++;
        }
        if (proto.hasDungeonModel()) {
            this.getDungeonModel().mergeChangeFromCs(proto.getDungeonModel());
            fieldCnt++;
        }
        if (proto.hasResetInfo()) {
            this.getResetInfo().mergeChangeFromCs(proto.getResetInfo());
            fieldCnt++;
        }
        if (proto.hasSettingModel()) {
            this.getSettingModel().mergeChangeFromCs(proto.getSettingModel());
            fieldCnt++;
        }
        if (proto.hasPaymentModel()) {
            this.getPaymentModel().mergeChangeFromCs(proto.getPaymentModel());
            fieldCnt++;
        }
        if (proto.hasFriendPlayer()) {
            this.getFriendPlayer().mergeChangeFromCs(proto.getFriendPlayer());
            fieldCnt++;
        }
        if (proto.hasVipModel()) {
            this.getVipModel().mergeChangeFromCs(proto.getVipModel());
            fieldCnt++;
        }
        if (proto.hasQuestModel()) {
            this.getQuestModel().mergeChangeFromCs(proto.getQuestModel());
            fieldCnt++;
        }
        if (proto.hasCityModel()) {
            this.getCityModel().mergeChangeFromCs(proto.getCityModel());
            fieldCnt++;
        }
        if (proto.hasDailyGoodsModel()) {
            this.getDailyGoodsModel().mergeChangeFromCs(proto.getDailyGoodsModel());
            fieldCnt++;
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeChangeFromCs(proto.getDevBuffSysNew());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromCs(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleModel()) {
            this.getTriggerBundleModel().mergeChangeFromCs(proto.getTriggerBundleModel());
            fieldCnt++;
        }
        if (proto.hasFeatureLockModel()) {
            this.getFeatureLockModel().mergeChangeFromCs(proto.getFeatureLockModel());
            fieldCnt++;
        }
        if (proto.hasBattlePassModel()) {
            this.getBattlePassModel().mergeChangeFromCs(proto.getBattlePassModel());
            fieldCnt++;
        }
        if (proto.hasAdditionSysNew()) {
            this.getAdditionSysNew().mergeChangeFromCs(proto.getAdditionSysNew());
            fieldCnt++;
        }
        if (proto.hasSkynetModel()) {
            this.getSkynetModel().mergeChangeFromCs(proto.getSkynetModel());
            fieldCnt++;
        }
        if (proto.hasSeasonBattlePassModel()) {
            this.getSeasonBattlePassModel().mergeChangeFromCs(proto.getSeasonBattlePassModel());
            fieldCnt++;
        }
        if (proto.hasPlayerInnerBuildRHModel()) {
            this.getPlayerInnerBuildRHModel().mergeChangeFromCs(proto.getPlayerInnerBuildRHModel());
            fieldCnt++;
        }
        if (proto.hasPlayerCampaignModel()) {
            this.getPlayerCampaignModel().mergeChangeFromCs(proto.getPlayerCampaignModel());
            fieldCnt++;
        }
        if (proto.hasPlayerUnitModel()) {
            this.getPlayerUnitModel().mergeChangeFromCs(proto.getPlayerUnitModel());
            fieldCnt++;
        }
        if (proto.hasPlayerMissionModel()) {
            this.getPlayerMissionModel().mergeChangeFromCs(proto.getPlayerMissionModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerEntity.Builder getCopyDbBuilder() {
        final PlayerEntity.Builder builder = PlayerEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (!this.getOpenId().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOpenId(this.getOpenId());
            fieldCnt++;
        }  else if (builder.hasOpenId()) {
            // 清理OpenId
            builder.clearOpenId();
            fieldCnt++;
        }
        if (this.scene != null) {
            Player.PlayerScene.Builder tmpBuilder = Player.PlayerScene.newBuilder();
            final int tmpFieldCnt = this.scene.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScene(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScene();
            }
        }  else if (builder.hasScene()) {
            // 清理Scene
            builder.clearScene();
            fieldCnt++;
        }
        if (this.scenePlayer != null) {
            Player.ScenePlayer.Builder tmpBuilder = Player.ScenePlayer.newBuilder();
            final int tmpFieldCnt = this.scenePlayer.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScenePlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScenePlayer();
            }
        }  else if (builder.hasScenePlayer()) {
            // 清理ScenePlayer
            builder.clearScenePlayer();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.clan != null) {
            Player.ClanInfo.Builder tmpBuilder = Player.ClanInfo.newBuilder();
            final int tmpFieldCnt = this.clan.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClan(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClan();
            }
        }  else if (builder.hasClan()) {
            // 清理Clan
            builder.clearClan();
            fieldCnt++;
        }
        if (this.fragments != null) {
            Player.HistoryFragments.Builder tmpBuilder = Player.HistoryFragments.newBuilder();
            final int tmpFieldCnt = this.fragments.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFragments(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFragments();
            }
        }  else if (builder.hasFragments()) {
            // 清理Fragments
            builder.clearFragments();
            fieldCnt++;
        }
        if (this.items != null) {
            Struct.Int64ItemMap.Builder tmpBuilder = Struct.Int64ItemMap.newBuilder();
            final int tmpFieldCnt = this.items.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.purse != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.purse.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPurse(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPurse();
            }
        }  else if (builder.hasPurse()) {
            // 清理Purse
            builder.clearPurse();
            fieldCnt++;
        }
        if (this.queueTaskMap != null) {
            StructPlayer.Int32QueueTasksMap.Builder tmpBuilder = StructPlayer.Int32QueueTasksMap.newBuilder();
            final int tmpFieldCnt = this.queueTaskMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQueueTaskMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQueueTaskMap();
            }
        }  else if (builder.hasQueueTaskMap()) {
            // 清理QueueTaskMap
            builder.clearQueueTaskMap();
            fieldCnt++;
        }
        if (this.playerSoldierInBuilding != null) {
            Struct.PlayerSoldierInBuilding.Builder tmpBuilder = Struct.PlayerSoldierInBuilding.newBuilder();
            final int tmpFieldCnt = this.playerSoldierInBuilding.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerSoldierInBuilding(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerSoldierInBuilding();
            }
        }  else if (builder.hasPlayerSoldierInBuilding()) {
            // 清理PlayerSoldierInBuilding
            builder.clearPlayerSoldierInBuilding();
            fieldCnt++;
        }
        if (this.playerPowerInfo != null) {
            Struct.PlayerPowerInfo.Builder tmpBuilder = Struct.PlayerPowerInfo.newBuilder();
            final int tmpFieldCnt = this.playerPowerInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerPowerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerPowerInfo();
            }
        }  else if (builder.hasPlayerPowerInfo()) {
            // 清理PlayerPowerInfo
            builder.clearPlayerPowerInfo();
            fieldCnt++;
        }
        if (this.playerDataRecord != null) {
            Struct.PlayerDataRecord.Builder tmpBuilder = Struct.PlayerDataRecord.newBuilder();
            final int tmpFieldCnt = this.playerDataRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerDataRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerDataRecord();
            }
        }  else if (builder.hasPlayerDataRecord()) {
            // 清理PlayerDataRecord
            builder.clearPlayerDataRecord();
            fieldCnt++;
        }
        if (this.basicInfo != null) {
            Player.PlayerBasicInfo.Builder tmpBuilder = Player.PlayerBasicInfo.newBuilder();
            final int tmpFieldCnt = this.basicInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBasicInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBasicInfo();
            }
        }  else if (builder.hasBasicInfo()) {
            // 清理BasicInfo
            builder.clearBasicInfo();
            fieldCnt++;
        }
        if (this.formation != null) {
            StructPlayer.PlayerFormation.Builder tmpBuilder = StructPlayer.PlayerFormation.newBuilder();
            final int tmpFieldCnt = this.formation.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormation();
            }
        }  else if (builder.hasFormation()) {
            // 清理Formation
            builder.clearFormation();
            fieldCnt++;
        }
        if (this.resourceProduce != null) {
            Player.PlayerResourceProduceMap.Builder tmpBuilder = Player.PlayerResourceProduceMap.newBuilder();
            final int tmpFieldCnt = this.resourceProduce.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResourceProduce(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResourceProduce();
            }
        }  else if (builder.hasResourceProduce()) {
            // 清理ResourceProduce
            builder.clearResourceProduce();
            fieldCnt++;
        }
        if (this.refreshSys != null) {
            Player.RefreshInfo.Builder tmpBuilder = Player.RefreshInfo.newBuilder();
            final int tmpFieldCnt = this.refreshSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshSys();
            }
        }  else if (builder.hasRefreshSys()) {
            // 清理RefreshSys
            builder.clearRefreshSys();
            fieldCnt++;
        }
        if (this.taskSystem != null) {
            Player.TaskSystem.Builder tmpBuilder = Player.TaskSystem.newBuilder();
            final int tmpFieldCnt = this.taskSystem.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskSystem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskSystem();
            }
        }  else if (builder.hasTaskSystem()) {
            // 清理TaskSystem
            builder.clearTaskSystem();
            fieldCnt++;
        }
        if (this.discountInfo != null) {
            Player.DiscountInfo.Builder tmpBuilder = Player.DiscountInfo.newBuilder();
            final int tmpFieldCnt = this.discountInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDiscountInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDiscountInfo();
            }
        }  else if (builder.hasDiscountInfo()) {
            // 清理DiscountInfo
            builder.clearDiscountInfo();
            fieldCnt++;
        }
        if (this.playerStoreModel != null) {
            Player.PlayerStoreModel.Builder tmpBuilder = Player.PlayerStoreModel.newBuilder();
            final int tmpFieldCnt = this.playerStoreModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerStoreModel();
            }
        }  else if (builder.hasPlayerStoreModel()) {
            // 清理PlayerStoreModel
            builder.clearPlayerStoreModel();
            fieldCnt++;
        }
        if (this.PlayerGuidanceModel != null) {
            Player.PlayerGuidanceModel.Builder tmpBuilder = Player.PlayerGuidanceModel.newBuilder();
            final int tmpFieldCnt = this.PlayerGuidanceModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerGuidanceModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerGuidanceModel();
            }
        }  else if (builder.hasPlayerGuidanceModel()) {
            // 清理PlayerGuidanceModel
            builder.clearPlayerGuidanceModel();
            fieldCnt++;
        }
        if (this.PlayerTechModel != null) {
            Player.PlayerTechnologyModel.Builder tmpBuilder = Player.PlayerTechnologyModel.newBuilder();
            final int tmpFieldCnt = this.PlayerTechModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerTechModel();
            }
        }  else if (builder.hasPlayerTechModel()) {
            // 清理PlayerTechModel
            builder.clearPlayerTechModel();
            fieldCnt++;
        }
        if (this.playerPlaneModel != null) {
            Player.PlayerPlaneModel.Builder tmpBuilder = Player.PlayerPlaneModel.newBuilder();
            final int tmpFieldCnt = this.playerPlaneModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerPlaneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerPlaneModel();
            }
        }  else if (builder.hasPlayerPlaneModel()) {
            // 清理PlayerPlaneModel
            builder.clearPlayerPlaneModel();
            fieldCnt++;
        }
        if (this.newbieModel != null) {
            Player.PlayerNewbieModel.Builder tmpBuilder = Player.PlayerNewbieModel.newBuilder();
            final int tmpFieldCnt = this.newbieModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNewbieModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNewbieModel();
            }
        }  else if (builder.hasNewbieModel()) {
            // 清理NewbieModel
            builder.clearNewbieModel();
            fieldCnt++;
        }
        if (this.statisticModel != null) {
            Player.PlayerStatisticModel.Builder tmpBuilder = Player.PlayerStatisticModel.newBuilder();
            final int tmpFieldCnt = this.statisticModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStatisticModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStatisticModel();
            }
        }  else if (builder.hasStatisticModel()) {
            // 清理StatisticModel
            builder.clearStatisticModel();
            fieldCnt++;
        }
        if (this.redDotModel != null) {
            Player.PlayerRedDotModel.Builder tmpBuilder = Player.PlayerRedDotModel.newBuilder();
            final int tmpFieldCnt = this.redDotModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotModel();
            }
        }  else if (builder.hasRedDotModel()) {
            // 清理RedDotModel
            builder.clearRedDotModel();
            fieldCnt++;
        }
        if (this.playerHeroModel != null) {
            Player.PlayerHeroModel.Builder tmpBuilder = Player.PlayerHeroModel.newBuilder();
            final int tmpFieldCnt = this.playerHeroModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerHeroModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerHeroModel();
            }
        }  else if (builder.hasPlayerHeroModel()) {
            // 清理PlayerHeroModel
            builder.clearPlayerHeroModel();
            fieldCnt++;
        }
        if (this.avatarModel != null) {
            Player.PlayerAvatarModel.Builder tmpBuilder = Player.PlayerAvatarModel.newBuilder();
            final int tmpFieldCnt = this.avatarModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAvatarModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAvatarModel();
            }
        }  else if (builder.hasAvatarModel()) {
            // 清理AvatarModel
            builder.clearAvatarModel();
            fieldCnt++;
        }
        if (this.zoneModel != null) {
            Player.PlayerZoneModel.Builder tmpBuilder = Player.PlayerZoneModel.newBuilder();
            final int tmpFieldCnt = this.zoneModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneModel();
            }
        }  else if (builder.hasZoneModel()) {
            // 清理ZoneModel
            builder.clearZoneModel();
            fieldCnt++;
        }
        if (this.energyModel != null) {
            Player.PlayerEnergyModel.Builder tmpBuilder = Player.PlayerEnergyModel.newBuilder();
            final int tmpFieldCnt = this.energyModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEnergyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEnergyModel();
            }
        }  else if (builder.hasEnergyModel()) {
            // 清理EnergyModel
            builder.clearEnergyModel();
            fieldCnt++;
        }
        if (this.playerRecruitModel != null) {
            Player.PlayerRecruitModel.Builder tmpBuilder = Player.PlayerRecruitModel.newBuilder();
            final int tmpFieldCnt = this.playerRecruitModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerRecruitModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerRecruitModel();
            }
        }  else if (builder.hasPlayerRecruitModel()) {
            // 清理PlayerRecruitModel
            builder.clearPlayerRecruitModel();
            fieldCnt++;
        }
        if (this.activityModel != null) {
            Player.PlayerActivityModel.Builder tmpBuilder = Player.PlayerActivityModel.newBuilder();
            final int tmpFieldCnt = this.activityModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityModel();
            }
        }  else if (builder.hasActivityModel()) {
            // 清理ActivityModel
            builder.clearActivityModel();
            fieldCnt++;
        }
        if (this.mailModel != null) {
            Player.PlayerMailModel.Builder tmpBuilder = Player.PlayerMailModel.newBuilder();
            final int tmpFieldCnt = this.mailModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMailModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMailModel();
            }
        }  else if (builder.hasMailModel()) {
            // 清理MailModel
            builder.clearMailModel();
            fieldCnt++;
        }
        if (this.killMonsterModel != null) {
            Player.PlayerKillMonsterModel.Builder tmpBuilder = Player.PlayerKillMonsterModel.newBuilder();
            final int tmpFieldCnt = this.killMonsterModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKillMonsterModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKillMonsterModel();
            }
        }  else if (builder.hasKillMonsterModel()) {
            // 清理KillMonsterModel
            builder.clearKillMonsterModel();
            fieldCnt++;
        }
        if (this.mileStoneModel != null) {
            Player.PlayerMileStoneModel.Builder tmpBuilder = Player.PlayerMileStoneModel.newBuilder();
            final int tmpFieldCnt = this.mileStoneModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneModel();
            }
        }  else if (builder.hasMileStoneModel()) {
            // 清理MileStoneModel
            builder.clearMileStoneModel();
            fieldCnt++;
        }
        if (this.dungeonModel != null) {
            Player.PlayerDungeonModel.Builder tmpBuilder = Player.PlayerDungeonModel.newBuilder();
            final int tmpFieldCnt = this.dungeonModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonModel();
            }
        }  else if (builder.hasDungeonModel()) {
            // 清理DungeonModel
            builder.clearDungeonModel();
            fieldCnt++;
        }
        if (this.resetInfo != null) {
            Player.PlayerResetInfo.Builder tmpBuilder = Player.PlayerResetInfo.newBuilder();
            final int tmpFieldCnt = this.resetInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResetInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResetInfo();
            }
        }  else if (builder.hasResetInfo()) {
            // 清理ResetInfo
            builder.clearResetInfo();
            fieldCnt++;
        }
        if (this.settingModel != null) {
            Player.PlayerSettingModel.Builder tmpBuilder = Player.PlayerSettingModel.newBuilder();
            final int tmpFieldCnt = this.settingModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSettingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSettingModel();
            }
        }  else if (builder.hasSettingModel()) {
            // 清理SettingModel
            builder.clearSettingModel();
            fieldCnt++;
        }
        if (this.paymentModel != null) {
            Player.PlayerPaymentModel.Builder tmpBuilder = Player.PlayerPaymentModel.newBuilder();
            final int tmpFieldCnt = this.paymentModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPaymentModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPaymentModel();
            }
        }  else if (builder.hasPaymentModel()) {
            // 清理PaymentModel
            builder.clearPaymentModel();
            fieldCnt++;
        }
        if (this.vipModel != null) {
            Player.PlayerVipModel.Builder tmpBuilder = Player.PlayerVipModel.newBuilder();
            final int tmpFieldCnt = this.vipModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipModel();
            }
        }  else if (builder.hasVipModel()) {
            // 清理VipModel
            builder.clearVipModel();
            fieldCnt++;
        }
        if (this.questModel != null) {
            Player.PlayerInnerQuestModel.Builder tmpBuilder = Player.PlayerInnerQuestModel.newBuilder();
            final int tmpFieldCnt = this.questModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQuestModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQuestModel();
            }
        }  else if (builder.hasQuestModel()) {
            // 清理QuestModel
            builder.clearQuestModel();
            fieldCnt++;
        }
        if (this.cityModel != null) {
            Player.PlayerCityModel.Builder tmpBuilder = Player.PlayerCityModel.newBuilder();
            final int tmpFieldCnt = this.cityModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityModel();
            }
        }  else if (builder.hasCityModel()) {
            // 清理CityModel
            builder.clearCityModel();
            fieldCnt++;
        }
        if (this.dailyGoodsModel != null) {
            Player.PlayerDailyGoodsModel.Builder tmpBuilder = Player.PlayerDailyGoodsModel.newBuilder();
            final int tmpFieldCnt = this.dailyGoodsModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyGoodsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyGoodsModel();
            }
        }  else if (builder.hasDailyGoodsModel()) {
            // 清理DailyGoodsModel
            builder.clearDailyGoodsModel();
            fieldCnt++;
        }
        if (this.drawInfo != null) {
            Struct.Int32DrawInfoMap.Builder tmpBuilder = Struct.Int32DrawInfoMap.newBuilder();
            final int tmpFieldCnt = this.drawInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDrawInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDrawInfo();
            }
        }  else if (builder.hasDrawInfo()) {
            // 清理DrawInfo
            builder.clearDrawInfo();
            fieldCnt++;
        }
        if (this.devBuffSysNew != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSysNew.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSysNew();
            }
        }  else if (builder.hasDevBuffSysNew()) {
            // 清理DevBuffSysNew
            builder.clearDevBuffSysNew();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            Player.PlayerKingdomModel.Builder tmpBuilder = Player.PlayerKingdomModel.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.zoneAdditionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.zoneAdditionSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneAdditionSys();
            }
        }  else if (builder.hasZoneAdditionSys()) {
            // 清理ZoneAdditionSys
            builder.clearZoneAdditionSys();
            fieldCnt++;
        }
        if (this.triggerBundleModel != null) {
            Player.PlayerTriggerBundleModel.Builder tmpBuilder = Player.PlayerTriggerBundleModel.newBuilder();
            final int tmpFieldCnt = this.triggerBundleModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerBundleModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerBundleModel();
            }
        }  else if (builder.hasTriggerBundleModel()) {
            // 清理TriggerBundleModel
            builder.clearTriggerBundleModel();
            fieldCnt++;
        }
        if (this.featureLockModel != null) {
            Player.PlayerFeatureLockModel.Builder tmpBuilder = Player.PlayerFeatureLockModel.newBuilder();
            final int tmpFieldCnt = this.featureLockModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFeatureLockModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFeatureLockModel();
            }
        }  else if (builder.hasFeatureLockModel()) {
            // 清理FeatureLockModel
            builder.clearFeatureLockModel();
            fieldCnt++;
        }
        if (this.battlePassModel != null) {
            Player.PlayerBattlePassModel.Builder tmpBuilder = Player.PlayerBattlePassModel.newBuilder();
            final int tmpFieldCnt = this.battlePassModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattlePassModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattlePassModel();
            }
        }  else if (builder.hasBattlePassModel()) {
            // 清理BattlePassModel
            builder.clearBattlePassModel();
            fieldCnt++;
        }
        if (this.getDataPatchVersion() != 0) {
            builder.setDataPatchVersion(this.getDataPatchVersion());
            fieldCnt++;
        }  else if (builder.hasDataPatchVersion()) {
            // 清理DataPatchVersion
            builder.clearDataPatchVersion();
            fieldCnt++;
        }
        if (this.additionSysNew != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSysNew.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSysNew();
            }
        }  else if (builder.hasAdditionSysNew()) {
            // 清理AdditionSysNew
            builder.clearAdditionSysNew();
            fieldCnt++;
        }
        if (this.contactsModel != null) {
            Player.PlayerContactsModel.Builder tmpBuilder = Player.PlayerContactsModel.newBuilder();
            final int tmpFieldCnt = this.contactsModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContactsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContactsModel();
            }
        }  else if (builder.hasContactsModel()) {
            // 清理ContactsModel
            builder.clearContactsModel();
            fieldCnt++;
        }
        if (this.ratingModel != null) {
            Player.PlayerStoreRatingModel.Builder tmpBuilder = Player.PlayerStoreRatingModel.newBuilder();
            final int tmpFieldCnt = this.ratingModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRatingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRatingModel();
            }
        }  else if (builder.hasRatingModel()) {
            // 清理RatingModel
            builder.clearRatingModel();
            fieldCnt++;
        }
        if (this.skynetModel != null) {
            Player.PlayerSkynetModel.Builder tmpBuilder = Player.PlayerSkynetModel.newBuilder();
            final int tmpFieldCnt = this.skynetModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkynetModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkynetModel();
            }
        }  else if (builder.hasSkynetModel()) {
            // 清理SkynetModel
            builder.clearSkynetModel();
            fieldCnt++;
        }
        if (this.achievementModel != null) {
            Player.PlayerAchievementModel.Builder tmpBuilder = Player.PlayerAchievementModel.newBuilder();
            final int tmpFieldCnt = this.achievementModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAchievementModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAchievementModel();
            }
        }  else if (builder.hasAchievementModel()) {
            // 清理AchievementModel
            builder.clearAchievementModel();
            fieldCnt++;
        }
        if (this.seasonBattlePassModel != null) {
            Player.PlayerBattlePassModel.Builder tmpBuilder = Player.PlayerBattlePassModel.newBuilder();
            final int tmpFieldCnt = this.seasonBattlePassModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSeasonBattlePassModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSeasonBattlePassModel();
            }
        }  else if (builder.hasSeasonBattlePassModel()) {
            // 清理SeasonBattlePassModel
            builder.clearSeasonBattlePassModel();
            fieldCnt++;
        }
        if (this.playerInnerBuildRHModel != null) {
            Player.PlayerInnerBuildRHModel.Builder tmpBuilder = Player.PlayerInnerBuildRHModel.newBuilder();
            final int tmpFieldCnt = this.playerInnerBuildRHModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerInnerBuildRHModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerInnerBuildRHModel();
            }
        }  else if (builder.hasPlayerInnerBuildRHModel()) {
            // 清理PlayerInnerBuildRHModel
            builder.clearPlayerInnerBuildRHModel();
            fieldCnt++;
        }
        if (this.playerCampaignModel != null) {
            Player.PlayerCampaignModel.Builder tmpBuilder = Player.PlayerCampaignModel.newBuilder();
            final int tmpFieldCnt = this.playerCampaignModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCampaignModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCampaignModel();
            }
        }  else if (builder.hasPlayerCampaignModel()) {
            // 清理PlayerCampaignModel
            builder.clearPlayerCampaignModel();
            fieldCnt++;
        }
        if (this.playerUnitModel != null) {
            Player.PlayerUnitModel.Builder tmpBuilder = Player.PlayerUnitModel.newBuilder();
            final int tmpFieldCnt = this.playerUnitModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerUnitModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerUnitModel();
            }
        }  else if (builder.hasPlayerUnitModel()) {
            // 清理PlayerUnitModel
            builder.clearPlayerUnitModel();
            fieldCnt++;
        }
        if (this.playerMissionModel != null) {
            Player.PlayerMissionModel.Builder tmpBuilder = Player.PlayerMissionModel.newBuilder();
            final int tmpFieldCnt = this.playerMissionModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerMissionModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerMissionModel();
            }
        }  else if (builder.hasPlayerMissionModel()) {
            // 清理PlayerMissionModel
            builder.clearPlayerMissionModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENID)) {
            builder.setOpenId(this.getOpenId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENE) && this.scene != null) {
            final boolean needClear = !builder.hasScene();
            final int tmpFieldCnt = this.scene.copyChangeToDb(builder.getSceneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScene();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEPLAYER) && this.scenePlayer != null) {
            final boolean needClear = !builder.hasScenePlayer();
            final int tmpFieldCnt = this.scenePlayer.copyChangeToDb(builder.getScenePlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScenePlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAN) && this.clan != null) {
            final boolean needClear = !builder.hasClan();
            final int tmpFieldCnt = this.clan.copyChangeToDb(builder.getClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRAGMENTS) && this.fragments != null) {
            final boolean needClear = !builder.hasFragments();
            final int tmpFieldCnt = this.fragments.copyChangeToDb(builder.getFragmentsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFragments();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToDb(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_PURSE) && this.purse != null) {
            final boolean needClear = !builder.hasPurse();
            final int tmpFieldCnt = this.purse.copyChangeToDb(builder.getPurseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPurse();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKMAP) && this.queueTaskMap != null) {
            final boolean needClear = !builder.hasQueueTaskMap();
            final int tmpFieldCnt = this.queueTaskMap.copyChangeToDb(builder.getQueueTaskMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSOLDIERINBUILDING) && this.playerSoldierInBuilding != null) {
            final boolean needClear = !builder.hasPlayerSoldierInBuilding();
            final int tmpFieldCnt = this.playerSoldierInBuilding.copyChangeToDb(builder.getPlayerSoldierInBuildingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSoldierInBuilding();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPOWERINFO) && this.playerPowerInfo != null) {
            final boolean needClear = !builder.hasPlayerPowerInfo();
            final int tmpFieldCnt = this.playerPowerInfo.copyChangeToDb(builder.getPlayerPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPowerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERDATARECORD) && this.playerDataRecord != null) {
            final boolean needClear = !builder.hasPlayerDataRecord();
            final int tmpFieldCnt = this.playerDataRecord.copyChangeToDb(builder.getPlayerDataRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerDataRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASICINFO) && this.basicInfo != null) {
            final boolean needClear = !builder.hasBasicInfo();
            final int tmpFieldCnt = this.basicInfo.copyChangeToDb(builder.getBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            final boolean needClear = !builder.hasFormation();
            final int tmpFieldCnt = this.formation.copyChangeToDb(builder.getFormationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormation();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEPRODUCE) && this.resourceProduce != null) {
            final boolean needClear = !builder.hasResourceProduce();
            final int tmpFieldCnt = this.resourceProduce.copyChangeToDb(builder.getResourceProduceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceProduce();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHSYS) && this.refreshSys != null) {
            final boolean needClear = !builder.hasRefreshSys();
            final int tmpFieldCnt = this.refreshSys.copyChangeToDb(builder.getRefreshSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKSYSTEM) && this.taskSystem != null) {
            final boolean needClear = !builder.hasTaskSystem();
            final int tmpFieldCnt = this.taskSystem.copyChangeToDb(builder.getTaskSystemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskSystem();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCOUNTINFO) && this.discountInfo != null) {
            final boolean needClear = !builder.hasDiscountInfo();
            final int tmpFieldCnt = this.discountInfo.copyChangeToDb(builder.getDiscountInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscountInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSTOREMODEL) && this.playerStoreModel != null) {
            final boolean needClear = !builder.hasPlayerStoreModel();
            final int tmpFieldCnt = this.playerStoreModel.copyChangeToDb(builder.getPlayerStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERGUIDANCEMODEL) && this.PlayerGuidanceModel != null) {
            final boolean needClear = !builder.hasPlayerGuidanceModel();
            final int tmpFieldCnt = this.PlayerGuidanceModel.copyChangeToDb(builder.getPlayerGuidanceModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerGuidanceModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTECHMODEL) && this.PlayerTechModel != null) {
            final boolean needClear = !builder.hasPlayerTechModel();
            final int tmpFieldCnt = this.PlayerTechModel.copyChangeToDb(builder.getPlayerTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEMODEL) && this.playerPlaneModel != null) {
            final boolean needClear = !builder.hasPlayerPlaneModel();
            final int tmpFieldCnt = this.playerPlaneModel.copyChangeToDb(builder.getPlayerPlaneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPlaneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEMODEL) && this.newbieModel != null) {
            final boolean needClear = !builder.hasNewbieModel();
            final int tmpFieldCnt = this.newbieModel.copyChangeToDb(builder.getNewbieModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNewbieModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATISTICMODEL) && this.statisticModel != null) {
            final boolean needClear = !builder.hasStatisticModel();
            final int tmpFieldCnt = this.statisticModel.copyChangeToDb(builder.getStatisticModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStatisticModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToDb(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMODEL) && this.playerHeroModel != null) {
            final boolean needClear = !builder.hasPlayerHeroModel();
            final int tmpFieldCnt = this.playerHeroModel.copyChangeToDb(builder.getPlayerHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_AVATARMODEL) && this.avatarModel != null) {
            final boolean needClear = !builder.hasAvatarModel();
            final int tmpFieldCnt = this.avatarModel.copyChangeToDb(builder.getAvatarModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAvatarModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            final boolean needClear = !builder.hasZoneModel();
            final int tmpFieldCnt = this.zoneModel.copyChangeToDb(builder.getZoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENERGYMODEL) && this.energyModel != null) {
            final boolean needClear = !builder.hasEnergyModel();
            final int tmpFieldCnt = this.energyModel.copyChangeToDb(builder.getEnergyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEnergyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERRECRUITMODEL) && this.playerRecruitModel != null) {
            final boolean needClear = !builder.hasPlayerRecruitModel();
            final int tmpFieldCnt = this.playerRecruitModel.copyChangeToDb(builder.getPlayerRecruitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerRecruitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToDb(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAILMODEL) && this.mailModel != null) {
            final boolean needClear = !builder.hasMailModel();
            final int tmpFieldCnt = this.mailModel.copyChangeToDb(builder.getMailModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMailModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMODEL) && this.killMonsterModel != null) {
            final boolean needClear = !builder.hasKillMonsterModel();
            final int tmpFieldCnt = this.killMonsterModel.copyChangeToDb(builder.getKillMonsterModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKillMonsterModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEMODEL) && this.mileStoneModel != null) {
            final boolean needClear = !builder.hasMileStoneModel();
            final int tmpFieldCnt = this.mileStoneModel.copyChangeToDb(builder.getMileStoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONMODEL) && this.dungeonModel != null) {
            final boolean needClear = !builder.hasDungeonModel();
            final int tmpFieldCnt = this.dungeonModel.copyChangeToDb(builder.getDungeonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETINFO) && this.resetInfo != null) {
            final boolean needClear = !builder.hasResetInfo();
            final int tmpFieldCnt = this.resetInfo.copyChangeToDb(builder.getResetInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETTINGMODEL) && this.settingModel != null) {
            final boolean needClear = !builder.hasSettingModel();
            final int tmpFieldCnt = this.settingModel.copyChangeToDb(builder.getSettingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSettingModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYMENTMODEL) && this.paymentModel != null) {
            final boolean needClear = !builder.hasPaymentModel();
            final int tmpFieldCnt = this.paymentModel.copyChangeToDb(builder.getPaymentModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPaymentModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPMODEL) && this.vipModel != null) {
            final boolean needClear = !builder.hasVipModel();
            final int tmpFieldCnt = this.vipModel.copyChangeToDb(builder.getVipModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUESTMODEL) && this.questModel != null) {
            final boolean needClear = !builder.hasQuestModel();
            final int tmpFieldCnt = this.questModel.copyChangeToDb(builder.getQuestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQuestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYMODEL) && this.cityModel != null) {
            final boolean needClear = !builder.hasCityModel();
            final int tmpFieldCnt = this.cityModel.copyChangeToDb(builder.getCityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DAILYGOODSMODEL) && this.dailyGoodsModel != null) {
            final boolean needClear = !builder.hasDailyGoodsModel();
            final int tmpFieldCnt = this.dailyGoodsModel.copyChangeToDb(builder.getDailyGoodsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyGoodsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DRAWINFO) && this.drawInfo != null) {
            final boolean needClear = !builder.hasDrawInfo();
            final int tmpFieldCnt = this.drawInfo.copyChangeToDb(builder.getDrawInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDrawInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToDb(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToDb(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEADDITIONSYS) && this.zoneAdditionSys != null) {
            final boolean needClear = !builder.hasZoneAdditionSys();
            final int tmpFieldCnt = this.zoneAdditionSys.copyChangeToDb(builder.getZoneAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEMODEL) && this.triggerBundleModel != null) {
            final boolean needClear = !builder.hasTriggerBundleModel();
            final int tmpFieldCnt = this.triggerBundleModel.copyChangeToDb(builder.getTriggerBundleModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FEATURELOCKMODEL) && this.featureLockModel != null) {
            final boolean needClear = !builder.hasFeatureLockModel();
            final int tmpFieldCnt = this.featureLockModel.copyChangeToDb(builder.getFeatureLockModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFeatureLockModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPASSMODEL) && this.battlePassModel != null) {
            final boolean needClear = !builder.hasBattlePassModel();
            final int tmpFieldCnt = this.battlePassModel.copyChangeToDb(builder.getBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DATAPATCHVERSION)) {
            builder.setDataPatchVersion(this.getDataPatchVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYSNEW) && this.additionSysNew != null) {
            final boolean needClear = !builder.hasAdditionSysNew();
            final int tmpFieldCnt = this.additionSysNew.copyChangeToDb(builder.getAdditionSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTACTSMODEL) && this.contactsModel != null) {
            final boolean needClear = !builder.hasContactsModel();
            final int tmpFieldCnt = this.contactsModel.copyChangeToDb(builder.getContactsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContactsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RATINGMODEL) && this.ratingModel != null) {
            final boolean needClear = !builder.hasRatingModel();
            final int tmpFieldCnt = this.ratingModel.copyChangeToDb(builder.getRatingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRatingModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKYNETMODEL) && this.skynetModel != null) {
            final boolean needClear = !builder.hasSkynetModel();
            final int tmpFieldCnt = this.skynetModel.copyChangeToDb(builder.getSkynetModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkynetModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTMODEL) && this.achievementModel != null) {
            final boolean needClear = !builder.hasAchievementModel();
            final int tmpFieldCnt = this.achievementModel.copyChangeToDb(builder.getAchievementModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAchievementModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONBATTLEPASSMODEL) && this.seasonBattlePassModel != null) {
            final boolean needClear = !builder.hasSeasonBattlePassModel();
            final int tmpFieldCnt = this.seasonBattlePassModel.copyChangeToDb(builder.getSeasonBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINNERBUILDRHMODEL) && this.playerInnerBuildRHModel != null) {
            final boolean needClear = !builder.hasPlayerInnerBuildRHModel();
            final int tmpFieldCnt = this.playerInnerBuildRHModel.copyChangeToDb(builder.getPlayerInnerBuildRHModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInnerBuildRHModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMPAIGNMODEL) && this.playerCampaignModel != null) {
            final boolean needClear = !builder.hasPlayerCampaignModel();
            final int tmpFieldCnt = this.playerCampaignModel.copyChangeToDb(builder.getPlayerCampaignModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCampaignModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMODEL) && this.playerUnitModel != null) {
            final boolean needClear = !builder.hasPlayerUnitModel();
            final int tmpFieldCnt = this.playerUnitModel.copyChangeToDb(builder.getPlayerUnitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERMISSIONMODEL) && this.playerMissionModel != null) {
            final boolean needClear = !builder.hasPlayerMissionModel();
            final int tmpFieldCnt = this.playerMissionModel.copyChangeToDb(builder.getPlayerMissionModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerMissionModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOpenId()) {
            this.innerSetOpenId(proto.getOpenId());
        } else {
            this.innerSetOpenId(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasScene()) {
            this.getScene().mergeFromDb(proto.getScene());
        } else {
            if (this.scene != null) {
                this.scene.mergeFromDb(proto.getScene());
            }
        }
        if (proto.hasScenePlayer()) {
            this.getScenePlayer().mergeFromDb(proto.getScenePlayer());
        } else {
            if (this.scenePlayer != null) {
                this.scenePlayer.mergeFromDb(proto.getScenePlayer());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClan()) {
            this.getClan().mergeFromDb(proto.getClan());
        } else {
            if (this.clan != null) {
                this.clan.mergeFromDb(proto.getClan());
            }
        }
        if (proto.hasFragments()) {
            this.getFragments().mergeFromDb(proto.getFragments());
        } else {
            if (this.fragments != null) {
                this.fragments.mergeFromDb(proto.getFragments());
            }
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromDb(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromDb(proto.getItems());
            }
        }
        if (proto.hasPurse()) {
            this.getPurse().mergeFromDb(proto.getPurse());
        } else {
            if (this.purse != null) {
                this.purse.mergeFromDb(proto.getPurse());
            }
        }
        if (proto.hasQueueTaskMap()) {
            this.getQueueTaskMap().mergeFromDb(proto.getQueueTaskMap());
        } else {
            if (this.queueTaskMap != null) {
                this.queueTaskMap.mergeFromDb(proto.getQueueTaskMap());
            }
        }
        if (proto.hasPlayerSoldierInBuilding()) {
            this.getPlayerSoldierInBuilding().mergeFromDb(proto.getPlayerSoldierInBuilding());
        } else {
            if (this.playerSoldierInBuilding != null) {
                this.playerSoldierInBuilding.mergeFromDb(proto.getPlayerSoldierInBuilding());
            }
        }
        if (proto.hasPlayerPowerInfo()) {
            this.getPlayerPowerInfo().mergeFromDb(proto.getPlayerPowerInfo());
        } else {
            if (this.playerPowerInfo != null) {
                this.playerPowerInfo.mergeFromDb(proto.getPlayerPowerInfo());
            }
        }
        if (proto.hasPlayerDataRecord()) {
            this.getPlayerDataRecord().mergeFromDb(proto.getPlayerDataRecord());
        } else {
            if (this.playerDataRecord != null) {
                this.playerDataRecord.mergeFromDb(proto.getPlayerDataRecord());
            }
        }
        if (proto.hasBasicInfo()) {
            this.getBasicInfo().mergeFromDb(proto.getBasicInfo());
        } else {
            if (this.basicInfo != null) {
                this.basicInfo.mergeFromDb(proto.getBasicInfo());
            }
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeFromDb(proto.getFormation());
        } else {
            if (this.formation != null) {
                this.formation.mergeFromDb(proto.getFormation());
            }
        }
        if (proto.hasResourceProduce()) {
            this.getResourceProduce().mergeFromDb(proto.getResourceProduce());
        } else {
            if (this.resourceProduce != null) {
                this.resourceProduce.mergeFromDb(proto.getResourceProduce());
            }
        }
        if (proto.hasRefreshSys()) {
            this.getRefreshSys().mergeFromDb(proto.getRefreshSys());
        } else {
            if (this.refreshSys != null) {
                this.refreshSys.mergeFromDb(proto.getRefreshSys());
            }
        }
        if (proto.hasTaskSystem()) {
            this.getTaskSystem().mergeFromDb(proto.getTaskSystem());
        } else {
            if (this.taskSystem != null) {
                this.taskSystem.mergeFromDb(proto.getTaskSystem());
            }
        }
        if (proto.hasDiscountInfo()) {
            this.getDiscountInfo().mergeFromDb(proto.getDiscountInfo());
        } else {
            if (this.discountInfo != null) {
                this.discountInfo.mergeFromDb(proto.getDiscountInfo());
            }
        }
        if (proto.hasPlayerStoreModel()) {
            this.getPlayerStoreModel().mergeFromDb(proto.getPlayerStoreModel());
        } else {
            if (this.playerStoreModel != null) {
                this.playerStoreModel.mergeFromDb(proto.getPlayerStoreModel());
            }
        }
        if (proto.hasPlayerGuidanceModel()) {
            this.getPlayerGuidanceModel().mergeFromDb(proto.getPlayerGuidanceModel());
        } else {
            if (this.PlayerGuidanceModel != null) {
                this.PlayerGuidanceModel.mergeFromDb(proto.getPlayerGuidanceModel());
            }
        }
        if (proto.hasPlayerTechModel()) {
            this.getPlayerTechModel().mergeFromDb(proto.getPlayerTechModel());
        } else {
            if (this.PlayerTechModel != null) {
                this.PlayerTechModel.mergeFromDb(proto.getPlayerTechModel());
            }
        }
        if (proto.hasPlayerPlaneModel()) {
            this.getPlayerPlaneModel().mergeFromDb(proto.getPlayerPlaneModel());
        } else {
            if (this.playerPlaneModel != null) {
                this.playerPlaneModel.mergeFromDb(proto.getPlayerPlaneModel());
            }
        }
        if (proto.hasNewbieModel()) {
            this.getNewbieModel().mergeFromDb(proto.getNewbieModel());
        } else {
            if (this.newbieModel != null) {
                this.newbieModel.mergeFromDb(proto.getNewbieModel());
            }
        }
        if (proto.hasStatisticModel()) {
            this.getStatisticModel().mergeFromDb(proto.getStatisticModel());
        } else {
            if (this.statisticModel != null) {
                this.statisticModel.mergeFromDb(proto.getStatisticModel());
            }
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeFromDb(proto.getRedDotModel());
        } else {
            if (this.redDotModel != null) {
                this.redDotModel.mergeFromDb(proto.getRedDotModel());
            }
        }
        if (proto.hasPlayerHeroModel()) {
            this.getPlayerHeroModel().mergeFromDb(proto.getPlayerHeroModel());
        } else {
            if (this.playerHeroModel != null) {
                this.playerHeroModel.mergeFromDb(proto.getPlayerHeroModel());
            }
        }
        if (proto.hasAvatarModel()) {
            this.getAvatarModel().mergeFromDb(proto.getAvatarModel());
        } else {
            if (this.avatarModel != null) {
                this.avatarModel.mergeFromDb(proto.getAvatarModel());
            }
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeFromDb(proto.getZoneModel());
        } else {
            if (this.zoneModel != null) {
                this.zoneModel.mergeFromDb(proto.getZoneModel());
            }
        }
        if (proto.hasEnergyModel()) {
            this.getEnergyModel().mergeFromDb(proto.getEnergyModel());
        } else {
            if (this.energyModel != null) {
                this.energyModel.mergeFromDb(proto.getEnergyModel());
            }
        }
        if (proto.hasPlayerRecruitModel()) {
            this.getPlayerRecruitModel().mergeFromDb(proto.getPlayerRecruitModel());
        } else {
            if (this.playerRecruitModel != null) {
                this.playerRecruitModel.mergeFromDb(proto.getPlayerRecruitModel());
            }
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeFromDb(proto.getActivityModel());
        } else {
            if (this.activityModel != null) {
                this.activityModel.mergeFromDb(proto.getActivityModel());
            }
        }
        if (proto.hasMailModel()) {
            this.getMailModel().mergeFromDb(proto.getMailModel());
        } else {
            if (this.mailModel != null) {
                this.mailModel.mergeFromDb(proto.getMailModel());
            }
        }
        if (proto.hasKillMonsterModel()) {
            this.getKillMonsterModel().mergeFromDb(proto.getKillMonsterModel());
        } else {
            if (this.killMonsterModel != null) {
                this.killMonsterModel.mergeFromDb(proto.getKillMonsterModel());
            }
        }
        if (proto.hasMileStoneModel()) {
            this.getMileStoneModel().mergeFromDb(proto.getMileStoneModel());
        } else {
            if (this.mileStoneModel != null) {
                this.mileStoneModel.mergeFromDb(proto.getMileStoneModel());
            }
        }
        if (proto.hasDungeonModel()) {
            this.getDungeonModel().mergeFromDb(proto.getDungeonModel());
        } else {
            if (this.dungeonModel != null) {
                this.dungeonModel.mergeFromDb(proto.getDungeonModel());
            }
        }
        if (proto.hasResetInfo()) {
            this.getResetInfo().mergeFromDb(proto.getResetInfo());
        } else {
            if (this.resetInfo != null) {
                this.resetInfo.mergeFromDb(proto.getResetInfo());
            }
        }
        if (proto.hasSettingModel()) {
            this.getSettingModel().mergeFromDb(proto.getSettingModel());
        } else {
            if (this.settingModel != null) {
                this.settingModel.mergeFromDb(proto.getSettingModel());
            }
        }
        if (proto.hasPaymentModel()) {
            this.getPaymentModel().mergeFromDb(proto.getPaymentModel());
        } else {
            if (this.paymentModel != null) {
                this.paymentModel.mergeFromDb(proto.getPaymentModel());
            }
        }
        if (proto.hasVipModel()) {
            this.getVipModel().mergeFromDb(proto.getVipModel());
        } else {
            if (this.vipModel != null) {
                this.vipModel.mergeFromDb(proto.getVipModel());
            }
        }
        if (proto.hasQuestModel()) {
            this.getQuestModel().mergeFromDb(proto.getQuestModel());
        } else {
            if (this.questModel != null) {
                this.questModel.mergeFromDb(proto.getQuestModel());
            }
        }
        if (proto.hasCityModel()) {
            this.getCityModel().mergeFromDb(proto.getCityModel());
        } else {
            if (this.cityModel != null) {
                this.cityModel.mergeFromDb(proto.getCityModel());
            }
        }
        if (proto.hasDailyGoodsModel()) {
            this.getDailyGoodsModel().mergeFromDb(proto.getDailyGoodsModel());
        } else {
            if (this.dailyGoodsModel != null) {
                this.dailyGoodsModel.mergeFromDb(proto.getDailyGoodsModel());
            }
        }
        if (proto.hasDrawInfo()) {
            this.getDrawInfo().mergeFromDb(proto.getDrawInfo());
        } else {
            if (this.drawInfo != null) {
                this.drawInfo.mergeFromDb(proto.getDrawInfo());
            }
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeFromDb(proto.getDevBuffSysNew());
        } else {
            if (this.devBuffSysNew != null) {
                this.devBuffSysNew.mergeFromDb(proto.getDevBuffSysNew());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromDb(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromDb(proto.getKingdomModel());
            }
        }
        if (proto.hasZoneAdditionSys()) {
            this.getZoneAdditionSys().mergeFromDb(proto.getZoneAdditionSys());
        } else {
            if (this.zoneAdditionSys != null) {
                this.zoneAdditionSys.mergeFromDb(proto.getZoneAdditionSys());
            }
        }
        if (proto.hasTriggerBundleModel()) {
            this.getTriggerBundleModel().mergeFromDb(proto.getTriggerBundleModel());
        } else {
            if (this.triggerBundleModel != null) {
                this.triggerBundleModel.mergeFromDb(proto.getTriggerBundleModel());
            }
        }
        if (proto.hasFeatureLockModel()) {
            this.getFeatureLockModel().mergeFromDb(proto.getFeatureLockModel());
        } else {
            if (this.featureLockModel != null) {
                this.featureLockModel.mergeFromDb(proto.getFeatureLockModel());
            }
        }
        if (proto.hasBattlePassModel()) {
            this.getBattlePassModel().mergeFromDb(proto.getBattlePassModel());
        } else {
            if (this.battlePassModel != null) {
                this.battlePassModel.mergeFromDb(proto.getBattlePassModel());
            }
        }
        if (proto.hasDataPatchVersion()) {
            this.innerSetDataPatchVersion(proto.getDataPatchVersion());
        } else {
            this.innerSetDataPatchVersion(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAdditionSysNew()) {
            this.getAdditionSysNew().mergeFromDb(proto.getAdditionSysNew());
        } else {
            if (this.additionSysNew != null) {
                this.additionSysNew.mergeFromDb(proto.getAdditionSysNew());
            }
        }
        if (proto.hasContactsModel()) {
            this.getContactsModel().mergeFromDb(proto.getContactsModel());
        } else {
            if (this.contactsModel != null) {
                this.contactsModel.mergeFromDb(proto.getContactsModel());
            }
        }
        if (proto.hasRatingModel()) {
            this.getRatingModel().mergeFromDb(proto.getRatingModel());
        } else {
            if (this.ratingModel != null) {
                this.ratingModel.mergeFromDb(proto.getRatingModel());
            }
        }
        if (proto.hasSkynetModel()) {
            this.getSkynetModel().mergeFromDb(proto.getSkynetModel());
        } else {
            if (this.skynetModel != null) {
                this.skynetModel.mergeFromDb(proto.getSkynetModel());
            }
        }
        if (proto.hasAchievementModel()) {
            this.getAchievementModel().mergeFromDb(proto.getAchievementModel());
        } else {
            if (this.achievementModel != null) {
                this.achievementModel.mergeFromDb(proto.getAchievementModel());
            }
        }
        if (proto.hasSeasonBattlePassModel()) {
            this.getSeasonBattlePassModel().mergeFromDb(proto.getSeasonBattlePassModel());
        } else {
            if (this.seasonBattlePassModel != null) {
                this.seasonBattlePassModel.mergeFromDb(proto.getSeasonBattlePassModel());
            }
        }
        if (proto.hasPlayerInnerBuildRHModel()) {
            this.getPlayerInnerBuildRHModel().mergeFromDb(proto.getPlayerInnerBuildRHModel());
        } else {
            if (this.playerInnerBuildRHModel != null) {
                this.playerInnerBuildRHModel.mergeFromDb(proto.getPlayerInnerBuildRHModel());
            }
        }
        if (proto.hasPlayerCampaignModel()) {
            this.getPlayerCampaignModel().mergeFromDb(proto.getPlayerCampaignModel());
        } else {
            if (this.playerCampaignModel != null) {
                this.playerCampaignModel.mergeFromDb(proto.getPlayerCampaignModel());
            }
        }
        if (proto.hasPlayerUnitModel()) {
            this.getPlayerUnitModel().mergeFromDb(proto.getPlayerUnitModel());
        } else {
            if (this.playerUnitModel != null) {
                this.playerUnitModel.mergeFromDb(proto.getPlayerUnitModel());
            }
        }
        if (proto.hasPlayerMissionModel()) {
            this.getPlayerMissionModel().mergeFromDb(proto.getPlayerMissionModel());
        } else {
            if (this.playerMissionModel != null) {
                this.playerMissionModel.mergeFromDb(proto.getPlayerMissionModel());
            }
        }
        this.markAll();
        return PlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasOpenId()) {
            this.setOpenId(proto.getOpenId());
            fieldCnt++;
        }
        if (proto.hasScene()) {
            this.getScene().mergeChangeFromDb(proto.getScene());
            fieldCnt++;
        }
        if (proto.hasScenePlayer()) {
            this.getScenePlayer().mergeChangeFromDb(proto.getScenePlayer());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasClan()) {
            this.getClan().mergeChangeFromDb(proto.getClan());
            fieldCnt++;
        }
        if (proto.hasFragments()) {
            this.getFragments().mergeChangeFromDb(proto.getFragments());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromDb(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasPurse()) {
            this.getPurse().mergeChangeFromDb(proto.getPurse());
            fieldCnt++;
        }
        if (proto.hasQueueTaskMap()) {
            this.getQueueTaskMap().mergeChangeFromDb(proto.getQueueTaskMap());
            fieldCnt++;
        }
        if (proto.hasPlayerSoldierInBuilding()) {
            this.getPlayerSoldierInBuilding().mergeChangeFromDb(proto.getPlayerSoldierInBuilding());
            fieldCnt++;
        }
        if (proto.hasPlayerPowerInfo()) {
            this.getPlayerPowerInfo().mergeChangeFromDb(proto.getPlayerPowerInfo());
            fieldCnt++;
        }
        if (proto.hasPlayerDataRecord()) {
            this.getPlayerDataRecord().mergeChangeFromDb(proto.getPlayerDataRecord());
            fieldCnt++;
        }
        if (proto.hasBasicInfo()) {
            this.getBasicInfo().mergeChangeFromDb(proto.getBasicInfo());
            fieldCnt++;
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeChangeFromDb(proto.getFormation());
            fieldCnt++;
        }
        if (proto.hasResourceProduce()) {
            this.getResourceProduce().mergeChangeFromDb(proto.getResourceProduce());
            fieldCnt++;
        }
        if (proto.hasRefreshSys()) {
            this.getRefreshSys().mergeChangeFromDb(proto.getRefreshSys());
            fieldCnt++;
        }
        if (proto.hasTaskSystem()) {
            this.getTaskSystem().mergeChangeFromDb(proto.getTaskSystem());
            fieldCnt++;
        }
        if (proto.hasDiscountInfo()) {
            this.getDiscountInfo().mergeChangeFromDb(proto.getDiscountInfo());
            fieldCnt++;
        }
        if (proto.hasPlayerStoreModel()) {
            this.getPlayerStoreModel().mergeChangeFromDb(proto.getPlayerStoreModel());
            fieldCnt++;
        }
        if (proto.hasPlayerGuidanceModel()) {
            this.getPlayerGuidanceModel().mergeChangeFromDb(proto.getPlayerGuidanceModel());
            fieldCnt++;
        }
        if (proto.hasPlayerTechModel()) {
            this.getPlayerTechModel().mergeChangeFromDb(proto.getPlayerTechModel());
            fieldCnt++;
        }
        if (proto.hasPlayerPlaneModel()) {
            this.getPlayerPlaneModel().mergeChangeFromDb(proto.getPlayerPlaneModel());
            fieldCnt++;
        }
        if (proto.hasNewbieModel()) {
            this.getNewbieModel().mergeChangeFromDb(proto.getNewbieModel());
            fieldCnt++;
        }
        if (proto.hasStatisticModel()) {
            this.getStatisticModel().mergeChangeFromDb(proto.getStatisticModel());
            fieldCnt++;
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeChangeFromDb(proto.getRedDotModel());
            fieldCnt++;
        }
        if (proto.hasPlayerHeroModel()) {
            this.getPlayerHeroModel().mergeChangeFromDb(proto.getPlayerHeroModel());
            fieldCnt++;
        }
        if (proto.hasAvatarModel()) {
            this.getAvatarModel().mergeChangeFromDb(proto.getAvatarModel());
            fieldCnt++;
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeChangeFromDb(proto.getZoneModel());
            fieldCnt++;
        }
        if (proto.hasEnergyModel()) {
            this.getEnergyModel().mergeChangeFromDb(proto.getEnergyModel());
            fieldCnt++;
        }
        if (proto.hasPlayerRecruitModel()) {
            this.getPlayerRecruitModel().mergeChangeFromDb(proto.getPlayerRecruitModel());
            fieldCnt++;
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeChangeFromDb(proto.getActivityModel());
            fieldCnt++;
        }
        if (proto.hasMailModel()) {
            this.getMailModel().mergeChangeFromDb(proto.getMailModel());
            fieldCnt++;
        }
        if (proto.hasKillMonsterModel()) {
            this.getKillMonsterModel().mergeChangeFromDb(proto.getKillMonsterModel());
            fieldCnt++;
        }
        if (proto.hasMileStoneModel()) {
            this.getMileStoneModel().mergeChangeFromDb(proto.getMileStoneModel());
            fieldCnt++;
        }
        if (proto.hasDungeonModel()) {
            this.getDungeonModel().mergeChangeFromDb(proto.getDungeonModel());
            fieldCnt++;
        }
        if (proto.hasResetInfo()) {
            this.getResetInfo().mergeChangeFromDb(proto.getResetInfo());
            fieldCnt++;
        }
        if (proto.hasSettingModel()) {
            this.getSettingModel().mergeChangeFromDb(proto.getSettingModel());
            fieldCnt++;
        }
        if (proto.hasPaymentModel()) {
            this.getPaymentModel().mergeChangeFromDb(proto.getPaymentModel());
            fieldCnt++;
        }
        if (proto.hasVipModel()) {
            this.getVipModel().mergeChangeFromDb(proto.getVipModel());
            fieldCnt++;
        }
        if (proto.hasQuestModel()) {
            this.getQuestModel().mergeChangeFromDb(proto.getQuestModel());
            fieldCnt++;
        }
        if (proto.hasCityModel()) {
            this.getCityModel().mergeChangeFromDb(proto.getCityModel());
            fieldCnt++;
        }
        if (proto.hasDailyGoodsModel()) {
            this.getDailyGoodsModel().mergeChangeFromDb(proto.getDailyGoodsModel());
            fieldCnt++;
        }
        if (proto.hasDrawInfo()) {
            this.getDrawInfo().mergeChangeFromDb(proto.getDrawInfo());
            fieldCnt++;
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeChangeFromDb(proto.getDevBuffSysNew());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromDb(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasZoneAdditionSys()) {
            this.getZoneAdditionSys().mergeChangeFromDb(proto.getZoneAdditionSys());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleModel()) {
            this.getTriggerBundleModel().mergeChangeFromDb(proto.getTriggerBundleModel());
            fieldCnt++;
        }
        if (proto.hasFeatureLockModel()) {
            this.getFeatureLockModel().mergeChangeFromDb(proto.getFeatureLockModel());
            fieldCnt++;
        }
        if (proto.hasBattlePassModel()) {
            this.getBattlePassModel().mergeChangeFromDb(proto.getBattlePassModel());
            fieldCnt++;
        }
        if (proto.hasDataPatchVersion()) {
            this.setDataPatchVersion(proto.getDataPatchVersion());
            fieldCnt++;
        }
        if (proto.hasAdditionSysNew()) {
            this.getAdditionSysNew().mergeChangeFromDb(proto.getAdditionSysNew());
            fieldCnt++;
        }
        if (proto.hasContactsModel()) {
            this.getContactsModel().mergeChangeFromDb(proto.getContactsModel());
            fieldCnt++;
        }
        if (proto.hasRatingModel()) {
            this.getRatingModel().mergeChangeFromDb(proto.getRatingModel());
            fieldCnt++;
        }
        if (proto.hasSkynetModel()) {
            this.getSkynetModel().mergeChangeFromDb(proto.getSkynetModel());
            fieldCnt++;
        }
        if (proto.hasAchievementModel()) {
            this.getAchievementModel().mergeChangeFromDb(proto.getAchievementModel());
            fieldCnt++;
        }
        if (proto.hasSeasonBattlePassModel()) {
            this.getSeasonBattlePassModel().mergeChangeFromDb(proto.getSeasonBattlePassModel());
            fieldCnt++;
        }
        if (proto.hasPlayerInnerBuildRHModel()) {
            this.getPlayerInnerBuildRHModel().mergeChangeFromDb(proto.getPlayerInnerBuildRHModel());
            fieldCnt++;
        }
        if (proto.hasPlayerCampaignModel()) {
            this.getPlayerCampaignModel().mergeChangeFromDb(proto.getPlayerCampaignModel());
            fieldCnt++;
        }
        if (proto.hasPlayerUnitModel()) {
            this.getPlayerUnitModel().mergeChangeFromDb(proto.getPlayerUnitModel());
            fieldCnt++;
        }
        if (proto.hasPlayerMissionModel()) {
            this.getPlayerMissionModel().mergeChangeFromDb(proto.getPlayerMissionModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerEntity.Builder getCopySsBuilder() {
        final PlayerEntity.Builder builder = PlayerEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (!this.getOpenId().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOpenId(this.getOpenId());
            fieldCnt++;
        }  else if (builder.hasOpenId()) {
            // 清理OpenId
            builder.clearOpenId();
            fieldCnt++;
        }
        if (this.scene != null) {
            Player.PlayerScene.Builder tmpBuilder = Player.PlayerScene.newBuilder();
            final int tmpFieldCnt = this.scene.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScene(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScene();
            }
        }  else if (builder.hasScene()) {
            // 清理Scene
            builder.clearScene();
            fieldCnt++;
        }
        if (this.scenePlayer != null) {
            Player.ScenePlayer.Builder tmpBuilder = Player.ScenePlayer.newBuilder();
            final int tmpFieldCnt = this.scenePlayer.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScenePlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScenePlayer();
            }
        }  else if (builder.hasScenePlayer()) {
            // 清理ScenePlayer
            builder.clearScenePlayer();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.clan != null) {
            Player.ClanInfo.Builder tmpBuilder = Player.ClanInfo.newBuilder();
            final int tmpFieldCnt = this.clan.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClan(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClan();
            }
        }  else if (builder.hasClan()) {
            // 清理Clan
            builder.clearClan();
            fieldCnt++;
        }
        if (this.fragments != null) {
            Player.HistoryFragments.Builder tmpBuilder = Player.HistoryFragments.newBuilder();
            final int tmpFieldCnt = this.fragments.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFragments(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFragments();
            }
        }  else if (builder.hasFragments()) {
            // 清理Fragments
            builder.clearFragments();
            fieldCnt++;
        }
        if (this.items != null) {
            Struct.Int64ItemMap.Builder tmpBuilder = Struct.Int64ItemMap.newBuilder();
            final int tmpFieldCnt = this.items.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setItems(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearItems();
            }
        }  else if (builder.hasItems()) {
            // 清理Items
            builder.clearItems();
            fieldCnt++;
        }
        if (this.purse != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.purse.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPurse(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPurse();
            }
        }  else if (builder.hasPurse()) {
            // 清理Purse
            builder.clearPurse();
            fieldCnt++;
        }
        if (this.queueTaskMap != null) {
            StructPlayer.Int32QueueTasksMap.Builder tmpBuilder = StructPlayer.Int32QueueTasksMap.newBuilder();
            final int tmpFieldCnt = this.queueTaskMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQueueTaskMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQueueTaskMap();
            }
        }  else if (builder.hasQueueTaskMap()) {
            // 清理QueueTaskMap
            builder.clearQueueTaskMap();
            fieldCnt++;
        }
        if (this.playerSoldierInBuilding != null) {
            Struct.PlayerSoldierInBuilding.Builder tmpBuilder = Struct.PlayerSoldierInBuilding.newBuilder();
            final int tmpFieldCnt = this.playerSoldierInBuilding.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerSoldierInBuilding(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerSoldierInBuilding();
            }
        }  else if (builder.hasPlayerSoldierInBuilding()) {
            // 清理PlayerSoldierInBuilding
            builder.clearPlayerSoldierInBuilding();
            fieldCnt++;
        }
        if (this.playerPowerInfo != null) {
            Struct.PlayerPowerInfo.Builder tmpBuilder = Struct.PlayerPowerInfo.newBuilder();
            final int tmpFieldCnt = this.playerPowerInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerPowerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerPowerInfo();
            }
        }  else if (builder.hasPlayerPowerInfo()) {
            // 清理PlayerPowerInfo
            builder.clearPlayerPowerInfo();
            fieldCnt++;
        }
        if (this.playerDataRecord != null) {
            Struct.PlayerDataRecord.Builder tmpBuilder = Struct.PlayerDataRecord.newBuilder();
            final int tmpFieldCnt = this.playerDataRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerDataRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerDataRecord();
            }
        }  else if (builder.hasPlayerDataRecord()) {
            // 清理PlayerDataRecord
            builder.clearPlayerDataRecord();
            fieldCnt++;
        }
        if (this.basicInfo != null) {
            Player.PlayerBasicInfo.Builder tmpBuilder = Player.PlayerBasicInfo.newBuilder();
            final int tmpFieldCnt = this.basicInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBasicInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBasicInfo();
            }
        }  else if (builder.hasBasicInfo()) {
            // 清理BasicInfo
            builder.clearBasicInfo();
            fieldCnt++;
        }
        if (this.formation != null) {
            StructPlayer.PlayerFormation.Builder tmpBuilder = StructPlayer.PlayerFormation.newBuilder();
            final int tmpFieldCnt = this.formation.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormation();
            }
        }  else if (builder.hasFormation()) {
            // 清理Formation
            builder.clearFormation();
            fieldCnt++;
        }
        if (this.resourceProduce != null) {
            Player.PlayerResourceProduceMap.Builder tmpBuilder = Player.PlayerResourceProduceMap.newBuilder();
            final int tmpFieldCnt = this.resourceProduce.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResourceProduce(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResourceProduce();
            }
        }  else if (builder.hasResourceProduce()) {
            // 清理ResourceProduce
            builder.clearResourceProduce();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.refreshSys != null) {
            Player.RefreshInfo.Builder tmpBuilder = Player.RefreshInfo.newBuilder();
            final int tmpFieldCnt = this.refreshSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshSys();
            }
        }  else if (builder.hasRefreshSys()) {
            // 清理RefreshSys
            builder.clearRefreshSys();
            fieldCnt++;
        }
        if (this.taskSystem != null) {
            Player.TaskSystem.Builder tmpBuilder = Player.TaskSystem.newBuilder();
            final int tmpFieldCnt = this.taskSystem.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskSystem(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskSystem();
            }
        }  else if (builder.hasTaskSystem()) {
            // 清理TaskSystem
            builder.clearTaskSystem();
            fieldCnt++;
        }
        if (this.discountInfo != null) {
            Player.DiscountInfo.Builder tmpBuilder = Player.DiscountInfo.newBuilder();
            final int tmpFieldCnt = this.discountInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDiscountInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDiscountInfo();
            }
        }  else if (builder.hasDiscountInfo()) {
            // 清理DiscountInfo
            builder.clearDiscountInfo();
            fieldCnt++;
        }
        if (this.playerStoreModel != null) {
            Player.PlayerStoreModel.Builder tmpBuilder = Player.PlayerStoreModel.newBuilder();
            final int tmpFieldCnt = this.playerStoreModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerStoreModel();
            }
        }  else if (builder.hasPlayerStoreModel()) {
            // 清理PlayerStoreModel
            builder.clearPlayerStoreModel();
            fieldCnt++;
        }
        if (this.PlayerGuidanceModel != null) {
            Player.PlayerGuidanceModel.Builder tmpBuilder = Player.PlayerGuidanceModel.newBuilder();
            final int tmpFieldCnt = this.PlayerGuidanceModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerGuidanceModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerGuidanceModel();
            }
        }  else if (builder.hasPlayerGuidanceModel()) {
            // 清理PlayerGuidanceModel
            builder.clearPlayerGuidanceModel();
            fieldCnt++;
        }
        if (this.PlayerTechModel != null) {
            Player.PlayerTechnologyModel.Builder tmpBuilder = Player.PlayerTechnologyModel.newBuilder();
            final int tmpFieldCnt = this.PlayerTechModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerTechModel();
            }
        }  else if (builder.hasPlayerTechModel()) {
            // 清理PlayerTechModel
            builder.clearPlayerTechModel();
            fieldCnt++;
        }
        if (this.playerPlaneModel != null) {
            Player.PlayerPlaneModel.Builder tmpBuilder = Player.PlayerPlaneModel.newBuilder();
            final int tmpFieldCnt = this.playerPlaneModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerPlaneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerPlaneModel();
            }
        }  else if (builder.hasPlayerPlaneModel()) {
            // 清理PlayerPlaneModel
            builder.clearPlayerPlaneModel();
            fieldCnt++;
        }
        if (this.ChatPlayer != null) {
            Player.ChatPlayer.Builder tmpBuilder = Player.ChatPlayer.newBuilder();
            final int tmpFieldCnt = this.ChatPlayer.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChatPlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChatPlayer();
            }
        }  else if (builder.hasChatPlayer()) {
            // 清理ChatPlayer
            builder.clearChatPlayer();
            fieldCnt++;
        }
        if (this.newbieModel != null) {
            Player.PlayerNewbieModel.Builder tmpBuilder = Player.PlayerNewbieModel.newBuilder();
            final int tmpFieldCnt = this.newbieModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setNewbieModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearNewbieModel();
            }
        }  else if (builder.hasNewbieModel()) {
            // 清理NewbieModel
            builder.clearNewbieModel();
            fieldCnt++;
        }
        if (this.statisticModel != null) {
            Player.PlayerStatisticModel.Builder tmpBuilder = Player.PlayerStatisticModel.newBuilder();
            final int tmpFieldCnt = this.statisticModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStatisticModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStatisticModel();
            }
        }  else if (builder.hasStatisticModel()) {
            // 清理StatisticModel
            builder.clearStatisticModel();
            fieldCnt++;
        }
        if (this.redDotModel != null) {
            Player.PlayerRedDotModel.Builder tmpBuilder = Player.PlayerRedDotModel.newBuilder();
            final int tmpFieldCnt = this.redDotModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotModel();
            }
        }  else if (builder.hasRedDotModel()) {
            // 清理RedDotModel
            builder.clearRedDotModel();
            fieldCnt++;
        }
        if (this.playerHeroModel != null) {
            Player.PlayerHeroModel.Builder tmpBuilder = Player.PlayerHeroModel.newBuilder();
            final int tmpFieldCnt = this.playerHeroModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerHeroModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerHeroModel();
            }
        }  else if (builder.hasPlayerHeroModel()) {
            // 清理PlayerHeroModel
            builder.clearPlayerHeroModel();
            fieldCnt++;
        }
        if (this.avatarModel != null) {
            Player.PlayerAvatarModel.Builder tmpBuilder = Player.PlayerAvatarModel.newBuilder();
            final int tmpFieldCnt = this.avatarModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAvatarModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAvatarModel();
            }
        }  else if (builder.hasAvatarModel()) {
            // 清理AvatarModel
            builder.clearAvatarModel();
            fieldCnt++;
        }
        if (this.zoneModel != null) {
            Player.PlayerZoneModel.Builder tmpBuilder = Player.PlayerZoneModel.newBuilder();
            final int tmpFieldCnt = this.zoneModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneModel();
            }
        }  else if (builder.hasZoneModel()) {
            // 清理ZoneModel
            builder.clearZoneModel();
            fieldCnt++;
        }
        if (this.energyModel != null) {
            Player.PlayerEnergyModel.Builder tmpBuilder = Player.PlayerEnergyModel.newBuilder();
            final int tmpFieldCnt = this.energyModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setEnergyModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearEnergyModel();
            }
        }  else if (builder.hasEnergyModel()) {
            // 清理EnergyModel
            builder.clearEnergyModel();
            fieldCnt++;
        }
        if (this.playerRecruitModel != null) {
            Player.PlayerRecruitModel.Builder tmpBuilder = Player.PlayerRecruitModel.newBuilder();
            final int tmpFieldCnt = this.playerRecruitModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerRecruitModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerRecruitModel();
            }
        }  else if (builder.hasPlayerRecruitModel()) {
            // 清理PlayerRecruitModel
            builder.clearPlayerRecruitModel();
            fieldCnt++;
        }
        if (this.activityModel != null) {
            Player.PlayerActivityModel.Builder tmpBuilder = Player.PlayerActivityModel.newBuilder();
            final int tmpFieldCnt = this.activityModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivityModel();
            }
        }  else if (builder.hasActivityModel()) {
            // 清理ActivityModel
            builder.clearActivityModel();
            fieldCnt++;
        }
        if (this.mailModel != null) {
            Player.PlayerMailModel.Builder tmpBuilder = Player.PlayerMailModel.newBuilder();
            final int tmpFieldCnt = this.mailModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMailModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMailModel();
            }
        }  else if (builder.hasMailModel()) {
            // 清理MailModel
            builder.clearMailModel();
            fieldCnt++;
        }
        if (this.dungeonPlayer != null) {
            Player.DungeonPlayer.Builder tmpBuilder = Player.DungeonPlayer.newBuilder();
            final int tmpFieldCnt = this.dungeonPlayer.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonPlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonPlayer();
            }
        }  else if (builder.hasDungeonPlayer()) {
            // 清理DungeonPlayer
            builder.clearDungeonPlayer();
            fieldCnt++;
        }
        if (this.killMonsterModel != null) {
            Player.PlayerKillMonsterModel.Builder tmpBuilder = Player.PlayerKillMonsterModel.newBuilder();
            final int tmpFieldCnt = this.killMonsterModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKillMonsterModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKillMonsterModel();
            }
        }  else if (builder.hasKillMonsterModel()) {
            // 清理KillMonsterModel
            builder.clearKillMonsterModel();
            fieldCnt++;
        }
        if (this.mileStoneModel != null) {
            Player.PlayerMileStoneModel.Builder tmpBuilder = Player.PlayerMileStoneModel.newBuilder();
            final int tmpFieldCnt = this.mileStoneModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMileStoneModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMileStoneModel();
            }
        }  else if (builder.hasMileStoneModel()) {
            // 清理MileStoneModel
            builder.clearMileStoneModel();
            fieldCnt++;
        }
        if (this.dungeonModel != null) {
            Player.PlayerDungeonModel.Builder tmpBuilder = Player.PlayerDungeonModel.newBuilder();
            final int tmpFieldCnt = this.dungeonModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDungeonModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDungeonModel();
            }
        }  else if (builder.hasDungeonModel()) {
            // 清理DungeonModel
            builder.clearDungeonModel();
            fieldCnt++;
        }
        if (this.resetInfo != null) {
            Player.PlayerResetInfo.Builder tmpBuilder = Player.PlayerResetInfo.newBuilder();
            final int tmpFieldCnt = this.resetInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResetInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResetInfo();
            }
        }  else if (builder.hasResetInfo()) {
            // 清理ResetInfo
            builder.clearResetInfo();
            fieldCnt++;
        }
        if (this.settingModel != null) {
            Player.PlayerSettingModel.Builder tmpBuilder = Player.PlayerSettingModel.newBuilder();
            final int tmpFieldCnt = this.settingModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSettingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSettingModel();
            }
        }  else if (builder.hasSettingModel()) {
            // 清理SettingModel
            builder.clearSettingModel();
            fieldCnt++;
        }
        if (this.paymentModel != null) {
            Player.PlayerPaymentModel.Builder tmpBuilder = Player.PlayerPaymentModel.newBuilder();
            final int tmpFieldCnt = this.paymentModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPaymentModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPaymentModel();
            }
        }  else if (builder.hasPaymentModel()) {
            // 清理PaymentModel
            builder.clearPaymentModel();
            fieldCnt++;
        }
        if (this.friendPlayer != null) {
            Player.FriendPlayer.Builder tmpBuilder = Player.FriendPlayer.newBuilder();
            final int tmpFieldCnt = this.friendPlayer.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFriendPlayer(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFriendPlayer();
            }
        }  else if (builder.hasFriendPlayer()) {
            // 清理FriendPlayer
            builder.clearFriendPlayer();
            fieldCnt++;
        }
        if (this.vipModel != null) {
            Player.PlayerVipModel.Builder tmpBuilder = Player.PlayerVipModel.newBuilder();
            final int tmpFieldCnt = this.vipModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setVipModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearVipModel();
            }
        }  else if (builder.hasVipModel()) {
            // 清理VipModel
            builder.clearVipModel();
            fieldCnt++;
        }
        if (this.questModel != null) {
            Player.PlayerInnerQuestModel.Builder tmpBuilder = Player.PlayerInnerQuestModel.newBuilder();
            final int tmpFieldCnt = this.questModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setQuestModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearQuestModel();
            }
        }  else if (builder.hasQuestModel()) {
            // 清理QuestModel
            builder.clearQuestModel();
            fieldCnt++;
        }
        if (this.cityModel != null) {
            Player.PlayerCityModel.Builder tmpBuilder = Player.PlayerCityModel.newBuilder();
            final int tmpFieldCnt = this.cityModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCityModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCityModel();
            }
        }  else if (builder.hasCityModel()) {
            // 清理CityModel
            builder.clearCityModel();
            fieldCnt++;
        }
        if (this.dailyGoodsModel != null) {
            Player.PlayerDailyGoodsModel.Builder tmpBuilder = Player.PlayerDailyGoodsModel.newBuilder();
            final int tmpFieldCnt = this.dailyGoodsModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDailyGoodsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDailyGoodsModel();
            }
        }  else if (builder.hasDailyGoodsModel()) {
            // 清理DailyGoodsModel
            builder.clearDailyGoodsModel();
            fieldCnt++;
        }
        if (this.drawInfo != null) {
            Struct.Int32DrawInfoMap.Builder tmpBuilder = Struct.Int32DrawInfoMap.newBuilder();
            final int tmpFieldCnt = this.drawInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDrawInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDrawInfo();
            }
        }  else if (builder.hasDrawInfo()) {
            // 清理DrawInfo
            builder.clearDrawInfo();
            fieldCnt++;
        }
        if (this.devBuffSysNew != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSysNew.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSysNew();
            }
        }  else if (builder.hasDevBuffSysNew()) {
            // 清理DevBuffSysNew
            builder.clearDevBuffSysNew();
            fieldCnt++;
        }
        if (this.kingdomModel != null) {
            Player.PlayerKingdomModel.Builder tmpBuilder = Player.PlayerKingdomModel.newBuilder();
            final int tmpFieldCnt = this.kingdomModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setKingdomModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearKingdomModel();
            }
        }  else if (builder.hasKingdomModel()) {
            // 清理KingdomModel
            builder.clearKingdomModel();
            fieldCnt++;
        }
        if (this.zoneAdditionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.zoneAdditionSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneAdditionSys();
            }
        }  else if (builder.hasZoneAdditionSys()) {
            // 清理ZoneAdditionSys
            builder.clearZoneAdditionSys();
            fieldCnt++;
        }
        if (this.triggerBundleModel != null) {
            Player.PlayerTriggerBundleModel.Builder tmpBuilder = Player.PlayerTriggerBundleModel.newBuilder();
            final int tmpFieldCnt = this.triggerBundleModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerBundleModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerBundleModel();
            }
        }  else if (builder.hasTriggerBundleModel()) {
            // 清理TriggerBundleModel
            builder.clearTriggerBundleModel();
            fieldCnt++;
        }
        if (this.featureLockModel != null) {
            Player.PlayerFeatureLockModel.Builder tmpBuilder = Player.PlayerFeatureLockModel.newBuilder();
            final int tmpFieldCnt = this.featureLockModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFeatureLockModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFeatureLockModel();
            }
        }  else if (builder.hasFeatureLockModel()) {
            // 清理FeatureLockModel
            builder.clearFeatureLockModel();
            fieldCnt++;
        }
        if (this.battlePassModel != null) {
            Player.PlayerBattlePassModel.Builder tmpBuilder = Player.PlayerBattlePassModel.newBuilder();
            final int tmpFieldCnt = this.battlePassModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattlePassModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattlePassModel();
            }
        }  else if (builder.hasBattlePassModel()) {
            // 清理BattlePassModel
            builder.clearBattlePassModel();
            fieldCnt++;
        }
        if (this.getDataPatchVersion() != 0) {
            builder.setDataPatchVersion(this.getDataPatchVersion());
            fieldCnt++;
        }  else if (builder.hasDataPatchVersion()) {
            // 清理DataPatchVersion
            builder.clearDataPatchVersion();
            fieldCnt++;
        }
        if (this.additionSysNew != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSysNew.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSysNew(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSysNew();
            }
        }  else if (builder.hasAdditionSysNew()) {
            // 清理AdditionSysNew
            builder.clearAdditionSysNew();
            fieldCnt++;
        }
        if (this.contactsModel != null) {
            Player.PlayerContactsModel.Builder tmpBuilder = Player.PlayerContactsModel.newBuilder();
            final int tmpFieldCnt = this.contactsModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContactsModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContactsModel();
            }
        }  else if (builder.hasContactsModel()) {
            // 清理ContactsModel
            builder.clearContactsModel();
            fieldCnt++;
        }
        if (this.ratingModel != null) {
            Player.PlayerStoreRatingModel.Builder tmpBuilder = Player.PlayerStoreRatingModel.newBuilder();
            final int tmpFieldCnt = this.ratingModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRatingModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRatingModel();
            }
        }  else if (builder.hasRatingModel()) {
            // 清理RatingModel
            builder.clearRatingModel();
            fieldCnt++;
        }
        if (this.skynetModel != null) {
            Player.PlayerSkynetModel.Builder tmpBuilder = Player.PlayerSkynetModel.newBuilder();
            final int tmpFieldCnt = this.skynetModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkynetModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkynetModel();
            }
        }  else if (builder.hasSkynetModel()) {
            // 清理SkynetModel
            builder.clearSkynetModel();
            fieldCnt++;
        }
        if (this.achievementModel != null) {
            Player.PlayerAchievementModel.Builder tmpBuilder = Player.PlayerAchievementModel.newBuilder();
            final int tmpFieldCnt = this.achievementModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAchievementModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAchievementModel();
            }
        }  else if (builder.hasAchievementModel()) {
            // 清理AchievementModel
            builder.clearAchievementModel();
            fieldCnt++;
        }
        if (this.seasonBattlePassModel != null) {
            Player.PlayerBattlePassModel.Builder tmpBuilder = Player.PlayerBattlePassModel.newBuilder();
            final int tmpFieldCnt = this.seasonBattlePassModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSeasonBattlePassModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSeasonBattlePassModel();
            }
        }  else if (builder.hasSeasonBattlePassModel()) {
            // 清理SeasonBattlePassModel
            builder.clearSeasonBattlePassModel();
            fieldCnt++;
        }
        if (this.playerInnerBuildRHModel != null) {
            Player.PlayerInnerBuildRHModel.Builder tmpBuilder = Player.PlayerInnerBuildRHModel.newBuilder();
            final int tmpFieldCnt = this.playerInnerBuildRHModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerInnerBuildRHModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerInnerBuildRHModel();
            }
        }  else if (builder.hasPlayerInnerBuildRHModel()) {
            // 清理PlayerInnerBuildRHModel
            builder.clearPlayerInnerBuildRHModel();
            fieldCnt++;
        }
        if (this.playerCampaignModel != null) {
            Player.PlayerCampaignModel.Builder tmpBuilder = Player.PlayerCampaignModel.newBuilder();
            final int tmpFieldCnt = this.playerCampaignModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerCampaignModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerCampaignModel();
            }
        }  else if (builder.hasPlayerCampaignModel()) {
            // 清理PlayerCampaignModel
            builder.clearPlayerCampaignModel();
            fieldCnt++;
        }
        if (this.playerUnitModel != null) {
            Player.PlayerUnitModel.Builder tmpBuilder = Player.PlayerUnitModel.newBuilder();
            final int tmpFieldCnt = this.playerUnitModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerUnitModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerUnitModel();
            }
        }  else if (builder.hasPlayerUnitModel()) {
            // 清理PlayerUnitModel
            builder.clearPlayerUnitModel();
            fieldCnt++;
        }
        if (this.playerMissionModel != null) {
            Player.PlayerMissionModel.Builder tmpBuilder = Player.PlayerMissionModel.newBuilder();
            final int tmpFieldCnt = this.playerMissionModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerMissionModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerMissionModel();
            }
        }  else if (builder.hasPlayerMissionModel()) {
            // 清理PlayerMissionModel
            builder.clearPlayerMissionModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENID)) {
            builder.setOpenId(this.getOpenId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENE) && this.scene != null) {
            final boolean needClear = !builder.hasScene();
            final int tmpFieldCnt = this.scene.copyChangeToSs(builder.getSceneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScene();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEPLAYER) && this.scenePlayer != null) {
            final boolean needClear = !builder.hasScenePlayer();
            final int tmpFieldCnt = this.scenePlayer.copyChangeToSs(builder.getScenePlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScenePlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAN) && this.clan != null) {
            final boolean needClear = !builder.hasClan();
            final int tmpFieldCnt = this.clan.copyChangeToSs(builder.getClanBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClan();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRAGMENTS) && this.fragments != null) {
            final boolean needClear = !builder.hasFragments();
            final int tmpFieldCnt = this.fragments.copyChangeToSs(builder.getFragmentsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFragments();
            }
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            final boolean needClear = !builder.hasItems();
            final int tmpFieldCnt = this.items.copyChangeToSs(builder.getItemsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearItems();
            }
        }
        if (this.hasMark(FIELD_INDEX_PURSE) && this.purse != null) {
            final boolean needClear = !builder.hasPurse();
            final int tmpFieldCnt = this.purse.copyChangeToSs(builder.getPurseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPurse();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKMAP) && this.queueTaskMap != null) {
            final boolean needClear = !builder.hasQueueTaskMap();
            final int tmpFieldCnt = this.queueTaskMap.copyChangeToSs(builder.getQueueTaskMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQueueTaskMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSOLDIERINBUILDING) && this.playerSoldierInBuilding != null) {
            final boolean needClear = !builder.hasPlayerSoldierInBuilding();
            final int tmpFieldCnt = this.playerSoldierInBuilding.copyChangeToSs(builder.getPlayerSoldierInBuildingBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerSoldierInBuilding();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPOWERINFO) && this.playerPowerInfo != null) {
            final boolean needClear = !builder.hasPlayerPowerInfo();
            final int tmpFieldCnt = this.playerPowerInfo.copyChangeToSs(builder.getPlayerPowerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPowerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERDATARECORD) && this.playerDataRecord != null) {
            final boolean needClear = !builder.hasPlayerDataRecord();
            final int tmpFieldCnt = this.playerDataRecord.copyChangeToSs(builder.getPlayerDataRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerDataRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_BASICINFO) && this.basicInfo != null) {
            final boolean needClear = !builder.hasBasicInfo();
            final int tmpFieldCnt = this.basicInfo.copyChangeToSs(builder.getBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            final boolean needClear = !builder.hasFormation();
            final int tmpFieldCnt = this.formation.copyChangeToSs(builder.getFormationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormation();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEPRODUCE) && this.resourceProduce != null) {
            final boolean needClear = !builder.hasResourceProduce();
            final int tmpFieldCnt = this.resourceProduce.copyChangeToSs(builder.getResourceProduceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResourceProduce();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToSs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToSs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHSYS) && this.refreshSys != null) {
            final boolean needClear = !builder.hasRefreshSys();
            final int tmpFieldCnt = this.refreshSys.copyChangeToSs(builder.getRefreshSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_TASKSYSTEM) && this.taskSystem != null) {
            final boolean needClear = !builder.hasTaskSystem();
            final int tmpFieldCnt = this.taskSystem.copyChangeToSs(builder.getTaskSystemBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskSystem();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCOUNTINFO) && this.discountInfo != null) {
            final boolean needClear = !builder.hasDiscountInfo();
            final int tmpFieldCnt = this.discountInfo.copyChangeToSs(builder.getDiscountInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscountInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSTOREMODEL) && this.playerStoreModel != null) {
            final boolean needClear = !builder.hasPlayerStoreModel();
            final int tmpFieldCnt = this.playerStoreModel.copyChangeToSs(builder.getPlayerStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERGUIDANCEMODEL) && this.PlayerGuidanceModel != null) {
            final boolean needClear = !builder.hasPlayerGuidanceModel();
            final int tmpFieldCnt = this.PlayerGuidanceModel.copyChangeToSs(builder.getPlayerGuidanceModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerGuidanceModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTECHMODEL) && this.PlayerTechModel != null) {
            final boolean needClear = !builder.hasPlayerTechModel();
            final int tmpFieldCnt = this.PlayerTechModel.copyChangeToSs(builder.getPlayerTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEMODEL) && this.playerPlaneModel != null) {
            final boolean needClear = !builder.hasPlayerPlaneModel();
            final int tmpFieldCnt = this.playerPlaneModel.copyChangeToSs(builder.getPlayerPlaneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerPlaneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHATPLAYER) && this.ChatPlayer != null) {
            final boolean needClear = !builder.hasChatPlayer();
            final int tmpFieldCnt = this.ChatPlayer.copyChangeToSs(builder.getChatPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChatPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEMODEL) && this.newbieModel != null) {
            final boolean needClear = !builder.hasNewbieModel();
            final int tmpFieldCnt = this.newbieModel.copyChangeToSs(builder.getNewbieModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearNewbieModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STATISTICMODEL) && this.statisticModel != null) {
            final boolean needClear = !builder.hasStatisticModel();
            final int tmpFieldCnt = this.statisticModel.copyChangeToSs(builder.getStatisticModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStatisticModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToSs(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMODEL) && this.playerHeroModel != null) {
            final boolean needClear = !builder.hasPlayerHeroModel();
            final int tmpFieldCnt = this.playerHeroModel.copyChangeToSs(builder.getPlayerHeroModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_AVATARMODEL) && this.avatarModel != null) {
            final boolean needClear = !builder.hasAvatarModel();
            final int tmpFieldCnt = this.avatarModel.copyChangeToSs(builder.getAvatarModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAvatarModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            final boolean needClear = !builder.hasZoneModel();
            final int tmpFieldCnt = this.zoneModel.copyChangeToSs(builder.getZoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ENERGYMODEL) && this.energyModel != null) {
            final boolean needClear = !builder.hasEnergyModel();
            final int tmpFieldCnt = this.energyModel.copyChangeToSs(builder.getEnergyModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearEnergyModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERRECRUITMODEL) && this.playerRecruitModel != null) {
            final boolean needClear = !builder.hasPlayerRecruitModel();
            final int tmpFieldCnt = this.playerRecruitModel.copyChangeToSs(builder.getPlayerRecruitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerRecruitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            final boolean needClear = !builder.hasActivityModel();
            final int tmpFieldCnt = this.activityModel.copyChangeToSs(builder.getActivityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAILMODEL) && this.mailModel != null) {
            final boolean needClear = !builder.hasMailModel();
            final int tmpFieldCnt = this.mailModel.copyChangeToSs(builder.getMailModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMailModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONPLAYER) && this.dungeonPlayer != null) {
            final boolean needClear = !builder.hasDungeonPlayer();
            final int tmpFieldCnt = this.dungeonPlayer.copyChangeToSs(builder.getDungeonPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMODEL) && this.killMonsterModel != null) {
            final boolean needClear = !builder.hasKillMonsterModel();
            final int tmpFieldCnt = this.killMonsterModel.copyChangeToSs(builder.getKillMonsterModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKillMonsterModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEMODEL) && this.mileStoneModel != null) {
            final boolean needClear = !builder.hasMileStoneModel();
            final int tmpFieldCnt = this.mileStoneModel.copyChangeToSs(builder.getMileStoneModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMileStoneModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONMODEL) && this.dungeonModel != null) {
            final boolean needClear = !builder.hasDungeonModel();
            final int tmpFieldCnt = this.dungeonModel.copyChangeToSs(builder.getDungeonModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDungeonModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETINFO) && this.resetInfo != null) {
            final boolean needClear = !builder.hasResetInfo();
            final int tmpFieldCnt = this.resetInfo.copyChangeToSs(builder.getResetInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_SETTINGMODEL) && this.settingModel != null) {
            final boolean needClear = !builder.hasSettingModel();
            final int tmpFieldCnt = this.settingModel.copyChangeToSs(builder.getSettingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSettingModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYMENTMODEL) && this.paymentModel != null) {
            final boolean needClear = !builder.hasPaymentModel();
            final int tmpFieldCnt = this.paymentModel.copyChangeToSs(builder.getPaymentModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPaymentModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FRIENDPLAYER) && this.friendPlayer != null) {
            final boolean needClear = !builder.hasFriendPlayer();
            final int tmpFieldCnt = this.friendPlayer.copyChangeToSs(builder.getFriendPlayerBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFriendPlayer();
            }
        }
        if (this.hasMark(FIELD_INDEX_VIPMODEL) && this.vipModel != null) {
            final boolean needClear = !builder.hasVipModel();
            final int tmpFieldCnt = this.vipModel.copyChangeToSs(builder.getVipModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearVipModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_QUESTMODEL) && this.questModel != null) {
            final boolean needClear = !builder.hasQuestModel();
            final int tmpFieldCnt = this.questModel.copyChangeToSs(builder.getQuestModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearQuestModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CITYMODEL) && this.cityModel != null) {
            final boolean needClear = !builder.hasCityModel();
            final int tmpFieldCnt = this.cityModel.copyChangeToSs(builder.getCityModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCityModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DAILYGOODSMODEL) && this.dailyGoodsModel != null) {
            final boolean needClear = !builder.hasDailyGoodsModel();
            final int tmpFieldCnt = this.dailyGoodsModel.copyChangeToSs(builder.getDailyGoodsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDailyGoodsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DRAWINFO) && this.drawInfo != null) {
            final boolean needClear = !builder.hasDrawInfo();
            final int tmpFieldCnt = this.drawInfo.copyChangeToSs(builder.getDrawInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDrawInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            final boolean needClear = !builder.hasDevBuffSysNew();
            final int tmpFieldCnt = this.devBuffSysNew.copyChangeToSs(builder.getDevBuffSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            final boolean needClear = !builder.hasKingdomModel();
            final int tmpFieldCnt = this.kingdomModel.copyChangeToSs(builder.getKingdomModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearKingdomModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONEADDITIONSYS) && this.zoneAdditionSys != null) {
            final boolean needClear = !builder.hasZoneAdditionSys();
            final int tmpFieldCnt = this.zoneAdditionSys.copyChangeToSs(builder.getZoneAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEMODEL) && this.triggerBundleModel != null) {
            final boolean needClear = !builder.hasTriggerBundleModel();
            final int tmpFieldCnt = this.triggerBundleModel.copyChangeToSs(builder.getTriggerBundleModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerBundleModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_FEATURELOCKMODEL) && this.featureLockModel != null) {
            final boolean needClear = !builder.hasFeatureLockModel();
            final int tmpFieldCnt = this.featureLockModel.copyChangeToSs(builder.getFeatureLockModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFeatureLockModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPASSMODEL) && this.battlePassModel != null) {
            final boolean needClear = !builder.hasBattlePassModel();
            final int tmpFieldCnt = this.battlePassModel.copyChangeToSs(builder.getBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_DATAPATCHVERSION)) {
            builder.setDataPatchVersion(this.getDataPatchVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYSNEW) && this.additionSysNew != null) {
            final boolean needClear = !builder.hasAdditionSysNew();
            final int tmpFieldCnt = this.additionSysNew.copyChangeToSs(builder.getAdditionSysNewBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSysNew();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTACTSMODEL) && this.contactsModel != null) {
            final boolean needClear = !builder.hasContactsModel();
            final int tmpFieldCnt = this.contactsModel.copyChangeToSs(builder.getContactsModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContactsModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RATINGMODEL) && this.ratingModel != null) {
            final boolean needClear = !builder.hasRatingModel();
            final int tmpFieldCnt = this.ratingModel.copyChangeToSs(builder.getRatingModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRatingModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SKYNETMODEL) && this.skynetModel != null) {
            final boolean needClear = !builder.hasSkynetModel();
            final int tmpFieldCnt = this.skynetModel.copyChangeToSs(builder.getSkynetModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkynetModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTMODEL) && this.achievementModel != null) {
            final boolean needClear = !builder.hasAchievementModel();
            final int tmpFieldCnt = this.achievementModel.copyChangeToSs(builder.getAchievementModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAchievementModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SEASONBATTLEPASSMODEL) && this.seasonBattlePassModel != null) {
            final boolean needClear = !builder.hasSeasonBattlePassModel();
            final int tmpFieldCnt = this.seasonBattlePassModel.copyChangeToSs(builder.getSeasonBattlePassModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSeasonBattlePassModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINNERBUILDRHMODEL) && this.playerInnerBuildRHModel != null) {
            final boolean needClear = !builder.hasPlayerInnerBuildRHModel();
            final int tmpFieldCnt = this.playerInnerBuildRHModel.copyChangeToSs(builder.getPlayerInnerBuildRHModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerInnerBuildRHModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMPAIGNMODEL) && this.playerCampaignModel != null) {
            final boolean needClear = !builder.hasPlayerCampaignModel();
            final int tmpFieldCnt = this.playerCampaignModel.copyChangeToSs(builder.getPlayerCampaignModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerCampaignModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMODEL) && this.playerUnitModel != null) {
            final boolean needClear = !builder.hasPlayerUnitModel();
            final int tmpFieldCnt = this.playerUnitModel.copyChangeToSs(builder.getPlayerUnitModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERMISSIONMODEL) && this.playerMissionModel != null) {
            final boolean needClear = !builder.hasPlayerMissionModel();
            final int tmpFieldCnt = this.playerMissionModel.copyChangeToSs(builder.getPlayerMissionModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerMissionModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOpenId()) {
            this.innerSetOpenId(proto.getOpenId());
        } else {
            this.innerSetOpenId(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasScene()) {
            this.getScene().mergeFromSs(proto.getScene());
        } else {
            if (this.scene != null) {
                this.scene.mergeFromSs(proto.getScene());
            }
        }
        if (proto.hasScenePlayer()) {
            this.getScenePlayer().mergeFromSs(proto.getScenePlayer());
        } else {
            if (this.scenePlayer != null) {
                this.scenePlayer.mergeFromSs(proto.getScenePlayer());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClan()) {
            this.getClan().mergeFromSs(proto.getClan());
        } else {
            if (this.clan != null) {
                this.clan.mergeFromSs(proto.getClan());
            }
        }
        if (proto.hasFragments()) {
            this.getFragments().mergeFromSs(proto.getFragments());
        } else {
            if (this.fragments != null) {
                this.fragments.mergeFromSs(proto.getFragments());
            }
        }
        if (proto.hasItems()) {
            this.getItems().mergeFromSs(proto.getItems());
        } else {
            if (this.items != null) {
                this.items.mergeFromSs(proto.getItems());
            }
        }
        if (proto.hasPurse()) {
            this.getPurse().mergeFromSs(proto.getPurse());
        } else {
            if (this.purse != null) {
                this.purse.mergeFromSs(proto.getPurse());
            }
        }
        if (proto.hasQueueTaskMap()) {
            this.getQueueTaskMap().mergeFromSs(proto.getQueueTaskMap());
        } else {
            if (this.queueTaskMap != null) {
                this.queueTaskMap.mergeFromSs(proto.getQueueTaskMap());
            }
        }
        if (proto.hasPlayerSoldierInBuilding()) {
            this.getPlayerSoldierInBuilding().mergeFromSs(proto.getPlayerSoldierInBuilding());
        } else {
            if (this.playerSoldierInBuilding != null) {
                this.playerSoldierInBuilding.mergeFromSs(proto.getPlayerSoldierInBuilding());
            }
        }
        if (proto.hasPlayerPowerInfo()) {
            this.getPlayerPowerInfo().mergeFromSs(proto.getPlayerPowerInfo());
        } else {
            if (this.playerPowerInfo != null) {
                this.playerPowerInfo.mergeFromSs(proto.getPlayerPowerInfo());
            }
        }
        if (proto.hasPlayerDataRecord()) {
            this.getPlayerDataRecord().mergeFromSs(proto.getPlayerDataRecord());
        } else {
            if (this.playerDataRecord != null) {
                this.playerDataRecord.mergeFromSs(proto.getPlayerDataRecord());
            }
        }
        if (proto.hasBasicInfo()) {
            this.getBasicInfo().mergeFromSs(proto.getBasicInfo());
        } else {
            if (this.basicInfo != null) {
                this.basicInfo.mergeFromSs(proto.getBasicInfo());
            }
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeFromSs(proto.getFormation());
        } else {
            if (this.formation != null) {
                this.formation.mergeFromSs(proto.getFormation());
            }
        }
        if (proto.hasResourceProduce()) {
            this.getResourceProduce().mergeFromSs(proto.getResourceProduce());
        } else {
            if (this.resourceProduce != null) {
                this.resourceProduce.mergeFromSs(proto.getResourceProduce());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromSs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromSs(proto.getAdditionSys());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromSs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromSs(proto.getDevBuffSys());
            }
        }
        if (proto.hasRefreshSys()) {
            this.getRefreshSys().mergeFromSs(proto.getRefreshSys());
        } else {
            if (this.refreshSys != null) {
                this.refreshSys.mergeFromSs(proto.getRefreshSys());
            }
        }
        if (proto.hasTaskSystem()) {
            this.getTaskSystem().mergeFromSs(proto.getTaskSystem());
        } else {
            if (this.taskSystem != null) {
                this.taskSystem.mergeFromSs(proto.getTaskSystem());
            }
        }
        if (proto.hasDiscountInfo()) {
            this.getDiscountInfo().mergeFromSs(proto.getDiscountInfo());
        } else {
            if (this.discountInfo != null) {
                this.discountInfo.mergeFromSs(proto.getDiscountInfo());
            }
        }
        if (proto.hasPlayerStoreModel()) {
            this.getPlayerStoreModel().mergeFromSs(proto.getPlayerStoreModel());
        } else {
            if (this.playerStoreModel != null) {
                this.playerStoreModel.mergeFromSs(proto.getPlayerStoreModel());
            }
        }
        if (proto.hasPlayerGuidanceModel()) {
            this.getPlayerGuidanceModel().mergeFromSs(proto.getPlayerGuidanceModel());
        } else {
            if (this.PlayerGuidanceModel != null) {
                this.PlayerGuidanceModel.mergeFromSs(proto.getPlayerGuidanceModel());
            }
        }
        if (proto.hasPlayerTechModel()) {
            this.getPlayerTechModel().mergeFromSs(proto.getPlayerTechModel());
        } else {
            if (this.PlayerTechModel != null) {
                this.PlayerTechModel.mergeFromSs(proto.getPlayerTechModel());
            }
        }
        if (proto.hasPlayerPlaneModel()) {
            this.getPlayerPlaneModel().mergeFromSs(proto.getPlayerPlaneModel());
        } else {
            if (this.playerPlaneModel != null) {
                this.playerPlaneModel.mergeFromSs(proto.getPlayerPlaneModel());
            }
        }
        if (proto.hasChatPlayer()) {
            this.getChatPlayer().mergeFromSs(proto.getChatPlayer());
        } else {
            if (this.ChatPlayer != null) {
                this.ChatPlayer.mergeFromSs(proto.getChatPlayer());
            }
        }
        if (proto.hasNewbieModel()) {
            this.getNewbieModel().mergeFromSs(proto.getNewbieModel());
        } else {
            if (this.newbieModel != null) {
                this.newbieModel.mergeFromSs(proto.getNewbieModel());
            }
        }
        if (proto.hasStatisticModel()) {
            this.getStatisticModel().mergeFromSs(proto.getStatisticModel());
        } else {
            if (this.statisticModel != null) {
                this.statisticModel.mergeFromSs(proto.getStatisticModel());
            }
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeFromSs(proto.getRedDotModel());
        } else {
            if (this.redDotModel != null) {
                this.redDotModel.mergeFromSs(proto.getRedDotModel());
            }
        }
        if (proto.hasPlayerHeroModel()) {
            this.getPlayerHeroModel().mergeFromSs(proto.getPlayerHeroModel());
        } else {
            if (this.playerHeroModel != null) {
                this.playerHeroModel.mergeFromSs(proto.getPlayerHeroModel());
            }
        }
        if (proto.hasAvatarModel()) {
            this.getAvatarModel().mergeFromSs(proto.getAvatarModel());
        } else {
            if (this.avatarModel != null) {
                this.avatarModel.mergeFromSs(proto.getAvatarModel());
            }
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeFromSs(proto.getZoneModel());
        } else {
            if (this.zoneModel != null) {
                this.zoneModel.mergeFromSs(proto.getZoneModel());
            }
        }
        if (proto.hasEnergyModel()) {
            this.getEnergyModel().mergeFromSs(proto.getEnergyModel());
        } else {
            if (this.energyModel != null) {
                this.energyModel.mergeFromSs(proto.getEnergyModel());
            }
        }
        if (proto.hasPlayerRecruitModel()) {
            this.getPlayerRecruitModel().mergeFromSs(proto.getPlayerRecruitModel());
        } else {
            if (this.playerRecruitModel != null) {
                this.playerRecruitModel.mergeFromSs(proto.getPlayerRecruitModel());
            }
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeFromSs(proto.getActivityModel());
        } else {
            if (this.activityModel != null) {
                this.activityModel.mergeFromSs(proto.getActivityModel());
            }
        }
        if (proto.hasMailModel()) {
            this.getMailModel().mergeFromSs(proto.getMailModel());
        } else {
            if (this.mailModel != null) {
                this.mailModel.mergeFromSs(proto.getMailModel());
            }
        }
        if (proto.hasDungeonPlayer()) {
            this.getDungeonPlayer().mergeFromSs(proto.getDungeonPlayer());
        } else {
            if (this.dungeonPlayer != null) {
                this.dungeonPlayer.mergeFromSs(proto.getDungeonPlayer());
            }
        }
        if (proto.hasKillMonsterModel()) {
            this.getKillMonsterModel().mergeFromSs(proto.getKillMonsterModel());
        } else {
            if (this.killMonsterModel != null) {
                this.killMonsterModel.mergeFromSs(proto.getKillMonsterModel());
            }
        }
        if (proto.hasMileStoneModel()) {
            this.getMileStoneModel().mergeFromSs(proto.getMileStoneModel());
        } else {
            if (this.mileStoneModel != null) {
                this.mileStoneModel.mergeFromSs(proto.getMileStoneModel());
            }
        }
        if (proto.hasDungeonModel()) {
            this.getDungeonModel().mergeFromSs(proto.getDungeonModel());
        } else {
            if (this.dungeonModel != null) {
                this.dungeonModel.mergeFromSs(proto.getDungeonModel());
            }
        }
        if (proto.hasResetInfo()) {
            this.getResetInfo().mergeFromSs(proto.getResetInfo());
        } else {
            if (this.resetInfo != null) {
                this.resetInfo.mergeFromSs(proto.getResetInfo());
            }
        }
        if (proto.hasSettingModel()) {
            this.getSettingModel().mergeFromSs(proto.getSettingModel());
        } else {
            if (this.settingModel != null) {
                this.settingModel.mergeFromSs(proto.getSettingModel());
            }
        }
        if (proto.hasPaymentModel()) {
            this.getPaymentModel().mergeFromSs(proto.getPaymentModel());
        } else {
            if (this.paymentModel != null) {
                this.paymentModel.mergeFromSs(proto.getPaymentModel());
            }
        }
        if (proto.hasFriendPlayer()) {
            this.getFriendPlayer().mergeFromSs(proto.getFriendPlayer());
        } else {
            if (this.friendPlayer != null) {
                this.friendPlayer.mergeFromSs(proto.getFriendPlayer());
            }
        }
        if (proto.hasVipModel()) {
            this.getVipModel().mergeFromSs(proto.getVipModel());
        } else {
            if (this.vipModel != null) {
                this.vipModel.mergeFromSs(proto.getVipModel());
            }
        }
        if (proto.hasQuestModel()) {
            this.getQuestModel().mergeFromSs(proto.getQuestModel());
        } else {
            if (this.questModel != null) {
                this.questModel.mergeFromSs(proto.getQuestModel());
            }
        }
        if (proto.hasCityModel()) {
            this.getCityModel().mergeFromSs(proto.getCityModel());
        } else {
            if (this.cityModel != null) {
                this.cityModel.mergeFromSs(proto.getCityModel());
            }
        }
        if (proto.hasDailyGoodsModel()) {
            this.getDailyGoodsModel().mergeFromSs(proto.getDailyGoodsModel());
        } else {
            if (this.dailyGoodsModel != null) {
                this.dailyGoodsModel.mergeFromSs(proto.getDailyGoodsModel());
            }
        }
        if (proto.hasDrawInfo()) {
            this.getDrawInfo().mergeFromSs(proto.getDrawInfo());
        } else {
            if (this.drawInfo != null) {
                this.drawInfo.mergeFromSs(proto.getDrawInfo());
            }
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeFromSs(proto.getDevBuffSysNew());
        } else {
            if (this.devBuffSysNew != null) {
                this.devBuffSysNew.mergeFromSs(proto.getDevBuffSysNew());
            }
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeFromSs(proto.getKingdomModel());
        } else {
            if (this.kingdomModel != null) {
                this.kingdomModel.mergeFromSs(proto.getKingdomModel());
            }
        }
        if (proto.hasZoneAdditionSys()) {
            this.getZoneAdditionSys().mergeFromSs(proto.getZoneAdditionSys());
        } else {
            if (this.zoneAdditionSys != null) {
                this.zoneAdditionSys.mergeFromSs(proto.getZoneAdditionSys());
            }
        }
        if (proto.hasTriggerBundleModel()) {
            this.getTriggerBundleModel().mergeFromSs(proto.getTriggerBundleModel());
        } else {
            if (this.triggerBundleModel != null) {
                this.triggerBundleModel.mergeFromSs(proto.getTriggerBundleModel());
            }
        }
        if (proto.hasFeatureLockModel()) {
            this.getFeatureLockModel().mergeFromSs(proto.getFeatureLockModel());
        } else {
            if (this.featureLockModel != null) {
                this.featureLockModel.mergeFromSs(proto.getFeatureLockModel());
            }
        }
        if (proto.hasBattlePassModel()) {
            this.getBattlePassModel().mergeFromSs(proto.getBattlePassModel());
        } else {
            if (this.battlePassModel != null) {
                this.battlePassModel.mergeFromSs(proto.getBattlePassModel());
            }
        }
        if (proto.hasDataPatchVersion()) {
            this.innerSetDataPatchVersion(proto.getDataPatchVersion());
        } else {
            this.innerSetDataPatchVersion(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAdditionSysNew()) {
            this.getAdditionSysNew().mergeFromSs(proto.getAdditionSysNew());
        } else {
            if (this.additionSysNew != null) {
                this.additionSysNew.mergeFromSs(proto.getAdditionSysNew());
            }
        }
        if (proto.hasContactsModel()) {
            this.getContactsModel().mergeFromSs(proto.getContactsModel());
        } else {
            if (this.contactsModel != null) {
                this.contactsModel.mergeFromSs(proto.getContactsModel());
            }
        }
        if (proto.hasRatingModel()) {
            this.getRatingModel().mergeFromSs(proto.getRatingModel());
        } else {
            if (this.ratingModel != null) {
                this.ratingModel.mergeFromSs(proto.getRatingModel());
            }
        }
        if (proto.hasSkynetModel()) {
            this.getSkynetModel().mergeFromSs(proto.getSkynetModel());
        } else {
            if (this.skynetModel != null) {
                this.skynetModel.mergeFromSs(proto.getSkynetModel());
            }
        }
        if (proto.hasAchievementModel()) {
            this.getAchievementModel().mergeFromSs(proto.getAchievementModel());
        } else {
            if (this.achievementModel != null) {
                this.achievementModel.mergeFromSs(proto.getAchievementModel());
            }
        }
        if (proto.hasSeasonBattlePassModel()) {
            this.getSeasonBattlePassModel().mergeFromSs(proto.getSeasonBattlePassModel());
        } else {
            if (this.seasonBattlePassModel != null) {
                this.seasonBattlePassModel.mergeFromSs(proto.getSeasonBattlePassModel());
            }
        }
        if (proto.hasPlayerInnerBuildRHModel()) {
            this.getPlayerInnerBuildRHModel().mergeFromSs(proto.getPlayerInnerBuildRHModel());
        } else {
            if (this.playerInnerBuildRHModel != null) {
                this.playerInnerBuildRHModel.mergeFromSs(proto.getPlayerInnerBuildRHModel());
            }
        }
        if (proto.hasPlayerCampaignModel()) {
            this.getPlayerCampaignModel().mergeFromSs(proto.getPlayerCampaignModel());
        } else {
            if (this.playerCampaignModel != null) {
                this.playerCampaignModel.mergeFromSs(proto.getPlayerCampaignModel());
            }
        }
        if (proto.hasPlayerUnitModel()) {
            this.getPlayerUnitModel().mergeFromSs(proto.getPlayerUnitModel());
        } else {
            if (this.playerUnitModel != null) {
                this.playerUnitModel.mergeFromSs(proto.getPlayerUnitModel());
            }
        }
        if (proto.hasPlayerMissionModel()) {
            this.getPlayerMissionModel().mergeFromSs(proto.getPlayerMissionModel());
        } else {
            if (this.playerMissionModel != null) {
                this.playerMissionModel.mergeFromSs(proto.getPlayerMissionModel());
            }
        }
        this.markAll();
        return PlayerProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasOpenId()) {
            this.setOpenId(proto.getOpenId());
            fieldCnt++;
        }
        if (proto.hasScene()) {
            this.getScene().mergeChangeFromSs(proto.getScene());
            fieldCnt++;
        }
        if (proto.hasScenePlayer()) {
            this.getScenePlayer().mergeChangeFromSs(proto.getScenePlayer());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasClan()) {
            this.getClan().mergeChangeFromSs(proto.getClan());
            fieldCnt++;
        }
        if (proto.hasFragments()) {
            this.getFragments().mergeChangeFromSs(proto.getFragments());
            fieldCnt++;
        }
        if (proto.hasItems()) {
            this.getItems().mergeChangeFromSs(proto.getItems());
            fieldCnt++;
        }
        if (proto.hasPurse()) {
            this.getPurse().mergeChangeFromSs(proto.getPurse());
            fieldCnt++;
        }
        if (proto.hasQueueTaskMap()) {
            this.getQueueTaskMap().mergeChangeFromSs(proto.getQueueTaskMap());
            fieldCnt++;
        }
        if (proto.hasPlayerSoldierInBuilding()) {
            this.getPlayerSoldierInBuilding().mergeChangeFromSs(proto.getPlayerSoldierInBuilding());
            fieldCnt++;
        }
        if (proto.hasPlayerPowerInfo()) {
            this.getPlayerPowerInfo().mergeChangeFromSs(proto.getPlayerPowerInfo());
            fieldCnt++;
        }
        if (proto.hasPlayerDataRecord()) {
            this.getPlayerDataRecord().mergeChangeFromSs(proto.getPlayerDataRecord());
            fieldCnt++;
        }
        if (proto.hasBasicInfo()) {
            this.getBasicInfo().mergeChangeFromSs(proto.getBasicInfo());
            fieldCnt++;
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeChangeFromSs(proto.getFormation());
            fieldCnt++;
        }
        if (proto.hasResourceProduce()) {
            this.getResourceProduce().mergeChangeFromSs(proto.getResourceProduce());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromSs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromSs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasRefreshSys()) {
            this.getRefreshSys().mergeChangeFromSs(proto.getRefreshSys());
            fieldCnt++;
        }
        if (proto.hasTaskSystem()) {
            this.getTaskSystem().mergeChangeFromSs(proto.getTaskSystem());
            fieldCnt++;
        }
        if (proto.hasDiscountInfo()) {
            this.getDiscountInfo().mergeChangeFromSs(proto.getDiscountInfo());
            fieldCnt++;
        }
        if (proto.hasPlayerStoreModel()) {
            this.getPlayerStoreModel().mergeChangeFromSs(proto.getPlayerStoreModel());
            fieldCnt++;
        }
        if (proto.hasPlayerGuidanceModel()) {
            this.getPlayerGuidanceModel().mergeChangeFromSs(proto.getPlayerGuidanceModel());
            fieldCnt++;
        }
        if (proto.hasPlayerTechModel()) {
            this.getPlayerTechModel().mergeChangeFromSs(proto.getPlayerTechModel());
            fieldCnt++;
        }
        if (proto.hasPlayerPlaneModel()) {
            this.getPlayerPlaneModel().mergeChangeFromSs(proto.getPlayerPlaneModel());
            fieldCnt++;
        }
        if (proto.hasChatPlayer()) {
            this.getChatPlayer().mergeChangeFromSs(proto.getChatPlayer());
            fieldCnt++;
        }
        if (proto.hasNewbieModel()) {
            this.getNewbieModel().mergeChangeFromSs(proto.getNewbieModel());
            fieldCnt++;
        }
        if (proto.hasStatisticModel()) {
            this.getStatisticModel().mergeChangeFromSs(proto.getStatisticModel());
            fieldCnt++;
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeChangeFromSs(proto.getRedDotModel());
            fieldCnt++;
        }
        if (proto.hasPlayerHeroModel()) {
            this.getPlayerHeroModel().mergeChangeFromSs(proto.getPlayerHeroModel());
            fieldCnt++;
        }
        if (proto.hasAvatarModel()) {
            this.getAvatarModel().mergeChangeFromSs(proto.getAvatarModel());
            fieldCnt++;
        }
        if (proto.hasZoneModel()) {
            this.getZoneModel().mergeChangeFromSs(proto.getZoneModel());
            fieldCnt++;
        }
        if (proto.hasEnergyModel()) {
            this.getEnergyModel().mergeChangeFromSs(proto.getEnergyModel());
            fieldCnt++;
        }
        if (proto.hasPlayerRecruitModel()) {
            this.getPlayerRecruitModel().mergeChangeFromSs(proto.getPlayerRecruitModel());
            fieldCnt++;
        }
        if (proto.hasActivityModel()) {
            this.getActivityModel().mergeChangeFromSs(proto.getActivityModel());
            fieldCnt++;
        }
        if (proto.hasMailModel()) {
            this.getMailModel().mergeChangeFromSs(proto.getMailModel());
            fieldCnt++;
        }
        if (proto.hasDungeonPlayer()) {
            this.getDungeonPlayer().mergeChangeFromSs(proto.getDungeonPlayer());
            fieldCnt++;
        }
        if (proto.hasKillMonsterModel()) {
            this.getKillMonsterModel().mergeChangeFromSs(proto.getKillMonsterModel());
            fieldCnt++;
        }
        if (proto.hasMileStoneModel()) {
            this.getMileStoneModel().mergeChangeFromSs(proto.getMileStoneModel());
            fieldCnt++;
        }
        if (proto.hasDungeonModel()) {
            this.getDungeonModel().mergeChangeFromSs(proto.getDungeonModel());
            fieldCnt++;
        }
        if (proto.hasResetInfo()) {
            this.getResetInfo().mergeChangeFromSs(proto.getResetInfo());
            fieldCnt++;
        }
        if (proto.hasSettingModel()) {
            this.getSettingModel().mergeChangeFromSs(proto.getSettingModel());
            fieldCnt++;
        }
        if (proto.hasPaymentModel()) {
            this.getPaymentModel().mergeChangeFromSs(proto.getPaymentModel());
            fieldCnt++;
        }
        if (proto.hasFriendPlayer()) {
            this.getFriendPlayer().mergeChangeFromSs(proto.getFriendPlayer());
            fieldCnt++;
        }
        if (proto.hasVipModel()) {
            this.getVipModel().mergeChangeFromSs(proto.getVipModel());
            fieldCnt++;
        }
        if (proto.hasQuestModel()) {
            this.getQuestModel().mergeChangeFromSs(proto.getQuestModel());
            fieldCnt++;
        }
        if (proto.hasCityModel()) {
            this.getCityModel().mergeChangeFromSs(proto.getCityModel());
            fieldCnt++;
        }
        if (proto.hasDailyGoodsModel()) {
            this.getDailyGoodsModel().mergeChangeFromSs(proto.getDailyGoodsModel());
            fieldCnt++;
        }
        if (proto.hasDrawInfo()) {
            this.getDrawInfo().mergeChangeFromSs(proto.getDrawInfo());
            fieldCnt++;
        }
        if (proto.hasDevBuffSysNew()) {
            this.getDevBuffSysNew().mergeChangeFromSs(proto.getDevBuffSysNew());
            fieldCnt++;
        }
        if (proto.hasKingdomModel()) {
            this.getKingdomModel().mergeChangeFromSs(proto.getKingdomModel());
            fieldCnt++;
        }
        if (proto.hasZoneAdditionSys()) {
            this.getZoneAdditionSys().mergeChangeFromSs(proto.getZoneAdditionSys());
            fieldCnt++;
        }
        if (proto.hasTriggerBundleModel()) {
            this.getTriggerBundleModel().mergeChangeFromSs(proto.getTriggerBundleModel());
            fieldCnt++;
        }
        if (proto.hasFeatureLockModel()) {
            this.getFeatureLockModel().mergeChangeFromSs(proto.getFeatureLockModel());
            fieldCnt++;
        }
        if (proto.hasBattlePassModel()) {
            this.getBattlePassModel().mergeChangeFromSs(proto.getBattlePassModel());
            fieldCnt++;
        }
        if (proto.hasDataPatchVersion()) {
            this.setDataPatchVersion(proto.getDataPatchVersion());
            fieldCnt++;
        }
        if (proto.hasAdditionSysNew()) {
            this.getAdditionSysNew().mergeChangeFromSs(proto.getAdditionSysNew());
            fieldCnt++;
        }
        if (proto.hasContactsModel()) {
            this.getContactsModel().mergeChangeFromSs(proto.getContactsModel());
            fieldCnt++;
        }
        if (proto.hasRatingModel()) {
            this.getRatingModel().mergeChangeFromSs(proto.getRatingModel());
            fieldCnt++;
        }
        if (proto.hasSkynetModel()) {
            this.getSkynetModel().mergeChangeFromSs(proto.getSkynetModel());
            fieldCnt++;
        }
        if (proto.hasAchievementModel()) {
            this.getAchievementModel().mergeChangeFromSs(proto.getAchievementModel());
            fieldCnt++;
        }
        if (proto.hasSeasonBattlePassModel()) {
            this.getSeasonBattlePassModel().mergeChangeFromSs(proto.getSeasonBattlePassModel());
            fieldCnt++;
        }
        if (proto.hasPlayerInnerBuildRHModel()) {
            this.getPlayerInnerBuildRHModel().mergeChangeFromSs(proto.getPlayerInnerBuildRHModel());
            fieldCnt++;
        }
        if (proto.hasPlayerCampaignModel()) {
            this.getPlayerCampaignModel().mergeChangeFromSs(proto.getPlayerCampaignModel());
            fieldCnt++;
        }
        if (proto.hasPlayerUnitModel()) {
            this.getPlayerUnitModel().mergeChangeFromSs(proto.getPlayerUnitModel());
            fieldCnt++;
        }
        if (proto.hasPlayerMissionModel()) {
            this.getPlayerMissionModel().mergeChangeFromSs(proto.getPlayerMissionModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerEntity.Builder builder = PlayerEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SCENE) && this.scene != null) {
            this.scene.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCENEPLAYER) && this.scenePlayer != null) {
            this.scenePlayer.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLAN) && this.clan != null) {
            this.clan.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FRAGMENTS) && this.fragments != null) {
            this.fragments.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ITEMS) && this.items != null) {
            this.items.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PURSE) && this.purse != null) {
            this.purse.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_QUEUETASKMAP) && this.queueTaskMap != null) {
            this.queueTaskMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSOLDIERINBUILDING) && this.playerSoldierInBuilding != null) {
            this.playerSoldierInBuilding.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPOWERINFO) && this.playerPowerInfo != null) {
            this.playerPowerInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERDATARECORD) && this.playerDataRecord != null) {
            this.playerDataRecord.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BASICINFO) && this.basicInfo != null) {
            this.basicInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            this.formation.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESOURCEPRODUCE) && this.resourceProduce != null) {
            this.resourceProduce.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            this.additionSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            this.devBuffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REFRESHSYS) && this.refreshSys != null) {
            this.refreshSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TASKSYSTEM) && this.taskSystem != null) {
            this.taskSystem.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DISCOUNTINFO) && this.discountInfo != null) {
            this.discountInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERSTOREMODEL) && this.playerStoreModel != null) {
            this.playerStoreModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERGUIDANCEMODEL) && this.PlayerGuidanceModel != null) {
            this.PlayerGuidanceModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERTECHMODEL) && this.PlayerTechModel != null) {
            this.PlayerTechModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEMODEL) && this.playerPlaneModel != null) {
            this.playerPlaneModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CHATPLAYER) && this.ChatPlayer != null) {
            this.ChatPlayer.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_NEWBIEMODEL) && this.newbieModel != null) {
            this.newbieModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STATISTICMODEL) && this.statisticModel != null) {
            this.statisticModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            this.redDotModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMODEL) && this.playerHeroModel != null) {
            this.playerHeroModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_AVATARMODEL) && this.avatarModel != null) {
            this.avatarModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ZONEMODEL) && this.zoneModel != null) {
            this.zoneModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ENERGYMODEL) && this.energyModel != null) {
            this.energyModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERRECRUITMODEL) && this.playerRecruitModel != null) {
            this.playerRecruitModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITYMODEL) && this.activityModel != null) {
            this.activityModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MAILMODEL) && this.mailModel != null) {
            this.mailModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONPLAYER) && this.dungeonPlayer != null) {
            this.dungeonPlayer.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_KILLMONSTERMODEL) && this.killMonsterModel != null) {
            this.killMonsterModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MILESTONEMODEL) && this.mileStoneModel != null) {
            this.mileStoneModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DUNGEONMODEL) && this.dungeonModel != null) {
            this.dungeonModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESETINFO) && this.resetInfo != null) {
            this.resetInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SETTINGMODEL) && this.settingModel != null) {
            this.settingModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PAYMENTMODEL) && this.paymentModel != null) {
            this.paymentModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FRIENDPLAYER) && this.friendPlayer != null) {
            this.friendPlayer.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_VIPMODEL) && this.vipModel != null) {
            this.vipModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_QUESTMODEL) && this.questModel != null) {
            this.questModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CITYMODEL) && this.cityModel != null) {
            this.cityModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DAILYGOODSMODEL) && this.dailyGoodsModel != null) {
            this.dailyGoodsModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DRAWINFO) && this.drawInfo != null) {
            this.drawInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYSNEW) && this.devBuffSysNew != null) {
            this.devBuffSysNew.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMMODEL) && this.kingdomModel != null) {
            this.kingdomModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ZONEADDITIONSYS) && this.zoneAdditionSys != null) {
            this.zoneAdditionSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERBUNDLEMODEL) && this.triggerBundleModel != null) {
            this.triggerBundleModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FEATURELOCKMODEL) && this.featureLockModel != null) {
            this.featureLockModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPASSMODEL) && this.battlePassModel != null) {
            this.battlePassModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYSNEW) && this.additionSysNew != null) {
            this.additionSysNew.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CONTACTSMODEL) && this.contactsModel != null) {
            this.contactsModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RATINGMODEL) && this.ratingModel != null) {
            this.ratingModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SKYNETMODEL) && this.skynetModel != null) {
            this.skynetModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTMODEL) && this.achievementModel != null) {
            this.achievementModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SEASONBATTLEPASSMODEL) && this.seasonBattlePassModel != null) {
            this.seasonBattlePassModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERINNERBUILDRHMODEL) && this.playerInnerBuildRHModel != null) {
            this.playerInnerBuildRHModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERCAMPAIGNMODEL) && this.playerCampaignModel != null) {
            this.playerCampaignModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMODEL) && this.playerUnitModel != null) {
            this.playerUnitModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PLAYERMISSIONMODEL) && this.playerMissionModel != null) {
            this.playerMissionModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.scene != null) {
            this.scene.markAll();
        }
        if (this.scenePlayer != null) {
            this.scenePlayer.markAll();
        }
        if (this.clan != null) {
            this.clan.markAll();
        }
        if (this.fragments != null) {
            this.fragments.markAll();
        }
        if (this.items != null) {
            this.items.markAll();
        }
        if (this.purse != null) {
            this.purse.markAll();
        }
        if (this.queueTaskMap != null) {
            this.queueTaskMap.markAll();
        }
        if (this.playerSoldierInBuilding != null) {
            this.playerSoldierInBuilding.markAll();
        }
        if (this.playerPowerInfo != null) {
            this.playerPowerInfo.markAll();
        }
        if (this.playerDataRecord != null) {
            this.playerDataRecord.markAll();
        }
        if (this.basicInfo != null) {
            this.basicInfo.markAll();
        }
        if (this.formation != null) {
            this.formation.markAll();
        }
        if (this.resourceProduce != null) {
            this.resourceProduce.markAll();
        }
        if (this.additionSys != null) {
            this.additionSys.markAll();
        }
        if (this.devBuffSys != null) {
            this.devBuffSys.markAll();
        }
        if (this.refreshSys != null) {
            this.refreshSys.markAll();
        }
        if (this.taskSystem != null) {
            this.taskSystem.markAll();
        }
        if (this.discountInfo != null) {
            this.discountInfo.markAll();
        }
        if (this.playerStoreModel != null) {
            this.playerStoreModel.markAll();
        }
        if (this.PlayerGuidanceModel != null) {
            this.PlayerGuidanceModel.markAll();
        }
        if (this.PlayerTechModel != null) {
            this.PlayerTechModel.markAll();
        }
        if (this.playerPlaneModel != null) {
            this.playerPlaneModel.markAll();
        }
        if (this.ChatPlayer != null) {
            this.ChatPlayer.markAll();
        }
        if (this.newbieModel != null) {
            this.newbieModel.markAll();
        }
        if (this.statisticModel != null) {
            this.statisticModel.markAll();
        }
        if (this.redDotModel != null) {
            this.redDotModel.markAll();
        }
        if (this.playerHeroModel != null) {
            this.playerHeroModel.markAll();
        }
        if (this.avatarModel != null) {
            this.avatarModel.markAll();
        }
        if (this.zoneModel != null) {
            this.zoneModel.markAll();
        }
        if (this.energyModel != null) {
            this.energyModel.markAll();
        }
        if (this.playerRecruitModel != null) {
            this.playerRecruitModel.markAll();
        }
        if (this.activityModel != null) {
            this.activityModel.markAll();
        }
        if (this.mailModel != null) {
            this.mailModel.markAll();
        }
        if (this.dungeonPlayer != null) {
            this.dungeonPlayer.markAll();
        }
        if (this.killMonsterModel != null) {
            this.killMonsterModel.markAll();
        }
        if (this.mileStoneModel != null) {
            this.mileStoneModel.markAll();
        }
        if (this.dungeonModel != null) {
            this.dungeonModel.markAll();
        }
        if (this.resetInfo != null) {
            this.resetInfo.markAll();
        }
        if (this.settingModel != null) {
            this.settingModel.markAll();
        }
        if (this.paymentModel != null) {
            this.paymentModel.markAll();
        }
        if (this.friendPlayer != null) {
            this.friendPlayer.markAll();
        }
        if (this.vipModel != null) {
            this.vipModel.markAll();
        }
        if (this.questModel != null) {
            this.questModel.markAll();
        }
        if (this.cityModel != null) {
            this.cityModel.markAll();
        }
        if (this.dailyGoodsModel != null) {
            this.dailyGoodsModel.markAll();
        }
        if (this.drawInfo != null) {
            this.drawInfo.markAll();
        }
        if (this.devBuffSysNew != null) {
            this.devBuffSysNew.markAll();
        }
        if (this.kingdomModel != null) {
            this.kingdomModel.markAll();
        }
        if (this.zoneAdditionSys != null) {
            this.zoneAdditionSys.markAll();
        }
        if (this.triggerBundleModel != null) {
            this.triggerBundleModel.markAll();
        }
        if (this.featureLockModel != null) {
            this.featureLockModel.markAll();
        }
        if (this.battlePassModel != null) {
            this.battlePassModel.markAll();
        }
        if (this.additionSysNew != null) {
            this.additionSysNew.markAll();
        }
        if (this.contactsModel != null) {
            this.contactsModel.markAll();
        }
        if (this.ratingModel != null) {
            this.ratingModel.markAll();
        }
        if (this.skynetModel != null) {
            this.skynetModel.markAll();
        }
        if (this.achievementModel != null) {
            this.achievementModel.markAll();
        }
        if (this.seasonBattlePassModel != null) {
            this.seasonBattlePassModel.markAll();
        }
        if (this.playerInnerBuildRHModel != null) {
            this.playerInnerBuildRHModel.markAll();
        }
        if (this.playerCampaignModel != null) {
            this.playerCampaignModel.markAll();
        }
        if (this.playerUnitModel != null) {
            this.playerUnitModel.markAll();
        }
        if (this.playerMissionModel != null) {
            this.playerMissionModel.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("PlayerProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("PlayerProp");
            }
            this.markParentNode();
            return;
        }
        if (inWhichMarkBits == 1) {
            this.markBits1 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("PlayerProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        if (inWhichMarkBits == 1) {
            this.markBits1 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        if (inWhichMarkBits == 1) {
            return (this.markBits1 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        mark |= this.markBits1;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerProp)) {
            return false;
        }
        final PlayerProp otherNode = (PlayerProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.openId, otherNode.openId)) {
            return false;
        }
        if (!this.getScene().compareDataTo(otherNode.getScene())) {
            return false;
        }
        if (!this.getScenePlayer().compareDataTo(otherNode.getScenePlayer())) {
            return false;
        }
        if (this.createTime != otherNode.createTime) {
            return false;
        }
        if (!this.getClan().compareDataTo(otherNode.getClan())) {
            return false;
        }
        if (!this.getFragments().compareDataTo(otherNode.getFragments())) {
            return false;
        }
        if (!this.getItems().compareDataTo(otherNode.getItems())) {
            return false;
        }
        if (!this.getPurse().compareDataTo(otherNode.getPurse())) {
            return false;
        }
        if (!this.getQueueTaskMap().compareDataTo(otherNode.getQueueTaskMap())) {
            return false;
        }
        if (!this.getPlayerSoldierInBuilding().compareDataTo(otherNode.getPlayerSoldierInBuilding())) {
            return false;
        }
        if (!this.getPlayerPowerInfo().compareDataTo(otherNode.getPlayerPowerInfo())) {
            return false;
        }
        if (!this.getPlayerDataRecord().compareDataTo(otherNode.getPlayerDataRecord())) {
            return false;
        }
        if (!this.getBasicInfo().compareDataTo(otherNode.getBasicInfo())) {
            return false;
        }
        if (!this.getFormation().compareDataTo(otherNode.getFormation())) {
            return false;
        }
        if (!this.getResourceProduce().compareDataTo(otherNode.getResourceProduce())) {
            return false;
        }
        if (!this.getAdditionSys().compareDataTo(otherNode.getAdditionSys())) {
            return false;
        }
        if (!this.getDevBuffSys().compareDataTo(otherNode.getDevBuffSys())) {
            return false;
        }
        if (!this.getRefreshSys().compareDataTo(otherNode.getRefreshSys())) {
            return false;
        }
        if (!this.getTaskSystem().compareDataTo(otherNode.getTaskSystem())) {
            return false;
        }
        if (!this.getDiscountInfo().compareDataTo(otherNode.getDiscountInfo())) {
            return false;
        }
        if (!this.getPlayerStoreModel().compareDataTo(otherNode.getPlayerStoreModel())) {
            return false;
        }
        if (!this.getPlayerGuidanceModel().compareDataTo(otherNode.getPlayerGuidanceModel())) {
            return false;
        }
        if (!this.getPlayerTechModel().compareDataTo(otherNode.getPlayerTechModel())) {
            return false;
        }
        if (!this.getPlayerPlaneModel().compareDataTo(otherNode.getPlayerPlaneModel())) {
            return false;
        }
        if (!this.getChatPlayer().compareDataTo(otherNode.getChatPlayer())) {
            return false;
        }
        if (!this.getNewbieModel().compareDataTo(otherNode.getNewbieModel())) {
            return false;
        }
        if (!this.getStatisticModel().compareDataTo(otherNode.getStatisticModel())) {
            return false;
        }
        if (!this.getRedDotModel().compareDataTo(otherNode.getRedDotModel())) {
            return false;
        }
        if (!this.getPlayerHeroModel().compareDataTo(otherNode.getPlayerHeroModel())) {
            return false;
        }
        if (!this.getAvatarModel().compareDataTo(otherNode.getAvatarModel())) {
            return false;
        }
        if (!this.getZoneModel().compareDataTo(otherNode.getZoneModel())) {
            return false;
        }
        if (!this.getEnergyModel().compareDataTo(otherNode.getEnergyModel())) {
            return false;
        }
        if (!this.getPlayerRecruitModel().compareDataTo(otherNode.getPlayerRecruitModel())) {
            return false;
        }
        if (!this.getActivityModel().compareDataTo(otherNode.getActivityModel())) {
            return false;
        }
        if (!this.getMailModel().compareDataTo(otherNode.getMailModel())) {
            return false;
        }
        if (!this.getDungeonPlayer().compareDataTo(otherNode.getDungeonPlayer())) {
            return false;
        }
        if (!this.getKillMonsterModel().compareDataTo(otherNode.getKillMonsterModel())) {
            return false;
        }
        if (!this.getMileStoneModel().compareDataTo(otherNode.getMileStoneModel())) {
            return false;
        }
        if (!this.getDungeonModel().compareDataTo(otherNode.getDungeonModel())) {
            return false;
        }
        if (!this.getResetInfo().compareDataTo(otherNode.getResetInfo())) {
            return false;
        }
        if (!this.getSettingModel().compareDataTo(otherNode.getSettingModel())) {
            return false;
        }
        if (!this.getPaymentModel().compareDataTo(otherNode.getPaymentModel())) {
            return false;
        }
        if (!this.getFriendPlayer().compareDataTo(otherNode.getFriendPlayer())) {
            return false;
        }
        if (!this.getVipModel().compareDataTo(otherNode.getVipModel())) {
            return false;
        }
        if (!this.getQuestModel().compareDataTo(otherNode.getQuestModel())) {
            return false;
        }
        if (!this.getCityModel().compareDataTo(otherNode.getCityModel())) {
            return false;
        }
        if (!this.getDailyGoodsModel().compareDataTo(otherNode.getDailyGoodsModel())) {
            return false;
        }
        if (!this.getDrawInfo().compareDataTo(otherNode.getDrawInfo())) {
            return false;
        }
        if (!this.getDevBuffSysNew().compareDataTo(otherNode.getDevBuffSysNew())) {
            return false;
        }
        if (!this.getKingdomModel().compareDataTo(otherNode.getKingdomModel())) {
            return false;
        }
        if (!this.getZoneAdditionSys().compareDataTo(otherNode.getZoneAdditionSys())) {
            return false;
        }
        if (!this.getTriggerBundleModel().compareDataTo(otherNode.getTriggerBundleModel())) {
            return false;
        }
        if (!this.getFeatureLockModel().compareDataTo(otherNode.getFeatureLockModel())) {
            return false;
        }
        if (!this.getBattlePassModel().compareDataTo(otherNode.getBattlePassModel())) {
            return false;
        }
        if (this.dataPatchVersion != otherNode.dataPatchVersion) {
            return false;
        }
        if (!this.getAdditionSysNew().compareDataTo(otherNode.getAdditionSysNew())) {
            return false;
        }
        if (!this.getContactsModel().compareDataTo(otherNode.getContactsModel())) {
            return false;
        }
        if (!this.getRatingModel().compareDataTo(otherNode.getRatingModel())) {
            return false;
        }
        if (!this.getSkynetModel().compareDataTo(otherNode.getSkynetModel())) {
            return false;
        }
        if (!this.getAchievementModel().compareDataTo(otherNode.getAchievementModel())) {
            return false;
        }
        if (!this.getSeasonBattlePassModel().compareDataTo(otherNode.getSeasonBattlePassModel())) {
            return false;
        }
        if (!this.getPlayerInnerBuildRHModel().compareDataTo(otherNode.getPlayerInnerBuildRHModel())) {
            return false;
        }
        if (!this.getPlayerCampaignModel().compareDataTo(otherNode.getPlayerCampaignModel())) {
            return false;
        }
        if (!this.getPlayerUnitModel().compareDataTo(otherNode.getPlayerUnitModel())) {
            return false;
        }
        if (!this.getPlayerMissionModel().compareDataTo(otherNode.getPlayerMissionModel())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static PlayerProp of(PlayerEntity fullAttrDb, PlayerEntity changeAttrDb) {
        PlayerProp prop = new PlayerProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L);
        this.markBits1 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
        this.markBits1 = 0L;
    }
}
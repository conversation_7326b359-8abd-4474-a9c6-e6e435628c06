package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructClan.ClanContribution;
import com.yorha.proto.StructClanPB.ClanContributionPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanContributionProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CONTRIBUTION = 0;
    public static final int FIELD_INDEX_CONTRIBUTIONWEEKLY = 1;
    public static final int FIELD_INDEX_CONTRIBUTIONSEASON = 2;
    public static final int FIELD_INDEX_CONTRIBUTIONDAILY = 3;
    public static final int FIELD_INDEX_UPDATETSMS = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long contribution = Constant.DEFAULT_LONG_VALUE;
    private long contributionWeekly = Constant.DEFAULT_LONG_VALUE;
    private long contributionSeason = Constant.DEFAULT_LONG_VALUE;
    private long contributionDaily = Constant.DEFAULT_LONG_VALUE;
    private long updateTsMs = Constant.DEFAULT_LONG_VALUE;

    public ClanContributionProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanContributionProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get contribution
     *
     * @return contribution value
     */
    public long getContribution() {
        return this.contribution;
    }

    /**
     * set contribution && set marked
     *
     * @param contribution new value
     * @return current object
     */
    public ClanContributionProp setContribution(long contribution) {
        if (this.contribution != contribution) {
            this.mark(FIELD_INDEX_CONTRIBUTION);
            this.contribution = contribution;
        }
        return this;
    }

    /**
     * inner set contribution
     *
     * @param contribution new value
     */
    private void innerSetContribution(long contribution) {
        this.contribution = contribution;
    }

    /**
     * get contributionWeekly
     *
     * @return contributionWeekly value
     */
    public long getContributionWeekly() {
        return this.contributionWeekly;
    }

    /**
     * set contributionWeekly && set marked
     *
     * @param contributionWeekly new value
     * @return current object
     */
    public ClanContributionProp setContributionWeekly(long contributionWeekly) {
        if (this.contributionWeekly != contributionWeekly) {
            this.mark(FIELD_INDEX_CONTRIBUTIONWEEKLY);
            this.contributionWeekly = contributionWeekly;
        }
        return this;
    }

    /**
     * inner set contributionWeekly
     *
     * @param contributionWeekly new value
     */
    private void innerSetContributionWeekly(long contributionWeekly) {
        this.contributionWeekly = contributionWeekly;
    }

    /**
     * get contributionSeason
     *
     * @return contributionSeason value
     */
    public long getContributionSeason() {
        return this.contributionSeason;
    }

    /**
     * set contributionSeason && set marked
     *
     * @param contributionSeason new value
     * @return current object
     */
    public ClanContributionProp setContributionSeason(long contributionSeason) {
        if (this.contributionSeason != contributionSeason) {
            this.mark(FIELD_INDEX_CONTRIBUTIONSEASON);
            this.contributionSeason = contributionSeason;
        }
        return this;
    }

    /**
     * inner set contributionSeason
     *
     * @param contributionSeason new value
     */
    private void innerSetContributionSeason(long contributionSeason) {
        this.contributionSeason = contributionSeason;
    }

    /**
     * get contributionDaily
     *
     * @return contributionDaily value
     */
    public long getContributionDaily() {
        return this.contributionDaily;
    }

    /**
     * set contributionDaily && set marked
     *
     * @param contributionDaily new value
     * @return current object
     */
    public ClanContributionProp setContributionDaily(long contributionDaily) {
        if (this.contributionDaily != contributionDaily) {
            this.mark(FIELD_INDEX_CONTRIBUTIONDAILY);
            this.contributionDaily = contributionDaily;
        }
        return this;
    }

    /**
     * inner set contributionDaily
     *
     * @param contributionDaily new value
     */
    private void innerSetContributionDaily(long contributionDaily) {
        this.contributionDaily = contributionDaily;
    }

    /**
     * get updateTsMs
     *
     * @return updateTsMs value
     */
    public long getUpdateTsMs() {
        return this.updateTsMs;
    }

    /**
     * set updateTsMs && set marked
     *
     * @param updateTsMs new value
     * @return current object
     */
    public ClanContributionProp setUpdateTsMs(long updateTsMs) {
        if (this.updateTsMs != updateTsMs) {
            this.mark(FIELD_INDEX_UPDATETSMS);
            this.updateTsMs = updateTsMs;
        }
        return this;
    }

    /**
     * inner set updateTsMs
     *
     * @param updateTsMs new value
     */
    private void innerSetUpdateTsMs(long updateTsMs) {
        this.updateTsMs = updateTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanContributionPB.Builder getCopyCsBuilder() {
        final ClanContributionPB.Builder builder = ClanContributionPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanContributionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getContribution() != 0L) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        if (this.getContributionWeekly() != 0L) {
            builder.setContributionWeekly(this.getContributionWeekly());
            fieldCnt++;
        }  else if (builder.hasContributionWeekly()) {
            // 清理ContributionWeekly
            builder.clearContributionWeekly();
            fieldCnt++;
        }
        if (this.getContributionSeason() != 0L) {
            builder.setContributionSeason(this.getContributionSeason());
            fieldCnt++;
        }  else if (builder.hasContributionSeason()) {
            // 清理ContributionSeason
            builder.clearContributionSeason();
            fieldCnt++;
        }
        if (this.getContributionDaily() != 0L) {
            builder.setContributionDaily(this.getContributionDaily());
            fieldCnt++;
        }  else if (builder.hasContributionDaily()) {
            // 清理ContributionDaily
            builder.clearContributionDaily();
            fieldCnt++;
        }
        if (this.getUpdateTsMs() != 0L) {
            builder.setUpdateTsMs(this.getUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasUpdateTsMs()) {
            // 清理UpdateTsMs
            builder.clearUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanContributionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONWEEKLY)) {
            builder.setContributionWeekly(this.getContributionWeekly());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONSEASON)) {
            builder.setContributionSeason(this.getContributionSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONDAILY)) {
            builder.setContributionDaily(this.getContributionDaily());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPDATETSMS)) {
            builder.setUpdateTsMs(this.getUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanContributionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONWEEKLY)) {
            builder.setContributionWeekly(this.getContributionWeekly());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONSEASON)) {
            builder.setContributionSeason(this.getContributionSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONDAILY)) {
            builder.setContributionDaily(this.getContributionDaily());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPDATETSMS)) {
            builder.setUpdateTsMs(this.getUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanContributionPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContribution()) {
            this.innerSetContribution(proto.getContribution());
        } else {
            this.innerSetContribution(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionWeekly()) {
            this.innerSetContributionWeekly(proto.getContributionWeekly());
        } else {
            this.innerSetContributionWeekly(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionSeason()) {
            this.innerSetContributionSeason(proto.getContributionSeason());
        } else {
            this.innerSetContributionSeason(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionDaily()) {
            this.innerSetContributionDaily(proto.getContributionDaily());
        } else {
            this.innerSetContributionDaily(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUpdateTsMs()) {
            this.innerSetUpdateTsMs(proto.getUpdateTsMs());
        } else {
            this.innerSetUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanContributionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanContributionPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContribution()) {
            this.setContribution(proto.getContribution());
            fieldCnt++;
        }
        if (proto.hasContributionWeekly()) {
            this.setContributionWeekly(proto.getContributionWeekly());
            fieldCnt++;
        }
        if (proto.hasContributionSeason()) {
            this.setContributionSeason(proto.getContributionSeason());
            fieldCnt++;
        }
        if (proto.hasContributionDaily()) {
            this.setContributionDaily(proto.getContributionDaily());
            fieldCnt++;
        }
        if (proto.hasUpdateTsMs()) {
            this.setUpdateTsMs(proto.getUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanContribution.Builder getCopyDbBuilder() {
        final ClanContribution.Builder builder = ClanContribution.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanContribution.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getContribution() != 0L) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        if (this.getContributionWeekly() != 0L) {
            builder.setContributionWeekly(this.getContributionWeekly());
            fieldCnt++;
        }  else if (builder.hasContributionWeekly()) {
            // 清理ContributionWeekly
            builder.clearContributionWeekly();
            fieldCnt++;
        }
        if (this.getContributionSeason() != 0L) {
            builder.setContributionSeason(this.getContributionSeason());
            fieldCnt++;
        }  else if (builder.hasContributionSeason()) {
            // 清理ContributionSeason
            builder.clearContributionSeason();
            fieldCnt++;
        }
        if (this.getContributionDaily() != 0L) {
            builder.setContributionDaily(this.getContributionDaily());
            fieldCnt++;
        }  else if (builder.hasContributionDaily()) {
            // 清理ContributionDaily
            builder.clearContributionDaily();
            fieldCnt++;
        }
        if (this.getUpdateTsMs() != 0L) {
            builder.setUpdateTsMs(this.getUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasUpdateTsMs()) {
            // 清理UpdateTsMs
            builder.clearUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanContribution.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONWEEKLY)) {
            builder.setContributionWeekly(this.getContributionWeekly());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONSEASON)) {
            builder.setContributionSeason(this.getContributionSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONDAILY)) {
            builder.setContributionDaily(this.getContributionDaily());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPDATETSMS)) {
            builder.setUpdateTsMs(this.getUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanContribution proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContribution()) {
            this.innerSetContribution(proto.getContribution());
        } else {
            this.innerSetContribution(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionWeekly()) {
            this.innerSetContributionWeekly(proto.getContributionWeekly());
        } else {
            this.innerSetContributionWeekly(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionSeason()) {
            this.innerSetContributionSeason(proto.getContributionSeason());
        } else {
            this.innerSetContributionSeason(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionDaily()) {
            this.innerSetContributionDaily(proto.getContributionDaily());
        } else {
            this.innerSetContributionDaily(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUpdateTsMs()) {
            this.innerSetUpdateTsMs(proto.getUpdateTsMs());
        } else {
            this.innerSetUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanContributionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanContribution proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContribution()) {
            this.setContribution(proto.getContribution());
            fieldCnt++;
        }
        if (proto.hasContributionWeekly()) {
            this.setContributionWeekly(proto.getContributionWeekly());
            fieldCnt++;
        }
        if (proto.hasContributionSeason()) {
            this.setContributionSeason(proto.getContributionSeason());
            fieldCnt++;
        }
        if (proto.hasContributionDaily()) {
            this.setContributionDaily(proto.getContributionDaily());
            fieldCnt++;
        }
        if (proto.hasUpdateTsMs()) {
            this.setUpdateTsMs(proto.getUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanContribution.Builder getCopySsBuilder() {
        final ClanContribution.Builder builder = ClanContribution.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanContribution.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getContribution() != 0L) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        if (this.getContributionWeekly() != 0L) {
            builder.setContributionWeekly(this.getContributionWeekly());
            fieldCnt++;
        }  else if (builder.hasContributionWeekly()) {
            // 清理ContributionWeekly
            builder.clearContributionWeekly();
            fieldCnt++;
        }
        if (this.getContributionSeason() != 0L) {
            builder.setContributionSeason(this.getContributionSeason());
            fieldCnt++;
        }  else if (builder.hasContributionSeason()) {
            // 清理ContributionSeason
            builder.clearContributionSeason();
            fieldCnt++;
        }
        if (this.getContributionDaily() != 0L) {
            builder.setContributionDaily(this.getContributionDaily());
            fieldCnt++;
        }  else if (builder.hasContributionDaily()) {
            // 清理ContributionDaily
            builder.clearContributionDaily();
            fieldCnt++;
        }
        if (this.getUpdateTsMs() != 0L) {
            builder.setUpdateTsMs(this.getUpdateTsMs());
            fieldCnt++;
        }  else if (builder.hasUpdateTsMs()) {
            // 清理UpdateTsMs
            builder.clearUpdateTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanContribution.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONWEEKLY)) {
            builder.setContributionWeekly(this.getContributionWeekly());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONSEASON)) {
            builder.setContributionSeason(this.getContributionSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTIONDAILY)) {
            builder.setContributionDaily(this.getContributionDaily());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UPDATETSMS)) {
            builder.setUpdateTsMs(this.getUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanContribution proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContribution()) {
            this.innerSetContribution(proto.getContribution());
        } else {
            this.innerSetContribution(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionWeekly()) {
            this.innerSetContributionWeekly(proto.getContributionWeekly());
        } else {
            this.innerSetContributionWeekly(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionSeason()) {
            this.innerSetContributionSeason(proto.getContributionSeason());
        } else {
            this.innerSetContributionSeason(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasContributionDaily()) {
            this.innerSetContributionDaily(proto.getContributionDaily());
        } else {
            this.innerSetContributionDaily(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUpdateTsMs()) {
            this.innerSetUpdateTsMs(proto.getUpdateTsMs());
        } else {
            this.innerSetUpdateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanContributionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanContribution proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContribution()) {
            this.setContribution(proto.getContribution());
            fieldCnt++;
        }
        if (proto.hasContributionWeekly()) {
            this.setContributionWeekly(proto.getContributionWeekly());
            fieldCnt++;
        }
        if (proto.hasContributionSeason()) {
            this.setContributionSeason(proto.getContributionSeason());
            fieldCnt++;
        }
        if (proto.hasContributionDaily()) {
            this.setContributionDaily(proto.getContributionDaily());
            fieldCnt++;
        }
        if (proto.hasUpdateTsMs()) {
            this.setUpdateTsMs(proto.getUpdateTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanContribution.Builder builder = ClanContribution.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanContributionProp)) {
            return false;
        }
        final ClanContributionProp otherNode = (ClanContributionProp) node;
        if (this.contribution != otherNode.contribution) {
            return false;
        }
        if (this.contributionWeekly != otherNode.contributionWeekly) {
            return false;
        }
        if (this.contributionSeason != otherNode.contributionSeason) {
            return false;
        }
        if (this.contributionDaily != otherNode.contributionDaily) {
            return false;
        }
        if (this.updateTsMs != otherNode.updateTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
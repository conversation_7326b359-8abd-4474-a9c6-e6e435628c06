package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.SceneBattlePlaneListPB;
import com.yorha.proto.Struct.SceneBattlePlaneList;
import com.yorha.proto.StructPB.SceneBattlePlanePB;
import com.yorha.proto.Struct.SceneBattlePlane;

/**
 * <AUTHOR> auto gen
 */
public class SceneBattlePlaneListProp extends AbstractListNode<SceneBattlePlaneProp> {
    /**
     * Create a SceneBattlePlaneListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public SceneBattlePlaneListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to SceneBattlePlaneListProp
     *
     * @return new object
     */
    @Override
    public SceneBattlePlaneProp addEmptyValue() {
        final SceneBattlePlaneProp newProp = new SceneBattlePlaneProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneBattlePlaneListPB.Builder getCopyCsBuilder() {
        final SceneBattlePlaneListPB.Builder builder = SceneBattlePlaneListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(SceneBattlePlaneListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SceneBattlePlaneListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SceneBattlePlaneProp v : this) {
            final SceneBattlePlanePB.Builder itemBuilder = SceneBattlePlanePB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(SceneBattlePlaneListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(SceneBattlePlaneListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SceneBattlePlanePB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(SceneBattlePlaneListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneBattlePlaneList.Builder getCopyDbBuilder() {
        final SceneBattlePlaneList.Builder builder = SceneBattlePlaneList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(SceneBattlePlaneList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SceneBattlePlaneListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SceneBattlePlaneProp v : this) {
            final SceneBattlePlane.Builder itemBuilder = SceneBattlePlane.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(SceneBattlePlaneList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(SceneBattlePlaneList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SceneBattlePlane v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(SceneBattlePlaneList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneBattlePlaneList.Builder getCopySsBuilder() {
        final SceneBattlePlaneList.Builder builder = SceneBattlePlaneList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(SceneBattlePlaneList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SceneBattlePlaneListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SceneBattlePlaneProp v : this) {
            final SceneBattlePlane.Builder itemBuilder = SceneBattlePlane.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(SceneBattlePlaneList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(SceneBattlePlaneList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SceneBattlePlane v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return SceneBattlePlaneListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(SceneBattlePlaneList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        SceneBattlePlaneList.Builder builder = SceneBattlePlaneList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
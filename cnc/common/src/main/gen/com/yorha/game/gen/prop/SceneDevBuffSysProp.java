package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.SceneDevBuffSys;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.SceneDevBuffSysPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class SceneDevBuffSysProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DEVBUFFSYS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32SceneDevBuffMapProp devBuffSys = null;

    public SceneDevBuffSysProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SceneDevBuffSysProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get devBuffSys
     *
     * @return devBuffSys value
     */
    public Int32SceneDevBuffMapProp getDevBuffSys() {
        if (this.devBuffSys == null) {
            this.devBuffSys = new Int32SceneDevBuffMapProp(this, FIELD_INDEX_DEVBUFFSYS);
        }
        return this.devBuffSys;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDevBuffSysV(SceneDevBuffProp v) {
        this.getDevBuffSys().put(v.getDevBuffId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SceneDevBuffProp addEmptyDevBuffSys(Integer k) {
        return this.getDevBuffSys().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDevBuffSysSize() {
        if (this.devBuffSys == null) {
            return 0;
        }
        return this.devBuffSys.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDevBuffSysEmpty() {
        if (this.devBuffSys == null) {
            return true;
        }
        return this.devBuffSys.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SceneDevBuffProp getDevBuffSysV(Integer k) {
        if (this.devBuffSys == null || !this.devBuffSys.containsKey(k)) {
            return null;
        }
        return this.devBuffSys.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDevBuffSys() {
        if (this.devBuffSys != null) {
            this.devBuffSys.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDevBuffSysV(Integer k) {
        if (this.devBuffSys != null) {
            this.devBuffSys.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneDevBuffSysPB.Builder getCopyCsBuilder() {
        final SceneDevBuffSysPB.Builder builder = SceneDevBuffSysPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SceneDevBuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.devBuffSys != null) {
            StructBattlePB.Int32SceneDevBuffMapPB.Builder tmpBuilder = StructBattlePB.Int32SceneDevBuffMapPB.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SceneDevBuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SceneDevBuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SceneDevBuffSysPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromCs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromCs(proto.getDevBuffSys());
            }
        }
        this.markAll();
        return SceneDevBuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SceneDevBuffSysPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromCs(proto.getDevBuffSys());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneDevBuffSys.Builder getCopyDbBuilder() {
        final SceneDevBuffSys.Builder builder = SceneDevBuffSys.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SceneDevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.devBuffSys != null) {
            StructBattle.Int32SceneDevBuffMap.Builder tmpBuilder = StructBattle.Int32SceneDevBuffMap.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SceneDevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToDb(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SceneDevBuffSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromDb(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromDb(proto.getDevBuffSys());
            }
        }
        this.markAll();
        return SceneDevBuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SceneDevBuffSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromDb(proto.getDevBuffSys());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneDevBuffSys.Builder getCopySsBuilder() {
        final SceneDevBuffSys.Builder builder = SceneDevBuffSys.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SceneDevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.devBuffSys != null) {
            StructBattle.Int32SceneDevBuffMap.Builder tmpBuilder = StructBattle.Int32SceneDevBuffMap.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SceneDevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToSs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SceneDevBuffSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromSs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromSs(proto.getDevBuffSys());
            }
        }
        this.markAll();
        return SceneDevBuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SceneDevBuffSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromSs(proto.getDevBuffSys());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SceneDevBuffSys.Builder builder = SceneDevBuffSys.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            this.devBuffSys.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.devBuffSys != null) {
            this.devBuffSys.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SceneDevBuffSysProp)) {
            return false;
        }
        final SceneDevBuffSysProp otherNode = (SceneDevBuffSysProp) node;
        if (!this.getDevBuffSys().compareDataTo(otherNode.getDevBuffSys())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
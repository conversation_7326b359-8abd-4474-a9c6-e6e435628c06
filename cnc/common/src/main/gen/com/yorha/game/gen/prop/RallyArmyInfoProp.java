package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer.RallyArmyInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.RallyArmyInfoPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class RallyArmyInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ARMYID = 0;
    public static final int FIELD_INDEX_OWNERID = 1;
    public static final int FIELD_INDEX_TROOP = 2;
    public static final int FIELD_INDEX_ARMYSTATE = 3;
    public static final int FIELD_INDEX_RALLYROLE = 4;
    public static final int FIELD_INDEX_JOINTS = 5;
    public static final int FIELD_INDEX_ARRIVETS = 6;
    public static final int FIELD_INDEX_USENUM = 7;
    public static final int FIELD_INDEX_CARDHEAD = 8;
    public static final int FIELD_INDEX_ISNPC = 9;

    public static final int FIELD_COUNT = 10;

    private long markBits0 = 0L;

    private long armyId = Constant.DEFAULT_LONG_VALUE;
    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private TroopProp troop = null;
    private ArmyState armyState = ArmyState.forNumber(0);
    private RallyArmyRoleType rallyRole = RallyArmyRoleType.forNumber(0);
    private long joinTs = Constant.DEFAULT_LONG_VALUE;
    private long arriveTs = Constant.DEFAULT_LONG_VALUE;
    private long useNum = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp cardHead = null;
    private boolean isNpc = Constant.DEFAULT_BOOLEAN_VALUE;

    public RallyArmyInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public RallyArmyInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get armyId
     *
     * @return armyId value
     */
    public long getArmyId() {
        return this.armyId;
    }

    /**
     * set armyId && set marked
     *
     * @param armyId new value
     * @return current object
     */
    public RallyArmyInfoProp setArmyId(long armyId) {
        if (this.armyId != armyId) {
            this.mark(FIELD_INDEX_ARMYID);
            this.armyId = armyId;
        }
        return this;
    }

    /**
     * inner set armyId
     *
     * @param armyId new value
     */
    private void innerSetArmyId(long armyId) {
        this.armyId = armyId;
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public RallyArmyInfoProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public TroopProp getTroop() {
        if (this.troop == null) {
            this.troop = new TroopProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    /**
     * get armyState
     *
     * @return armyState value
     */
    public ArmyState getArmyState() {
        return this.armyState;
    }

    /**
     * set armyState && set marked
     *
     * @param armyState new value
     * @return current object
     */
    public RallyArmyInfoProp setArmyState(ArmyState armyState) {
        if (armyState == null) {
            throw new NullPointerException();
        }
        if (this.armyState != armyState) {
            this.mark(FIELD_INDEX_ARMYSTATE);
            this.armyState = armyState;
        }
        return this;
    }

    /**
     * inner set armyState
     *
     * @param armyState new value
     */
    private void innerSetArmyState(ArmyState armyState) {
        this.armyState = armyState;
    }

    /**
     * get rallyRole
     *
     * @return rallyRole value
     */
    public RallyArmyRoleType getRallyRole() {
        return this.rallyRole;
    }

    /**
     * set rallyRole && set marked
     *
     * @param rallyRole new value
     * @return current object
     */
    public RallyArmyInfoProp setRallyRole(RallyArmyRoleType rallyRole) {
        if (rallyRole == null) {
            throw new NullPointerException();
        }
        if (this.rallyRole != rallyRole) {
            this.mark(FIELD_INDEX_RALLYROLE);
            this.rallyRole = rallyRole;
        }
        return this;
    }

    /**
     * inner set rallyRole
     *
     * @param rallyRole new value
     */
    private void innerSetRallyRole(RallyArmyRoleType rallyRole) {
        this.rallyRole = rallyRole;
    }

    /**
     * get joinTs
     *
     * @return joinTs value
     */
    public long getJoinTs() {
        return this.joinTs;
    }

    /**
     * set joinTs && set marked
     *
     * @param joinTs new value
     * @return current object
     */
    public RallyArmyInfoProp setJoinTs(long joinTs) {
        if (this.joinTs != joinTs) {
            this.mark(FIELD_INDEX_JOINTS);
            this.joinTs = joinTs;
        }
        return this;
    }

    /**
     * inner set joinTs
     *
     * @param joinTs new value
     */
    private void innerSetJoinTs(long joinTs) {
        this.joinTs = joinTs;
    }

    /**
     * get arriveTs
     *
     * @return arriveTs value
     */
    public long getArriveTs() {
        return this.arriveTs;
    }

    /**
     * set arriveTs && set marked
     *
     * @param arriveTs new value
     * @return current object
     */
    public RallyArmyInfoProp setArriveTs(long arriveTs) {
        if (this.arriveTs != arriveTs) {
            this.mark(FIELD_INDEX_ARRIVETS);
            this.arriveTs = arriveTs;
        }
        return this;
    }

    /**
     * inner set arriveTs
     *
     * @param arriveTs new value
     */
    private void innerSetArriveTs(long arriveTs) {
        this.arriveTs = arriveTs;
    }

    /**
     * get useNum
     *
     * @return useNum value
     */
    public long getUseNum() {
        return this.useNum;
    }

    /**
     * set useNum && set marked
     *
     * @param useNum new value
     * @return current object
     */
    public RallyArmyInfoProp setUseNum(long useNum) {
        if (this.useNum != useNum) {
            this.mark(FIELD_INDEX_USENUM);
            this.useNum = useNum;
        }
        return this;
    }

    /**
     * inner set useNum
     *
     * @param useNum new value
     */
    private void innerSetUseNum(long useNum) {
        this.useNum = useNum;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get isNpc
     *
     * @return isNpc value
     */
    public boolean getIsNpc() {
        return this.isNpc;
    }

    /**
     * set isNpc && set marked
     *
     * @param isNpc new value
     * @return current object
     */
    public RallyArmyInfoProp setIsNpc(boolean isNpc) {
        if (this.isNpc != isNpc) {
            this.mark(FIELD_INDEX_ISNPC);
            this.isNpc = isNpc;
        }
        return this;
    }

    /**
     * inner set isNpc
     *
     * @param isNpc new value
     */
    private void innerSetIsNpc(boolean isNpc) {
        this.isNpc = isNpc;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RallyArmyInfoPB.Builder getCopyCsBuilder() {
        final RallyArmyInfoPB.Builder builder = RallyArmyInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(RallyArmyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.TroopPB.Builder tmpBuilder = StructPlayerPB.TroopPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.getArmyState() != ArmyState.forNumber(0)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }  else if (builder.hasArmyState()) {
            // 清理ArmyState
            builder.clearArmyState();
            fieldCnt++;
        }
        if (this.getRallyRole() != RallyArmyRoleType.forNumber(0)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }  else if (builder.hasRallyRole()) {
            // 清理RallyRole
            builder.clearRallyRole();
            fieldCnt++;
        }
        if (this.getJoinTs() != 0L) {
            builder.setJoinTs(this.getJoinTs());
            fieldCnt++;
        }  else if (builder.hasJoinTs()) {
            // 清理JoinTs
            builder.clearJoinTs();
            fieldCnt++;
        }
        if (this.getArriveTs() != 0L) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }  else if (builder.hasArriveTs()) {
            // 清理ArriveTs
            builder.clearArriveTs();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getIsNpc()) {
            builder.setIsNpc(this.getIsNpc());
            fieldCnt++;
        }  else if (builder.hasIsNpc()) {
            // 清理IsNpc
            builder.clearIsNpc();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(RallyArmyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYSTATE)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYROLE)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_JOINTS)) {
            builder.setJoinTs(this.getJoinTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARRIVETS)) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISNPC)) {
            builder.setIsNpc(this.getIsNpc());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(RallyArmyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYSTATE)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYROLE)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_JOINTS)) {
            builder.setJoinTs(this.getJoinTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARRIVETS)) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISNPC)) {
            builder.setIsNpc(this.getIsNpc());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(RallyArmyInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasArmyState()) {
            this.innerSetArmyState(proto.getArmyState());
        } else {
            this.innerSetArmyState(ArmyState.forNumber(0));
        }
        if (proto.hasRallyRole()) {
            this.innerSetRallyRole(proto.getRallyRole());
        } else {
            this.innerSetRallyRole(RallyArmyRoleType.forNumber(0));
        }
        if (proto.hasJoinTs()) {
            this.innerSetJoinTs(proto.getJoinTs());
        } else {
            this.innerSetJoinTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArriveTs()) {
            this.innerSetArriveTs(proto.getArriveTs());
        } else {
            this.innerSetArriveTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasIsNpc()) {
            this.innerSetIsNpc(proto.getIsNpc());
        } else {
            this.innerSetIsNpc(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return RallyArmyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(RallyArmyInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasArmyState()) {
            this.setArmyState(proto.getArmyState());
            fieldCnt++;
        }
        if (proto.hasRallyRole()) {
            this.setRallyRole(proto.getRallyRole());
            fieldCnt++;
        }
        if (proto.hasJoinTs()) {
            this.setJoinTs(proto.getJoinTs());
            fieldCnt++;
        }
        if (proto.hasArriveTs()) {
            this.setArriveTs(proto.getArriveTs());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasIsNpc()) {
            this.setIsNpc(proto.getIsNpc());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RallyArmyInfo.Builder getCopySsBuilder() {
        final RallyArmyInfo.Builder builder = RallyArmyInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(RallyArmyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Troop.Builder tmpBuilder = StructPlayer.Troop.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.getArmyState() != ArmyState.forNumber(0)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }  else if (builder.hasArmyState()) {
            // 清理ArmyState
            builder.clearArmyState();
            fieldCnt++;
        }
        if (this.getRallyRole() != RallyArmyRoleType.forNumber(0)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }  else if (builder.hasRallyRole()) {
            // 清理RallyRole
            builder.clearRallyRole();
            fieldCnt++;
        }
        if (this.getJoinTs() != 0L) {
            builder.setJoinTs(this.getJoinTs());
            fieldCnt++;
        }  else if (builder.hasJoinTs()) {
            // 清理JoinTs
            builder.clearJoinTs();
            fieldCnt++;
        }
        if (this.getArriveTs() != 0L) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }  else if (builder.hasArriveTs()) {
            // 清理ArriveTs
            builder.clearArriveTs();
            fieldCnt++;
        }
        if (this.getUseNum() != 0L) {
            builder.setUseNum(this.getUseNum());
            fieldCnt++;
        }  else if (builder.hasUseNum()) {
            // 清理UseNum
            builder.clearUseNum();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getIsNpc()) {
            builder.setIsNpc(this.getIsNpc());
            fieldCnt++;
        }  else if (builder.hasIsNpc()) {
            // 清理IsNpc
            builder.clearIsNpc();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(RallyArmyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_ARMYSTATE)) {
            builder.setArmyState(this.getArmyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYROLE)) {
            builder.setRallyRole(this.getRallyRole());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_JOINTS)) {
            builder.setJoinTs(this.getJoinTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARRIVETS)) {
            builder.setArriveTs(this.getArriveTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USENUM)) {
            builder.setUseNum(this.getUseNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISNPC)) {
            builder.setIsNpc(this.getIsNpc());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(RallyArmyInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasArmyState()) {
            this.innerSetArmyState(proto.getArmyState());
        } else {
            this.innerSetArmyState(ArmyState.forNumber(0));
        }
        if (proto.hasRallyRole()) {
            this.innerSetRallyRole(proto.getRallyRole());
        } else {
            this.innerSetRallyRole(RallyArmyRoleType.forNumber(0));
        }
        if (proto.hasJoinTs()) {
            this.innerSetJoinTs(proto.getJoinTs());
        } else {
            this.innerSetJoinTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArriveTs()) {
            this.innerSetArriveTs(proto.getArriveTs());
        } else {
            this.innerSetArriveTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUseNum()) {
            this.innerSetUseNum(proto.getUseNum());
        } else {
            this.innerSetUseNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasIsNpc()) {
            this.innerSetIsNpc(proto.getIsNpc());
        } else {
            this.innerSetIsNpc(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return RallyArmyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(RallyArmyInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasArmyState()) {
            this.setArmyState(proto.getArmyState());
            fieldCnt++;
        }
        if (proto.hasRallyRole()) {
            this.setRallyRole(proto.getRallyRole());
            fieldCnt++;
        }
        if (proto.hasJoinTs()) {
            this.setJoinTs(proto.getJoinTs());
            fieldCnt++;
        }
        if (proto.hasArriveTs()) {
            this.setArriveTs(proto.getArriveTs());
            fieldCnt++;
        }
        if (proto.hasUseNum()) {
            this.setUseNum(proto.getUseNum());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasIsNpc()) {
            this.setIsNpc(proto.getIsNpc());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        RallyArmyInfo.Builder builder = RallyArmyInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.armyId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof RallyArmyInfoProp)) {
            return false;
        }
        final RallyArmyInfoProp otherNode = (RallyArmyInfoProp) node;
        if (this.armyId != otherNode.armyId) {
            return false;
        }
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (this.armyState != otherNode.armyState) {
            return false;
        }
        if (this.rallyRole != otherNode.rallyRole) {
            return false;
        }
        if (this.joinTs != otherNode.joinTs) {
            return false;
        }
        if (this.arriveTs != otherNode.arriveTs) {
            return false;
        }
        if (this.useNum != otherNode.useNum) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (this.isNpc != otherNode.isNpc) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 54;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
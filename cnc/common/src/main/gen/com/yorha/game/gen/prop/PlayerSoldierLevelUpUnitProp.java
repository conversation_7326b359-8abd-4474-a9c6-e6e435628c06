package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerSoldierLevelUpUnit;
import com.yorha.proto.StructPB.PlayerSoldierLevelUpUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerSoldierLevelUpUnitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TOSOLDIERID = 0;
    public static final int FIELD_INDEX_FROMSOLDIERID = 1;
    public static final int FIELD_INDEX_NUM = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int toSoldierId = Constant.DEFAULT_INT_VALUE;
    private int fromSoldierId = Constant.DEFAULT_INT_VALUE;
    private int num = Constant.DEFAULT_INT_VALUE;

    public PlayerSoldierLevelUpUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerSoldierLevelUpUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get toSoldierId
     *
     * @return toSoldierId value
     */
    public int getToSoldierId() {
        return this.toSoldierId;
    }

    /**
     * set toSoldierId && set marked
     *
     * @param toSoldierId new value
     * @return current object
     */
    public PlayerSoldierLevelUpUnitProp setToSoldierId(int toSoldierId) {
        if (this.toSoldierId != toSoldierId) {
            this.mark(FIELD_INDEX_TOSOLDIERID);
            this.toSoldierId = toSoldierId;
        }
        return this;
    }

    /**
     * inner set toSoldierId
     *
     * @param toSoldierId new value
     */
    private void innerSetToSoldierId(int toSoldierId) {
        this.toSoldierId = toSoldierId;
    }

    /**
     * get fromSoldierId
     *
     * @return fromSoldierId value
     */
    public int getFromSoldierId() {
        return this.fromSoldierId;
    }

    /**
     * set fromSoldierId && set marked
     *
     * @param fromSoldierId new value
     * @return current object
     */
    public PlayerSoldierLevelUpUnitProp setFromSoldierId(int fromSoldierId) {
        if (this.fromSoldierId != fromSoldierId) {
            this.mark(FIELD_INDEX_FROMSOLDIERID);
            this.fromSoldierId = fromSoldierId;
        }
        return this;
    }

    /**
     * inner set fromSoldierId
     *
     * @param fromSoldierId new value
     */
    private void innerSetFromSoldierId(int fromSoldierId) {
        this.fromSoldierId = fromSoldierId;
    }

    /**
     * get num
     *
     * @return num value
     */
    public int getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public PlayerSoldierLevelUpUnitProp setNum(int num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(int num) {
        this.num = num;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierLevelUpUnitPB.Builder getCopyCsBuilder() {
        final PlayerSoldierLevelUpUnitPB.Builder builder = PlayerSoldierLevelUpUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerSoldierLevelUpUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getToSoldierId() != 0) {
            builder.setToSoldierId(this.getToSoldierId());
            fieldCnt++;
        }  else if (builder.hasToSoldierId()) {
            // 清理ToSoldierId
            builder.clearToSoldierId();
            fieldCnt++;
        }
        if (this.getFromSoldierId() != 0) {
            builder.setFromSoldierId(this.getFromSoldierId());
            fieldCnt++;
        }  else if (builder.hasFromSoldierId()) {
            // 清理FromSoldierId
            builder.clearFromSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerSoldierLevelUpUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOSOLDIERID)) {
            builder.setToSoldierId(this.getToSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FROMSOLDIERID)) {
            builder.setFromSoldierId(this.getFromSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerSoldierLevelUpUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOSOLDIERID)) {
            builder.setToSoldierId(this.getToSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FROMSOLDIERID)) {
            builder.setFromSoldierId(this.getFromSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerSoldierLevelUpUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasToSoldierId()) {
            this.innerSetToSoldierId(proto.getToSoldierId());
        } else {
            this.innerSetToSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFromSoldierId()) {
            this.innerSetFromSoldierId(proto.getFromSoldierId());
        } else {
            this.innerSetFromSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerSoldierLevelUpUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerSoldierLevelUpUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasToSoldierId()) {
            this.setToSoldierId(proto.getToSoldierId());
            fieldCnt++;
        }
        if (proto.hasFromSoldierId()) {
            this.setFromSoldierId(proto.getFromSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierLevelUpUnit.Builder getCopyDbBuilder() {
        final PlayerSoldierLevelUpUnit.Builder builder = PlayerSoldierLevelUpUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerSoldierLevelUpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getToSoldierId() != 0) {
            builder.setToSoldierId(this.getToSoldierId());
            fieldCnt++;
        }  else if (builder.hasToSoldierId()) {
            // 清理ToSoldierId
            builder.clearToSoldierId();
            fieldCnt++;
        }
        if (this.getFromSoldierId() != 0) {
            builder.setFromSoldierId(this.getFromSoldierId());
            fieldCnt++;
        }  else if (builder.hasFromSoldierId()) {
            // 清理FromSoldierId
            builder.clearFromSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerSoldierLevelUpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOSOLDIERID)) {
            builder.setToSoldierId(this.getToSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FROMSOLDIERID)) {
            builder.setFromSoldierId(this.getFromSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerSoldierLevelUpUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasToSoldierId()) {
            this.innerSetToSoldierId(proto.getToSoldierId());
        } else {
            this.innerSetToSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFromSoldierId()) {
            this.innerSetFromSoldierId(proto.getFromSoldierId());
        } else {
            this.innerSetFromSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerSoldierLevelUpUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerSoldierLevelUpUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasToSoldierId()) {
            this.setToSoldierId(proto.getToSoldierId());
            fieldCnt++;
        }
        if (proto.hasFromSoldierId()) {
            this.setFromSoldierId(proto.getFromSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSoldierLevelUpUnit.Builder getCopySsBuilder() {
        final PlayerSoldierLevelUpUnit.Builder builder = PlayerSoldierLevelUpUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerSoldierLevelUpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getToSoldierId() != 0) {
            builder.setToSoldierId(this.getToSoldierId());
            fieldCnt++;
        }  else if (builder.hasToSoldierId()) {
            // 清理ToSoldierId
            builder.clearToSoldierId();
            fieldCnt++;
        }
        if (this.getFromSoldierId() != 0) {
            builder.setFromSoldierId(this.getFromSoldierId());
            fieldCnt++;
        }  else if (builder.hasFromSoldierId()) {
            // 清理FromSoldierId
            builder.clearFromSoldierId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerSoldierLevelUpUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TOSOLDIERID)) {
            builder.setToSoldierId(this.getToSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FROMSOLDIERID)) {
            builder.setFromSoldierId(this.getFromSoldierId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerSoldierLevelUpUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasToSoldierId()) {
            this.innerSetToSoldierId(proto.getToSoldierId());
        } else {
            this.innerSetToSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFromSoldierId()) {
            this.innerSetFromSoldierId(proto.getFromSoldierId());
        } else {
            this.innerSetFromSoldierId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerSoldierLevelUpUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerSoldierLevelUpUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasToSoldierId()) {
            this.setToSoldierId(proto.getToSoldierId());
            fieldCnt++;
        }
        if (proto.hasFromSoldierId()) {
            this.setFromSoldierId(proto.getFromSoldierId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerSoldierLevelUpUnit.Builder builder = PlayerSoldierLevelUpUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.toSoldierId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerSoldierLevelUpUnitProp)) {
            return false;
        }
        final PlayerSoldierLevelUpUnitProp otherNode = (PlayerSoldierLevelUpUnitProp) node;
        if (this.toSoldierId != otherNode.toSoldierId) {
            return false;
        }
        if (this.fromSoldierId != otherNode.fromSoldierId) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanResetQuietPeriod;
import com.yorha.proto.ClanPB.ClanResetQuietPeriodPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanResetQuietPeriodProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RESETNAMEQUIETTSMS = 0;
    public static final int FIELD_INDEX_RESETDESCRIBEQUIETTSMS = 1;
    public static final int FIELD_INDEX_RESETSNAMEQUIETTSMS = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long resetNameQuietTsMs = Constant.DEFAULT_LONG_VALUE;
    private long resetDescribeQuietTsMs = Constant.DEFAULT_LONG_VALUE;
    private long resetSNameQuietTsMs = Constant.DEFAULT_LONG_VALUE;

    public ClanResetQuietPeriodProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanResetQuietPeriodProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get resetNameQuietTsMs
     *
     * @return resetNameQuietTsMs value
     */
    public long getResetNameQuietTsMs() {
        return this.resetNameQuietTsMs;
    }

    /**
     * set resetNameQuietTsMs && set marked
     *
     * @param resetNameQuietTsMs new value
     * @return current object
     */
    public ClanResetQuietPeriodProp setResetNameQuietTsMs(long resetNameQuietTsMs) {
        if (this.resetNameQuietTsMs != resetNameQuietTsMs) {
            this.mark(FIELD_INDEX_RESETNAMEQUIETTSMS);
            this.resetNameQuietTsMs = resetNameQuietTsMs;
        }
        return this;
    }

    /**
     * inner set resetNameQuietTsMs
     *
     * @param resetNameQuietTsMs new value
     */
    private void innerSetResetNameQuietTsMs(long resetNameQuietTsMs) {
        this.resetNameQuietTsMs = resetNameQuietTsMs;
    }

    /**
     * get resetDescribeQuietTsMs
     *
     * @return resetDescribeQuietTsMs value
     */
    public long getResetDescribeQuietTsMs() {
        return this.resetDescribeQuietTsMs;
    }

    /**
     * set resetDescribeQuietTsMs && set marked
     *
     * @param resetDescribeQuietTsMs new value
     * @return current object
     */
    public ClanResetQuietPeriodProp setResetDescribeQuietTsMs(long resetDescribeQuietTsMs) {
        if (this.resetDescribeQuietTsMs != resetDescribeQuietTsMs) {
            this.mark(FIELD_INDEX_RESETDESCRIBEQUIETTSMS);
            this.resetDescribeQuietTsMs = resetDescribeQuietTsMs;
        }
        return this;
    }

    /**
     * inner set resetDescribeQuietTsMs
     *
     * @param resetDescribeQuietTsMs new value
     */
    private void innerSetResetDescribeQuietTsMs(long resetDescribeQuietTsMs) {
        this.resetDescribeQuietTsMs = resetDescribeQuietTsMs;
    }

    /**
     * get resetSNameQuietTsMs
     *
     * @return resetSNameQuietTsMs value
     */
    public long getResetSNameQuietTsMs() {
        return this.resetSNameQuietTsMs;
    }

    /**
     * set resetSNameQuietTsMs && set marked
     *
     * @param resetSNameQuietTsMs new value
     * @return current object
     */
    public ClanResetQuietPeriodProp setResetSNameQuietTsMs(long resetSNameQuietTsMs) {
        if (this.resetSNameQuietTsMs != resetSNameQuietTsMs) {
            this.mark(FIELD_INDEX_RESETSNAMEQUIETTSMS);
            this.resetSNameQuietTsMs = resetSNameQuietTsMs;
        }
        return this;
    }

    /**
     * inner set resetSNameQuietTsMs
     *
     * @param resetSNameQuietTsMs new value
     */
    private void innerSetResetSNameQuietTsMs(long resetSNameQuietTsMs) {
        this.resetSNameQuietTsMs = resetSNameQuietTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResetQuietPeriodPB.Builder getCopyCsBuilder() {
        final ClanResetQuietPeriodPB.Builder builder = ClanResetQuietPeriodPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanResetQuietPeriodPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getResetNameQuietTsMs() != 0L) {
            builder.setResetNameQuietTsMs(this.getResetNameQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetNameQuietTsMs()) {
            // 清理ResetNameQuietTsMs
            builder.clearResetNameQuietTsMs();
            fieldCnt++;
        }
        if (this.getResetDescribeQuietTsMs() != 0L) {
            builder.setResetDescribeQuietTsMs(this.getResetDescribeQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetDescribeQuietTsMs()) {
            // 清理ResetDescribeQuietTsMs
            builder.clearResetDescribeQuietTsMs();
            fieldCnt++;
        }
        if (this.getResetSNameQuietTsMs() != 0L) {
            builder.setResetSNameQuietTsMs(this.getResetSNameQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetSNameQuietTsMs()) {
            // 清理ResetSNameQuietTsMs
            builder.clearResetSNameQuietTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanResetQuietPeriodPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESETNAMEQUIETTSMS)) {
            builder.setResetNameQuietTsMs(this.getResetNameQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETDESCRIBEQUIETTSMS)) {
            builder.setResetDescribeQuietTsMs(this.getResetDescribeQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETSNAMEQUIETTSMS)) {
            builder.setResetSNameQuietTsMs(this.getResetSNameQuietTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanResetQuietPeriodPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESETNAMEQUIETTSMS)) {
            builder.setResetNameQuietTsMs(this.getResetNameQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETDESCRIBEQUIETTSMS)) {
            builder.setResetDescribeQuietTsMs(this.getResetDescribeQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETSNAMEQUIETTSMS)) {
            builder.setResetSNameQuietTsMs(this.getResetSNameQuietTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanResetQuietPeriodPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResetNameQuietTsMs()) {
            this.innerSetResetNameQuietTsMs(proto.getResetNameQuietTsMs());
        } else {
            this.innerSetResetNameQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResetDescribeQuietTsMs()) {
            this.innerSetResetDescribeQuietTsMs(proto.getResetDescribeQuietTsMs());
        } else {
            this.innerSetResetDescribeQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResetSNameQuietTsMs()) {
            this.innerSetResetSNameQuietTsMs(proto.getResetSNameQuietTsMs());
        } else {
            this.innerSetResetSNameQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanResetQuietPeriodProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanResetQuietPeriodPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResetNameQuietTsMs()) {
            this.setResetNameQuietTsMs(proto.getResetNameQuietTsMs());
            fieldCnt++;
        }
        if (proto.hasResetDescribeQuietTsMs()) {
            this.setResetDescribeQuietTsMs(proto.getResetDescribeQuietTsMs());
            fieldCnt++;
        }
        if (proto.hasResetSNameQuietTsMs()) {
            this.setResetSNameQuietTsMs(proto.getResetSNameQuietTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResetQuietPeriod.Builder getCopyDbBuilder() {
        final ClanResetQuietPeriod.Builder builder = ClanResetQuietPeriod.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanResetQuietPeriod.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getResetNameQuietTsMs() != 0L) {
            builder.setResetNameQuietTsMs(this.getResetNameQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetNameQuietTsMs()) {
            // 清理ResetNameQuietTsMs
            builder.clearResetNameQuietTsMs();
            fieldCnt++;
        }
        if (this.getResetDescribeQuietTsMs() != 0L) {
            builder.setResetDescribeQuietTsMs(this.getResetDescribeQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetDescribeQuietTsMs()) {
            // 清理ResetDescribeQuietTsMs
            builder.clearResetDescribeQuietTsMs();
            fieldCnt++;
        }
        if (this.getResetSNameQuietTsMs() != 0L) {
            builder.setResetSNameQuietTsMs(this.getResetSNameQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetSNameQuietTsMs()) {
            // 清理ResetSNameQuietTsMs
            builder.clearResetSNameQuietTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanResetQuietPeriod.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESETNAMEQUIETTSMS)) {
            builder.setResetNameQuietTsMs(this.getResetNameQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETDESCRIBEQUIETTSMS)) {
            builder.setResetDescribeQuietTsMs(this.getResetDescribeQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETSNAMEQUIETTSMS)) {
            builder.setResetSNameQuietTsMs(this.getResetSNameQuietTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanResetQuietPeriod proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResetNameQuietTsMs()) {
            this.innerSetResetNameQuietTsMs(proto.getResetNameQuietTsMs());
        } else {
            this.innerSetResetNameQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResetDescribeQuietTsMs()) {
            this.innerSetResetDescribeQuietTsMs(proto.getResetDescribeQuietTsMs());
        } else {
            this.innerSetResetDescribeQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResetSNameQuietTsMs()) {
            this.innerSetResetSNameQuietTsMs(proto.getResetSNameQuietTsMs());
        } else {
            this.innerSetResetSNameQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanResetQuietPeriodProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanResetQuietPeriod proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResetNameQuietTsMs()) {
            this.setResetNameQuietTsMs(proto.getResetNameQuietTsMs());
            fieldCnt++;
        }
        if (proto.hasResetDescribeQuietTsMs()) {
            this.setResetDescribeQuietTsMs(proto.getResetDescribeQuietTsMs());
            fieldCnt++;
        }
        if (proto.hasResetSNameQuietTsMs()) {
            this.setResetSNameQuietTsMs(proto.getResetSNameQuietTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanResetQuietPeriod.Builder getCopySsBuilder() {
        final ClanResetQuietPeriod.Builder builder = ClanResetQuietPeriod.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanResetQuietPeriod.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getResetNameQuietTsMs() != 0L) {
            builder.setResetNameQuietTsMs(this.getResetNameQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetNameQuietTsMs()) {
            // 清理ResetNameQuietTsMs
            builder.clearResetNameQuietTsMs();
            fieldCnt++;
        }
        if (this.getResetDescribeQuietTsMs() != 0L) {
            builder.setResetDescribeQuietTsMs(this.getResetDescribeQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetDescribeQuietTsMs()) {
            // 清理ResetDescribeQuietTsMs
            builder.clearResetDescribeQuietTsMs();
            fieldCnt++;
        }
        if (this.getResetSNameQuietTsMs() != 0L) {
            builder.setResetSNameQuietTsMs(this.getResetSNameQuietTsMs());
            fieldCnt++;
        }  else if (builder.hasResetSNameQuietTsMs()) {
            // 清理ResetSNameQuietTsMs
            builder.clearResetSNameQuietTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanResetQuietPeriod.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RESETNAMEQUIETTSMS)) {
            builder.setResetNameQuietTsMs(this.getResetNameQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETDESCRIBEQUIETTSMS)) {
            builder.setResetDescribeQuietTsMs(this.getResetDescribeQuietTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESETSNAMEQUIETTSMS)) {
            builder.setResetSNameQuietTsMs(this.getResetSNameQuietTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanResetQuietPeriod proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasResetNameQuietTsMs()) {
            this.innerSetResetNameQuietTsMs(proto.getResetNameQuietTsMs());
        } else {
            this.innerSetResetNameQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResetDescribeQuietTsMs()) {
            this.innerSetResetDescribeQuietTsMs(proto.getResetDescribeQuietTsMs());
        } else {
            this.innerSetResetDescribeQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResetSNameQuietTsMs()) {
            this.innerSetResetSNameQuietTsMs(proto.getResetSNameQuietTsMs());
        } else {
            this.innerSetResetSNameQuietTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanResetQuietPeriodProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanResetQuietPeriod proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasResetNameQuietTsMs()) {
            this.setResetNameQuietTsMs(proto.getResetNameQuietTsMs());
            fieldCnt++;
        }
        if (proto.hasResetDescribeQuietTsMs()) {
            this.setResetDescribeQuietTsMs(proto.getResetDescribeQuietTsMs());
            fieldCnt++;
        }
        if (proto.hasResetSNameQuietTsMs()) {
            this.setResetSNameQuietTsMs(proto.getResetSNameQuietTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanResetQuietPeriod.Builder builder = ClanResetQuietPeriod.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanResetQuietPeriodProp)) {
            return false;
        }
        final ClanResetQuietPeriodProp otherNode = (ClanResetQuietPeriodProp) node;
        if (this.resetNameQuietTsMs != otherNode.resetNameQuietTsMs) {
            return false;
        }
        if (this.resetDescribeQuietTsMs != otherNode.resetDescribeQuietTsMs) {
            return false;
        }
        if (this.resetSNameQuietTsMs != otherNode.resetSNameQuietTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
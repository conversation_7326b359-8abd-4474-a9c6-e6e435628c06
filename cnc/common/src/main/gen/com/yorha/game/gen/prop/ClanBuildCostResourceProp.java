package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Clan.ClanBuildCostResource;
import com.yorha.proto.Struct;
import com.yorha.proto.ClanPB.ClanBuildCostResourcePB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanBuildCostResourceProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_BUILDINGID = 0;
    public static final int FIELD_INDEX_COSTRESOURCE = 1;
    public static final int FIELD_INDEX_PLAYERID = 2;
    public static final int FIELD_INDEX_TYPE = 3;
    public static final int FIELD_INDEX_CARDHEAD = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long buildingId = Constant.DEFAULT_LONG_VALUE;
    private Int32CurrencyMapProp costResource = null;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private MapBuildingType type = MapBuildingType.forNumber(0);
    private PlayerCardHeadProp cardHead = null;

    public ClanBuildCostResourceProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanBuildCostResourceProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get buildingId
     *
     * @return buildingId value
     */
    public long getBuildingId() {
        return this.buildingId;
    }

    /**
     * set buildingId && set marked
     *
     * @param buildingId new value
     * @return current object
     */
    public ClanBuildCostResourceProp setBuildingId(long buildingId) {
        if (this.buildingId != buildingId) {
            this.mark(FIELD_INDEX_BUILDINGID);
            this.buildingId = buildingId;
        }
        return this;
    }

    /**
     * inner set buildingId
     *
     * @param buildingId new value
     */
    private void innerSetBuildingId(long buildingId) {
        this.buildingId = buildingId;
    }

    /**
     * get costResource
     *
     * @return costResource value
     */
    public Int32CurrencyMapProp getCostResource() {
        if (this.costResource == null) {
            this.costResource = new Int32CurrencyMapProp(this, FIELD_INDEX_COSTRESOURCE);
        }
        return this.costResource;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putCostResourceV(CurrencyProp v) {
        this.getCostResource().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyCostResource(Integer k) {
        return this.getCostResource().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getCostResourceSize() {
        if (this.costResource == null) {
            return 0;
        }
        return this.costResource.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isCostResourceEmpty() {
        if (this.costResource == null) {
            return true;
        }
        return this.costResource.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getCostResourceV(Integer k) {
        if (this.costResource == null || !this.costResource.containsKey(k)) {
            return null;
        }
        return this.costResource.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearCostResource() {
        if (this.costResource != null) {
            this.costResource.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeCostResourceV(Integer k) {
        if (this.costResource != null) {
            this.costResource.remove(k);
        }
    }
    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public ClanBuildCostResourceProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get type
     *
     * @return type value
     */
    public MapBuildingType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public ClanBuildCostResourceProp setType(MapBuildingType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(MapBuildingType type) {
        this.type = type;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBuildCostResourcePB.Builder getCopyCsBuilder() {
        final ClanBuildCostResourcePB.Builder builder = ClanBuildCostResourcePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanBuildCostResourcePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBuildingId() != 0L) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }  else if (builder.hasBuildingId()) {
            // 清理BuildingId
            builder.clearBuildingId();
            fieldCnt++;
        }
        if (this.costResource != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getType() != MapBuildingType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanBuildCostResourcePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToCs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanBuildCostResourcePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToAndClearDeleteKeysCs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanBuildCostResourcePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuildingId()) {
            this.innerSetBuildingId(proto.getBuildingId());
        } else {
            this.innerSetBuildingId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromCs(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromCs(proto.getCostResource());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(MapBuildingType.forNumber(0));
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        this.markAll();
        return ClanBuildCostResourceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanBuildCostResourcePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuildingId()) {
            this.setBuildingId(proto.getBuildingId());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromCs(proto.getCostResource());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBuildCostResource.Builder getCopyDbBuilder() {
        final ClanBuildCostResource.Builder builder = ClanBuildCostResource.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanBuildCostResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBuildingId() != 0L) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }  else if (builder.hasBuildingId()) {
            // 清理BuildingId
            builder.clearBuildingId();
            fieldCnt++;
        }
        if (this.costResource != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getType() != MapBuildingType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanBuildCostResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToDb(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanBuildCostResource proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuildingId()) {
            this.innerSetBuildingId(proto.getBuildingId());
        } else {
            this.innerSetBuildingId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromDb(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromDb(proto.getCostResource());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(MapBuildingType.forNumber(0));
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        this.markAll();
        return ClanBuildCostResourceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanBuildCostResource proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuildingId()) {
            this.setBuildingId(proto.getBuildingId());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromDb(proto.getCostResource());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanBuildCostResource.Builder getCopySsBuilder() {
        final ClanBuildCostResource.Builder builder = ClanBuildCostResource.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanBuildCostResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBuildingId() != 0L) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }  else if (builder.hasBuildingId()) {
            // 清理BuildingId
            builder.clearBuildingId();
            fieldCnt++;
        }
        if (this.costResource != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.costResource.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCostResource(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCostResource();
            }
        }  else if (builder.hasCostResource()) {
            // 清理CostResource
            builder.clearCostResource();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getType() != MapBuildingType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanBuildCostResource.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUILDINGID)) {
            builder.setBuildingId(this.getBuildingId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            final boolean needClear = !builder.hasCostResource();
            final int tmpFieldCnt = this.costResource.copyChangeToSs(builder.getCostResourceBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCostResource();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanBuildCostResource proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuildingId()) {
            this.innerSetBuildingId(proto.getBuildingId());
        } else {
            this.innerSetBuildingId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeFromSs(proto.getCostResource());
        } else {
            if (this.costResource != null) {
                this.costResource.mergeFromSs(proto.getCostResource());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(MapBuildingType.forNumber(0));
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        this.markAll();
        return ClanBuildCostResourceProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanBuildCostResource proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuildingId()) {
            this.setBuildingId(proto.getBuildingId());
            fieldCnt++;
        }
        if (proto.hasCostResource()) {
            this.getCostResource().mergeChangeFromSs(proto.getCostResource());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanBuildCostResource.Builder builder = ClanBuildCostResource.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_COSTRESOURCE) && this.costResource != null) {
            this.costResource.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.costResource != null) {
            this.costResource.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.buildingId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanBuildCostResourceProp)) {
            return false;
        }
        final ClanBuildCostResourceProp otherNode = (ClanBuildCostResourceProp) node;
        if (this.buildingId != otherNode.buildingId) {
            return false;
        }
        if (!this.getCostResource().compareDataTo(otherNode.getCostResource())) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.type != otherNode.type) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
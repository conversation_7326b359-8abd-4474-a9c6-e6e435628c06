package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.YoAssetPackage;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.YoAssetPackagePB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class YoAssetPackageProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ASSETS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private YoAssetDescListProp assets = null;

    public YoAssetPackageProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoAssetPackageProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get assets
     *
     * @return assets value
     */
    public YoAssetDescListProp getAssets() {
        if (this.assets == null) {
            this.assets = new YoAssetDescListProp(this, FIELD_INDEX_ASSETS);
        }
        return this.assets;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addAssets(YoAssetDescProp v) {
        this.getAssets().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public YoAssetDescProp getAssetsIndex(int index) {
        return this.getAssets().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public YoAssetDescProp removeAssets(YoAssetDescProp v) {
        if (this.assets == null) {
            return null;
        }
        if(this.assets.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getAssetsSize() {
        if (this.assets == null) {
            return 0;
        }
        return this.assets.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isAssetsEmpty() {
        if (this.assets == null) {
            return true;
        }
        return this.getAssets().isEmpty();
    }

    /**
     * clear list
     */
    public void clearAssets() {
        this.getAssets().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public YoAssetDescProp removeAssetsIndex(int index) {
        return this.getAssets().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public YoAssetDescProp setAssetsIndex(int index, YoAssetDescProp v) {
        return this.getAssets().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoAssetPackagePB.Builder getCopyCsBuilder() {
        final YoAssetPackagePB.Builder builder = YoAssetPackagePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoAssetPackagePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.assets != null) {
            StructPB.YoAssetDescListPB.Builder tmpBuilder = StructPB.YoAssetDescListPB.newBuilder();
            final int tmpFieldCnt = this.assets.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssets(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssets();
            }
        }  else if (builder.hasAssets()) {
            // 清理Assets
            builder.clearAssets();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoAssetPackagePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ASSETS) && this.assets != null) {
            final boolean needClear = !builder.hasAssets();
            final int tmpFieldCnt = this.assets.copyChangeToCs(builder.getAssetsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssets();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoAssetPackagePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ASSETS) && this.assets != null) {
            final boolean needClear = !builder.hasAssets();
            final int tmpFieldCnt = this.assets.copyChangeToCs(builder.getAssetsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssets();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoAssetPackagePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAssets()) {
            this.getAssets().mergeFromCs(proto.getAssets());
        } else {
            if (this.assets != null) {
                this.assets.mergeFromCs(proto.getAssets());
            }
        }
        this.markAll();
        return YoAssetPackageProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoAssetPackagePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAssets()) {
            this.getAssets().mergeChangeFromCs(proto.getAssets());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoAssetPackage.Builder getCopySsBuilder() {
        final YoAssetPackage.Builder builder = YoAssetPackage.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoAssetPackage.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.assets != null) {
            Struct.YoAssetDescList.Builder tmpBuilder = Struct.YoAssetDescList.newBuilder();
            final int tmpFieldCnt = this.assets.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAssets(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAssets();
            }
        }  else if (builder.hasAssets()) {
            // 清理Assets
            builder.clearAssets();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoAssetPackage.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ASSETS) && this.assets != null) {
            final boolean needClear = !builder.hasAssets();
            final int tmpFieldCnt = this.assets.copyChangeToSs(builder.getAssetsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAssets();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoAssetPackage proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAssets()) {
            this.getAssets().mergeFromSs(proto.getAssets());
        } else {
            if (this.assets != null) {
                this.assets.mergeFromSs(proto.getAssets());
            }
        }
        this.markAll();
        return YoAssetPackageProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoAssetPackage proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAssets()) {
            this.getAssets().mergeChangeFromSs(proto.getAssets());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoAssetPackage.Builder builder = YoAssetPackage.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ASSETS) && this.assets != null) {
            this.assets.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.assets != null) {
            this.assets.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoAssetPackageProp)) {
            return false;
        }
        final YoAssetPackageProp otherNode = (YoAssetPackageProp) node;
        if (!this.getAssets().compareDataTo(otherNode.getAssets())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
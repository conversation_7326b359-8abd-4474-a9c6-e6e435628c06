package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerCardInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.PlayerCardInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerCardInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PLAYERID = 0;
    public static final int FIELD_INDEX_COMBAT = 1;
    public static final int FIELD_INDEX_CLANID = 2;
    public static final int FIELD_INDEX_CLANSIMPLENAME = 3;
    public static final int FIELD_INDEX_CLANNAME = 4;
    public static final int FIELD_INDEX_CITYLEVEL = 5;
    public static final int FIELD_INDEX_CARDHEAD = 6;
    public static final int FIELD_INDEX_KILLSCORE = 7;
    public static final int FIELD_INDEX_KINGDOMOFFICE = 8;
    public static final int FIELD_INDEX_ZONEID = 9;
    public static final int FIELD_INDEX_CITYSKINID = 10;
    public static final int FIELD_INDEX_LOGOFF = 11;
    public static final int FIELD_INDEX_ACHIEVEMENTGOLDNUM = 12;
    public static final int FIELD_INDEX_ACHIEVEMENTSILVERNUM = 13;
    public static final int FIELD_INDEX_ACHIEVEMENTCOPPERNUM = 14;

    public static final int FIELD_COUNT = 15;

    private long markBits0 = 0L;

    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private long combat = Constant.DEFAULT_LONG_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private String clanSimpleName = Constant.DEFAULT_STR_VALUE;
    private String clanName = Constant.DEFAULT_STR_VALUE;
    private int cityLevel = Constant.DEFAULT_INT_VALUE;
    private PlayerCardHeadProp cardHead = null;
    private long killScore = Constant.DEFAULT_LONG_VALUE;
    private int kingdomOffice = Constant.DEFAULT_INT_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;
    private int citySkinId = Constant.DEFAULT_INT_VALUE;
    private boolean logOff = Constant.DEFAULT_BOOLEAN_VALUE;
    private int achievementGoldNum = Constant.DEFAULT_INT_VALUE;
    private int achievementSilverNum = Constant.DEFAULT_INT_VALUE;
    private int achievementCopperNum = Constant.DEFAULT_INT_VALUE;

    public PlayerCardInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerCardInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public PlayerCardInfoProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get combat
     *
     * @return combat value
     */
    public long getCombat() {
        return this.combat;
    }

    /**
     * set combat && set marked
     *
     * @param combat new value
     * @return current object
     */
    public PlayerCardInfoProp setCombat(long combat) {
        if (this.combat != combat) {
            this.mark(FIELD_INDEX_COMBAT);
            this.combat = combat;
        }
        return this;
    }

    /**
     * inner set combat
     *
     * @param combat new value
     */
    private void innerSetCombat(long combat) {
        this.combat = combat;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public PlayerCardInfoProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get clanSimpleName
     *
     * @return clanSimpleName value
     */
    public String getClanSimpleName() {
        return this.clanSimpleName;
    }

    /**
     * set clanSimpleName && set marked
     *
     * @param clanSimpleName new value
     * @return current object
     */
    public PlayerCardInfoProp setClanSimpleName(String clanSimpleName) {
        if (clanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, clanSimpleName)) {
            this.mark(FIELD_INDEX_CLANSIMPLENAME);
            this.clanSimpleName = clanSimpleName;
        }
        return this;
    }

    /**
     * inner set clanSimpleName
     *
     * @param clanSimpleName new value
     */
    private void innerSetClanSimpleName(String clanSimpleName) {
        this.clanSimpleName = clanSimpleName;
    }

    /**
     * get clanName
     *
     * @return clanName value
     */
    public String getClanName() {
        return this.clanName;
    }

    /**
     * set clanName && set marked
     *
     * @param clanName new value
     * @return current object
     */
    public PlayerCardInfoProp setClanName(String clanName) {
        if (clanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, clanName)) {
            this.mark(FIELD_INDEX_CLANNAME);
            this.clanName = clanName;
        }
        return this;
    }

    /**
     * inner set clanName
     *
     * @param clanName new value
     */
    private void innerSetClanName(String clanName) {
        this.clanName = clanName;
    }

    /**
     * get cityLevel
     *
     * @return cityLevel value
     */
    public int getCityLevel() {
        return this.cityLevel;
    }

    /**
     * set cityLevel && set marked
     *
     * @param cityLevel new value
     * @return current object
     */
    public PlayerCardInfoProp setCityLevel(int cityLevel) {
        if (this.cityLevel != cityLevel) {
            this.mark(FIELD_INDEX_CITYLEVEL);
            this.cityLevel = cityLevel;
        }
        return this;
    }

    /**
     * inner set cityLevel
     *
     * @param cityLevel new value
     */
    private void innerSetCityLevel(int cityLevel) {
        this.cityLevel = cityLevel;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get killScore
     *
     * @return killScore value
     */
    public long getKillScore() {
        return this.killScore;
    }

    /**
     * set killScore && set marked
     *
     * @param killScore new value
     * @return current object
     */
    public PlayerCardInfoProp setKillScore(long killScore) {
        if (this.killScore != killScore) {
            this.mark(FIELD_INDEX_KILLSCORE);
            this.killScore = killScore;
        }
        return this;
    }

    /**
     * inner set killScore
     *
     * @param killScore new value
     */
    private void innerSetKillScore(long killScore) {
        this.killScore = killScore;
    }

    /**
     * get kingdomOffice
     *
     * @return kingdomOffice value
     */
    public int getKingdomOffice() {
        return this.kingdomOffice;
    }

    /**
     * set kingdomOffice && set marked
     *
     * @param kingdomOffice new value
     * @return current object
     */
    public PlayerCardInfoProp setKingdomOffice(int kingdomOffice) {
        if (this.kingdomOffice != kingdomOffice) {
            this.mark(FIELD_INDEX_KINGDOMOFFICE);
            this.kingdomOffice = kingdomOffice;
        }
        return this;
    }

    /**
     * inner set kingdomOffice
     *
     * @param kingdomOffice new value
     */
    private void innerSetKingdomOffice(int kingdomOffice) {
        this.kingdomOffice = kingdomOffice;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public PlayerCardInfoProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    /**
     * get citySkinId
     *
     * @return citySkinId value
     */
    public int getCitySkinId() {
        return this.citySkinId;
    }

    /**
     * set citySkinId && set marked
     *
     * @param citySkinId new value
     * @return current object
     */
    public PlayerCardInfoProp setCitySkinId(int citySkinId) {
        if (this.citySkinId != citySkinId) {
            this.mark(FIELD_INDEX_CITYSKINID);
            this.citySkinId = citySkinId;
        }
        return this;
    }

    /**
     * inner set citySkinId
     *
     * @param citySkinId new value
     */
    private void innerSetCitySkinId(int citySkinId) {
        this.citySkinId = citySkinId;
    }

    /**
     * get logOff
     *
     * @return logOff value
     */
    public boolean getLogOff() {
        return this.logOff;
    }

    /**
     * set logOff && set marked
     *
     * @param logOff new value
     * @return current object
     */
    public PlayerCardInfoProp setLogOff(boolean logOff) {
        if (this.logOff != logOff) {
            this.mark(FIELD_INDEX_LOGOFF);
            this.logOff = logOff;
        }
        return this;
    }

    /**
     * inner set logOff
     *
     * @param logOff new value
     */
    private void innerSetLogOff(boolean logOff) {
        this.logOff = logOff;
    }

    /**
     * get achievementGoldNum
     *
     * @return achievementGoldNum value
     */
    public int getAchievementGoldNum() {
        return this.achievementGoldNum;
    }

    /**
     * set achievementGoldNum && set marked
     *
     * @param achievementGoldNum new value
     * @return current object
     */
    public PlayerCardInfoProp setAchievementGoldNum(int achievementGoldNum) {
        if (this.achievementGoldNum != achievementGoldNum) {
            this.mark(FIELD_INDEX_ACHIEVEMENTGOLDNUM);
            this.achievementGoldNum = achievementGoldNum;
        }
        return this;
    }

    /**
     * inner set achievementGoldNum
     *
     * @param achievementGoldNum new value
     */
    private void innerSetAchievementGoldNum(int achievementGoldNum) {
        this.achievementGoldNum = achievementGoldNum;
    }

    /**
     * get achievementSilverNum
     *
     * @return achievementSilverNum value
     */
    public int getAchievementSilverNum() {
        return this.achievementSilverNum;
    }

    /**
     * set achievementSilverNum && set marked
     *
     * @param achievementSilverNum new value
     * @return current object
     */
    public PlayerCardInfoProp setAchievementSilverNum(int achievementSilverNum) {
        if (this.achievementSilverNum != achievementSilverNum) {
            this.mark(FIELD_INDEX_ACHIEVEMENTSILVERNUM);
            this.achievementSilverNum = achievementSilverNum;
        }
        return this;
    }

    /**
     * inner set achievementSilverNum
     *
     * @param achievementSilverNum new value
     */
    private void innerSetAchievementSilverNum(int achievementSilverNum) {
        this.achievementSilverNum = achievementSilverNum;
    }

    /**
     * get achievementCopperNum
     *
     * @return achievementCopperNum value
     */
    public int getAchievementCopperNum() {
        return this.achievementCopperNum;
    }

    /**
     * set achievementCopperNum && set marked
     *
     * @param achievementCopperNum new value
     * @return current object
     */
    public PlayerCardInfoProp setAchievementCopperNum(int achievementCopperNum) {
        if (this.achievementCopperNum != achievementCopperNum) {
            this.mark(FIELD_INDEX_ACHIEVEMENTCOPPERNUM);
            this.achievementCopperNum = achievementCopperNum;
        }
        return this;
    }

    /**
     * inner set achievementCopperNum
     *
     * @param achievementCopperNum new value
     */
    private void innerSetAchievementCopperNum(int achievementCopperNum) {
        this.achievementCopperNum = achievementCopperNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCardInfoPB.Builder getCopyCsBuilder() {
        final PlayerCardInfoPB.Builder builder = PlayerCardInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerCardInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCombat() != 0L) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }  else if (builder.hasCombat()) {
            // 清理Combat
            builder.clearCombat();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getCityLevel() != 0) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }  else if (builder.hasCityLevel()) {
            // 清理CityLevel
            builder.clearCityLevel();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.getKingdomOffice() != 0) {
            builder.setKingdomOffice(this.getKingdomOffice());
            fieldCnt++;
        }  else if (builder.hasKingdomOffice()) {
            // 清理KingdomOffice
            builder.clearKingdomOffice();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getCitySkinId() != 0) {
            builder.setCitySkinId(this.getCitySkinId());
            fieldCnt++;
        }  else if (builder.hasCitySkinId()) {
            // 清理CitySkinId
            builder.clearCitySkinId();
            fieldCnt++;
        }
        if (this.getLogOff()) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }  else if (builder.hasLogOff()) {
            // 清理LogOff
            builder.clearLogOff();
            fieldCnt++;
        }
        if (this.getAchievementGoldNum() != 0) {
            builder.setAchievementGoldNum(this.getAchievementGoldNum());
            fieldCnt++;
        }  else if (builder.hasAchievementGoldNum()) {
            // 清理AchievementGoldNum
            builder.clearAchievementGoldNum();
            fieldCnt++;
        }
        if (this.getAchievementSilverNum() != 0) {
            builder.setAchievementSilverNum(this.getAchievementSilverNum());
            fieldCnt++;
        }  else if (builder.hasAchievementSilverNum()) {
            // 清理AchievementSilverNum
            builder.clearAchievementSilverNum();
            fieldCnt++;
        }
        if (this.getAchievementCopperNum() != 0) {
            builder.setAchievementCopperNum(this.getAchievementCopperNum());
            fieldCnt++;
        }  else if (builder.hasAchievementCopperNum()) {
            // 清理AchievementCopperNum
            builder.clearAchievementCopperNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerCardInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMOFFICE)) {
            builder.setKingdomOffice(this.getKingdomOffice());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYSKINID)) {
            builder.setCitySkinId(this.getCitySkinId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTGOLDNUM)) {
            builder.setAchievementGoldNum(this.getAchievementGoldNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTSILVERNUM)) {
            builder.setAchievementSilverNum(this.getAchievementSilverNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTCOPPERNUM)) {
            builder.setAchievementCopperNum(this.getAchievementCopperNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerCardInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMOFFICE)) {
            builder.setKingdomOffice(this.getKingdomOffice());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYSKINID)) {
            builder.setCitySkinId(this.getCitySkinId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTGOLDNUM)) {
            builder.setAchievementGoldNum(this.getAchievementGoldNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTSILVERNUM)) {
            builder.setAchievementSilverNum(this.getAchievementSilverNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTCOPPERNUM)) {
            builder.setAchievementCopperNum(this.getAchievementCopperNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerCardInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCombat()) {
            this.innerSetCombat(proto.getCombat());
        } else {
            this.innerSetCombat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCityLevel()) {
            this.innerSetCityLevel(proto.getCityLevel());
        } else {
            this.innerSetCityLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasKingdomOffice()) {
            this.innerSetKingdomOffice(proto.getKingdomOffice());
        } else {
            this.innerSetKingdomOffice(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCitySkinId()) {
            this.innerSetCitySkinId(proto.getCitySkinId());
        } else {
            this.innerSetCitySkinId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLogOff()) {
            this.innerSetLogOff(proto.getLogOff());
        } else {
            this.innerSetLogOff(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAchievementGoldNum()) {
            this.innerSetAchievementGoldNum(proto.getAchievementGoldNum());
        } else {
            this.innerSetAchievementGoldNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAchievementSilverNum()) {
            this.innerSetAchievementSilverNum(proto.getAchievementSilverNum());
        } else {
            this.innerSetAchievementSilverNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAchievementCopperNum()) {
            this.innerSetAchievementCopperNum(proto.getAchievementCopperNum());
        } else {
            this.innerSetAchievementCopperNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerCardInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerCardInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCombat()) {
            this.setCombat(proto.getCombat());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasCityLevel()) {
            this.setCityLevel(proto.getCityLevel());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasKingdomOffice()) {
            this.setKingdomOffice(proto.getKingdomOffice());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasCitySkinId()) {
            this.setCitySkinId(proto.getCitySkinId());
            fieldCnt++;
        }
        if (proto.hasLogOff()) {
            this.setLogOff(proto.getLogOff());
            fieldCnt++;
        }
        if (proto.hasAchievementGoldNum()) {
            this.setAchievementGoldNum(proto.getAchievementGoldNum());
            fieldCnt++;
        }
        if (proto.hasAchievementSilverNum()) {
            this.setAchievementSilverNum(proto.getAchievementSilverNum());
            fieldCnt++;
        }
        if (proto.hasAchievementCopperNum()) {
            this.setAchievementCopperNum(proto.getAchievementCopperNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCardInfo.Builder getCopyDbBuilder() {
        final PlayerCardInfo.Builder builder = PlayerCardInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerCardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCombat() != 0L) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }  else if (builder.hasCombat()) {
            // 清理Combat
            builder.clearCombat();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getCityLevel() != 0) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }  else if (builder.hasCityLevel()) {
            // 清理CityLevel
            builder.clearCityLevel();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.getKingdomOffice() != 0) {
            builder.setKingdomOffice(this.getKingdomOffice());
            fieldCnt++;
        }  else if (builder.hasKingdomOffice()) {
            // 清理KingdomOffice
            builder.clearKingdomOffice();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getCitySkinId() != 0) {
            builder.setCitySkinId(this.getCitySkinId());
            fieldCnt++;
        }  else if (builder.hasCitySkinId()) {
            // 清理CitySkinId
            builder.clearCitySkinId();
            fieldCnt++;
        }
        if (this.getLogOff()) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }  else if (builder.hasLogOff()) {
            // 清理LogOff
            builder.clearLogOff();
            fieldCnt++;
        }
        if (this.getAchievementGoldNum() != 0) {
            builder.setAchievementGoldNum(this.getAchievementGoldNum());
            fieldCnt++;
        }  else if (builder.hasAchievementGoldNum()) {
            // 清理AchievementGoldNum
            builder.clearAchievementGoldNum();
            fieldCnt++;
        }
        if (this.getAchievementSilverNum() != 0) {
            builder.setAchievementSilverNum(this.getAchievementSilverNum());
            fieldCnt++;
        }  else if (builder.hasAchievementSilverNum()) {
            // 清理AchievementSilverNum
            builder.clearAchievementSilverNum();
            fieldCnt++;
        }
        if (this.getAchievementCopperNum() != 0) {
            builder.setAchievementCopperNum(this.getAchievementCopperNum());
            fieldCnt++;
        }  else if (builder.hasAchievementCopperNum()) {
            // 清理AchievementCopperNum
            builder.clearAchievementCopperNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerCardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMOFFICE)) {
            builder.setKingdomOffice(this.getKingdomOffice());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYSKINID)) {
            builder.setCitySkinId(this.getCitySkinId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTGOLDNUM)) {
            builder.setAchievementGoldNum(this.getAchievementGoldNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTSILVERNUM)) {
            builder.setAchievementSilverNum(this.getAchievementSilverNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTCOPPERNUM)) {
            builder.setAchievementCopperNum(this.getAchievementCopperNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerCardInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCombat()) {
            this.innerSetCombat(proto.getCombat());
        } else {
            this.innerSetCombat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCityLevel()) {
            this.innerSetCityLevel(proto.getCityLevel());
        } else {
            this.innerSetCityLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasKingdomOffice()) {
            this.innerSetKingdomOffice(proto.getKingdomOffice());
        } else {
            this.innerSetKingdomOffice(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCitySkinId()) {
            this.innerSetCitySkinId(proto.getCitySkinId());
        } else {
            this.innerSetCitySkinId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLogOff()) {
            this.innerSetLogOff(proto.getLogOff());
        } else {
            this.innerSetLogOff(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAchievementGoldNum()) {
            this.innerSetAchievementGoldNum(proto.getAchievementGoldNum());
        } else {
            this.innerSetAchievementGoldNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAchievementSilverNum()) {
            this.innerSetAchievementSilverNum(proto.getAchievementSilverNum());
        } else {
            this.innerSetAchievementSilverNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAchievementCopperNum()) {
            this.innerSetAchievementCopperNum(proto.getAchievementCopperNum());
        } else {
            this.innerSetAchievementCopperNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerCardInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerCardInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCombat()) {
            this.setCombat(proto.getCombat());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasCityLevel()) {
            this.setCityLevel(proto.getCityLevel());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasKingdomOffice()) {
            this.setKingdomOffice(proto.getKingdomOffice());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasCitySkinId()) {
            this.setCitySkinId(proto.getCitySkinId());
            fieldCnt++;
        }
        if (proto.hasLogOff()) {
            this.setLogOff(proto.getLogOff());
            fieldCnt++;
        }
        if (proto.hasAchievementGoldNum()) {
            this.setAchievementGoldNum(proto.getAchievementGoldNum());
            fieldCnt++;
        }
        if (proto.hasAchievementSilverNum()) {
            this.setAchievementSilverNum(proto.getAchievementSilverNum());
            fieldCnt++;
        }
        if (proto.hasAchievementCopperNum()) {
            this.setAchievementCopperNum(proto.getAchievementCopperNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCardInfo.Builder getCopySsBuilder() {
        final PlayerCardInfo.Builder builder = PlayerCardInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerCardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCombat() != 0L) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }  else if (builder.hasCombat()) {
            // 清理Combat
            builder.clearCombat();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasClanSimpleName()) {
            // 清理ClanSimpleName
            builder.clearClanSimpleName();
            fieldCnt++;
        }
        if (!this.getClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }  else if (builder.hasClanName()) {
            // 清理ClanName
            builder.clearClanName();
            fieldCnt++;
        }
        if (this.getCityLevel() != 0) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }  else if (builder.hasCityLevel()) {
            // 清理CityLevel
            builder.clearCityLevel();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.getKingdomOffice() != 0) {
            builder.setKingdomOffice(this.getKingdomOffice());
            fieldCnt++;
        }  else if (builder.hasKingdomOffice()) {
            // 清理KingdomOffice
            builder.clearKingdomOffice();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getCitySkinId() != 0) {
            builder.setCitySkinId(this.getCitySkinId());
            fieldCnt++;
        }  else if (builder.hasCitySkinId()) {
            // 清理CitySkinId
            builder.clearCitySkinId();
            fieldCnt++;
        }
        if (this.getLogOff()) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }  else if (builder.hasLogOff()) {
            // 清理LogOff
            builder.clearLogOff();
            fieldCnt++;
        }
        if (this.getAchievementGoldNum() != 0) {
            builder.setAchievementGoldNum(this.getAchievementGoldNum());
            fieldCnt++;
        }  else if (builder.hasAchievementGoldNum()) {
            // 清理AchievementGoldNum
            builder.clearAchievementGoldNum();
            fieldCnt++;
        }
        if (this.getAchievementSilverNum() != 0) {
            builder.setAchievementSilverNum(this.getAchievementSilverNum());
            fieldCnt++;
        }  else if (builder.hasAchievementSilverNum()) {
            // 清理AchievementSilverNum
            builder.clearAchievementSilverNum();
            fieldCnt++;
        }
        if (this.getAchievementCopperNum() != 0) {
            builder.setAchievementCopperNum(this.getAchievementCopperNum());
            fieldCnt++;
        }  else if (builder.hasAchievementCopperNum()) {
            // 清理AchievementCopperNum
            builder.clearAchievementCopperNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerCardInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANSIMPLENAME)) {
            builder.setClanSimpleName(this.getClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANNAME)) {
            builder.setClanName(this.getClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYLEVEL)) {
            builder.setCityLevel(this.getCityLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KINGDOMOFFICE)) {
            builder.setKingdomOffice(this.getKingdomOffice());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CITYSKINID)) {
            builder.setCitySkinId(this.getCitySkinId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LOGOFF)) {
            builder.setLogOff(this.getLogOff());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTGOLDNUM)) {
            builder.setAchievementGoldNum(this.getAchievementGoldNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTSILVERNUM)) {
            builder.setAchievementSilverNum(this.getAchievementSilverNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACHIEVEMENTCOPPERNUM)) {
            builder.setAchievementCopperNum(this.getAchievementCopperNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerCardInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCombat()) {
            this.innerSetCombat(proto.getCombat());
        } else {
            this.innerSetCombat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasClanSimpleName()) {
            this.innerSetClanSimpleName(proto.getClanSimpleName());
        } else {
            this.innerSetClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanName()) {
            this.innerSetClanName(proto.getClanName());
        } else {
            this.innerSetClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasCityLevel()) {
            this.innerSetCityLevel(proto.getCityLevel());
        } else {
            this.innerSetCityLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasKingdomOffice()) {
            this.innerSetKingdomOffice(proto.getKingdomOffice());
        } else {
            this.innerSetKingdomOffice(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCitySkinId()) {
            this.innerSetCitySkinId(proto.getCitySkinId());
        } else {
            this.innerSetCitySkinId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLogOff()) {
            this.innerSetLogOff(proto.getLogOff());
        } else {
            this.innerSetLogOff(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasAchievementGoldNum()) {
            this.innerSetAchievementGoldNum(proto.getAchievementGoldNum());
        } else {
            this.innerSetAchievementGoldNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAchievementSilverNum()) {
            this.innerSetAchievementSilverNum(proto.getAchievementSilverNum());
        } else {
            this.innerSetAchievementSilverNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAchievementCopperNum()) {
            this.innerSetAchievementCopperNum(proto.getAchievementCopperNum());
        } else {
            this.innerSetAchievementCopperNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerCardInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerCardInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCombat()) {
            this.setCombat(proto.getCombat());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasClanSimpleName()) {
            this.setClanSimpleName(proto.getClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasClanName()) {
            this.setClanName(proto.getClanName());
            fieldCnt++;
        }
        if (proto.hasCityLevel()) {
            this.setCityLevel(proto.getCityLevel());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasKingdomOffice()) {
            this.setKingdomOffice(proto.getKingdomOffice());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasCitySkinId()) {
            this.setCitySkinId(proto.getCitySkinId());
            fieldCnt++;
        }
        if (proto.hasLogOff()) {
            this.setLogOff(proto.getLogOff());
            fieldCnt++;
        }
        if (proto.hasAchievementGoldNum()) {
            this.setAchievementGoldNum(proto.getAchievementGoldNum());
            fieldCnt++;
        }
        if (proto.hasAchievementSilverNum()) {
            this.setAchievementSilverNum(proto.getAchievementSilverNum());
            fieldCnt++;
        }
        if (proto.hasAchievementCopperNum()) {
            this.setAchievementCopperNum(proto.getAchievementCopperNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerCardInfo.Builder builder = PlayerCardInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerCardInfoProp)) {
            return false;
        }
        final PlayerCardInfoProp otherNode = (PlayerCardInfoProp) node;
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.combat != otherNode.combat) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanSimpleName, otherNode.clanSimpleName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.clanName, otherNode.clanName)) {
            return false;
        }
        if (this.cityLevel != otherNode.cityLevel) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (this.killScore != otherNode.killScore) {
            return false;
        }
        if (this.kingdomOffice != otherNode.kingdomOffice) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        if (this.citySkinId != otherNode.citySkinId) {
            return false;
        }
        if (this.logOff != otherNode.logOff) {
            return false;
        }
        if (this.achievementGoldNum != otherNode.achievementGoldNum) {
            return false;
        }
        if (this.achievementSilverNum != otherNode.achievementSilverNum) {
            return false;
        }
        if (this.achievementCopperNum != otherNode.achievementCopperNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 49;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanTechModel;
import com.yorha.proto.Struct;
import com.yorha.proto.ClanPB.ClanTechModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanTechModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_FINISHEDTECH = 0;
    public static final int FIELD_INDEX_RESEARCHINGTECH = 1;
    public static final int FIELD_INDEX_RECOMMENDTECHID = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private Int32ClanTechInfoMapProp finishedTech = null;
    private Int32ClanTechResearchInfoMapProp researchingTech = null;
    private int recommendTechId = Constant.DEFAULT_INT_VALUE;

    public ClanTechModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanTechModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get finishedTech
     *
     * @return finishedTech value
     */
    public Int32ClanTechInfoMapProp getFinishedTech() {
        if (this.finishedTech == null) {
            this.finishedTech = new Int32ClanTechInfoMapProp(this, FIELD_INDEX_FINISHEDTECH);
        }
        return this.finishedTech;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putFinishedTechV(ClanTechInfoProp v) {
        this.getFinishedTech().put(v.getTechId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanTechInfoProp addEmptyFinishedTech(Integer k) {
        return this.getFinishedTech().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getFinishedTechSize() {
        if (this.finishedTech == null) {
            return 0;
        }
        return this.finishedTech.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isFinishedTechEmpty() {
        if (this.finishedTech == null) {
            return true;
        }
        return this.finishedTech.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanTechInfoProp getFinishedTechV(Integer k) {
        if (this.finishedTech == null || !this.finishedTech.containsKey(k)) {
            return null;
        }
        return this.finishedTech.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearFinishedTech() {
        if (this.finishedTech != null) {
            this.finishedTech.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeFinishedTechV(Integer k) {
        if (this.finishedTech != null) {
            this.finishedTech.remove(k);
        }
    }
    /**
     * get researchingTech
     *
     * @return researchingTech value
     */
    public Int32ClanTechResearchInfoMapProp getResearchingTech() {
        if (this.researchingTech == null) {
            this.researchingTech = new Int32ClanTechResearchInfoMapProp(this, FIELD_INDEX_RESEARCHINGTECH);
        }
        return this.researchingTech;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putResearchingTechV(ClanTechResearchInfoProp v) {
        this.getResearchingTech().put(v.getTechSubId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanTechResearchInfoProp addEmptyResearchingTech(Integer k) {
        return this.getResearchingTech().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getResearchingTechSize() {
        if (this.researchingTech == null) {
            return 0;
        }
        return this.researchingTech.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isResearchingTechEmpty() {
        if (this.researchingTech == null) {
            return true;
        }
        return this.researchingTech.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanTechResearchInfoProp getResearchingTechV(Integer k) {
        if (this.researchingTech == null || !this.researchingTech.containsKey(k)) {
            return null;
        }
        return this.researchingTech.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearResearchingTech() {
        if (this.researchingTech != null) {
            this.researchingTech.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeResearchingTechV(Integer k) {
        if (this.researchingTech != null) {
            this.researchingTech.remove(k);
        }
    }
    /**
     * get recommendTechId
     *
     * @return recommendTechId value
     */
    public int getRecommendTechId() {
        return this.recommendTechId;
    }

    /**
     * set recommendTechId && set marked
     *
     * @param recommendTechId new value
     * @return current object
     */
    public ClanTechModelProp setRecommendTechId(int recommendTechId) {
        if (this.recommendTechId != recommendTechId) {
            this.mark(FIELD_INDEX_RECOMMENDTECHID);
            this.recommendTechId = recommendTechId;
        }
        return this;
    }

    /**
     * inner set recommendTechId
     *
     * @param recommendTechId new value
     */
    private void innerSetRecommendTechId(int recommendTechId) {
        this.recommendTechId = recommendTechId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechModelPB.Builder getCopyCsBuilder() {
        final ClanTechModelPB.Builder builder = ClanTechModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.finishedTech != null) {
            StructPB.Int32ClanTechInfoMapPB.Builder tmpBuilder = StructPB.Int32ClanTechInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.finishedTech.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFinishedTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFinishedTech();
            }
        }  else if (builder.hasFinishedTech()) {
            // 清理FinishedTech
            builder.clearFinishedTech();
            fieldCnt++;
        }
        if (this.researchingTech != null) {
            StructPB.Int32ClanTechResearchInfoMapPB.Builder tmpBuilder = StructPB.Int32ClanTechResearchInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.researchingTech.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResearchingTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResearchingTech();
            }
        }  else if (builder.hasResearchingTech()) {
            // 清理ResearchingTech
            builder.clearResearchingTech();
            fieldCnt++;
        }
        if (this.getRecommendTechId() != 0) {
            builder.setRecommendTechId(this.getRecommendTechId());
            fieldCnt++;
        }  else if (builder.hasRecommendTechId()) {
            // 清理RecommendTechId
            builder.clearRecommendTechId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FINISHEDTECH) && this.finishedTech != null) {
            final boolean needClear = !builder.hasFinishedTech();
            final int tmpFieldCnt = this.finishedTech.copyChangeToCs(builder.getFinishedTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESEARCHINGTECH) && this.researchingTech != null) {
            final boolean needClear = !builder.hasResearchingTech();
            final int tmpFieldCnt = this.researchingTech.copyChangeToCs(builder.getResearchingTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResearchingTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDTECHID)) {
            builder.setRecommendTechId(this.getRecommendTechId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FINISHEDTECH) && this.finishedTech != null) {
            final boolean needClear = !builder.hasFinishedTech();
            final int tmpFieldCnt = this.finishedTech.copyChangeToAndClearDeleteKeysCs(builder.getFinishedTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESEARCHINGTECH) && this.researchingTech != null) {
            final boolean needClear = !builder.hasResearchingTech();
            final int tmpFieldCnt = this.researchingTech.copyChangeToAndClearDeleteKeysCs(builder.getResearchingTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResearchingTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDTECHID)) {
            builder.setRecommendTechId(this.getRecommendTechId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanTechModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFinishedTech()) {
            this.getFinishedTech().mergeFromCs(proto.getFinishedTech());
        } else {
            if (this.finishedTech != null) {
                this.finishedTech.mergeFromCs(proto.getFinishedTech());
            }
        }
        if (proto.hasResearchingTech()) {
            this.getResearchingTech().mergeFromCs(proto.getResearchingTech());
        } else {
            if (this.researchingTech != null) {
                this.researchingTech.mergeFromCs(proto.getResearchingTech());
            }
        }
        if (proto.hasRecommendTechId()) {
            this.innerSetRecommendTechId(proto.getRecommendTechId());
        } else {
            this.innerSetRecommendTechId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanTechModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFinishedTech()) {
            this.getFinishedTech().mergeChangeFromCs(proto.getFinishedTech());
            fieldCnt++;
        }
        if (proto.hasResearchingTech()) {
            this.getResearchingTech().mergeChangeFromCs(proto.getResearchingTech());
            fieldCnt++;
        }
        if (proto.hasRecommendTechId()) {
            this.setRecommendTechId(proto.getRecommendTechId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechModel.Builder getCopyDbBuilder() {
        final ClanTechModel.Builder builder = ClanTechModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.finishedTech != null) {
            Struct.Int32ClanTechInfoMap.Builder tmpBuilder = Struct.Int32ClanTechInfoMap.newBuilder();
            final int tmpFieldCnt = this.finishedTech.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFinishedTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFinishedTech();
            }
        }  else if (builder.hasFinishedTech()) {
            // 清理FinishedTech
            builder.clearFinishedTech();
            fieldCnt++;
        }
        if (this.researchingTech != null) {
            Struct.Int32ClanTechResearchInfoMap.Builder tmpBuilder = Struct.Int32ClanTechResearchInfoMap.newBuilder();
            final int tmpFieldCnt = this.researchingTech.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResearchingTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResearchingTech();
            }
        }  else if (builder.hasResearchingTech()) {
            // 清理ResearchingTech
            builder.clearResearchingTech();
            fieldCnt++;
        }
        if (this.getRecommendTechId() != 0) {
            builder.setRecommendTechId(this.getRecommendTechId());
            fieldCnt++;
        }  else if (builder.hasRecommendTechId()) {
            // 清理RecommendTechId
            builder.clearRecommendTechId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FINISHEDTECH) && this.finishedTech != null) {
            final boolean needClear = !builder.hasFinishedTech();
            final int tmpFieldCnt = this.finishedTech.copyChangeToDb(builder.getFinishedTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESEARCHINGTECH) && this.researchingTech != null) {
            final boolean needClear = !builder.hasResearchingTech();
            final int tmpFieldCnt = this.researchingTech.copyChangeToDb(builder.getResearchingTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResearchingTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDTECHID)) {
            builder.setRecommendTechId(this.getRecommendTechId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanTechModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFinishedTech()) {
            this.getFinishedTech().mergeFromDb(proto.getFinishedTech());
        } else {
            if (this.finishedTech != null) {
                this.finishedTech.mergeFromDb(proto.getFinishedTech());
            }
        }
        if (proto.hasResearchingTech()) {
            this.getResearchingTech().mergeFromDb(proto.getResearchingTech());
        } else {
            if (this.researchingTech != null) {
                this.researchingTech.mergeFromDb(proto.getResearchingTech());
            }
        }
        if (proto.hasRecommendTechId()) {
            this.innerSetRecommendTechId(proto.getRecommendTechId());
        } else {
            this.innerSetRecommendTechId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanTechModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFinishedTech()) {
            this.getFinishedTech().mergeChangeFromDb(proto.getFinishedTech());
            fieldCnt++;
        }
        if (proto.hasResearchingTech()) {
            this.getResearchingTech().mergeChangeFromDb(proto.getResearchingTech());
            fieldCnt++;
        }
        if (proto.hasRecommendTechId()) {
            this.setRecommendTechId(proto.getRecommendTechId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanTechModel.Builder getCopySsBuilder() {
        final ClanTechModel.Builder builder = ClanTechModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.finishedTech != null) {
            Struct.Int32ClanTechInfoMap.Builder tmpBuilder = Struct.Int32ClanTechInfoMap.newBuilder();
            final int tmpFieldCnt = this.finishedTech.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFinishedTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFinishedTech();
            }
        }  else if (builder.hasFinishedTech()) {
            // 清理FinishedTech
            builder.clearFinishedTech();
            fieldCnt++;
        }
        if (this.researchingTech != null) {
            Struct.Int32ClanTechResearchInfoMap.Builder tmpBuilder = Struct.Int32ClanTechResearchInfoMap.newBuilder();
            final int tmpFieldCnt = this.researchingTech.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResearchingTech(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResearchingTech();
            }
        }  else if (builder.hasResearchingTech()) {
            // 清理ResearchingTech
            builder.clearResearchingTech();
            fieldCnt++;
        }
        if (this.getRecommendTechId() != 0) {
            builder.setRecommendTechId(this.getRecommendTechId());
            fieldCnt++;
        }  else if (builder.hasRecommendTechId()) {
            // 清理RecommendTechId
            builder.clearRecommendTechId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FINISHEDTECH) && this.finishedTech != null) {
            final boolean needClear = !builder.hasFinishedTech();
            final int tmpFieldCnt = this.finishedTech.copyChangeToSs(builder.getFinishedTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESEARCHINGTECH) && this.researchingTech != null) {
            final boolean needClear = !builder.hasResearchingTech();
            final int tmpFieldCnt = this.researchingTech.copyChangeToSs(builder.getResearchingTechBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResearchingTech();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDTECHID)) {
            builder.setRecommendTechId(this.getRecommendTechId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanTechModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFinishedTech()) {
            this.getFinishedTech().mergeFromSs(proto.getFinishedTech());
        } else {
            if (this.finishedTech != null) {
                this.finishedTech.mergeFromSs(proto.getFinishedTech());
            }
        }
        if (proto.hasResearchingTech()) {
            this.getResearchingTech().mergeFromSs(proto.getResearchingTech());
        } else {
            if (this.researchingTech != null) {
                this.researchingTech.mergeFromSs(proto.getResearchingTech());
            }
        }
        if (proto.hasRecommendTechId()) {
            this.innerSetRecommendTechId(proto.getRecommendTechId());
        } else {
            this.innerSetRecommendTechId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanTechModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFinishedTech()) {
            this.getFinishedTech().mergeChangeFromSs(proto.getFinishedTech());
            fieldCnt++;
        }
        if (proto.hasResearchingTech()) {
            this.getResearchingTech().mergeChangeFromSs(proto.getResearchingTech());
            fieldCnt++;
        }
        if (proto.hasRecommendTechId()) {
            this.setRecommendTechId(proto.getRecommendTechId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanTechModel.Builder builder = ClanTechModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_FINISHEDTECH) && this.finishedTech != null) {
            this.finishedTech.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESEARCHINGTECH) && this.researchingTech != null) {
            this.researchingTech.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.finishedTech != null) {
            this.finishedTech.markAll();
        }
        if (this.researchingTech != null) {
            this.researchingTech.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanTechModelProp)) {
            return false;
        }
        final ClanTechModelProp otherNode = (ClanTechModelProp) node;
        if (!this.getFinishedTech().compareDataTo(otherNode.getFinishedTech())) {
            return false;
        }
        if (!this.getResearchingTech().compareDataTo(otherNode.getResearchingTech())) {
            return false;
        }
        if (this.recommendTechId != otherNode.recommendTechId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
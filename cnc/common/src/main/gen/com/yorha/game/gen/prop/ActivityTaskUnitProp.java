package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityTaskUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityTaskUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityTaskUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TASKS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32TaskInfoMapProp tasks = null;

    public ActivityTaskUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityTaskUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get tasks
     *
     * @return tasks value
     */
    public Int32TaskInfoMapProp getTasks() {
        if (this.tasks == null) {
            this.tasks = new Int32TaskInfoMapProp(this, FIELD_INDEX_TASKS);
        }
        return this.tasks;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTasksV(TaskInfoProp v) {
        this.getTasks().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TaskInfoProp addEmptyTasks(Integer k) {
        return this.getTasks().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTasksSize() {
        if (this.tasks == null) {
            return 0;
        }
        return this.tasks.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTasksEmpty() {
        if (this.tasks == null) {
            return true;
        }
        return this.tasks.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TaskInfoProp getTasksV(Integer k) {
        if (this.tasks == null || !this.tasks.containsKey(k)) {
            return null;
        }
        return this.tasks.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTasks() {
        if (this.tasks != null) {
            this.tasks.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTasksV(Integer k) {
        if (this.tasks != null) {
            this.tasks.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityTaskUnitPB.Builder getCopyCsBuilder() {
        final ActivityTaskUnitPB.Builder builder = ActivityTaskUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityTaskUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.tasks != null) {
            StructPB.Int32TaskInfoMapPB.Builder tmpBuilder = StructPB.Int32TaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.tasks.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTasks();
            }
        }  else if (builder.hasTasks()) {
            // 清理Tasks
            builder.clearTasks();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityTaskUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToCs(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityTaskUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToAndClearDeleteKeysCs(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityTaskUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeFromCs(proto.getTasks());
        } else {
            if (this.tasks != null) {
                this.tasks.mergeFromCs(proto.getTasks());
            }
        }
        this.markAll();
        return ActivityTaskUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityTaskUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTasks()) {
            this.getTasks().mergeChangeFromCs(proto.getTasks());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityTaskUnit.Builder getCopyDbBuilder() {
        final ActivityTaskUnit.Builder builder = ActivityTaskUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityTaskUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.tasks != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.tasks.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTasks();
            }
        }  else if (builder.hasTasks()) {
            // 清理Tasks
            builder.clearTasks();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityTaskUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToDb(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityTaskUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeFromDb(proto.getTasks());
        } else {
            if (this.tasks != null) {
                this.tasks.mergeFromDb(proto.getTasks());
            }
        }
        this.markAll();
        return ActivityTaskUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityTaskUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTasks()) {
            this.getTasks().mergeChangeFromDb(proto.getTasks());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityTaskUnit.Builder getCopySsBuilder() {
        final ActivityTaskUnit.Builder builder = ActivityTaskUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityTaskUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.tasks != null) {
            Struct.Int32TaskInfoMap.Builder tmpBuilder = Struct.Int32TaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.tasks.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTasks();
            }
        }  else if (builder.hasTasks()) {
            // 清理Tasks
            builder.clearTasks();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityTaskUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToSs(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityTaskUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeFromSs(proto.getTasks());
        } else {
            if (this.tasks != null) {
                this.tasks.mergeFromSs(proto.getTasks());
            }
        }
        this.markAll();
        return ActivityTaskUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityTaskUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTasks()) {
            this.getTasks().mergeChangeFromSs(proto.getTasks());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityTaskUnit.Builder builder = ActivityTaskUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            this.tasks.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.tasks != null) {
            this.tasks.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityTaskUnitProp)) {
            return false;
        }
        final ActivityTaskUnitProp otherNode = (ActivityTaskUnitProp) node;
        if (!this.getTasks().compareDataTo(otherNode.getTasks())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
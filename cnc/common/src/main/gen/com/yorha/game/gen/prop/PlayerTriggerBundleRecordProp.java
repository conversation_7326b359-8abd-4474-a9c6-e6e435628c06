package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerTriggerBundleRecord;
import com.yorha.proto.StructPB.PlayerTriggerBundleRecordPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerTriggerBundleRecordProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TEMPLATEID = 0;
    public static final int FIELD_INDEX_LASTHAPPENTSSEC = 1;
    public static final int FIELD_INDEX_LASTBUNDLEID = 2;
    public static final int FIELD_INDEX_TIMES = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int templateId = Constant.DEFAULT_INT_VALUE;
    private long lastHappenTsSec = Constant.DEFAULT_LONG_VALUE;
    private long lastBundleId = Constant.DEFAULT_LONG_VALUE;
    private int times = Constant.DEFAULT_INT_VALUE;

    public PlayerTriggerBundleRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerTriggerBundleRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public PlayerTriggerBundleRecordProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get lastHappenTsSec
     *
     * @return lastHappenTsSec value
     */
    public long getLastHappenTsSec() {
        return this.lastHappenTsSec;
    }

    /**
     * set lastHappenTsSec && set marked
     *
     * @param lastHappenTsSec new value
     * @return current object
     */
    public PlayerTriggerBundleRecordProp setLastHappenTsSec(long lastHappenTsSec) {
        if (this.lastHappenTsSec != lastHappenTsSec) {
            this.mark(FIELD_INDEX_LASTHAPPENTSSEC);
            this.lastHappenTsSec = lastHappenTsSec;
        }
        return this;
    }

    /**
     * inner set lastHappenTsSec
     *
     * @param lastHappenTsSec new value
     */
    private void innerSetLastHappenTsSec(long lastHappenTsSec) {
        this.lastHappenTsSec = lastHappenTsSec;
    }

    /**
     * get lastBundleId
     *
     * @return lastBundleId value
     */
    public long getLastBundleId() {
        return this.lastBundleId;
    }

    /**
     * set lastBundleId && set marked
     *
     * @param lastBundleId new value
     * @return current object
     */
    public PlayerTriggerBundleRecordProp setLastBundleId(long lastBundleId) {
        if (this.lastBundleId != lastBundleId) {
            this.mark(FIELD_INDEX_LASTBUNDLEID);
            this.lastBundleId = lastBundleId;
        }
        return this;
    }

    /**
     * inner set lastBundleId
     *
     * @param lastBundleId new value
     */
    private void innerSetLastBundleId(long lastBundleId) {
        this.lastBundleId = lastBundleId;
    }

    /**
     * get times
     *
     * @return times value
     */
    public int getTimes() {
        return this.times;
    }

    /**
     * set times && set marked
     *
     * @param times new value
     * @return current object
     */
    public PlayerTriggerBundleRecordProp setTimes(int times) {
        if (this.times != times) {
            this.mark(FIELD_INDEX_TIMES);
            this.times = times;
        }
        return this;
    }

    /**
     * inner set times
     *
     * @param times new value
     */
    private void innerSetTimes(int times) {
        this.times = times;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleRecordPB.Builder getCopyCsBuilder() {
        final PlayerTriggerBundleRecordPB.Builder builder = PlayerTriggerBundleRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerTriggerBundleRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getLastHappenTsSec() != 0L) {
            builder.setLastHappenTsSec(this.getLastHappenTsSec());
            fieldCnt++;
        }  else if (builder.hasLastHappenTsSec()) {
            // 清理LastHappenTsSec
            builder.clearLastHappenTsSec();
            fieldCnt++;
        }
        if (this.getLastBundleId() != 0L) {
            builder.setLastBundleId(this.getLastBundleId());
            fieldCnt++;
        }  else if (builder.hasLastBundleId()) {
            // 清理LastBundleId
            builder.clearLastBundleId();
            fieldCnt++;
        }
        if (this.getTimes() != 0) {
            builder.setTimes(this.getTimes());
            fieldCnt++;
        }  else if (builder.hasTimes()) {
            // 清理Times
            builder.clearTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerTriggerBundleRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTHAPPENTSSEC)) {
            builder.setLastHappenTsSec(this.getLastHappenTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBUNDLEID)) {
            builder.setLastBundleId(this.getLastBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMES)) {
            builder.setTimes(this.getTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerTriggerBundleRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTHAPPENTSSEC)) {
            builder.setLastHappenTsSec(this.getLastHappenTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBUNDLEID)) {
            builder.setLastBundleId(this.getLastBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMES)) {
            builder.setTimes(this.getTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerTriggerBundleRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastHappenTsSec()) {
            this.innerSetLastHappenTsSec(proto.getLastHappenTsSec());
        } else {
            this.innerSetLastHappenTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastBundleId()) {
            this.innerSetLastBundleId(proto.getLastBundleId());
        } else {
            this.innerSetLastBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTimes()) {
            this.innerSetTimes(proto.getTimes());
        } else {
            this.innerSetTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerTriggerBundleRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasLastHappenTsSec()) {
            this.setLastHappenTsSec(proto.getLastHappenTsSec());
            fieldCnt++;
        }
        if (proto.hasLastBundleId()) {
            this.setLastBundleId(proto.getLastBundleId());
            fieldCnt++;
        }
        if (proto.hasTimes()) {
            this.setTimes(proto.getTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleRecord.Builder getCopyDbBuilder() {
        final PlayerTriggerBundleRecord.Builder builder = PlayerTriggerBundleRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerTriggerBundleRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getLastHappenTsSec() != 0L) {
            builder.setLastHappenTsSec(this.getLastHappenTsSec());
            fieldCnt++;
        }  else if (builder.hasLastHappenTsSec()) {
            // 清理LastHappenTsSec
            builder.clearLastHappenTsSec();
            fieldCnt++;
        }
        if (this.getLastBundleId() != 0L) {
            builder.setLastBundleId(this.getLastBundleId());
            fieldCnt++;
        }  else if (builder.hasLastBundleId()) {
            // 清理LastBundleId
            builder.clearLastBundleId();
            fieldCnt++;
        }
        if (this.getTimes() != 0) {
            builder.setTimes(this.getTimes());
            fieldCnt++;
        }  else if (builder.hasTimes()) {
            // 清理Times
            builder.clearTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerTriggerBundleRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTHAPPENTSSEC)) {
            builder.setLastHappenTsSec(this.getLastHappenTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBUNDLEID)) {
            builder.setLastBundleId(this.getLastBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMES)) {
            builder.setTimes(this.getTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerTriggerBundleRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastHappenTsSec()) {
            this.innerSetLastHappenTsSec(proto.getLastHappenTsSec());
        } else {
            this.innerSetLastHappenTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastBundleId()) {
            this.innerSetLastBundleId(proto.getLastBundleId());
        } else {
            this.innerSetLastBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTimes()) {
            this.innerSetTimes(proto.getTimes());
        } else {
            this.innerSetTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerTriggerBundleRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasLastHappenTsSec()) {
            this.setLastHappenTsSec(proto.getLastHappenTsSec());
            fieldCnt++;
        }
        if (proto.hasLastBundleId()) {
            this.setLastBundleId(proto.getLastBundleId());
            fieldCnt++;
        }
        if (proto.hasTimes()) {
            this.setTimes(proto.getTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleRecord.Builder getCopySsBuilder() {
        final PlayerTriggerBundleRecord.Builder builder = PlayerTriggerBundleRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerTriggerBundleRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getLastHappenTsSec() != 0L) {
            builder.setLastHappenTsSec(this.getLastHappenTsSec());
            fieldCnt++;
        }  else if (builder.hasLastHappenTsSec()) {
            // 清理LastHappenTsSec
            builder.clearLastHappenTsSec();
            fieldCnt++;
        }
        if (this.getLastBundleId() != 0L) {
            builder.setLastBundleId(this.getLastBundleId());
            fieldCnt++;
        }  else if (builder.hasLastBundleId()) {
            // 清理LastBundleId
            builder.clearLastBundleId();
            fieldCnt++;
        }
        if (this.getTimes() != 0) {
            builder.setTimes(this.getTimes());
            fieldCnt++;
        }  else if (builder.hasTimes()) {
            // 清理Times
            builder.clearTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerTriggerBundleRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTHAPPENTSSEC)) {
            builder.setLastHappenTsSec(this.getLastHappenTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTBUNDLEID)) {
            builder.setLastBundleId(this.getLastBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TIMES)) {
            builder.setTimes(this.getTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerTriggerBundleRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastHappenTsSec()) {
            this.innerSetLastHappenTsSec(proto.getLastHappenTsSec());
        } else {
            this.innerSetLastHappenTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastBundleId()) {
            this.innerSetLastBundleId(proto.getLastBundleId());
        } else {
            this.innerSetLastBundleId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTimes()) {
            this.innerSetTimes(proto.getTimes());
        } else {
            this.innerSetTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerTriggerBundleRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasLastHappenTsSec()) {
            this.setLastHappenTsSec(proto.getLastHappenTsSec());
            fieldCnt++;
        }
        if (proto.hasLastBundleId()) {
            this.setLastBundleId(proto.getLastBundleId());
            fieldCnt++;
        }
        if (proto.hasTimes()) {
            this.setTimes(proto.getTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerTriggerBundleRecord.Builder builder = PlayerTriggerBundleRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.templateId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerTriggerBundleRecordProp)) {
            return false;
        }
        final PlayerTriggerBundleRecordProp otherNode = (PlayerTriggerBundleRecordProp) node;
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.lastHappenTsSec != otherNode.lastHappenTsSec) {
            return false;
        }
        if (this.lastBundleId != otherNode.lastBundleId) {
            return false;
        }
        if (this.times != otherNode.times) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
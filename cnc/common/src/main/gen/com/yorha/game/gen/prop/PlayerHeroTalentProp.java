package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerHeroTalent;
import com.yorha.proto.PlayerPB.PlayerHeroTalentPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerHeroTalentProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TALENTGROUPID = 0;
    public static final int FIELD_INDEX_TALENTID = 1;
    public static final int FIELD_INDEX_USEDTALENTPOINT = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int talentGroupId = Constant.DEFAULT_INT_VALUE;
    private int talentId = Constant.DEFAULT_INT_VALUE;
    private int usedTalentPoint = Constant.DEFAULT_INT_VALUE;

    public PlayerHeroTalentProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerHeroTalentProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get talentGroupId
     *
     * @return talentGroupId value
     */
    public int getTalentGroupId() {
        return this.talentGroupId;
    }

    /**
     * set talentGroupId && set marked
     *
     * @param talentGroupId new value
     * @return current object
     */
    public PlayerHeroTalentProp setTalentGroupId(int talentGroupId) {
        if (this.talentGroupId != talentGroupId) {
            this.mark(FIELD_INDEX_TALENTGROUPID);
            this.talentGroupId = talentGroupId;
        }
        return this;
    }

    /**
     * inner set talentGroupId
     *
     * @param talentGroupId new value
     */
    private void innerSetTalentGroupId(int talentGroupId) {
        this.talentGroupId = talentGroupId;
    }

    /**
     * get talentId
     *
     * @return talentId value
     */
    public int getTalentId() {
        return this.talentId;
    }

    /**
     * set talentId && set marked
     *
     * @param talentId new value
     * @return current object
     */
    public PlayerHeroTalentProp setTalentId(int talentId) {
        if (this.talentId != talentId) {
            this.mark(FIELD_INDEX_TALENTID);
            this.talentId = talentId;
        }
        return this;
    }

    /**
     * inner set talentId
     *
     * @param talentId new value
     */
    private void innerSetTalentId(int talentId) {
        this.talentId = talentId;
    }

    /**
     * get usedTalentPoint
     *
     * @return usedTalentPoint value
     */
    public int getUsedTalentPoint() {
        return this.usedTalentPoint;
    }

    /**
     * set usedTalentPoint && set marked
     *
     * @param usedTalentPoint new value
     * @return current object
     */
    public PlayerHeroTalentProp setUsedTalentPoint(int usedTalentPoint) {
        if (this.usedTalentPoint != usedTalentPoint) {
            this.mark(FIELD_INDEX_USEDTALENTPOINT);
            this.usedTalentPoint = usedTalentPoint;
        }
        return this;
    }

    /**
     * inner set usedTalentPoint
     *
     * @param usedTalentPoint new value
     */
    private void innerSetUsedTalentPoint(int usedTalentPoint) {
        this.usedTalentPoint = usedTalentPoint;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroTalentPB.Builder getCopyCsBuilder() {
        final PlayerHeroTalentPB.Builder builder = PlayerHeroTalentPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerHeroTalentPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTalentGroupId() != 0) {
            builder.setTalentGroupId(this.getTalentGroupId());
            fieldCnt++;
        }  else if (builder.hasTalentGroupId()) {
            // 清理TalentGroupId
            builder.clearTalentGroupId();
            fieldCnt++;
        }
        if (this.getTalentId() != 0) {
            builder.setTalentId(this.getTalentId());
            fieldCnt++;
        }  else if (builder.hasTalentId()) {
            // 清理TalentId
            builder.clearTalentId();
            fieldCnt++;
        }
        if (this.getUsedTalentPoint() != 0) {
            builder.setUsedTalentPoint(this.getUsedTalentPoint());
            fieldCnt++;
        }  else if (builder.hasUsedTalentPoint()) {
            // 清理UsedTalentPoint
            builder.clearUsedTalentPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerHeroTalentPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TALENTGROUPID)) {
            builder.setTalentGroupId(this.getTalentGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTID)) {
            builder.setTalentId(this.getTalentId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDTALENTPOINT)) {
            builder.setUsedTalentPoint(this.getUsedTalentPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerHeroTalentPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TALENTGROUPID)) {
            builder.setTalentGroupId(this.getTalentGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTID)) {
            builder.setTalentId(this.getTalentId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDTALENTPOINT)) {
            builder.setUsedTalentPoint(this.getUsedTalentPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerHeroTalentPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTalentGroupId()) {
            this.innerSetTalentGroupId(proto.getTalentGroupId());
        } else {
            this.innerSetTalentGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentId()) {
            this.innerSetTalentId(proto.getTalentId());
        } else {
            this.innerSetTalentId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUsedTalentPoint()) {
            this.innerSetUsedTalentPoint(proto.getUsedTalentPoint());
        } else {
            this.innerSetUsedTalentPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroTalentProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerHeroTalentPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTalentGroupId()) {
            this.setTalentGroupId(proto.getTalentGroupId());
            fieldCnt++;
        }
        if (proto.hasTalentId()) {
            this.setTalentId(proto.getTalentId());
            fieldCnt++;
        }
        if (proto.hasUsedTalentPoint()) {
            this.setUsedTalentPoint(proto.getUsedTalentPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroTalent.Builder getCopyDbBuilder() {
        final PlayerHeroTalent.Builder builder = PlayerHeroTalent.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerHeroTalent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTalentGroupId() != 0) {
            builder.setTalentGroupId(this.getTalentGroupId());
            fieldCnt++;
        }  else if (builder.hasTalentGroupId()) {
            // 清理TalentGroupId
            builder.clearTalentGroupId();
            fieldCnt++;
        }
        if (this.getTalentId() != 0) {
            builder.setTalentId(this.getTalentId());
            fieldCnt++;
        }  else if (builder.hasTalentId()) {
            // 清理TalentId
            builder.clearTalentId();
            fieldCnt++;
        }
        if (this.getUsedTalentPoint() != 0) {
            builder.setUsedTalentPoint(this.getUsedTalentPoint());
            fieldCnt++;
        }  else if (builder.hasUsedTalentPoint()) {
            // 清理UsedTalentPoint
            builder.clearUsedTalentPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerHeroTalent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TALENTGROUPID)) {
            builder.setTalentGroupId(this.getTalentGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTID)) {
            builder.setTalentId(this.getTalentId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDTALENTPOINT)) {
            builder.setUsedTalentPoint(this.getUsedTalentPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerHeroTalent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTalentGroupId()) {
            this.innerSetTalentGroupId(proto.getTalentGroupId());
        } else {
            this.innerSetTalentGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentId()) {
            this.innerSetTalentId(proto.getTalentId());
        } else {
            this.innerSetTalentId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUsedTalentPoint()) {
            this.innerSetUsedTalentPoint(proto.getUsedTalentPoint());
        } else {
            this.innerSetUsedTalentPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroTalentProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerHeroTalent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTalentGroupId()) {
            this.setTalentGroupId(proto.getTalentGroupId());
            fieldCnt++;
        }
        if (proto.hasTalentId()) {
            this.setTalentId(proto.getTalentId());
            fieldCnt++;
        }
        if (proto.hasUsedTalentPoint()) {
            this.setUsedTalentPoint(proto.getUsedTalentPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroTalent.Builder getCopySsBuilder() {
        final PlayerHeroTalent.Builder builder = PlayerHeroTalent.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerHeroTalent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTalentGroupId() != 0) {
            builder.setTalentGroupId(this.getTalentGroupId());
            fieldCnt++;
        }  else if (builder.hasTalentGroupId()) {
            // 清理TalentGroupId
            builder.clearTalentGroupId();
            fieldCnt++;
        }
        if (this.getTalentId() != 0) {
            builder.setTalentId(this.getTalentId());
            fieldCnt++;
        }  else if (builder.hasTalentId()) {
            // 清理TalentId
            builder.clearTalentId();
            fieldCnt++;
        }
        if (this.getUsedTalentPoint() != 0) {
            builder.setUsedTalentPoint(this.getUsedTalentPoint());
            fieldCnt++;
        }  else if (builder.hasUsedTalentPoint()) {
            // 清理UsedTalentPoint
            builder.clearUsedTalentPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerHeroTalent.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TALENTGROUPID)) {
            builder.setTalentGroupId(this.getTalentGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TALENTID)) {
            builder.setTalentId(this.getTalentId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USEDTALENTPOINT)) {
            builder.setUsedTalentPoint(this.getUsedTalentPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerHeroTalent proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTalentGroupId()) {
            this.innerSetTalentGroupId(proto.getTalentGroupId());
        } else {
            this.innerSetTalentGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTalentId()) {
            this.innerSetTalentId(proto.getTalentId());
        } else {
            this.innerSetTalentId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUsedTalentPoint()) {
            this.innerSetUsedTalentPoint(proto.getUsedTalentPoint());
        } else {
            this.innerSetUsedTalentPoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerHeroTalentProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerHeroTalent proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTalentGroupId()) {
            this.setTalentGroupId(proto.getTalentGroupId());
            fieldCnt++;
        }
        if (proto.hasTalentId()) {
            this.setTalentId(proto.getTalentId());
            fieldCnt++;
        }
        if (proto.hasUsedTalentPoint()) {
            this.setUsedTalentPoint(proto.getUsedTalentPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerHeroTalent.Builder builder = PlayerHeroTalent.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.talentGroupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerHeroTalentProp)) {
            return false;
        }
        final PlayerHeroTalentProp otherNode = (PlayerHeroTalentProp) node;
        if (this.talentGroupId != otherNode.talentGroupId) {
            return false;
        }
        if (this.talentId != otherNode.talentId) {
            return false;
        }
        if (this.usedTalentPoint != otherNode.usedTalentPoint) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
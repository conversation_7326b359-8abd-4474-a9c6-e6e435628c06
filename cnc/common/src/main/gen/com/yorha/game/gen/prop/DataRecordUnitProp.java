package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.DataRecordUnit;
import com.yorha.proto.StructPB.DataRecordUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class DataRecordUnitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_RECORDTYPE = 0;
    public static final int FIELD_INDEX_VALUE = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int recordType = Constant.DEFAULT_INT_VALUE;
    private long value = Constant.DEFAULT_LONG_VALUE;

    public DataRecordUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DataRecordUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get recordType
     *
     * @return recordType value
     */
    public int getRecordType() {
        return this.recordType;
    }

    /**
     * set recordType && set marked
     *
     * @param recordType new value
     * @return current object
     */
    public DataRecordUnitProp setRecordType(int recordType) {
        if (this.recordType != recordType) {
            this.mark(FIELD_INDEX_RECORDTYPE);
            this.recordType = recordType;
        }
        return this;
    }

    /**
     * inner set recordType
     *
     * @param recordType new value
     */
    private void innerSetRecordType(int recordType) {
        this.recordType = recordType;
    }

    /**
     * get value
     *
     * @return value value
     */
    public long getValue() {
        return this.value;
    }

    /**
     * set value && set marked
     *
     * @param value new value
     * @return current object
     */
    public DataRecordUnitProp setValue(long value) {
        if (this.value != value) {
            this.mark(FIELD_INDEX_VALUE);
            this.value = value;
        }
        return this;
    }

    /**
     * inner set value
     *
     * @param value new value
     */
    private void innerSetValue(long value) {
        this.value = value;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DataRecordUnitPB.Builder getCopyCsBuilder() {
        final DataRecordUnitPB.Builder builder = DataRecordUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DataRecordUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordType() != 0) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }  else if (builder.hasRecordType()) {
            // 清理RecordType
            builder.clearRecordType();
            fieldCnt++;
        }
        if (this.getValue() != 0L) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }  else if (builder.hasValue()) {
            // 清理Value
            builder.clearValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DataRecordUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DataRecordUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DataRecordUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordType()) {
            this.innerSetRecordType(proto.getRecordType());
        } else {
            this.innerSetRecordType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasValue()) {
            this.innerSetValue(proto.getValue());
        } else {
            this.innerSetValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DataRecordUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DataRecordUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordType()) {
            this.setRecordType(proto.getRecordType());
            fieldCnt++;
        }
        if (proto.hasValue()) {
            this.setValue(proto.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DataRecordUnit.Builder getCopyDbBuilder() {
        final DataRecordUnit.Builder builder = DataRecordUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DataRecordUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordType() != 0) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }  else if (builder.hasRecordType()) {
            // 清理RecordType
            builder.clearRecordType();
            fieldCnt++;
        }
        if (this.getValue() != 0L) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }  else if (builder.hasValue()) {
            // 清理Value
            builder.clearValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DataRecordUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DataRecordUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordType()) {
            this.innerSetRecordType(proto.getRecordType());
        } else {
            this.innerSetRecordType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasValue()) {
            this.innerSetValue(proto.getValue());
        } else {
            this.innerSetValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DataRecordUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DataRecordUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordType()) {
            this.setRecordType(proto.getRecordType());
            fieldCnt++;
        }
        if (proto.hasValue()) {
            this.setValue(proto.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DataRecordUnit.Builder getCopySsBuilder() {
        final DataRecordUnit.Builder builder = DataRecordUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DataRecordUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRecordType() != 0) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }  else if (builder.hasRecordType()) {
            // 清理RecordType
            builder.clearRecordType();
            fieldCnt++;
        }
        if (this.getValue() != 0L) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }  else if (builder.hasValue()) {
            // 清理Value
            builder.clearValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DataRecordUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDTYPE)) {
            builder.setRecordType(this.getRecordType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DataRecordUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecordType()) {
            this.innerSetRecordType(proto.getRecordType());
        } else {
            this.innerSetRecordType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasValue()) {
            this.innerSetValue(proto.getValue());
        } else {
            this.innerSetValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return DataRecordUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DataRecordUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecordType()) {
            this.setRecordType(proto.getRecordType());
            fieldCnt++;
        }
        if (proto.hasValue()) {
            this.setValue(proto.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DataRecordUnit.Builder builder = DataRecordUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.recordType;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DataRecordUnitProp)) {
            return false;
        }
        final DataRecordUnitProp otherNode = (DataRecordUnitProp) node;
        if (this.recordType != otherNode.recordType) {
            return false;
        }
        if (this.value != otherNode.value) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
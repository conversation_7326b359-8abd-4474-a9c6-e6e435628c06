package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.VipSelectedHeroInfo;
import com.yorha.proto.PlayerPB.VipSelectedHeroInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class VipSelectedHeroInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_UID = 0;
    public static final int FIELD_INDEX_HEROID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int uid = Constant.DEFAULT_INT_VALUE;
    private int heroId = Constant.DEFAULT_INT_VALUE;

    public VipSelectedHeroInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public VipSelectedHeroInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get uid
     *
     * @return uid value
     */
    public int getUid() {
        return this.uid;
    }

    /**
     * set uid && set marked
     *
     * @param uid new value
     * @return current object
     */
    public VipSelectedHeroInfoProp setUid(int uid) {
        if (this.uid != uid) {
            this.mark(FIELD_INDEX_UID);
            this.uid = uid;
        }
        return this;
    }

    /**
     * inner set uid
     *
     * @param uid new value
     */
    private void innerSetUid(int uid) {
        this.uid = uid;
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public VipSelectedHeroInfoProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public VipSelectedHeroInfoPB.Builder getCopyCsBuilder() {
        final VipSelectedHeroInfoPB.Builder builder = VipSelectedHeroInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(VipSelectedHeroInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUid() != 0) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }  else if (builder.hasUid()) {
            // 清理Uid
            builder.clearUid();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(VipSelectedHeroInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(VipSelectedHeroInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(VipSelectedHeroInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUid()) {
            this.innerSetUid(proto.getUid());
        } else {
            this.innerSetUid(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return VipSelectedHeroInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(VipSelectedHeroInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUid()) {
            this.setUid(proto.getUid());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public VipSelectedHeroInfo.Builder getCopyDbBuilder() {
        final VipSelectedHeroInfo.Builder builder = VipSelectedHeroInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(VipSelectedHeroInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUid() != 0) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }  else if (builder.hasUid()) {
            // 清理Uid
            builder.clearUid();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(VipSelectedHeroInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(VipSelectedHeroInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUid()) {
            this.innerSetUid(proto.getUid());
        } else {
            this.innerSetUid(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return VipSelectedHeroInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(VipSelectedHeroInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUid()) {
            this.setUid(proto.getUid());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public VipSelectedHeroInfo.Builder getCopySsBuilder() {
        final VipSelectedHeroInfo.Builder builder = VipSelectedHeroInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(VipSelectedHeroInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUid() != 0) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }  else if (builder.hasUid()) {
            // 清理Uid
            builder.clearUid();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(VipSelectedHeroInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UID)) {
            builder.setUid(this.getUid());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(VipSelectedHeroInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUid()) {
            this.innerSetUid(proto.getUid());
        } else {
            this.innerSetUid(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return VipSelectedHeroInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(VipSelectedHeroInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUid()) {
            this.setUid(proto.getUid());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        VipSelectedHeroInfo.Builder builder = VipSelectedHeroInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.uid;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof VipSelectedHeroInfoProp)) {
            return false;
        }
        final VipSelectedHeroInfoProp otherNode = (VipSelectedHeroInfoProp) node;
        if (this.uid != otherNode.uid) {
            return false;
        }
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
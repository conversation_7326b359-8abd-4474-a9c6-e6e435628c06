package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailClanTechResearchFinishData;
import com.yorha.proto.StructMailPB.MailClanTechResearchFinishDataPB;


/**
 * <AUTHOR> auto gen
 */
public class MailClanTechResearchFinishDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TECHSUBID = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private int techSubId = Constant.DEFAULT_INT_VALUE;

    public MailClanTechResearchFinishDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailClanTechResearchFinishDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get techSubId
     *
     * @return techSubId value
     */
    public int getTechSubId() {
        return this.techSubId;
    }

    /**
     * set techSubId && set marked
     *
     * @param techSubId new value
     * @return current object
     */
    public MailClanTechResearchFinishDataProp setTechSubId(int techSubId) {
        if (this.techSubId != techSubId) {
            this.mark(FIELD_INDEX_TECHSUBID);
            this.techSubId = techSubId;
        }
        return this;
    }

    /**
     * inner set techSubId
     *
     * @param techSubId new value
     */
    private void innerSetTechSubId(int techSubId) {
        this.techSubId = techSubId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanTechResearchFinishDataPB.Builder getCopyCsBuilder() {
        final MailClanTechResearchFinishDataPB.Builder builder = MailClanTechResearchFinishDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailClanTechResearchFinishDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailClanTechResearchFinishDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailClanTechResearchFinishDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailClanTechResearchFinishDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MailClanTechResearchFinishDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailClanTechResearchFinishDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanTechResearchFinishData.Builder getCopyDbBuilder() {
        final MailClanTechResearchFinishData.Builder builder = MailClanTechResearchFinishData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailClanTechResearchFinishData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailClanTechResearchFinishData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailClanTechResearchFinishData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MailClanTechResearchFinishDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailClanTechResearchFinishData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanTechResearchFinishData.Builder getCopySsBuilder() {
        final MailClanTechResearchFinishData.Builder builder = MailClanTechResearchFinishData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailClanTechResearchFinishData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTechSubId() != 0) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }  else if (builder.hasTechSubId()) {
            // 清理TechSubId
            builder.clearTechSubId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailClanTechResearchFinishData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TECHSUBID)) {
            builder.setTechSubId(this.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailClanTechResearchFinishData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTechSubId()) {
            this.innerSetTechSubId(proto.getTechSubId());
        } else {
            this.innerSetTechSubId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return MailClanTechResearchFinishDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailClanTechResearchFinishData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTechSubId()) {
            this.setTechSubId(proto.getTechSubId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailClanTechResearchFinishData.Builder builder = MailClanTechResearchFinishData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailClanTechResearchFinishDataProp)) {
            return false;
        }
        final MailClanTechResearchFinishDataProp otherNode = (MailClanTechResearchFinishDataProp) node;
        if (this.techSubId != otherNode.techSubId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
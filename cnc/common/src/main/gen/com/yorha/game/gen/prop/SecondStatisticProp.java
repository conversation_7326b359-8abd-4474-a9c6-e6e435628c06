package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.SecondStatistic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.SecondStatisticPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SecondStatisticProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ENUMID = 0;
    public static final int FIELD_INDEX_SINGLESTATISTICMAP = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int enumId = Constant.DEFAULT_INT_VALUE;
    private Int32SingleStatisticMapProp singleStatisticMap = null;

    public SecondStatisticProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SecondStatisticProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get enumId
     *
     * @return enumId value
     */
    public int getEnumId() {
        return this.enumId;
    }

    /**
     * set enumId && set marked
     *
     * @param enumId new value
     * @return current object
     */
    public SecondStatisticProp setEnumId(int enumId) {
        if (this.enumId != enumId) {
            this.mark(FIELD_INDEX_ENUMID);
            this.enumId = enumId;
        }
        return this;
    }

    /**
     * inner set enumId
     *
     * @param enumId new value
     */
    private void innerSetEnumId(int enumId) {
        this.enumId = enumId;
    }

    /**
     * get singleStatisticMap
     *
     * @return singleStatisticMap value
     */
    public Int32SingleStatisticMapProp getSingleStatisticMap() {
        if (this.singleStatisticMap == null) {
            this.singleStatisticMap = new Int32SingleStatisticMapProp(this, FIELD_INDEX_SINGLESTATISTICMAP);
        }
        return this.singleStatisticMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSingleStatisticMapV(SingleStatisticProp v) {
        this.getSingleStatisticMap().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SingleStatisticProp addEmptySingleStatisticMap(Integer k) {
        return this.getSingleStatisticMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSingleStatisticMapSize() {
        if (this.singleStatisticMap == null) {
            return 0;
        }
        return this.singleStatisticMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSingleStatisticMapEmpty() {
        if (this.singleStatisticMap == null) {
            return true;
        }
        return this.singleStatisticMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SingleStatisticProp getSingleStatisticMapV(Integer k) {
        if (this.singleStatisticMap == null || !this.singleStatisticMap.containsKey(k)) {
            return null;
        }
        return this.singleStatisticMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSingleStatisticMap() {
        if (this.singleStatisticMap != null) {
            this.singleStatisticMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSingleStatisticMapV(Integer k) {
        if (this.singleStatisticMap != null) {
            this.singleStatisticMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SecondStatisticPB.Builder getCopyCsBuilder() {
        final SecondStatisticPB.Builder builder = SecondStatisticPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SecondStatisticPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEnumId() != 0) {
            builder.setEnumId(this.getEnumId());
            fieldCnt++;
        }  else if (builder.hasEnumId()) {
            // 清理EnumId
            builder.clearEnumId();
            fieldCnt++;
        }
        if (this.singleStatisticMap != null) {
            StructPB.Int32SingleStatisticMapPB.Builder tmpBuilder = StructPB.Int32SingleStatisticMapPB.newBuilder();
            final int tmpFieldCnt = this.singleStatisticMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleStatisticMap();
            }
        }  else if (builder.hasSingleStatisticMap()) {
            // 清理SingleStatisticMap
            builder.clearSingleStatisticMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SecondStatisticPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENUMID)) {
            builder.setEnumId(this.getEnumId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToCs(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SecondStatisticPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENUMID)) {
            builder.setEnumId(this.getEnumId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToAndClearDeleteKeysCs(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SecondStatisticPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEnumId()) {
            this.innerSetEnumId(proto.getEnumId());
        } else {
            this.innerSetEnumId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeFromCs(proto.getSingleStatisticMap());
        } else {
            if (this.singleStatisticMap != null) {
                this.singleStatisticMap.mergeFromCs(proto.getSingleStatisticMap());
            }
        }
        this.markAll();
        return SecondStatisticProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SecondStatisticPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEnumId()) {
            this.setEnumId(proto.getEnumId());
            fieldCnt++;
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeChangeFromCs(proto.getSingleStatisticMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SecondStatistic.Builder getCopyDbBuilder() {
        final SecondStatistic.Builder builder = SecondStatistic.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SecondStatistic.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEnumId() != 0) {
            builder.setEnumId(this.getEnumId());
            fieldCnt++;
        }  else if (builder.hasEnumId()) {
            // 清理EnumId
            builder.clearEnumId();
            fieldCnt++;
        }
        if (this.singleStatisticMap != null) {
            Struct.Int32SingleStatisticMap.Builder tmpBuilder = Struct.Int32SingleStatisticMap.newBuilder();
            final int tmpFieldCnt = this.singleStatisticMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleStatisticMap();
            }
        }  else if (builder.hasSingleStatisticMap()) {
            // 清理SingleStatisticMap
            builder.clearSingleStatisticMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SecondStatistic.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENUMID)) {
            builder.setEnumId(this.getEnumId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToDb(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SecondStatistic proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEnumId()) {
            this.innerSetEnumId(proto.getEnumId());
        } else {
            this.innerSetEnumId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeFromDb(proto.getSingleStatisticMap());
        } else {
            if (this.singleStatisticMap != null) {
                this.singleStatisticMap.mergeFromDb(proto.getSingleStatisticMap());
            }
        }
        this.markAll();
        return SecondStatisticProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SecondStatistic proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEnumId()) {
            this.setEnumId(proto.getEnumId());
            fieldCnt++;
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeChangeFromDb(proto.getSingleStatisticMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SecondStatistic.Builder getCopySsBuilder() {
        final SecondStatistic.Builder builder = SecondStatistic.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SecondStatistic.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getEnumId() != 0) {
            builder.setEnumId(this.getEnumId());
            fieldCnt++;
        }  else if (builder.hasEnumId()) {
            // 清理EnumId
            builder.clearEnumId();
            fieldCnt++;
        }
        if (this.singleStatisticMap != null) {
            Struct.Int32SingleStatisticMap.Builder tmpBuilder = Struct.Int32SingleStatisticMap.newBuilder();
            final int tmpFieldCnt = this.singleStatisticMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleStatisticMap();
            }
        }  else if (builder.hasSingleStatisticMap()) {
            // 清理SingleStatisticMap
            builder.clearSingleStatisticMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SecondStatistic.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ENUMID)) {
            builder.setEnumId(this.getEnumId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToSs(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SecondStatistic proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasEnumId()) {
            this.innerSetEnumId(proto.getEnumId());
        } else {
            this.innerSetEnumId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeFromSs(proto.getSingleStatisticMap());
        } else {
            if (this.singleStatisticMap != null) {
                this.singleStatisticMap.mergeFromSs(proto.getSingleStatisticMap());
            }
        }
        this.markAll();
        return SecondStatisticProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SecondStatistic proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasEnumId()) {
            this.setEnumId(proto.getEnumId());
            fieldCnt++;
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeChangeFromSs(proto.getSingleStatisticMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SecondStatistic.Builder builder = SecondStatistic.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            this.singleStatisticMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.singleStatisticMap != null) {
            this.singleStatisticMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.enumId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SecondStatisticProp)) {
            return false;
        }
        final SecondStatisticProp otherNode = (SecondStatisticProp) node;
        if (this.enumId != otherNode.enumId) {
            return false;
        }
        if (!this.getSingleStatisticMap().compareDataTo(otherNode.getSingleStatisticMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
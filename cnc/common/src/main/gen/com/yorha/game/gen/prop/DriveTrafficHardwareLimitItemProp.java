package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.DriveTrafficHardwareLimitItem;
import com.yorha.proto.ZonePB.DriveTrafficHardwareLimitItemPB;


/**
 * <AUTHOR> auto gen
 */
public class DriveTrafficHardwareLimitItemProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_DRIVETRAFFICHARDWARELEVEL = 0;
    public static final int FIELD_INDEX_COUNT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int driveTrafficHardwareLevel = Constant.DEFAULT_INT_VALUE;
    private int count = Constant.DEFAULT_INT_VALUE;

    public DriveTrafficHardwareLimitItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DriveTrafficHardwareLimitItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get driveTrafficHardwareLevel
     *
     * @return driveTrafficHardwareLevel value
     */
    public int getDriveTrafficHardwareLevel() {
        return this.driveTrafficHardwareLevel;
    }

    /**
     * set driveTrafficHardwareLevel && set marked
     *
     * @param driveTrafficHardwareLevel new value
     * @return current object
     */
    public DriveTrafficHardwareLimitItemProp setDriveTrafficHardwareLevel(int driveTrafficHardwareLevel) {
        if (this.driveTrafficHardwareLevel != driveTrafficHardwareLevel) {
            this.mark(FIELD_INDEX_DRIVETRAFFICHARDWARELEVEL);
            this.driveTrafficHardwareLevel = driveTrafficHardwareLevel;
        }
        return this;
    }

    /**
     * inner set driveTrafficHardwareLevel
     *
     * @param driveTrafficHardwareLevel new value
     */
    private void innerSetDriveTrafficHardwareLevel(int driveTrafficHardwareLevel) {
        this.driveTrafficHardwareLevel = driveTrafficHardwareLevel;
    }

    /**
     * get count
     *
     * @return count value
     */
    public int getCount() {
        return this.count;
    }

    /**
     * set count && set marked
     *
     * @param count new value
     * @return current object
     */
    public DriveTrafficHardwareLimitItemProp setCount(int count) {
        if (this.count != count) {
            this.mark(FIELD_INDEX_COUNT);
            this.count = count;
        }
        return this;
    }

    /**
     * inner set count
     *
     * @param count new value
     */
    private void innerSetCount(int count) {
        this.count = count;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DriveTrafficHardwareLimitItemPB.Builder getCopyCsBuilder() {
        final DriveTrafficHardwareLimitItemPB.Builder builder = DriveTrafficHardwareLimitItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DriveTrafficHardwareLimitItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDriveTrafficHardwareLevel() != 0) {
            builder.setDriveTrafficHardwareLevel(this.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }  else if (builder.hasDriveTrafficHardwareLevel()) {
            // 清理DriveTrafficHardwareLevel
            builder.clearDriveTrafficHardwareLevel();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DriveTrafficHardwareLimitItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELEVEL)) {
            builder.setDriveTrafficHardwareLevel(this.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DriveTrafficHardwareLimitItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELEVEL)) {
            builder.setDriveTrafficHardwareLevel(this.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DriveTrafficHardwareLimitItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDriveTrafficHardwareLevel()) {
            this.innerSetDriveTrafficHardwareLevel(proto.getDriveTrafficHardwareLevel());
        } else {
            this.innerSetDriveTrafficHardwareLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return DriveTrafficHardwareLimitItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DriveTrafficHardwareLimitItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDriveTrafficHardwareLevel()) {
            this.setDriveTrafficHardwareLevel(proto.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DriveTrafficHardwareLimitItem.Builder getCopyDbBuilder() {
        final DriveTrafficHardwareLimitItem.Builder builder = DriveTrafficHardwareLimitItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DriveTrafficHardwareLimitItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDriveTrafficHardwareLevel() != 0) {
            builder.setDriveTrafficHardwareLevel(this.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }  else if (builder.hasDriveTrafficHardwareLevel()) {
            // 清理DriveTrafficHardwareLevel
            builder.clearDriveTrafficHardwareLevel();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DriveTrafficHardwareLimitItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELEVEL)) {
            builder.setDriveTrafficHardwareLevel(this.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DriveTrafficHardwareLimitItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDriveTrafficHardwareLevel()) {
            this.innerSetDriveTrafficHardwareLevel(proto.getDriveTrafficHardwareLevel());
        } else {
            this.innerSetDriveTrafficHardwareLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return DriveTrafficHardwareLimitItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DriveTrafficHardwareLimitItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDriveTrafficHardwareLevel()) {
            this.setDriveTrafficHardwareLevel(proto.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DriveTrafficHardwareLimitItem.Builder getCopySsBuilder() {
        final DriveTrafficHardwareLimitItem.Builder builder = DriveTrafficHardwareLimitItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DriveTrafficHardwareLimitItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDriveTrafficHardwareLevel() != 0) {
            builder.setDriveTrafficHardwareLevel(this.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }  else if (builder.hasDriveTrafficHardwareLevel()) {
            // 清理DriveTrafficHardwareLevel
            builder.clearDriveTrafficHardwareLevel();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DriveTrafficHardwareLimitItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRIVETRAFFICHARDWARELEVEL)) {
            builder.setDriveTrafficHardwareLevel(this.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DriveTrafficHardwareLimitItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDriveTrafficHardwareLevel()) {
            this.innerSetDriveTrafficHardwareLevel(proto.getDriveTrafficHardwareLevel());
        } else {
            this.innerSetDriveTrafficHardwareLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return DriveTrafficHardwareLimitItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DriveTrafficHardwareLimitItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDriveTrafficHardwareLevel()) {
            this.setDriveTrafficHardwareLevel(proto.getDriveTrafficHardwareLevel());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DriveTrafficHardwareLimitItem.Builder builder = DriveTrafficHardwareLimitItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.driveTrafficHardwareLevel;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DriveTrafficHardwareLimitItemProp)) {
            return false;
        }
        final DriveTrafficHardwareLimitItemProp otherNode = (DriveTrafficHardwareLimitItemProp) node;
        if (this.driveTrafficHardwareLevel != otherNode.driveTrafficHardwareLevel) {
            return false;
        }
        if (this.count != otherNode.count) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
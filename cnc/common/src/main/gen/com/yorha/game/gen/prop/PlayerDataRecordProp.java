package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerDataRecord;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.PlayerDataRecordPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerDataRecordProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RECORDS = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32DataRecordUnitMapProp records = null;

    public PlayerDataRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerDataRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get records
     *
     * @return records value
     */
    public Int32DataRecordUnitMapProp getRecords() {
        if (this.records == null) {
            this.records = new Int32DataRecordUnitMapProp(this, FIELD_INDEX_RECORDS);
        }
        return this.records;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRecordsV(DataRecordUnitProp v) {
        this.getRecords().put(v.getRecordType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public DataRecordUnitProp addEmptyRecords(Integer k) {
        return this.getRecords().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRecordsSize() {
        if (this.records == null) {
            return 0;
        }
        return this.records.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRecordsEmpty() {
        if (this.records == null) {
            return true;
        }
        return this.records.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public DataRecordUnitProp getRecordsV(Integer k) {
        if (this.records == null || !this.records.containsKey(k)) {
            return null;
        }
        return this.records.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRecords() {
        if (this.records != null) {
            this.records.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRecordsV(Integer k) {
        if (this.records != null) {
            this.records.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDataRecordPB.Builder getCopyCsBuilder() {
        final PlayerDataRecordPB.Builder builder = PlayerDataRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerDataRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.records != null) {
            StructPB.Int32DataRecordUnitMapPB.Builder tmpBuilder = StructPB.Int32DataRecordUnitMapPB.newBuilder();
            final int tmpFieldCnt = this.records.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecords();
            }
        }  else if (builder.hasRecords()) {
            // 清理Records
            builder.clearRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerDataRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDS) && this.records != null) {
            final boolean needClear = !builder.hasRecords();
            final int tmpFieldCnt = this.records.copyChangeToCs(builder.getRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerDataRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDS) && this.records != null) {
            final boolean needClear = !builder.hasRecords();
            final int tmpFieldCnt = this.records.copyChangeToAndClearDeleteKeysCs(builder.getRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerDataRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecords()) {
            this.getRecords().mergeFromCs(proto.getRecords());
        } else {
            if (this.records != null) {
                this.records.mergeFromCs(proto.getRecords());
            }
        }
        this.markAll();
        return PlayerDataRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerDataRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecords()) {
            this.getRecords().mergeChangeFromCs(proto.getRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDataRecord.Builder getCopyDbBuilder() {
        final PlayerDataRecord.Builder builder = PlayerDataRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerDataRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.records != null) {
            Struct.Int32DataRecordUnitMap.Builder tmpBuilder = Struct.Int32DataRecordUnitMap.newBuilder();
            final int tmpFieldCnt = this.records.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecords();
            }
        }  else if (builder.hasRecords()) {
            // 清理Records
            builder.clearRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerDataRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDS) && this.records != null) {
            final boolean needClear = !builder.hasRecords();
            final int tmpFieldCnt = this.records.copyChangeToDb(builder.getRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerDataRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecords()) {
            this.getRecords().mergeFromDb(proto.getRecords());
        } else {
            if (this.records != null) {
                this.records.mergeFromDb(proto.getRecords());
            }
        }
        this.markAll();
        return PlayerDataRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerDataRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecords()) {
            this.getRecords().mergeChangeFromDb(proto.getRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDataRecord.Builder getCopySsBuilder() {
        final PlayerDataRecord.Builder builder = PlayerDataRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerDataRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.records != null) {
            Struct.Int32DataRecordUnitMap.Builder tmpBuilder = Struct.Int32DataRecordUnitMap.newBuilder();
            final int tmpFieldCnt = this.records.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecords();
            }
        }  else if (builder.hasRecords()) {
            // 清理Records
            builder.clearRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerDataRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORDS) && this.records != null) {
            final boolean needClear = !builder.hasRecords();
            final int tmpFieldCnt = this.records.copyChangeToSs(builder.getRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerDataRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecords()) {
            this.getRecords().mergeFromSs(proto.getRecords());
        } else {
            if (this.records != null) {
                this.records.mergeFromSs(proto.getRecords());
            }
        }
        this.markAll();
        return PlayerDataRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerDataRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecords()) {
            this.getRecords().mergeChangeFromSs(proto.getRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerDataRecord.Builder builder = PlayerDataRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RECORDS) && this.records != null) {
            this.records.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.records != null) {
            this.records.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerDataRecordProp)) {
            return false;
        }
        final PlayerDataRecordProp otherNode = (PlayerDataRecordProp) node;
        if (!this.getRecords().compareDataTo(otherNode.getRecords())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
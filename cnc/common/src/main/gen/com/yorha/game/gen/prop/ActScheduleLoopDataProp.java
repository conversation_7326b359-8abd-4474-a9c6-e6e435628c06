package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActScheduleLoopData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActScheduleLoopDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActScheduleLoopDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURLOOPTIMES = 0;
    public static final int FIELD_INDEX_LASTACT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int curLoopTimes = Constant.DEFAULT_INT_VALUE;
    private ActTimingCellProp lastAct = null;

    public ActScheduleLoopDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActScheduleLoopDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curLoopTimes
     *
     * @return curLoopTimes value
     */
    public int getCurLoopTimes() {
        return this.curLoopTimes;
    }

    /**
     * set curLoopTimes && set marked
     *
     * @param curLoopTimes new value
     * @return current object
     */
    public ActScheduleLoopDataProp setCurLoopTimes(int curLoopTimes) {
        if (this.curLoopTimes != curLoopTimes) {
            this.mark(FIELD_INDEX_CURLOOPTIMES);
            this.curLoopTimes = curLoopTimes;
        }
        return this;
    }

    /**
     * inner set curLoopTimes
     *
     * @param curLoopTimes new value
     */
    private void innerSetCurLoopTimes(int curLoopTimes) {
        this.curLoopTimes = curLoopTimes;
    }

    /**
     * get lastAct
     *
     * @return lastAct value
     */
    public ActTimingCellProp getLastAct() {
        if (this.lastAct == null) {
            this.lastAct = new ActTimingCellProp(this, FIELD_INDEX_LASTACT);
        }
        return this.lastAct;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActScheduleLoopDataPB.Builder getCopyCsBuilder() {
        final ActScheduleLoopDataPB.Builder builder = ActScheduleLoopDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActScheduleLoopDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurLoopTimes() != 0) {
            builder.setCurLoopTimes(this.getCurLoopTimes());
            fieldCnt++;
        }  else if (builder.hasCurLoopTimes()) {
            // 清理CurLoopTimes
            builder.clearCurLoopTimes();
            fieldCnt++;
        }
        if (this.lastAct != null) {
            StructPB.ActTimingCellPB.Builder tmpBuilder = StructPB.ActTimingCellPB.newBuilder();
            final int tmpFieldCnt = this.lastAct.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLastAct(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLastAct();
            }
        }  else if (builder.hasLastAct()) {
            // 清理LastAct
            builder.clearLastAct();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActScheduleLoopDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLOOPTIMES)) {
            builder.setCurLoopTimes(this.getCurLoopTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTACT) && this.lastAct != null) {
            final boolean needClear = !builder.hasLastAct();
            final int tmpFieldCnt = this.lastAct.copyChangeToCs(builder.getLastActBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLastAct();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActScheduleLoopDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLOOPTIMES)) {
            builder.setCurLoopTimes(this.getCurLoopTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTACT) && this.lastAct != null) {
            final boolean needClear = !builder.hasLastAct();
            final int tmpFieldCnt = this.lastAct.copyChangeToAndClearDeleteKeysCs(builder.getLastActBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLastAct();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActScheduleLoopDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurLoopTimes()) {
            this.innerSetCurLoopTimes(proto.getCurLoopTimes());
        } else {
            this.innerSetCurLoopTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastAct()) {
            this.getLastAct().mergeFromCs(proto.getLastAct());
        } else {
            if (this.lastAct != null) {
                this.lastAct.mergeFromCs(proto.getLastAct());
            }
        }
        this.markAll();
        return ActScheduleLoopDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActScheduleLoopDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurLoopTimes()) {
            this.setCurLoopTimes(proto.getCurLoopTimes());
            fieldCnt++;
        }
        if (proto.hasLastAct()) {
            this.getLastAct().mergeChangeFromCs(proto.getLastAct());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActScheduleLoopData.Builder getCopyDbBuilder() {
        final ActScheduleLoopData.Builder builder = ActScheduleLoopData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActScheduleLoopData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurLoopTimes() != 0) {
            builder.setCurLoopTimes(this.getCurLoopTimes());
            fieldCnt++;
        }  else if (builder.hasCurLoopTimes()) {
            // 清理CurLoopTimes
            builder.clearCurLoopTimes();
            fieldCnt++;
        }
        if (this.lastAct != null) {
            Struct.ActTimingCell.Builder tmpBuilder = Struct.ActTimingCell.newBuilder();
            final int tmpFieldCnt = this.lastAct.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLastAct(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLastAct();
            }
        }  else if (builder.hasLastAct()) {
            // 清理LastAct
            builder.clearLastAct();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActScheduleLoopData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLOOPTIMES)) {
            builder.setCurLoopTimes(this.getCurLoopTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTACT) && this.lastAct != null) {
            final boolean needClear = !builder.hasLastAct();
            final int tmpFieldCnt = this.lastAct.copyChangeToDb(builder.getLastActBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLastAct();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActScheduleLoopData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurLoopTimes()) {
            this.innerSetCurLoopTimes(proto.getCurLoopTimes());
        } else {
            this.innerSetCurLoopTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastAct()) {
            this.getLastAct().mergeFromDb(proto.getLastAct());
        } else {
            if (this.lastAct != null) {
                this.lastAct.mergeFromDb(proto.getLastAct());
            }
        }
        this.markAll();
        return ActScheduleLoopDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActScheduleLoopData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurLoopTimes()) {
            this.setCurLoopTimes(proto.getCurLoopTimes());
            fieldCnt++;
        }
        if (proto.hasLastAct()) {
            this.getLastAct().mergeChangeFromDb(proto.getLastAct());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActScheduleLoopData.Builder getCopySsBuilder() {
        final ActScheduleLoopData.Builder builder = ActScheduleLoopData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActScheduleLoopData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurLoopTimes() != 0) {
            builder.setCurLoopTimes(this.getCurLoopTimes());
            fieldCnt++;
        }  else if (builder.hasCurLoopTimes()) {
            // 清理CurLoopTimes
            builder.clearCurLoopTimes();
            fieldCnt++;
        }
        if (this.lastAct != null) {
            Struct.ActTimingCell.Builder tmpBuilder = Struct.ActTimingCell.newBuilder();
            final int tmpFieldCnt = this.lastAct.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLastAct(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLastAct();
            }
        }  else if (builder.hasLastAct()) {
            // 清理LastAct
            builder.clearLastAct();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActScheduleLoopData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURLOOPTIMES)) {
            builder.setCurLoopTimes(this.getCurLoopTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTACT) && this.lastAct != null) {
            final boolean needClear = !builder.hasLastAct();
            final int tmpFieldCnt = this.lastAct.copyChangeToSs(builder.getLastActBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLastAct();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActScheduleLoopData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurLoopTimes()) {
            this.innerSetCurLoopTimes(proto.getCurLoopTimes());
        } else {
            this.innerSetCurLoopTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastAct()) {
            this.getLastAct().mergeFromSs(proto.getLastAct());
        } else {
            if (this.lastAct != null) {
                this.lastAct.mergeFromSs(proto.getLastAct());
            }
        }
        this.markAll();
        return ActScheduleLoopDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActScheduleLoopData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurLoopTimes()) {
            this.setCurLoopTimes(proto.getCurLoopTimes());
            fieldCnt++;
        }
        if (proto.hasLastAct()) {
            this.getLastAct().mergeChangeFromSs(proto.getLastAct());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActScheduleLoopData.Builder builder = ActScheduleLoopData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LASTACT) && this.lastAct != null) {
            this.lastAct.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.lastAct != null) {
            this.lastAct.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActScheduleLoopDataProp)) {
            return false;
        }
        final ActScheduleLoopDataProp otherNode = (ActScheduleLoopDataProp) node;
        if (this.curLoopTimes != otherNode.curLoopTimes) {
            return false;
        }
        if (!this.getLastAct().compareDataTo(otherNode.getLastAct())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
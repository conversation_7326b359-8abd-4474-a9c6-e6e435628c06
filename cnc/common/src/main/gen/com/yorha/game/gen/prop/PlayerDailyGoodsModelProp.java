package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerDailyGoodsModel;
import com.yorha.proto.Basic;
import com.yorha.proto.PlayerPB.PlayerDailyGoodsModelPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerDailyGoodsModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BOUGHTGOODLEVEL = 0;
    public static final int FIELD_INDEX_REWARDTAKEN = 1;
    public static final int FIELD_INDEX_HEROID = 2;
    public static final int FIELD_INDEX_BOUGHTSUPERGOODLEVEL = 3;
    public static final int FIELD_INDEX_SUPERREWARDTAKEN = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private Int32SetProp boughtGoodLevel = null;
    private boolean rewardTaken = Constant.DEFAULT_BOOLEAN_VALUE;
    private int heroId = Constant.DEFAULT_INT_VALUE;
    private Int32SetProp boughtSuperGoodLevel = null;
    private boolean superRewardTaken = Constant.DEFAULT_BOOLEAN_VALUE;

    public PlayerDailyGoodsModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerDailyGoodsModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get boughtGoodLevel
     *
     * @return boughtGoodLevel value
     */
    public Int32SetProp getBoughtGoodLevel() {
        if (this.boughtGoodLevel == null) {
            this.boughtGoodLevel = new Int32SetProp(this, FIELD_INDEX_BOUGHTGOODLEVEL);
        }
        return this.boughtGoodLevel;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addBoughtGoodLevel(Integer e) {
        this.getBoughtGoodLevel().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeBoughtGoodLevel(Integer e) {
        if (this.boughtGoodLevel == null) {
            return null;
        }
        if(this.boughtGoodLevel.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getBoughtGoodLevelSize() {
        if (this.boughtGoodLevel == null) {
            return 0;
        }
        return this.boughtGoodLevel.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isBoughtGoodLevelEmpty() {
        if (this.boughtGoodLevel == null) {
            return true;
        }
        return this.getBoughtGoodLevel().isEmpty();
    }

    /**
     * clear set
     */
    public void clearBoughtGoodLevel() {
        this.getBoughtGoodLevel().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isBoughtGoodLevelContains(Integer e) {
        return this.boughtGoodLevel != null && this.boughtGoodLevel.contains(e);
    }

    /**
     * get rewardTaken
     *
     * @return rewardTaken value
     */
    public boolean getRewardTaken() {
        return this.rewardTaken;
    }

    /**
     * set rewardTaken && set marked
     *
     * @param rewardTaken new value
     * @return current object
     */
    public PlayerDailyGoodsModelProp setRewardTaken(boolean rewardTaken) {
        if (this.rewardTaken != rewardTaken) {
            this.mark(FIELD_INDEX_REWARDTAKEN);
            this.rewardTaken = rewardTaken;
        }
        return this;
    }

    /**
     * inner set rewardTaken
     *
     * @param rewardTaken new value
     */
    private void innerSetRewardTaken(boolean rewardTaken) {
        this.rewardTaken = rewardTaken;
    }

    /**
     * get heroId
     *
     * @return heroId value
     */
    public int getHeroId() {
        return this.heroId;
    }

    /**
     * set heroId && set marked
     *
     * @param heroId new value
     * @return current object
     */
    public PlayerDailyGoodsModelProp setHeroId(int heroId) {
        if (this.heroId != heroId) {
            this.mark(FIELD_INDEX_HEROID);
            this.heroId = heroId;
        }
        return this;
    }

    /**
     * inner set heroId
     *
     * @param heroId new value
     */
    private void innerSetHeroId(int heroId) {
        this.heroId = heroId;
    }

    /**
     * get boughtSuperGoodLevel
     *
     * @return boughtSuperGoodLevel value
     */
    public Int32SetProp getBoughtSuperGoodLevel() {
        if (this.boughtSuperGoodLevel == null) {
            this.boughtSuperGoodLevel = new Int32SetProp(this, FIELD_INDEX_BOUGHTSUPERGOODLEVEL);
        }
        return this.boughtSuperGoodLevel;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addBoughtSuperGoodLevel(Integer e) {
        this.getBoughtSuperGoodLevel().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeBoughtSuperGoodLevel(Integer e) {
        if (this.boughtSuperGoodLevel == null) {
            return null;
        }
        if(this.boughtSuperGoodLevel.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getBoughtSuperGoodLevelSize() {
        if (this.boughtSuperGoodLevel == null) {
            return 0;
        }
        return this.boughtSuperGoodLevel.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isBoughtSuperGoodLevelEmpty() {
        if (this.boughtSuperGoodLevel == null) {
            return true;
        }
        return this.getBoughtSuperGoodLevel().isEmpty();
    }

    /**
     * clear set
     */
    public void clearBoughtSuperGoodLevel() {
        this.getBoughtSuperGoodLevel().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isBoughtSuperGoodLevelContains(Integer e) {
        return this.boughtSuperGoodLevel != null && this.boughtSuperGoodLevel.contains(e);
    }

    /**
     * get superRewardTaken
     *
     * @return superRewardTaken value
     */
    public boolean getSuperRewardTaken() {
        return this.superRewardTaken;
    }

    /**
     * set superRewardTaken && set marked
     *
     * @param superRewardTaken new value
     * @return current object
     */
    public PlayerDailyGoodsModelProp setSuperRewardTaken(boolean superRewardTaken) {
        if (this.superRewardTaken != superRewardTaken) {
            this.mark(FIELD_INDEX_SUPERREWARDTAKEN);
            this.superRewardTaken = superRewardTaken;
        }
        return this;
    }

    /**
     * inner set superRewardTaken
     *
     * @param superRewardTaken new value
     */
    private void innerSetSuperRewardTaken(boolean superRewardTaken) {
        this.superRewardTaken = superRewardTaken;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDailyGoodsModelPB.Builder getCopyCsBuilder() {
        final PlayerDailyGoodsModelPB.Builder builder = PlayerDailyGoodsModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerDailyGoodsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.boughtGoodLevel != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.boughtGoodLevel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtGoodLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtGoodLevel();
            }
        }  else if (builder.hasBoughtGoodLevel()) {
            // 清理BoughtGoodLevel
            builder.clearBoughtGoodLevel();
            fieldCnt++;
        }
        if (this.getRewardTaken()) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }  else if (builder.hasRewardTaken()) {
            // 清理RewardTaken
            builder.clearRewardTaken();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.boughtSuperGoodLevel != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.boughtSuperGoodLevel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtSuperGoodLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtSuperGoodLevel();
            }
        }  else if (builder.hasBoughtSuperGoodLevel()) {
            // 清理BoughtSuperGoodLevel
            builder.clearBoughtSuperGoodLevel();
            fieldCnt++;
        }
        if (this.getSuperRewardTaken()) {
            builder.setSuperRewardTaken(this.getSuperRewardTaken());
            fieldCnt++;
        }  else if (builder.hasSuperRewardTaken()) {
            // 清理SuperRewardTaken
            builder.clearSuperRewardTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerDailyGoodsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODLEVEL) && this.boughtGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtGoodLevel();
            final int tmpFieldCnt = this.boughtGoodLevel.copyChangeToCs(builder.getBoughtGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTSUPERGOODLEVEL) && this.boughtSuperGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtSuperGoodLevel();
            final int tmpFieldCnt = this.boughtSuperGoodLevel.copyChangeToCs(builder.getBoughtSuperGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtSuperGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUPERREWARDTAKEN)) {
            builder.setSuperRewardTaken(this.getSuperRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerDailyGoodsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODLEVEL) && this.boughtGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtGoodLevel();
            final int tmpFieldCnt = this.boughtGoodLevel.copyChangeToAndClearDeleteKeysCs(builder.getBoughtGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTSUPERGOODLEVEL) && this.boughtSuperGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtSuperGoodLevel();
            final int tmpFieldCnt = this.boughtSuperGoodLevel.copyChangeToAndClearDeleteKeysCs(builder.getBoughtSuperGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtSuperGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUPERREWARDTAKEN)) {
            builder.setSuperRewardTaken(this.getSuperRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerDailyGoodsModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBoughtGoodLevel()) {
            this.getBoughtGoodLevel().mergeFromCs(proto.getBoughtGoodLevel());
        } else {
            if (this.boughtGoodLevel != null) {
                this.boughtGoodLevel.mergeFromCs(proto.getBoughtGoodLevel());
            }
        }
        if (proto.hasRewardTaken()) {
            this.innerSetRewardTaken(proto.getRewardTaken());
        } else {
            this.innerSetRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtSuperGoodLevel()) {
            this.getBoughtSuperGoodLevel().mergeFromCs(proto.getBoughtSuperGoodLevel());
        } else {
            if (this.boughtSuperGoodLevel != null) {
                this.boughtSuperGoodLevel.mergeFromCs(proto.getBoughtSuperGoodLevel());
            }
        }
        if (proto.hasSuperRewardTaken()) {
            this.innerSetSuperRewardTaken(proto.getSuperRewardTaken());
        } else {
            this.innerSetSuperRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerDailyGoodsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerDailyGoodsModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBoughtGoodLevel()) {
            this.getBoughtGoodLevel().mergeChangeFromCs(proto.getBoughtGoodLevel());
            fieldCnt++;
        }
        if (proto.hasRewardTaken()) {
            this.setRewardTaken(proto.getRewardTaken());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasBoughtSuperGoodLevel()) {
            this.getBoughtSuperGoodLevel().mergeChangeFromCs(proto.getBoughtSuperGoodLevel());
            fieldCnt++;
        }
        if (proto.hasSuperRewardTaken()) {
            this.setSuperRewardTaken(proto.getSuperRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDailyGoodsModel.Builder getCopyDbBuilder() {
        final PlayerDailyGoodsModel.Builder builder = PlayerDailyGoodsModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerDailyGoodsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.boughtGoodLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.boughtGoodLevel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtGoodLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtGoodLevel();
            }
        }  else if (builder.hasBoughtGoodLevel()) {
            // 清理BoughtGoodLevel
            builder.clearBoughtGoodLevel();
            fieldCnt++;
        }
        if (this.getRewardTaken()) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }  else if (builder.hasRewardTaken()) {
            // 清理RewardTaken
            builder.clearRewardTaken();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.boughtSuperGoodLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.boughtSuperGoodLevel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtSuperGoodLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtSuperGoodLevel();
            }
        }  else if (builder.hasBoughtSuperGoodLevel()) {
            // 清理BoughtSuperGoodLevel
            builder.clearBoughtSuperGoodLevel();
            fieldCnt++;
        }
        if (this.getSuperRewardTaken()) {
            builder.setSuperRewardTaken(this.getSuperRewardTaken());
            fieldCnt++;
        }  else if (builder.hasSuperRewardTaken()) {
            // 清理SuperRewardTaken
            builder.clearSuperRewardTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerDailyGoodsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODLEVEL) && this.boughtGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtGoodLevel();
            final int tmpFieldCnt = this.boughtGoodLevel.copyChangeToDb(builder.getBoughtGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTSUPERGOODLEVEL) && this.boughtSuperGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtSuperGoodLevel();
            final int tmpFieldCnt = this.boughtSuperGoodLevel.copyChangeToDb(builder.getBoughtSuperGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtSuperGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUPERREWARDTAKEN)) {
            builder.setSuperRewardTaken(this.getSuperRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerDailyGoodsModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBoughtGoodLevel()) {
            this.getBoughtGoodLevel().mergeFromDb(proto.getBoughtGoodLevel());
        } else {
            if (this.boughtGoodLevel != null) {
                this.boughtGoodLevel.mergeFromDb(proto.getBoughtGoodLevel());
            }
        }
        if (proto.hasRewardTaken()) {
            this.innerSetRewardTaken(proto.getRewardTaken());
        } else {
            this.innerSetRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtSuperGoodLevel()) {
            this.getBoughtSuperGoodLevel().mergeFromDb(proto.getBoughtSuperGoodLevel());
        } else {
            if (this.boughtSuperGoodLevel != null) {
                this.boughtSuperGoodLevel.mergeFromDb(proto.getBoughtSuperGoodLevel());
            }
        }
        if (proto.hasSuperRewardTaken()) {
            this.innerSetSuperRewardTaken(proto.getSuperRewardTaken());
        } else {
            this.innerSetSuperRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerDailyGoodsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerDailyGoodsModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBoughtGoodLevel()) {
            this.getBoughtGoodLevel().mergeChangeFromDb(proto.getBoughtGoodLevel());
            fieldCnt++;
        }
        if (proto.hasRewardTaken()) {
            this.setRewardTaken(proto.getRewardTaken());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasBoughtSuperGoodLevel()) {
            this.getBoughtSuperGoodLevel().mergeChangeFromDb(proto.getBoughtSuperGoodLevel());
            fieldCnt++;
        }
        if (proto.hasSuperRewardTaken()) {
            this.setSuperRewardTaken(proto.getSuperRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerDailyGoodsModel.Builder getCopySsBuilder() {
        final PlayerDailyGoodsModel.Builder builder = PlayerDailyGoodsModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerDailyGoodsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.boughtGoodLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.boughtGoodLevel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtGoodLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtGoodLevel();
            }
        }  else if (builder.hasBoughtGoodLevel()) {
            // 清理BoughtGoodLevel
            builder.clearBoughtGoodLevel();
            fieldCnt++;
        }
        if (this.getRewardTaken()) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }  else if (builder.hasRewardTaken()) {
            // 清理RewardTaken
            builder.clearRewardTaken();
            fieldCnt++;
        }
        if (this.getHeroId() != 0) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }  else if (builder.hasHeroId()) {
            // 清理HeroId
            builder.clearHeroId();
            fieldCnt++;
        }
        if (this.boughtSuperGoodLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.boughtSuperGoodLevel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBoughtSuperGoodLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBoughtSuperGoodLevel();
            }
        }  else if (builder.hasBoughtSuperGoodLevel()) {
            // 清理BoughtSuperGoodLevel
            builder.clearBoughtSuperGoodLevel();
            fieldCnt++;
        }
        if (this.getSuperRewardTaken()) {
            builder.setSuperRewardTaken(this.getSuperRewardTaken());
            fieldCnt++;
        }  else if (builder.hasSuperRewardTaken()) {
            // 清理SuperRewardTaken
            builder.clearSuperRewardTaken();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerDailyGoodsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODLEVEL) && this.boughtGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtGoodLevel();
            final int tmpFieldCnt = this.boughtGoodLevel.copyChangeToSs(builder.getBoughtGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDTAKEN)) {
            builder.setRewardTaken(this.getRewardTaken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HEROID)) {
            builder.setHeroId(this.getHeroId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTSUPERGOODLEVEL) && this.boughtSuperGoodLevel != null) {
            final boolean needClear = !builder.hasBoughtSuperGoodLevel();
            final int tmpFieldCnt = this.boughtSuperGoodLevel.copyChangeToSs(builder.getBoughtSuperGoodLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBoughtSuperGoodLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_SUPERREWARDTAKEN)) {
            builder.setSuperRewardTaken(this.getSuperRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerDailyGoodsModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBoughtGoodLevel()) {
            this.getBoughtGoodLevel().mergeFromSs(proto.getBoughtGoodLevel());
        } else {
            if (this.boughtGoodLevel != null) {
                this.boughtGoodLevel.mergeFromSs(proto.getBoughtGoodLevel());
            }
        }
        if (proto.hasRewardTaken()) {
            this.innerSetRewardTaken(proto.getRewardTaken());
        } else {
            this.innerSetRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasHeroId()) {
            this.innerSetHeroId(proto.getHeroId());
        } else {
            this.innerSetHeroId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBoughtSuperGoodLevel()) {
            this.getBoughtSuperGoodLevel().mergeFromSs(proto.getBoughtSuperGoodLevel());
        } else {
            if (this.boughtSuperGoodLevel != null) {
                this.boughtSuperGoodLevel.mergeFromSs(proto.getBoughtSuperGoodLevel());
            }
        }
        if (proto.hasSuperRewardTaken()) {
            this.innerSetSuperRewardTaken(proto.getSuperRewardTaken());
        } else {
            this.innerSetSuperRewardTaken(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerDailyGoodsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerDailyGoodsModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBoughtGoodLevel()) {
            this.getBoughtGoodLevel().mergeChangeFromSs(proto.getBoughtGoodLevel());
            fieldCnt++;
        }
        if (proto.hasRewardTaken()) {
            this.setRewardTaken(proto.getRewardTaken());
            fieldCnt++;
        }
        if (proto.hasHeroId()) {
            this.setHeroId(proto.getHeroId());
            fieldCnt++;
        }
        if (proto.hasBoughtSuperGoodLevel()) {
            this.getBoughtSuperGoodLevel().mergeChangeFromSs(proto.getBoughtSuperGoodLevel());
            fieldCnt++;
        }
        if (proto.hasSuperRewardTaken()) {
            this.setSuperRewardTaken(proto.getSuperRewardTaken());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerDailyGoodsModel.Builder builder = PlayerDailyGoodsModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTGOODLEVEL) && this.boughtGoodLevel != null) {
            this.boughtGoodLevel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BOUGHTSUPERGOODLEVEL) && this.boughtSuperGoodLevel != null) {
            this.boughtSuperGoodLevel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.boughtGoodLevel != null) {
            this.boughtGoodLevel.markAll();
        }
        if (this.boughtSuperGoodLevel != null) {
            this.boughtSuperGoodLevel.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerDailyGoodsModelProp)) {
            return false;
        }
        final PlayerDailyGoodsModelProp otherNode = (PlayerDailyGoodsModelProp) node;
        if (!this.getBoughtGoodLevel().compareDataTo(otherNode.getBoughtGoodLevel())) {
            return false;
        }
        if (this.rewardTaken != otherNode.rewardTaken) {
            return false;
        }
        if (this.heroId != otherNode.heroId) {
            return false;
        }
        if (!this.getBoughtSuperGoodLevel().compareDataTo(otherNode.getBoughtSuperGoodLevel())) {
            return false;
        }
        if (this.superRewardTaken != otherNode.superRewardTaken) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
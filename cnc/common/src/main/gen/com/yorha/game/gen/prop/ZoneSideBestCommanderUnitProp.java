package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Zoneside.ZoneSideBestCommanderUnit;
import com.yorha.proto.Zoneside;
import com.yorha.proto.ZonesidePB.ZoneSideBestCommanderUnitPB;
import com.yorha.proto.ZonesidePB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneSideBestCommanderUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SUBRANKSETTLEDATA = 0;
    public static final int FIELD_INDEX_CURSEASON = 1;
    public static final int FIELD_INDEX_VOLUMECURSEASON = 2;
    public static final int FIELD_INDEX_CURSTAGE = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private Int32ZoneSideBestCommanderRankSettleDataMapProp subRankSettleData = null;
    private ZoneSeason curSeason = ZoneSeason.forNumber(0);
    private int volumeCurSeason = Constant.DEFAULT_INT_VALUE;
    private ZoneSeasonStage curStage = ZoneSeasonStage.forNumber(0);

    public ZoneSideBestCommanderUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneSideBestCommanderUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get subRankSettleData
     *
     * @return subRankSettleData value
     */
    public Int32ZoneSideBestCommanderRankSettleDataMapProp getSubRankSettleData() {
        if (this.subRankSettleData == null) {
            this.subRankSettleData = new Int32ZoneSideBestCommanderRankSettleDataMapProp(this, FIELD_INDEX_SUBRANKSETTLEDATA);
        }
        return this.subRankSettleData;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSubRankSettleDataV(ZoneSideBestCommanderRankSettleDataProp v) {
        this.getSubRankSettleData().put(v.getActId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ZoneSideBestCommanderRankSettleDataProp addEmptySubRankSettleData(Integer k) {
        return this.getSubRankSettleData().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSubRankSettleDataSize() {
        if (this.subRankSettleData == null) {
            return 0;
        }
        return this.subRankSettleData.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSubRankSettleDataEmpty() {
        if (this.subRankSettleData == null) {
            return true;
        }
        return this.subRankSettleData.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ZoneSideBestCommanderRankSettleDataProp getSubRankSettleDataV(Integer k) {
        if (this.subRankSettleData == null || !this.subRankSettleData.containsKey(k)) {
            return null;
        }
        return this.subRankSettleData.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSubRankSettleData() {
        if (this.subRankSettleData != null) {
            this.subRankSettleData.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSubRankSettleDataV(Integer k) {
        if (this.subRankSettleData != null) {
            this.subRankSettleData.remove(k);
        }
    }
    /**
     * get curSeason
     *
     * @return curSeason value
     */
    public ZoneSeason getCurSeason() {
        return this.curSeason;
    }

    /**
     * set curSeason && set marked
     *
     * @param curSeason new value
     * @return current object
     */
    public ZoneSideBestCommanderUnitProp setCurSeason(ZoneSeason curSeason) {
        if (curSeason == null) {
            throw new NullPointerException();
        }
        if (this.curSeason != curSeason) {
            this.mark(FIELD_INDEX_CURSEASON);
            this.curSeason = curSeason;
        }
        return this;
    }

    /**
     * inner set curSeason
     *
     * @param curSeason new value
     */
    private void innerSetCurSeason(ZoneSeason curSeason) {
        this.curSeason = curSeason;
    }

    /**
     * get volumeCurSeason
     *
     * @return volumeCurSeason value
     */
    public int getVolumeCurSeason() {
        return this.volumeCurSeason;
    }

    /**
     * set volumeCurSeason && set marked
     *
     * @param volumeCurSeason new value
     * @return current object
     */
    public ZoneSideBestCommanderUnitProp setVolumeCurSeason(int volumeCurSeason) {
        if (this.volumeCurSeason != volumeCurSeason) {
            this.mark(FIELD_INDEX_VOLUMECURSEASON);
            this.volumeCurSeason = volumeCurSeason;
        }
        return this;
    }

    /**
     * inner set volumeCurSeason
     *
     * @param volumeCurSeason new value
     */
    private void innerSetVolumeCurSeason(int volumeCurSeason) {
        this.volumeCurSeason = volumeCurSeason;
    }

    /**
     * get curStage
     *
     * @return curStage value
     */
    public ZoneSeasonStage getCurStage() {
        return this.curStage;
    }

    /**
     * set curStage && set marked
     *
     * @param curStage new value
     * @return current object
     */
    public ZoneSideBestCommanderUnitProp setCurStage(ZoneSeasonStage curStage) {
        if (curStage == null) {
            throw new NullPointerException();
        }
        if (this.curStage != curStage) {
            this.mark(FIELD_INDEX_CURSTAGE);
            this.curStage = curStage;
        }
        return this;
    }

    /**
     * inner set curStage
     *
     * @param curStage new value
     */
    private void innerSetCurStage(ZoneSeasonStage curStage) {
        this.curStage = curStage;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderUnitPB.Builder getCopyCsBuilder() {
        final ZoneSideBestCommanderUnitPB.Builder builder = ZoneSideBestCommanderUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneSideBestCommanderUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.subRankSettleData != null) {
            ZonesidePB.Int32ZoneSideBestCommanderRankSettleDataMapPB.Builder tmpBuilder = ZonesidePB.Int32ZoneSideBestCommanderRankSettleDataMapPB.newBuilder();
            final int tmpFieldCnt = this.subRankSettleData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSubRankSettleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSubRankSettleData();
            }
        }  else if (builder.hasSubRankSettleData()) {
            // 清理SubRankSettleData
            builder.clearSubRankSettleData();
            fieldCnt++;
        }
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneSideBestCommanderUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SUBRANKSETTLEDATA) && this.subRankSettleData != null) {
            final boolean needClear = !builder.hasSubRankSettleData();
            final int tmpFieldCnt = this.subRankSettleData.copyChangeToCs(builder.getSubRankSettleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubRankSettleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneSideBestCommanderUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SUBRANKSETTLEDATA) && this.subRankSettleData != null) {
            final boolean needClear = !builder.hasSubRankSettleData();
            final int tmpFieldCnt = this.subRankSettleData.copyChangeToAndClearDeleteKeysCs(builder.getSubRankSettleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubRankSettleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneSideBestCommanderUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSubRankSettleData()) {
            this.getSubRankSettleData().mergeFromCs(proto.getSubRankSettleData());
        } else {
            if (this.subRankSettleData != null) {
                this.subRankSettleData.mergeFromCs(proto.getSubRankSettleData());
            }
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideBestCommanderUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneSideBestCommanderUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSubRankSettleData()) {
            this.getSubRankSettleData().mergeChangeFromCs(proto.getSubRankSettleData());
            fieldCnt++;
        }
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderUnit.Builder getCopyDbBuilder() {
        final ZoneSideBestCommanderUnit.Builder builder = ZoneSideBestCommanderUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneSideBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.subRankSettleData != null) {
            Zoneside.Int32ZoneSideBestCommanderRankSettleDataMap.Builder tmpBuilder = Zoneside.Int32ZoneSideBestCommanderRankSettleDataMap.newBuilder();
            final int tmpFieldCnt = this.subRankSettleData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSubRankSettleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSubRankSettleData();
            }
        }  else if (builder.hasSubRankSettleData()) {
            // 清理SubRankSettleData
            builder.clearSubRankSettleData();
            fieldCnt++;
        }
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneSideBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SUBRANKSETTLEDATA) && this.subRankSettleData != null) {
            final boolean needClear = !builder.hasSubRankSettleData();
            final int tmpFieldCnt = this.subRankSettleData.copyChangeToDb(builder.getSubRankSettleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubRankSettleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneSideBestCommanderUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSubRankSettleData()) {
            this.getSubRankSettleData().mergeFromDb(proto.getSubRankSettleData());
        } else {
            if (this.subRankSettleData != null) {
                this.subRankSettleData.mergeFromDb(proto.getSubRankSettleData());
            }
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideBestCommanderUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneSideBestCommanderUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSubRankSettleData()) {
            this.getSubRankSettleData().mergeChangeFromDb(proto.getSubRankSettleData());
            fieldCnt++;
        }
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderUnit.Builder getCopySsBuilder() {
        final ZoneSideBestCommanderUnit.Builder builder = ZoneSideBestCommanderUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneSideBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.subRankSettleData != null) {
            Zoneside.Int32ZoneSideBestCommanderRankSettleDataMap.Builder tmpBuilder = Zoneside.Int32ZoneSideBestCommanderRankSettleDataMap.newBuilder();
            final int tmpFieldCnt = this.subRankSettleData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSubRankSettleData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSubRankSettleData();
            }
        }  else if (builder.hasSubRankSettleData()) {
            // 清理SubRankSettleData
            builder.clearSubRankSettleData();
            fieldCnt++;
        }
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getVolumeCurSeason() != 0) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }  else if (builder.hasVolumeCurSeason()) {
            // 清理VolumeCurSeason
            builder.clearVolumeCurSeason();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneSideBestCommanderUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SUBRANKSETTLEDATA) && this.subRankSettleData != null) {
            final boolean needClear = !builder.hasSubRankSettleData();
            final int tmpFieldCnt = this.subRankSettleData.copyChangeToSs(builder.getSubRankSettleDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSubRankSettleData();
            }
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VOLUMECURSEASON)) {
            builder.setVolumeCurSeason(this.getVolumeCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneSideBestCommanderUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSubRankSettleData()) {
            this.getSubRankSettleData().mergeFromSs(proto.getSubRankSettleData());
        } else {
            if (this.subRankSettleData != null) {
                this.subRankSettleData.mergeFromSs(proto.getSubRankSettleData());
            }
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasVolumeCurSeason()) {
            this.innerSetVolumeCurSeason(proto.getVolumeCurSeason());
        } else {
            this.innerSetVolumeCurSeason(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideBestCommanderUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneSideBestCommanderUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSubRankSettleData()) {
            this.getSubRankSettleData().mergeChangeFromSs(proto.getSubRankSettleData());
            fieldCnt++;
        }
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasVolumeCurSeason()) {
            this.setVolumeCurSeason(proto.getVolumeCurSeason());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneSideBestCommanderUnit.Builder builder = ZoneSideBestCommanderUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SUBRANKSETTLEDATA) && this.subRankSettleData != null) {
            this.subRankSettleData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.subRankSettleData != null) {
            this.subRankSettleData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneSideBestCommanderUnitProp)) {
            return false;
        }
        final ZoneSideBestCommanderUnitProp otherNode = (ZoneSideBestCommanderUnitProp) node;
        if (!this.getSubRankSettleData().compareDataTo(otherNode.getSubRankSettleData())) {
            return false;
        }
        if (this.curSeason != otherNode.curSeason) {
            return false;
        }
        if (this.volumeCurSeason != otherNode.volumeCurSeason) {
            return false;
        }
        if (this.curStage != otherNode.curStage) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
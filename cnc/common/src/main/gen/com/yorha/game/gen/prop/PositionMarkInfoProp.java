package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PositionMarkInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.PositionMarkInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PositionMarkInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_MARKID = 0;
    public static final int FIELD_INDEX_POINT = 1;
    public static final int FIELD_INDEX_MARKNAME = 2;
    public static final int FIELD_INDEX_MARKPICID = 3;
    public static final int FIELD_INDEX_OPERATORNAME = 4;
    public static final int FIELD_INDEX_ZONEID = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long markId = Constant.DEFAULT_LONG_VALUE;
    private PointProp point = null;
    private String markName = Constant.DEFAULT_STR_VALUE;
    private int markPicId = Constant.DEFAULT_INT_VALUE;
    private String operatorName = Constant.DEFAULT_STR_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public PositionMarkInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PositionMarkInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get markId
     *
     * @return markId value
     */
    public long getMarkId() {
        return this.markId;
    }

    /**
     * set markId && set marked
     *
     * @param markId new value
     * @return current object
     */
    public PositionMarkInfoProp setMarkId(long markId) {
        if (this.markId != markId) {
            this.mark(FIELD_INDEX_MARKID);
            this.markId = markId;
        }
        return this;
    }

    /**
     * inner set markId
     *
     * @param markId new value
     */
    private void innerSetMarkId(long markId) {
        this.markId = markId;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get markName
     *
     * @return markName value
     */
    public String getMarkName() {
        return this.markName;
    }

    /**
     * set markName && set marked
     *
     * @param markName new value
     * @return current object
     */
    public PositionMarkInfoProp setMarkName(String markName) {
        if (markName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.markName, markName)) {
            this.mark(FIELD_INDEX_MARKNAME);
            this.markName = markName;
        }
        return this;
    }

    /**
     * inner set markName
     *
     * @param markName new value
     */
    private void innerSetMarkName(String markName) {
        this.markName = markName;
    }

    /**
     * get markPicId
     *
     * @return markPicId value
     */
    public int getMarkPicId() {
        return this.markPicId;
    }

    /**
     * set markPicId && set marked
     *
     * @param markPicId new value
     * @return current object
     */
    public PositionMarkInfoProp setMarkPicId(int markPicId) {
        if (this.markPicId != markPicId) {
            this.mark(FIELD_INDEX_MARKPICID);
            this.markPicId = markPicId;
        }
        return this;
    }

    /**
     * inner set markPicId
     *
     * @param markPicId new value
     */
    private void innerSetMarkPicId(int markPicId) {
        this.markPicId = markPicId;
    }

    /**
     * get operatorName
     *
     * @return operatorName value
     */
    public String getOperatorName() {
        return this.operatorName;
    }

    /**
     * set operatorName && set marked
     *
     * @param operatorName new value
     * @return current object
     */
    public PositionMarkInfoProp setOperatorName(String operatorName) {
        if (operatorName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.operatorName, operatorName)) {
            this.mark(FIELD_INDEX_OPERATORNAME);
            this.operatorName = operatorName;
        }
        return this;
    }

    /**
     * inner set operatorName
     *
     * @param operatorName new value
     */
    private void innerSetOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public PositionMarkInfoProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PositionMarkInfoPB.Builder getCopyCsBuilder() {
        final PositionMarkInfoPB.Builder builder = PositionMarkInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PositionMarkInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMarkId() != 0L) {
            builder.setMarkId(this.getMarkId());
            fieldCnt++;
        }  else if (builder.hasMarkId()) {
            // 清理MarkId
            builder.clearMarkId();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (!this.getMarkName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setMarkName(this.getMarkName());
            fieldCnt++;
        }  else if (builder.hasMarkName()) {
            // 清理MarkName
            builder.clearMarkName();
            fieldCnt++;
        }
        if (this.getMarkPicId() != 0) {
            builder.setMarkPicId(this.getMarkPicId());
            fieldCnt++;
        }  else if (builder.hasMarkPicId()) {
            // 清理MarkPicId
            builder.clearMarkPicId();
            fieldCnt++;
        }
        if (!this.getOperatorName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOperatorName(this.getOperatorName());
            fieldCnt++;
        }  else if (builder.hasOperatorName()) {
            // 清理OperatorName
            builder.clearOperatorName();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PositionMarkInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MARKID)) {
            builder.setMarkId(this.getMarkId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_MARKNAME)) {
            builder.setMarkName(this.getMarkName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MARKPICID)) {
            builder.setMarkPicId(this.getMarkPicId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPERATORNAME)) {
            builder.setOperatorName(this.getOperatorName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PositionMarkInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MARKID)) {
            builder.setMarkId(this.getMarkId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_MARKNAME)) {
            builder.setMarkName(this.getMarkName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MARKPICID)) {
            builder.setMarkPicId(this.getMarkPicId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPERATORNAME)) {
            builder.setOperatorName(this.getOperatorName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PositionMarkInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMarkId()) {
            this.innerSetMarkId(proto.getMarkId());
        } else {
            this.innerSetMarkId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasMarkName()) {
            this.innerSetMarkName(proto.getMarkName());
        } else {
            this.innerSetMarkName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasMarkPicId()) {
            this.innerSetMarkPicId(proto.getMarkPicId());
        } else {
            this.innerSetMarkPicId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOperatorName()) {
            this.innerSetOperatorName(proto.getOperatorName());
        } else {
            this.innerSetOperatorName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PositionMarkInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PositionMarkInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMarkId()) {
            this.setMarkId(proto.getMarkId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasMarkName()) {
            this.setMarkName(proto.getMarkName());
            fieldCnt++;
        }
        if (proto.hasMarkPicId()) {
            this.setMarkPicId(proto.getMarkPicId());
            fieldCnt++;
        }
        if (proto.hasOperatorName()) {
            this.setOperatorName(proto.getOperatorName());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PositionMarkInfo.Builder getCopyDbBuilder() {
        final PositionMarkInfo.Builder builder = PositionMarkInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PositionMarkInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMarkId() != 0L) {
            builder.setMarkId(this.getMarkId());
            fieldCnt++;
        }  else if (builder.hasMarkId()) {
            // 清理MarkId
            builder.clearMarkId();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (!this.getMarkName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setMarkName(this.getMarkName());
            fieldCnt++;
        }  else if (builder.hasMarkName()) {
            // 清理MarkName
            builder.clearMarkName();
            fieldCnt++;
        }
        if (this.getMarkPicId() != 0) {
            builder.setMarkPicId(this.getMarkPicId());
            fieldCnt++;
        }  else if (builder.hasMarkPicId()) {
            // 清理MarkPicId
            builder.clearMarkPicId();
            fieldCnt++;
        }
        if (!this.getOperatorName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOperatorName(this.getOperatorName());
            fieldCnt++;
        }  else if (builder.hasOperatorName()) {
            // 清理OperatorName
            builder.clearOperatorName();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PositionMarkInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MARKID)) {
            builder.setMarkId(this.getMarkId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_MARKNAME)) {
            builder.setMarkName(this.getMarkName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MARKPICID)) {
            builder.setMarkPicId(this.getMarkPicId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPERATORNAME)) {
            builder.setOperatorName(this.getOperatorName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PositionMarkInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMarkId()) {
            this.innerSetMarkId(proto.getMarkId());
        } else {
            this.innerSetMarkId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasMarkName()) {
            this.innerSetMarkName(proto.getMarkName());
        } else {
            this.innerSetMarkName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasMarkPicId()) {
            this.innerSetMarkPicId(proto.getMarkPicId());
        } else {
            this.innerSetMarkPicId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOperatorName()) {
            this.innerSetOperatorName(proto.getOperatorName());
        } else {
            this.innerSetOperatorName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PositionMarkInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PositionMarkInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMarkId()) {
            this.setMarkId(proto.getMarkId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasMarkName()) {
            this.setMarkName(proto.getMarkName());
            fieldCnt++;
        }
        if (proto.hasMarkPicId()) {
            this.setMarkPicId(proto.getMarkPicId());
            fieldCnt++;
        }
        if (proto.hasOperatorName()) {
            this.setOperatorName(proto.getOperatorName());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PositionMarkInfo.Builder getCopySsBuilder() {
        final PositionMarkInfo.Builder builder = PositionMarkInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PositionMarkInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMarkId() != 0L) {
            builder.setMarkId(this.getMarkId());
            fieldCnt++;
        }  else if (builder.hasMarkId()) {
            // 清理MarkId
            builder.clearMarkId();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (!this.getMarkName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setMarkName(this.getMarkName());
            fieldCnt++;
        }  else if (builder.hasMarkName()) {
            // 清理MarkName
            builder.clearMarkName();
            fieldCnt++;
        }
        if (this.getMarkPicId() != 0) {
            builder.setMarkPicId(this.getMarkPicId());
            fieldCnt++;
        }  else if (builder.hasMarkPicId()) {
            // 清理MarkPicId
            builder.clearMarkPicId();
            fieldCnt++;
        }
        if (!this.getOperatorName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOperatorName(this.getOperatorName());
            fieldCnt++;
        }  else if (builder.hasOperatorName()) {
            // 清理OperatorName
            builder.clearOperatorName();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PositionMarkInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MARKID)) {
            builder.setMarkId(this.getMarkId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_MARKNAME)) {
            builder.setMarkName(this.getMarkName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MARKPICID)) {
            builder.setMarkPicId(this.getMarkPicId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPERATORNAME)) {
            builder.setOperatorName(this.getOperatorName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PositionMarkInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMarkId()) {
            this.innerSetMarkId(proto.getMarkId());
        } else {
            this.innerSetMarkId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasMarkName()) {
            this.innerSetMarkName(proto.getMarkName());
        } else {
            this.innerSetMarkName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasMarkPicId()) {
            this.innerSetMarkPicId(proto.getMarkPicId());
        } else {
            this.innerSetMarkPicId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOperatorName()) {
            this.innerSetOperatorName(proto.getOperatorName());
        } else {
            this.innerSetOperatorName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PositionMarkInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PositionMarkInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMarkId()) {
            this.setMarkId(proto.getMarkId());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasMarkName()) {
            this.setMarkName(proto.getMarkName());
            fieldCnt++;
        }
        if (proto.hasMarkPicId()) {
            this.setMarkPicId(proto.getMarkPicId());
            fieldCnt++;
        }
        if (proto.hasOperatorName()) {
            this.setOperatorName(proto.getOperatorName());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PositionMarkInfo.Builder builder = PositionMarkInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.markId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PositionMarkInfoProp)) {
            return false;
        }
        final PositionMarkInfoProp otherNode = (PositionMarkInfoProp) node;
        if (this.markId != otherNode.markId) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.markName, otherNode.markName)) {
            return false;
        }
        if (this.markPicId != otherNode.markPicId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.operatorName, otherNode.operatorName)) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
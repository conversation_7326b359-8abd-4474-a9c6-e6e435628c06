package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.FestivalBpParam;
import com.yorha.proto.StructPB.FestivalBpParamPB;


/**
 * <AUTHOR> auto gen
 */
public class FestivalBpParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTID = 0;
    public static final int FIELD_INDEX_UNITID = 1;
    public static final int FIELD_INDEX_BPID = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int actId = Constant.DEFAULT_INT_VALUE;
    private int unitId = Constant.DEFAULT_INT_VALUE;
    private int bpId = Constant.DEFAULT_INT_VALUE;

    public FestivalBpParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public FestivalBpParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actId
     *
     * @return actId value
     */
    public int getActId() {
        return this.actId;
    }

    /**
     * set actId && set marked
     *
     * @param actId new value
     * @return current object
     */
    public FestivalBpParamProp setActId(int actId) {
        if (this.actId != actId) {
            this.mark(FIELD_INDEX_ACTID);
            this.actId = actId;
        }
        return this;
    }

    /**
     * inner set actId
     *
     * @param actId new value
     */
    private void innerSetActId(int actId) {
        this.actId = actId;
    }

    /**
     * get unitId
     *
     * @return unitId value
     */
    public int getUnitId() {
        return this.unitId;
    }

    /**
     * set unitId && set marked
     *
     * @param unitId new value
     * @return current object
     */
    public FestivalBpParamProp setUnitId(int unitId) {
        if (this.unitId != unitId) {
            this.mark(FIELD_INDEX_UNITID);
            this.unitId = unitId;
        }
        return this;
    }

    /**
     * inner set unitId
     *
     * @param unitId new value
     */
    private void innerSetUnitId(int unitId) {
        this.unitId = unitId;
    }

    /**
     * get bpId
     *
     * @return bpId value
     */
    public int getBpId() {
        return this.bpId;
    }

    /**
     * set bpId && set marked
     *
     * @param bpId new value
     * @return current object
     */
    public FestivalBpParamProp setBpId(int bpId) {
        if (this.bpId != bpId) {
            this.mark(FIELD_INDEX_BPID);
            this.bpId = bpId;
        }
        return this;
    }

    /**
     * inner set bpId
     *
     * @param bpId new value
     */
    private void innerSetBpId(int bpId) {
        this.bpId = bpId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FestivalBpParamPB.Builder getCopyCsBuilder() {
        final FestivalBpParamPB.Builder builder = FestivalBpParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(FestivalBpParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getBpId() != 0) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }  else if (builder.hasBpId()) {
            // 清理BpId
            builder.clearBpId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(FestivalBpParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(FestivalBpParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(FestivalBpParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBpId()) {
            this.innerSetBpId(proto.getBpId());
        } else {
            this.innerSetBpId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return FestivalBpParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(FestivalBpParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasBpId()) {
            this.setBpId(proto.getBpId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FestivalBpParam.Builder getCopyDbBuilder() {
        final FestivalBpParam.Builder builder = FestivalBpParam.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(FestivalBpParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getBpId() != 0) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }  else if (builder.hasBpId()) {
            // 清理BpId
            builder.clearBpId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(FestivalBpParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(FestivalBpParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBpId()) {
            this.innerSetBpId(proto.getBpId());
        } else {
            this.innerSetBpId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return FestivalBpParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(FestivalBpParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasBpId()) {
            this.setBpId(proto.getBpId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public FestivalBpParam.Builder getCopySsBuilder() {
        final FestivalBpParam.Builder builder = FestivalBpParam.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(FestivalBpParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getBpId() != 0) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }  else if (builder.hasBpId()) {
            // 清理BpId
            builder.clearBpId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(FestivalBpParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPID)) {
            builder.setBpId(this.getBpId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(FestivalBpParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBpId()) {
            this.innerSetBpId(proto.getBpId());
        } else {
            this.innerSetBpId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return FestivalBpParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(FestivalBpParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasBpId()) {
            this.setBpId(proto.getBpId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        FestivalBpParam.Builder builder = FestivalBpParam.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof FestivalBpParamProp)) {
            return false;
        }
        final FestivalBpParamProp otherNode = (FestivalBpParamProp) node;
        if (this.actId != otherNode.actId) {
            return false;
        }
        if (this.unitId != otherNode.unitId) {
            return false;
        }
        if (this.bpId != otherNode.bpId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
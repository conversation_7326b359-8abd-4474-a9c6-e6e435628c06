package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ShopBuyInfo;
import com.yorha.proto.StructPB.ShopBuyInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ShopBuyInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_BUYCOUNT = 1;
    public static final int FIELD_INDEX_STARTTIME = 2;
    public static final int FIELD_INDEX_LASTREFRESHTIME = 3;
    public static final int FIELD_INDEX_BUYCOUNTWITHDAY = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int buyCount = Constant.DEFAULT_INT_VALUE;
    private long startTime = Constant.DEFAULT_LONG_VALUE;
    private long lastRefreshTime = Constant.DEFAULT_LONG_VALUE;
    private int buyCountWithDay = Constant.DEFAULT_INT_VALUE;

    public ShopBuyInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ShopBuyInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ShopBuyInfoProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get buyCount
     *
     * @return buyCount value
     */
    public int getBuyCount() {
        return this.buyCount;
    }

    /**
     * set buyCount && set marked
     *
     * @param buyCount new value
     * @return current object
     */
    public ShopBuyInfoProp setBuyCount(int buyCount) {
        if (this.buyCount != buyCount) {
            this.mark(FIELD_INDEX_BUYCOUNT);
            this.buyCount = buyCount;
        }
        return this;
    }

    /**
     * inner set buyCount
     *
     * @param buyCount new value
     */
    private void innerSetBuyCount(int buyCount) {
        this.buyCount = buyCount;
    }

    /**
     * get startTime
     *
     * @return startTime value
     */
    public long getStartTime() {
        return this.startTime;
    }

    /**
     * set startTime && set marked
     *
     * @param startTime new value
     * @return current object
     */
    public ShopBuyInfoProp setStartTime(long startTime) {
        if (this.startTime != startTime) {
            this.mark(FIELD_INDEX_STARTTIME);
            this.startTime = startTime;
        }
        return this;
    }

    /**
     * inner set startTime
     *
     * @param startTime new value
     */
    private void innerSetStartTime(long startTime) {
        this.startTime = startTime;
    }

    /**
     * get lastRefreshTime
     *
     * @return lastRefreshTime value
     */
    public long getLastRefreshTime() {
        return this.lastRefreshTime;
    }

    /**
     * set lastRefreshTime && set marked
     *
     * @param lastRefreshTime new value
     * @return current object
     */
    public ShopBuyInfoProp setLastRefreshTime(long lastRefreshTime) {
        if (this.lastRefreshTime != lastRefreshTime) {
            this.mark(FIELD_INDEX_LASTREFRESHTIME);
            this.lastRefreshTime = lastRefreshTime;
        }
        return this;
    }

    /**
     * inner set lastRefreshTime
     *
     * @param lastRefreshTime new value
     */
    private void innerSetLastRefreshTime(long lastRefreshTime) {
        this.lastRefreshTime = lastRefreshTime;
    }

    /**
     * get buyCountWithDay
     *
     * @return buyCountWithDay value
     */
    public int getBuyCountWithDay() {
        return this.buyCountWithDay;
    }

    /**
     * set buyCountWithDay && set marked
     *
     * @param buyCountWithDay new value
     * @return current object
     */
    public ShopBuyInfoProp setBuyCountWithDay(int buyCountWithDay) {
        if (this.buyCountWithDay != buyCountWithDay) {
            this.mark(FIELD_INDEX_BUYCOUNTWITHDAY);
            this.buyCountWithDay = buyCountWithDay;
        }
        return this;
    }

    /**
     * inner set buyCountWithDay
     *
     * @param buyCountWithDay new value
     */
    private void innerSetBuyCountWithDay(int buyCountWithDay) {
        this.buyCountWithDay = buyCountWithDay;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ShopBuyInfoPB.Builder getCopyCsBuilder() {
        final ShopBuyInfoPB.Builder builder = ShopBuyInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ShopBuyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getBuyCount() != 0) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }  else if (builder.hasBuyCount()) {
            // 清理BuyCount
            builder.clearBuyCount();
            fieldCnt++;
        }
        if (this.getStartTime() != 0L) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }  else if (builder.hasStartTime()) {
            // 清理StartTime
            builder.clearStartTime();
            fieldCnt++;
        }
        if (this.getLastRefreshTime() != 0L) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTime()) {
            // 清理LastRefreshTime
            builder.clearLastRefreshTime();
            fieldCnt++;
        }
        if (this.getBuyCountWithDay() != 0) {
            builder.setBuyCountWithDay(this.getBuyCountWithDay());
            fieldCnt++;
        }  else if (builder.hasBuyCountWithDay()) {
            // 清理BuyCountWithDay
            builder.clearBuyCountWithDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ShopBuyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNTWITHDAY)) {
            builder.setBuyCountWithDay(this.getBuyCountWithDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ShopBuyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNTWITHDAY)) {
            builder.setBuyCountWithDay(this.getBuyCountWithDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ShopBuyInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuyCount()) {
            this.innerSetBuyCount(proto.getBuyCount());
        } else {
            this.innerSetBuyCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTime()) {
            this.innerSetStartTime(proto.getStartTime());
        } else {
            this.innerSetStartTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastRefreshTime()) {
            this.innerSetLastRefreshTime(proto.getLastRefreshTime());
        } else {
            this.innerSetLastRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuyCountWithDay()) {
            this.innerSetBuyCountWithDay(proto.getBuyCountWithDay());
        } else {
            this.innerSetBuyCountWithDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ShopBuyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ShopBuyInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasBuyCount()) {
            this.setBuyCount(proto.getBuyCount());
            fieldCnt++;
        }
        if (proto.hasStartTime()) {
            this.setStartTime(proto.getStartTime());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTime()) {
            this.setLastRefreshTime(proto.getLastRefreshTime());
            fieldCnt++;
        }
        if (proto.hasBuyCountWithDay()) {
            this.setBuyCountWithDay(proto.getBuyCountWithDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ShopBuyInfo.Builder getCopyDbBuilder() {
        final ShopBuyInfo.Builder builder = ShopBuyInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ShopBuyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getBuyCount() != 0) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }  else if (builder.hasBuyCount()) {
            // 清理BuyCount
            builder.clearBuyCount();
            fieldCnt++;
        }
        if (this.getStartTime() != 0L) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }  else if (builder.hasStartTime()) {
            // 清理StartTime
            builder.clearStartTime();
            fieldCnt++;
        }
        if (this.getLastRefreshTime() != 0L) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTime()) {
            // 清理LastRefreshTime
            builder.clearLastRefreshTime();
            fieldCnt++;
        }
        if (this.getBuyCountWithDay() != 0) {
            builder.setBuyCountWithDay(this.getBuyCountWithDay());
            fieldCnt++;
        }  else if (builder.hasBuyCountWithDay()) {
            // 清理BuyCountWithDay
            builder.clearBuyCountWithDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ShopBuyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNTWITHDAY)) {
            builder.setBuyCountWithDay(this.getBuyCountWithDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ShopBuyInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuyCount()) {
            this.innerSetBuyCount(proto.getBuyCount());
        } else {
            this.innerSetBuyCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTime()) {
            this.innerSetStartTime(proto.getStartTime());
        } else {
            this.innerSetStartTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastRefreshTime()) {
            this.innerSetLastRefreshTime(proto.getLastRefreshTime());
        } else {
            this.innerSetLastRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuyCountWithDay()) {
            this.innerSetBuyCountWithDay(proto.getBuyCountWithDay());
        } else {
            this.innerSetBuyCountWithDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ShopBuyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ShopBuyInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasBuyCount()) {
            this.setBuyCount(proto.getBuyCount());
            fieldCnt++;
        }
        if (proto.hasStartTime()) {
            this.setStartTime(proto.getStartTime());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTime()) {
            this.setLastRefreshTime(proto.getLastRefreshTime());
            fieldCnt++;
        }
        if (proto.hasBuyCountWithDay()) {
            this.setBuyCountWithDay(proto.getBuyCountWithDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ShopBuyInfo.Builder getCopySsBuilder() {
        final ShopBuyInfo.Builder builder = ShopBuyInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ShopBuyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getBuyCount() != 0) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }  else if (builder.hasBuyCount()) {
            // 清理BuyCount
            builder.clearBuyCount();
            fieldCnt++;
        }
        if (this.getStartTime() != 0L) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }  else if (builder.hasStartTime()) {
            // 清理StartTime
            builder.clearStartTime();
            fieldCnt++;
        }
        if (this.getLastRefreshTime() != 0L) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTime()) {
            // 清理LastRefreshTime
            builder.clearLastRefreshTime();
            fieldCnt++;
        }
        if (this.getBuyCountWithDay() != 0) {
            builder.setBuyCountWithDay(this.getBuyCountWithDay());
            fieldCnt++;
        }  else if (builder.hasBuyCountWithDay()) {
            // 清理BuyCountWithDay
            builder.clearBuyCountWithDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ShopBuyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNT)) {
            builder.setBuyCount(this.getBuyCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STARTTIME)) {
            builder.setStartTime(this.getStartTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYCOUNTWITHDAY)) {
            builder.setBuyCountWithDay(this.getBuyCountWithDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ShopBuyInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuyCount()) {
            this.innerSetBuyCount(proto.getBuyCount());
        } else {
            this.innerSetBuyCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStartTime()) {
            this.innerSetStartTime(proto.getStartTime());
        } else {
            this.innerSetStartTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastRefreshTime()) {
            this.innerSetLastRefreshTime(proto.getLastRefreshTime());
        } else {
            this.innerSetLastRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuyCountWithDay()) {
            this.innerSetBuyCountWithDay(proto.getBuyCountWithDay());
        } else {
            this.innerSetBuyCountWithDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ShopBuyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ShopBuyInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasBuyCount()) {
            this.setBuyCount(proto.getBuyCount());
            fieldCnt++;
        }
        if (proto.hasStartTime()) {
            this.setStartTime(proto.getStartTime());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTime()) {
            this.setLastRefreshTime(proto.getLastRefreshTime());
            fieldCnt++;
        }
        if (proto.hasBuyCountWithDay()) {
            this.setBuyCountWithDay(proto.getBuyCountWithDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ShopBuyInfo.Builder builder = ShopBuyInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ShopBuyInfoProp)) {
            return false;
        }
        final ShopBuyInfoProp otherNode = (ShopBuyInfoProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.buyCount != otherNode.buyCount) {
            return false;
        }
        if (this.startTime != otherNode.startTime) {
            return false;
        }
        if (this.lastRefreshTime != otherNode.lastRefreshTime) {
            return false;
        }
        if (this.buyCountWithDay != otherNode.buyCountWithDay) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
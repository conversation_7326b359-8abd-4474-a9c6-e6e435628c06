package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PowerInfoUnit;
import com.yorha.proto.StructPB.PowerInfoUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class PowerInfoUnitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_POWERTYPE = 0;
    public static final int FIELD_INDEX_POWER = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int powerType = Constant.DEFAULT_INT_VALUE;
    private long power = Constant.DEFAULT_LONG_VALUE;

    public PowerInfoUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PowerInfoUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get powerType
     *
     * @return powerType value
     */
    public int getPowerType() {
        return this.powerType;
    }

    /**
     * set powerType && set marked
     *
     * @param powerType new value
     * @return current object
     */
    public PowerInfoUnitProp setPowerType(int powerType) {
        if (this.powerType != powerType) {
            this.mark(FIELD_INDEX_POWERTYPE);
            this.powerType = powerType;
        }
        return this;
    }

    /**
     * inner set powerType
     *
     * @param powerType new value
     */
    private void innerSetPowerType(int powerType) {
        this.powerType = powerType;
    }

    /**
     * get power
     *
     * @return power value
     */
    public long getPower() {
        return this.power;
    }

    /**
     * set power && set marked
     *
     * @param power new value
     * @return current object
     */
    public PowerInfoUnitProp setPower(long power) {
        if (this.power != power) {
            this.mark(FIELD_INDEX_POWER);
            this.power = power;
        }
        return this;
    }

    /**
     * inner set power
     *
     * @param power new value
     */
    private void innerSetPower(long power) {
        this.power = power;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PowerInfoUnitPB.Builder getCopyCsBuilder() {
        final PowerInfoUnitPB.Builder builder = PowerInfoUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PowerInfoUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPowerType() != 0) {
            builder.setPowerType(this.getPowerType());
            fieldCnt++;
        }  else if (builder.hasPowerType()) {
            // 清理PowerType
            builder.clearPowerType();
            fieldCnt++;
        }
        if (this.getPower() != 0L) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }  else if (builder.hasPower()) {
            // 清理Power
            builder.clearPower();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PowerInfoUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POWERTYPE)) {
            builder.setPowerType(this.getPowerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWER)) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PowerInfoUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POWERTYPE)) {
            builder.setPowerType(this.getPowerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWER)) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PowerInfoUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPowerType()) {
            this.innerSetPowerType(proto.getPowerType());
        } else {
            this.innerSetPowerType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPower()) {
            this.innerSetPower(proto.getPower());
        } else {
            this.innerSetPower(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PowerInfoUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PowerInfoUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPowerType()) {
            this.setPowerType(proto.getPowerType());
            fieldCnt++;
        }
        if (proto.hasPower()) {
            this.setPower(proto.getPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PowerInfoUnit.Builder getCopyDbBuilder() {
        final PowerInfoUnit.Builder builder = PowerInfoUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PowerInfoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPowerType() != 0) {
            builder.setPowerType(this.getPowerType());
            fieldCnt++;
        }  else if (builder.hasPowerType()) {
            // 清理PowerType
            builder.clearPowerType();
            fieldCnt++;
        }
        if (this.getPower() != 0L) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }  else if (builder.hasPower()) {
            // 清理Power
            builder.clearPower();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PowerInfoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POWERTYPE)) {
            builder.setPowerType(this.getPowerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWER)) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PowerInfoUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPowerType()) {
            this.innerSetPowerType(proto.getPowerType());
        } else {
            this.innerSetPowerType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPower()) {
            this.innerSetPower(proto.getPower());
        } else {
            this.innerSetPower(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PowerInfoUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PowerInfoUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPowerType()) {
            this.setPowerType(proto.getPowerType());
            fieldCnt++;
        }
        if (proto.hasPower()) {
            this.setPower(proto.getPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PowerInfoUnit.Builder getCopySsBuilder() {
        final PowerInfoUnit.Builder builder = PowerInfoUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PowerInfoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getPowerType() != 0) {
            builder.setPowerType(this.getPowerType());
            fieldCnt++;
        }  else if (builder.hasPowerType()) {
            // 清理PowerType
            builder.clearPowerType();
            fieldCnt++;
        }
        if (this.getPower() != 0L) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }  else if (builder.hasPower()) {
            // 清理Power
            builder.clearPower();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PowerInfoUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_POWERTYPE)) {
            builder.setPowerType(this.getPowerType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POWER)) {
            builder.setPower(this.getPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PowerInfoUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPowerType()) {
            this.innerSetPowerType(proto.getPowerType());
        } else {
            this.innerSetPowerType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPower()) {
            this.innerSetPower(proto.getPower());
        } else {
            this.innerSetPower(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PowerInfoUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PowerInfoUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPowerType()) {
            this.setPowerType(proto.getPowerType());
            fieldCnt++;
        }
        if (proto.hasPower()) {
            this.setPower(proto.getPower());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PowerInfoUnit.Builder builder = PowerInfoUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.powerType;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PowerInfoUnitProp)) {
            return false;
        }
        final PowerInfoUnitProp otherNode = (PowerInfoUnitProp) node;
        if (this.powerType != otherNode.powerType) {
            return false;
        }
        if (this.power != otherNode.power) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
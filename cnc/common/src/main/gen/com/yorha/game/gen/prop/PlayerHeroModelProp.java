package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerHeroModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerHeroModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerHeroModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PLAYERHEROMAP = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32PlayerHeroMapProp playerHeroMap = null;

    public PlayerHeroModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerHeroModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerHeroMap
     *
     * @return playerHeroMap value
     */
    public Int32PlayerHeroMapProp getPlayerHeroMap() {
        if (this.playerHeroMap == null) {
            this.playerHeroMap = new Int32PlayerHeroMapProp(this, FIELD_INDEX_PLAYERHEROMAP);
        }
        return this.playerHeroMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPlayerHeroMapV(PlayerHeroProp v) {
        this.getPlayerHeroMap().put(v.getHeroId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerHeroProp addEmptyPlayerHeroMap(Integer k) {
        return this.getPlayerHeroMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPlayerHeroMapSize() {
        if (this.playerHeroMap == null) {
            return 0;
        }
        return this.playerHeroMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPlayerHeroMapEmpty() {
        if (this.playerHeroMap == null) {
            return true;
        }
        return this.playerHeroMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerHeroProp getPlayerHeroMapV(Integer k) {
        if (this.playerHeroMap == null || !this.playerHeroMap.containsKey(k)) {
            return null;
        }
        return this.playerHeroMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPlayerHeroMap() {
        if (this.playerHeroMap != null) {
            this.playerHeroMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePlayerHeroMapV(Integer k) {
        if (this.playerHeroMap != null) {
            this.playerHeroMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroModelPB.Builder getCopyCsBuilder() {
        final PlayerHeroModelPB.Builder builder = PlayerHeroModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerHeroModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerHeroMap != null) {
            PlayerPB.Int32PlayerHeroMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerHeroMapPB.newBuilder();
            final int tmpFieldCnt = this.playerHeroMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerHeroMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerHeroMap();
            }
        }  else if (builder.hasPlayerHeroMap()) {
            // 清理PlayerHeroMap
            builder.clearPlayerHeroMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerHeroModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMAP) && this.playerHeroMap != null) {
            final boolean needClear = !builder.hasPlayerHeroMap();
            final int tmpFieldCnt = this.playerHeroMap.copyChangeToCs(builder.getPlayerHeroMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerHeroModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMAP) && this.playerHeroMap != null) {
            final boolean needClear = !builder.hasPlayerHeroMap();
            final int tmpFieldCnt = this.playerHeroMap.copyChangeToAndClearDeleteKeysCs(builder.getPlayerHeroMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerHeroModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerHeroMap()) {
            this.getPlayerHeroMap().mergeFromCs(proto.getPlayerHeroMap());
        } else {
            if (this.playerHeroMap != null) {
                this.playerHeroMap.mergeFromCs(proto.getPlayerHeroMap());
            }
        }
        this.markAll();
        return PlayerHeroModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerHeroModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerHeroMap()) {
            this.getPlayerHeroMap().mergeChangeFromCs(proto.getPlayerHeroMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroModel.Builder getCopyDbBuilder() {
        final PlayerHeroModel.Builder builder = PlayerHeroModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerHeroModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerHeroMap != null) {
            Player.Int32PlayerHeroMap.Builder tmpBuilder = Player.Int32PlayerHeroMap.newBuilder();
            final int tmpFieldCnt = this.playerHeroMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerHeroMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerHeroMap();
            }
        }  else if (builder.hasPlayerHeroMap()) {
            // 清理PlayerHeroMap
            builder.clearPlayerHeroMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerHeroModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMAP) && this.playerHeroMap != null) {
            final boolean needClear = !builder.hasPlayerHeroMap();
            final int tmpFieldCnt = this.playerHeroMap.copyChangeToDb(builder.getPlayerHeroMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerHeroModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerHeroMap()) {
            this.getPlayerHeroMap().mergeFromDb(proto.getPlayerHeroMap());
        } else {
            if (this.playerHeroMap != null) {
                this.playerHeroMap.mergeFromDb(proto.getPlayerHeroMap());
            }
        }
        this.markAll();
        return PlayerHeroModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerHeroModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerHeroMap()) {
            this.getPlayerHeroMap().mergeChangeFromDb(proto.getPlayerHeroMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerHeroModel.Builder getCopySsBuilder() {
        final PlayerHeroModel.Builder builder = PlayerHeroModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerHeroModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerHeroMap != null) {
            Player.Int32PlayerHeroMap.Builder tmpBuilder = Player.Int32PlayerHeroMap.newBuilder();
            final int tmpFieldCnt = this.playerHeroMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerHeroMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerHeroMap();
            }
        }  else if (builder.hasPlayerHeroMap()) {
            // 清理PlayerHeroMap
            builder.clearPlayerHeroMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerHeroModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMAP) && this.playerHeroMap != null) {
            final boolean needClear = !builder.hasPlayerHeroMap();
            final int tmpFieldCnt = this.playerHeroMap.copyChangeToSs(builder.getPlayerHeroMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerHeroMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerHeroModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerHeroMap()) {
            this.getPlayerHeroMap().mergeFromSs(proto.getPlayerHeroMap());
        } else {
            if (this.playerHeroMap != null) {
                this.playerHeroMap.mergeFromSs(proto.getPlayerHeroMap());
            }
        }
        this.markAll();
        return PlayerHeroModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerHeroModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerHeroMap()) {
            this.getPlayerHeroMap().mergeChangeFromSs(proto.getPlayerHeroMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerHeroModel.Builder builder = PlayerHeroModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERHEROMAP) && this.playerHeroMap != null) {
            this.playerHeroMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.playerHeroMap != null) {
            this.playerHeroMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerHeroModelProp)) {
            return false;
        }
        final PlayerHeroModelProp otherNode = (PlayerHeroModelProp) node;
        if (!this.getPlayerHeroMap().compareDataTo(otherNode.getPlayerHeroMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanMiscModel;
import com.yorha.proto.ClanPB.ClanMiscModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanMiscModelProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_KILLSCORE = 0;
    public static final int FIELD_INDEX_OWNERBECOMETSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long killScore = Constant.DEFAULT_LONG_VALUE;
    private long ownerBecomeTsMs = Constant.DEFAULT_LONG_VALUE;

    public ClanMiscModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanMiscModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get killScore
     *
     * @return killScore value
     */
    public long getKillScore() {
        return this.killScore;
    }

    /**
     * set killScore && set marked
     *
     * @param killScore new value
     * @return current object
     */
    public ClanMiscModelProp setKillScore(long killScore) {
        if (this.killScore != killScore) {
            this.mark(FIELD_INDEX_KILLSCORE);
            this.killScore = killScore;
        }
        return this;
    }

    /**
     * inner set killScore
     *
     * @param killScore new value
     */
    private void innerSetKillScore(long killScore) {
        this.killScore = killScore;
    }

    /**
     * get ownerBecomeTsMs
     *
     * @return ownerBecomeTsMs value
     */
    public long getOwnerBecomeTsMs() {
        return this.ownerBecomeTsMs;
    }

    /**
     * set ownerBecomeTsMs && set marked
     *
     * @param ownerBecomeTsMs new value
     * @return current object
     */
    public ClanMiscModelProp setOwnerBecomeTsMs(long ownerBecomeTsMs) {
        if (this.ownerBecomeTsMs != ownerBecomeTsMs) {
            this.mark(FIELD_INDEX_OWNERBECOMETSMS);
            this.ownerBecomeTsMs = ownerBecomeTsMs;
        }
        return this;
    }

    /**
     * inner set ownerBecomeTsMs
     *
     * @param ownerBecomeTsMs new value
     */
    private void innerSetOwnerBecomeTsMs(long ownerBecomeTsMs) {
        this.ownerBecomeTsMs = ownerBecomeTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMiscModelPB.Builder getCopyCsBuilder() {
        final ClanMiscModelPB.Builder builder = ClanMiscModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanMiscModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.getOwnerBecomeTsMs() != 0L) {
            builder.setOwnerBecomeTsMs(this.getOwnerBecomeTsMs());
            fieldCnt++;
        }  else if (builder.hasOwnerBecomeTsMs()) {
            // 清理OwnerBecomeTsMs
            builder.clearOwnerBecomeTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanMiscModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERBECOMETSMS)) {
            builder.setOwnerBecomeTsMs(this.getOwnerBecomeTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanMiscModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERBECOMETSMS)) {
            builder.setOwnerBecomeTsMs(this.getOwnerBecomeTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanMiscModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerBecomeTsMs()) {
            this.innerSetOwnerBecomeTsMs(proto.getOwnerBecomeTsMs());
        } else {
            this.innerSetOwnerBecomeTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanMiscModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanMiscModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasOwnerBecomeTsMs()) {
            this.setOwnerBecomeTsMs(proto.getOwnerBecomeTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMiscModel.Builder getCopyDbBuilder() {
        final ClanMiscModel.Builder builder = ClanMiscModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanMiscModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.getOwnerBecomeTsMs() != 0L) {
            builder.setOwnerBecomeTsMs(this.getOwnerBecomeTsMs());
            fieldCnt++;
        }  else if (builder.hasOwnerBecomeTsMs()) {
            // 清理OwnerBecomeTsMs
            builder.clearOwnerBecomeTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanMiscModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERBECOMETSMS)) {
            builder.setOwnerBecomeTsMs(this.getOwnerBecomeTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanMiscModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerBecomeTsMs()) {
            this.innerSetOwnerBecomeTsMs(proto.getOwnerBecomeTsMs());
        } else {
            this.innerSetOwnerBecomeTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanMiscModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanMiscModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasOwnerBecomeTsMs()) {
            this.setOwnerBecomeTsMs(proto.getOwnerBecomeTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMiscModel.Builder getCopySsBuilder() {
        final ClanMiscModel.Builder builder = ClanMiscModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanMiscModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.getOwnerBecomeTsMs() != 0L) {
            builder.setOwnerBecomeTsMs(this.getOwnerBecomeTsMs());
            fieldCnt++;
        }  else if (builder.hasOwnerBecomeTsMs()) {
            // 清理OwnerBecomeTsMs
            builder.clearOwnerBecomeTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanMiscModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERBECOMETSMS)) {
            builder.setOwnerBecomeTsMs(this.getOwnerBecomeTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanMiscModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerBecomeTsMs()) {
            this.innerSetOwnerBecomeTsMs(proto.getOwnerBecomeTsMs());
        } else {
            this.innerSetOwnerBecomeTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanMiscModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanMiscModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasOwnerBecomeTsMs()) {
            this.setOwnerBecomeTsMs(proto.getOwnerBecomeTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanMiscModel.Builder builder = ClanMiscModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.killScore;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanMiscModelProp)) {
            return false;
        }
        final ClanMiscModelProp otherNode = (ClanMiscModelProp) node;
        if (this.killScore != otherNode.killScore) {
            return false;
        }
        if (this.ownerBecomeTsMs != otherNode.ownerBecomeTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
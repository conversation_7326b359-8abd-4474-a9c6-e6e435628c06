package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.MapBuilding.OccupyInfo;
import com.yorha.proto.MapBuildingPB.OccupyInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class OccupyInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_STATE = 0;
    public static final int FIELD_INDEX_STATESTARTTSMS = 1;
    public static final int FIELD_INDEX_STATEENDTSMS = 2;
    public static final int FIELD_INDEX_OWNERCLANID = 3;
    public static final int FIELD_INDEX_SHOWCLANSIMPLENAME = 4;
    public static final int FIELD_INDEX_SHOWCLANNAME = 5;
    public static final int FIELD_INDEX_OWNEROCCUPYTSMS = 6;
    public static final int FIELD_INDEX_OCCUPYNUM = 7;
    public static final int FIELD_INDEX_OCCUPYTSMS = 8;
    public static final int FIELD_INDEX_OCCUPYNUMCALCTSMS = 9;
    public static final int FIELD_INDEX_OCCUPYSPEED = 10;
    public static final int FIELD_INDEX_OCCUPYCLANID = 11;
    public static final int FIELD_INDEX_SHOWCOLOR = 12;
    public static final int FIELD_INDEX_FISRTOWNTSMS = 13;
    public static final int FIELD_INDEX_REBUILDNUMCALCTSMS = 14;
    public static final int FIELD_INDEX_REBUILDSPEED = 15;
    public static final int FIELD_INDEX_REBUILDNUM = 16;
    public static final int FIELD_INDEX_FILENUMCALCTSMS = 17;
    public static final int FIELD_INDEX_ALREADYFIRENUM = 18;
    public static final int FIELD_INDEX_WOULDOVERBURN = 19;
    public static final int FIELD_INDEX_REBUILDTOTALWORK = 20;
    public static final int FIELD_INDEX_MUSTBURNENDTSMS = 21;
    public static final int FIELD_INDEX_FLAGCOLOR = 22;
    public static final int FIELD_INDEX_FLAGSHADING = 23;
    public static final int FIELD_INDEX_FLAGSIGN = 24;
    public static final int FIELD_INDEX_LASTHITPLAYERNAME = 25;
    public static final int FIELD_INDEX_FIRESPEED = 26;
    public static final int FIELD_INDEX_TRIGGERFIRECLANID = 27;
    public static final int FIELD_INDEX_NATIONFLAGID = 28;
    public static final int FIELD_INDEX_ZONEID = 29;
    public static final int FIELD_INDEX_ZONECOLOR = 30;

    public static final int FIELD_COUNT = 31;

    private long markBits0 = 0L;

    private OccupyState state = OccupyState.forNumber(0);
    private long stateStartTsMs = Constant.DEFAULT_LONG_VALUE;
    private long stateEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private long ownerClanId = Constant.DEFAULT_LONG_VALUE;
    private String showClanSimpleName = Constant.DEFAULT_STR_VALUE;
    private String showClanName = Constant.DEFAULT_STR_VALUE;
    private long ownerOccupyTsMs = Constant.DEFAULT_LONG_VALUE;
    private int occupyNum = Constant.DEFAULT_INT_VALUE;
    private long occupyTsMs = Constant.DEFAULT_LONG_VALUE;
    private long occupyNumCalcTsMs = Constant.DEFAULT_LONG_VALUE;
    private int occupySpeed = Constant.DEFAULT_INT_VALUE;
    private long occupyClanId = Constant.DEFAULT_LONG_VALUE;
    private int showColor = Constant.DEFAULT_INT_VALUE;
    private long fisrtOwnTsMs = Constant.DEFAULT_LONG_VALUE;
    private long rebuildNumCalcTsMs = Constant.DEFAULT_LONG_VALUE;
    private int rebuildSpeed = Constant.DEFAULT_INT_VALUE;
    private int rebuildNum = Constant.DEFAULT_INT_VALUE;
    private long fileNumCalcTsMs = Constant.DEFAULT_LONG_VALUE;
    private int alreadyFireNum = Constant.DEFAULT_INT_VALUE;
    private boolean wouldOverBurn = Constant.DEFAULT_BOOLEAN_VALUE;
    private int rebuildTotalWork = Constant.DEFAULT_INT_VALUE;
    private long mustBurnEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private int flagColor = Constant.DEFAULT_INT_VALUE;
    private int flagShading = Constant.DEFAULT_INT_VALUE;
    private int flagSign = Constant.DEFAULT_INT_VALUE;
    private String lastHitPlayerName = Constant.DEFAULT_STR_VALUE;
    private int fireSpeed = Constant.DEFAULT_INT_VALUE;
    private long triggerFireClanId = Constant.DEFAULT_LONG_VALUE;
    private int nationFlagId = Constant.DEFAULT_INT_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;
    private int zoneColor = Constant.DEFAULT_INT_VALUE;

    public OccupyInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public OccupyInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get state
     *
     * @return state value
     */
    public OccupyState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public OccupyInfoProp setState(OccupyState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(OccupyState state) {
        this.state = state;
    }

    /**
     * get stateStartTsMs
     *
     * @return stateStartTsMs value
     */
    public long getStateStartTsMs() {
        return this.stateStartTsMs;
    }

    /**
     * set stateStartTsMs && set marked
     *
     * @param stateStartTsMs new value
     * @return current object
     */
    public OccupyInfoProp setStateStartTsMs(long stateStartTsMs) {
        if (this.stateStartTsMs != stateStartTsMs) {
            this.mark(FIELD_INDEX_STATESTARTTSMS);
            this.stateStartTsMs = stateStartTsMs;
        }
        return this;
    }

    /**
     * inner set stateStartTsMs
     *
     * @param stateStartTsMs new value
     */
    private void innerSetStateStartTsMs(long stateStartTsMs) {
        this.stateStartTsMs = stateStartTsMs;
    }

    /**
     * get stateEndTsMs
     *
     * @return stateEndTsMs value
     */
    public long getStateEndTsMs() {
        return this.stateEndTsMs;
    }

    /**
     * set stateEndTsMs && set marked
     *
     * @param stateEndTsMs new value
     * @return current object
     */
    public OccupyInfoProp setStateEndTsMs(long stateEndTsMs) {
        if (this.stateEndTsMs != stateEndTsMs) {
            this.mark(FIELD_INDEX_STATEENDTSMS);
            this.stateEndTsMs = stateEndTsMs;
        }
        return this;
    }

    /**
     * inner set stateEndTsMs
     *
     * @param stateEndTsMs new value
     */
    private void innerSetStateEndTsMs(long stateEndTsMs) {
        this.stateEndTsMs = stateEndTsMs;
    }

    /**
     * get ownerClanId
     *
     * @return ownerClanId value
     */
    public long getOwnerClanId() {
        return this.ownerClanId;
    }

    /**
     * set ownerClanId && set marked
     *
     * @param ownerClanId new value
     * @return current object
     */
    public OccupyInfoProp setOwnerClanId(long ownerClanId) {
        if (this.ownerClanId != ownerClanId) {
            this.mark(FIELD_INDEX_OWNERCLANID);
            this.ownerClanId = ownerClanId;
        }
        return this;
    }

    /**
     * inner set ownerClanId
     *
     * @param ownerClanId new value
     */
    private void innerSetOwnerClanId(long ownerClanId) {
        this.ownerClanId = ownerClanId;
    }

    /**
     * get showClanSimpleName
     *
     * @return showClanSimpleName value
     */
    public String getShowClanSimpleName() {
        return this.showClanSimpleName;
    }

    /**
     * set showClanSimpleName && set marked
     *
     * @param showClanSimpleName new value
     * @return current object
     */
    public OccupyInfoProp setShowClanSimpleName(String showClanSimpleName) {
        if (showClanSimpleName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanSimpleName, showClanSimpleName)) {
            this.mark(FIELD_INDEX_SHOWCLANSIMPLENAME);
            this.showClanSimpleName = showClanSimpleName;
        }
        return this;
    }

    /**
     * inner set showClanSimpleName
     *
     * @param showClanSimpleName new value
     */
    private void innerSetShowClanSimpleName(String showClanSimpleName) {
        this.showClanSimpleName = showClanSimpleName;
    }

    /**
     * get showClanName
     *
     * @return showClanName value
     */
    public String getShowClanName() {
        return this.showClanName;
    }

    /**
     * set showClanName && set marked
     *
     * @param showClanName new value
     * @return current object
     */
    public OccupyInfoProp setShowClanName(String showClanName) {
        if (showClanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanName, showClanName)) {
            this.mark(FIELD_INDEX_SHOWCLANNAME);
            this.showClanName = showClanName;
        }
        return this;
    }

    /**
     * inner set showClanName
     *
     * @param showClanName new value
     */
    private void innerSetShowClanName(String showClanName) {
        this.showClanName = showClanName;
    }

    /**
     * get ownerOccupyTsMs
     *
     * @return ownerOccupyTsMs value
     */
    public long getOwnerOccupyTsMs() {
        return this.ownerOccupyTsMs;
    }

    /**
     * set ownerOccupyTsMs && set marked
     *
     * @param ownerOccupyTsMs new value
     * @return current object
     */
    public OccupyInfoProp setOwnerOccupyTsMs(long ownerOccupyTsMs) {
        if (this.ownerOccupyTsMs != ownerOccupyTsMs) {
            this.mark(FIELD_INDEX_OWNEROCCUPYTSMS);
            this.ownerOccupyTsMs = ownerOccupyTsMs;
        }
        return this;
    }

    /**
     * inner set ownerOccupyTsMs
     *
     * @param ownerOccupyTsMs new value
     */
    private void innerSetOwnerOccupyTsMs(long ownerOccupyTsMs) {
        this.ownerOccupyTsMs = ownerOccupyTsMs;
    }

    /**
     * get occupyNum
     *
     * @return occupyNum value
     */
    public int getOccupyNum() {
        return this.occupyNum;
    }

    /**
     * set occupyNum && set marked
     *
     * @param occupyNum new value
     * @return current object
     */
    public OccupyInfoProp setOccupyNum(int occupyNum) {
        if (this.occupyNum != occupyNum) {
            this.mark(FIELD_INDEX_OCCUPYNUM);
            this.occupyNum = occupyNum;
        }
        return this;
    }

    /**
     * inner set occupyNum
     *
     * @param occupyNum new value
     */
    private void innerSetOccupyNum(int occupyNum) {
        this.occupyNum = occupyNum;
    }

    /**
     * get occupyTsMs
     *
     * @return occupyTsMs value
     */
    public long getOccupyTsMs() {
        return this.occupyTsMs;
    }

    /**
     * set occupyTsMs && set marked
     *
     * @param occupyTsMs new value
     * @return current object
     */
    public OccupyInfoProp setOccupyTsMs(long occupyTsMs) {
        if (this.occupyTsMs != occupyTsMs) {
            this.mark(FIELD_INDEX_OCCUPYTSMS);
            this.occupyTsMs = occupyTsMs;
        }
        return this;
    }

    /**
     * inner set occupyTsMs
     *
     * @param occupyTsMs new value
     */
    private void innerSetOccupyTsMs(long occupyTsMs) {
        this.occupyTsMs = occupyTsMs;
    }

    /**
     * get occupyNumCalcTsMs
     *
     * @return occupyNumCalcTsMs value
     */
    public long getOccupyNumCalcTsMs() {
        return this.occupyNumCalcTsMs;
    }

    /**
     * set occupyNumCalcTsMs && set marked
     *
     * @param occupyNumCalcTsMs new value
     * @return current object
     */
    public OccupyInfoProp setOccupyNumCalcTsMs(long occupyNumCalcTsMs) {
        if (this.occupyNumCalcTsMs != occupyNumCalcTsMs) {
            this.mark(FIELD_INDEX_OCCUPYNUMCALCTSMS);
            this.occupyNumCalcTsMs = occupyNumCalcTsMs;
        }
        return this;
    }

    /**
     * inner set occupyNumCalcTsMs
     *
     * @param occupyNumCalcTsMs new value
     */
    private void innerSetOccupyNumCalcTsMs(long occupyNumCalcTsMs) {
        this.occupyNumCalcTsMs = occupyNumCalcTsMs;
    }

    /**
     * get occupySpeed
     *
     * @return occupySpeed value
     */
    public int getOccupySpeed() {
        return this.occupySpeed;
    }

    /**
     * set occupySpeed && set marked
     *
     * @param occupySpeed new value
     * @return current object
     */
    public OccupyInfoProp setOccupySpeed(int occupySpeed) {
        if (this.occupySpeed != occupySpeed) {
            this.mark(FIELD_INDEX_OCCUPYSPEED);
            this.occupySpeed = occupySpeed;
        }
        return this;
    }

    /**
     * inner set occupySpeed
     *
     * @param occupySpeed new value
     */
    private void innerSetOccupySpeed(int occupySpeed) {
        this.occupySpeed = occupySpeed;
    }

    /**
     * get occupyClanId
     *
     * @return occupyClanId value
     */
    public long getOccupyClanId() {
        return this.occupyClanId;
    }

    /**
     * set occupyClanId && set marked
     *
     * @param occupyClanId new value
     * @return current object
     */
    public OccupyInfoProp setOccupyClanId(long occupyClanId) {
        if (this.occupyClanId != occupyClanId) {
            this.mark(FIELD_INDEX_OCCUPYCLANID);
            this.occupyClanId = occupyClanId;
        }
        return this;
    }

    /**
     * inner set occupyClanId
     *
     * @param occupyClanId new value
     */
    private void innerSetOccupyClanId(long occupyClanId) {
        this.occupyClanId = occupyClanId;
    }

    /**
     * get showColor
     *
     * @return showColor value
     */
    public int getShowColor() {
        return this.showColor;
    }

    /**
     * set showColor && set marked
     *
     * @param showColor new value
     * @return current object
     */
    public OccupyInfoProp setShowColor(int showColor) {
        if (this.showColor != showColor) {
            this.mark(FIELD_INDEX_SHOWCOLOR);
            this.showColor = showColor;
        }
        return this;
    }

    /**
     * inner set showColor
     *
     * @param showColor new value
     */
    private void innerSetShowColor(int showColor) {
        this.showColor = showColor;
    }

    /**
     * get fisrtOwnTsMs
     *
     * @return fisrtOwnTsMs value
     */
    public long getFisrtOwnTsMs() {
        return this.fisrtOwnTsMs;
    }

    /**
     * set fisrtOwnTsMs && set marked
     *
     * @param fisrtOwnTsMs new value
     * @return current object
     */
    public OccupyInfoProp setFisrtOwnTsMs(long fisrtOwnTsMs) {
        if (this.fisrtOwnTsMs != fisrtOwnTsMs) {
            this.mark(FIELD_INDEX_FISRTOWNTSMS);
            this.fisrtOwnTsMs = fisrtOwnTsMs;
        }
        return this;
    }

    /**
     * inner set fisrtOwnTsMs
     *
     * @param fisrtOwnTsMs new value
     */
    private void innerSetFisrtOwnTsMs(long fisrtOwnTsMs) {
        this.fisrtOwnTsMs = fisrtOwnTsMs;
    }

    /**
     * get rebuildNumCalcTsMs
     *
     * @return rebuildNumCalcTsMs value
     */
    public long getRebuildNumCalcTsMs() {
        return this.rebuildNumCalcTsMs;
    }

    /**
     * set rebuildNumCalcTsMs && set marked
     *
     * @param rebuildNumCalcTsMs new value
     * @return current object
     */
    public OccupyInfoProp setRebuildNumCalcTsMs(long rebuildNumCalcTsMs) {
        if (this.rebuildNumCalcTsMs != rebuildNumCalcTsMs) {
            this.mark(FIELD_INDEX_REBUILDNUMCALCTSMS);
            this.rebuildNumCalcTsMs = rebuildNumCalcTsMs;
        }
        return this;
    }

    /**
     * inner set rebuildNumCalcTsMs
     *
     * @param rebuildNumCalcTsMs new value
     */
    private void innerSetRebuildNumCalcTsMs(long rebuildNumCalcTsMs) {
        this.rebuildNumCalcTsMs = rebuildNumCalcTsMs;
    }

    /**
     * get rebuildSpeed
     *
     * @return rebuildSpeed value
     */
    public int getRebuildSpeed() {
        return this.rebuildSpeed;
    }

    /**
     * set rebuildSpeed && set marked
     *
     * @param rebuildSpeed new value
     * @return current object
     */
    public OccupyInfoProp setRebuildSpeed(int rebuildSpeed) {
        if (this.rebuildSpeed != rebuildSpeed) {
            this.mark(FIELD_INDEX_REBUILDSPEED);
            this.rebuildSpeed = rebuildSpeed;
        }
        return this;
    }

    /**
     * inner set rebuildSpeed
     *
     * @param rebuildSpeed new value
     */
    private void innerSetRebuildSpeed(int rebuildSpeed) {
        this.rebuildSpeed = rebuildSpeed;
    }

    /**
     * get rebuildNum
     *
     * @return rebuildNum value
     */
    public int getRebuildNum() {
        return this.rebuildNum;
    }

    /**
     * set rebuildNum && set marked
     *
     * @param rebuildNum new value
     * @return current object
     */
    public OccupyInfoProp setRebuildNum(int rebuildNum) {
        if (this.rebuildNum != rebuildNum) {
            this.mark(FIELD_INDEX_REBUILDNUM);
            this.rebuildNum = rebuildNum;
        }
        return this;
    }

    /**
     * inner set rebuildNum
     *
     * @param rebuildNum new value
     */
    private void innerSetRebuildNum(int rebuildNum) {
        this.rebuildNum = rebuildNum;
    }

    /**
     * get fileNumCalcTsMs
     *
     * @return fileNumCalcTsMs value
     */
    public long getFileNumCalcTsMs() {
        return this.fileNumCalcTsMs;
    }

    /**
     * set fileNumCalcTsMs && set marked
     *
     * @param fileNumCalcTsMs new value
     * @return current object
     */
    public OccupyInfoProp setFileNumCalcTsMs(long fileNumCalcTsMs) {
        if (this.fileNumCalcTsMs != fileNumCalcTsMs) {
            this.mark(FIELD_INDEX_FILENUMCALCTSMS);
            this.fileNumCalcTsMs = fileNumCalcTsMs;
        }
        return this;
    }

    /**
     * inner set fileNumCalcTsMs
     *
     * @param fileNumCalcTsMs new value
     */
    private void innerSetFileNumCalcTsMs(long fileNumCalcTsMs) {
        this.fileNumCalcTsMs = fileNumCalcTsMs;
    }

    /**
     * get alreadyFireNum
     *
     * @return alreadyFireNum value
     */
    public int getAlreadyFireNum() {
        return this.alreadyFireNum;
    }

    /**
     * set alreadyFireNum && set marked
     *
     * @param alreadyFireNum new value
     * @return current object
     */
    public OccupyInfoProp setAlreadyFireNum(int alreadyFireNum) {
        if (this.alreadyFireNum != alreadyFireNum) {
            this.mark(FIELD_INDEX_ALREADYFIRENUM);
            this.alreadyFireNum = alreadyFireNum;
        }
        return this;
    }

    /**
     * inner set alreadyFireNum
     *
     * @param alreadyFireNum new value
     */
    private void innerSetAlreadyFireNum(int alreadyFireNum) {
        this.alreadyFireNum = alreadyFireNum;
    }

    /**
     * get wouldOverBurn
     *
     * @return wouldOverBurn value
     */
    public boolean getWouldOverBurn() {
        return this.wouldOverBurn;
    }

    /**
     * set wouldOverBurn && set marked
     *
     * @param wouldOverBurn new value
     * @return current object
     */
    public OccupyInfoProp setWouldOverBurn(boolean wouldOverBurn) {
        if (this.wouldOverBurn != wouldOverBurn) {
            this.mark(FIELD_INDEX_WOULDOVERBURN);
            this.wouldOverBurn = wouldOverBurn;
        }
        return this;
    }

    /**
     * inner set wouldOverBurn
     *
     * @param wouldOverBurn new value
     */
    private void innerSetWouldOverBurn(boolean wouldOverBurn) {
        this.wouldOverBurn = wouldOverBurn;
    }

    /**
     * get rebuildTotalWork
     *
     * @return rebuildTotalWork value
     */
    public int getRebuildTotalWork() {
        return this.rebuildTotalWork;
    }

    /**
     * set rebuildTotalWork && set marked
     *
     * @param rebuildTotalWork new value
     * @return current object
     */
    public OccupyInfoProp setRebuildTotalWork(int rebuildTotalWork) {
        if (this.rebuildTotalWork != rebuildTotalWork) {
            this.mark(FIELD_INDEX_REBUILDTOTALWORK);
            this.rebuildTotalWork = rebuildTotalWork;
        }
        return this;
    }

    /**
     * inner set rebuildTotalWork
     *
     * @param rebuildTotalWork new value
     */
    private void innerSetRebuildTotalWork(int rebuildTotalWork) {
        this.rebuildTotalWork = rebuildTotalWork;
    }

    /**
     * get mustBurnEndTsMs
     *
     * @return mustBurnEndTsMs value
     */
    public long getMustBurnEndTsMs() {
        return this.mustBurnEndTsMs;
    }

    /**
     * set mustBurnEndTsMs && set marked
     *
     * @param mustBurnEndTsMs new value
     * @return current object
     */
    public OccupyInfoProp setMustBurnEndTsMs(long mustBurnEndTsMs) {
        if (this.mustBurnEndTsMs != mustBurnEndTsMs) {
            this.mark(FIELD_INDEX_MUSTBURNENDTSMS);
            this.mustBurnEndTsMs = mustBurnEndTsMs;
        }
        return this;
    }

    /**
     * inner set mustBurnEndTsMs
     *
     * @param mustBurnEndTsMs new value
     */
    private void innerSetMustBurnEndTsMs(long mustBurnEndTsMs) {
        this.mustBurnEndTsMs = mustBurnEndTsMs;
    }

    /**
     * get flagColor
     *
     * @return flagColor value
     */
    public int getFlagColor() {
        return this.flagColor;
    }

    /**
     * set flagColor && set marked
     *
     * @param flagColor new value
     * @return current object
     */
    public OccupyInfoProp setFlagColor(int flagColor) {
        if (this.flagColor != flagColor) {
            this.mark(FIELD_INDEX_FLAGCOLOR);
            this.flagColor = flagColor;
        }
        return this;
    }

    /**
     * inner set flagColor
     *
     * @param flagColor new value
     */
    private void innerSetFlagColor(int flagColor) {
        this.flagColor = flagColor;
    }

    /**
     * get flagShading
     *
     * @return flagShading value
     */
    public int getFlagShading() {
        return this.flagShading;
    }

    /**
     * set flagShading && set marked
     *
     * @param flagShading new value
     * @return current object
     */
    public OccupyInfoProp setFlagShading(int flagShading) {
        if (this.flagShading != flagShading) {
            this.mark(FIELD_INDEX_FLAGSHADING);
            this.flagShading = flagShading;
        }
        return this;
    }

    /**
     * inner set flagShading
     *
     * @param flagShading new value
     */
    private void innerSetFlagShading(int flagShading) {
        this.flagShading = flagShading;
    }

    /**
     * get flagSign
     *
     * @return flagSign value
     */
    public int getFlagSign() {
        return this.flagSign;
    }

    /**
     * set flagSign && set marked
     *
     * @param flagSign new value
     * @return current object
     */
    public OccupyInfoProp setFlagSign(int flagSign) {
        if (this.flagSign != flagSign) {
            this.mark(FIELD_INDEX_FLAGSIGN);
            this.flagSign = flagSign;
        }
        return this;
    }

    /**
     * inner set flagSign
     *
     * @param flagSign new value
     */
    private void innerSetFlagSign(int flagSign) {
        this.flagSign = flagSign;
    }

    /**
     * get lastHitPlayerName
     *
     * @return lastHitPlayerName value
     */
    public String getLastHitPlayerName() {
        return this.lastHitPlayerName;
    }

    /**
     * set lastHitPlayerName && set marked
     *
     * @param lastHitPlayerName new value
     * @return current object
     */
    public OccupyInfoProp setLastHitPlayerName(String lastHitPlayerName) {
        if (lastHitPlayerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.lastHitPlayerName, lastHitPlayerName)) {
            this.mark(FIELD_INDEX_LASTHITPLAYERNAME);
            this.lastHitPlayerName = lastHitPlayerName;
        }
        return this;
    }

    /**
     * inner set lastHitPlayerName
     *
     * @param lastHitPlayerName new value
     */
    private void innerSetLastHitPlayerName(String lastHitPlayerName) {
        this.lastHitPlayerName = lastHitPlayerName;
    }

    /**
     * get fireSpeed
     *
     * @return fireSpeed value
     */
    public int getFireSpeed() {
        return this.fireSpeed;
    }

    /**
     * set fireSpeed && set marked
     *
     * @param fireSpeed new value
     * @return current object
     */
    public OccupyInfoProp setFireSpeed(int fireSpeed) {
        if (this.fireSpeed != fireSpeed) {
            this.mark(FIELD_INDEX_FIRESPEED);
            this.fireSpeed = fireSpeed;
        }
        return this;
    }

    /**
     * inner set fireSpeed
     *
     * @param fireSpeed new value
     */
    private void innerSetFireSpeed(int fireSpeed) {
        this.fireSpeed = fireSpeed;
    }

    /**
     * get triggerFireClanId
     *
     * @return triggerFireClanId value
     */
    public long getTriggerFireClanId() {
        return this.triggerFireClanId;
    }

    /**
     * set triggerFireClanId && set marked
     *
     * @param triggerFireClanId new value
     * @return current object
     */
    public OccupyInfoProp setTriggerFireClanId(long triggerFireClanId) {
        if (this.triggerFireClanId != triggerFireClanId) {
            this.mark(FIELD_INDEX_TRIGGERFIRECLANID);
            this.triggerFireClanId = triggerFireClanId;
        }
        return this;
    }

    /**
     * inner set triggerFireClanId
     *
     * @param triggerFireClanId new value
     */
    private void innerSetTriggerFireClanId(long triggerFireClanId) {
        this.triggerFireClanId = triggerFireClanId;
    }

    /**
     * get nationFlagId
     *
     * @return nationFlagId value
     */
    public int getNationFlagId() {
        return this.nationFlagId;
    }

    /**
     * set nationFlagId && set marked
     *
     * @param nationFlagId new value
     * @return current object
     */
    public OccupyInfoProp setNationFlagId(int nationFlagId) {
        if (this.nationFlagId != nationFlagId) {
            this.mark(FIELD_INDEX_NATIONFLAGID);
            this.nationFlagId = nationFlagId;
        }
        return this;
    }

    /**
     * inner set nationFlagId
     *
     * @param nationFlagId new value
     */
    private void innerSetNationFlagId(int nationFlagId) {
        this.nationFlagId = nationFlagId;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public OccupyInfoProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }

    /**
     * get zoneColor
     *
     * @return zoneColor value
     */
    public int getZoneColor() {
        return this.zoneColor;
    }

    /**
     * set zoneColor && set marked
     *
     * @param zoneColor new value
     * @return current object
     */
    public OccupyInfoProp setZoneColor(int zoneColor) {
        if (this.zoneColor != zoneColor) {
            this.mark(FIELD_INDEX_ZONECOLOR);
            this.zoneColor = zoneColor;
        }
        return this;
    }

    /**
     * inner set zoneColor
     *
     * @param zoneColor new value
     */
    private void innerSetZoneColor(int zoneColor) {
        this.zoneColor = zoneColor;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public OccupyInfoPB.Builder getCopyCsBuilder() {
        final OccupyInfoPB.Builder builder = OccupyInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(OccupyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getState() != OccupyState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getStateStartTsMs() != 0L) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStateStartTsMs()) {
            // 清理StateStartTsMs
            builder.clearStateStartTsMs();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getOwnerClanId() != 0L) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }  else if (builder.hasOwnerClanId()) {
            // 清理OwnerClanId
            builder.clearOwnerClanId();
            fieldCnt++;
        }
        if (!this.getShowClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasShowClanSimpleName()) {
            // 清理ShowClanSimpleName
            builder.clearShowClanSimpleName();
            fieldCnt++;
        }
        if (!this.getShowClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }  else if (builder.hasShowClanName()) {
            // 清理ShowClanName
            builder.clearShowClanName();
            fieldCnt++;
        }
        if (this.getOccupyNum() != 0) {
            builder.setOccupyNum(this.getOccupyNum());
            fieldCnt++;
        }  else if (builder.hasOccupyNum()) {
            // 清理OccupyNum
            builder.clearOccupyNum();
            fieldCnt++;
        }
        if (this.getOccupyNumCalcTsMs() != 0L) {
            builder.setOccupyNumCalcTsMs(this.getOccupyNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasOccupyNumCalcTsMs()) {
            // 清理OccupyNumCalcTsMs
            builder.clearOccupyNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getOccupySpeed() != 0) {
            builder.setOccupySpeed(this.getOccupySpeed());
            fieldCnt++;
        }  else if (builder.hasOccupySpeed()) {
            // 清理OccupySpeed
            builder.clearOccupySpeed();
            fieldCnt++;
        }
        if (this.getOccupyClanId() != 0L) {
            builder.setOccupyClanId(this.getOccupyClanId());
            fieldCnt++;
        }  else if (builder.hasOccupyClanId()) {
            // 清理OccupyClanId
            builder.clearOccupyClanId();
            fieldCnt++;
        }
        if (this.getShowColor() != 0) {
            builder.setShowColor(this.getShowColor());
            fieldCnt++;
        }  else if (builder.hasShowColor()) {
            // 清理ShowColor
            builder.clearShowColor();
            fieldCnt++;
        }
        if (this.getFisrtOwnTsMs() != 0L) {
            builder.setFisrtOwnTsMs(this.getFisrtOwnTsMs());
            fieldCnt++;
        }  else if (builder.hasFisrtOwnTsMs()) {
            // 清理FisrtOwnTsMs
            builder.clearFisrtOwnTsMs();
            fieldCnt++;
        }
        if (this.getRebuildNumCalcTsMs() != 0L) {
            builder.setRebuildNumCalcTsMs(this.getRebuildNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasRebuildNumCalcTsMs()) {
            // 清理RebuildNumCalcTsMs
            builder.clearRebuildNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getRebuildSpeed() != 0) {
            builder.setRebuildSpeed(this.getRebuildSpeed());
            fieldCnt++;
        }  else if (builder.hasRebuildSpeed()) {
            // 清理RebuildSpeed
            builder.clearRebuildSpeed();
            fieldCnt++;
        }
        if (this.getRebuildNum() != 0) {
            builder.setRebuildNum(this.getRebuildNum());
            fieldCnt++;
        }  else if (builder.hasRebuildNum()) {
            // 清理RebuildNum
            builder.clearRebuildNum();
            fieldCnt++;
        }
        if (this.getFileNumCalcTsMs() != 0L) {
            builder.setFileNumCalcTsMs(this.getFileNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasFileNumCalcTsMs()) {
            // 清理FileNumCalcTsMs
            builder.clearFileNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getAlreadyFireNum() != 0) {
            builder.setAlreadyFireNum(this.getAlreadyFireNum());
            fieldCnt++;
        }  else if (builder.hasAlreadyFireNum()) {
            // 清理AlreadyFireNum
            builder.clearAlreadyFireNum();
            fieldCnt++;
        }
        if (this.getWouldOverBurn()) {
            builder.setWouldOverBurn(this.getWouldOverBurn());
            fieldCnt++;
        }  else if (builder.hasWouldOverBurn()) {
            // 清理WouldOverBurn
            builder.clearWouldOverBurn();
            fieldCnt++;
        }
        if (this.getRebuildTotalWork() != 0) {
            builder.setRebuildTotalWork(this.getRebuildTotalWork());
            fieldCnt++;
        }  else if (builder.hasRebuildTotalWork()) {
            // 清理RebuildTotalWork
            builder.clearRebuildTotalWork();
            fieldCnt++;
        }
        if (this.getMustBurnEndTsMs() != 0L) {
            builder.setMustBurnEndTsMs(this.getMustBurnEndTsMs());
            fieldCnt++;
        }  else if (builder.hasMustBurnEndTsMs()) {
            // 清理MustBurnEndTsMs
            builder.clearMustBurnEndTsMs();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getFireSpeed() != 0) {
            builder.setFireSpeed(this.getFireSpeed());
            fieldCnt++;
        }  else if (builder.hasFireSpeed()) {
            // 清理FireSpeed
            builder.clearFireSpeed();
            fieldCnt++;
        }
        if (this.getTriggerFireClanId() != 0L) {
            builder.setTriggerFireClanId(this.getTriggerFireClanId());
            fieldCnt++;
        }  else if (builder.hasTriggerFireClanId()) {
            // 清理TriggerFireClanId
            builder.clearTriggerFireClanId();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getZoneColor() != 0) {
            builder.setZoneColor(this.getZoneColor());
            fieldCnt++;
        }  else if (builder.hasZoneColor()) {
            // 清理ZoneColor
            builder.clearZoneColor();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(OccupyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUM)) {
            builder.setOccupyNum(this.getOccupyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUMCALCTSMS)) {
            builder.setOccupyNumCalcTsMs(this.getOccupyNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYSPEED)) {
            builder.setOccupySpeed(this.getOccupySpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYCLANID)) {
            builder.setOccupyClanId(this.getOccupyClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCOLOR)) {
            builder.setShowColor(this.getShowColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FISRTOWNTSMS)) {
            builder.setFisrtOwnTsMs(this.getFisrtOwnTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUMCALCTSMS)) {
            builder.setRebuildNumCalcTsMs(this.getRebuildNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDSPEED)) {
            builder.setRebuildSpeed(this.getRebuildSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUM)) {
            builder.setRebuildNum(this.getRebuildNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FILENUMCALCTSMS)) {
            builder.setFileNumCalcTsMs(this.getFileNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYFIRENUM)) {
            builder.setAlreadyFireNum(this.getAlreadyFireNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WOULDOVERBURN)) {
            builder.setWouldOverBurn(this.getWouldOverBurn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDTOTALWORK)) {
            builder.setRebuildTotalWork(this.getRebuildTotalWork());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MUSTBURNENDTSMS)) {
            builder.setMustBurnEndTsMs(this.getMustBurnEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRESPEED)) {
            builder.setFireSpeed(this.getFireSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERFIRECLANID)) {
            builder.setTriggerFireClanId(this.getTriggerFireClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONECOLOR)) {
            builder.setZoneColor(this.getZoneColor());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(OccupyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUM)) {
            builder.setOccupyNum(this.getOccupyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUMCALCTSMS)) {
            builder.setOccupyNumCalcTsMs(this.getOccupyNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYSPEED)) {
            builder.setOccupySpeed(this.getOccupySpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYCLANID)) {
            builder.setOccupyClanId(this.getOccupyClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCOLOR)) {
            builder.setShowColor(this.getShowColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FISRTOWNTSMS)) {
            builder.setFisrtOwnTsMs(this.getFisrtOwnTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUMCALCTSMS)) {
            builder.setRebuildNumCalcTsMs(this.getRebuildNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDSPEED)) {
            builder.setRebuildSpeed(this.getRebuildSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUM)) {
            builder.setRebuildNum(this.getRebuildNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FILENUMCALCTSMS)) {
            builder.setFileNumCalcTsMs(this.getFileNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYFIRENUM)) {
            builder.setAlreadyFireNum(this.getAlreadyFireNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WOULDOVERBURN)) {
            builder.setWouldOverBurn(this.getWouldOverBurn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDTOTALWORK)) {
            builder.setRebuildTotalWork(this.getRebuildTotalWork());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MUSTBURNENDTSMS)) {
            builder.setMustBurnEndTsMs(this.getMustBurnEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRESPEED)) {
            builder.setFireSpeed(this.getFireSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERFIRECLANID)) {
            builder.setTriggerFireClanId(this.getTriggerFireClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONECOLOR)) {
            builder.setZoneColor(this.getZoneColor());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(OccupyInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(OccupyState.forNumber(0));
        }
        if (proto.hasStateStartTsMs()) {
            this.innerSetStateStartTsMs(proto.getStateStartTsMs());
        } else {
            this.innerSetStateStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerClanId()) {
            this.innerSetOwnerClanId(proto.getOwnerClanId());
        } else {
            this.innerSetOwnerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowClanSimpleName()) {
            this.innerSetShowClanSimpleName(proto.getShowClanSimpleName());
        } else {
            this.innerSetShowClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasShowClanName()) {
            this.innerSetShowClanName(proto.getShowClanName());
        } else {
            this.innerSetShowClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasOccupyNum()) {
            this.innerSetOccupyNum(proto.getOccupyNum());
        } else {
            this.innerSetOccupyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOccupyNumCalcTsMs()) {
            this.innerSetOccupyNumCalcTsMs(proto.getOccupyNumCalcTsMs());
        } else {
            this.innerSetOccupyNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOccupySpeed()) {
            this.innerSetOccupySpeed(proto.getOccupySpeed());
        } else {
            this.innerSetOccupySpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOccupyClanId()) {
            this.innerSetOccupyClanId(proto.getOccupyClanId());
        } else {
            this.innerSetOccupyClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowColor()) {
            this.innerSetShowColor(proto.getShowColor());
        } else {
            this.innerSetShowColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFisrtOwnTsMs()) {
            this.innerSetFisrtOwnTsMs(proto.getFisrtOwnTsMs());
        } else {
            this.innerSetFisrtOwnTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRebuildNumCalcTsMs()) {
            this.innerSetRebuildNumCalcTsMs(proto.getRebuildNumCalcTsMs());
        } else {
            this.innerSetRebuildNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRebuildSpeed()) {
            this.innerSetRebuildSpeed(proto.getRebuildSpeed());
        } else {
            this.innerSetRebuildSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRebuildNum()) {
            this.innerSetRebuildNum(proto.getRebuildNum());
        } else {
            this.innerSetRebuildNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFileNumCalcTsMs()) {
            this.innerSetFileNumCalcTsMs(proto.getFileNumCalcTsMs());
        } else {
            this.innerSetFileNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAlreadyFireNum()) {
            this.innerSetAlreadyFireNum(proto.getAlreadyFireNum());
        } else {
            this.innerSetAlreadyFireNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasWouldOverBurn()) {
            this.innerSetWouldOverBurn(proto.getWouldOverBurn());
        } else {
            this.innerSetWouldOverBurn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRebuildTotalWork()) {
            this.innerSetRebuildTotalWork(proto.getRebuildTotalWork());
        } else {
            this.innerSetRebuildTotalWork(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMustBurnEndTsMs()) {
            this.innerSetMustBurnEndTsMs(proto.getMustBurnEndTsMs());
        } else {
            this.innerSetMustBurnEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFireSpeed()) {
            this.innerSetFireSpeed(proto.getFireSpeed());
        } else {
            this.innerSetFireSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerFireClanId()) {
            this.innerSetTriggerFireClanId(proto.getTriggerFireClanId());
        } else {
            this.innerSetTriggerFireClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneColor()) {
            this.innerSetZoneColor(proto.getZoneColor());
        } else {
            this.innerSetZoneColor(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return OccupyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(OccupyInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasStateStartTsMs()) {
            this.setStateStartTsMs(proto.getStateStartTsMs());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasOwnerClanId()) {
            this.setOwnerClanId(proto.getOwnerClanId());
            fieldCnt++;
        }
        if (proto.hasShowClanSimpleName()) {
            this.setShowClanSimpleName(proto.getShowClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasShowClanName()) {
            this.setShowClanName(proto.getShowClanName());
            fieldCnt++;
        }
        if (proto.hasOccupyNum()) {
            this.setOccupyNum(proto.getOccupyNum());
            fieldCnt++;
        }
        if (proto.hasOccupyNumCalcTsMs()) {
            this.setOccupyNumCalcTsMs(proto.getOccupyNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasOccupySpeed()) {
            this.setOccupySpeed(proto.getOccupySpeed());
            fieldCnt++;
        }
        if (proto.hasOccupyClanId()) {
            this.setOccupyClanId(proto.getOccupyClanId());
            fieldCnt++;
        }
        if (proto.hasShowColor()) {
            this.setShowColor(proto.getShowColor());
            fieldCnt++;
        }
        if (proto.hasFisrtOwnTsMs()) {
            this.setFisrtOwnTsMs(proto.getFisrtOwnTsMs());
            fieldCnt++;
        }
        if (proto.hasRebuildNumCalcTsMs()) {
            this.setRebuildNumCalcTsMs(proto.getRebuildNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasRebuildSpeed()) {
            this.setRebuildSpeed(proto.getRebuildSpeed());
            fieldCnt++;
        }
        if (proto.hasRebuildNum()) {
            this.setRebuildNum(proto.getRebuildNum());
            fieldCnt++;
        }
        if (proto.hasFileNumCalcTsMs()) {
            this.setFileNumCalcTsMs(proto.getFileNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasAlreadyFireNum()) {
            this.setAlreadyFireNum(proto.getAlreadyFireNum());
            fieldCnt++;
        }
        if (proto.hasWouldOverBurn()) {
            this.setWouldOverBurn(proto.getWouldOverBurn());
            fieldCnt++;
        }
        if (proto.hasRebuildTotalWork()) {
            this.setRebuildTotalWork(proto.getRebuildTotalWork());
            fieldCnt++;
        }
        if (proto.hasMustBurnEndTsMs()) {
            this.setMustBurnEndTsMs(proto.getMustBurnEndTsMs());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasFireSpeed()) {
            this.setFireSpeed(proto.getFireSpeed());
            fieldCnt++;
        }
        if (proto.hasTriggerFireClanId()) {
            this.setTriggerFireClanId(proto.getTriggerFireClanId());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasZoneColor()) {
            this.setZoneColor(proto.getZoneColor());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public OccupyInfo.Builder getCopyDbBuilder() {
        final OccupyInfo.Builder builder = OccupyInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(OccupyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getState() != OccupyState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getStateStartTsMs() != 0L) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStateStartTsMs()) {
            // 清理StateStartTsMs
            builder.clearStateStartTsMs();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getOwnerClanId() != 0L) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }  else if (builder.hasOwnerClanId()) {
            // 清理OwnerClanId
            builder.clearOwnerClanId();
            fieldCnt++;
        }
        if (!this.getShowClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasShowClanSimpleName()) {
            // 清理ShowClanSimpleName
            builder.clearShowClanSimpleName();
            fieldCnt++;
        }
        if (!this.getShowClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }  else if (builder.hasShowClanName()) {
            // 清理ShowClanName
            builder.clearShowClanName();
            fieldCnt++;
        }
        if (this.getOwnerOccupyTsMs() != 0L) {
            builder.setOwnerOccupyTsMs(this.getOwnerOccupyTsMs());
            fieldCnt++;
        }  else if (builder.hasOwnerOccupyTsMs()) {
            // 清理OwnerOccupyTsMs
            builder.clearOwnerOccupyTsMs();
            fieldCnt++;
        }
        if (this.getOccupyNum() != 0) {
            builder.setOccupyNum(this.getOccupyNum());
            fieldCnt++;
        }  else if (builder.hasOccupyNum()) {
            // 清理OccupyNum
            builder.clearOccupyNum();
            fieldCnt++;
        }
        if (this.getOccupyTsMs() != 0L) {
            builder.setOccupyTsMs(this.getOccupyTsMs());
            fieldCnt++;
        }  else if (builder.hasOccupyTsMs()) {
            // 清理OccupyTsMs
            builder.clearOccupyTsMs();
            fieldCnt++;
        }
        if (this.getOccupyNumCalcTsMs() != 0L) {
            builder.setOccupyNumCalcTsMs(this.getOccupyNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasOccupyNumCalcTsMs()) {
            // 清理OccupyNumCalcTsMs
            builder.clearOccupyNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getOccupySpeed() != 0) {
            builder.setOccupySpeed(this.getOccupySpeed());
            fieldCnt++;
        }  else if (builder.hasOccupySpeed()) {
            // 清理OccupySpeed
            builder.clearOccupySpeed();
            fieldCnt++;
        }
        if (this.getOccupyClanId() != 0L) {
            builder.setOccupyClanId(this.getOccupyClanId());
            fieldCnt++;
        }  else if (builder.hasOccupyClanId()) {
            // 清理OccupyClanId
            builder.clearOccupyClanId();
            fieldCnt++;
        }
        if (this.getShowColor() != 0) {
            builder.setShowColor(this.getShowColor());
            fieldCnt++;
        }  else if (builder.hasShowColor()) {
            // 清理ShowColor
            builder.clearShowColor();
            fieldCnt++;
        }
        if (this.getFisrtOwnTsMs() != 0L) {
            builder.setFisrtOwnTsMs(this.getFisrtOwnTsMs());
            fieldCnt++;
        }  else if (builder.hasFisrtOwnTsMs()) {
            // 清理FisrtOwnTsMs
            builder.clearFisrtOwnTsMs();
            fieldCnt++;
        }
        if (this.getRebuildNumCalcTsMs() != 0L) {
            builder.setRebuildNumCalcTsMs(this.getRebuildNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasRebuildNumCalcTsMs()) {
            // 清理RebuildNumCalcTsMs
            builder.clearRebuildNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getRebuildSpeed() != 0) {
            builder.setRebuildSpeed(this.getRebuildSpeed());
            fieldCnt++;
        }  else if (builder.hasRebuildSpeed()) {
            // 清理RebuildSpeed
            builder.clearRebuildSpeed();
            fieldCnt++;
        }
        if (this.getRebuildNum() != 0) {
            builder.setRebuildNum(this.getRebuildNum());
            fieldCnt++;
        }  else if (builder.hasRebuildNum()) {
            // 清理RebuildNum
            builder.clearRebuildNum();
            fieldCnt++;
        }
        if (this.getFileNumCalcTsMs() != 0L) {
            builder.setFileNumCalcTsMs(this.getFileNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasFileNumCalcTsMs()) {
            // 清理FileNumCalcTsMs
            builder.clearFileNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getAlreadyFireNum() != 0) {
            builder.setAlreadyFireNum(this.getAlreadyFireNum());
            fieldCnt++;
        }  else if (builder.hasAlreadyFireNum()) {
            // 清理AlreadyFireNum
            builder.clearAlreadyFireNum();
            fieldCnt++;
        }
        if (this.getWouldOverBurn()) {
            builder.setWouldOverBurn(this.getWouldOverBurn());
            fieldCnt++;
        }  else if (builder.hasWouldOverBurn()) {
            // 清理WouldOverBurn
            builder.clearWouldOverBurn();
            fieldCnt++;
        }
        if (this.getRebuildTotalWork() != 0) {
            builder.setRebuildTotalWork(this.getRebuildTotalWork());
            fieldCnt++;
        }  else if (builder.hasRebuildTotalWork()) {
            // 清理RebuildTotalWork
            builder.clearRebuildTotalWork();
            fieldCnt++;
        }
        if (this.getMustBurnEndTsMs() != 0L) {
            builder.setMustBurnEndTsMs(this.getMustBurnEndTsMs());
            fieldCnt++;
        }  else if (builder.hasMustBurnEndTsMs()) {
            // 清理MustBurnEndTsMs
            builder.clearMustBurnEndTsMs();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (!this.getLastHitPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setLastHitPlayerName(this.getLastHitPlayerName());
            fieldCnt++;
        }  else if (builder.hasLastHitPlayerName()) {
            // 清理LastHitPlayerName
            builder.clearLastHitPlayerName();
            fieldCnt++;
        }
        if (this.getFireSpeed() != 0) {
            builder.setFireSpeed(this.getFireSpeed());
            fieldCnt++;
        }  else if (builder.hasFireSpeed()) {
            // 清理FireSpeed
            builder.clearFireSpeed();
            fieldCnt++;
        }
        if (this.getTriggerFireClanId() != 0L) {
            builder.setTriggerFireClanId(this.getTriggerFireClanId());
            fieldCnt++;
        }  else if (builder.hasTriggerFireClanId()) {
            // 清理TriggerFireClanId
            builder.clearTriggerFireClanId();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getZoneColor() != 0) {
            builder.setZoneColor(this.getZoneColor());
            fieldCnt++;
        }  else if (builder.hasZoneColor()) {
            // 清理ZoneColor
            builder.clearZoneColor();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(OccupyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNEROCCUPYTSMS)) {
            builder.setOwnerOccupyTsMs(this.getOwnerOccupyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUM)) {
            builder.setOccupyNum(this.getOccupyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYTSMS)) {
            builder.setOccupyTsMs(this.getOccupyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUMCALCTSMS)) {
            builder.setOccupyNumCalcTsMs(this.getOccupyNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYSPEED)) {
            builder.setOccupySpeed(this.getOccupySpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYCLANID)) {
            builder.setOccupyClanId(this.getOccupyClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCOLOR)) {
            builder.setShowColor(this.getShowColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FISRTOWNTSMS)) {
            builder.setFisrtOwnTsMs(this.getFisrtOwnTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUMCALCTSMS)) {
            builder.setRebuildNumCalcTsMs(this.getRebuildNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDSPEED)) {
            builder.setRebuildSpeed(this.getRebuildSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUM)) {
            builder.setRebuildNum(this.getRebuildNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FILENUMCALCTSMS)) {
            builder.setFileNumCalcTsMs(this.getFileNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYFIRENUM)) {
            builder.setAlreadyFireNum(this.getAlreadyFireNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WOULDOVERBURN)) {
            builder.setWouldOverBurn(this.getWouldOverBurn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDTOTALWORK)) {
            builder.setRebuildTotalWork(this.getRebuildTotalWork());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MUSTBURNENDTSMS)) {
            builder.setMustBurnEndTsMs(this.getMustBurnEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTHITPLAYERNAME)) {
            builder.setLastHitPlayerName(this.getLastHitPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRESPEED)) {
            builder.setFireSpeed(this.getFireSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERFIRECLANID)) {
            builder.setTriggerFireClanId(this.getTriggerFireClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONECOLOR)) {
            builder.setZoneColor(this.getZoneColor());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(OccupyInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(OccupyState.forNumber(0));
        }
        if (proto.hasStateStartTsMs()) {
            this.innerSetStateStartTsMs(proto.getStateStartTsMs());
        } else {
            this.innerSetStateStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerClanId()) {
            this.innerSetOwnerClanId(proto.getOwnerClanId());
        } else {
            this.innerSetOwnerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowClanSimpleName()) {
            this.innerSetShowClanSimpleName(proto.getShowClanSimpleName());
        } else {
            this.innerSetShowClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasShowClanName()) {
            this.innerSetShowClanName(proto.getShowClanName());
        } else {
            this.innerSetShowClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasOwnerOccupyTsMs()) {
            this.innerSetOwnerOccupyTsMs(proto.getOwnerOccupyTsMs());
        } else {
            this.innerSetOwnerOccupyTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOccupyNum()) {
            this.innerSetOccupyNum(proto.getOccupyNum());
        } else {
            this.innerSetOccupyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOccupyTsMs()) {
            this.innerSetOccupyTsMs(proto.getOccupyTsMs());
        } else {
            this.innerSetOccupyTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOccupyNumCalcTsMs()) {
            this.innerSetOccupyNumCalcTsMs(proto.getOccupyNumCalcTsMs());
        } else {
            this.innerSetOccupyNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOccupySpeed()) {
            this.innerSetOccupySpeed(proto.getOccupySpeed());
        } else {
            this.innerSetOccupySpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOccupyClanId()) {
            this.innerSetOccupyClanId(proto.getOccupyClanId());
        } else {
            this.innerSetOccupyClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowColor()) {
            this.innerSetShowColor(proto.getShowColor());
        } else {
            this.innerSetShowColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFisrtOwnTsMs()) {
            this.innerSetFisrtOwnTsMs(proto.getFisrtOwnTsMs());
        } else {
            this.innerSetFisrtOwnTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRebuildNumCalcTsMs()) {
            this.innerSetRebuildNumCalcTsMs(proto.getRebuildNumCalcTsMs());
        } else {
            this.innerSetRebuildNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRebuildSpeed()) {
            this.innerSetRebuildSpeed(proto.getRebuildSpeed());
        } else {
            this.innerSetRebuildSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRebuildNum()) {
            this.innerSetRebuildNum(proto.getRebuildNum());
        } else {
            this.innerSetRebuildNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFileNumCalcTsMs()) {
            this.innerSetFileNumCalcTsMs(proto.getFileNumCalcTsMs());
        } else {
            this.innerSetFileNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAlreadyFireNum()) {
            this.innerSetAlreadyFireNum(proto.getAlreadyFireNum());
        } else {
            this.innerSetAlreadyFireNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasWouldOverBurn()) {
            this.innerSetWouldOverBurn(proto.getWouldOverBurn());
        } else {
            this.innerSetWouldOverBurn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRebuildTotalWork()) {
            this.innerSetRebuildTotalWork(proto.getRebuildTotalWork());
        } else {
            this.innerSetRebuildTotalWork(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMustBurnEndTsMs()) {
            this.innerSetMustBurnEndTsMs(proto.getMustBurnEndTsMs());
        } else {
            this.innerSetMustBurnEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastHitPlayerName()) {
            this.innerSetLastHitPlayerName(proto.getLastHitPlayerName());
        } else {
            this.innerSetLastHitPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasFireSpeed()) {
            this.innerSetFireSpeed(proto.getFireSpeed());
        } else {
            this.innerSetFireSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerFireClanId()) {
            this.innerSetTriggerFireClanId(proto.getTriggerFireClanId());
        } else {
            this.innerSetTriggerFireClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneColor()) {
            this.innerSetZoneColor(proto.getZoneColor());
        } else {
            this.innerSetZoneColor(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return OccupyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(OccupyInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasStateStartTsMs()) {
            this.setStateStartTsMs(proto.getStateStartTsMs());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasOwnerClanId()) {
            this.setOwnerClanId(proto.getOwnerClanId());
            fieldCnt++;
        }
        if (proto.hasShowClanSimpleName()) {
            this.setShowClanSimpleName(proto.getShowClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasShowClanName()) {
            this.setShowClanName(proto.getShowClanName());
            fieldCnt++;
        }
        if (proto.hasOwnerOccupyTsMs()) {
            this.setOwnerOccupyTsMs(proto.getOwnerOccupyTsMs());
            fieldCnt++;
        }
        if (proto.hasOccupyNum()) {
            this.setOccupyNum(proto.getOccupyNum());
            fieldCnt++;
        }
        if (proto.hasOccupyTsMs()) {
            this.setOccupyTsMs(proto.getOccupyTsMs());
            fieldCnt++;
        }
        if (proto.hasOccupyNumCalcTsMs()) {
            this.setOccupyNumCalcTsMs(proto.getOccupyNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasOccupySpeed()) {
            this.setOccupySpeed(proto.getOccupySpeed());
            fieldCnt++;
        }
        if (proto.hasOccupyClanId()) {
            this.setOccupyClanId(proto.getOccupyClanId());
            fieldCnt++;
        }
        if (proto.hasShowColor()) {
            this.setShowColor(proto.getShowColor());
            fieldCnt++;
        }
        if (proto.hasFisrtOwnTsMs()) {
            this.setFisrtOwnTsMs(proto.getFisrtOwnTsMs());
            fieldCnt++;
        }
        if (proto.hasRebuildNumCalcTsMs()) {
            this.setRebuildNumCalcTsMs(proto.getRebuildNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasRebuildSpeed()) {
            this.setRebuildSpeed(proto.getRebuildSpeed());
            fieldCnt++;
        }
        if (proto.hasRebuildNum()) {
            this.setRebuildNum(proto.getRebuildNum());
            fieldCnt++;
        }
        if (proto.hasFileNumCalcTsMs()) {
            this.setFileNumCalcTsMs(proto.getFileNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasAlreadyFireNum()) {
            this.setAlreadyFireNum(proto.getAlreadyFireNum());
            fieldCnt++;
        }
        if (proto.hasWouldOverBurn()) {
            this.setWouldOverBurn(proto.getWouldOverBurn());
            fieldCnt++;
        }
        if (proto.hasRebuildTotalWork()) {
            this.setRebuildTotalWork(proto.getRebuildTotalWork());
            fieldCnt++;
        }
        if (proto.hasMustBurnEndTsMs()) {
            this.setMustBurnEndTsMs(proto.getMustBurnEndTsMs());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasLastHitPlayerName()) {
            this.setLastHitPlayerName(proto.getLastHitPlayerName());
            fieldCnt++;
        }
        if (proto.hasFireSpeed()) {
            this.setFireSpeed(proto.getFireSpeed());
            fieldCnt++;
        }
        if (proto.hasTriggerFireClanId()) {
            this.setTriggerFireClanId(proto.getTriggerFireClanId());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasZoneColor()) {
            this.setZoneColor(proto.getZoneColor());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public OccupyInfo.Builder getCopySsBuilder() {
        final OccupyInfo.Builder builder = OccupyInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(OccupyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getState() != OccupyState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (this.getStateStartTsMs() != 0L) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }  else if (builder.hasStateStartTsMs()) {
            // 清理StateStartTsMs
            builder.clearStateStartTsMs();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getOwnerClanId() != 0L) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }  else if (builder.hasOwnerClanId()) {
            // 清理OwnerClanId
            builder.clearOwnerClanId();
            fieldCnt++;
        }
        if (!this.getShowClanSimpleName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }  else if (builder.hasShowClanSimpleName()) {
            // 清理ShowClanSimpleName
            builder.clearShowClanSimpleName();
            fieldCnt++;
        }
        if (!this.getShowClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }  else if (builder.hasShowClanName()) {
            // 清理ShowClanName
            builder.clearShowClanName();
            fieldCnt++;
        }
        if (this.getOwnerOccupyTsMs() != 0L) {
            builder.setOwnerOccupyTsMs(this.getOwnerOccupyTsMs());
            fieldCnt++;
        }  else if (builder.hasOwnerOccupyTsMs()) {
            // 清理OwnerOccupyTsMs
            builder.clearOwnerOccupyTsMs();
            fieldCnt++;
        }
        if (this.getOccupyNum() != 0) {
            builder.setOccupyNum(this.getOccupyNum());
            fieldCnt++;
        }  else if (builder.hasOccupyNum()) {
            // 清理OccupyNum
            builder.clearOccupyNum();
            fieldCnt++;
        }
        if (this.getOccupyTsMs() != 0L) {
            builder.setOccupyTsMs(this.getOccupyTsMs());
            fieldCnt++;
        }  else if (builder.hasOccupyTsMs()) {
            // 清理OccupyTsMs
            builder.clearOccupyTsMs();
            fieldCnt++;
        }
        if (this.getOccupyNumCalcTsMs() != 0L) {
            builder.setOccupyNumCalcTsMs(this.getOccupyNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasOccupyNumCalcTsMs()) {
            // 清理OccupyNumCalcTsMs
            builder.clearOccupyNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getOccupySpeed() != 0) {
            builder.setOccupySpeed(this.getOccupySpeed());
            fieldCnt++;
        }  else if (builder.hasOccupySpeed()) {
            // 清理OccupySpeed
            builder.clearOccupySpeed();
            fieldCnt++;
        }
        if (this.getOccupyClanId() != 0L) {
            builder.setOccupyClanId(this.getOccupyClanId());
            fieldCnt++;
        }  else if (builder.hasOccupyClanId()) {
            // 清理OccupyClanId
            builder.clearOccupyClanId();
            fieldCnt++;
        }
        if (this.getShowColor() != 0) {
            builder.setShowColor(this.getShowColor());
            fieldCnt++;
        }  else if (builder.hasShowColor()) {
            // 清理ShowColor
            builder.clearShowColor();
            fieldCnt++;
        }
        if (this.getFisrtOwnTsMs() != 0L) {
            builder.setFisrtOwnTsMs(this.getFisrtOwnTsMs());
            fieldCnt++;
        }  else if (builder.hasFisrtOwnTsMs()) {
            // 清理FisrtOwnTsMs
            builder.clearFisrtOwnTsMs();
            fieldCnt++;
        }
        if (this.getRebuildNumCalcTsMs() != 0L) {
            builder.setRebuildNumCalcTsMs(this.getRebuildNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasRebuildNumCalcTsMs()) {
            // 清理RebuildNumCalcTsMs
            builder.clearRebuildNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getRebuildSpeed() != 0) {
            builder.setRebuildSpeed(this.getRebuildSpeed());
            fieldCnt++;
        }  else if (builder.hasRebuildSpeed()) {
            // 清理RebuildSpeed
            builder.clearRebuildSpeed();
            fieldCnt++;
        }
        if (this.getRebuildNum() != 0) {
            builder.setRebuildNum(this.getRebuildNum());
            fieldCnt++;
        }  else if (builder.hasRebuildNum()) {
            // 清理RebuildNum
            builder.clearRebuildNum();
            fieldCnt++;
        }
        if (this.getFileNumCalcTsMs() != 0L) {
            builder.setFileNumCalcTsMs(this.getFileNumCalcTsMs());
            fieldCnt++;
        }  else if (builder.hasFileNumCalcTsMs()) {
            // 清理FileNumCalcTsMs
            builder.clearFileNumCalcTsMs();
            fieldCnt++;
        }
        if (this.getAlreadyFireNum() != 0) {
            builder.setAlreadyFireNum(this.getAlreadyFireNum());
            fieldCnt++;
        }  else if (builder.hasAlreadyFireNum()) {
            // 清理AlreadyFireNum
            builder.clearAlreadyFireNum();
            fieldCnt++;
        }
        if (this.getWouldOverBurn()) {
            builder.setWouldOverBurn(this.getWouldOverBurn());
            fieldCnt++;
        }  else if (builder.hasWouldOverBurn()) {
            // 清理WouldOverBurn
            builder.clearWouldOverBurn();
            fieldCnt++;
        }
        if (this.getRebuildTotalWork() != 0) {
            builder.setRebuildTotalWork(this.getRebuildTotalWork());
            fieldCnt++;
        }  else if (builder.hasRebuildTotalWork()) {
            // 清理RebuildTotalWork
            builder.clearRebuildTotalWork();
            fieldCnt++;
        }
        if (this.getMustBurnEndTsMs() != 0L) {
            builder.setMustBurnEndTsMs(this.getMustBurnEndTsMs());
            fieldCnt++;
        }  else if (builder.hasMustBurnEndTsMs()) {
            // 清理MustBurnEndTsMs
            builder.clearMustBurnEndTsMs();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (!this.getLastHitPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setLastHitPlayerName(this.getLastHitPlayerName());
            fieldCnt++;
        }  else if (builder.hasLastHitPlayerName()) {
            // 清理LastHitPlayerName
            builder.clearLastHitPlayerName();
            fieldCnt++;
        }
        if (this.getFireSpeed() != 0) {
            builder.setFireSpeed(this.getFireSpeed());
            fieldCnt++;
        }  else if (builder.hasFireSpeed()) {
            // 清理FireSpeed
            builder.clearFireSpeed();
            fieldCnt++;
        }
        if (this.getTriggerFireClanId() != 0L) {
            builder.setTriggerFireClanId(this.getTriggerFireClanId());
            fieldCnt++;
        }  else if (builder.hasTriggerFireClanId()) {
            // 清理TriggerFireClanId
            builder.clearTriggerFireClanId();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        if (this.getZoneColor() != 0) {
            builder.setZoneColor(this.getZoneColor());
            fieldCnt++;
        }  else if (builder.hasZoneColor()) {
            // 清理ZoneColor
            builder.clearZoneColor();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(OccupyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTSMS)) {
            builder.setStateStartTsMs(this.getStateStartTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERCLANID)) {
            builder.setOwnerClanId(this.getOwnerClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANSIMPLENAME)) {
            builder.setShowClanSimpleName(this.getShowClanSimpleName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCLANNAME)) {
            builder.setShowClanName(this.getShowClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNEROCCUPYTSMS)) {
            builder.setOwnerOccupyTsMs(this.getOwnerOccupyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUM)) {
            builder.setOccupyNum(this.getOccupyNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYTSMS)) {
            builder.setOccupyTsMs(this.getOccupyTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYNUMCALCTSMS)) {
            builder.setOccupyNumCalcTsMs(this.getOccupyNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYSPEED)) {
            builder.setOccupySpeed(this.getOccupySpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYCLANID)) {
            builder.setOccupyClanId(this.getOccupyClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWCOLOR)) {
            builder.setShowColor(this.getShowColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FISRTOWNTSMS)) {
            builder.setFisrtOwnTsMs(this.getFisrtOwnTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUMCALCTSMS)) {
            builder.setRebuildNumCalcTsMs(this.getRebuildNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDSPEED)) {
            builder.setRebuildSpeed(this.getRebuildSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDNUM)) {
            builder.setRebuildNum(this.getRebuildNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FILENUMCALCTSMS)) {
            builder.setFileNumCalcTsMs(this.getFileNumCalcTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYFIRENUM)) {
            builder.setAlreadyFireNum(this.getAlreadyFireNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WOULDOVERBURN)) {
            builder.setWouldOverBurn(this.getWouldOverBurn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REBUILDTOTALWORK)) {
            builder.setRebuildTotalWork(this.getRebuildTotalWork());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MUSTBURNENDTSMS)) {
            builder.setMustBurnEndTsMs(this.getMustBurnEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTHITPLAYERNAME)) {
            builder.setLastHitPlayerName(this.getLastHitPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FIRESPEED)) {
            builder.setFireSpeed(this.getFireSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERFIRECLANID)) {
            builder.setTriggerFireClanId(this.getTriggerFireClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONECOLOR)) {
            builder.setZoneColor(this.getZoneColor());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(OccupyInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(OccupyState.forNumber(0));
        }
        if (proto.hasStateStartTsMs()) {
            this.innerSetStateStartTsMs(proto.getStateStartTsMs());
        } else {
            this.innerSetStateStartTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerClanId()) {
            this.innerSetOwnerClanId(proto.getOwnerClanId());
        } else {
            this.innerSetOwnerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowClanSimpleName()) {
            this.innerSetShowClanSimpleName(proto.getShowClanSimpleName());
        } else {
            this.innerSetShowClanSimpleName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasShowClanName()) {
            this.innerSetShowClanName(proto.getShowClanName());
        } else {
            this.innerSetShowClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasOwnerOccupyTsMs()) {
            this.innerSetOwnerOccupyTsMs(proto.getOwnerOccupyTsMs());
        } else {
            this.innerSetOwnerOccupyTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOccupyNum()) {
            this.innerSetOccupyNum(proto.getOccupyNum());
        } else {
            this.innerSetOccupyNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOccupyTsMs()) {
            this.innerSetOccupyTsMs(proto.getOccupyTsMs());
        } else {
            this.innerSetOccupyTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOccupyNumCalcTsMs()) {
            this.innerSetOccupyNumCalcTsMs(proto.getOccupyNumCalcTsMs());
        } else {
            this.innerSetOccupyNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOccupySpeed()) {
            this.innerSetOccupySpeed(proto.getOccupySpeed());
        } else {
            this.innerSetOccupySpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOccupyClanId()) {
            this.innerSetOccupyClanId(proto.getOccupyClanId());
        } else {
            this.innerSetOccupyClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowColor()) {
            this.innerSetShowColor(proto.getShowColor());
        } else {
            this.innerSetShowColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFisrtOwnTsMs()) {
            this.innerSetFisrtOwnTsMs(proto.getFisrtOwnTsMs());
        } else {
            this.innerSetFisrtOwnTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRebuildNumCalcTsMs()) {
            this.innerSetRebuildNumCalcTsMs(proto.getRebuildNumCalcTsMs());
        } else {
            this.innerSetRebuildNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRebuildSpeed()) {
            this.innerSetRebuildSpeed(proto.getRebuildSpeed());
        } else {
            this.innerSetRebuildSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRebuildNum()) {
            this.innerSetRebuildNum(proto.getRebuildNum());
        } else {
            this.innerSetRebuildNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFileNumCalcTsMs()) {
            this.innerSetFileNumCalcTsMs(proto.getFileNumCalcTsMs());
        } else {
            this.innerSetFileNumCalcTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAlreadyFireNum()) {
            this.innerSetAlreadyFireNum(proto.getAlreadyFireNum());
        } else {
            this.innerSetAlreadyFireNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasWouldOverBurn()) {
            this.innerSetWouldOverBurn(proto.getWouldOverBurn());
        } else {
            this.innerSetWouldOverBurn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRebuildTotalWork()) {
            this.innerSetRebuildTotalWork(proto.getRebuildTotalWork());
        } else {
            this.innerSetRebuildTotalWork(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMustBurnEndTsMs()) {
            this.innerSetMustBurnEndTsMs(proto.getMustBurnEndTsMs());
        } else {
            this.innerSetMustBurnEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastHitPlayerName()) {
            this.innerSetLastHitPlayerName(proto.getLastHitPlayerName());
        } else {
            this.innerSetLastHitPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasFireSpeed()) {
            this.innerSetFireSpeed(proto.getFireSpeed());
        } else {
            this.innerSetFireSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerFireClanId()) {
            this.innerSetTriggerFireClanId(proto.getTriggerFireClanId());
        } else {
            this.innerSetTriggerFireClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneColor()) {
            this.innerSetZoneColor(proto.getZoneColor());
        } else {
            this.innerSetZoneColor(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return OccupyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(OccupyInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasStateStartTsMs()) {
            this.setStateStartTsMs(proto.getStateStartTsMs());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasOwnerClanId()) {
            this.setOwnerClanId(proto.getOwnerClanId());
            fieldCnt++;
        }
        if (proto.hasShowClanSimpleName()) {
            this.setShowClanSimpleName(proto.getShowClanSimpleName());
            fieldCnt++;
        }
        if (proto.hasShowClanName()) {
            this.setShowClanName(proto.getShowClanName());
            fieldCnt++;
        }
        if (proto.hasOwnerOccupyTsMs()) {
            this.setOwnerOccupyTsMs(proto.getOwnerOccupyTsMs());
            fieldCnt++;
        }
        if (proto.hasOccupyNum()) {
            this.setOccupyNum(proto.getOccupyNum());
            fieldCnt++;
        }
        if (proto.hasOccupyTsMs()) {
            this.setOccupyTsMs(proto.getOccupyTsMs());
            fieldCnt++;
        }
        if (proto.hasOccupyNumCalcTsMs()) {
            this.setOccupyNumCalcTsMs(proto.getOccupyNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasOccupySpeed()) {
            this.setOccupySpeed(proto.getOccupySpeed());
            fieldCnt++;
        }
        if (proto.hasOccupyClanId()) {
            this.setOccupyClanId(proto.getOccupyClanId());
            fieldCnt++;
        }
        if (proto.hasShowColor()) {
            this.setShowColor(proto.getShowColor());
            fieldCnt++;
        }
        if (proto.hasFisrtOwnTsMs()) {
            this.setFisrtOwnTsMs(proto.getFisrtOwnTsMs());
            fieldCnt++;
        }
        if (proto.hasRebuildNumCalcTsMs()) {
            this.setRebuildNumCalcTsMs(proto.getRebuildNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasRebuildSpeed()) {
            this.setRebuildSpeed(proto.getRebuildSpeed());
            fieldCnt++;
        }
        if (proto.hasRebuildNum()) {
            this.setRebuildNum(proto.getRebuildNum());
            fieldCnt++;
        }
        if (proto.hasFileNumCalcTsMs()) {
            this.setFileNumCalcTsMs(proto.getFileNumCalcTsMs());
            fieldCnt++;
        }
        if (proto.hasAlreadyFireNum()) {
            this.setAlreadyFireNum(proto.getAlreadyFireNum());
            fieldCnt++;
        }
        if (proto.hasWouldOverBurn()) {
            this.setWouldOverBurn(proto.getWouldOverBurn());
            fieldCnt++;
        }
        if (proto.hasRebuildTotalWork()) {
            this.setRebuildTotalWork(proto.getRebuildTotalWork());
            fieldCnt++;
        }
        if (proto.hasMustBurnEndTsMs()) {
            this.setMustBurnEndTsMs(proto.getMustBurnEndTsMs());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasLastHitPlayerName()) {
            this.setLastHitPlayerName(proto.getLastHitPlayerName());
            fieldCnt++;
        }
        if (proto.hasFireSpeed()) {
            this.setFireSpeed(proto.getFireSpeed());
            fieldCnt++;
        }
        if (proto.hasTriggerFireClanId()) {
            this.setTriggerFireClanId(proto.getTriggerFireClanId());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        if (proto.hasZoneColor()) {
            this.setZoneColor(proto.getZoneColor());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        OccupyInfo.Builder builder = OccupyInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof OccupyInfoProp)) {
            return false;
        }
        final OccupyInfoProp otherNode = (OccupyInfoProp) node;
        if (this.state != otherNode.state) {
            return false;
        }
        if (this.stateStartTsMs != otherNode.stateStartTsMs) {
            return false;
        }
        if (this.stateEndTsMs != otherNode.stateEndTsMs) {
            return false;
        }
        if (this.ownerClanId != otherNode.ownerClanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanSimpleName, otherNode.showClanSimpleName)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.showClanName, otherNode.showClanName)) {
            return false;
        }
        if (this.ownerOccupyTsMs != otherNode.ownerOccupyTsMs) {
            return false;
        }
        if (this.occupyNum != otherNode.occupyNum) {
            return false;
        }
        if (this.occupyTsMs != otherNode.occupyTsMs) {
            return false;
        }
        if (this.occupyNumCalcTsMs != otherNode.occupyNumCalcTsMs) {
            return false;
        }
        if (this.occupySpeed != otherNode.occupySpeed) {
            return false;
        }
        if (this.occupyClanId != otherNode.occupyClanId) {
            return false;
        }
        if (this.showColor != otherNode.showColor) {
            return false;
        }
        if (this.fisrtOwnTsMs != otherNode.fisrtOwnTsMs) {
            return false;
        }
        if (this.rebuildNumCalcTsMs != otherNode.rebuildNumCalcTsMs) {
            return false;
        }
        if (this.rebuildSpeed != otherNode.rebuildSpeed) {
            return false;
        }
        if (this.rebuildNum != otherNode.rebuildNum) {
            return false;
        }
        if (this.fileNumCalcTsMs != otherNode.fileNumCalcTsMs) {
            return false;
        }
        if (this.alreadyFireNum != otherNode.alreadyFireNum) {
            return false;
        }
        if (this.wouldOverBurn != otherNode.wouldOverBurn) {
            return false;
        }
        if (this.rebuildTotalWork != otherNode.rebuildTotalWork) {
            return false;
        }
        if (this.mustBurnEndTsMs != otherNode.mustBurnEndTsMs) {
            return false;
        }
        if (this.flagColor != otherNode.flagColor) {
            return false;
        }
        if (this.flagShading != otherNode.flagShading) {
            return false;
        }
        if (this.flagSign != otherNode.flagSign) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.lastHitPlayerName, otherNode.lastHitPlayerName)) {
            return false;
        }
        if (this.fireSpeed != otherNode.fireSpeed) {
            return false;
        }
        if (this.triggerFireClanId != otherNode.triggerFireClanId) {
            return false;
        }
        if (this.nationFlagId != otherNode.nationFlagId) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        if (this.zoneColor != otherNode.zoneColor) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 33;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.DiscountInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.DiscountInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class DiscountInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LASTREFRESHTIME = 0;
    public static final int FIELD_INDEX_REFRESHTIMES = 1;
    public static final int FIELD_INDEX_FREETIMES = 2;
    public static final int FIELD_INDEX_GOODSTORE = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long lastRefreshTime = Constant.DEFAULT_LONG_VALUE;
    private int refreshTimes = Constant.DEFAULT_INT_VALUE;
    private int freeTimes = Constant.DEFAULT_INT_VALUE;
    private Int32GoodInfoMapProp goodStore = null;

    public DiscountInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DiscountInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get lastRefreshTime
     *
     * @return lastRefreshTime value
     */
    public long getLastRefreshTime() {
        return this.lastRefreshTime;
    }

    /**
     * set lastRefreshTime && set marked
     *
     * @param lastRefreshTime new value
     * @return current object
     */
    public DiscountInfoProp setLastRefreshTime(long lastRefreshTime) {
        if (this.lastRefreshTime != lastRefreshTime) {
            this.mark(FIELD_INDEX_LASTREFRESHTIME);
            this.lastRefreshTime = lastRefreshTime;
        }
        return this;
    }

    /**
     * inner set lastRefreshTime
     *
     * @param lastRefreshTime new value
     */
    private void innerSetLastRefreshTime(long lastRefreshTime) {
        this.lastRefreshTime = lastRefreshTime;
    }

    /**
     * get refreshTimes
     *
     * @return refreshTimes value
     */
    public int getRefreshTimes() {
        return this.refreshTimes;
    }

    /**
     * set refreshTimes && set marked
     *
     * @param refreshTimes new value
     * @return current object
     */
    public DiscountInfoProp setRefreshTimes(int refreshTimes) {
        if (this.refreshTimes != refreshTimes) {
            this.mark(FIELD_INDEX_REFRESHTIMES);
            this.refreshTimes = refreshTimes;
        }
        return this;
    }

    /**
     * inner set refreshTimes
     *
     * @param refreshTimes new value
     */
    private void innerSetRefreshTimes(int refreshTimes) {
        this.refreshTimes = refreshTimes;
    }

    /**
     * get freeTimes
     *
     * @return freeTimes value
     */
    public int getFreeTimes() {
        return this.freeTimes;
    }

    /**
     * set freeTimes && set marked
     *
     * @param freeTimes new value
     * @return current object
     */
    public DiscountInfoProp setFreeTimes(int freeTimes) {
        if (this.freeTimes != freeTimes) {
            this.mark(FIELD_INDEX_FREETIMES);
            this.freeTimes = freeTimes;
        }
        return this;
    }

    /**
     * inner set freeTimes
     *
     * @param freeTimes new value
     */
    private void innerSetFreeTimes(int freeTimes) {
        this.freeTimes = freeTimes;
    }

    /**
     * get goodStore
     *
     * @return goodStore value
     */
    public Int32GoodInfoMapProp getGoodStore() {
        if (this.goodStore == null) {
            this.goodStore = new Int32GoodInfoMapProp(this, FIELD_INDEX_GOODSTORE);
        }
        return this.goodStore;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putGoodStoreV(GoodInfoProp v) {
        this.getGoodStore().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public GoodInfoProp addEmptyGoodStore(Integer k) {
        return this.getGoodStore().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getGoodStoreSize() {
        if (this.goodStore == null) {
            return 0;
        }
        return this.goodStore.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isGoodStoreEmpty() {
        if (this.goodStore == null) {
            return true;
        }
        return this.goodStore.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public GoodInfoProp getGoodStoreV(Integer k) {
        if (this.goodStore == null || !this.goodStore.containsKey(k)) {
            return null;
        }
        return this.goodStore.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearGoodStore() {
        if (this.goodStore != null) {
            this.goodStore.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeGoodStoreV(Integer k) {
        if (this.goodStore != null) {
            this.goodStore.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DiscountInfoPB.Builder getCopyCsBuilder() {
        final DiscountInfoPB.Builder builder = DiscountInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DiscountInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastRefreshTime() != 0L) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTime()) {
            // 清理LastRefreshTime
            builder.clearLastRefreshTime();
            fieldCnt++;
        }
        if (this.getRefreshTimes() != 0) {
            builder.setRefreshTimes(this.getRefreshTimes());
            fieldCnt++;
        }  else if (builder.hasRefreshTimes()) {
            // 清理RefreshTimes
            builder.clearRefreshTimes();
            fieldCnt++;
        }
        if (this.getFreeTimes() != 0) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }  else if (builder.hasFreeTimes()) {
            // 清理FreeTimes
            builder.clearFreeTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DiscountInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REFRESHTIMES)) {
            builder.setRefreshTimes(this.getRefreshTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DiscountInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REFRESHTIMES)) {
            builder.setRefreshTimes(this.getRefreshTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DiscountInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastRefreshTime()) {
            this.innerSetLastRefreshTime(proto.getLastRefreshTime());
        } else {
            this.innerSetLastRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRefreshTimes()) {
            this.innerSetRefreshTimes(proto.getRefreshTimes());
        } else {
            this.innerSetRefreshTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeTimes()) {
            this.innerSetFreeTimes(proto.getFreeTimes());
        } else {
            this.innerSetFreeTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return DiscountInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DiscountInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastRefreshTime()) {
            this.setLastRefreshTime(proto.getLastRefreshTime());
            fieldCnt++;
        }
        if (proto.hasRefreshTimes()) {
            this.setRefreshTimes(proto.getRefreshTimes());
            fieldCnt++;
        }
        if (proto.hasFreeTimes()) {
            this.setFreeTimes(proto.getFreeTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DiscountInfo.Builder getCopyDbBuilder() {
        final DiscountInfo.Builder builder = DiscountInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DiscountInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastRefreshTime() != 0L) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTime()) {
            // 清理LastRefreshTime
            builder.clearLastRefreshTime();
            fieldCnt++;
        }
        if (this.getRefreshTimes() != 0) {
            builder.setRefreshTimes(this.getRefreshTimes());
            fieldCnt++;
        }  else if (builder.hasRefreshTimes()) {
            // 清理RefreshTimes
            builder.clearRefreshTimes();
            fieldCnt++;
        }
        if (this.getFreeTimes() != 0) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }  else if (builder.hasFreeTimes()) {
            // 清理FreeTimes
            builder.clearFreeTimes();
            fieldCnt++;
        }
        if (this.goodStore != null) {
            Struct.Int32GoodInfoMap.Builder tmpBuilder = Struct.Int32GoodInfoMap.newBuilder();
            final int tmpFieldCnt = this.goodStore.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodStore();
            }
        }  else if (builder.hasGoodStore()) {
            // 清理GoodStore
            builder.clearGoodStore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DiscountInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REFRESHTIMES)) {
            builder.setRefreshTimes(this.getRefreshTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSTORE) && this.goodStore != null) {
            final boolean needClear = !builder.hasGoodStore();
            final int tmpFieldCnt = this.goodStore.copyChangeToDb(builder.getGoodStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodStore();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DiscountInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastRefreshTime()) {
            this.innerSetLastRefreshTime(proto.getLastRefreshTime());
        } else {
            this.innerSetLastRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRefreshTimes()) {
            this.innerSetRefreshTimes(proto.getRefreshTimes());
        } else {
            this.innerSetRefreshTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeTimes()) {
            this.innerSetFreeTimes(proto.getFreeTimes());
        } else {
            this.innerSetFreeTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGoodStore()) {
            this.getGoodStore().mergeFromDb(proto.getGoodStore());
        } else {
            if (this.goodStore != null) {
                this.goodStore.mergeFromDb(proto.getGoodStore());
            }
        }
        this.markAll();
        return DiscountInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DiscountInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastRefreshTime()) {
            this.setLastRefreshTime(proto.getLastRefreshTime());
            fieldCnt++;
        }
        if (proto.hasRefreshTimes()) {
            this.setRefreshTimes(proto.getRefreshTimes());
            fieldCnt++;
        }
        if (proto.hasFreeTimes()) {
            this.setFreeTimes(proto.getFreeTimes());
            fieldCnt++;
        }
        if (proto.hasGoodStore()) {
            this.getGoodStore().mergeChangeFromDb(proto.getGoodStore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DiscountInfo.Builder getCopySsBuilder() {
        final DiscountInfo.Builder builder = DiscountInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DiscountInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLastRefreshTime() != 0L) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTime()) {
            // 清理LastRefreshTime
            builder.clearLastRefreshTime();
            fieldCnt++;
        }
        if (this.getRefreshTimes() != 0) {
            builder.setRefreshTimes(this.getRefreshTimes());
            fieldCnt++;
        }  else if (builder.hasRefreshTimes()) {
            // 清理RefreshTimes
            builder.clearRefreshTimes();
            fieldCnt++;
        }
        if (this.getFreeTimes() != 0) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }  else if (builder.hasFreeTimes()) {
            // 清理FreeTimes
            builder.clearFreeTimes();
            fieldCnt++;
        }
        if (this.goodStore != null) {
            Struct.Int32GoodInfoMap.Builder tmpBuilder = Struct.Int32GoodInfoMap.newBuilder();
            final int tmpFieldCnt = this.goodStore.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodStore(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodStore();
            }
        }  else if (builder.hasGoodStore()) {
            // 清理GoodStore
            builder.clearGoodStore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DiscountInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTIME)) {
            builder.setLastRefreshTime(this.getLastRefreshTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REFRESHTIMES)) {
            builder.setRefreshTimes(this.getRefreshTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSTORE) && this.goodStore != null) {
            final boolean needClear = !builder.hasGoodStore();
            final int tmpFieldCnt = this.goodStore.copyChangeToSs(builder.getGoodStoreBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodStore();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DiscountInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLastRefreshTime()) {
            this.innerSetLastRefreshTime(proto.getLastRefreshTime());
        } else {
            this.innerSetLastRefreshTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRefreshTimes()) {
            this.innerSetRefreshTimes(proto.getRefreshTimes());
        } else {
            this.innerSetRefreshTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeTimes()) {
            this.innerSetFreeTimes(proto.getFreeTimes());
        } else {
            this.innerSetFreeTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasGoodStore()) {
            this.getGoodStore().mergeFromSs(proto.getGoodStore());
        } else {
            if (this.goodStore != null) {
                this.goodStore.mergeFromSs(proto.getGoodStore());
            }
        }
        this.markAll();
        return DiscountInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DiscountInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLastRefreshTime()) {
            this.setLastRefreshTime(proto.getLastRefreshTime());
            fieldCnt++;
        }
        if (proto.hasRefreshTimes()) {
            this.setRefreshTimes(proto.getRefreshTimes());
            fieldCnt++;
        }
        if (proto.hasFreeTimes()) {
            this.setFreeTimes(proto.getFreeTimes());
            fieldCnt++;
        }
        if (proto.hasGoodStore()) {
            this.getGoodStore().mergeChangeFromSs(proto.getGoodStore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DiscountInfo.Builder builder = DiscountInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GOODSTORE) && this.goodStore != null) {
            this.goodStore.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.goodStore != null) {
            this.goodStore.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DiscountInfoProp)) {
            return false;
        }
        final DiscountInfoProp otherNode = (DiscountInfoProp) node;
        if (this.lastRefreshTime != otherNode.lastRefreshTime) {
            return false;
        }
        if (this.refreshTimes != otherNode.refreshTimes) {
            return false;
        }
        if (this.freeTimes != otherNode.freeTimes) {
            return false;
        }
        if (!this.getGoodStore().compareDataTo(otherNode.getGoodStore())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
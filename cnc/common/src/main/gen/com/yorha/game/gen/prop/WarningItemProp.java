package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.WarningItem;
import com.yorha.proto.StructPB.WarningItemPB;


/**
 * <AUTHOR> auto gen
 */
public class WarningItemProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ARMYID = 0;
    public static final int FIELD_INDEX_TYPE = 1;
    public static final int FIELD_INDEX_ISIGNORE = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private long armyId = Constant.DEFAULT_LONG_VALUE;
    private WarningType type = WarningType.forNumber(0);
    private boolean isIgnore = Constant.DEFAULT_BOOLEAN_VALUE;

    public WarningItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public WarningItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get armyId
     *
     * @return armyId value
     */
    public long getArmyId() {
        return this.armyId;
    }

    /**
     * set armyId && set marked
     *
     * @param armyId new value
     * @return current object
     */
    public WarningItemProp setArmyId(long armyId) {
        if (this.armyId != armyId) {
            this.mark(FIELD_INDEX_ARMYID);
            this.armyId = armyId;
        }
        return this;
    }

    /**
     * inner set armyId
     *
     * @param armyId new value
     */
    private void innerSetArmyId(long armyId) {
        this.armyId = armyId;
    }

    /**
     * get type
     *
     * @return type value
     */
    public WarningType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public WarningItemProp setType(WarningType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(WarningType type) {
        this.type = type;
    }

    /**
     * get isIgnore
     *
     * @return isIgnore value
     */
    public boolean getIsIgnore() {
        return this.isIgnore;
    }

    /**
     * set isIgnore && set marked
     *
     * @param isIgnore new value
     * @return current object
     */
    public WarningItemProp setIsIgnore(boolean isIgnore) {
        if (this.isIgnore != isIgnore) {
            this.mark(FIELD_INDEX_ISIGNORE);
            this.isIgnore = isIgnore;
        }
        return this;
    }

    /**
     * inner set isIgnore
     *
     * @param isIgnore new value
     */
    private void innerSetIsIgnore(boolean isIgnore) {
        this.isIgnore = isIgnore;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarningItemPB.Builder getCopyCsBuilder() {
        final WarningItemPB.Builder builder = WarningItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(WarningItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getType() != WarningType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getIsIgnore()) {
            builder.setIsIgnore(this.getIsIgnore());
            fieldCnt++;
        }  else if (builder.hasIsIgnore()) {
            // 清理IsIgnore
            builder.clearIsIgnore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(WarningItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISIGNORE)) {
            builder.setIsIgnore(this.getIsIgnore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(WarningItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISIGNORE)) {
            builder.setIsIgnore(this.getIsIgnore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(WarningItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(WarningType.forNumber(0));
        }
        if (proto.hasIsIgnore()) {
            this.innerSetIsIgnore(proto.getIsIgnore());
        } else {
            this.innerSetIsIgnore(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return WarningItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(WarningItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasIsIgnore()) {
            this.setIsIgnore(proto.getIsIgnore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarningItem.Builder getCopyDbBuilder() {
        final WarningItem.Builder builder = WarningItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(WarningItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getType() != WarningType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getIsIgnore()) {
            builder.setIsIgnore(this.getIsIgnore());
            fieldCnt++;
        }  else if (builder.hasIsIgnore()) {
            // 清理IsIgnore
            builder.clearIsIgnore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(WarningItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISIGNORE)) {
            builder.setIsIgnore(this.getIsIgnore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(WarningItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(WarningType.forNumber(0));
        }
        if (proto.hasIsIgnore()) {
            this.innerSetIsIgnore(proto.getIsIgnore());
        } else {
            this.innerSetIsIgnore(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return WarningItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(WarningItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasIsIgnore()) {
            this.setIsIgnore(proto.getIsIgnore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarningItem.Builder getCopySsBuilder() {
        final WarningItem.Builder builder = WarningItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(WarningItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getArmyId() != 0L) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }  else if (builder.hasArmyId()) {
            // 清理ArmyId
            builder.clearArmyId();
            fieldCnt++;
        }
        if (this.getType() != WarningType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (this.getIsIgnore()) {
            builder.setIsIgnore(this.getIsIgnore());
            fieldCnt++;
        }  else if (builder.hasIsIgnore()) {
            // 清理IsIgnore
            builder.clearIsIgnore();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(WarningItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ARMYID)) {
            builder.setArmyId(this.getArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISIGNORE)) {
            builder.setIsIgnore(this.getIsIgnore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(WarningItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasArmyId()) {
            this.innerSetArmyId(proto.getArmyId());
        } else {
            this.innerSetArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(WarningType.forNumber(0));
        }
        if (proto.hasIsIgnore()) {
            this.innerSetIsIgnore(proto.getIsIgnore());
        } else {
            this.innerSetIsIgnore(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return WarningItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(WarningItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasArmyId()) {
            this.setArmyId(proto.getArmyId());
            fieldCnt++;
        }
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasIsIgnore()) {
            this.setIsIgnore(proto.getIsIgnore());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        WarningItem.Builder builder = WarningItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof WarningItemProp)) {
            return false;
        }
        final WarningItemProp otherNode = (WarningItemProp) node;
        if (this.armyId != otherNode.armyId) {
            return false;
        }
        if (this.type != otherNode.type) {
            return false;
        }
        if (this.isIgnore != otherNode.isIgnore) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
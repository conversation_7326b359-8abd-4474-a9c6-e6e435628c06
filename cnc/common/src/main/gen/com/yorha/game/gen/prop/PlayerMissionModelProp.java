package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerMissionModel;
import com.yorha.proto.Basic;
import com.yorha.proto.PlayerPB.PlayerMissionModelPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerMissionModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURMISSIONID = 0;
    public static final int FIELD_INDEX_FINISHEDMISSIONS = 1;
    public static final int FIELD_INDEX_BATTLEINFO = 2;
    public static final int FIELD_INDEX_BATTLEMISSIONID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int curMissionId = Constant.DEFAULT_INT_VALUE;
    private Int32SetProp finishedMissions = null;
    private com.google.protobuf.ByteString battleInfo = Constant.DEFAULT_BYTE_STRING_VALUE;
    private int battleMissionId = Constant.DEFAULT_INT_VALUE;

    public PlayerMissionModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerMissionModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curMissionId
     *
     * @return curMissionId value
     */
    public int getCurMissionId() {
        return this.curMissionId;
    }

    /**
     * set curMissionId && set marked
     *
     * @param curMissionId new value
     * @return current object
     */
    public PlayerMissionModelProp setCurMissionId(int curMissionId) {
        if (this.curMissionId != curMissionId) {
            this.mark(FIELD_INDEX_CURMISSIONID);
            this.curMissionId = curMissionId;
        }
        return this;
    }

    /**
     * inner set curMissionId
     *
     * @param curMissionId new value
     */
    private void innerSetCurMissionId(int curMissionId) {
        this.curMissionId = curMissionId;
    }

    /**
     * get finishedMissions
     *
     * @return finishedMissions value
     */
    public Int32SetProp getFinishedMissions() {
        if (this.finishedMissions == null) {
            this.finishedMissions = new Int32SetProp(this, FIELD_INDEX_FINISHEDMISSIONS);
        }
        return this.finishedMissions;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addFinishedMissions(Integer e) {
        this.getFinishedMissions().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeFinishedMissions(Integer e) {
        if (this.finishedMissions == null) {
            return null;
        }
        if(this.finishedMissions.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getFinishedMissionsSize() {
        if (this.finishedMissions == null) {
            return 0;
        }
        return this.finishedMissions.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isFinishedMissionsEmpty() {
        if (this.finishedMissions == null) {
            return true;
        }
        return this.getFinishedMissions().isEmpty();
    }

    /**
     * clear set
     */
    public void clearFinishedMissions() {
        this.getFinishedMissions().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isFinishedMissionsContains(Integer e) {
        return this.finishedMissions != null && this.finishedMissions.contains(e);
    }

    /**
     * get battleInfo
     *
     * @return battleInfo value
     */
    public com.google.protobuf.ByteString getBattleInfo() {
        return this.battleInfo;
    }

    /**
     * set battleInfo && set marked
     *
     * @param battleInfo new value
     * @return current object
     */
    public PlayerMissionModelProp setBattleInfo(com.google.protobuf.ByteString battleInfo) {
        if (this.battleInfo != battleInfo) {
            this.mark(FIELD_INDEX_BATTLEINFO);
            this.battleInfo = battleInfo;
        }
        return this;
    }

    /**
     * inner set battleInfo
     *
     * @param battleInfo new value
     */
    private void innerSetBattleInfo(com.google.protobuf.ByteString battleInfo) {
        this.battleInfo = battleInfo;
    }

    /**
     * get battleMissionId
     *
     * @return battleMissionId value
     */
    public int getBattleMissionId() {
        return this.battleMissionId;
    }

    /**
     * set battleMissionId && set marked
     *
     * @param battleMissionId new value
     * @return current object
     */
    public PlayerMissionModelProp setBattleMissionId(int battleMissionId) {
        if (this.battleMissionId != battleMissionId) {
            this.mark(FIELD_INDEX_BATTLEMISSIONID);
            this.battleMissionId = battleMissionId;
        }
        return this;
    }

    /**
     * inner set battleMissionId
     *
     * @param battleMissionId new value
     */
    private void innerSetBattleMissionId(int battleMissionId) {
        this.battleMissionId = battleMissionId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMissionModelPB.Builder getCopyCsBuilder() {
        final PlayerMissionModelPB.Builder builder = PlayerMissionModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerMissionModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMissionId() != 0) {
            builder.setCurMissionId(this.getCurMissionId());
            fieldCnt++;
        }  else if (builder.hasCurMissionId()) {
            // 清理CurMissionId
            builder.clearCurMissionId();
            fieldCnt++;
        }
        if (this.finishedMissions != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.finishedMissions.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFinishedMissions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFinishedMissions();
            }
        }  else if (builder.hasFinishedMissions()) {
            // 清理FinishedMissions
            builder.clearFinishedMissions();
            fieldCnt++;
        }
        if (this.getBattleMissionId() != 0) {
            builder.setBattleMissionId(this.getBattleMissionId());
            fieldCnt++;
        }  else if (builder.hasBattleMissionId()) {
            // 清理BattleMissionId
            builder.clearBattleMissionId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerMissionModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMISSIONID)) {
            builder.setCurMissionId(this.getCurMissionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHEDMISSIONS) && this.finishedMissions != null) {
            final boolean needClear = !builder.hasFinishedMissions();
            final int tmpFieldCnt = this.finishedMissions.copyChangeToCs(builder.getFinishedMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEMISSIONID)) {
            builder.setBattleMissionId(this.getBattleMissionId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerMissionModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMISSIONID)) {
            builder.setCurMissionId(this.getCurMissionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHEDMISSIONS) && this.finishedMissions != null) {
            final boolean needClear = !builder.hasFinishedMissions();
            final int tmpFieldCnt = this.finishedMissions.copyChangeToAndClearDeleteKeysCs(builder.getFinishedMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEMISSIONID)) {
            builder.setBattleMissionId(this.getBattleMissionId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerMissionModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMissionId()) {
            this.innerSetCurMissionId(proto.getCurMissionId());
        } else {
            this.innerSetCurMissionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinishedMissions()) {
            this.getFinishedMissions().mergeFromCs(proto.getFinishedMissions());
        } else {
            if (this.finishedMissions != null) {
                this.finishedMissions.mergeFromCs(proto.getFinishedMissions());
            }
        }
        if (proto.hasBattleMissionId()) {
            this.innerSetBattleMissionId(proto.getBattleMissionId());
        } else {
            this.innerSetBattleMissionId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMissionModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerMissionModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMissionId()) {
            this.setCurMissionId(proto.getCurMissionId());
            fieldCnt++;
        }
        if (proto.hasFinishedMissions()) {
            this.getFinishedMissions().mergeChangeFromCs(proto.getFinishedMissions());
            fieldCnt++;
        }
        if (proto.hasBattleMissionId()) {
            this.setBattleMissionId(proto.getBattleMissionId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMissionModel.Builder getCopyDbBuilder() {
        final PlayerMissionModel.Builder builder = PlayerMissionModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerMissionModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMissionId() != 0) {
            builder.setCurMissionId(this.getCurMissionId());
            fieldCnt++;
        }  else if (builder.hasCurMissionId()) {
            // 清理CurMissionId
            builder.clearCurMissionId();
            fieldCnt++;
        }
        if (this.finishedMissions != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.finishedMissions.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFinishedMissions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFinishedMissions();
            }
        }  else if (builder.hasFinishedMissions()) {
            // 清理FinishedMissions
            builder.clearFinishedMissions();
            fieldCnt++;
        }
        if (this.getBattleInfo() != com.google.protobuf.ByteString.EMPTY) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }  else if (builder.hasBattleInfo()) {
            // 清理BattleInfo
            builder.clearBattleInfo();
            fieldCnt++;
        }
        if (this.getBattleMissionId() != 0) {
            builder.setBattleMissionId(this.getBattleMissionId());
            fieldCnt++;
        }  else if (builder.hasBattleMissionId()) {
            // 清理BattleMissionId
            builder.clearBattleMissionId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerMissionModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMISSIONID)) {
            builder.setCurMissionId(this.getCurMissionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHEDMISSIONS) && this.finishedMissions != null) {
            final boolean needClear = !builder.hasFinishedMissions();
            final int tmpFieldCnt = this.finishedMissions.copyChangeToDb(builder.getFinishedMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEINFO)) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEMISSIONID)) {
            builder.setBattleMissionId(this.getBattleMissionId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerMissionModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMissionId()) {
            this.innerSetCurMissionId(proto.getCurMissionId());
        } else {
            this.innerSetCurMissionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinishedMissions()) {
            this.getFinishedMissions().mergeFromDb(proto.getFinishedMissions());
        } else {
            if (this.finishedMissions != null) {
                this.finishedMissions.mergeFromDb(proto.getFinishedMissions());
            }
        }
        if (proto.hasBattleInfo()) {
            this.innerSetBattleInfo(proto.getBattleInfo());
        } else {
            this.innerSetBattleInfo(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        if (proto.hasBattleMissionId()) {
            this.innerSetBattleMissionId(proto.getBattleMissionId());
        } else {
            this.innerSetBattleMissionId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMissionModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerMissionModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMissionId()) {
            this.setCurMissionId(proto.getCurMissionId());
            fieldCnt++;
        }
        if (proto.hasFinishedMissions()) {
            this.getFinishedMissions().mergeChangeFromDb(proto.getFinishedMissions());
            fieldCnt++;
        }
        if (proto.hasBattleInfo()) {
            this.setBattleInfo(proto.getBattleInfo());
            fieldCnt++;
        }
        if (proto.hasBattleMissionId()) {
            this.setBattleMissionId(proto.getBattleMissionId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMissionModel.Builder getCopySsBuilder() {
        final PlayerMissionModel.Builder builder = PlayerMissionModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerMissionModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurMissionId() != 0) {
            builder.setCurMissionId(this.getCurMissionId());
            fieldCnt++;
        }  else if (builder.hasCurMissionId()) {
            // 清理CurMissionId
            builder.clearCurMissionId();
            fieldCnt++;
        }
        if (this.finishedMissions != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.finishedMissions.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFinishedMissions(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFinishedMissions();
            }
        }  else if (builder.hasFinishedMissions()) {
            // 清理FinishedMissions
            builder.clearFinishedMissions();
            fieldCnt++;
        }
        if (this.getBattleInfo() != com.google.protobuf.ByteString.EMPTY) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }  else if (builder.hasBattleInfo()) {
            // 清理BattleInfo
            builder.clearBattleInfo();
            fieldCnt++;
        }
        if (this.getBattleMissionId() != 0) {
            builder.setBattleMissionId(this.getBattleMissionId());
            fieldCnt++;
        }  else if (builder.hasBattleMissionId()) {
            // 清理BattleMissionId
            builder.clearBattleMissionId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerMissionModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURMISSIONID)) {
            builder.setCurMissionId(this.getCurMissionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHEDMISSIONS) && this.finishedMissions != null) {
            final boolean needClear = !builder.hasFinishedMissions();
            final int tmpFieldCnt = this.finishedMissions.copyChangeToSs(builder.getFinishedMissionsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFinishedMissions();
            }
        }
        if (this.hasMark(FIELD_INDEX_BATTLEINFO)) {
            builder.setBattleInfo(this.getBattleInfo());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEMISSIONID)) {
            builder.setBattleMissionId(this.getBattleMissionId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerMissionModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurMissionId()) {
            this.innerSetCurMissionId(proto.getCurMissionId());
        } else {
            this.innerSetCurMissionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFinishedMissions()) {
            this.getFinishedMissions().mergeFromSs(proto.getFinishedMissions());
        } else {
            if (this.finishedMissions != null) {
                this.finishedMissions.mergeFromSs(proto.getFinishedMissions());
            }
        }
        if (proto.hasBattleInfo()) {
            this.innerSetBattleInfo(proto.getBattleInfo());
        } else {
            this.innerSetBattleInfo(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        if (proto.hasBattleMissionId()) {
            this.innerSetBattleMissionId(proto.getBattleMissionId());
        } else {
            this.innerSetBattleMissionId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerMissionModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerMissionModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurMissionId()) {
            this.setCurMissionId(proto.getCurMissionId());
            fieldCnt++;
        }
        if (proto.hasFinishedMissions()) {
            this.getFinishedMissions().mergeChangeFromSs(proto.getFinishedMissions());
            fieldCnt++;
        }
        if (proto.hasBattleInfo()) {
            this.setBattleInfo(proto.getBattleInfo());
            fieldCnt++;
        }
        if (proto.hasBattleMissionId()) {
            this.setBattleMissionId(proto.getBattleMissionId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerMissionModel.Builder builder = PlayerMissionModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_FINISHEDMISSIONS) && this.finishedMissions != null) {
            this.finishedMissions.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.finishedMissions != null) {
            this.finishedMissions.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerMissionModelProp)) {
            return false;
        }
        final PlayerMissionModelProp otherNode = (PlayerMissionModelProp) node;
        if (this.curMissionId != otherNode.curMissionId) {
            return false;
        }
        if (!this.getFinishedMissions().compareDataTo(otherNode.getFinishedMissions())) {
            return false;
        }
        if (this.battleInfo != otherNode.battleInfo) {
            return false;
        }
        if (this.battleMissionId != otherNode.battleMissionId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
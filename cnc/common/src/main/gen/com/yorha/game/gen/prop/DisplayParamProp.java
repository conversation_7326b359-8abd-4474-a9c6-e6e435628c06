package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.DisplayParam;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.DisplayParamPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class DisplayParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TYPE = 0;
    public static final int FIELD_INDEX_TEXT = 1;
    public static final int FIELD_INDEX_NUMBER = 2;
    public static final int FIELD_INDEX_POSITION = 3;
    public static final int FIELD_INDEX_MULTILANGTXT = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private DisplayParamType type = DisplayParamType.forNumber(0);
    private String text = Constant.DEFAULT_STR_VALUE;
    private long number = Constant.DEFAULT_LONG_VALUE;
    private PointProp position = null;
    private Int32MultiLangTxtMapProp multiLangTxt = null;

    public DisplayParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DisplayParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get type
     *
     * @return type value
     */
    public DisplayParamType getType() {
        return this.type;
    }

    /**
     * set type && set marked
     *
     * @param type new value
     * @return current object
     */
    public DisplayParamProp setType(DisplayParamType type) {
        if (type == null) {
            throw new NullPointerException();
        }
        if (this.type != type) {
            this.mark(FIELD_INDEX_TYPE);
            this.type = type;
        }
        return this;
    }

    /**
     * inner set type
     *
     * @param type new value
     */
    private void innerSetType(DisplayParamType type) {
        this.type = type;
    }

    /**
     * get text
     *
     * @return text value
     */
    public String getText() {
        return this.text;
    }

    /**
     * set text && set marked
     *
     * @param text new value
     * @return current object
     */
    public DisplayParamProp setText(String text) {
        if (text == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.text, text)) {
            this.mark(FIELD_INDEX_TEXT);
            this.text = text;
        }
        return this;
    }

    /**
     * inner set text
     *
     * @param text new value
     */
    private void innerSetText(String text) {
        this.text = text;
    }

    /**
     * get number
     *
     * @return number value
     */
    public long getNumber() {
        return this.number;
    }

    /**
     * set number && set marked
     *
     * @param number new value
     * @return current object
     */
    public DisplayParamProp setNumber(long number) {
        if (this.number != number) {
            this.mark(FIELD_INDEX_NUMBER);
            this.number = number;
        }
        return this;
    }

    /**
     * inner set number
     *
     * @param number new value
     */
    private void innerSetNumber(long number) {
        this.number = number;
    }

    /**
     * get position
     *
     * @return position value
     */
    public PointProp getPosition() {
        if (this.position == null) {
            this.position = new PointProp(this, FIELD_INDEX_POSITION);
        }
        return this.position;
    }

    /**
     * get multiLangTxt
     *
     * @return multiLangTxt value
     */
    public Int32MultiLangTxtMapProp getMultiLangTxt() {
        if (this.multiLangTxt == null) {
            this.multiLangTxt = new Int32MultiLangTxtMapProp(this, FIELD_INDEX_MULTILANGTXT);
        }
        return this.multiLangTxt;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putMultiLangTxtV(MultiLangTxtProp v) {
        this.getMultiLangTxt().put(v.getLanguage(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public MultiLangTxtProp addEmptyMultiLangTxt(Integer k) {
        return this.getMultiLangTxt().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getMultiLangTxtSize() {
        if (this.multiLangTxt == null) {
            return 0;
        }
        return this.multiLangTxt.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isMultiLangTxtEmpty() {
        if (this.multiLangTxt == null) {
            return true;
        }
        return this.multiLangTxt.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public MultiLangTxtProp getMultiLangTxtV(Integer k) {
        if (this.multiLangTxt == null || !this.multiLangTxt.containsKey(k)) {
            return null;
        }
        return this.multiLangTxt.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearMultiLangTxt() {
        if (this.multiLangTxt != null) {
            this.multiLangTxt.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeMultiLangTxtV(Integer k) {
        if (this.multiLangTxt != null) {
            this.multiLangTxt.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayParamPB.Builder getCopyCsBuilder() {
        final DisplayParamPB.Builder builder = DisplayParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DisplayParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != DisplayParamType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        if (this.getNumber() != 0L) {
            builder.setNumber(this.getNumber());
            fieldCnt++;
        }  else if (builder.hasNumber()) {
            // 清理Number
            builder.clearNumber();
            fieldCnt++;
        }
        if (this.position != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.position.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPosition(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPosition();
            }
        }  else if (builder.hasPosition()) {
            // 清理Position
            builder.clearPosition();
            fieldCnt++;
        }
        if (this.multiLangTxt != null) {
            StructPB.Int32MultiLangTxtMapPB.Builder tmpBuilder = StructPB.Int32MultiLangTxtMapPB.newBuilder();
            final int tmpFieldCnt = this.multiLangTxt.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMultiLangTxt(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMultiLangTxt();
            }
        }  else if (builder.hasMultiLangTxt()) {
            // 清理MultiLangTxt
            builder.clearMultiLangTxt();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DisplayParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUMBER)) {
            builder.setNumber(this.getNumber());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POSITION) && this.position != null) {
            final boolean needClear = !builder.hasPosition();
            final int tmpFieldCnt = this.position.copyChangeToCs(builder.getPositionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosition();
            }
        }
        if (this.hasMark(FIELD_INDEX_MULTILANGTXT) && this.multiLangTxt != null) {
            final boolean needClear = !builder.hasMultiLangTxt();
            final int tmpFieldCnt = this.multiLangTxt.copyChangeToCs(builder.getMultiLangTxtBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMultiLangTxt();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DisplayParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUMBER)) {
            builder.setNumber(this.getNumber());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POSITION) && this.position != null) {
            final boolean needClear = !builder.hasPosition();
            final int tmpFieldCnt = this.position.copyChangeToAndClearDeleteKeysCs(builder.getPositionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosition();
            }
        }
        if (this.hasMark(FIELD_INDEX_MULTILANGTXT) && this.multiLangTxt != null) {
            final boolean needClear = !builder.hasMultiLangTxt();
            final int tmpFieldCnt = this.multiLangTxt.copyChangeToAndClearDeleteKeysCs(builder.getMultiLangTxtBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMultiLangTxt();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DisplayParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(DisplayParamType.forNumber(0));
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNumber()) {
            this.innerSetNumber(proto.getNumber());
        } else {
            this.innerSetNumber(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPosition()) {
            this.getPosition().mergeFromCs(proto.getPosition());
        } else {
            if (this.position != null) {
                this.position.mergeFromCs(proto.getPosition());
            }
        }
        if (proto.hasMultiLangTxt()) {
            this.getMultiLangTxt().mergeFromCs(proto.getMultiLangTxt());
        } else {
            if (this.multiLangTxt != null) {
                this.multiLangTxt.mergeFromCs(proto.getMultiLangTxt());
            }
        }
        this.markAll();
        return DisplayParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DisplayParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        if (proto.hasNumber()) {
            this.setNumber(proto.getNumber());
            fieldCnt++;
        }
        if (proto.hasPosition()) {
            this.getPosition().mergeChangeFromCs(proto.getPosition());
            fieldCnt++;
        }
        if (proto.hasMultiLangTxt()) {
            this.getMultiLangTxt().mergeChangeFromCs(proto.getMultiLangTxt());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayParam.Builder getCopyDbBuilder() {
        final DisplayParam.Builder builder = DisplayParam.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DisplayParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != DisplayParamType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        if (this.getNumber() != 0L) {
            builder.setNumber(this.getNumber());
            fieldCnt++;
        }  else if (builder.hasNumber()) {
            // 清理Number
            builder.clearNumber();
            fieldCnt++;
        }
        if (this.position != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.position.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPosition(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPosition();
            }
        }  else if (builder.hasPosition()) {
            // 清理Position
            builder.clearPosition();
            fieldCnt++;
        }
        if (this.multiLangTxt != null) {
            Struct.Int32MultiLangTxtMap.Builder tmpBuilder = Struct.Int32MultiLangTxtMap.newBuilder();
            final int tmpFieldCnt = this.multiLangTxt.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMultiLangTxt(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMultiLangTxt();
            }
        }  else if (builder.hasMultiLangTxt()) {
            // 清理MultiLangTxt
            builder.clearMultiLangTxt();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DisplayParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUMBER)) {
            builder.setNumber(this.getNumber());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POSITION) && this.position != null) {
            final boolean needClear = !builder.hasPosition();
            final int tmpFieldCnt = this.position.copyChangeToDb(builder.getPositionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosition();
            }
        }
        if (this.hasMark(FIELD_INDEX_MULTILANGTXT) && this.multiLangTxt != null) {
            final boolean needClear = !builder.hasMultiLangTxt();
            final int tmpFieldCnt = this.multiLangTxt.copyChangeToDb(builder.getMultiLangTxtBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMultiLangTxt();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DisplayParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(DisplayParamType.forNumber(0));
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNumber()) {
            this.innerSetNumber(proto.getNumber());
        } else {
            this.innerSetNumber(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPosition()) {
            this.getPosition().mergeFromDb(proto.getPosition());
        } else {
            if (this.position != null) {
                this.position.mergeFromDb(proto.getPosition());
            }
        }
        if (proto.hasMultiLangTxt()) {
            this.getMultiLangTxt().mergeFromDb(proto.getMultiLangTxt());
        } else {
            if (this.multiLangTxt != null) {
                this.multiLangTxt.mergeFromDb(proto.getMultiLangTxt());
            }
        }
        this.markAll();
        return DisplayParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DisplayParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        if (proto.hasNumber()) {
            this.setNumber(proto.getNumber());
            fieldCnt++;
        }
        if (proto.hasPosition()) {
            this.getPosition().mergeChangeFromDb(proto.getPosition());
            fieldCnt++;
        }
        if (proto.hasMultiLangTxt()) {
            this.getMultiLangTxt().mergeChangeFromDb(proto.getMultiLangTxt());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DisplayParam.Builder getCopySsBuilder() {
        final DisplayParam.Builder builder = DisplayParam.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DisplayParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getType() != DisplayParamType.forNumber(0)) {
            builder.setType(this.getType());
            fieldCnt++;
        }  else if (builder.hasType()) {
            // 清理Type
            builder.clearType();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        if (this.getNumber() != 0L) {
            builder.setNumber(this.getNumber());
            fieldCnt++;
        }  else if (builder.hasNumber()) {
            // 清理Number
            builder.clearNumber();
            fieldCnt++;
        }
        if (this.position != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.position.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPosition(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPosition();
            }
        }  else if (builder.hasPosition()) {
            // 清理Position
            builder.clearPosition();
            fieldCnt++;
        }
        if (this.multiLangTxt != null) {
            Struct.Int32MultiLangTxtMap.Builder tmpBuilder = Struct.Int32MultiLangTxtMap.newBuilder();
            final int tmpFieldCnt = this.multiLangTxt.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMultiLangTxt(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMultiLangTxt();
            }
        }  else if (builder.hasMultiLangTxt()) {
            // 清理MultiLangTxt
            builder.clearMultiLangTxt();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DisplayParam.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TYPE)) {
            builder.setType(this.getType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUMBER)) {
            builder.setNumber(this.getNumber());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POSITION) && this.position != null) {
            final boolean needClear = !builder.hasPosition();
            final int tmpFieldCnt = this.position.copyChangeToSs(builder.getPositionBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPosition();
            }
        }
        if (this.hasMark(FIELD_INDEX_MULTILANGTXT) && this.multiLangTxt != null) {
            final boolean needClear = !builder.hasMultiLangTxt();
            final int tmpFieldCnt = this.multiLangTxt.copyChangeToSs(builder.getMultiLangTxtBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMultiLangTxt();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DisplayParam proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasType()) {
            this.innerSetType(proto.getType());
        } else {
            this.innerSetType(DisplayParamType.forNumber(0));
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasNumber()) {
            this.innerSetNumber(proto.getNumber());
        } else {
            this.innerSetNumber(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPosition()) {
            this.getPosition().mergeFromSs(proto.getPosition());
        } else {
            if (this.position != null) {
                this.position.mergeFromSs(proto.getPosition());
            }
        }
        if (proto.hasMultiLangTxt()) {
            this.getMultiLangTxt().mergeFromSs(proto.getMultiLangTxt());
        } else {
            if (this.multiLangTxt != null) {
                this.multiLangTxt.mergeFromSs(proto.getMultiLangTxt());
            }
        }
        this.markAll();
        return DisplayParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DisplayParam proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasType()) {
            this.setType(proto.getType());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        if (proto.hasNumber()) {
            this.setNumber(proto.getNumber());
            fieldCnt++;
        }
        if (proto.hasPosition()) {
            this.getPosition().mergeChangeFromSs(proto.getPosition());
            fieldCnt++;
        }
        if (proto.hasMultiLangTxt()) {
            this.getMultiLangTxt().mergeChangeFromSs(proto.getMultiLangTxt());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DisplayParam.Builder builder = DisplayParam.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POSITION) && this.position != null) {
            this.position.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MULTILANGTXT) && this.multiLangTxt != null) {
            this.multiLangTxt.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.position != null) {
            this.position.markAll();
        }
        if (this.multiLangTxt != null) {
            this.multiLangTxt.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DisplayParamProp)) {
            return false;
        }
        final DisplayParamProp otherNode = (DisplayParamProp) node;
        if (this.type != otherNode.type) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.text, otherNode.text)) {
            return false;
        }
        if (this.number != otherNode.number) {
            return false;
        }
        if (!this.getPosition().compareDataTo(otherNode.getPosition())) {
            return false;
        }
        if (!this.getMultiLangTxt().compareDataTo(otherNode.getMultiLangTxt())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
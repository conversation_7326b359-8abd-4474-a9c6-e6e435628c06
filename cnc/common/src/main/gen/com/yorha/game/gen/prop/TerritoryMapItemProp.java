package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.TerritoryMapItem;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPB.TerritoryMapItemPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class TerritoryMapItemProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CLANID = 0;
    public static final int FIELD_INDEX_COLOR = 1;
    public static final int FIELD_INDEX_OWNPARTID = 2;
    public static final int FIELD_INDEX_OCCUPYPARTID = 3;
    public static final int FIELD_INDEX_NATIONFLAGID = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private int color = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp ownPartId = null;
    private Int32ListProp occupyPartId = null;
    private int nationFlagId = Constant.DEFAULT_INT_VALUE;

    public TerritoryMapItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TerritoryMapItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public TerritoryMapItemProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get color
     *
     * @return color value
     */
    public int getColor() {
        return this.color;
    }

    /**
     * set color && set marked
     *
     * @param color new value
     * @return current object
     */
    public TerritoryMapItemProp setColor(int color) {
        if (this.color != color) {
            this.mark(FIELD_INDEX_COLOR);
            this.color = color;
        }
        return this;
    }

    /**
     * inner set color
     *
     * @param color new value
     */
    private void innerSetColor(int color) {
        this.color = color;
    }

    /**
     * get ownPartId
     *
     * @return ownPartId value
     */
    public Int32ListProp getOwnPartId() {
        if (this.ownPartId == null) {
            this.ownPartId = new Int32ListProp(this, FIELD_INDEX_OWNPARTID);
        }
        return this.ownPartId;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addOwnPartId(Integer v) {
        this.getOwnPartId().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getOwnPartIdIndex(int index) {
        return this.getOwnPartId().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeOwnPartId(Integer v) {
        if (this.ownPartId == null) {
            return null;
        }
        if(this.ownPartId.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getOwnPartIdSize() {
        if (this.ownPartId == null) {
            return 0;
        }
        return this.ownPartId.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isOwnPartIdEmpty() {
        if (this.ownPartId == null) {
            return true;
        }
        return this.getOwnPartId().isEmpty();
    }

    /**
     * clear list
     */
    public void clearOwnPartId() {
        this.getOwnPartId().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeOwnPartIdIndex(int index) {
        return this.getOwnPartId().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setOwnPartIdIndex(int index, Integer v) {
        return this.getOwnPartId().set(index, v);
    }
    /**
     * get occupyPartId
     *
     * @return occupyPartId value
     */
    public Int32ListProp getOccupyPartId() {
        if (this.occupyPartId == null) {
            this.occupyPartId = new Int32ListProp(this, FIELD_INDEX_OCCUPYPARTID);
        }
        return this.occupyPartId;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addOccupyPartId(Integer v) {
        this.getOccupyPartId().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getOccupyPartIdIndex(int index) {
        return this.getOccupyPartId().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeOccupyPartId(Integer v) {
        if (this.occupyPartId == null) {
            return null;
        }
        if(this.occupyPartId.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getOccupyPartIdSize() {
        if (this.occupyPartId == null) {
            return 0;
        }
        return this.occupyPartId.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isOccupyPartIdEmpty() {
        if (this.occupyPartId == null) {
            return true;
        }
        return this.getOccupyPartId().isEmpty();
    }

    /**
     * clear list
     */
    public void clearOccupyPartId() {
        this.getOccupyPartId().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeOccupyPartIdIndex(int index) {
        return this.getOccupyPartId().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setOccupyPartIdIndex(int index, Integer v) {
        return this.getOccupyPartId().set(index, v);
    }
    /**
     * get nationFlagId
     *
     * @return nationFlagId value
     */
    public int getNationFlagId() {
        return this.nationFlagId;
    }

    /**
     * set nationFlagId && set marked
     *
     * @param nationFlagId new value
     * @return current object
     */
    public TerritoryMapItemProp setNationFlagId(int nationFlagId) {
        if (this.nationFlagId != nationFlagId) {
            this.mark(FIELD_INDEX_NATIONFLAGID);
            this.nationFlagId = nationFlagId;
        }
        return this;
    }

    /**
     * inner set nationFlagId
     *
     * @param nationFlagId new value
     */
    private void innerSetNationFlagId(int nationFlagId) {
        this.nationFlagId = nationFlagId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TerritoryMapItemPB.Builder getCopyCsBuilder() {
        final TerritoryMapItemPB.Builder builder = TerritoryMapItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TerritoryMapItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getColor() != 0) {
            builder.setColor(this.getColor());
            fieldCnt++;
        }  else if (builder.hasColor()) {
            // 清理Color
            builder.clearColor();
            fieldCnt++;
        }
        if (this.ownPartId != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.ownPartId.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOwnPartId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOwnPartId();
            }
        }  else if (builder.hasOwnPartId()) {
            // 清理OwnPartId
            builder.clearOwnPartId();
            fieldCnt++;
        }
        if (this.occupyPartId != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.occupyPartId.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOccupyPartId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOccupyPartId();
            }
        }  else if (builder.hasOccupyPartId()) {
            // 清理OccupyPartId
            builder.clearOccupyPartId();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TerritoryMapItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLOR)) {
            builder.setColor(this.getColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNPARTID) && this.ownPartId != null) {
            final boolean needClear = !builder.hasOwnPartId();
            final int tmpFieldCnt = this.ownPartId.copyChangeToCs(builder.getOwnPartIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOwnPartId();
            }
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYPARTID) && this.occupyPartId != null) {
            final boolean needClear = !builder.hasOccupyPartId();
            final int tmpFieldCnt = this.occupyPartId.copyChangeToCs(builder.getOccupyPartIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOccupyPartId();
            }
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TerritoryMapItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLOR)) {
            builder.setColor(this.getColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNPARTID) && this.ownPartId != null) {
            final boolean needClear = !builder.hasOwnPartId();
            final int tmpFieldCnt = this.ownPartId.copyChangeToCs(builder.getOwnPartIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOwnPartId();
            }
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYPARTID) && this.occupyPartId != null) {
            final boolean needClear = !builder.hasOccupyPartId();
            final int tmpFieldCnt = this.occupyPartId.copyChangeToCs(builder.getOccupyPartIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOccupyPartId();
            }
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TerritoryMapItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasColor()) {
            this.innerSetColor(proto.getColor());
        } else {
            this.innerSetColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnPartId()) {
            this.getOwnPartId().mergeFromCs(proto.getOwnPartId());
        } else {
            if (this.ownPartId != null) {
                this.ownPartId.mergeFromCs(proto.getOwnPartId());
            }
        }
        if (proto.hasOccupyPartId()) {
            this.getOccupyPartId().mergeFromCs(proto.getOccupyPartId());
        } else {
            if (this.occupyPartId != null) {
                this.occupyPartId.mergeFromCs(proto.getOccupyPartId());
            }
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TerritoryMapItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TerritoryMapItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasColor()) {
            this.setColor(proto.getColor());
            fieldCnt++;
        }
        if (proto.hasOwnPartId()) {
            this.getOwnPartId().mergeChangeFromCs(proto.getOwnPartId());
            fieldCnt++;
        }
        if (proto.hasOccupyPartId()) {
            this.getOccupyPartId().mergeChangeFromCs(proto.getOccupyPartId());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TerritoryMapItem.Builder getCopySsBuilder() {
        final TerritoryMapItem.Builder builder = TerritoryMapItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TerritoryMapItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (this.getColor() != 0) {
            builder.setColor(this.getColor());
            fieldCnt++;
        }  else if (builder.hasColor()) {
            // 清理Color
            builder.clearColor();
            fieldCnt++;
        }
        if (this.ownPartId != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.ownPartId.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOwnPartId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOwnPartId();
            }
        }  else if (builder.hasOwnPartId()) {
            // 清理OwnPartId
            builder.clearOwnPartId();
            fieldCnt++;
        }
        if (this.occupyPartId != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.occupyPartId.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOccupyPartId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOccupyPartId();
            }
        }  else if (builder.hasOccupyPartId()) {
            // 清理OccupyPartId
            builder.clearOccupyPartId();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TerritoryMapItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COLOR)) {
            builder.setColor(this.getColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNPARTID) && this.ownPartId != null) {
            final boolean needClear = !builder.hasOwnPartId();
            final int tmpFieldCnt = this.ownPartId.copyChangeToSs(builder.getOwnPartIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOwnPartId();
            }
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYPARTID) && this.occupyPartId != null) {
            final boolean needClear = !builder.hasOccupyPartId();
            final int tmpFieldCnt = this.occupyPartId.copyChangeToSs(builder.getOccupyPartIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOccupyPartId();
            }
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TerritoryMapItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasColor()) {
            this.innerSetColor(proto.getColor());
        } else {
            this.innerSetColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOwnPartId()) {
            this.getOwnPartId().mergeFromSs(proto.getOwnPartId());
        } else {
            if (this.ownPartId != null) {
                this.ownPartId.mergeFromSs(proto.getOwnPartId());
            }
        }
        if (proto.hasOccupyPartId()) {
            this.getOccupyPartId().mergeFromSs(proto.getOccupyPartId());
        } else {
            if (this.occupyPartId != null) {
                this.occupyPartId.mergeFromSs(proto.getOccupyPartId());
            }
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TerritoryMapItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TerritoryMapItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasColor()) {
            this.setColor(proto.getColor());
            fieldCnt++;
        }
        if (proto.hasOwnPartId()) {
            this.getOwnPartId().mergeChangeFromSs(proto.getOwnPartId());
            fieldCnt++;
        }
        if (proto.hasOccupyPartId()) {
            this.getOccupyPartId().mergeChangeFromSs(proto.getOccupyPartId());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TerritoryMapItem.Builder builder = TerritoryMapItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_OWNPARTID) && this.ownPartId != null) {
            this.ownPartId.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_OCCUPYPARTID) && this.occupyPartId != null) {
            this.occupyPartId.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.ownPartId != null) {
            this.ownPartId.markAll();
        }
        if (this.occupyPartId != null) {
            this.occupyPartId.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TerritoryMapItemProp)) {
            return false;
        }
        final TerritoryMapItemProp otherNode = (TerritoryMapItemProp) node;
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (this.color != otherNode.color) {
            return false;
        }
        if (!this.getOwnPartId().compareDataTo(otherNode.getOwnPartId())) {
            return false;
        }
        if (!this.getOccupyPartId().compareDataTo(otherNode.getOccupyPartId())) {
            return false;
        }
        if (this.nationFlagId != otherNode.nationFlagId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
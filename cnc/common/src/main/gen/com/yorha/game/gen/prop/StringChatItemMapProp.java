 
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.AbstractMapNode;
import com.yorha.proto.PlayerPB.StringChatItemMapPB;
import com.yorha.proto.PlayerPB.ChatItemPB;
import com.yorha.proto.Player.StringChatItemMap;
import com.yorha.proto.Player.ChatItem;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> auto gen
 */
public class StringChatItemMapProp extends AbstractMapNode<String, ChatItemProp> {
    /**
     * Creates a StringChatItemMapProp container
     *
     * @param parent     parent node
     * @param fieldIndex field index in parent node
     */
    public StringChatItemMapProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to StringChatItemMapProp
     *
     * @param k map key
     * @return new object
     */
    @Override
    public ChatItemProp addEmptyValue(String k) {
        ChatItemProp newProp = new ChatItemProp(null, DEFAULT_FIELD_INDEX);
        newProp.setId(k);
        this.put(k, newProp);
        return newProp;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringChatItemMapPB.Builder getCopyCsBuilder() {
        final StringChatItemMapPB.Builder builder = StringChatItemMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyToCs(StringChatItemMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringChatItemMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, ChatItemProp> entry : this.entrySet()) {
            ChatItemPB.Builder itemBuilder = ChatItemPB.newBuilder();
            entry.getValue().copyToCs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringChatItemMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToCs(StringChatItemMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final ChatItemProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ChatItemPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ChatItemPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringChatItemMapProp.FIELD_COUNT: 0;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last. it wll clear clearFlag and deleteKeys.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToAndClearDeleteKeysCs(StringChatItemMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        // clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            isChanged = true;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final ChatItemProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ChatItemPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ChatItemPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToAndClearDeleteKeysCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        if (isChanged) {
            builder.clearDeleteKeys();
            builder.clearClearFlag();
        }
        return isChanged? StringChatItemMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(StringChatItemMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, ChatItemPB> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromCs(entry.getValue());
        }
        this.markAll();
        return StringChatItemMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeChangeFromCs(StringChatItemMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, ChatItemPB> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromCs(entry.getValue());
                changeCnt = StringChatItemMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromCs(entry.getValue());
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringChatItemMap.Builder getCopyDbBuilder() {
        final StringChatItemMap.Builder builder = StringChatItemMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToDb(StringChatItemMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringChatItemMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, ChatItemProp> entry : this.entrySet()) {
            ChatItem.Builder itemBuilder = ChatItem.newBuilder();
            entry.getValue().copyToDb(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringChatItemMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToDb(StringChatItemMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final ChatItemProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ChatItem.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ChatItem.newBuilder();
            final int changeCnt = oldValue.copyChangeToDb(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringChatItemMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(StringChatItemMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, ChatItem> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromDb(entry.getValue());
        }
        this.markAll();
        return StringChatItemMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromDb(StringChatItemMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, ChatItem> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromDb(entry.getValue());
                changeCnt = StringChatItemMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromDb(entry.getValue());
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringChatItemMap.Builder getCopySsBuilder() {
        final StringChatItemMap.Builder builder = StringChatItemMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToSs(StringChatItemMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringChatItemMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, ChatItemProp> entry : this.entrySet()) {
            ChatItem.Builder itemBuilder = ChatItem.newBuilder();
            entry.getValue().copyToSs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringChatItemMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToSs(StringChatItemMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final ChatItemProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final ChatItem.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : ChatItem.newBuilder();
            final int changeCnt = oldValue.copyChangeToSs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringChatItemMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(StringChatItemMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, ChatItem> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromSs(entry.getValue());
        }
        this.markAll();
        return StringChatItemMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromSs(StringChatItemMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, ChatItem> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromSs(entry.getValue());
                changeCnt = StringChatItemMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromSs(entry.getValue());
            changeCnt = StringChatItemMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    @Override
    public String toString() {
        StringChatItemMap.Builder builder = StringChatItemMap.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
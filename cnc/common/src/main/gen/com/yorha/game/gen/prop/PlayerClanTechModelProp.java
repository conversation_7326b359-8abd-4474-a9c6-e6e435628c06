package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerClanTechModel;
import com.yorha.proto.PlayerPB.PlayerClanTechModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerClanTechModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CANRESDONATETIMES = 0;
    public static final int FIELD_INDEX_LASTREFRESHTSSEC = 1;
    public static final int FIELD_INDEX_DAILYDIAMONDDONATETIMES = 2;
    public static final int FIELD_INDEX_DAILYDONATEPOINT = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int canResDonateTimes = Constant.DEFAULT_INT_VALUE;
    private int lastRefreshTsSec = Constant.DEFAULT_INT_VALUE;
    private int dailyDiamondDonateTimes = Constant.DEFAULT_INT_VALUE;
    private int dailyDonatePoint = Constant.DEFAULT_INT_VALUE;

    public PlayerClanTechModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerClanTechModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get canResDonateTimes
     *
     * @return canResDonateTimes value
     */
    public int getCanResDonateTimes() {
        return this.canResDonateTimes;
    }

    /**
     * set canResDonateTimes && set marked
     *
     * @param canResDonateTimes new value
     * @return current object
     */
    public PlayerClanTechModelProp setCanResDonateTimes(int canResDonateTimes) {
        if (this.canResDonateTimes != canResDonateTimes) {
            this.mark(FIELD_INDEX_CANRESDONATETIMES);
            this.canResDonateTimes = canResDonateTimes;
        }
        return this;
    }

    /**
     * inner set canResDonateTimes
     *
     * @param canResDonateTimes new value
     */
    private void innerSetCanResDonateTimes(int canResDonateTimes) {
        this.canResDonateTimes = canResDonateTimes;
    }

    /**
     * get lastRefreshTsSec
     *
     * @return lastRefreshTsSec value
     */
    public int getLastRefreshTsSec() {
        return this.lastRefreshTsSec;
    }

    /**
     * set lastRefreshTsSec && set marked
     *
     * @param lastRefreshTsSec new value
     * @return current object
     */
    public PlayerClanTechModelProp setLastRefreshTsSec(int lastRefreshTsSec) {
        if (this.lastRefreshTsSec != lastRefreshTsSec) {
            this.mark(FIELD_INDEX_LASTREFRESHTSSEC);
            this.lastRefreshTsSec = lastRefreshTsSec;
        }
        return this;
    }

    /**
     * inner set lastRefreshTsSec
     *
     * @param lastRefreshTsSec new value
     */
    private void innerSetLastRefreshTsSec(int lastRefreshTsSec) {
        this.lastRefreshTsSec = lastRefreshTsSec;
    }

    /**
     * get dailyDiamondDonateTimes
     *
     * @return dailyDiamondDonateTimes value
     */
    public int getDailyDiamondDonateTimes() {
        return this.dailyDiamondDonateTimes;
    }

    /**
     * set dailyDiamondDonateTimes && set marked
     *
     * @param dailyDiamondDonateTimes new value
     * @return current object
     */
    public PlayerClanTechModelProp setDailyDiamondDonateTimes(int dailyDiamondDonateTimes) {
        if (this.dailyDiamondDonateTimes != dailyDiamondDonateTimes) {
            this.mark(FIELD_INDEX_DAILYDIAMONDDONATETIMES);
            this.dailyDiamondDonateTimes = dailyDiamondDonateTimes;
        }
        return this;
    }

    /**
     * inner set dailyDiamondDonateTimes
     *
     * @param dailyDiamondDonateTimes new value
     */
    private void innerSetDailyDiamondDonateTimes(int dailyDiamondDonateTimes) {
        this.dailyDiamondDonateTimes = dailyDiamondDonateTimes;
    }

    /**
     * get dailyDonatePoint
     *
     * @return dailyDonatePoint value
     */
    public int getDailyDonatePoint() {
        return this.dailyDonatePoint;
    }

    /**
     * set dailyDonatePoint && set marked
     *
     * @param dailyDonatePoint new value
     * @return current object
     */
    public PlayerClanTechModelProp setDailyDonatePoint(int dailyDonatePoint) {
        if (this.dailyDonatePoint != dailyDonatePoint) {
            this.mark(FIELD_INDEX_DAILYDONATEPOINT);
            this.dailyDonatePoint = dailyDonatePoint;
        }
        return this;
    }

    /**
     * inner set dailyDonatePoint
     *
     * @param dailyDonatePoint new value
     */
    private void innerSetDailyDonatePoint(int dailyDonatePoint) {
        this.dailyDonatePoint = dailyDonatePoint;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanTechModelPB.Builder getCopyCsBuilder() {
        final PlayerClanTechModelPB.Builder builder = PlayerClanTechModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerClanTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCanResDonateTimes() != 0) {
            builder.setCanResDonateTimes(this.getCanResDonateTimes());
            fieldCnt++;
        }  else if (builder.hasCanResDonateTimes()) {
            // 清理CanResDonateTimes
            builder.clearCanResDonateTimes();
            fieldCnt++;
        }
        if (this.getLastRefreshTsSec() != 0) {
            builder.setLastRefreshTsSec(this.getLastRefreshTsSec());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsSec()) {
            // 清理LastRefreshTsSec
            builder.clearLastRefreshTsSec();
            fieldCnt++;
        }
        if (this.getDailyDiamondDonateTimes() != 0) {
            builder.setDailyDiamondDonateTimes(this.getDailyDiamondDonateTimes());
            fieldCnt++;
        }  else if (builder.hasDailyDiamondDonateTimes()) {
            // 清理DailyDiamondDonateTimes
            builder.clearDailyDiamondDonateTimes();
            fieldCnt++;
        }
        if (this.getDailyDonatePoint() != 0) {
            builder.setDailyDonatePoint(this.getDailyDonatePoint());
            fieldCnt++;
        }  else if (builder.hasDailyDonatePoint()) {
            // 清理DailyDonatePoint
            builder.clearDailyDonatePoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerClanTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CANRESDONATETIMES)) {
            builder.setCanResDonateTimes(this.getCanResDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSSEC)) {
            builder.setLastRefreshTsSec(this.getLastRefreshTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDIAMONDDONATETIMES)) {
            builder.setDailyDiamondDonateTimes(this.getDailyDiamondDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDONATEPOINT)) {
            builder.setDailyDonatePoint(this.getDailyDonatePoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerClanTechModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CANRESDONATETIMES)) {
            builder.setCanResDonateTimes(this.getCanResDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSSEC)) {
            builder.setLastRefreshTsSec(this.getLastRefreshTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDIAMONDDONATETIMES)) {
            builder.setDailyDiamondDonateTimes(this.getDailyDiamondDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDONATEPOINT)) {
            builder.setDailyDonatePoint(this.getDailyDonatePoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerClanTechModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCanResDonateTimes()) {
            this.innerSetCanResDonateTimes(proto.getCanResDonateTimes());
        } else {
            this.innerSetCanResDonateTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastRefreshTsSec()) {
            this.innerSetLastRefreshTsSec(proto.getLastRefreshTsSec());
        } else {
            this.innerSetLastRefreshTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyDiamondDonateTimes()) {
            this.innerSetDailyDiamondDonateTimes(proto.getDailyDiamondDonateTimes());
        } else {
            this.innerSetDailyDiamondDonateTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyDonatePoint()) {
            this.innerSetDailyDonatePoint(proto.getDailyDonatePoint());
        } else {
            this.innerSetDailyDonatePoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerClanTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerClanTechModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCanResDonateTimes()) {
            this.setCanResDonateTimes(proto.getCanResDonateTimes());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsSec()) {
            this.setLastRefreshTsSec(proto.getLastRefreshTsSec());
            fieldCnt++;
        }
        if (proto.hasDailyDiamondDonateTimes()) {
            this.setDailyDiamondDonateTimes(proto.getDailyDiamondDonateTimes());
            fieldCnt++;
        }
        if (proto.hasDailyDonatePoint()) {
            this.setDailyDonatePoint(proto.getDailyDonatePoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanTechModel.Builder getCopyDbBuilder() {
        final PlayerClanTechModel.Builder builder = PlayerClanTechModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCanResDonateTimes() != 0) {
            builder.setCanResDonateTimes(this.getCanResDonateTimes());
            fieldCnt++;
        }  else if (builder.hasCanResDonateTimes()) {
            // 清理CanResDonateTimes
            builder.clearCanResDonateTimes();
            fieldCnt++;
        }
        if (this.getLastRefreshTsSec() != 0) {
            builder.setLastRefreshTsSec(this.getLastRefreshTsSec());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsSec()) {
            // 清理LastRefreshTsSec
            builder.clearLastRefreshTsSec();
            fieldCnt++;
        }
        if (this.getDailyDiamondDonateTimes() != 0) {
            builder.setDailyDiamondDonateTimes(this.getDailyDiamondDonateTimes());
            fieldCnt++;
        }  else if (builder.hasDailyDiamondDonateTimes()) {
            // 清理DailyDiamondDonateTimes
            builder.clearDailyDiamondDonateTimes();
            fieldCnt++;
        }
        if (this.getDailyDonatePoint() != 0) {
            builder.setDailyDonatePoint(this.getDailyDonatePoint());
            fieldCnt++;
        }  else if (builder.hasDailyDonatePoint()) {
            // 清理DailyDonatePoint
            builder.clearDailyDonatePoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CANRESDONATETIMES)) {
            builder.setCanResDonateTimes(this.getCanResDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSSEC)) {
            builder.setLastRefreshTsSec(this.getLastRefreshTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDIAMONDDONATETIMES)) {
            builder.setDailyDiamondDonateTimes(this.getDailyDiamondDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDONATEPOINT)) {
            builder.setDailyDonatePoint(this.getDailyDonatePoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerClanTechModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCanResDonateTimes()) {
            this.innerSetCanResDonateTimes(proto.getCanResDonateTimes());
        } else {
            this.innerSetCanResDonateTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastRefreshTsSec()) {
            this.innerSetLastRefreshTsSec(proto.getLastRefreshTsSec());
        } else {
            this.innerSetLastRefreshTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyDiamondDonateTimes()) {
            this.innerSetDailyDiamondDonateTimes(proto.getDailyDiamondDonateTimes());
        } else {
            this.innerSetDailyDiamondDonateTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyDonatePoint()) {
            this.innerSetDailyDonatePoint(proto.getDailyDonatePoint());
        } else {
            this.innerSetDailyDonatePoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerClanTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerClanTechModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCanResDonateTimes()) {
            this.setCanResDonateTimes(proto.getCanResDonateTimes());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsSec()) {
            this.setLastRefreshTsSec(proto.getLastRefreshTsSec());
            fieldCnt++;
        }
        if (proto.hasDailyDiamondDonateTimes()) {
            this.setDailyDiamondDonateTimes(proto.getDailyDiamondDonateTimes());
            fieldCnt++;
        }
        if (proto.hasDailyDonatePoint()) {
            this.setDailyDonatePoint(proto.getDailyDonatePoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerClanTechModel.Builder getCopySsBuilder() {
        final PlayerClanTechModel.Builder builder = PlayerClanTechModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCanResDonateTimes() != 0) {
            builder.setCanResDonateTimes(this.getCanResDonateTimes());
            fieldCnt++;
        }  else if (builder.hasCanResDonateTimes()) {
            // 清理CanResDonateTimes
            builder.clearCanResDonateTimes();
            fieldCnt++;
        }
        if (this.getLastRefreshTsSec() != 0) {
            builder.setLastRefreshTsSec(this.getLastRefreshTsSec());
            fieldCnt++;
        }  else if (builder.hasLastRefreshTsSec()) {
            // 清理LastRefreshTsSec
            builder.clearLastRefreshTsSec();
            fieldCnt++;
        }
        if (this.getDailyDiamondDonateTimes() != 0) {
            builder.setDailyDiamondDonateTimes(this.getDailyDiamondDonateTimes());
            fieldCnt++;
        }  else if (builder.hasDailyDiamondDonateTimes()) {
            // 清理DailyDiamondDonateTimes
            builder.clearDailyDiamondDonateTimes();
            fieldCnt++;
        }
        if (this.getDailyDonatePoint() != 0) {
            builder.setDailyDonatePoint(this.getDailyDonatePoint());
            fieldCnt++;
        }  else if (builder.hasDailyDonatePoint()) {
            // 清理DailyDonatePoint
            builder.clearDailyDonatePoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerClanTechModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CANRESDONATETIMES)) {
            builder.setCanResDonateTimes(this.getCanResDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHTSSEC)) {
            builder.setLastRefreshTsSec(this.getLastRefreshTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDIAMONDDONATETIMES)) {
            builder.setDailyDiamondDonateTimes(this.getDailyDiamondDonateTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYDONATEPOINT)) {
            builder.setDailyDonatePoint(this.getDailyDonatePoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerClanTechModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCanResDonateTimes()) {
            this.innerSetCanResDonateTimes(proto.getCanResDonateTimes());
        } else {
            this.innerSetCanResDonateTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastRefreshTsSec()) {
            this.innerSetLastRefreshTsSec(proto.getLastRefreshTsSec());
        } else {
            this.innerSetLastRefreshTsSec(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyDiamondDonateTimes()) {
            this.innerSetDailyDiamondDonateTimes(proto.getDailyDiamondDonateTimes());
        } else {
            this.innerSetDailyDiamondDonateTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyDonatePoint()) {
            this.innerSetDailyDonatePoint(proto.getDailyDonatePoint());
        } else {
            this.innerSetDailyDonatePoint(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerClanTechModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerClanTechModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCanResDonateTimes()) {
            this.setCanResDonateTimes(proto.getCanResDonateTimes());
            fieldCnt++;
        }
        if (proto.hasLastRefreshTsSec()) {
            this.setLastRefreshTsSec(proto.getLastRefreshTsSec());
            fieldCnt++;
        }
        if (proto.hasDailyDiamondDonateTimes()) {
            this.setDailyDiamondDonateTimes(proto.getDailyDiamondDonateTimes());
            fieldCnt++;
        }
        if (proto.hasDailyDonatePoint()) {
            this.setDailyDonatePoint(proto.getDailyDonatePoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerClanTechModel.Builder builder = PlayerClanTechModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerClanTechModelProp)) {
            return false;
        }
        final PlayerClanTechModelProp otherNode = (PlayerClanTechModelProp) node;
        if (this.canResDonateTimes != otherNode.canResDonateTimes) {
            return false;
        }
        if (this.lastRefreshTsSec != otherNode.lastRefreshTsSec) {
            return false;
        }
        if (this.dailyDiamondDonateTimes != otherNode.dailyDiamondDonateTimes) {
            return false;
        }
        if (this.dailyDonatePoint != otherNode.dailyDonatePoint) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActLotteryDrawData;
import com.yorha.proto.StructPB.ActLotteryDrawDataPB;


/**
 * <AUTHOR> auto gen
 */
public class ActLotteryDrawDataProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_DRAWID = 0;
    public static final int FIELD_INDEX_DRAWTIMES = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int drawId = Constant.DEFAULT_INT_VALUE;
    private int drawTimes = Constant.DEFAULT_INT_VALUE;

    public ActLotteryDrawDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActLotteryDrawDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get drawId
     *
     * @return drawId value
     */
    public int getDrawId() {
        return this.drawId;
    }

    /**
     * set drawId && set marked
     *
     * @param drawId new value
     * @return current object
     */
    public ActLotteryDrawDataProp setDrawId(int drawId) {
        if (this.drawId != drawId) {
            this.mark(FIELD_INDEX_DRAWID);
            this.drawId = drawId;
        }
        return this;
    }

    /**
     * inner set drawId
     *
     * @param drawId new value
     */
    private void innerSetDrawId(int drawId) {
        this.drawId = drawId;
    }

    /**
     * get drawTimes
     *
     * @return drawTimes value
     */
    public int getDrawTimes() {
        return this.drawTimes;
    }

    /**
     * set drawTimes && set marked
     *
     * @param drawTimes new value
     * @return current object
     */
    public ActLotteryDrawDataProp setDrawTimes(int drawTimes) {
        if (this.drawTimes != drawTimes) {
            this.mark(FIELD_INDEX_DRAWTIMES);
            this.drawTimes = drawTimes;
        }
        return this;
    }

    /**
     * inner set drawTimes
     *
     * @param drawTimes new value
     */
    private void innerSetDrawTimes(int drawTimes) {
        this.drawTimes = drawTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActLotteryDrawDataPB.Builder getCopyCsBuilder() {
        final ActLotteryDrawDataPB.Builder builder = ActLotteryDrawDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActLotteryDrawDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDrawId() != 0) {
            builder.setDrawId(this.getDrawId());
            fieldCnt++;
        }  else if (builder.hasDrawId()) {
            // 清理DrawId
            builder.clearDrawId();
            fieldCnt++;
        }
        if (this.getDrawTimes() != 0) {
            builder.setDrawTimes(this.getDrawTimes());
            fieldCnt++;
        }  else if (builder.hasDrawTimes()) {
            // 清理DrawTimes
            builder.clearDrawTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActLotteryDrawDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWID)) {
            builder.setDrawId(this.getDrawId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRAWTIMES)) {
            builder.setDrawTimes(this.getDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActLotteryDrawDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWID)) {
            builder.setDrawId(this.getDrawId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRAWTIMES)) {
            builder.setDrawTimes(this.getDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActLotteryDrawDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDrawId()) {
            this.innerSetDrawId(proto.getDrawId());
        } else {
            this.innerSetDrawId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDrawTimes()) {
            this.innerSetDrawTimes(proto.getDrawTimes());
        } else {
            this.innerSetDrawTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActLotteryDrawDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActLotteryDrawDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDrawId()) {
            this.setDrawId(proto.getDrawId());
            fieldCnt++;
        }
        if (proto.hasDrawTimes()) {
            this.setDrawTimes(proto.getDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActLotteryDrawData.Builder getCopyDbBuilder() {
        final ActLotteryDrawData.Builder builder = ActLotteryDrawData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActLotteryDrawData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDrawId() != 0) {
            builder.setDrawId(this.getDrawId());
            fieldCnt++;
        }  else if (builder.hasDrawId()) {
            // 清理DrawId
            builder.clearDrawId();
            fieldCnt++;
        }
        if (this.getDrawTimes() != 0) {
            builder.setDrawTimes(this.getDrawTimes());
            fieldCnt++;
        }  else if (builder.hasDrawTimes()) {
            // 清理DrawTimes
            builder.clearDrawTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActLotteryDrawData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWID)) {
            builder.setDrawId(this.getDrawId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRAWTIMES)) {
            builder.setDrawTimes(this.getDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActLotteryDrawData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDrawId()) {
            this.innerSetDrawId(proto.getDrawId());
        } else {
            this.innerSetDrawId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDrawTimes()) {
            this.innerSetDrawTimes(proto.getDrawTimes());
        } else {
            this.innerSetDrawTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActLotteryDrawDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActLotteryDrawData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDrawId()) {
            this.setDrawId(proto.getDrawId());
            fieldCnt++;
        }
        if (proto.hasDrawTimes()) {
            this.setDrawTimes(proto.getDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActLotteryDrawData.Builder getCopySsBuilder() {
        final ActLotteryDrawData.Builder builder = ActLotteryDrawData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActLotteryDrawData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDrawId() != 0) {
            builder.setDrawId(this.getDrawId());
            fieldCnt++;
        }  else if (builder.hasDrawId()) {
            // 清理DrawId
            builder.clearDrawId();
            fieldCnt++;
        }
        if (this.getDrawTimes() != 0) {
            builder.setDrawTimes(this.getDrawTimes());
            fieldCnt++;
        }  else if (builder.hasDrawTimes()) {
            // 清理DrawTimes
            builder.clearDrawTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActLotteryDrawData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWID)) {
            builder.setDrawId(this.getDrawId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DRAWTIMES)) {
            builder.setDrawTimes(this.getDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActLotteryDrawData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDrawId()) {
            this.innerSetDrawId(proto.getDrawId());
        } else {
            this.innerSetDrawId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDrawTimes()) {
            this.innerSetDrawTimes(proto.getDrawTimes());
        } else {
            this.innerSetDrawTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActLotteryDrawDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActLotteryDrawData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDrawId()) {
            this.setDrawId(proto.getDrawId());
            fieldCnt++;
        }
        if (proto.hasDrawTimes()) {
            this.setDrawTimes(proto.getDrawTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActLotteryDrawData.Builder builder = ActLotteryDrawData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.drawId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActLotteryDrawDataProp)) {
            return false;
        }
        final ActLotteryDrawDataProp otherNode = (ActLotteryDrawDataProp) node;
        if (this.drawId != otherNode.drawId) {
            return false;
        }
        if (this.drawTimes != otherNode.drawTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
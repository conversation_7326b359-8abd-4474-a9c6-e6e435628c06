package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructMailPB.MailUnreadCountListPB;
import com.yorha.proto.StructMail.MailUnreadCountList;
import com.yorha.proto.StructMailPB.MailUnreadCountPB;
import com.yorha.proto.StructMail.MailUnreadCount;

/**
 * <AUTHOR> auto gen
 */
public class MailUnreadCountListProp extends AbstractListNode<MailUnreadCountProp> {
    /**
     * Create a MailUnreadCountListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public MailUnreadCountListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to MailUnreadCountListProp
     *
     * @return new object
     */
    @Override
    public MailUnreadCountProp addEmptyValue() {
        final MailUnreadCountProp newProp = new MailUnreadCountProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailUnreadCountListPB.Builder getCopyCsBuilder() {
        final MailUnreadCountListPB.Builder builder = MailUnreadCountListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(MailUnreadCountListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return MailUnreadCountListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final MailUnreadCountProp v : this) {
            final MailUnreadCountPB.Builder itemBuilder = MailUnreadCountPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return MailUnreadCountListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(MailUnreadCountListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return MailUnreadCountListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(MailUnreadCountListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (MailUnreadCountPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return MailUnreadCountListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(MailUnreadCountListPB proto) {
        return mergeFromCs(proto);
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailUnreadCountList.Builder getCopySsBuilder() {
        final MailUnreadCountList.Builder builder = MailUnreadCountList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(MailUnreadCountList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return MailUnreadCountListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final MailUnreadCountProp v : this) {
            final MailUnreadCount.Builder itemBuilder = MailUnreadCount.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return MailUnreadCountListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(MailUnreadCountList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return MailUnreadCountListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(MailUnreadCountList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (MailUnreadCount v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return MailUnreadCountListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(MailUnreadCountList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        MailUnreadCountList.Builder builder = MailUnreadCountList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
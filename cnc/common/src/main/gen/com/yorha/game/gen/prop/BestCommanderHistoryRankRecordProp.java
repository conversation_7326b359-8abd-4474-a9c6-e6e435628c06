package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zoneside.BestCommanderHistoryRankRecord;
import com.yorha.proto.ZonesidePB.BestCommanderHistoryRankRecordPB;


/**
 * <AUTHOR> auto gen
 */
public class BestCommanderHistoryRankRecordProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_AGE = 0;
    public static final int FIELD_INDEX_HISTORYRANKBYTES = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int age = Constant.DEFAULT_INT_VALUE;
    private com.google.protobuf.ByteString historyRankBytes = Constant.DEFAULT_BYTE_STRING_VALUE;

    public BestCommanderHistoryRankRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BestCommanderHistoryRankRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get age
     *
     * @return age value
     */
    public int getAge() {
        return this.age;
    }

    /**
     * set age && set marked
     *
     * @param age new value
     * @return current object
     */
    public BestCommanderHistoryRankRecordProp setAge(int age) {
        if (this.age != age) {
            this.mark(FIELD_INDEX_AGE);
            this.age = age;
        }
        return this;
    }

    /**
     * inner set age
     *
     * @param age new value
     */
    private void innerSetAge(int age) {
        this.age = age;
    }

    /**
     * get historyRankBytes
     *
     * @return historyRankBytes value
     */
    public com.google.protobuf.ByteString getHistoryRankBytes() {
        return this.historyRankBytes;
    }

    /**
     * set historyRankBytes && set marked
     *
     * @param historyRankBytes new value
     * @return current object
     */
    public BestCommanderHistoryRankRecordProp setHistoryRankBytes(com.google.protobuf.ByteString historyRankBytes) {
        if (this.historyRankBytes != historyRankBytes) {
            this.mark(FIELD_INDEX_HISTORYRANKBYTES);
            this.historyRankBytes = historyRankBytes;
        }
        return this;
    }

    /**
     * inner set historyRankBytes
     *
     * @param historyRankBytes new value
     */
    private void innerSetHistoryRankBytes(com.google.protobuf.ByteString historyRankBytes) {
        this.historyRankBytes = historyRankBytes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BestCommanderHistoryRankRecordPB.Builder getCopyCsBuilder() {
        final BestCommanderHistoryRankRecordPB.Builder builder = BestCommanderHistoryRankRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BestCommanderHistoryRankRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAge() != 0) {
            builder.setAge(this.getAge());
            fieldCnt++;
        }  else if (builder.hasAge()) {
            // 清理Age
            builder.clearAge();
            fieldCnt++;
        }
        if (this.getHistoryRankBytes() != com.google.protobuf.ByteString.EMPTY) {
            builder.setHistoryRankBytes(this.getHistoryRankBytes());
            fieldCnt++;
        }  else if (builder.hasHistoryRankBytes()) {
            // 清理HistoryRankBytes
            builder.clearHistoryRankBytes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BestCommanderHistoryRankRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AGE)) {
            builder.setAge(this.getAge());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HISTORYRANKBYTES)) {
            builder.setHistoryRankBytes(this.getHistoryRankBytes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BestCommanderHistoryRankRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AGE)) {
            builder.setAge(this.getAge());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HISTORYRANKBYTES)) {
            builder.setHistoryRankBytes(this.getHistoryRankBytes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BestCommanderHistoryRankRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAge()) {
            this.innerSetAge(proto.getAge());
        } else {
            this.innerSetAge(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHistoryRankBytes()) {
            this.innerSetHistoryRankBytes(proto.getHistoryRankBytes());
        } else {
            this.innerSetHistoryRankBytes(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        this.markAll();
        return BestCommanderHistoryRankRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BestCommanderHistoryRankRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAge()) {
            this.setAge(proto.getAge());
            fieldCnt++;
        }
        if (proto.hasHistoryRankBytes()) {
            this.setHistoryRankBytes(proto.getHistoryRankBytes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BestCommanderHistoryRankRecord.Builder getCopyDbBuilder() {
        final BestCommanderHistoryRankRecord.Builder builder = BestCommanderHistoryRankRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BestCommanderHistoryRankRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAge() != 0) {
            builder.setAge(this.getAge());
            fieldCnt++;
        }  else if (builder.hasAge()) {
            // 清理Age
            builder.clearAge();
            fieldCnt++;
        }
        if (this.getHistoryRankBytes() != com.google.protobuf.ByteString.EMPTY) {
            builder.setHistoryRankBytes(this.getHistoryRankBytes());
            fieldCnt++;
        }  else if (builder.hasHistoryRankBytes()) {
            // 清理HistoryRankBytes
            builder.clearHistoryRankBytes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BestCommanderHistoryRankRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AGE)) {
            builder.setAge(this.getAge());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HISTORYRANKBYTES)) {
            builder.setHistoryRankBytes(this.getHistoryRankBytes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BestCommanderHistoryRankRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAge()) {
            this.innerSetAge(proto.getAge());
        } else {
            this.innerSetAge(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHistoryRankBytes()) {
            this.innerSetHistoryRankBytes(proto.getHistoryRankBytes());
        } else {
            this.innerSetHistoryRankBytes(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        this.markAll();
        return BestCommanderHistoryRankRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BestCommanderHistoryRankRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAge()) {
            this.setAge(proto.getAge());
            fieldCnt++;
        }
        if (proto.hasHistoryRankBytes()) {
            this.setHistoryRankBytes(proto.getHistoryRankBytes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BestCommanderHistoryRankRecord.Builder getCopySsBuilder() {
        final BestCommanderHistoryRankRecord.Builder builder = BestCommanderHistoryRankRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BestCommanderHistoryRankRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAge() != 0) {
            builder.setAge(this.getAge());
            fieldCnt++;
        }  else if (builder.hasAge()) {
            // 清理Age
            builder.clearAge();
            fieldCnt++;
        }
        if (this.getHistoryRankBytes() != com.google.protobuf.ByteString.EMPTY) {
            builder.setHistoryRankBytes(this.getHistoryRankBytes());
            fieldCnt++;
        }  else if (builder.hasHistoryRankBytes()) {
            // 清理HistoryRankBytes
            builder.clearHistoryRankBytes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BestCommanderHistoryRankRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AGE)) {
            builder.setAge(this.getAge());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HISTORYRANKBYTES)) {
            builder.setHistoryRankBytes(this.getHistoryRankBytes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BestCommanderHistoryRankRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAge()) {
            this.innerSetAge(proto.getAge());
        } else {
            this.innerSetAge(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHistoryRankBytes()) {
            this.innerSetHistoryRankBytes(proto.getHistoryRankBytes());
        } else {
            this.innerSetHistoryRankBytes(Constant.DEFAULT_BYTE_STRING_VALUE);
        }
        this.markAll();
        return BestCommanderHistoryRankRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BestCommanderHistoryRankRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAge()) {
            this.setAge(proto.getAge());
            fieldCnt++;
        }
        if (proto.hasHistoryRankBytes()) {
            this.setHistoryRankBytes(proto.getHistoryRankBytes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BestCommanderHistoryRankRecord.Builder builder = BestCommanderHistoryRankRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.age;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BestCommanderHistoryRankRecordProp)) {
            return false;
        }
        final BestCommanderHistoryRankRecordProp otherNode = (BestCommanderHistoryRankRecordProp) node;
        if (this.age != otherNode.age) {
            return false;
        }
        if (this.historyRankBytes != otherNode.historyRankBytes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
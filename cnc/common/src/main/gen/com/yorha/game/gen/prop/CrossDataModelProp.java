package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructCommon.CrossDataModel;
import com.yorha.proto.StructCommon;
import com.yorha.proto.StructCommonPB.CrossDataModelPB;
import com.yorha.proto.StructCommonPB;


/**
 * <AUTHOR> auto gen
 */
public class CrossDataModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DATA = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32CrossDataMapProp data = null;

    public CrossDataModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CrossDataModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get data
     *
     * @return data value
     */
    public Int32CrossDataMapProp getData() {
        if (this.data == null) {
            this.data = new Int32CrossDataMapProp(this, FIELD_INDEX_DATA);
        }
        return this.data;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDataV(CrossDataProp v) {
        this.getData().put(v.getPartId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CrossDataProp addEmptyData(Integer k) {
        return this.getData().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDataSize() {
        if (this.data == null) {
            return 0;
        }
        return this.data.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDataEmpty() {
        if (this.data == null) {
            return true;
        }
        return this.data.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CrossDataProp getDataV(Integer k) {
        if (this.data == null || !this.data.containsKey(k)) {
            return null;
        }
        return this.data.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearData() {
        if (this.data != null) {
            this.data.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDataV(Integer k) {
        if (this.data != null) {
            this.data.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CrossDataModelPB.Builder getCopyCsBuilder() {
        final CrossDataModelPB.Builder builder = CrossDataModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CrossDataModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.data != null) {
            StructCommonPB.Int32CrossDataMapPB.Builder tmpBuilder = StructCommonPB.Int32CrossDataMapPB.newBuilder();
            final int tmpFieldCnt = this.data.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearData();
            }
        }  else if (builder.hasData()) {
            // 清理Data
            builder.clearData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CrossDataModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            final boolean needClear = !builder.hasData();
            final int tmpFieldCnt = this.data.copyChangeToCs(builder.getDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearData();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CrossDataModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            final boolean needClear = !builder.hasData();
            final int tmpFieldCnt = this.data.copyChangeToAndClearDeleteKeysCs(builder.getDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CrossDataModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasData()) {
            this.getData().mergeFromCs(proto.getData());
        } else {
            if (this.data != null) {
                this.data.mergeFromCs(proto.getData());
            }
        }
        this.markAll();
        return CrossDataModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CrossDataModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasData()) {
            this.getData().mergeChangeFromCs(proto.getData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CrossDataModel.Builder getCopyDbBuilder() {
        final CrossDataModel.Builder builder = CrossDataModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CrossDataModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.data != null) {
            StructCommon.Int32CrossDataMap.Builder tmpBuilder = StructCommon.Int32CrossDataMap.newBuilder();
            final int tmpFieldCnt = this.data.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearData();
            }
        }  else if (builder.hasData()) {
            // 清理Data
            builder.clearData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CrossDataModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            final boolean needClear = !builder.hasData();
            final int tmpFieldCnt = this.data.copyChangeToDb(builder.getDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CrossDataModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasData()) {
            this.getData().mergeFromDb(proto.getData());
        } else {
            if (this.data != null) {
                this.data.mergeFromDb(proto.getData());
            }
        }
        this.markAll();
        return CrossDataModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CrossDataModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasData()) {
            this.getData().mergeChangeFromDb(proto.getData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CrossDataModel.Builder getCopySsBuilder() {
        final CrossDataModel.Builder builder = CrossDataModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CrossDataModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.data != null) {
            StructCommon.Int32CrossDataMap.Builder tmpBuilder = StructCommon.Int32CrossDataMap.newBuilder();
            final int tmpFieldCnt = this.data.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearData();
            }
        }  else if (builder.hasData()) {
            // 清理Data
            builder.clearData();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CrossDataModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            final boolean needClear = !builder.hasData();
            final int tmpFieldCnt = this.data.copyChangeToSs(builder.getDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearData();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CrossDataModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasData()) {
            this.getData().mergeFromSs(proto.getData());
        } else {
            if (this.data != null) {
                this.data.mergeFromSs(proto.getData());
            }
        }
        this.markAll();
        return CrossDataModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CrossDataModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasData()) {
            this.getData().mergeChangeFromSs(proto.getData());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CrossDataModel.Builder builder = CrossDataModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DATA) && this.data != null) {
            this.data.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.data != null) {
            this.data.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CrossDataModelProp)) {
            return false;
        }
        final CrossDataModelProp otherNode = (CrossDataModelProp) node;
        if (!this.getData().compareDataTo(otherNode.getData())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
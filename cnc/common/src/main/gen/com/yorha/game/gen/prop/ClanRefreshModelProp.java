package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanRefreshModel;
import com.yorha.proto.ClanPB.ClanRefreshModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanRefreshModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DAYREFRESHTSMS = 0;
    public static final int FIELD_INDEX_WEEKREFRESHTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long dayRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private long weekRefreshTsMs = Constant.DEFAULT_LONG_VALUE;

    public ClanRefreshModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanRefreshModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dayRefreshTsMs
     *
     * @return dayRefreshTsMs value
     */
    public long getDayRefreshTsMs() {
        return this.dayRefreshTsMs;
    }

    /**
     * set dayRefreshTsMs && set marked
     *
     * @param dayRefreshTsMs new value
     * @return current object
     */
    public ClanRefreshModelProp setDayRefreshTsMs(long dayRefreshTsMs) {
        if (this.dayRefreshTsMs != dayRefreshTsMs) {
            this.mark(FIELD_INDEX_DAYREFRESHTSMS);
            this.dayRefreshTsMs = dayRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set dayRefreshTsMs
     *
     * @param dayRefreshTsMs new value
     */
    private void innerSetDayRefreshTsMs(long dayRefreshTsMs) {
        this.dayRefreshTsMs = dayRefreshTsMs;
    }

    /**
     * get weekRefreshTsMs
     *
     * @return weekRefreshTsMs value
     */
    public long getWeekRefreshTsMs() {
        return this.weekRefreshTsMs;
    }

    /**
     * set weekRefreshTsMs && set marked
     *
     * @param weekRefreshTsMs new value
     * @return current object
     */
    public ClanRefreshModelProp setWeekRefreshTsMs(long weekRefreshTsMs) {
        if (this.weekRefreshTsMs != weekRefreshTsMs) {
            this.mark(FIELD_INDEX_WEEKREFRESHTSMS);
            this.weekRefreshTsMs = weekRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set weekRefreshTsMs
     *
     * @param weekRefreshTsMs new value
     */
    private void innerSetWeekRefreshTsMs(long weekRefreshTsMs) {
        this.weekRefreshTsMs = weekRefreshTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanRefreshModelPB.Builder getCopyCsBuilder() {
        final ClanRefreshModelPB.Builder builder = ClanRefreshModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanRefreshModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanRefreshModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanRefreshModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanRefreshModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        this.markAll();
        return ClanRefreshModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanRefreshModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanRefreshModel.Builder getCopyDbBuilder() {
        final ClanRefreshModel.Builder builder = ClanRefreshModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanRefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDayRefreshTsMs() != 0L) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasDayRefreshTsMs()) {
            // 清理DayRefreshTsMs
            builder.clearDayRefreshTsMs();
            fieldCnt++;
        }
        if (this.getWeekRefreshTsMs() != 0L) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasWeekRefreshTsMs()) {
            // 清理WeekRefreshTsMs
            builder.clearWeekRefreshTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanRefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAYREFRESHTSMS)) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WEEKREFRESHTSMS)) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanRefreshModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDayRefreshTsMs()) {
            this.innerSetDayRefreshTsMs(proto.getDayRefreshTsMs());
        } else {
            this.innerSetDayRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.innerSetWeekRefreshTsMs(proto.getWeekRefreshTsMs());
        } else {
            this.innerSetWeekRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanRefreshModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanRefreshModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDayRefreshTsMs()) {
            this.setDayRefreshTsMs(proto.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.setWeekRefreshTsMs(proto.getWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanRefreshModel.Builder getCopySsBuilder() {
        final ClanRefreshModel.Builder builder = ClanRefreshModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanRefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDayRefreshTsMs() != 0L) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasDayRefreshTsMs()) {
            // 清理DayRefreshTsMs
            builder.clearDayRefreshTsMs();
            fieldCnt++;
        }
        if (this.getWeekRefreshTsMs() != 0L) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasWeekRefreshTsMs()) {
            // 清理WeekRefreshTsMs
            builder.clearWeekRefreshTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanRefreshModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DAYREFRESHTSMS)) {
            builder.setDayRefreshTsMs(this.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_WEEKREFRESHTSMS)) {
            builder.setWeekRefreshTsMs(this.getWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanRefreshModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDayRefreshTsMs()) {
            this.innerSetDayRefreshTsMs(proto.getDayRefreshTsMs());
        } else {
            this.innerSetDayRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.innerSetWeekRefreshTsMs(proto.getWeekRefreshTsMs());
        } else {
            this.innerSetWeekRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanRefreshModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanRefreshModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDayRefreshTsMs()) {
            this.setDayRefreshTsMs(proto.getDayRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasWeekRefreshTsMs()) {
            this.setWeekRefreshTsMs(proto.getWeekRefreshTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanRefreshModel.Builder builder = ClanRefreshModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanRefreshModelProp)) {
            return false;
        }
        final ClanRefreshModelProp otherNode = (ClanRefreshModelProp) node;
        if (this.dayRefreshTsMs != otherNode.dayRefreshTsMs) {
            return false;
        }
        if (this.weekRefreshTsMs != otherNode.weekRefreshTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.SimpleSkillListPB;
import com.yorha.proto.Struct.SimpleSkillList;
import com.yorha.proto.StructPB.SimpleSkillPB;
import com.yorha.proto.Struct.SimpleSkill;

/**
 * <AUTHOR> auto gen
 */
public class SimpleSkillListProp extends AbstractListNode<SimpleSkillProp> {
    /**
     * Create a SimpleSkillListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public SimpleSkillListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to SimpleSkillListProp
     *
     * @return new object
     */
    @Override
    public SimpleSkillProp addEmptyValue() {
        final SimpleSkillProp newProp = new SimpleSkillProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleSkillListPB.Builder getCopyCsBuilder() {
        final SimpleSkillListPB.Builder builder = SimpleSkillListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(SimpleSkillListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SimpleSkillListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SimpleSkillProp v : this) {
            final SimpleSkillPB.Builder itemBuilder = SimpleSkillPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SimpleSkillListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(SimpleSkillListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return SimpleSkillListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(SimpleSkillListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SimpleSkillPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return SimpleSkillListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(SimpleSkillListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleSkillList.Builder getCopyDbBuilder() {
        final SimpleSkillList.Builder builder = SimpleSkillList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(SimpleSkillList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SimpleSkillListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SimpleSkillProp v : this) {
            final SimpleSkill.Builder itemBuilder = SimpleSkill.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SimpleSkillListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(SimpleSkillList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return SimpleSkillListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(SimpleSkillList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SimpleSkill v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return SimpleSkillListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(SimpleSkillList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SimpleSkillList.Builder getCopySsBuilder() {
        final SimpleSkillList.Builder builder = SimpleSkillList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(SimpleSkillList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return SimpleSkillListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final SimpleSkillProp v : this) {
            final SimpleSkill.Builder itemBuilder = SimpleSkill.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return SimpleSkillListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(SimpleSkillList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return SimpleSkillListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(SimpleSkillList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (SimpleSkill v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return SimpleSkillListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(SimpleSkillList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        SimpleSkillList.Builder builder = SimpleSkillList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructSkynet.SkynetBossTask;
import com.yorha.proto.StructSkynet;
import com.yorha.proto.StructSkynetPB.SkynetBossTaskPB;
import com.yorha.proto.StructSkynetPB;


/**
 * <AUTHOR> auto gen
 */
public class SkynetBossTaskProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BOSSTASK = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private SkynetTaskProp bossTask = null;

    public SkynetBossTaskProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SkynetBossTaskProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get bossTask
     *
     * @return bossTask value
     */
    public SkynetTaskProp getBossTask() {
        if (this.bossTask == null) {
            this.bossTask = new SkynetTaskProp(this, FIELD_INDEX_BOSSTASK);
        }
        return this.bossTask;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetBossTaskPB.Builder getCopyCsBuilder() {
        final SkynetBossTaskPB.Builder builder = SkynetBossTaskPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SkynetBossTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bossTask != null) {
            StructSkynetPB.SkynetTaskPB.Builder tmpBuilder = StructSkynetPB.SkynetTaskPB.newBuilder();
            final int tmpFieldCnt = this.bossTask.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBossTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBossTask();
            }
        }  else if (builder.hasBossTask()) {
            // 清理BossTask
            builder.clearBossTask();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SkynetBossTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToCs(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SkynetBossTaskPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToAndClearDeleteKeysCs(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SkynetBossTaskPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeFromCs(proto.getBossTask());
        } else {
            if (this.bossTask != null) {
                this.bossTask.mergeFromCs(proto.getBossTask());
            }
        }
        this.markAll();
        return SkynetBossTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SkynetBossTaskPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBossTask()) {
            this.getBossTask().mergeChangeFromCs(proto.getBossTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetBossTask.Builder getCopyDbBuilder() {
        final SkynetBossTask.Builder builder = SkynetBossTask.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SkynetBossTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bossTask != null) {
            StructSkynet.SkynetTask.Builder tmpBuilder = StructSkynet.SkynetTask.newBuilder();
            final int tmpFieldCnt = this.bossTask.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBossTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBossTask();
            }
        }  else if (builder.hasBossTask()) {
            // 清理BossTask
            builder.clearBossTask();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SkynetBossTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToDb(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SkynetBossTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeFromDb(proto.getBossTask());
        } else {
            if (this.bossTask != null) {
                this.bossTask.mergeFromDb(proto.getBossTask());
            }
        }
        this.markAll();
        return SkynetBossTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SkynetBossTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBossTask()) {
            this.getBossTask().mergeChangeFromDb(proto.getBossTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetBossTask.Builder getCopySsBuilder() {
        final SkynetBossTask.Builder builder = SkynetBossTask.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SkynetBossTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bossTask != null) {
            StructSkynet.SkynetTask.Builder tmpBuilder = StructSkynet.SkynetTask.newBuilder();
            final int tmpFieldCnt = this.bossTask.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBossTask(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBossTask();
            }
        }  else if (builder.hasBossTask()) {
            // 清理BossTask
            builder.clearBossTask();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SkynetBossTask.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            final boolean needClear = !builder.hasBossTask();
            final int tmpFieldCnt = this.bossTask.copyChangeToSs(builder.getBossTaskBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBossTask();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SkynetBossTask proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBossTask()) {
            this.getBossTask().mergeFromSs(proto.getBossTask());
        } else {
            if (this.bossTask != null) {
                this.bossTask.mergeFromSs(proto.getBossTask());
            }
        }
        this.markAll();
        return SkynetBossTaskProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SkynetBossTask proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBossTask()) {
            this.getBossTask().mergeChangeFromSs(proto.getBossTask());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SkynetBossTask.Builder builder = SkynetBossTask.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BOSSTASK) && this.bossTask != null) {
            this.bossTask.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.bossTask != null) {
            this.bossTask.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SkynetBossTaskProp)) {
            return false;
        }
        final SkynetBossTaskProp otherNode = (SkynetBossTaskProp) node;
        if (!this.getBossTask().compareDataTo(otherNode.getBossTask())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
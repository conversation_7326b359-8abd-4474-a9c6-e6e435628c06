package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.BigSceneRecords;
import com.yorha.proto.PlayerPB.BigSceneRecordsPB;


/**
 * <AUTHOR> auto gen
 */
public class BigSceneRecordsProp extends AbstractPropNode {

    public static final int FIELD_INDEX_AUTOADDMONSTERCOUNT = 0;
    public static final int FIELD_INDEX_ABLEKILLMONSTERLIMIT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int autoAddMonsterCount = Constant.DEFAULT_INT_VALUE;
    private int ableKillMonsterLimit = Constant.DEFAULT_INT_VALUE;

    public BigSceneRecordsProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BigSceneRecordsProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get autoAddMonsterCount
     *
     * @return autoAddMonsterCount value
     */
    public int getAutoAddMonsterCount() {
        return this.autoAddMonsterCount;
    }

    /**
     * set autoAddMonsterCount && set marked
     *
     * @param autoAddMonsterCount new value
     * @return current object
     */
    public BigSceneRecordsProp setAutoAddMonsterCount(int autoAddMonsterCount) {
        if (this.autoAddMonsterCount != autoAddMonsterCount) {
            this.mark(FIELD_INDEX_AUTOADDMONSTERCOUNT);
            this.autoAddMonsterCount = autoAddMonsterCount;
        }
        return this;
    }

    /**
     * inner set autoAddMonsterCount
     *
     * @param autoAddMonsterCount new value
     */
    private void innerSetAutoAddMonsterCount(int autoAddMonsterCount) {
        this.autoAddMonsterCount = autoAddMonsterCount;
    }

    /**
     * get ableKillMonsterLimit
     *
     * @return ableKillMonsterLimit value
     */
    public int getAbleKillMonsterLimit() {
        return this.ableKillMonsterLimit;
    }

    /**
     * set ableKillMonsterLimit && set marked
     *
     * @param ableKillMonsterLimit new value
     * @return current object
     */
    public BigSceneRecordsProp setAbleKillMonsterLimit(int ableKillMonsterLimit) {
        if (this.ableKillMonsterLimit != ableKillMonsterLimit) {
            this.mark(FIELD_INDEX_ABLEKILLMONSTERLIMIT);
            this.ableKillMonsterLimit = ableKillMonsterLimit;
        }
        return this;
    }

    /**
     * inner set ableKillMonsterLimit
     *
     * @param ableKillMonsterLimit new value
     */
    private void innerSetAbleKillMonsterLimit(int ableKillMonsterLimit) {
        this.ableKillMonsterLimit = ableKillMonsterLimit;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BigSceneRecordsPB.Builder getCopyCsBuilder() {
        final BigSceneRecordsPB.Builder builder = BigSceneRecordsPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BigSceneRecordsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAutoAddMonsterCount() != 0) {
            builder.setAutoAddMonsterCount(this.getAutoAddMonsterCount());
            fieldCnt++;
        }  else if (builder.hasAutoAddMonsterCount()) {
            // 清理AutoAddMonsterCount
            builder.clearAutoAddMonsterCount();
            fieldCnt++;
        }
        if (this.getAbleKillMonsterLimit() != 0) {
            builder.setAbleKillMonsterLimit(this.getAbleKillMonsterLimit());
            fieldCnt++;
        }  else if (builder.hasAbleKillMonsterLimit()) {
            // 清理AbleKillMonsterLimit
            builder.clearAbleKillMonsterLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BigSceneRecordsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AUTOADDMONSTERCOUNT)) {
            builder.setAutoAddMonsterCount(this.getAutoAddMonsterCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ABLEKILLMONSTERLIMIT)) {
            builder.setAbleKillMonsterLimit(this.getAbleKillMonsterLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BigSceneRecordsPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AUTOADDMONSTERCOUNT)) {
            builder.setAutoAddMonsterCount(this.getAutoAddMonsterCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ABLEKILLMONSTERLIMIT)) {
            builder.setAbleKillMonsterLimit(this.getAbleKillMonsterLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BigSceneRecordsPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAutoAddMonsterCount()) {
            this.innerSetAutoAddMonsterCount(proto.getAutoAddMonsterCount());
        } else {
            this.innerSetAutoAddMonsterCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAbleKillMonsterLimit()) {
            this.innerSetAbleKillMonsterLimit(proto.getAbleKillMonsterLimit());
        } else {
            this.innerSetAbleKillMonsterLimit(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BigSceneRecordsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BigSceneRecordsPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAutoAddMonsterCount()) {
            this.setAutoAddMonsterCount(proto.getAutoAddMonsterCount());
            fieldCnt++;
        }
        if (proto.hasAbleKillMonsterLimit()) {
            this.setAbleKillMonsterLimit(proto.getAbleKillMonsterLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BigSceneRecords.Builder getCopyDbBuilder() {
        final BigSceneRecords.Builder builder = BigSceneRecords.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BigSceneRecords.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAutoAddMonsterCount() != 0) {
            builder.setAutoAddMonsterCount(this.getAutoAddMonsterCount());
            fieldCnt++;
        }  else if (builder.hasAutoAddMonsterCount()) {
            // 清理AutoAddMonsterCount
            builder.clearAutoAddMonsterCount();
            fieldCnt++;
        }
        if (this.getAbleKillMonsterLimit() != 0) {
            builder.setAbleKillMonsterLimit(this.getAbleKillMonsterLimit());
            fieldCnt++;
        }  else if (builder.hasAbleKillMonsterLimit()) {
            // 清理AbleKillMonsterLimit
            builder.clearAbleKillMonsterLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BigSceneRecords.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AUTOADDMONSTERCOUNT)) {
            builder.setAutoAddMonsterCount(this.getAutoAddMonsterCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ABLEKILLMONSTERLIMIT)) {
            builder.setAbleKillMonsterLimit(this.getAbleKillMonsterLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BigSceneRecords proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAutoAddMonsterCount()) {
            this.innerSetAutoAddMonsterCount(proto.getAutoAddMonsterCount());
        } else {
            this.innerSetAutoAddMonsterCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAbleKillMonsterLimit()) {
            this.innerSetAbleKillMonsterLimit(proto.getAbleKillMonsterLimit());
        } else {
            this.innerSetAbleKillMonsterLimit(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BigSceneRecordsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BigSceneRecords proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAutoAddMonsterCount()) {
            this.setAutoAddMonsterCount(proto.getAutoAddMonsterCount());
            fieldCnt++;
        }
        if (proto.hasAbleKillMonsterLimit()) {
            this.setAbleKillMonsterLimit(proto.getAbleKillMonsterLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BigSceneRecords.Builder getCopySsBuilder() {
        final BigSceneRecords.Builder builder = BigSceneRecords.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BigSceneRecords.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAutoAddMonsterCount() != 0) {
            builder.setAutoAddMonsterCount(this.getAutoAddMonsterCount());
            fieldCnt++;
        }  else if (builder.hasAutoAddMonsterCount()) {
            // 清理AutoAddMonsterCount
            builder.clearAutoAddMonsterCount();
            fieldCnt++;
        }
        if (this.getAbleKillMonsterLimit() != 0) {
            builder.setAbleKillMonsterLimit(this.getAbleKillMonsterLimit());
            fieldCnt++;
        }  else if (builder.hasAbleKillMonsterLimit()) {
            // 清理AbleKillMonsterLimit
            builder.clearAbleKillMonsterLimit();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BigSceneRecords.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_AUTOADDMONSTERCOUNT)) {
            builder.setAutoAddMonsterCount(this.getAutoAddMonsterCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ABLEKILLMONSTERLIMIT)) {
            builder.setAbleKillMonsterLimit(this.getAbleKillMonsterLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BigSceneRecords proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAutoAddMonsterCount()) {
            this.innerSetAutoAddMonsterCount(proto.getAutoAddMonsterCount());
        } else {
            this.innerSetAutoAddMonsterCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAbleKillMonsterLimit()) {
            this.innerSetAbleKillMonsterLimit(proto.getAbleKillMonsterLimit());
        } else {
            this.innerSetAbleKillMonsterLimit(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BigSceneRecordsProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BigSceneRecords proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAutoAddMonsterCount()) {
            this.setAutoAddMonsterCount(proto.getAutoAddMonsterCount());
            fieldCnt++;
        }
        if (proto.hasAbleKillMonsterLimit()) {
            this.setAbleKillMonsterLimit(proto.getAbleKillMonsterLimit());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BigSceneRecords.Builder builder = BigSceneRecords.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BigSceneRecordsProp)) {
            return false;
        }
        final BigSceneRecordsProp otherNode = (BigSceneRecordsProp) node;
        if (this.autoAddMonsterCount != otherNode.autoAddMonsterCount) {
            return false;
        }
        if (this.ableKillMonsterLimit != otherNode.ableKillMonsterLimit) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerGuidanceModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerGuidanceModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerGuidanceModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ALREADYFINISHGUIDANCES = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32GuidanceInfoMapProp alreadyFinishGuidances = null;

    public PlayerGuidanceModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerGuidanceModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get alreadyFinishGuidances
     *
     * @return alreadyFinishGuidances value
     */
    public Int32GuidanceInfoMapProp getAlreadyFinishGuidances() {
        if (this.alreadyFinishGuidances == null) {
            this.alreadyFinishGuidances = new Int32GuidanceInfoMapProp(this, FIELD_INDEX_ALREADYFINISHGUIDANCES);
        }
        return this.alreadyFinishGuidances;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putAlreadyFinishGuidancesV(GuidanceInfoProp v) {
        this.getAlreadyFinishGuidances().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public GuidanceInfoProp addEmptyAlreadyFinishGuidances(Integer k) {
        return this.getAlreadyFinishGuidances().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getAlreadyFinishGuidancesSize() {
        if (this.alreadyFinishGuidances == null) {
            return 0;
        }
        return this.alreadyFinishGuidances.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isAlreadyFinishGuidancesEmpty() {
        if (this.alreadyFinishGuidances == null) {
            return true;
        }
        return this.alreadyFinishGuidances.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public GuidanceInfoProp getAlreadyFinishGuidancesV(Integer k) {
        if (this.alreadyFinishGuidances == null || !this.alreadyFinishGuidances.containsKey(k)) {
            return null;
        }
        return this.alreadyFinishGuidances.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearAlreadyFinishGuidances() {
        if (this.alreadyFinishGuidances != null) {
            this.alreadyFinishGuidances.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeAlreadyFinishGuidancesV(Integer k) {
        if (this.alreadyFinishGuidances != null) {
            this.alreadyFinishGuidances.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGuidanceModelPB.Builder getCopyCsBuilder() {
        final PlayerGuidanceModelPB.Builder builder = PlayerGuidanceModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerGuidanceModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.alreadyFinishGuidances != null) {
            StructPB.Int32GuidanceInfoMapPB.Builder tmpBuilder = StructPB.Int32GuidanceInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.alreadyFinishGuidances.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyFinishGuidances(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyFinishGuidances();
            }
        }  else if (builder.hasAlreadyFinishGuidances()) {
            // 清理AlreadyFinishGuidances
            builder.clearAlreadyFinishGuidances();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerGuidanceModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALREADYFINISHGUIDANCES) && this.alreadyFinishGuidances != null) {
            final boolean needClear = !builder.hasAlreadyFinishGuidances();
            final int tmpFieldCnt = this.alreadyFinishGuidances.copyChangeToCs(builder.getAlreadyFinishGuidancesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyFinishGuidances();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerGuidanceModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALREADYFINISHGUIDANCES) && this.alreadyFinishGuidances != null) {
            final boolean needClear = !builder.hasAlreadyFinishGuidances();
            final int tmpFieldCnt = this.alreadyFinishGuidances.copyChangeToAndClearDeleteKeysCs(builder.getAlreadyFinishGuidancesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyFinishGuidances();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerGuidanceModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAlreadyFinishGuidances()) {
            this.getAlreadyFinishGuidances().mergeFromCs(proto.getAlreadyFinishGuidances());
        } else {
            if (this.alreadyFinishGuidances != null) {
                this.alreadyFinishGuidances.mergeFromCs(proto.getAlreadyFinishGuidances());
            }
        }
        this.markAll();
        return PlayerGuidanceModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerGuidanceModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAlreadyFinishGuidances()) {
            this.getAlreadyFinishGuidances().mergeChangeFromCs(proto.getAlreadyFinishGuidances());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGuidanceModel.Builder getCopyDbBuilder() {
        final PlayerGuidanceModel.Builder builder = PlayerGuidanceModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerGuidanceModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.alreadyFinishGuidances != null) {
            Struct.Int32GuidanceInfoMap.Builder tmpBuilder = Struct.Int32GuidanceInfoMap.newBuilder();
            final int tmpFieldCnt = this.alreadyFinishGuidances.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyFinishGuidances(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyFinishGuidances();
            }
        }  else if (builder.hasAlreadyFinishGuidances()) {
            // 清理AlreadyFinishGuidances
            builder.clearAlreadyFinishGuidances();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerGuidanceModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALREADYFINISHGUIDANCES) && this.alreadyFinishGuidances != null) {
            final boolean needClear = !builder.hasAlreadyFinishGuidances();
            final int tmpFieldCnt = this.alreadyFinishGuidances.copyChangeToDb(builder.getAlreadyFinishGuidancesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyFinishGuidances();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerGuidanceModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAlreadyFinishGuidances()) {
            this.getAlreadyFinishGuidances().mergeFromDb(proto.getAlreadyFinishGuidances());
        } else {
            if (this.alreadyFinishGuidances != null) {
                this.alreadyFinishGuidances.mergeFromDb(proto.getAlreadyFinishGuidances());
            }
        }
        this.markAll();
        return PlayerGuidanceModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerGuidanceModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAlreadyFinishGuidances()) {
            this.getAlreadyFinishGuidances().mergeChangeFromDb(proto.getAlreadyFinishGuidances());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGuidanceModel.Builder getCopySsBuilder() {
        final PlayerGuidanceModel.Builder builder = PlayerGuidanceModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerGuidanceModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.alreadyFinishGuidances != null) {
            Struct.Int32GuidanceInfoMap.Builder tmpBuilder = Struct.Int32GuidanceInfoMap.newBuilder();
            final int tmpFieldCnt = this.alreadyFinishGuidances.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAlreadyFinishGuidances(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAlreadyFinishGuidances();
            }
        }  else if (builder.hasAlreadyFinishGuidances()) {
            // 清理AlreadyFinishGuidances
            builder.clearAlreadyFinishGuidances();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerGuidanceModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ALREADYFINISHGUIDANCES) && this.alreadyFinishGuidances != null) {
            final boolean needClear = !builder.hasAlreadyFinishGuidances();
            final int tmpFieldCnt = this.alreadyFinishGuidances.copyChangeToSs(builder.getAlreadyFinishGuidancesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAlreadyFinishGuidances();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerGuidanceModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAlreadyFinishGuidances()) {
            this.getAlreadyFinishGuidances().mergeFromSs(proto.getAlreadyFinishGuidances());
        } else {
            if (this.alreadyFinishGuidances != null) {
                this.alreadyFinishGuidances.mergeFromSs(proto.getAlreadyFinishGuidances());
            }
        }
        this.markAll();
        return PlayerGuidanceModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerGuidanceModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAlreadyFinishGuidances()) {
            this.getAlreadyFinishGuidances().mergeChangeFromSs(proto.getAlreadyFinishGuidances());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerGuidanceModel.Builder builder = PlayerGuidanceModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYFINISHGUIDANCES) && this.alreadyFinishGuidances != null) {
            this.alreadyFinishGuidances.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.alreadyFinishGuidances != null) {
            this.alreadyFinishGuidances.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerGuidanceModelProp)) {
            return false;
        }
        final PlayerGuidanceModelProp otherNode = (PlayerGuidanceModelProp) node;
        if (!this.getAlreadyFinishGuidances().compareDataTo(otherNode.getAlreadyFinishGuidances())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
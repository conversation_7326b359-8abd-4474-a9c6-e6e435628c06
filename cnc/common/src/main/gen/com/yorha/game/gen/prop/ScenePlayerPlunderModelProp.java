package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ScenePlayerPlunderModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.ScenePlayerPlunderModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerPlunderModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TODAYPLUNDER = 0;
    public static final int FIELD_INDEX_LASTPLUNDERTIME = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32CurrencyMapProp todayPlunder = null;
    private long lastPlunderTime = Constant.DEFAULT_LONG_VALUE;

    public ScenePlayerPlunderModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerPlunderModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get todayPlunder
     *
     * @return todayPlunder value
     */
    public Int32CurrencyMapProp getTodayPlunder() {
        if (this.todayPlunder == null) {
            this.todayPlunder = new Int32CurrencyMapProp(this, FIELD_INDEX_TODAYPLUNDER);
        }
        return this.todayPlunder;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTodayPlunderV(CurrencyProp v) {
        this.getTodayPlunder().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyTodayPlunder(Integer k) {
        return this.getTodayPlunder().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTodayPlunderSize() {
        if (this.todayPlunder == null) {
            return 0;
        }
        return this.todayPlunder.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTodayPlunderEmpty() {
        if (this.todayPlunder == null) {
            return true;
        }
        return this.todayPlunder.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getTodayPlunderV(Integer k) {
        if (this.todayPlunder == null || !this.todayPlunder.containsKey(k)) {
            return null;
        }
        return this.todayPlunder.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTodayPlunder() {
        if (this.todayPlunder != null) {
            this.todayPlunder.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTodayPlunderV(Integer k) {
        if (this.todayPlunder != null) {
            this.todayPlunder.remove(k);
        }
    }
    /**
     * get lastPlunderTime
     *
     * @return lastPlunderTime value
     */
    public long getLastPlunderTime() {
        return this.lastPlunderTime;
    }

    /**
     * set lastPlunderTime && set marked
     *
     * @param lastPlunderTime new value
     * @return current object
     */
    public ScenePlayerPlunderModelProp setLastPlunderTime(long lastPlunderTime) {
        if (this.lastPlunderTime != lastPlunderTime) {
            this.mark(FIELD_INDEX_LASTPLUNDERTIME);
            this.lastPlunderTime = lastPlunderTime;
        }
        return this;
    }

    /**
     * inner set lastPlunderTime
     *
     * @param lastPlunderTime new value
     */
    private void innerSetLastPlunderTime(long lastPlunderTime) {
        this.lastPlunderTime = lastPlunderTime;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPlunderModelPB.Builder getCopyCsBuilder() {
        final ScenePlayerPlunderModelPB.Builder builder = ScenePlayerPlunderModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerPlunderModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.todayPlunder != null) {
            StructPB.Int32CurrencyMapPB.Builder tmpBuilder = StructPB.Int32CurrencyMapPB.newBuilder();
            final int tmpFieldCnt = this.todayPlunder.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTodayPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTodayPlunder();
            }
        }  else if (builder.hasTodayPlunder()) {
            // 清理TodayPlunder
            builder.clearTodayPlunder();
            fieldCnt++;
        }
        if (this.getLastPlunderTime() != 0L) {
            builder.setLastPlunderTime(this.getLastPlunderTime());
            fieldCnt++;
        }  else if (builder.hasLastPlunderTime()) {
            // 清理LastPlunderTime
            builder.clearLastPlunderTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerPlunderModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYPLUNDER) && this.todayPlunder != null) {
            final boolean needClear = !builder.hasTodayPlunder();
            final int tmpFieldCnt = this.todayPlunder.copyChangeToCs(builder.getTodayPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTodayPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTPLUNDERTIME)) {
            builder.setLastPlunderTime(this.getLastPlunderTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerPlunderModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYPLUNDER) && this.todayPlunder != null) {
            final boolean needClear = !builder.hasTodayPlunder();
            final int tmpFieldCnt = this.todayPlunder.copyChangeToAndClearDeleteKeysCs(builder.getTodayPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTodayPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTPLUNDERTIME)) {
            builder.setLastPlunderTime(this.getLastPlunderTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerPlunderModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTodayPlunder()) {
            this.getTodayPlunder().mergeFromCs(proto.getTodayPlunder());
        } else {
            if (this.todayPlunder != null) {
                this.todayPlunder.mergeFromCs(proto.getTodayPlunder());
            }
        }
        if (proto.hasLastPlunderTime()) {
            this.innerSetLastPlunderTime(proto.getLastPlunderTime());
        } else {
            this.innerSetLastPlunderTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePlayerPlunderModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerPlunderModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTodayPlunder()) {
            this.getTodayPlunder().mergeChangeFromCs(proto.getTodayPlunder());
            fieldCnt++;
        }
        if (proto.hasLastPlunderTime()) {
            this.setLastPlunderTime(proto.getLastPlunderTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPlunderModel.Builder getCopyDbBuilder() {
        final ScenePlayerPlunderModel.Builder builder = ScenePlayerPlunderModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerPlunderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.todayPlunder != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.todayPlunder.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTodayPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTodayPlunder();
            }
        }  else if (builder.hasTodayPlunder()) {
            // 清理TodayPlunder
            builder.clearTodayPlunder();
            fieldCnt++;
        }
        if (this.getLastPlunderTime() != 0L) {
            builder.setLastPlunderTime(this.getLastPlunderTime());
            fieldCnt++;
        }  else if (builder.hasLastPlunderTime()) {
            // 清理LastPlunderTime
            builder.clearLastPlunderTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerPlunderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYPLUNDER) && this.todayPlunder != null) {
            final boolean needClear = !builder.hasTodayPlunder();
            final int tmpFieldCnt = this.todayPlunder.copyChangeToDb(builder.getTodayPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTodayPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTPLUNDERTIME)) {
            builder.setLastPlunderTime(this.getLastPlunderTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerPlunderModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTodayPlunder()) {
            this.getTodayPlunder().mergeFromDb(proto.getTodayPlunder());
        } else {
            if (this.todayPlunder != null) {
                this.todayPlunder.mergeFromDb(proto.getTodayPlunder());
            }
        }
        if (proto.hasLastPlunderTime()) {
            this.innerSetLastPlunderTime(proto.getLastPlunderTime());
        } else {
            this.innerSetLastPlunderTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePlayerPlunderModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerPlunderModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTodayPlunder()) {
            this.getTodayPlunder().mergeChangeFromDb(proto.getTodayPlunder());
            fieldCnt++;
        }
        if (proto.hasLastPlunderTime()) {
            this.setLastPlunderTime(proto.getLastPlunderTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerPlunderModel.Builder getCopySsBuilder() {
        final ScenePlayerPlunderModel.Builder builder = ScenePlayerPlunderModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerPlunderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.todayPlunder != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.todayPlunder.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTodayPlunder(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTodayPlunder();
            }
        }  else if (builder.hasTodayPlunder()) {
            // 清理TodayPlunder
            builder.clearTodayPlunder();
            fieldCnt++;
        }
        if (this.getLastPlunderTime() != 0L) {
            builder.setLastPlunderTime(this.getLastPlunderTime());
            fieldCnt++;
        }  else if (builder.hasLastPlunderTime()) {
            // 清理LastPlunderTime
            builder.clearLastPlunderTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerPlunderModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TODAYPLUNDER) && this.todayPlunder != null) {
            final boolean needClear = !builder.hasTodayPlunder();
            final int tmpFieldCnt = this.todayPlunder.copyChangeToSs(builder.getTodayPlunderBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTodayPlunder();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTPLUNDERTIME)) {
            builder.setLastPlunderTime(this.getLastPlunderTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerPlunderModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTodayPlunder()) {
            this.getTodayPlunder().mergeFromSs(proto.getTodayPlunder());
        } else {
            if (this.todayPlunder != null) {
                this.todayPlunder.mergeFromSs(proto.getTodayPlunder());
            }
        }
        if (proto.hasLastPlunderTime()) {
            this.innerSetLastPlunderTime(proto.getLastPlunderTime());
        } else {
            this.innerSetLastPlunderTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ScenePlayerPlunderModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerPlunderModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTodayPlunder()) {
            this.getTodayPlunder().mergeChangeFromSs(proto.getTodayPlunder());
            fieldCnt++;
        }
        if (proto.hasLastPlunderTime()) {
            this.setLastPlunderTime(proto.getLastPlunderTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerPlunderModel.Builder builder = ScenePlayerPlunderModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TODAYPLUNDER) && this.todayPlunder != null) {
            this.todayPlunder.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.todayPlunder != null) {
            this.todayPlunder.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerPlunderModelProp)) {
            return false;
        }
        final ScenePlayerPlunderModelProp otherNode = (ScenePlayerPlunderModelProp) node;
        if (!this.getTodayPlunder().compareDataTo(otherNode.getTodayPlunder())) {
            return false;
        }
        if (this.lastPlunderTime != otherNode.lastPlunderTime) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
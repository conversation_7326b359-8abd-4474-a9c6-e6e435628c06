package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityStoreRatingUnit;
import com.yorha.proto.StructPB.ActivityStoreRatingUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityStoreRatingUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_UNRATEDTIME = 0;
    public static final int FIELD_INDEX_ONLINETIMEMS = 1;
    public static final int FIELD_INDEX_ISRATED = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int unratedTime = Constant.DEFAULT_INT_VALUE;
    private long onlineTimeMs = Constant.DEFAULT_LONG_VALUE;
    private boolean isRated = Constant.DEFAULT_BOOLEAN_VALUE;

    public ActivityStoreRatingUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityStoreRatingUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get unratedTime
     *
     * @return unratedTime value
     */
    public int getUnratedTime() {
        return this.unratedTime;
    }

    /**
     * set unratedTime && set marked
     *
     * @param unratedTime new value
     * @return current object
     */
    public ActivityStoreRatingUnitProp setUnratedTime(int unratedTime) {
        if (this.unratedTime != unratedTime) {
            this.mark(FIELD_INDEX_UNRATEDTIME);
            this.unratedTime = unratedTime;
        }
        return this;
    }

    /**
     * inner set unratedTime
     *
     * @param unratedTime new value
     */
    private void innerSetUnratedTime(int unratedTime) {
        this.unratedTime = unratedTime;
    }

    /**
     * get onlineTimeMs
     *
     * @return onlineTimeMs value
     */
    public long getOnlineTimeMs() {
        return this.onlineTimeMs;
    }

    /**
     * set onlineTimeMs && set marked
     *
     * @param onlineTimeMs new value
     * @return current object
     */
    public ActivityStoreRatingUnitProp setOnlineTimeMs(long onlineTimeMs) {
        if (this.onlineTimeMs != onlineTimeMs) {
            this.mark(FIELD_INDEX_ONLINETIMEMS);
            this.onlineTimeMs = onlineTimeMs;
        }
        return this;
    }

    /**
     * inner set onlineTimeMs
     *
     * @param onlineTimeMs new value
     */
    private void innerSetOnlineTimeMs(long onlineTimeMs) {
        this.onlineTimeMs = onlineTimeMs;
    }

    /**
     * get isRated
     *
     * @return isRated value
     */
    public boolean getIsRated() {
        return this.isRated;
    }

    /**
     * set isRated && set marked
     *
     * @param isRated new value
     * @return current object
     */
    public ActivityStoreRatingUnitProp setIsRated(boolean isRated) {
        if (this.isRated != isRated) {
            this.mark(FIELD_INDEX_ISRATED);
            this.isRated = isRated;
        }
        return this;
    }

    /**
     * inner set isRated
     *
     * @param isRated new value
     */
    private void innerSetIsRated(boolean isRated) {
        this.isRated = isRated;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreRatingUnitPB.Builder getCopyCsBuilder() {
        final ActivityStoreRatingUnitPB.Builder builder = ActivityStoreRatingUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityStoreRatingUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnratedTime() != 0) {
            builder.setUnratedTime(this.getUnratedTime());
            fieldCnt++;
        }  else if (builder.hasUnratedTime()) {
            // 清理UnratedTime
            builder.clearUnratedTime();
            fieldCnt++;
        }
        if (this.getOnlineTimeMs() != 0L) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }  else if (builder.hasOnlineTimeMs()) {
            // 清理OnlineTimeMs
            builder.clearOnlineTimeMs();
            fieldCnt++;
        }
        if (this.getIsRated()) {
            builder.setIsRated(this.getIsRated());
            fieldCnt++;
        }  else if (builder.hasIsRated()) {
            // 清理IsRated
            builder.clearIsRated();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityStoreRatingUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNRATEDTIME)) {
            builder.setUnratedTime(this.getUnratedTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISRATED)) {
            builder.setIsRated(this.getIsRated());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityStoreRatingUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNRATEDTIME)) {
            builder.setUnratedTime(this.getUnratedTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISRATED)) {
            builder.setIsRated(this.getIsRated());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityStoreRatingUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnratedTime()) {
            this.innerSetUnratedTime(proto.getUnratedTime());
        } else {
            this.innerSetUnratedTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOnlineTimeMs()) {
            this.innerSetOnlineTimeMs(proto.getOnlineTimeMs());
        } else {
            this.innerSetOnlineTimeMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsRated()) {
            this.innerSetIsRated(proto.getIsRated());
        } else {
            this.innerSetIsRated(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityStoreRatingUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityStoreRatingUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnratedTime()) {
            this.setUnratedTime(proto.getUnratedTime());
            fieldCnt++;
        }
        if (proto.hasOnlineTimeMs()) {
            this.setOnlineTimeMs(proto.getOnlineTimeMs());
            fieldCnt++;
        }
        if (proto.hasIsRated()) {
            this.setIsRated(proto.getIsRated());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreRatingUnit.Builder getCopyDbBuilder() {
        final ActivityStoreRatingUnit.Builder builder = ActivityStoreRatingUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityStoreRatingUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnratedTime() != 0) {
            builder.setUnratedTime(this.getUnratedTime());
            fieldCnt++;
        }  else if (builder.hasUnratedTime()) {
            // 清理UnratedTime
            builder.clearUnratedTime();
            fieldCnt++;
        }
        if (this.getOnlineTimeMs() != 0L) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }  else if (builder.hasOnlineTimeMs()) {
            // 清理OnlineTimeMs
            builder.clearOnlineTimeMs();
            fieldCnt++;
        }
        if (this.getIsRated()) {
            builder.setIsRated(this.getIsRated());
            fieldCnt++;
        }  else if (builder.hasIsRated()) {
            // 清理IsRated
            builder.clearIsRated();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityStoreRatingUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNRATEDTIME)) {
            builder.setUnratedTime(this.getUnratedTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISRATED)) {
            builder.setIsRated(this.getIsRated());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityStoreRatingUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnratedTime()) {
            this.innerSetUnratedTime(proto.getUnratedTime());
        } else {
            this.innerSetUnratedTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOnlineTimeMs()) {
            this.innerSetOnlineTimeMs(proto.getOnlineTimeMs());
        } else {
            this.innerSetOnlineTimeMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsRated()) {
            this.innerSetIsRated(proto.getIsRated());
        } else {
            this.innerSetIsRated(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityStoreRatingUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityStoreRatingUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnratedTime()) {
            this.setUnratedTime(proto.getUnratedTime());
            fieldCnt++;
        }
        if (proto.hasOnlineTimeMs()) {
            this.setOnlineTimeMs(proto.getOnlineTimeMs());
            fieldCnt++;
        }
        if (proto.hasIsRated()) {
            this.setIsRated(proto.getIsRated());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityStoreRatingUnit.Builder getCopySsBuilder() {
        final ActivityStoreRatingUnit.Builder builder = ActivityStoreRatingUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityStoreRatingUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnratedTime() != 0) {
            builder.setUnratedTime(this.getUnratedTime());
            fieldCnt++;
        }  else if (builder.hasUnratedTime()) {
            // 清理UnratedTime
            builder.clearUnratedTime();
            fieldCnt++;
        }
        if (this.getOnlineTimeMs() != 0L) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }  else if (builder.hasOnlineTimeMs()) {
            // 清理OnlineTimeMs
            builder.clearOnlineTimeMs();
            fieldCnt++;
        }
        if (this.getIsRated()) {
            builder.setIsRated(this.getIsRated());
            fieldCnt++;
        }  else if (builder.hasIsRated()) {
            // 清理IsRated
            builder.clearIsRated();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityStoreRatingUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNRATEDTIME)) {
            builder.setUnratedTime(this.getUnratedTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ONLINETIMEMS)) {
            builder.setOnlineTimeMs(this.getOnlineTimeMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISRATED)) {
            builder.setIsRated(this.getIsRated());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityStoreRatingUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnratedTime()) {
            this.innerSetUnratedTime(proto.getUnratedTime());
        } else {
            this.innerSetUnratedTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOnlineTimeMs()) {
            this.innerSetOnlineTimeMs(proto.getOnlineTimeMs());
        } else {
            this.innerSetOnlineTimeMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasIsRated()) {
            this.innerSetIsRated(proto.getIsRated());
        } else {
            this.innerSetIsRated(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ActivityStoreRatingUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityStoreRatingUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnratedTime()) {
            this.setUnratedTime(proto.getUnratedTime());
            fieldCnt++;
        }
        if (proto.hasOnlineTimeMs()) {
            this.setOnlineTimeMs(proto.getOnlineTimeMs());
            fieldCnt++;
        }
        if (proto.hasIsRated()) {
            this.setIsRated(proto.getIsRated());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityStoreRatingUnit.Builder builder = ActivityStoreRatingUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityStoreRatingUnitProp)) {
            return false;
        }
        final ActivityStoreRatingUnitProp otherNode = (ActivityStoreRatingUnitProp) node;
        if (this.unratedTime != otherNode.unratedTime) {
            return false;
        }
        if (this.onlineTimeMs != otherNode.onlineTimeMs) {
            return false;
        }
        if (this.isRated != otherNode.isRated) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
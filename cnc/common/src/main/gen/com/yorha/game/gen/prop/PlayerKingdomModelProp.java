package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerKingdomModel;
import com.yorha.proto.PlayerPB.PlayerKingdomModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerKingdomModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_OFFICEID = 0;
    public static final int FIELD_INDEX_BUFFCDENDTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int officeId = Constant.DEFAULT_INT_VALUE;
    private long buffCdEndTsMs = Constant.DEFAULT_LONG_VALUE;

    public PlayerKingdomModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerKingdomModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get officeId
     *
     * @return officeId value
     */
    public int getOfficeId() {
        return this.officeId;
    }

    /**
     * set officeId && set marked
     *
     * @param officeId new value
     * @return current object
     */
    public PlayerKingdomModelProp setOfficeId(int officeId) {
        if (this.officeId != officeId) {
            this.mark(FIELD_INDEX_OFFICEID);
            this.officeId = officeId;
        }
        return this;
    }

    /**
     * inner set officeId
     *
     * @param officeId new value
     */
    private void innerSetOfficeId(int officeId) {
        this.officeId = officeId;
    }

    /**
     * get buffCdEndTsMs
     *
     * @return buffCdEndTsMs value
     */
    public long getBuffCdEndTsMs() {
        return this.buffCdEndTsMs;
    }

    /**
     * set buffCdEndTsMs && set marked
     *
     * @param buffCdEndTsMs new value
     * @return current object
     */
    public PlayerKingdomModelProp setBuffCdEndTsMs(long buffCdEndTsMs) {
        if (this.buffCdEndTsMs != buffCdEndTsMs) {
            this.mark(FIELD_INDEX_BUFFCDENDTSMS);
            this.buffCdEndTsMs = buffCdEndTsMs;
        }
        return this;
    }

    /**
     * inner set buffCdEndTsMs
     *
     * @param buffCdEndTsMs new value
     */
    private void innerSetBuffCdEndTsMs(long buffCdEndTsMs) {
        this.buffCdEndTsMs = buffCdEndTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerKingdomModelPB.Builder getCopyCsBuilder() {
        final PlayerKingdomModelPB.Builder builder = PlayerKingdomModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerKingdomModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerKingdomModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerKingdomModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerKingdomModel.Builder getCopyDbBuilder() {
        final PlayerKingdomModel.Builder builder = PlayerKingdomModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.getBuffCdEndTsMs() != 0L) {
            builder.setBuffCdEndTsMs(this.getBuffCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasBuffCdEndTsMs()) {
            // 清理BuffCdEndTsMs
            builder.clearBuffCdEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFCDENDTSMS)) {
            builder.setBuffCdEndTsMs(this.getBuffCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerKingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuffCdEndTsMs()) {
            this.innerSetBuffCdEndTsMs(proto.getBuffCdEndTsMs());
        } else {
            this.innerSetBuffCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerKingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasBuffCdEndTsMs()) {
            this.setBuffCdEndTsMs(proto.getBuffCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerKingdomModel.Builder getCopySsBuilder() {
        final PlayerKingdomModel.Builder builder = PlayerKingdomModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOfficeId() != 0) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }  else if (builder.hasOfficeId()) {
            // 清理OfficeId
            builder.clearOfficeId();
            fieldCnt++;
        }
        if (this.getBuffCdEndTsMs() != 0L) {
            builder.setBuffCdEndTsMs(this.getBuffCdEndTsMs());
            fieldCnt++;
        }  else if (builder.hasBuffCdEndTsMs()) {
            // 清理BuffCdEndTsMs
            builder.clearBuffCdEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerKingdomModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OFFICEID)) {
            builder.setOfficeId(this.getOfficeId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUFFCDENDTSMS)) {
            builder.setBuffCdEndTsMs(this.getBuffCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerKingdomModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOfficeId()) {
            this.innerSetOfficeId(proto.getOfficeId());
        } else {
            this.innerSetOfficeId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBuffCdEndTsMs()) {
            this.innerSetBuffCdEndTsMs(proto.getBuffCdEndTsMs());
        } else {
            this.innerSetBuffCdEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerKingdomModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerKingdomModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOfficeId()) {
            this.setOfficeId(proto.getOfficeId());
            fieldCnt++;
        }
        if (proto.hasBuffCdEndTsMs()) {
            this.setBuffCdEndTsMs(proto.getBuffCdEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerKingdomModel.Builder builder = PlayerKingdomModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerKingdomModelProp)) {
            return false;
        }
        final PlayerKingdomModelProp otherNode = (PlayerKingdomModelProp) node;
        if (this.officeId != otherNode.officeId) {
            return false;
        }
        if (this.buffCdEndTsMs != otherNode.buffCdEndTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
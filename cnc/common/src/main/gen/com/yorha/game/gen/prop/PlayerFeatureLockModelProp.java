package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerFeatureLockModel;
import com.yorha.proto.PlayerPB.PlayerFeatureLockModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerFeatureLockModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_FEATURELOCKINFO = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private long featureLockInfo = Constant.DEFAULT_LONG_VALUE;

    public PlayerFeatureLockModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerFeatureLockModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get featureLockInfo
     *
     * @return featureLockInfo value
     */
    public long getFeatureLockInfo() {
        return this.featureLockInfo;
    }

    /**
     * set featureLockInfo && set marked
     *
     * @param featureLockInfo new value
     * @return current object
     */
    public PlayerFeatureLockModelProp setFeatureLockInfo(long featureLockInfo) {
        if (this.featureLockInfo != featureLockInfo) {
            this.mark(FIELD_INDEX_FEATURELOCKINFO);
            this.featureLockInfo = featureLockInfo;
        }
        return this;
    }

    /**
     * inner set featureLockInfo
     *
     * @param featureLockInfo new value
     */
    private void innerSetFeatureLockInfo(long featureLockInfo) {
        this.featureLockInfo = featureLockInfo;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerFeatureLockModelPB.Builder getCopyCsBuilder() {
        final PlayerFeatureLockModelPB.Builder builder = PlayerFeatureLockModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerFeatureLockModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getFeatureLockInfo() != 0L) {
            builder.setFeatureLockInfo(this.getFeatureLockInfo());
            fieldCnt++;
        }  else if (builder.hasFeatureLockInfo()) {
            // 清理FeatureLockInfo
            builder.clearFeatureLockInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerFeatureLockModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FEATURELOCKINFO)) {
            builder.setFeatureLockInfo(this.getFeatureLockInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerFeatureLockModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FEATURELOCKINFO)) {
            builder.setFeatureLockInfo(this.getFeatureLockInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerFeatureLockModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFeatureLockInfo()) {
            this.innerSetFeatureLockInfo(proto.getFeatureLockInfo());
        } else {
            this.innerSetFeatureLockInfo(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerFeatureLockModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerFeatureLockModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFeatureLockInfo()) {
            this.setFeatureLockInfo(proto.getFeatureLockInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerFeatureLockModel.Builder getCopyDbBuilder() {
        final PlayerFeatureLockModel.Builder builder = PlayerFeatureLockModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerFeatureLockModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getFeatureLockInfo() != 0L) {
            builder.setFeatureLockInfo(this.getFeatureLockInfo());
            fieldCnt++;
        }  else if (builder.hasFeatureLockInfo()) {
            // 清理FeatureLockInfo
            builder.clearFeatureLockInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerFeatureLockModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FEATURELOCKINFO)) {
            builder.setFeatureLockInfo(this.getFeatureLockInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerFeatureLockModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFeatureLockInfo()) {
            this.innerSetFeatureLockInfo(proto.getFeatureLockInfo());
        } else {
            this.innerSetFeatureLockInfo(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerFeatureLockModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerFeatureLockModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFeatureLockInfo()) {
            this.setFeatureLockInfo(proto.getFeatureLockInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerFeatureLockModel.Builder getCopySsBuilder() {
        final PlayerFeatureLockModel.Builder builder = PlayerFeatureLockModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerFeatureLockModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getFeatureLockInfo() != 0L) {
            builder.setFeatureLockInfo(this.getFeatureLockInfo());
            fieldCnt++;
        }  else if (builder.hasFeatureLockInfo()) {
            // 清理FeatureLockInfo
            builder.clearFeatureLockInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerFeatureLockModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FEATURELOCKINFO)) {
            builder.setFeatureLockInfo(this.getFeatureLockInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerFeatureLockModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFeatureLockInfo()) {
            this.innerSetFeatureLockInfo(proto.getFeatureLockInfo());
        } else {
            this.innerSetFeatureLockInfo(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerFeatureLockModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerFeatureLockModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFeatureLockInfo()) {
            this.setFeatureLockInfo(proto.getFeatureLockInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerFeatureLockModel.Builder builder = PlayerFeatureLockModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerFeatureLockModelProp)) {
            return false;
        }
        final PlayerFeatureLockModelProp otherNode = (PlayerFeatureLockModelProp) node;
        if (this.featureLockInfo != otherNode.featureLockInfo) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.City.CityKingdomSkillInfo;
import com.yorha.proto.CityPB.CityKingdomSkillInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class CityKingdomSkillInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SKILLID = 0;
    public static final int FIELD_INDEX_SKILLENDTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int skillId = Constant.DEFAULT_INT_VALUE;
    private long skillEndTsMs = Constant.DEFAULT_LONG_VALUE;

    public CityKingdomSkillInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CityKingdomSkillInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skillId
     *
     * @return skillId value
     */
    public int getSkillId() {
        return this.skillId;
    }

    /**
     * set skillId && set marked
     *
     * @param skillId new value
     * @return current object
     */
    public CityKingdomSkillInfoProp setSkillId(int skillId) {
        if (this.skillId != skillId) {
            this.mark(FIELD_INDEX_SKILLID);
            this.skillId = skillId;
        }
        return this;
    }

    /**
     * inner set skillId
     *
     * @param skillId new value
     */
    private void innerSetSkillId(int skillId) {
        this.skillId = skillId;
    }

    /**
     * get skillEndTsMs
     *
     * @return skillEndTsMs value
     */
    public long getSkillEndTsMs() {
        return this.skillEndTsMs;
    }

    /**
     * set skillEndTsMs && set marked
     *
     * @param skillEndTsMs new value
     * @return current object
     */
    public CityKingdomSkillInfoProp setSkillEndTsMs(long skillEndTsMs) {
        if (this.skillEndTsMs != skillEndTsMs) {
            this.mark(FIELD_INDEX_SKILLENDTSMS);
            this.skillEndTsMs = skillEndTsMs;
        }
        return this;
    }

    /**
     * inner set skillEndTsMs
     *
     * @param skillEndTsMs new value
     */
    private void innerSetSkillEndTsMs(long skillEndTsMs) {
        this.skillEndTsMs = skillEndTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityKingdomSkillInfoPB.Builder getCopyCsBuilder() {
        final CityKingdomSkillInfoPB.Builder builder = CityKingdomSkillInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CityKingdomSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getSkillEndTsMs() != 0L) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }  else if (builder.hasSkillEndTsMs()) {
            // 清理SkillEndTsMs
            builder.clearSkillEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CityKingdomSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CityKingdomSkillInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CityKingdomSkillInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillEndTsMs()) {
            this.innerSetSkillEndTsMs(proto.getSkillEndTsMs());
        } else {
            this.innerSetSkillEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityKingdomSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CityKingdomSkillInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasSkillEndTsMs()) {
            this.setSkillEndTsMs(proto.getSkillEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityKingdomSkillInfo.Builder getCopyDbBuilder() {
        final CityKingdomSkillInfo.Builder builder = CityKingdomSkillInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CityKingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getSkillEndTsMs() != 0L) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }  else if (builder.hasSkillEndTsMs()) {
            // 清理SkillEndTsMs
            builder.clearSkillEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CityKingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CityKingdomSkillInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillEndTsMs()) {
            this.innerSetSkillEndTsMs(proto.getSkillEndTsMs());
        } else {
            this.innerSetSkillEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityKingdomSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CityKingdomSkillInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasSkillEndTsMs()) {
            this.setSkillEndTsMs(proto.getSkillEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityKingdomSkillInfo.Builder getCopySsBuilder() {
        final CityKingdomSkillInfo.Builder builder = CityKingdomSkillInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CityKingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSkillId() != 0) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }  else if (builder.hasSkillId()) {
            // 清理SkillId
            builder.clearSkillId();
            fieldCnt++;
        }
        if (this.getSkillEndTsMs() != 0L) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }  else if (builder.hasSkillEndTsMs()) {
            // 清理SkillEndTsMs
            builder.clearSkillEndTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CityKingdomSkillInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLID)) {
            builder.setSkillId(this.getSkillId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SKILLENDTSMS)) {
            builder.setSkillEndTsMs(this.getSkillEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CityKingdomSkillInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillId()) {
            this.innerSetSkillId(proto.getSkillId());
        } else {
            this.innerSetSkillId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSkillEndTsMs()) {
            this.innerSetSkillEndTsMs(proto.getSkillEndTsMs());
        } else {
            this.innerSetSkillEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityKingdomSkillInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CityKingdomSkillInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillId()) {
            this.setSkillId(proto.getSkillId());
            fieldCnt++;
        }
        if (proto.hasSkillEndTsMs()) {
            this.setSkillEndTsMs(proto.getSkillEndTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CityKingdomSkillInfo.Builder builder = CityKingdomSkillInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.skillId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CityKingdomSkillInfoProp)) {
            return false;
        }
        final CityKingdomSkillInfoProp otherNode = (CityKingdomSkillInfoProp) node;
        if (this.skillId != otherNode.skillId) {
            return false;
        }
        if (this.skillEndTsMs != otherNode.skillEndTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ClanStoreLogItem;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ClanStoreLogItemPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanStoreLogItemProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_USAGERECORD = 1;
    public static final int FIELD_INDEX_PLAYERID = 2;
    public static final int FIELD_INDEX_CARDHEAD = 3;
    public static final int FIELD_INDEX_CREATETSMS = 4;
    public static final int FIELD_INDEX_ITEMID = 5;
    public static final int FIELD_INDEX_ITEMCOUNT = 6;

    public static final int FIELD_COUNT = 7;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private ClanRecordProp usageRecord = null;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp cardHead = null;
    private long createTsMs = Constant.DEFAULT_LONG_VALUE;
    private int itemId = Constant.DEFAULT_INT_VALUE;
    private int itemCount = Constant.DEFAULT_INT_VALUE;

    public ClanStoreLogItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanStoreLogItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ClanStoreLogItemProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get usageRecord
     *
     * @return usageRecord value
     */
    public ClanRecordProp getUsageRecord() {
        if (this.usageRecord == null) {
            this.usageRecord = new ClanRecordProp(this, FIELD_INDEX_USAGERECORD);
        }
        return this.usageRecord;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public ClanStoreLogItemProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get createTsMs
     *
     * @return createTsMs value
     */
    public long getCreateTsMs() {
        return this.createTsMs;
    }

    /**
     * set createTsMs && set marked
     *
     * @param createTsMs new value
     * @return current object
     */
    public ClanStoreLogItemProp setCreateTsMs(long createTsMs) {
        if (this.createTsMs != createTsMs) {
            this.mark(FIELD_INDEX_CREATETSMS);
            this.createTsMs = createTsMs;
        }
        return this;
    }

    /**
     * inner set createTsMs
     *
     * @param createTsMs new value
     */
    private void innerSetCreateTsMs(long createTsMs) {
        this.createTsMs = createTsMs;
    }

    /**
     * get itemId
     *
     * @return itemId value
     */
    public int getItemId() {
        return this.itemId;
    }

    /**
     * set itemId && set marked
     *
     * @param itemId new value
     * @return current object
     */
    public ClanStoreLogItemProp setItemId(int itemId) {
        if (this.itemId != itemId) {
            this.mark(FIELD_INDEX_ITEMID);
            this.itemId = itemId;
        }
        return this;
    }

    /**
     * inner set itemId
     *
     * @param itemId new value
     */
    private void innerSetItemId(int itemId) {
        this.itemId = itemId;
    }

    /**
     * get itemCount
     *
     * @return itemCount value
     */
    public int getItemCount() {
        return this.itemCount;
    }

    /**
     * set itemCount && set marked
     *
     * @param itemCount new value
     * @return current object
     */
    public ClanStoreLogItemProp setItemCount(int itemCount) {
        if (this.itemCount != itemCount) {
            this.mark(FIELD_INDEX_ITEMCOUNT);
            this.itemCount = itemCount;
        }
        return this;
    }

    /**
     * inner set itemCount
     *
     * @param itemCount new value
     */
    private void innerSetItemCount(int itemCount) {
        this.itemCount = itemCount;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreLogItemPB.Builder getCopyCsBuilder() {
        final ClanStoreLogItemPB.Builder builder = ClanStoreLogItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanStoreLogItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            StructPB.ClanRecordPB.Builder tmpBuilder = StructPB.ClanRecordPB.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        if (this.getItemCount() != 0) {
            builder.setItemCount(this.getItemCount());
            fieldCnt++;
        }  else if (builder.hasItemCount()) {
            // 清理ItemCount
            builder.clearItemCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanStoreLogItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToCs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMCOUNT)) {
            builder.setItemCount(this.getItemCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanStoreLogItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToAndClearDeleteKeysCs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMCOUNT)) {
            builder.setItemCount(this.getItemCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanStoreLogItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromCs(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromCs(proto.getUsageRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemCount()) {
            this.innerSetItemCount(proto.getItemCount());
        } else {
            this.innerSetItemCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanStoreLogItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanStoreLogItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromCs(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        if (proto.hasItemCount()) {
            this.setItemCount(proto.getItemCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreLogItem.Builder getCopyDbBuilder() {
        final ClanStoreLogItem.Builder builder = ClanStoreLogItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanStoreLogItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            Struct.ClanRecord.Builder tmpBuilder = Struct.ClanRecord.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        if (this.getItemCount() != 0) {
            builder.setItemCount(this.getItemCount());
            fieldCnt++;
        }  else if (builder.hasItemCount()) {
            // 清理ItemCount
            builder.clearItemCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanStoreLogItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToDb(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMCOUNT)) {
            builder.setItemCount(this.getItemCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanStoreLogItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromDb(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromDb(proto.getUsageRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemCount()) {
            this.innerSetItemCount(proto.getItemCount());
        } else {
            this.innerSetItemCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanStoreLogItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanStoreLogItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromDb(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        if (proto.hasItemCount()) {
            this.setItemCount(proto.getItemCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStoreLogItem.Builder getCopySsBuilder() {
        final ClanStoreLogItem.Builder builder = ClanStoreLogItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanStoreLogItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            Struct.ClanRecord.Builder tmpBuilder = Struct.ClanRecord.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.getItemId() != 0) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }  else if (builder.hasItemId()) {
            // 清理ItemId
            builder.clearItemId();
            fieldCnt++;
        }
        if (this.getItemCount() != 0) {
            builder.setItemCount(this.getItemCount());
            fieldCnt++;
        }  else if (builder.hasItemCount()) {
            // 清理ItemCount
            builder.clearItemCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanStoreLogItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToSs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMID)) {
            builder.setItemId(this.getItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ITEMCOUNT)) {
            builder.setItemCount(this.getItemCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanStoreLogItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromSs(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromSs(proto.getUsageRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasItemId()) {
            this.innerSetItemId(proto.getItemId());
        } else {
            this.innerSetItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasItemCount()) {
            this.innerSetItemCount(proto.getItemCount());
        } else {
            this.innerSetItemCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanStoreLogItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanStoreLogItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromSs(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasItemId()) {
            this.setItemId(proto.getItemId());
            fieldCnt++;
        }
        if (proto.hasItemCount()) {
            this.setItemCount(proto.getItemCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanStoreLogItem.Builder builder = ClanStoreLogItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            this.usageRecord.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.usageRecord != null) {
            this.usageRecord.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanStoreLogItemProp)) {
            return false;
        }
        final ClanStoreLogItemProp otherNode = (ClanStoreLogItemProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (!this.getUsageRecord().compareDataTo(otherNode.getUsageRecord())) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (this.createTsMs != otherNode.createTsMs) {
            return false;
        }
        if (this.itemId != otherNode.itemId) {
            return false;
        }
        if (this.itemCount != otherNode.itemCount) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 57;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
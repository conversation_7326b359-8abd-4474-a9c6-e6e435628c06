package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailClanContributionRankingItem;
import com.yorha.proto.StructMailPB.MailClanContributionRankingItemPB;


/**
 * <AUTHOR> auto gen
 */
public class MailClanContributionRankingItemProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RANK = 0;
    public static final int FIELD_INDEX_PLAYERID = 1;
    public static final int FIELD_INDEX_PLAYERNAME = 2;
    public static final int FIELD_INDEX_CONTRIBUTION = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int rank = Constant.DEFAULT_INT_VALUE;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private String playerName = Constant.DEFAULT_STR_VALUE;
    private long contribution = Constant.DEFAULT_LONG_VALUE;

    public MailClanContributionRankingItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailClanContributionRankingItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rank
     *
     * @return rank value
     */
    public int getRank() {
        return this.rank;
    }

    /**
     * set rank && set marked
     *
     * @param rank new value
     * @return current object
     */
    public MailClanContributionRankingItemProp setRank(int rank) {
        if (this.rank != rank) {
            this.mark(FIELD_INDEX_RANK);
            this.rank = rank;
        }
        return this;
    }

    /**
     * inner set rank
     *
     * @param rank new value
     */
    private void innerSetRank(int rank) {
        this.rank = rank;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public MailClanContributionRankingItemProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get playerName
     *
     * @return playerName value
     */
    public String getPlayerName() {
        return this.playerName;
    }

    /**
     * set playerName && set marked
     *
     * @param playerName new value
     * @return current object
     */
    public MailClanContributionRankingItemProp setPlayerName(String playerName) {
        if (playerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.playerName, playerName)) {
            this.mark(FIELD_INDEX_PLAYERNAME);
            this.playerName = playerName;
        }
        return this;
    }

    /**
     * inner set playerName
     *
     * @param playerName new value
     */
    private void innerSetPlayerName(String playerName) {
        this.playerName = playerName;
    }

    /**
     * get contribution
     *
     * @return contribution value
     */
    public long getContribution() {
        return this.contribution;
    }

    /**
     * set contribution && set marked
     *
     * @param contribution new value
     * @return current object
     */
    public MailClanContributionRankingItemProp setContribution(long contribution) {
        if (this.contribution != contribution) {
            this.mark(FIELD_INDEX_CONTRIBUTION);
            this.contribution = contribution;
        }
        return this;
    }

    /**
     * inner set contribution
     *
     * @param contribution new value
     */
    private void innerSetContribution(long contribution) {
        this.contribution = contribution;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingItemPB.Builder getCopyCsBuilder() {
        final MailClanContributionRankingItemPB.Builder builder = MailClanContributionRankingItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailClanContributionRankingItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRank() != 0) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }  else if (builder.hasRank()) {
            // 清理Rank
            builder.clearRank();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (this.getContribution() != 0L) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailClanContributionRankingItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailClanContributionRankingItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailClanContributionRankingItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRank()) {
            this.innerSetRank(proto.getRank());
        } else {
            this.innerSetRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasContribution()) {
            this.innerSetContribution(proto.getContribution());
        } else {
            this.innerSetContribution(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MailClanContributionRankingItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailClanContributionRankingItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRank()) {
            this.setRank(proto.getRank());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasContribution()) {
            this.setContribution(proto.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingItem.Builder getCopyDbBuilder() {
        final MailClanContributionRankingItem.Builder builder = MailClanContributionRankingItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailClanContributionRankingItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRank() != 0) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }  else if (builder.hasRank()) {
            // 清理Rank
            builder.clearRank();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (this.getContribution() != 0L) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailClanContributionRankingItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailClanContributionRankingItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRank()) {
            this.innerSetRank(proto.getRank());
        } else {
            this.innerSetRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasContribution()) {
            this.innerSetContribution(proto.getContribution());
        } else {
            this.innerSetContribution(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MailClanContributionRankingItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailClanContributionRankingItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRank()) {
            this.setRank(proto.getRank());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasContribution()) {
            this.setContribution(proto.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingItem.Builder getCopySsBuilder() {
        final MailClanContributionRankingItem.Builder builder = MailClanContributionRankingItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailClanContributionRankingItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRank() != 0) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }  else if (builder.hasRank()) {
            // 清理Rank
            builder.clearRank();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (!this.getPlayerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }  else if (builder.hasPlayerName()) {
            // 清理PlayerName
            builder.clearPlayerName();
            fieldCnt++;
        }
        if (this.getContribution() != 0L) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }  else if (builder.hasContribution()) {
            // 清理Contribution
            builder.clearContribution();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailClanContributionRankingItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERNAME)) {
            builder.setPlayerName(this.getPlayerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CONTRIBUTION)) {
            builder.setContribution(this.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailClanContributionRankingItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRank()) {
            this.innerSetRank(proto.getRank());
        } else {
            this.innerSetRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerName()) {
            this.innerSetPlayerName(proto.getPlayerName());
        } else {
            this.innerSetPlayerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasContribution()) {
            this.innerSetContribution(proto.getContribution());
        } else {
            this.innerSetContribution(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return MailClanContributionRankingItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailClanContributionRankingItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRank()) {
            this.setRank(proto.getRank());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasPlayerName()) {
            this.setPlayerName(proto.getPlayerName());
            fieldCnt++;
        }
        if (proto.hasContribution()) {
            this.setContribution(proto.getContribution());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailClanContributionRankingItem.Builder builder = MailClanContributionRankingItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailClanContributionRankingItemProp)) {
            return false;
        }
        final MailClanContributionRankingItemProp otherNode = (MailClanContributionRankingItemProp) node;
        if (this.rank != otherNode.rank) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.playerName, otherNode.playerName)) {
            return false;
        }
        if (this.contribution != otherNode.contribution) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
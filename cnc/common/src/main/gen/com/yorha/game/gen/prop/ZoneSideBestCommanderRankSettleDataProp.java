package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zoneside.ZoneSideBestCommanderRankSettleData;
import com.yorha.proto.Zoneside;
import com.yorha.proto.ZonesidePB.ZoneSideBestCommanderRankSettleDataPB;
import com.yorha.proto.ZonesidePB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneSideBestCommanderRankSettleDataProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ACTID = 0;
    public static final int FIELD_INDEX_RANKMAP = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int actId = Constant.DEFAULT_INT_VALUE;
    private Int64ZoneSideBestCommanderRankSettleItemMapProp rankMap = null;

    public ZoneSideBestCommanderRankSettleDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneSideBestCommanderRankSettleDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actId
     *
     * @return actId value
     */
    public int getActId() {
        return this.actId;
    }

    /**
     * set actId && set marked
     *
     * @param actId new value
     * @return current object
     */
    public ZoneSideBestCommanderRankSettleDataProp setActId(int actId) {
        if (this.actId != actId) {
            this.mark(FIELD_INDEX_ACTID);
            this.actId = actId;
        }
        return this;
    }

    /**
     * inner set actId
     *
     * @param actId new value
     */
    private void innerSetActId(int actId) {
        this.actId = actId;
    }

    /**
     * get rankMap
     *
     * @return rankMap value
     */
    public Int64ZoneSideBestCommanderRankSettleItemMapProp getRankMap() {
        if (this.rankMap == null) {
            this.rankMap = new Int64ZoneSideBestCommanderRankSettleItemMapProp(this, FIELD_INDEX_RANKMAP);
        }
        return this.rankMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRankMapV(ZoneSideBestCommanderRankSettleItemProp v) {
        this.getRankMap().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ZoneSideBestCommanderRankSettleItemProp addEmptyRankMap(Long k) {
        return this.getRankMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRankMapSize() {
        if (this.rankMap == null) {
            return 0;
        }
        return this.rankMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRankMapEmpty() {
        if (this.rankMap == null) {
            return true;
        }
        return this.rankMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ZoneSideBestCommanderRankSettleItemProp getRankMapV(Long k) {
        if (this.rankMap == null || !this.rankMap.containsKey(k)) {
            return null;
        }
        return this.rankMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRankMap() {
        if (this.rankMap != null) {
            this.rankMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRankMapV(Long k) {
        if (this.rankMap != null) {
            this.rankMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderRankSettleDataPB.Builder getCopyCsBuilder() {
        final ZoneSideBestCommanderRankSettleDataPB.Builder builder = ZoneSideBestCommanderRankSettleDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneSideBestCommanderRankSettleDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.rankMap != null) {
            ZonesidePB.Int64ZoneSideBestCommanderRankSettleItemMapPB.Builder tmpBuilder = ZonesidePB.Int64ZoneSideBestCommanderRankSettleItemMapPB.newBuilder();
            final int tmpFieldCnt = this.rankMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankMap();
            }
        }  else if (builder.hasRankMap()) {
            // 清理RankMap
            builder.clearRankMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneSideBestCommanderRankSettleDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKMAP) && this.rankMap != null) {
            final boolean needClear = !builder.hasRankMap();
            final int tmpFieldCnt = this.rankMap.copyChangeToCs(builder.getRankMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneSideBestCommanderRankSettleDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKMAP) && this.rankMap != null) {
            final boolean needClear = !builder.hasRankMap();
            final int tmpFieldCnt = this.rankMap.copyChangeToAndClearDeleteKeysCs(builder.getRankMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneSideBestCommanderRankSettleDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRankMap()) {
            this.getRankMap().mergeFromCs(proto.getRankMap());
        } else {
            if (this.rankMap != null) {
                this.rankMap.mergeFromCs(proto.getRankMap());
            }
        }
        this.markAll();
        return ZoneSideBestCommanderRankSettleDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneSideBestCommanderRankSettleDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasRankMap()) {
            this.getRankMap().mergeChangeFromCs(proto.getRankMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderRankSettleData.Builder getCopyDbBuilder() {
        final ZoneSideBestCommanderRankSettleData.Builder builder = ZoneSideBestCommanderRankSettleData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneSideBestCommanderRankSettleData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.rankMap != null) {
            Zoneside.Int64ZoneSideBestCommanderRankSettleItemMap.Builder tmpBuilder = Zoneside.Int64ZoneSideBestCommanderRankSettleItemMap.newBuilder();
            final int tmpFieldCnt = this.rankMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankMap();
            }
        }  else if (builder.hasRankMap()) {
            // 清理RankMap
            builder.clearRankMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneSideBestCommanderRankSettleData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKMAP) && this.rankMap != null) {
            final boolean needClear = !builder.hasRankMap();
            final int tmpFieldCnt = this.rankMap.copyChangeToDb(builder.getRankMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneSideBestCommanderRankSettleData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRankMap()) {
            this.getRankMap().mergeFromDb(proto.getRankMap());
        } else {
            if (this.rankMap != null) {
                this.rankMap.mergeFromDb(proto.getRankMap());
            }
        }
        this.markAll();
        return ZoneSideBestCommanderRankSettleDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneSideBestCommanderRankSettleData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasRankMap()) {
            this.getRankMap().mergeChangeFromDb(proto.getRankMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideBestCommanderRankSettleData.Builder getCopySsBuilder() {
        final ZoneSideBestCommanderRankSettleData.Builder builder = ZoneSideBestCommanderRankSettleData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneSideBestCommanderRankSettleData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.rankMap != null) {
            Zoneside.Int64ZoneSideBestCommanderRankSettleItemMap.Builder tmpBuilder = Zoneside.Int64ZoneSideBestCommanderRankSettleItemMap.newBuilder();
            final int tmpFieldCnt = this.rankMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankMap();
            }
        }  else if (builder.hasRankMap()) {
            // 清理RankMap
            builder.clearRankMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneSideBestCommanderRankSettleData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RANKMAP) && this.rankMap != null) {
            final boolean needClear = !builder.hasRankMap();
            final int tmpFieldCnt = this.rankMap.copyChangeToSs(builder.getRankMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneSideBestCommanderRankSettleData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRankMap()) {
            this.getRankMap().mergeFromSs(proto.getRankMap());
        } else {
            if (this.rankMap != null) {
                this.rankMap.mergeFromSs(proto.getRankMap());
            }
        }
        this.markAll();
        return ZoneSideBestCommanderRankSettleDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneSideBestCommanderRankSettleData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasRankMap()) {
            this.getRankMap().mergeChangeFromSs(proto.getRankMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneSideBestCommanderRankSettleData.Builder builder = ZoneSideBestCommanderRankSettleData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RANKMAP) && this.rankMap != null) {
            this.rankMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rankMap != null) {
            this.rankMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.actId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneSideBestCommanderRankSettleDataProp)) {
            return false;
        }
        final ZoneSideBestCommanderRankSettleDataProp otherNode = (ZoneSideBestCommanderRankSettleDataProp) node;
        if (this.actId != otherNode.actId) {
            return false;
        }
        if (!this.getRankMap().compareDataTo(otherNode.getRankMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerSpyPlane;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerSpyPlanePB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerSpyPlaneProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_PLANEMODEL = 1;
    public static final int FIELD_INDEX_STATUS = 2;
    public static final int FIELD_INDEX_ISNEEDREMINDER = 3;
    public static final int FIELD_INDEX_SIMULATEMOVE = 4;
    public static final int FIELD_INDEX_SCENEID = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int planeModel = Constant.DEFAULT_INT_VALUE;
    private PlaneStatus status = PlaneStatus.forNumber(0);
    private boolean isNeedReminder = Constant.DEFAULT_BOOLEAN_VALUE;
    private SimulateMoveProp simulateMove = null;
    private long sceneId = Constant.DEFAULT_LONG_VALUE;

    public PlayerSpyPlaneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerSpyPlaneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public PlayerSpyPlaneProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get planeModel
     *
     * @return planeModel value
     */
    public int getPlaneModel() {
        return this.planeModel;
    }

    /**
     * set planeModel && set marked
     *
     * @param planeModel new value
     * @return current object
     */
    public PlayerSpyPlaneProp setPlaneModel(int planeModel) {
        if (this.planeModel != planeModel) {
            this.mark(FIELD_INDEX_PLANEMODEL);
            this.planeModel = planeModel;
        }
        return this;
    }

    /**
     * inner set planeModel
     *
     * @param planeModel new value
     */
    private void innerSetPlaneModel(int planeModel) {
        this.planeModel = planeModel;
    }

    /**
     * get status
     *
     * @return status value
     */
    public PlaneStatus getStatus() {
        return this.status;
    }

    /**
     * set status && set marked
     *
     * @param status new value
     * @return current object
     */
    public PlayerSpyPlaneProp setStatus(PlaneStatus status) {
        if (status == null) {
            throw new NullPointerException();
        }
        if (this.status != status) {
            this.mark(FIELD_INDEX_STATUS);
            this.status = status;
        }
        return this;
    }

    /**
     * inner set status
     *
     * @param status new value
     */
    private void innerSetStatus(PlaneStatus status) {
        this.status = status;
    }

    /**
     * get isNeedReminder
     *
     * @return isNeedReminder value
     */
    public boolean getIsNeedReminder() {
        return this.isNeedReminder;
    }

    /**
     * set isNeedReminder && set marked
     *
     * @param isNeedReminder new value
     * @return current object
     */
    public PlayerSpyPlaneProp setIsNeedReminder(boolean isNeedReminder) {
        if (this.isNeedReminder != isNeedReminder) {
            this.mark(FIELD_INDEX_ISNEEDREMINDER);
            this.isNeedReminder = isNeedReminder;
        }
        return this;
    }

    /**
     * inner set isNeedReminder
     *
     * @param isNeedReminder new value
     */
    private void innerSetIsNeedReminder(boolean isNeedReminder) {
        this.isNeedReminder = isNeedReminder;
    }

    /**
     * get simulateMove
     *
     * @return simulateMove value
     */
    public SimulateMoveProp getSimulateMove() {
        if (this.simulateMove == null) {
            this.simulateMove = new SimulateMoveProp(this, FIELD_INDEX_SIMULATEMOVE);
        }
        return this.simulateMove;
    }

    /**
     * get sceneId
     *
     * @return sceneId value
     */
    public long getSceneId() {
        return this.sceneId;
    }

    /**
     * set sceneId && set marked
     *
     * @param sceneId new value
     * @return current object
     */
    public PlayerSpyPlaneProp setSceneId(long sceneId) {
        if (this.sceneId != sceneId) {
            this.mark(FIELD_INDEX_SCENEID);
            this.sceneId = sceneId;
        }
        return this;
    }

    /**
     * inner set sceneId
     *
     * @param sceneId new value
     */
    private void innerSetSceneId(long sceneId) {
        this.sceneId = sceneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSpyPlanePB.Builder getCopyCsBuilder() {
        final PlayerSpyPlanePB.Builder builder = PlayerSpyPlanePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerSpyPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getStatus() != PlaneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getIsNeedReminder()) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }  else if (builder.hasIsNeedReminder()) {
            // 清理IsNeedReminder
            builder.clearIsNeedReminder();
            fieldCnt++;
        }
        if (this.getSceneId() != 0L) {
            builder.setSceneId(this.getSceneId());
            fieldCnt++;
        }  else if (builder.hasSceneId()) {
            // 清理SceneId
            builder.clearSceneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerSpyPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENEID)) {
            builder.setSceneId(this.getSceneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerSpyPlanePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCENEID)) {
            builder.setSceneId(this.getSceneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerSpyPlanePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(PlaneStatus.forNumber(0));
        }
        if (proto.hasIsNeedReminder()) {
            this.innerSetIsNeedReminder(proto.getIsNeedReminder());
        } else {
            this.innerSetIsNeedReminder(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSceneId()) {
            this.innerSetSceneId(proto.getSceneId());
        } else {
            this.innerSetSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerSpyPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerSpyPlanePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasIsNeedReminder()) {
            this.setIsNeedReminder(proto.getIsNeedReminder());
            fieldCnt++;
        }
        if (proto.hasSceneId()) {
            this.setSceneId(proto.getSceneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSpyPlane.Builder getCopyDbBuilder() {
        final PlayerSpyPlane.Builder builder = PlayerSpyPlane.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerSpyPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getStatus() != PlaneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getIsNeedReminder()) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }  else if (builder.hasIsNeedReminder()) {
            // 清理IsNeedReminder
            builder.clearIsNeedReminder();
            fieldCnt++;
        }
        if (this.simulateMove != null) {
            Player.SimulateMove.Builder tmpBuilder = Player.SimulateMove.newBuilder();
            final int tmpFieldCnt = this.simulateMove.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSimulateMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSimulateMove();
            }
        }  else if (builder.hasSimulateMove()) {
            // 清理SimulateMove
            builder.clearSimulateMove();
            fieldCnt++;
        }
        if (this.getSceneId() != 0L) {
            builder.setSceneId(this.getSceneId());
            fieldCnt++;
        }  else if (builder.hasSceneId()) {
            // 清理SceneId
            builder.clearSceneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerSpyPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMULATEMOVE) && this.simulateMove != null) {
            final boolean needClear = !builder.hasSimulateMove();
            final int tmpFieldCnt = this.simulateMove.copyChangeToDb(builder.getSimulateMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSimulateMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEID)) {
            builder.setSceneId(this.getSceneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerSpyPlane proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(PlaneStatus.forNumber(0));
        }
        if (proto.hasIsNeedReminder()) {
            this.innerSetIsNeedReminder(proto.getIsNeedReminder());
        } else {
            this.innerSetIsNeedReminder(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSimulateMove()) {
            this.getSimulateMove().mergeFromDb(proto.getSimulateMove());
        } else {
            if (this.simulateMove != null) {
                this.simulateMove.mergeFromDb(proto.getSimulateMove());
            }
        }
        if (proto.hasSceneId()) {
            this.innerSetSceneId(proto.getSceneId());
        } else {
            this.innerSetSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerSpyPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerSpyPlane proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasIsNeedReminder()) {
            this.setIsNeedReminder(proto.getIsNeedReminder());
            fieldCnt++;
        }
        if (proto.hasSimulateMove()) {
            this.getSimulateMove().mergeChangeFromDb(proto.getSimulateMove());
            fieldCnt++;
        }
        if (proto.hasSceneId()) {
            this.setSceneId(proto.getSceneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerSpyPlane.Builder getCopySsBuilder() {
        final PlayerSpyPlane.Builder builder = PlayerSpyPlane.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerSpyPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getPlaneModel() != 0) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }  else if (builder.hasPlaneModel()) {
            // 清理PlaneModel
            builder.clearPlaneModel();
            fieldCnt++;
        }
        if (this.getStatus() != PlaneStatus.forNumber(0)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }  else if (builder.hasStatus()) {
            // 清理Status
            builder.clearStatus();
            fieldCnt++;
        }
        if (this.getIsNeedReminder()) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }  else if (builder.hasIsNeedReminder()) {
            // 清理IsNeedReminder
            builder.clearIsNeedReminder();
            fieldCnt++;
        }
        if (this.simulateMove != null) {
            Player.SimulateMove.Builder tmpBuilder = Player.SimulateMove.newBuilder();
            final int tmpFieldCnt = this.simulateMove.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSimulateMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSimulateMove();
            }
        }  else if (builder.hasSimulateMove()) {
            // 清理SimulateMove
            builder.clearSimulateMove();
            fieldCnt++;
        }
        if (this.getSceneId() != 0L) {
            builder.setSceneId(this.getSceneId());
            fieldCnt++;
        }  else if (builder.hasSceneId()) {
            // 清理SceneId
            builder.clearSceneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerSpyPlane.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLANEMODEL)) {
            builder.setPlaneModel(this.getPlaneModel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATUS)) {
            builder.setStatus(this.getStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISNEEDREMINDER)) {
            builder.setIsNeedReminder(this.getIsNeedReminder());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIMULATEMOVE) && this.simulateMove != null) {
            final boolean needClear = !builder.hasSimulateMove();
            final int tmpFieldCnt = this.simulateMove.copyChangeToSs(builder.getSimulateMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSimulateMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEID)) {
            builder.setSceneId(this.getSceneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerSpyPlane proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlaneModel()) {
            this.innerSetPlaneModel(proto.getPlaneModel());
        } else {
            this.innerSetPlaneModel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStatus()) {
            this.innerSetStatus(proto.getStatus());
        } else {
            this.innerSetStatus(PlaneStatus.forNumber(0));
        }
        if (proto.hasIsNeedReminder()) {
            this.innerSetIsNeedReminder(proto.getIsNeedReminder());
        } else {
            this.innerSetIsNeedReminder(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasSimulateMove()) {
            this.getSimulateMove().mergeFromSs(proto.getSimulateMove());
        } else {
            if (this.simulateMove != null) {
                this.simulateMove.mergeFromSs(proto.getSimulateMove());
            }
        }
        if (proto.hasSceneId()) {
            this.innerSetSceneId(proto.getSceneId());
        } else {
            this.innerSetSceneId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerSpyPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerSpyPlane proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasPlaneModel()) {
            this.setPlaneModel(proto.getPlaneModel());
            fieldCnt++;
        }
        if (proto.hasStatus()) {
            this.setStatus(proto.getStatus());
            fieldCnt++;
        }
        if (proto.hasIsNeedReminder()) {
            this.setIsNeedReminder(proto.getIsNeedReminder());
            fieldCnt++;
        }
        if (proto.hasSimulateMove()) {
            this.getSimulateMove().mergeChangeFromSs(proto.getSimulateMove());
            fieldCnt++;
        }
        if (proto.hasSceneId()) {
            this.setSceneId(proto.getSceneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerSpyPlane.Builder builder = PlayerSpyPlane.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SIMULATEMOVE) && this.simulateMove != null) {
            this.simulateMove.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.simulateMove != null) {
            this.simulateMove.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerSpyPlaneProp)) {
            return false;
        }
        final PlayerSpyPlaneProp otherNode = (PlayerSpyPlaneProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.planeModel != otherNode.planeModel) {
            return false;
        }
        if (this.status != otherNode.status) {
            return false;
        }
        if (this.isNeedReminder != otherNode.isNeedReminder) {
            return false;
        }
        if (!this.getSimulateMove().compareDataTo(otherNode.getSimulateMove())) {
            return false;
        }
        if (this.sceneId != otherNode.sceneId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
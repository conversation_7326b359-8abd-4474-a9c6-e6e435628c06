package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.HuntingModel;
import com.yorha.proto.PlayerPB.HuntingModelPB;


/**
 * <AUTHOR> auto gen
 */
public class HuntingModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_KILLSTREAK = 0;
    public static final int FIELD_INDEX_CURRENTFOCUSMONSTERID = 1;
    public static final int FIELD_INDEX_CURRENTTARGETCOSTENERGY = 2;
    public static final int FIELD_INDEX_ALREADYBATTLED = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int killStreak = Constant.DEFAULT_INT_VALUE;
    private long currentFocusMonsterId = Constant.DEFAULT_LONG_VALUE;
    private int currentTargetCostEnergy = Constant.DEFAULT_INT_VALUE;
    private boolean alreadyBattled = Constant.DEFAULT_BOOLEAN_VALUE;

    public HuntingModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public HuntingModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get killStreak
     *
     * @return killStreak value
     */
    public int getKillStreak() {
        return this.killStreak;
    }

    /**
     * set killStreak && set marked
     *
     * @param killStreak new value
     * @return current object
     */
    public HuntingModelProp setKillStreak(int killStreak) {
        if (this.killStreak != killStreak) {
            this.mark(FIELD_INDEX_KILLSTREAK);
            this.killStreak = killStreak;
        }
        return this;
    }

    /**
     * inner set killStreak
     *
     * @param killStreak new value
     */
    private void innerSetKillStreak(int killStreak) {
        this.killStreak = killStreak;
    }

    /**
     * get currentFocusMonsterId
     *
     * @return currentFocusMonsterId value
     */
    public long getCurrentFocusMonsterId() {
        return this.currentFocusMonsterId;
    }

    /**
     * set currentFocusMonsterId && set marked
     *
     * @param currentFocusMonsterId new value
     * @return current object
     */
    public HuntingModelProp setCurrentFocusMonsterId(long currentFocusMonsterId) {
        if (this.currentFocusMonsterId != currentFocusMonsterId) {
            this.mark(FIELD_INDEX_CURRENTFOCUSMONSTERID);
            this.currentFocusMonsterId = currentFocusMonsterId;
        }
        return this;
    }

    /**
     * inner set currentFocusMonsterId
     *
     * @param currentFocusMonsterId new value
     */
    private void innerSetCurrentFocusMonsterId(long currentFocusMonsterId) {
        this.currentFocusMonsterId = currentFocusMonsterId;
    }

    /**
     * get currentTargetCostEnergy
     *
     * @return currentTargetCostEnergy value
     */
    public int getCurrentTargetCostEnergy() {
        return this.currentTargetCostEnergy;
    }

    /**
     * set currentTargetCostEnergy && set marked
     *
     * @param currentTargetCostEnergy new value
     * @return current object
     */
    public HuntingModelProp setCurrentTargetCostEnergy(int currentTargetCostEnergy) {
        if (this.currentTargetCostEnergy != currentTargetCostEnergy) {
            this.mark(FIELD_INDEX_CURRENTTARGETCOSTENERGY);
            this.currentTargetCostEnergy = currentTargetCostEnergy;
        }
        return this;
    }

    /**
     * inner set currentTargetCostEnergy
     *
     * @param currentTargetCostEnergy new value
     */
    private void innerSetCurrentTargetCostEnergy(int currentTargetCostEnergy) {
        this.currentTargetCostEnergy = currentTargetCostEnergy;
    }

    /**
     * get alreadyBattled
     *
     * @return alreadyBattled value
     */
    public boolean getAlreadyBattled() {
        return this.alreadyBattled;
    }

    /**
     * set alreadyBattled && set marked
     *
     * @param alreadyBattled new value
     * @return current object
     */
    public HuntingModelProp setAlreadyBattled(boolean alreadyBattled) {
        if (this.alreadyBattled != alreadyBattled) {
            this.mark(FIELD_INDEX_ALREADYBATTLED);
            this.alreadyBattled = alreadyBattled;
        }
        return this;
    }

    /**
     * inner set alreadyBattled
     *
     * @param alreadyBattled new value
     */
    private void innerSetAlreadyBattled(boolean alreadyBattled) {
        this.alreadyBattled = alreadyBattled;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HuntingModelPB.Builder getCopyCsBuilder() {
        final HuntingModelPB.Builder builder = HuntingModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(HuntingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKillStreak() != 0) {
            builder.setKillStreak(this.getKillStreak());
            fieldCnt++;
        }  else if (builder.hasKillStreak()) {
            // 清理KillStreak
            builder.clearKillStreak();
            fieldCnt++;
        }
        if (this.getCurrentFocusMonsterId() != 0L) {
            builder.setCurrentFocusMonsterId(this.getCurrentFocusMonsterId());
            fieldCnt++;
        }  else if (builder.hasCurrentFocusMonsterId()) {
            // 清理CurrentFocusMonsterId
            builder.clearCurrentFocusMonsterId();
            fieldCnt++;
        }
        if (this.getCurrentTargetCostEnergy() != 0) {
            builder.setCurrentTargetCostEnergy(this.getCurrentTargetCostEnergy());
            fieldCnt++;
        }  else if (builder.hasCurrentTargetCostEnergy()) {
            // 清理CurrentTargetCostEnergy
            builder.clearCurrentTargetCostEnergy();
            fieldCnt++;
        }
        if (this.getAlreadyBattled()) {
            builder.setAlreadyBattled(this.getAlreadyBattled());
            fieldCnt++;
        }  else if (builder.hasAlreadyBattled()) {
            // 清理AlreadyBattled
            builder.clearAlreadyBattled();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(HuntingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSTREAK)) {
            builder.setKillStreak(this.getKillStreak());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTFOCUSMONSTERID)) {
            builder.setCurrentFocusMonsterId(this.getCurrentFocusMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTTARGETCOSTENERGY)) {
            builder.setCurrentTargetCostEnergy(this.getCurrentTargetCostEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYBATTLED)) {
            builder.setAlreadyBattled(this.getAlreadyBattled());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(HuntingModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSTREAK)) {
            builder.setKillStreak(this.getKillStreak());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTFOCUSMONSTERID)) {
            builder.setCurrentFocusMonsterId(this.getCurrentFocusMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTTARGETCOSTENERGY)) {
            builder.setCurrentTargetCostEnergy(this.getCurrentTargetCostEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYBATTLED)) {
            builder.setAlreadyBattled(this.getAlreadyBattled());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(HuntingModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKillStreak()) {
            this.innerSetKillStreak(proto.getKillStreak());
        } else {
            this.innerSetKillStreak(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurrentFocusMonsterId()) {
            this.innerSetCurrentFocusMonsterId(proto.getCurrentFocusMonsterId());
        } else {
            this.innerSetCurrentFocusMonsterId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurrentTargetCostEnergy()) {
            this.innerSetCurrentTargetCostEnergy(proto.getCurrentTargetCostEnergy());
        } else {
            this.innerSetCurrentTargetCostEnergy(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAlreadyBattled()) {
            this.innerSetAlreadyBattled(proto.getAlreadyBattled());
        } else {
            this.innerSetAlreadyBattled(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return HuntingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(HuntingModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKillStreak()) {
            this.setKillStreak(proto.getKillStreak());
            fieldCnt++;
        }
        if (proto.hasCurrentFocusMonsterId()) {
            this.setCurrentFocusMonsterId(proto.getCurrentFocusMonsterId());
            fieldCnt++;
        }
        if (proto.hasCurrentTargetCostEnergy()) {
            this.setCurrentTargetCostEnergy(proto.getCurrentTargetCostEnergy());
            fieldCnt++;
        }
        if (proto.hasAlreadyBattled()) {
            this.setAlreadyBattled(proto.getAlreadyBattled());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HuntingModel.Builder getCopyDbBuilder() {
        final HuntingModel.Builder builder = HuntingModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(HuntingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKillStreak() != 0) {
            builder.setKillStreak(this.getKillStreak());
            fieldCnt++;
        }  else if (builder.hasKillStreak()) {
            // 清理KillStreak
            builder.clearKillStreak();
            fieldCnt++;
        }
        if (this.getCurrentFocusMonsterId() != 0L) {
            builder.setCurrentFocusMonsterId(this.getCurrentFocusMonsterId());
            fieldCnt++;
        }  else if (builder.hasCurrentFocusMonsterId()) {
            // 清理CurrentFocusMonsterId
            builder.clearCurrentFocusMonsterId();
            fieldCnt++;
        }
        if (this.getCurrentTargetCostEnergy() != 0) {
            builder.setCurrentTargetCostEnergy(this.getCurrentTargetCostEnergy());
            fieldCnt++;
        }  else if (builder.hasCurrentTargetCostEnergy()) {
            // 清理CurrentTargetCostEnergy
            builder.clearCurrentTargetCostEnergy();
            fieldCnt++;
        }
        if (this.getAlreadyBattled()) {
            builder.setAlreadyBattled(this.getAlreadyBattled());
            fieldCnt++;
        }  else if (builder.hasAlreadyBattled()) {
            // 清理AlreadyBattled
            builder.clearAlreadyBattled();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(HuntingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSTREAK)) {
            builder.setKillStreak(this.getKillStreak());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTFOCUSMONSTERID)) {
            builder.setCurrentFocusMonsterId(this.getCurrentFocusMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTTARGETCOSTENERGY)) {
            builder.setCurrentTargetCostEnergy(this.getCurrentTargetCostEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYBATTLED)) {
            builder.setAlreadyBattled(this.getAlreadyBattled());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(HuntingModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKillStreak()) {
            this.innerSetKillStreak(proto.getKillStreak());
        } else {
            this.innerSetKillStreak(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurrentFocusMonsterId()) {
            this.innerSetCurrentFocusMonsterId(proto.getCurrentFocusMonsterId());
        } else {
            this.innerSetCurrentFocusMonsterId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurrentTargetCostEnergy()) {
            this.innerSetCurrentTargetCostEnergy(proto.getCurrentTargetCostEnergy());
        } else {
            this.innerSetCurrentTargetCostEnergy(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAlreadyBattled()) {
            this.innerSetAlreadyBattled(proto.getAlreadyBattled());
        } else {
            this.innerSetAlreadyBattled(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return HuntingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(HuntingModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKillStreak()) {
            this.setKillStreak(proto.getKillStreak());
            fieldCnt++;
        }
        if (proto.hasCurrentFocusMonsterId()) {
            this.setCurrentFocusMonsterId(proto.getCurrentFocusMonsterId());
            fieldCnt++;
        }
        if (proto.hasCurrentTargetCostEnergy()) {
            this.setCurrentTargetCostEnergy(proto.getCurrentTargetCostEnergy());
            fieldCnt++;
        }
        if (proto.hasAlreadyBattled()) {
            this.setAlreadyBattled(proto.getAlreadyBattled());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public HuntingModel.Builder getCopySsBuilder() {
        final HuntingModel.Builder builder = HuntingModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(HuntingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getKillStreak() != 0) {
            builder.setKillStreak(this.getKillStreak());
            fieldCnt++;
        }  else if (builder.hasKillStreak()) {
            // 清理KillStreak
            builder.clearKillStreak();
            fieldCnt++;
        }
        if (this.getCurrentFocusMonsterId() != 0L) {
            builder.setCurrentFocusMonsterId(this.getCurrentFocusMonsterId());
            fieldCnt++;
        }  else if (builder.hasCurrentFocusMonsterId()) {
            // 清理CurrentFocusMonsterId
            builder.clearCurrentFocusMonsterId();
            fieldCnt++;
        }
        if (this.getCurrentTargetCostEnergy() != 0) {
            builder.setCurrentTargetCostEnergy(this.getCurrentTargetCostEnergy());
            fieldCnt++;
        }  else if (builder.hasCurrentTargetCostEnergy()) {
            // 清理CurrentTargetCostEnergy
            builder.clearCurrentTargetCostEnergy();
            fieldCnt++;
        }
        if (this.getAlreadyBattled()) {
            builder.setAlreadyBattled(this.getAlreadyBattled());
            fieldCnt++;
        }  else if (builder.hasAlreadyBattled()) {
            // 清理AlreadyBattled
            builder.clearAlreadyBattled();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(HuntingModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KILLSTREAK)) {
            builder.setKillStreak(this.getKillStreak());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTFOCUSMONSTERID)) {
            builder.setCurrentFocusMonsterId(this.getCurrentFocusMonsterId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURRENTTARGETCOSTENERGY)) {
            builder.setCurrentTargetCostEnergy(this.getCurrentTargetCostEnergy());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ALREADYBATTLED)) {
            builder.setAlreadyBattled(this.getAlreadyBattled());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(HuntingModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKillStreak()) {
            this.innerSetKillStreak(proto.getKillStreak());
        } else {
            this.innerSetKillStreak(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurrentFocusMonsterId()) {
            this.innerSetCurrentFocusMonsterId(proto.getCurrentFocusMonsterId());
        } else {
            this.innerSetCurrentFocusMonsterId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurrentTargetCostEnergy()) {
            this.innerSetCurrentTargetCostEnergy(proto.getCurrentTargetCostEnergy());
        } else {
            this.innerSetCurrentTargetCostEnergy(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAlreadyBattled()) {
            this.innerSetAlreadyBattled(proto.getAlreadyBattled());
        } else {
            this.innerSetAlreadyBattled(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return HuntingModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(HuntingModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKillStreak()) {
            this.setKillStreak(proto.getKillStreak());
            fieldCnt++;
        }
        if (proto.hasCurrentFocusMonsterId()) {
            this.setCurrentFocusMonsterId(proto.getCurrentFocusMonsterId());
            fieldCnt++;
        }
        if (proto.hasCurrentTargetCostEnergy()) {
            this.setCurrentTargetCostEnergy(proto.getCurrentTargetCostEnergy());
            fieldCnt++;
        }
        if (proto.hasAlreadyBattled()) {
            this.setAlreadyBattled(proto.getAlreadyBattled());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        HuntingModel.Builder builder = HuntingModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof HuntingModelProp)) {
            return false;
        }
        final HuntingModelProp otherNode = (HuntingModelProp) node;
        if (this.killStreak != otherNode.killStreak) {
            return false;
        }
        if (this.currentFocusMonsterId != otherNode.currentFocusMonsterId) {
            return false;
        }
        if (this.currentTargetCostEnergy != otherNode.currentTargetCostEnergy) {
            return false;
        }
        if (this.alreadyBattled != otherNode.alreadyBattled) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
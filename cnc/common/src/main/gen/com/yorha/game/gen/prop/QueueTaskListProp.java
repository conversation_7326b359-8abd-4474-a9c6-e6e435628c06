package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPlayerPB.QueueTaskListPB;
import com.yorha.proto.StructPlayer.QueueTaskList;
import com.yorha.proto.StructPlayerPB.QueueTaskPB;
import com.yorha.proto.StructPlayer.QueueTask;

/**
 * <AUTHOR> auto gen
 */
public class QueueTaskListProp extends AbstractListNode<QueueTaskProp> {
    /**
     * Create a QueueTaskListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public QueueTaskListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to QueueTaskListProp
     *
     * @return new object
     */
    @Override
    public QueueTaskProp addEmptyValue() {
        final QueueTaskProp newProp = new QueueTaskProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTaskListPB.Builder getCopyCsBuilder() {
        final QueueTaskListPB.Builder builder = QueueTaskListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(QueueTaskListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return QueueTaskListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final QueueTaskProp v : this) {
            final QueueTaskPB.Builder itemBuilder = QueueTaskPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return QueueTaskListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(QueueTaskListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return QueueTaskListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(QueueTaskListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (QueueTaskPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return QueueTaskListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(QueueTaskListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTaskList.Builder getCopyDbBuilder() {
        final QueueTaskList.Builder builder = QueueTaskList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(QueueTaskList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return QueueTaskListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final QueueTaskProp v : this) {
            final QueueTask.Builder itemBuilder = QueueTask.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return QueueTaskListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(QueueTaskList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return QueueTaskListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(QueueTaskList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (QueueTask v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return QueueTaskListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(QueueTaskList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public QueueTaskList.Builder getCopySsBuilder() {
        final QueueTaskList.Builder builder = QueueTaskList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(QueueTaskList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return QueueTaskListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final QueueTaskProp v : this) {
            final QueueTask.Builder itemBuilder = QueueTask.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return QueueTaskListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(QueueTaskList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return QueueTaskListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(QueueTaskList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (QueueTask v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return QueueTaskListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(QueueTaskList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        QueueTaskList.Builder builder = QueueTaskList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;


/**
 * <AUTHOR> auto gen
 */
public class ItemRewardProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ITEMTEMPLATEID = 0;
    public static final int FIELD_INDEX_COUNT = 1;
    public static final int FIELD_INDEX_WEIGHT = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int itemTemplateId = Constant.DEFAULT_INT_VALUE;
    private int count = Constant.DEFAULT_INT_VALUE;
    private int weight = Constant.DEFAULT_INT_VALUE;

    public ItemRewardProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ItemRewardProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get itemTemplateId
     *
     * @return itemTemplateId value
     */
    public int getItemTemplateId() {
        return this.itemTemplateId;
    }

    /**
     * set itemTemplateId && set marked
     *
     * @param itemTemplateId new value
     * @return current object
     */
    public ItemRewardProp setItemTemplateId(int itemTemplateId) {
        if (this.itemTemplateId != itemTemplateId) {
            this.mark(FIELD_INDEX_ITEMTEMPLATEID);
            this.itemTemplateId = itemTemplateId;
        }
        return this;
    }

    /**
     * inner set itemTemplateId
     *
     * @param itemTemplateId new value
     */
    private void innerSetItemTemplateId(int itemTemplateId) {
        this.itemTemplateId = itemTemplateId;
    }

    /**
     * get count
     *
     * @return count value
     */
    public int getCount() {
        return this.count;
    }

    /**
     * set count && set marked
     *
     * @param count new value
     * @return current object
     */
    public ItemRewardProp setCount(int count) {
        if (this.count != count) {
            this.mark(FIELD_INDEX_COUNT);
            this.count = count;
        }
        return this;
    }

    /**
     * inner set count
     *
     * @param count new value
     */
    private void innerSetCount(int count) {
        this.count = count;
    }

    /**
     * get weight
     *
     * @return weight value
     */
    public int getWeight() {
        return this.weight;
    }

    /**
     * set weight && set marked
     *
     * @param weight new value
     * @return current object
     */
    public ItemRewardProp setWeight(int weight) {
        if (this.weight != weight) {
            this.mark(FIELD_INDEX_WEIGHT);
            this.weight = weight;
        }
        return this;
    }

    /**
     * inner set weight
     *
     * @param weight new value
     */
    private void innerSetWeight(int weight) {
        this.weight = weight;
    }

                        

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ItemRewardProp)) {
            return false;
        }
        final ItemRewardProp otherNode = (ItemRewardProp) node;
        if (this.itemTemplateId != otherNode.itemTemplateId) {
            return false;
        }
        if (this.count != otherNode.count) {
            return false;
        }
        if (this.weight != otherNode.weight) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
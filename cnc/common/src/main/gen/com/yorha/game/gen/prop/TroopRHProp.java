package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.TroopRH;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.TroopRHPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class TroopRHProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_TROOPID = 0;
    public static final int FIELD_INDEX_TROOP = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int troopId = Constant.DEFAULT_INT_VALUE;
    private Int32FormationRHMapProp troop = null;

    public TroopRHProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TroopRHProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get troopId
     *
     * @return troopId value
     */
    public int getTroopId() {
        return this.troopId;
    }

    /**
     * set troopId && set marked
     *
     * @param troopId new value
     * @return current object
     */
    public TroopRHProp setTroopId(int troopId) {
        if (this.troopId != troopId) {
            this.mark(FIELD_INDEX_TROOPID);
            this.troopId = troopId;
        }
        return this;
    }

    /**
     * inner set troopId
     *
     * @param troopId new value
     */
    private void innerSetTroopId(int troopId) {
        this.troopId = troopId;
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public Int32FormationRHMapProp getTroop() {
        if (this.troop == null) {
            this.troop = new Int32FormationRHMapProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTroopV(FormationRHProp v) {
        this.getTroop().put(v.getSlotId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public FormationRHProp addEmptyTroop(Integer k) {
        return this.getTroop().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTroopSize() {
        if (this.troop == null) {
            return 0;
        }
        return this.troop.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTroopEmpty() {
        if (this.troop == null) {
            return true;
        }
        return this.troop.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public FormationRHProp getTroopV(Integer k) {
        if (this.troop == null || !this.troop.containsKey(k)) {
            return null;
        }
        return this.troop.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTroop() {
        if (this.troop != null) {
            this.troop.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTroopV(Integer k) {
        if (this.troop != null) {
            this.troop.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TroopRHPB.Builder getCopyCsBuilder() {
        final TroopRHPB.Builder builder = TroopRHPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TroopRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTroopId() != 0) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }  else if (builder.hasTroopId()) {
            // 清理TroopId
            builder.clearTroopId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayerPB.Int32FormationRHMapPB.Builder tmpBuilder = StructPlayerPB.Int32FormationRHMapPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TroopRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TroopRHPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TroopRHPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroopId()) {
            this.innerSetTroopId(proto.getTroopId());
        } else {
            this.innerSetTroopId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        this.markAll();
        return TroopRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TroopRHPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroopId()) {
            this.setTroopId(proto.getTroopId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TroopRH.Builder getCopyDbBuilder() {
        final TroopRH.Builder builder = TroopRH.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TroopRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTroopId() != 0) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }  else if (builder.hasTroopId()) {
            // 清理TroopId
            builder.clearTroopId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Int32FormationRHMap.Builder tmpBuilder = StructPlayer.Int32FormationRHMap.newBuilder();
            final int tmpFieldCnt = this.troop.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TroopRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToDb(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TroopRH proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroopId()) {
            this.innerSetTroopId(proto.getTroopId());
        } else {
            this.innerSetTroopId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromDb(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromDb(proto.getTroop());
            }
        }
        this.markAll();
        return TroopRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TroopRH proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroopId()) {
            this.setTroopId(proto.getTroopId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromDb(proto.getTroop());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TroopRH.Builder getCopySsBuilder() {
        final TroopRH.Builder builder = TroopRH.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TroopRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTroopId() != 0) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }  else if (builder.hasTroopId()) {
            // 清理TroopId
            builder.clearTroopId();
            fieldCnt++;
        }
        if (this.troop != null) {
            StructPlayer.Int32FormationRHMap.Builder tmpBuilder = StructPlayer.Int32FormationRHMap.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TroopRH.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TroopRH proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroopId()) {
            this.innerSetTroopId(proto.getTroopId());
        } else {
            this.innerSetTroopId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        this.markAll();
        return TroopRHProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TroopRH proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroopId()) {
            this.setTroopId(proto.getTroopId());
            fieldCnt++;
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TroopRH.Builder builder = TroopRH.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troop != null) {
            this.troop.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.troopId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TroopRHProp)) {
            return false;
        }
        final TroopRHProp otherNode = (TroopRHProp) node;
        if (this.troopId != otherNode.troopId) {
            return false;
        }
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
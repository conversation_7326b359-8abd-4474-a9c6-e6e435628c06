package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructMailPB.MailClanContributionRankingItemListPB;
import com.yorha.proto.StructMail.MailClanContributionRankingItemList;
import com.yorha.proto.StructMailPB.MailClanContributionRankingItemPB;
import com.yorha.proto.StructMail.MailClanContributionRankingItem;

/**
 * <AUTHOR> auto gen
 */
public class MailClanContributionRankingItemListProp extends AbstractListNode<MailClanContributionRankingItemProp> {
    /**
     * Create a MailClanContributionRankingItemListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public MailClanContributionRankingItemListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to MailClanContributionRankingItemListProp
     *
     * @return new object
     */
    @Override
    public MailClanContributionRankingItemProp addEmptyValue() {
        final MailClanContributionRankingItemProp newProp = new MailClanContributionRankingItemProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingItemListPB.Builder getCopyCsBuilder() {
        final MailClanContributionRankingItemListPB.Builder builder = MailClanContributionRankingItemListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(MailClanContributionRankingItemListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return MailClanContributionRankingItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final MailClanContributionRankingItemProp v : this) {
            final MailClanContributionRankingItemPB.Builder itemBuilder = MailClanContributionRankingItemPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(MailClanContributionRankingItemListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(MailClanContributionRankingItemListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (MailClanContributionRankingItemPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(MailClanContributionRankingItemListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingItemList.Builder getCopyDbBuilder() {
        final MailClanContributionRankingItemList.Builder builder = MailClanContributionRankingItemList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(MailClanContributionRankingItemList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return MailClanContributionRankingItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final MailClanContributionRankingItemProp v : this) {
            final MailClanContributionRankingItem.Builder itemBuilder = MailClanContributionRankingItem.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(MailClanContributionRankingItemList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(MailClanContributionRankingItemList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (MailClanContributionRankingItem v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(MailClanContributionRankingItemList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailClanContributionRankingItemList.Builder getCopySsBuilder() {
        final MailClanContributionRankingItemList.Builder builder = MailClanContributionRankingItemList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(MailClanContributionRankingItemList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return MailClanContributionRankingItemListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final MailClanContributionRankingItemProp v : this) {
            final MailClanContributionRankingItem.Builder itemBuilder = MailClanContributionRankingItem.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(MailClanContributionRankingItemList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(MailClanContributionRankingItemList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (MailClanContributionRankingItem v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return MailClanContributionRankingItemListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(MailClanContributionRankingItemList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        MailClanContributionRankingItemList.Builder builder = MailClanContributionRankingItemList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
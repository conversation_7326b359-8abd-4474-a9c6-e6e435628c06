package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.Troop;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.TroopPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class TroopProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TROOP = 0;
    public static final int FIELD_INDEX_MAINHERO = 1;
    public static final int FIELD_INDEX_DEPUTYHERO = 2;
    public static final int FIELD_INDEX_SCENEBATTLEPLANE = 3;
    public static final int FIELD_INDEX_TROOPID = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private Int32SoldierMapProp troop = null;
    private HeroProp mainHero = null;
    private HeroProp deputyHero = null;
    private SceneBattlePlaneProp sceneBattlePlane = null;
    private int troopId = Constant.DEFAULT_INT_VALUE;

    public TroopProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TroopProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get troop
     *
     * @return troop value
     */
    public Int32SoldierMapProp getTroop() {
        if (this.troop == null) {
            this.troop = new Int32SoldierMapProp(this, FIELD_INDEX_TROOP);
        }
        return this.troop;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTroopV(SoldierProp v) {
        this.getTroop().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SoldierProp addEmptyTroop(Integer k) {
        return this.getTroop().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTroopSize() {
        if (this.troop == null) {
            return 0;
        }
        return this.troop.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTroopEmpty() {
        if (this.troop == null) {
            return true;
        }
        return this.troop.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SoldierProp getTroopV(Integer k) {
        if (this.troop == null || !this.troop.containsKey(k)) {
            return null;
        }
        return this.troop.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTroop() {
        if (this.troop != null) {
            this.troop.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTroopV(Integer k) {
        if (this.troop != null) {
            this.troop.remove(k);
        }
    }
    /**
     * get mainHero
     *
     * @return mainHero value
     */
    public HeroProp getMainHero() {
        if (this.mainHero == null) {
            this.mainHero = new HeroProp(this, FIELD_INDEX_MAINHERO);
        }
        return this.mainHero;
    }

    /**
     * get deputyHero
     *
     * @return deputyHero value
     */
    public HeroProp getDeputyHero() {
        if (this.deputyHero == null) {
            this.deputyHero = new HeroProp(this, FIELD_INDEX_DEPUTYHERO);
        }
        return this.deputyHero;
    }

    /**
     * get sceneBattlePlane
     *
     * @return sceneBattlePlane value
     */
    public SceneBattlePlaneProp getSceneBattlePlane() {
        if (this.sceneBattlePlane == null) {
            this.sceneBattlePlane = new SceneBattlePlaneProp(this, FIELD_INDEX_SCENEBATTLEPLANE);
        }
        return this.sceneBattlePlane;
    }

    /**
     * get troopId
     *
     * @return troopId value
     */
    public int getTroopId() {
        return this.troopId;
    }

    /**
     * set troopId && set marked
     *
     * @param troopId new value
     * @return current object
     */
    public TroopProp setTroopId(int troopId) {
        if (this.troopId != troopId) {
            this.mark(FIELD_INDEX_TROOPID);
            this.troopId = troopId;
        }
        return this;
    }

    /**
     * inner set troopId
     *
     * @param troopId new value
     */
    private void innerSetTroopId(int troopId) {
        this.troopId = troopId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TroopPB.Builder getCopyCsBuilder() {
        final TroopPB.Builder builder = TroopPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TroopPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.troop != null) {
            StructPB.Int32SoldierMapPB.Builder tmpBuilder = StructPB.Int32SoldierMapPB.newBuilder();
            final int tmpFieldCnt = this.troop.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            StructPB.HeroPB.Builder tmpBuilder = StructPB.HeroPB.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            StructPB.HeroPB.Builder tmpBuilder = StructPB.HeroPB.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.sceneBattlePlane != null) {
            StructPB.SceneBattlePlanePB.Builder tmpBuilder = StructPB.SceneBattlePlanePB.newBuilder();
            final int tmpFieldCnt = this.sceneBattlePlane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneBattlePlane();
            }
        }  else if (builder.hasSceneBattlePlane()) {
            // 清理SceneBattlePlane
            builder.clearSceneBattlePlane();
            fieldCnt++;
        }
        if (this.getTroopId() != 0) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }  else if (builder.hasTroopId()) {
            // 清理TroopId
            builder.clearTroopId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TroopPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEBATTLEPLANE) && this.sceneBattlePlane != null) {
            final boolean needClear = !builder.hasSceneBattlePlane();
            final int tmpFieldCnt = this.sceneBattlePlane.copyChangeToCs(builder.getSceneBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TroopPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToAndClearDeleteKeysCs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToAndClearDeleteKeysCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToAndClearDeleteKeysCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEBATTLEPLANE) && this.sceneBattlePlane != null) {
            final boolean needClear = !builder.hasSceneBattlePlane();
            final int tmpFieldCnt = this.sceneBattlePlane.copyChangeToAndClearDeleteKeysCs(builder.getSceneBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TroopPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromCs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromCs(proto.getTroop());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromCs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromCs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromCs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromCs(proto.getDeputyHero());
            }
        }
        if (proto.hasSceneBattlePlane()) {
            this.getSceneBattlePlane().mergeFromCs(proto.getSceneBattlePlane());
        } else {
            if (this.sceneBattlePlane != null) {
                this.sceneBattlePlane.mergeFromCs(proto.getSceneBattlePlane());
            }
        }
        if (proto.hasTroopId()) {
            this.innerSetTroopId(proto.getTroopId());
        } else {
            this.innerSetTroopId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TroopProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TroopPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromCs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromCs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromCs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasSceneBattlePlane()) {
            this.getSceneBattlePlane().mergeChangeFromCs(proto.getSceneBattlePlane());
            fieldCnt++;
        }
        if (proto.hasTroopId()) {
            this.setTroopId(proto.getTroopId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Troop.Builder getCopyDbBuilder() {
        final Troop.Builder builder = Troop.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Troop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.troop != null) {
            Struct.Int32SoldierMap.Builder tmpBuilder = Struct.Int32SoldierMap.newBuilder();
            final int tmpFieldCnt = this.troop.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.sceneBattlePlane != null) {
            Struct.SceneBattlePlane.Builder tmpBuilder = Struct.SceneBattlePlane.newBuilder();
            final int tmpFieldCnt = this.sceneBattlePlane.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneBattlePlane();
            }
        }  else if (builder.hasSceneBattlePlane()) {
            // 清理SceneBattlePlane
            builder.clearSceneBattlePlane();
            fieldCnt++;
        }
        if (this.getTroopId() != 0) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }  else if (builder.hasTroopId()) {
            // 清理TroopId
            builder.clearTroopId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Troop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToDb(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToDb(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToDb(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEBATTLEPLANE) && this.sceneBattlePlane != null) {
            final boolean needClear = !builder.hasSceneBattlePlane();
            final int tmpFieldCnt = this.sceneBattlePlane.copyChangeToDb(builder.getSceneBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Troop proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromDb(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromDb(proto.getTroop());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromDb(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromDb(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromDb(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromDb(proto.getDeputyHero());
            }
        }
        if (proto.hasSceneBattlePlane()) {
            this.getSceneBattlePlane().mergeFromDb(proto.getSceneBattlePlane());
        } else {
            if (this.sceneBattlePlane != null) {
                this.sceneBattlePlane.mergeFromDb(proto.getSceneBattlePlane());
            }
        }
        if (proto.hasTroopId()) {
            this.innerSetTroopId(proto.getTroopId());
        } else {
            this.innerSetTroopId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TroopProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Troop proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromDb(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromDb(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromDb(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasSceneBattlePlane()) {
            this.getSceneBattlePlane().mergeChangeFromDb(proto.getSceneBattlePlane());
            fieldCnt++;
        }
        if (proto.hasTroopId()) {
            this.setTroopId(proto.getTroopId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Troop.Builder getCopySsBuilder() {
        final Troop.Builder builder = Troop.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Troop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.troop != null) {
            Struct.Int32SoldierMap.Builder tmpBuilder = Struct.Int32SoldierMap.newBuilder();
            final int tmpFieldCnt = this.troop.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTroop(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTroop();
            }
        }  else if (builder.hasTroop()) {
            // 清理Troop
            builder.clearTroop();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.sceneBattlePlane != null) {
            Struct.SceneBattlePlane.Builder tmpBuilder = Struct.SceneBattlePlane.newBuilder();
            final int tmpFieldCnt = this.sceneBattlePlane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSceneBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSceneBattlePlane();
            }
        }  else if (builder.hasSceneBattlePlane()) {
            // 清理SceneBattlePlane
            builder.clearSceneBattlePlane();
            fieldCnt++;
        }
        if (this.getTroopId() != 0) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }  else if (builder.hasTroopId()) {
            // 清理TroopId
            builder.clearTroopId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Troop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            final boolean needClear = !builder.hasTroop();
            final int tmpFieldCnt = this.troop.copyChangeToSs(builder.getTroopBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTroop();
            }
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToSs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToSs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCENEBATTLEPLANE) && this.sceneBattlePlane != null) {
            final boolean needClear = !builder.hasSceneBattlePlane();
            final int tmpFieldCnt = this.sceneBattlePlane.copyChangeToSs(builder.getSceneBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSceneBattlePlane();
            }
        }
        if (this.hasMark(FIELD_INDEX_TROOPID)) {
            builder.setTroopId(this.getTroopId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Troop proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTroop()) {
            this.getTroop().mergeFromSs(proto.getTroop());
        } else {
            if (this.troop != null) {
                this.troop.mergeFromSs(proto.getTroop());
            }
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromSs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromSs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromSs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromSs(proto.getDeputyHero());
            }
        }
        if (proto.hasSceneBattlePlane()) {
            this.getSceneBattlePlane().mergeFromSs(proto.getSceneBattlePlane());
        } else {
            if (this.sceneBattlePlane != null) {
                this.sceneBattlePlane.mergeFromSs(proto.getSceneBattlePlane());
            }
        }
        if (proto.hasTroopId()) {
            this.innerSetTroopId(proto.getTroopId());
        } else {
            this.innerSetTroopId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return TroopProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Troop proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTroop()) {
            this.getTroop().mergeChangeFromSs(proto.getTroop());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromSs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromSs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasSceneBattlePlane()) {
            this.getSceneBattlePlane().mergeChangeFromSs(proto.getSceneBattlePlane());
            fieldCnt++;
        }
        if (proto.hasTroopId()) {
            this.setTroopId(proto.getTroopId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Troop.Builder builder = Troop.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TROOP) && this.troop != null) {
            this.troop.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            this.mainHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            this.deputyHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCENEBATTLEPLANE) && this.sceneBattlePlane != null) {
            this.sceneBattlePlane.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.troop != null) {
            this.troop.markAll();
        }
        if (this.mainHero != null) {
            this.mainHero.markAll();
        }
        if (this.deputyHero != null) {
            this.deputyHero.markAll();
        }
        if (this.sceneBattlePlane != null) {
            this.sceneBattlePlane.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TroopProp)) {
            return false;
        }
        final TroopProp otherNode = (TroopProp) node;
        if (!this.getTroop().compareDataTo(otherNode.getTroop())) {
            return false;
        }
        if (!this.getMainHero().compareDataTo(otherNode.getMainHero())) {
            return false;
        }
        if (!this.getDeputyHero().compareDataTo(otherNode.getDeputyHero())) {
            return false;
        }
        if (!this.getSceneBattlePlane().compareDataTo(otherNode.getSceneBattlePlane())) {
            return false;
        }
        if (this.troopId != otherNode.troopId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
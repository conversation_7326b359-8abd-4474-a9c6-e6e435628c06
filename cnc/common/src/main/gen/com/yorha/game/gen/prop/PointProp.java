package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.Point;
import com.yorha.proto.StructPB.PointPB;


/**
 * <AUTHOR> auto gen
 */
public class PointProp extends AbstractPropNode {

    public static final int FIELD_INDEX_X = 0;
    public static final int FIELD_INDEX_Y = 1;
    public static final int FIELD_INDEX_MAPTYPE = 2;
    public static final int FIELD_INDEX_MAPID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int x = Constant.DEFAULT_INT_VALUE;
    private int y = Constant.DEFAULT_INT_VALUE;
    private int mapType = Constant.DEFAULT_INT_VALUE;
    private long mapId = Constant.DEFAULT_LONG_VALUE;

    public PointProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PointProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get x
     *
     * @return x value
     */
    public int getX() {
        return this.x;
    }

    /**
     * set x && set marked
     *
     * @param x new value
     * @return current object
     */
    public PointProp setX(int x) {
        if (this.x != x) {
            this.mark(FIELD_INDEX_X);
            this.x = x;
        }
        return this;
    }

    /**
     * inner set x
     *
     * @param x new value
     */
    private void innerSetX(int x) {
        this.x = x;
    }

    /**
     * get y
     *
     * @return y value
     */
    public int getY() {
        return this.y;
    }

    /**
     * set y && set marked
     *
     * @param y new value
     * @return current object
     */
    public PointProp setY(int y) {
        if (this.y != y) {
            this.mark(FIELD_INDEX_Y);
            this.y = y;
        }
        return this;
    }

    /**
     * inner set y
     *
     * @param y new value
     */
    private void innerSetY(int y) {
        this.y = y;
    }

    /**
     * get mapType
     *
     * @return mapType value
     */
    public int getMapType() {
        return this.mapType;
    }

    /**
     * set mapType && set marked
     *
     * @param mapType new value
     * @return current object
     */
    public PointProp setMapType(int mapType) {
        if (this.mapType != mapType) {
            this.mark(FIELD_INDEX_MAPTYPE);
            this.mapType = mapType;
        }
        return this;
    }

    /**
     * inner set mapType
     *
     * @param mapType new value
     */
    private void innerSetMapType(int mapType) {
        this.mapType = mapType;
    }

    /**
     * get mapId
     *
     * @return mapId value
     */
    public long getMapId() {
        return this.mapId;
    }

    /**
     * set mapId && set marked
     *
     * @param mapId new value
     * @return current object
     */
    public PointProp setMapId(long mapId) {
        if (this.mapId != mapId) {
            this.mark(FIELD_INDEX_MAPID);
            this.mapId = mapId;
        }
        return this;
    }

    /**
     * inner set mapId
     *
     * @param mapId new value
     */
    private void innerSetMapId(long mapId) {
        this.mapId = mapId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PointPB.Builder getCopyCsBuilder() {
        final PointPB.Builder builder = PointPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PointPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getX() != 0) {
            builder.setX(this.getX());
            fieldCnt++;
        }  else if (builder.hasX()) {
            // 清理X
            builder.clearX();
            fieldCnt++;
        }
        if (this.getY() != 0) {
            builder.setY(this.getY());
            fieldCnt++;
        }  else if (builder.hasY()) {
            // 清理Y
            builder.clearY();
            fieldCnt++;
        }
        if (this.getMapType() != 0) {
            builder.setMapType(this.getMapType());
            fieldCnt++;
        }  else if (builder.hasMapType()) {
            // 清理MapType
            builder.clearMapType();
            fieldCnt++;
        }
        if (this.getMapId() != 0L) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PointPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_X)) {
            builder.setX(this.getX());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_Y)) {
            builder.setY(this.getY());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPTYPE)) {
            builder.setMapType(this.getMapType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PointPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_X)) {
            builder.setX(this.getX());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_Y)) {
            builder.setY(this.getY());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPTYPE)) {
            builder.setMapType(this.getMapType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PointPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasX()) {
            this.innerSetX(proto.getX());
        } else {
            this.innerSetX(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasY()) {
            this.innerSetY(proto.getY());
        } else {
            this.innerSetY(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapType()) {
            this.innerSetMapType(proto.getMapType());
        } else {
            this.innerSetMapType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PointProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PointPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasX()) {
            this.setX(proto.getX());
            fieldCnt++;
        }
        if (proto.hasY()) {
            this.setY(proto.getY());
            fieldCnt++;
        }
        if (proto.hasMapType()) {
            this.setMapType(proto.getMapType());
            fieldCnt++;
        }
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Point.Builder getCopyDbBuilder() {
        final Point.Builder builder = Point.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Point.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getX() != 0) {
            builder.setX(this.getX());
            fieldCnt++;
        }  else if (builder.hasX()) {
            // 清理X
            builder.clearX();
            fieldCnt++;
        }
        if (this.getY() != 0) {
            builder.setY(this.getY());
            fieldCnt++;
        }  else if (builder.hasY()) {
            // 清理Y
            builder.clearY();
            fieldCnt++;
        }
        if (this.getMapType() != 0) {
            builder.setMapType(this.getMapType());
            fieldCnt++;
        }  else if (builder.hasMapType()) {
            // 清理MapType
            builder.clearMapType();
            fieldCnt++;
        }
        if (this.getMapId() != 0L) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Point.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_X)) {
            builder.setX(this.getX());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_Y)) {
            builder.setY(this.getY());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPTYPE)) {
            builder.setMapType(this.getMapType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Point proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasX()) {
            this.innerSetX(proto.getX());
        } else {
            this.innerSetX(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasY()) {
            this.innerSetY(proto.getY());
        } else {
            this.innerSetY(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapType()) {
            this.innerSetMapType(proto.getMapType());
        } else {
            this.innerSetMapType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PointProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Point proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasX()) {
            this.setX(proto.getX());
            fieldCnt++;
        }
        if (proto.hasY()) {
            this.setY(proto.getY());
            fieldCnt++;
        }
        if (proto.hasMapType()) {
            this.setMapType(proto.getMapType());
            fieldCnt++;
        }
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Point.Builder getCopySsBuilder() {
        final Point.Builder builder = Point.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Point.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getX() != 0) {
            builder.setX(this.getX());
            fieldCnt++;
        }  else if (builder.hasX()) {
            // 清理X
            builder.clearX();
            fieldCnt++;
        }
        if (this.getY() != 0) {
            builder.setY(this.getY());
            fieldCnt++;
        }  else if (builder.hasY()) {
            // 清理Y
            builder.clearY();
            fieldCnt++;
        }
        if (this.getMapType() != 0) {
            builder.setMapType(this.getMapType());
            fieldCnt++;
        }  else if (builder.hasMapType()) {
            // 清理MapType
            builder.clearMapType();
            fieldCnt++;
        }
        if (this.getMapId() != 0L) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }  else if (builder.hasMapId()) {
            // 清理MapId
            builder.clearMapId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Point.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_X)) {
            builder.setX(this.getX());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_Y)) {
            builder.setY(this.getY());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPTYPE)) {
            builder.setMapType(this.getMapType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAPID)) {
            builder.setMapId(this.getMapId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Point proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasX()) {
            this.innerSetX(proto.getX());
        } else {
            this.innerSetX(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasY()) {
            this.innerSetY(proto.getY());
        } else {
            this.innerSetY(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapType()) {
            this.innerSetMapType(proto.getMapType());
        } else {
            this.innerSetMapType(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMapId()) {
            this.innerSetMapId(proto.getMapId());
        } else {
            this.innerSetMapId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PointProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Point proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasX()) {
            this.setX(proto.getX());
            fieldCnt++;
        }
        if (proto.hasY()) {
            this.setY(proto.getY());
            fieldCnt++;
        }
        if (proto.hasMapType()) {
            this.setMapType(proto.getMapType());
            fieldCnt++;
        }
        if (proto.hasMapId()) {
            this.setMapId(proto.getMapId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Point.Builder builder = Point.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PointProp)) {
            return false;
        }
        final PointProp otherNode = (PointProp) node;
        if (this.x != otherNode.x) {
            return false;
        }
        if (this.y != otherNode.y) {
            return false;
        }
        if (this.mapType != otherNode.mapType) {
            return false;
        }
        if (this.mapId != otherNode.mapId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Zoneside.ZoneSideLotteryModel;
import com.yorha.proto.ZonesidePB.ZoneSideLotteryModelPB;


/**
 * <AUTHOR> auto gen
 */
public class ZoneSideLotteryModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURVOLUME = 0;
    public static final int FIELD_INDEX_CURSEASON = 1;
    public static final int FIELD_INDEX_CURSTAGE = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int curVolume = Constant.DEFAULT_INT_VALUE;
    private ZoneSeason curSeason = ZoneSeason.forNumber(0);
    private ZoneSeasonStage curStage = ZoneSeasonStage.forNumber(0);

    public ZoneSideLotteryModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ZoneSideLotteryModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get curVolume
     *
     * @return curVolume value
     */
    public int getCurVolume() {
        return this.curVolume;
    }

    /**
     * set curVolume && set marked
     *
     * @param curVolume new value
     * @return current object
     */
    public ZoneSideLotteryModelProp setCurVolume(int curVolume) {
        if (this.curVolume != curVolume) {
            this.mark(FIELD_INDEX_CURVOLUME);
            this.curVolume = curVolume;
        }
        return this;
    }

    /**
     * inner set curVolume
     *
     * @param curVolume new value
     */
    private void innerSetCurVolume(int curVolume) {
        this.curVolume = curVolume;
    }

    /**
     * get curSeason
     *
     * @return curSeason value
     */
    public ZoneSeason getCurSeason() {
        return this.curSeason;
    }

    /**
     * set curSeason && set marked
     *
     * @param curSeason new value
     * @return current object
     */
    public ZoneSideLotteryModelProp setCurSeason(ZoneSeason curSeason) {
        if (curSeason == null) {
            throw new NullPointerException();
        }
        if (this.curSeason != curSeason) {
            this.mark(FIELD_INDEX_CURSEASON);
            this.curSeason = curSeason;
        }
        return this;
    }

    /**
     * inner set curSeason
     *
     * @param curSeason new value
     */
    private void innerSetCurSeason(ZoneSeason curSeason) {
        this.curSeason = curSeason;
    }

    /**
     * get curStage
     *
     * @return curStage value
     */
    public ZoneSeasonStage getCurStage() {
        return this.curStage;
    }

    /**
     * set curStage && set marked
     *
     * @param curStage new value
     * @return current object
     */
    public ZoneSideLotteryModelProp setCurStage(ZoneSeasonStage curStage) {
        if (curStage == null) {
            throw new NullPointerException();
        }
        if (this.curStage != curStage) {
            this.mark(FIELD_INDEX_CURSTAGE);
            this.curStage = curStage;
        }
        return this;
    }

    /**
     * inner set curStage
     *
     * @param curStage new value
     */
    private void innerSetCurStage(ZoneSeasonStage curStage) {
        this.curStage = curStage;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideLotteryModelPB.Builder getCopyCsBuilder() {
        final ZoneSideLotteryModelPB.Builder builder = ZoneSideLotteryModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ZoneSideLotteryModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurVolume() != 0) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }  else if (builder.hasCurVolume()) {
            // 清理CurVolume
            builder.clearCurVolume();
            fieldCnt++;
        }
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ZoneSideLotteryModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ZoneSideLotteryModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ZoneSideLotteryModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurVolume()) {
            this.innerSetCurVolume(proto.getCurVolume());
        } else {
            this.innerSetCurVolume(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideLotteryModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ZoneSideLotteryModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurVolume()) {
            this.setCurVolume(proto.getCurVolume());
            fieldCnt++;
        }
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideLotteryModel.Builder getCopyDbBuilder() {
        final ZoneSideLotteryModel.Builder builder = ZoneSideLotteryModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ZoneSideLotteryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurVolume() != 0) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }  else if (builder.hasCurVolume()) {
            // 清理CurVolume
            builder.clearCurVolume();
            fieldCnt++;
        }
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ZoneSideLotteryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ZoneSideLotteryModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurVolume()) {
            this.innerSetCurVolume(proto.getCurVolume());
        } else {
            this.innerSetCurVolume(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideLotteryModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ZoneSideLotteryModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurVolume()) {
            this.setCurVolume(proto.getCurVolume());
            fieldCnt++;
        }
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ZoneSideLotteryModel.Builder getCopySsBuilder() {
        final ZoneSideLotteryModel.Builder builder = ZoneSideLotteryModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ZoneSideLotteryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurVolume() != 0) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }  else if (builder.hasCurVolume()) {
            // 清理CurVolume
            builder.clearCurVolume();
            fieldCnt++;
        }
        if (this.getCurSeason() != ZoneSeason.forNumber(0)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }  else if (builder.hasCurSeason()) {
            // 清理CurSeason
            builder.clearCurSeason();
            fieldCnt++;
        }
        if (this.getCurStage() != ZoneSeasonStage.forNumber(0)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }  else if (builder.hasCurStage()) {
            // 清理CurStage
            builder.clearCurStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ZoneSideLotteryModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSEASON)) {
            builder.setCurSeason(this.getCurSeason());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSTAGE)) {
            builder.setCurStage(this.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ZoneSideLotteryModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurVolume()) {
            this.innerSetCurVolume(proto.getCurVolume());
        } else {
            this.innerSetCurVolume(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurSeason()) {
            this.innerSetCurSeason(proto.getCurSeason());
        } else {
            this.innerSetCurSeason(ZoneSeason.forNumber(0));
        }
        if (proto.hasCurStage()) {
            this.innerSetCurStage(proto.getCurStage());
        } else {
            this.innerSetCurStage(ZoneSeasonStage.forNumber(0));
        }
        this.markAll();
        return ZoneSideLotteryModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ZoneSideLotteryModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurVolume()) {
            this.setCurVolume(proto.getCurVolume());
            fieldCnt++;
        }
        if (proto.hasCurSeason()) {
            this.setCurSeason(proto.getCurSeason());
            fieldCnt++;
        }
        if (proto.hasCurStage()) {
            this.setCurStage(proto.getCurStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ZoneSideLotteryModel.Builder builder = ZoneSideLotteryModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ZoneSideLotteryModelProp)) {
            return false;
        }
        final ZoneSideLotteryModelProp otherNode = (ZoneSideLotteryModelProp) node;
        if (this.curVolume != otherNode.curVolume) {
            return false;
        }
        if (this.curSeason != otherNode.curSeason) {
            return false;
        }
        if (this.curStage != otherNode.curStage) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
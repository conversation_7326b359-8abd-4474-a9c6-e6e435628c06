package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerResellTriggerBundle;
import com.yorha.proto.StructPB.PlayerResellTriggerBundlePB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerResellTriggerBundleProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;
    public static final int FIELD_INDEX_BOUGHT = 2;
    public static final int FIELD_INDEX_GOODSSHOWNTIMES = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private boolean bought = Constant.DEFAULT_BOOLEAN_VALUE;
    private int goodsShownTimes = Constant.DEFAULT_INT_VALUE;

    public PlayerResellTriggerBundleProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerResellTriggerBundleProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public PlayerResellTriggerBundleProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public PlayerResellTriggerBundleProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get bought
     *
     * @return bought value
     */
    public boolean getBought() {
        return this.bought;
    }

    /**
     * set bought && set marked
     *
     * @param bought new value
     * @return current object
     */
    public PlayerResellTriggerBundleProp setBought(boolean bought) {
        if (this.bought != bought) {
            this.mark(FIELD_INDEX_BOUGHT);
            this.bought = bought;
        }
        return this;
    }

    /**
     * inner set bought
     *
     * @param bought new value
     */
    private void innerSetBought(boolean bought) {
        this.bought = bought;
    }

    /**
     * get goodsShownTimes
     *
     * @return goodsShownTimes value
     */
    public int getGoodsShownTimes() {
        return this.goodsShownTimes;
    }

    /**
     * set goodsShownTimes && set marked
     *
     * @param goodsShownTimes new value
     * @return current object
     */
    public PlayerResellTriggerBundleProp setGoodsShownTimes(int goodsShownTimes) {
        if (this.goodsShownTimes != goodsShownTimes) {
            this.mark(FIELD_INDEX_GOODSSHOWNTIMES);
            this.goodsShownTimes = goodsShownTimes;
        }
        return this;
    }

    /**
     * inner set goodsShownTimes
     *
     * @param goodsShownTimes new value
     */
    private void innerSetGoodsShownTimes(int goodsShownTimes) {
        this.goodsShownTimes = goodsShownTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResellTriggerBundlePB.Builder getCopyCsBuilder() {
        final PlayerResellTriggerBundlePB.Builder builder = PlayerResellTriggerBundlePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerResellTriggerBundlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getBought()) {
            builder.setBought(this.getBought());
            fieldCnt++;
        }  else if (builder.hasBought()) {
            // 清理Bought
            builder.clearBought();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerResellTriggerBundlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHT)) {
            builder.setBought(this.getBought());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerResellTriggerBundlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHT)) {
            builder.setBought(this.getBought());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerResellTriggerBundlePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBought()) {
            this.innerSetBought(proto.getBought());
        } else {
            this.innerSetBought(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PlayerResellTriggerBundleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerResellTriggerBundlePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasBought()) {
            this.setBought(proto.getBought());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResellTriggerBundle.Builder getCopyDbBuilder() {
        final PlayerResellTriggerBundle.Builder builder = PlayerResellTriggerBundle.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerResellTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getBought()) {
            builder.setBought(this.getBought());
            fieldCnt++;
        }  else if (builder.hasBought()) {
            // 清理Bought
            builder.clearBought();
            fieldCnt++;
        }
        if (this.getGoodsShownTimes() != 0) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }  else if (builder.hasGoodsShownTimes()) {
            // 清理GoodsShownTimes
            builder.clearGoodsShownTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerResellTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHT)) {
            builder.setBought(this.getBought());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSSHOWNTIMES)) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerResellTriggerBundle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBought()) {
            this.innerSetBought(proto.getBought());
        } else {
            this.innerSetBought(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasGoodsShownTimes()) {
            this.innerSetGoodsShownTimes(proto.getGoodsShownTimes());
        } else {
            this.innerSetGoodsShownTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerResellTriggerBundleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerResellTriggerBundle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasBought()) {
            this.setBought(proto.getBought());
            fieldCnt++;
        }
        if (proto.hasGoodsShownTimes()) {
            this.setGoodsShownTimes(proto.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerResellTriggerBundle.Builder getCopySsBuilder() {
        final PlayerResellTriggerBundle.Builder builder = PlayerResellTriggerBundle.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerResellTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getBought()) {
            builder.setBought(this.getBought());
            fieldCnt++;
        }  else if (builder.hasBought()) {
            // 清理Bought
            builder.clearBought();
            fieldCnt++;
        }
        if (this.getGoodsShownTimes() != 0) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }  else if (builder.hasGoodsShownTimes()) {
            // 清理GoodsShownTimes
            builder.clearGoodsShownTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerResellTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BOUGHT)) {
            builder.setBought(this.getBought());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSSHOWNTIMES)) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerResellTriggerBundle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasBought()) {
            this.innerSetBought(proto.getBought());
        } else {
            this.innerSetBought(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasGoodsShownTimes()) {
            this.innerSetGoodsShownTimes(proto.getGoodsShownTimes());
        } else {
            this.innerSetGoodsShownTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerResellTriggerBundleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerResellTriggerBundle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasBought()) {
            this.setBought(proto.getBought());
            fieldCnt++;
        }
        if (proto.hasGoodsShownTimes()) {
            this.setGoodsShownTimes(proto.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerResellTriggerBundle.Builder builder = PlayerResellTriggerBundle.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerResellTriggerBundleProp)) {
            return false;
        }
        final PlayerResellTriggerBundleProp otherNode = (PlayerResellTriggerBundleProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.bought != otherNode.bought) {
            return false;
        }
        if (this.goodsShownTimes != otherNode.goodsShownTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
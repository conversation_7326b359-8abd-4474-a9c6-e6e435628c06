package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.AssistRecord;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.AssistRecordPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class AssistRecordProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SOLDIERNUM = 0;
    public static final int FIELD_INDEX_ASSISTTSMS = 1;
    public static final int FIELD_INDEX_PLAYERID = 2;
    public static final int FIELD_INDEX_CARDHEAD = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int soldierNum = Constant.DEFAULT_INT_VALUE;
    private long assistTsMs = Constant.DEFAULT_LONG_VALUE;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp cardHead = null;

    public AssistRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public AssistRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldierNum
     *
     * @return soldierNum value
     */
    public int getSoldierNum() {
        return this.soldierNum;
    }

    /**
     * set soldierNum && set marked
     *
     * @param soldierNum new value
     * @return current object
     */
    public AssistRecordProp setSoldierNum(int soldierNum) {
        if (this.soldierNum != soldierNum) {
            this.mark(FIELD_INDEX_SOLDIERNUM);
            this.soldierNum = soldierNum;
        }
        return this;
    }

    /**
     * inner set soldierNum
     *
     * @param soldierNum new value
     */
    private void innerSetSoldierNum(int soldierNum) {
        this.soldierNum = soldierNum;
    }

    /**
     * get assistTsMs
     *
     * @return assistTsMs value
     */
    public long getAssistTsMs() {
        return this.assistTsMs;
    }

    /**
     * set assistTsMs && set marked
     *
     * @param assistTsMs new value
     * @return current object
     */
    public AssistRecordProp setAssistTsMs(long assistTsMs) {
        if (this.assistTsMs != assistTsMs) {
            this.mark(FIELD_INDEX_ASSISTTSMS);
            this.assistTsMs = assistTsMs;
        }
        return this;
    }

    /**
     * inner set assistTsMs
     *
     * @param assistTsMs new value
     */
    private void innerSetAssistTsMs(long assistTsMs) {
        this.assistTsMs = assistTsMs;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public AssistRecordProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AssistRecordPB.Builder getCopyCsBuilder() {
        final AssistRecordPB.Builder builder = AssistRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(AssistRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierNum() != 0) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }  else if (builder.hasSoldierNum()) {
            // 清理SoldierNum
            builder.clearSoldierNum();
            fieldCnt++;
        }
        if (this.getAssistTsMs() != 0L) {
            builder.setAssistTsMs(this.getAssistTsMs());
            fieldCnt++;
        }  else if (builder.hasAssistTsMs()) {
            // 清理AssistTsMs
            builder.clearAssistTsMs();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(AssistRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTTSMS)) {
            builder.setAssistTsMs(this.getAssistTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(AssistRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTTSMS)) {
            builder.setAssistTsMs(this.getAssistTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(AssistRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierNum()) {
            this.innerSetSoldierNum(proto.getSoldierNum());
        } else {
            this.innerSetSoldierNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAssistTsMs()) {
            this.innerSetAssistTsMs(proto.getAssistTsMs());
        } else {
            this.innerSetAssistTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        this.markAll();
        return AssistRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(AssistRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierNum()) {
            this.setSoldierNum(proto.getSoldierNum());
            fieldCnt++;
        }
        if (proto.hasAssistTsMs()) {
            this.setAssistTsMs(proto.getAssistTsMs());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AssistRecord.Builder getCopyDbBuilder() {
        final AssistRecord.Builder builder = AssistRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(AssistRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierNum() != 0) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }  else if (builder.hasSoldierNum()) {
            // 清理SoldierNum
            builder.clearSoldierNum();
            fieldCnt++;
        }
        if (this.getAssistTsMs() != 0L) {
            builder.setAssistTsMs(this.getAssistTsMs());
            fieldCnt++;
        }  else if (builder.hasAssistTsMs()) {
            // 清理AssistTsMs
            builder.clearAssistTsMs();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(AssistRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTTSMS)) {
            builder.setAssistTsMs(this.getAssistTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(AssistRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierNum()) {
            this.innerSetSoldierNum(proto.getSoldierNum());
        } else {
            this.innerSetSoldierNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAssistTsMs()) {
            this.innerSetAssistTsMs(proto.getAssistTsMs());
        } else {
            this.innerSetAssistTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        this.markAll();
        return AssistRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(AssistRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierNum()) {
            this.setSoldierNum(proto.getSoldierNum());
            fieldCnt++;
        }
        if (proto.hasAssistTsMs()) {
            this.setAssistTsMs(proto.getAssistTsMs());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public AssistRecord.Builder getCopySsBuilder() {
        final AssistRecord.Builder builder = AssistRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(AssistRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getSoldierNum() != 0) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }  else if (builder.hasSoldierNum()) {
            // 清理SoldierNum
            builder.clearSoldierNum();
            fieldCnt++;
        }
        if (this.getAssistTsMs() != 0L) {
            builder.setAssistTsMs(this.getAssistTsMs());
            fieldCnt++;
        }  else if (builder.hasAssistTsMs()) {
            // 清理AssistTsMs
            builder.clearAssistTsMs();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(AssistRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERNUM)) {
            builder.setSoldierNum(this.getSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ASSISTTSMS)) {
            builder.setAssistTsMs(this.getAssistTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(AssistRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierNum()) {
            this.innerSetSoldierNum(proto.getSoldierNum());
        } else {
            this.innerSetSoldierNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAssistTsMs()) {
            this.innerSetAssistTsMs(proto.getAssistTsMs());
        } else {
            this.innerSetAssistTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        this.markAll();
        return AssistRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(AssistRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierNum()) {
            this.setSoldierNum(proto.getSoldierNum());
            fieldCnt++;
        }
        if (proto.hasAssistTsMs()) {
            this.setAssistTsMs(proto.getAssistTsMs());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        AssistRecord.Builder builder = AssistRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof AssistRecordProp)) {
            return false;
        }
        final AssistRecordProp otherNode = (AssistRecordProp) node;
        if (this.soldierNum != otherNode.soldierNum) {
            return false;
        }
        if (this.assistTsMs != otherNode.assistTsMs) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
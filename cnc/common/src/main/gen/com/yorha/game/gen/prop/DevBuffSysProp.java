package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.DevBuffSys;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.DevBuffSysPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class DevBuffSysProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DEVBUFF = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32DevBuffMapProp devBuff = null;

    public DevBuffSysProp() {
        super(null, 0, FIELD_COUNT);
    }

    public DevBuffSysProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get devBuff
     *
     * @return devBuff value
     */
    public Int32DevBuffMapProp getDevBuff() {
        if (this.devBuff == null) {
            this.devBuff = new Int32DevBuffMapProp(this, FIELD_INDEX_DEVBUFF);
        }
        return this.devBuff;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDevBuffV(DevBuffProp v) {
        this.getDevBuff().put(v.getDevBuffId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public DevBuffProp addEmptyDevBuff(Integer k) {
        return this.getDevBuff().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDevBuffSize() {
        if (this.devBuff == null) {
            return 0;
        }
        return this.devBuff.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDevBuffEmpty() {
        if (this.devBuff == null) {
            return true;
        }
        return this.devBuff.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public DevBuffProp getDevBuffV(Integer k) {
        if (this.devBuff == null || !this.devBuff.containsKey(k)) {
            return null;
        }
        return this.devBuff.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDevBuff() {
        if (this.devBuff != null) {
            this.devBuff.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDevBuffV(Integer k) {
        if (this.devBuff != null) {
            this.devBuff.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DevBuffSysPB.Builder getCopyCsBuilder() {
        final DevBuffSysPB.Builder builder = DevBuffSysPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(DevBuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.devBuff != null) {
            StructBattlePB.Int32DevBuffMapPB.Builder tmpBuilder = StructBattlePB.Int32DevBuffMapPB.newBuilder();
            final int tmpFieldCnt = this.devBuff.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuff();
            }
        }  else if (builder.hasDevBuff()) {
            // 清理DevBuff
            builder.clearDevBuff();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(DevBuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFF) && this.devBuff != null) {
            final boolean needClear = !builder.hasDevBuff();
            final int tmpFieldCnt = this.devBuff.copyChangeToCs(builder.getDevBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(DevBuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFF) && this.devBuff != null) {
            final boolean needClear = !builder.hasDevBuff();
            final int tmpFieldCnt = this.devBuff.copyChangeToAndClearDeleteKeysCs(builder.getDevBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(DevBuffSysPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuff()) {
            this.getDevBuff().mergeFromCs(proto.getDevBuff());
        } else {
            if (this.devBuff != null) {
                this.devBuff.mergeFromCs(proto.getDevBuff());
            }
        }
        this.markAll();
        return DevBuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(DevBuffSysPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuff()) {
            this.getDevBuff().mergeChangeFromCs(proto.getDevBuff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DevBuffSys.Builder getCopyDbBuilder() {
        final DevBuffSys.Builder builder = DevBuffSys.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(DevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.devBuff != null) {
            StructBattle.Int32DevBuffMap.Builder tmpBuilder = StructBattle.Int32DevBuffMap.newBuilder();
            final int tmpFieldCnt = this.devBuff.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuff();
            }
        }  else if (builder.hasDevBuff()) {
            // 清理DevBuff
            builder.clearDevBuff();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(DevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFF) && this.devBuff != null) {
            final boolean needClear = !builder.hasDevBuff();
            final int tmpFieldCnt = this.devBuff.copyChangeToDb(builder.getDevBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(DevBuffSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuff()) {
            this.getDevBuff().mergeFromDb(proto.getDevBuff());
        } else {
            if (this.devBuff != null) {
                this.devBuff.mergeFromDb(proto.getDevBuff());
            }
        }
        this.markAll();
        return DevBuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(DevBuffSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuff()) {
            this.getDevBuff().mergeChangeFromDb(proto.getDevBuff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public DevBuffSys.Builder getCopySsBuilder() {
        final DevBuffSys.Builder builder = DevBuffSys.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(DevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.devBuff != null) {
            StructBattle.Int32DevBuffMap.Builder tmpBuilder = StructBattle.Int32DevBuffMap.newBuilder();
            final int tmpFieldCnt = this.devBuff.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuff();
            }
        }  else if (builder.hasDevBuff()) {
            // 清理DevBuff
            builder.clearDevBuff();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(DevBuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFF) && this.devBuff != null) {
            final boolean needClear = !builder.hasDevBuff();
            final int tmpFieldCnt = this.devBuff.copyChangeToSs(builder.getDevBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(DevBuffSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuff()) {
            this.getDevBuff().mergeFromSs(proto.getDevBuff());
        } else {
            if (this.devBuff != null) {
                this.devBuff.mergeFromSs(proto.getDevBuff());
            }
        }
        this.markAll();
        return DevBuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(DevBuffSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuff()) {
            this.getDevBuff().mergeChangeFromSs(proto.getDevBuff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        DevBuffSys.Builder builder = DevBuffSys.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFF) && this.devBuff != null) {
            this.devBuff.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.devBuff != null) {
            this.devBuff.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof DevBuffSysProp)) {
            return false;
        }
        final DevBuffSysProp otherNode = (DevBuffSysProp) node;
        if (!this.getDevBuff().compareDataTo(otherNode.getDevBuff())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
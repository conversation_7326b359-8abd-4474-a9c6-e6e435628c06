package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityOnlineRewardUnit;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPB.ActivityOnlineRewardUnitPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityOnlineRewardUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REWARDTAKENIDS = 0;
    public static final int FIELD_INDEX_CANRECEIVETSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32ListProp rewardTakenIds = null;
    private long canReceiveTsMs = Constant.DEFAULT_LONG_VALUE;

    public ActivityOnlineRewardUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityOnlineRewardUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardTakenIds
     *
     * @return rewardTakenIds value
     */
    public Int32ListProp getRewardTakenIds() {
        if (this.rewardTakenIds == null) {
            this.rewardTakenIds = new Int32ListProp(this, FIELD_INDEX_REWARDTAKENIDS);
        }
        return this.rewardTakenIds;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRewardTakenIds(Integer v) {
        this.getRewardTakenIds().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getRewardTakenIdsIndex(int index) {
        return this.getRewardTakenIds().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeRewardTakenIds(Integer v) {
        if (this.rewardTakenIds == null) {
            return null;
        }
        if(this.rewardTakenIds.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRewardTakenIdsSize() {
        if (this.rewardTakenIds == null) {
            return 0;
        }
        return this.rewardTakenIds.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRewardTakenIdsEmpty() {
        if (this.rewardTakenIds == null) {
            return true;
        }
        return this.getRewardTakenIds().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRewardTakenIds() {
        this.getRewardTakenIds().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeRewardTakenIdsIndex(int index) {
        return this.getRewardTakenIds().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setRewardTakenIdsIndex(int index, Integer v) {
        return this.getRewardTakenIds().set(index, v);
    }
    /**
     * get canReceiveTsMs
     *
     * @return canReceiveTsMs value
     */
    public long getCanReceiveTsMs() {
        return this.canReceiveTsMs;
    }

    /**
     * set canReceiveTsMs && set marked
     *
     * @param canReceiveTsMs new value
     * @return current object
     */
    public ActivityOnlineRewardUnitProp setCanReceiveTsMs(long canReceiveTsMs) {
        if (this.canReceiveTsMs != canReceiveTsMs) {
            this.mark(FIELD_INDEX_CANRECEIVETSMS);
            this.canReceiveTsMs = canReceiveTsMs;
        }
        return this;
    }

    /**
     * inner set canReceiveTsMs
     *
     * @param canReceiveTsMs new value
     */
    private void innerSetCanReceiveTsMs(long canReceiveTsMs) {
        this.canReceiveTsMs = canReceiveTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityOnlineRewardUnitPB.Builder getCopyCsBuilder() {
        final ActivityOnlineRewardUnitPB.Builder builder = ActivityOnlineRewardUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityOnlineRewardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardTakenIds != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.rewardTakenIds.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardTakenIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardTakenIds();
            }
        }  else if (builder.hasRewardTakenIds()) {
            // 清理RewardTakenIds
            builder.clearRewardTakenIds();
            fieldCnt++;
        }
        if (this.getCanReceiveTsMs() != 0L) {
            builder.setCanReceiveTsMs(this.getCanReceiveTsMs());
            fieldCnt++;
        }  else if (builder.hasCanReceiveTsMs()) {
            // 清理CanReceiveTsMs
            builder.clearCanReceiveTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityOnlineRewardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKENIDS) && this.rewardTakenIds != null) {
            final boolean needClear = !builder.hasRewardTakenIds();
            final int tmpFieldCnt = this.rewardTakenIds.copyChangeToCs(builder.getRewardTakenIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardTakenIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETSMS)) {
            builder.setCanReceiveTsMs(this.getCanReceiveTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityOnlineRewardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKENIDS) && this.rewardTakenIds != null) {
            final boolean needClear = !builder.hasRewardTakenIds();
            final int tmpFieldCnt = this.rewardTakenIds.copyChangeToCs(builder.getRewardTakenIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardTakenIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETSMS)) {
            builder.setCanReceiveTsMs(this.getCanReceiveTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityOnlineRewardUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardTakenIds()) {
            this.getRewardTakenIds().mergeFromCs(proto.getRewardTakenIds());
        } else {
            if (this.rewardTakenIds != null) {
                this.rewardTakenIds.mergeFromCs(proto.getRewardTakenIds());
            }
        }
        if (proto.hasCanReceiveTsMs()) {
            this.innerSetCanReceiveTsMs(proto.getCanReceiveTsMs());
        } else {
            this.innerSetCanReceiveTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityOnlineRewardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityOnlineRewardUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardTakenIds()) {
            this.getRewardTakenIds().mergeChangeFromCs(proto.getRewardTakenIds());
            fieldCnt++;
        }
        if (proto.hasCanReceiveTsMs()) {
            this.setCanReceiveTsMs(proto.getCanReceiveTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityOnlineRewardUnit.Builder getCopyDbBuilder() {
        final ActivityOnlineRewardUnit.Builder builder = ActivityOnlineRewardUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityOnlineRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardTakenIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.rewardTakenIds.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardTakenIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardTakenIds();
            }
        }  else if (builder.hasRewardTakenIds()) {
            // 清理RewardTakenIds
            builder.clearRewardTakenIds();
            fieldCnt++;
        }
        if (this.getCanReceiveTsMs() != 0L) {
            builder.setCanReceiveTsMs(this.getCanReceiveTsMs());
            fieldCnt++;
        }  else if (builder.hasCanReceiveTsMs()) {
            // 清理CanReceiveTsMs
            builder.clearCanReceiveTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityOnlineRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKENIDS) && this.rewardTakenIds != null) {
            final boolean needClear = !builder.hasRewardTakenIds();
            final int tmpFieldCnt = this.rewardTakenIds.copyChangeToDb(builder.getRewardTakenIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardTakenIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETSMS)) {
            builder.setCanReceiveTsMs(this.getCanReceiveTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityOnlineRewardUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardTakenIds()) {
            this.getRewardTakenIds().mergeFromDb(proto.getRewardTakenIds());
        } else {
            if (this.rewardTakenIds != null) {
                this.rewardTakenIds.mergeFromDb(proto.getRewardTakenIds());
            }
        }
        if (proto.hasCanReceiveTsMs()) {
            this.innerSetCanReceiveTsMs(proto.getCanReceiveTsMs());
        } else {
            this.innerSetCanReceiveTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityOnlineRewardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityOnlineRewardUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardTakenIds()) {
            this.getRewardTakenIds().mergeChangeFromDb(proto.getRewardTakenIds());
            fieldCnt++;
        }
        if (proto.hasCanReceiveTsMs()) {
            this.setCanReceiveTsMs(proto.getCanReceiveTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityOnlineRewardUnit.Builder getCopySsBuilder() {
        final ActivityOnlineRewardUnit.Builder builder = ActivityOnlineRewardUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityOnlineRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardTakenIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.rewardTakenIds.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardTakenIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardTakenIds();
            }
        }  else if (builder.hasRewardTakenIds()) {
            // 清理RewardTakenIds
            builder.clearRewardTakenIds();
            fieldCnt++;
        }
        if (this.getCanReceiveTsMs() != 0L) {
            builder.setCanReceiveTsMs(this.getCanReceiveTsMs());
            fieldCnt++;
        }  else if (builder.hasCanReceiveTsMs()) {
            // 清理CanReceiveTsMs
            builder.clearCanReceiveTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityOnlineRewardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDTAKENIDS) && this.rewardTakenIds != null) {
            final boolean needClear = !builder.hasRewardTakenIds();
            final int tmpFieldCnt = this.rewardTakenIds.copyChangeToSs(builder.getRewardTakenIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardTakenIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_CANRECEIVETSMS)) {
            builder.setCanReceiveTsMs(this.getCanReceiveTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityOnlineRewardUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardTakenIds()) {
            this.getRewardTakenIds().mergeFromSs(proto.getRewardTakenIds());
        } else {
            if (this.rewardTakenIds != null) {
                this.rewardTakenIds.mergeFromSs(proto.getRewardTakenIds());
            }
        }
        if (proto.hasCanReceiveTsMs()) {
            this.innerSetCanReceiveTsMs(proto.getCanReceiveTsMs());
        } else {
            this.innerSetCanReceiveTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityOnlineRewardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityOnlineRewardUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardTakenIds()) {
            this.getRewardTakenIds().mergeChangeFromSs(proto.getRewardTakenIds());
            fieldCnt++;
        }
        if (proto.hasCanReceiveTsMs()) {
            this.setCanReceiveTsMs(proto.getCanReceiveTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityOnlineRewardUnit.Builder builder = ActivityOnlineRewardUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REWARDTAKENIDS) && this.rewardTakenIds != null) {
            this.rewardTakenIds.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rewardTakenIds != null) {
            this.rewardTakenIds.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityOnlineRewardUnitProp)) {
            return false;
        }
        final ActivityOnlineRewardUnitProp otherNode = (ActivityOnlineRewardUnitProp) node;
        if (!this.getRewardTakenIds().compareDataTo(otherNode.getRewardTakenIds())) {
            return false;
        }
        if (this.canReceiveTsMs != otherNode.canReceiveTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
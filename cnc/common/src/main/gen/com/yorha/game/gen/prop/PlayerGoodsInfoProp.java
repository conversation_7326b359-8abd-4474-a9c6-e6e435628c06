package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerGoodsInfo;
import com.yorha.proto.PlayerPB.PlayerGoodsInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerGoodsInfoProp extends AbstractContainerElementNode<String> {

    public static final int FIELD_INDEX_KEY = 0;
    public static final int FIELD_INDEX_TSMS = 1;
    public static final int FIELD_INDEX_BILLNO = 2;
    public static final int FIELD_INDEX_TOKEN = 3;
    public static final int FIELD_INDEX_APPMETA = 4;
    public static final int FIELD_INDEX_GOODSID = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private String key = Constant.DEFAULT_STR_VALUE;
    private long tsMs = Constant.DEFAULT_LONG_VALUE;
    private String billno = Constant.DEFAULT_STR_VALUE;
    private String token = Constant.DEFAULT_STR_VALUE;
    private String appMeta = Constant.DEFAULT_STR_VALUE;
    private int goodsId = Constant.DEFAULT_INT_VALUE;

    public PlayerGoodsInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerGoodsInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get key
     *
     * @return key value
     */
    public String getKey() {
        return this.key;
    }

    /**
     * set key && set marked
     *
     * @param key new value
     * @return current object
     */
    public PlayerGoodsInfoProp setKey(String key) {
        if (key == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.key, key)) {
            this.mark(FIELD_INDEX_KEY);
            this.key = key;
        }
        return this;
    }

    /**
     * inner set key
     *
     * @param key new value
     */
    private void innerSetKey(String key) {
        this.key = key;
    }

    /**
     * get tsMs
     *
     * @return tsMs value
     */
    public long getTsMs() {
        return this.tsMs;
    }

    /**
     * set tsMs && set marked
     *
     * @param tsMs new value
     * @return current object
     */
    public PlayerGoodsInfoProp setTsMs(long tsMs) {
        if (this.tsMs != tsMs) {
            this.mark(FIELD_INDEX_TSMS);
            this.tsMs = tsMs;
        }
        return this;
    }

    /**
     * inner set tsMs
     *
     * @param tsMs new value
     */
    private void innerSetTsMs(long tsMs) {
        this.tsMs = tsMs;
    }

    /**
     * get billno
     *
     * @return billno value
     */
    public String getBillno() {
        return this.billno;
    }

    /**
     * set billno && set marked
     *
     * @param billno new value
     * @return current object
     */
    public PlayerGoodsInfoProp setBillno(String billno) {
        if (billno == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.billno, billno)) {
            this.mark(FIELD_INDEX_BILLNO);
            this.billno = billno;
        }
        return this;
    }

    /**
     * inner set billno
     *
     * @param billno new value
     */
    private void innerSetBillno(String billno) {
        this.billno = billno;
    }

    /**
     * get token
     *
     * @return token value
     */
    public String getToken() {
        return this.token;
    }

    /**
     * set token && set marked
     *
     * @param token new value
     * @return current object
     */
    public PlayerGoodsInfoProp setToken(String token) {
        if (token == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.token, token)) {
            this.mark(FIELD_INDEX_TOKEN);
            this.token = token;
        }
        return this;
    }

    /**
     * inner set token
     *
     * @param token new value
     */
    private void innerSetToken(String token) {
        this.token = token;
    }

    /**
     * get appMeta
     *
     * @return appMeta value
     */
    public String getAppMeta() {
        return this.appMeta;
    }

    /**
     * set appMeta && set marked
     *
     * @param appMeta new value
     * @return current object
     */
    public PlayerGoodsInfoProp setAppMeta(String appMeta) {
        if (appMeta == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.appMeta, appMeta)) {
            this.mark(FIELD_INDEX_APPMETA);
            this.appMeta = appMeta;
        }
        return this;
    }

    /**
     * inner set appMeta
     *
     * @param appMeta new value
     */
    private void innerSetAppMeta(String appMeta) {
        this.appMeta = appMeta;
    }

    /**
     * get goodsId
     *
     * @return goodsId value
     */
    public int getGoodsId() {
        return this.goodsId;
    }

    /**
     * set goodsId && set marked
     *
     * @param goodsId new value
     * @return current object
     */
    public PlayerGoodsInfoProp setGoodsId(int goodsId) {
        if (this.goodsId != goodsId) {
            this.mark(FIELD_INDEX_GOODSID);
            this.goodsId = goodsId;
        }
        return this;
    }

    /**
     * inner set goodsId
     *
     * @param goodsId new value
     */
    private void innerSetGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsInfoPB.Builder getCopyCsBuilder() {
        final PlayerGoodsInfoPB.Builder builder = PlayerGoodsInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.getTsMs() != 0L) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }  else if (builder.hasTsMs()) {
            // 清理TsMs
            builder.clearTsMs();
            fieldCnt++;
        }
        if (!this.getBillno().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBillno(this.getBillno());
            fieldCnt++;
        }  else if (builder.hasBillno()) {
            // 清理Billno
            builder.clearBillno();
            fieldCnt++;
        }
        if (!this.getToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }  else if (builder.hasToken()) {
            // 清理Token
            builder.clearToken();
            fieldCnt++;
        }
        if (!this.getAppMeta().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setAppMeta(this.getAppMeta());
            fieldCnt++;
        }  else if (builder.hasAppMeta()) {
            // 清理AppMeta
            builder.clearAppMeta();
            fieldCnt++;
        }
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BILLNO)) {
            builder.setBillno(this.getBillno());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPMETA)) {
            builder.setAppMeta(this.getAppMeta());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerGoodsInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BILLNO)) {
            builder.setBillno(this.getBillno());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPMETA)) {
            builder.setAppMeta(this.getAppMeta());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerGoodsInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTsMs()) {
            this.innerSetTsMs(proto.getTsMs());
        } else {
            this.innerSetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBillno()) {
            this.innerSetBillno(proto.getBillno());
        } else {
            this.innerSetBillno(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasToken()) {
            this.innerSetToken(proto.getToken());
        } else {
            this.innerSetToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasAppMeta()) {
            this.innerSetAppMeta(proto.getAppMeta());
        } else {
            this.innerSetAppMeta(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerGoodsInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasTsMs()) {
            this.setTsMs(proto.getTsMs());
            fieldCnt++;
        }
        if (proto.hasBillno()) {
            this.setBillno(proto.getBillno());
            fieldCnt++;
        }
        if (proto.hasToken()) {
            this.setToken(proto.getToken());
            fieldCnt++;
        }
        if (proto.hasAppMeta()) {
            this.setAppMeta(proto.getAppMeta());
            fieldCnt++;
        }
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsInfo.Builder getCopyDbBuilder() {
        final PlayerGoodsInfo.Builder builder = PlayerGoodsInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.getTsMs() != 0L) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }  else if (builder.hasTsMs()) {
            // 清理TsMs
            builder.clearTsMs();
            fieldCnt++;
        }
        if (!this.getBillno().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBillno(this.getBillno());
            fieldCnt++;
        }  else if (builder.hasBillno()) {
            // 清理Billno
            builder.clearBillno();
            fieldCnt++;
        }
        if (!this.getToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }  else if (builder.hasToken()) {
            // 清理Token
            builder.clearToken();
            fieldCnt++;
        }
        if (!this.getAppMeta().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setAppMeta(this.getAppMeta());
            fieldCnt++;
        }  else if (builder.hasAppMeta()) {
            // 清理AppMeta
            builder.clearAppMeta();
            fieldCnt++;
        }
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BILLNO)) {
            builder.setBillno(this.getBillno());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPMETA)) {
            builder.setAppMeta(this.getAppMeta());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerGoodsInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTsMs()) {
            this.innerSetTsMs(proto.getTsMs());
        } else {
            this.innerSetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBillno()) {
            this.innerSetBillno(proto.getBillno());
        } else {
            this.innerSetBillno(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasToken()) {
            this.innerSetToken(proto.getToken());
        } else {
            this.innerSetToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasAppMeta()) {
            this.innerSetAppMeta(proto.getAppMeta());
        } else {
            this.innerSetAppMeta(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerGoodsInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasTsMs()) {
            this.setTsMs(proto.getTsMs());
            fieldCnt++;
        }
        if (proto.hasBillno()) {
            this.setBillno(proto.getBillno());
            fieldCnt++;
        }
        if (proto.hasToken()) {
            this.setToken(proto.getToken());
            fieldCnt++;
        }
        if (proto.hasAppMeta()) {
            this.setAppMeta(proto.getAppMeta());
            fieldCnt++;
        }
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerGoodsInfo.Builder getCopySsBuilder() {
        final PlayerGoodsInfo.Builder builder = PlayerGoodsInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getKey().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }  else if (builder.hasKey()) {
            // 清理Key
            builder.clearKey();
            fieldCnt++;
        }
        if (this.getTsMs() != 0L) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }  else if (builder.hasTsMs()) {
            // 清理TsMs
            builder.clearTsMs();
            fieldCnt++;
        }
        if (!this.getBillno().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBillno(this.getBillno());
            fieldCnt++;
        }  else if (builder.hasBillno()) {
            // 清理Billno
            builder.clearBillno();
            fieldCnt++;
        }
        if (!this.getToken().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }  else if (builder.hasToken()) {
            // 清理Token
            builder.clearToken();
            fieldCnt++;
        }
        if (!this.getAppMeta().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setAppMeta(this.getAppMeta());
            fieldCnt++;
        }  else if (builder.hasAppMeta()) {
            // 清理AppMeta
            builder.clearAppMeta();
            fieldCnt++;
        }
        if (this.getGoodsId() != 0) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }  else if (builder.hasGoodsId()) {
            // 清理GoodsId
            builder.clearGoodsId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerGoodsInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_KEY)) {
            builder.setKey(this.getKey());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BILLNO)) {
            builder.setBillno(this.getBillno());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOKEN)) {
            builder.setToken(this.getToken());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_APPMETA)) {
            builder.setAppMeta(this.getAppMeta());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSID)) {
            builder.setGoodsId(this.getGoodsId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerGoodsInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasKey()) {
            this.innerSetKey(proto.getKey());
        } else {
            this.innerSetKey(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTsMs()) {
            this.innerSetTsMs(proto.getTsMs());
        } else {
            this.innerSetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBillno()) {
            this.innerSetBillno(proto.getBillno());
        } else {
            this.innerSetBillno(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasToken()) {
            this.innerSetToken(proto.getToken());
        } else {
            this.innerSetToken(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasAppMeta()) {
            this.innerSetAppMeta(proto.getAppMeta());
        } else {
            this.innerSetAppMeta(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasGoodsId()) {
            this.innerSetGoodsId(proto.getGoodsId());
        } else {
            this.innerSetGoodsId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerGoodsInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerGoodsInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasKey()) {
            this.setKey(proto.getKey());
            fieldCnt++;
        }
        if (proto.hasTsMs()) {
            this.setTsMs(proto.getTsMs());
            fieldCnt++;
        }
        if (proto.hasBillno()) {
            this.setBillno(proto.getBillno());
            fieldCnt++;
        }
        if (proto.hasToken()) {
            this.setToken(proto.getToken());
            fieldCnt++;
        }
        if (proto.hasAppMeta()) {
            this.setAppMeta(proto.getAppMeta());
            fieldCnt++;
        }
        if (proto.hasGoodsId()) {
            this.setGoodsId(proto.getGoodsId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerGoodsInfo.Builder builder = PlayerGoodsInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public String getPrivateKey() {
        return this.key;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerGoodsInfoProp)) {
            return false;
        }
        final PlayerGoodsInfoProp otherNode = (PlayerGoodsInfoProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.key, otherNode.key)) {
            return false;
        }
        if (this.tsMs != otherNode.tsMs) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.billno, otherNode.billno)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.token, otherNode.token)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.appMeta, otherNode.appMeta)) {
            return false;
        }
        if (this.goodsId != otherNode.goodsId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
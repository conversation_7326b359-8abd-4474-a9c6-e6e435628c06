package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerTriggerBundleModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerTriggerBundleModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerTriggerBundleModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BUNDLES = 0;
    public static final int FIELD_INDEX_EXPIREDBUNDLES = 1;
    public static final int FIELD_INDEX_BUNDLERECORD = 2;
    public static final int FIELD_INDEX_BUNDLESHOWNRECORD = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private Int64PlayerTriggerBundleMapProp bundles = null;
    private Int64PlayerTriggerBundleMapProp expiredBundles = null;
    private Int32PlayerTriggerBundleRecordMapProp bundleRecord = null;
    private Int32PlayerTriggerBundleShownRecordMapProp bundleShownRecord = null;

    public PlayerTriggerBundleModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerTriggerBundleModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get bundles
     *
     * @return bundles value
     */
    public Int64PlayerTriggerBundleMapProp getBundles() {
        if (this.bundles == null) {
            this.bundles = new Int64PlayerTriggerBundleMapProp(this, FIELD_INDEX_BUNDLES);
        }
        return this.bundles;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBundlesV(PlayerTriggerBundleProp v) {
        this.getBundles().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerTriggerBundleProp addEmptyBundles(Long k) {
        return this.getBundles().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBundlesSize() {
        if (this.bundles == null) {
            return 0;
        }
        return this.bundles.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBundlesEmpty() {
        if (this.bundles == null) {
            return true;
        }
        return this.bundles.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerTriggerBundleProp getBundlesV(Long k) {
        if (this.bundles == null || !this.bundles.containsKey(k)) {
            return null;
        }
        return this.bundles.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBundles() {
        if (this.bundles != null) {
            this.bundles.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBundlesV(Long k) {
        if (this.bundles != null) {
            this.bundles.remove(k);
        }
    }
    /**
     * get expiredBundles
     *
     * @return expiredBundles value
     */
    public Int64PlayerTriggerBundleMapProp getExpiredBundles() {
        if (this.expiredBundles == null) {
            this.expiredBundles = new Int64PlayerTriggerBundleMapProp(this, FIELD_INDEX_EXPIREDBUNDLES);
        }
        return this.expiredBundles;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putExpiredBundlesV(PlayerTriggerBundleProp v) {
        this.getExpiredBundles().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerTriggerBundleProp addEmptyExpiredBundles(Long k) {
        return this.getExpiredBundles().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getExpiredBundlesSize() {
        if (this.expiredBundles == null) {
            return 0;
        }
        return this.expiredBundles.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isExpiredBundlesEmpty() {
        if (this.expiredBundles == null) {
            return true;
        }
        return this.expiredBundles.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerTriggerBundleProp getExpiredBundlesV(Long k) {
        if (this.expiredBundles == null || !this.expiredBundles.containsKey(k)) {
            return null;
        }
        return this.expiredBundles.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearExpiredBundles() {
        if (this.expiredBundles != null) {
            this.expiredBundles.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeExpiredBundlesV(Long k) {
        if (this.expiredBundles != null) {
            this.expiredBundles.remove(k);
        }
    }
    /**
     * get bundleRecord
     *
     * @return bundleRecord value
     */
    public Int32PlayerTriggerBundleRecordMapProp getBundleRecord() {
        if (this.bundleRecord == null) {
            this.bundleRecord = new Int32PlayerTriggerBundleRecordMapProp(this, FIELD_INDEX_BUNDLERECORD);
        }
        return this.bundleRecord;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBundleRecordV(PlayerTriggerBundleRecordProp v) {
        this.getBundleRecord().put(v.getTemplateId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerTriggerBundleRecordProp addEmptyBundleRecord(Integer k) {
        return this.getBundleRecord().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBundleRecordSize() {
        if (this.bundleRecord == null) {
            return 0;
        }
        return this.bundleRecord.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBundleRecordEmpty() {
        if (this.bundleRecord == null) {
            return true;
        }
        return this.bundleRecord.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerTriggerBundleRecordProp getBundleRecordV(Integer k) {
        if (this.bundleRecord == null || !this.bundleRecord.containsKey(k)) {
            return null;
        }
        return this.bundleRecord.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBundleRecord() {
        if (this.bundleRecord != null) {
            this.bundleRecord.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBundleRecordV(Integer k) {
        if (this.bundleRecord != null) {
            this.bundleRecord.remove(k);
        }
    }
    /**
     * get bundleShownRecord
     *
     * @return bundleShownRecord value
     */
    public Int32PlayerTriggerBundleShownRecordMapProp getBundleShownRecord() {
        if (this.bundleShownRecord == null) {
            this.bundleShownRecord = new Int32PlayerTriggerBundleShownRecordMapProp(this, FIELD_INDEX_BUNDLESHOWNRECORD);
        }
        return this.bundleShownRecord;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBundleShownRecordV(PlayerTriggerBundleShownRecordProp v) {
        this.getBundleShownRecord().put(v.getBundleId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerTriggerBundleShownRecordProp addEmptyBundleShownRecord(Integer k) {
        return this.getBundleShownRecord().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBundleShownRecordSize() {
        if (this.bundleShownRecord == null) {
            return 0;
        }
        return this.bundleShownRecord.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBundleShownRecordEmpty() {
        if (this.bundleShownRecord == null) {
            return true;
        }
        return this.bundleShownRecord.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerTriggerBundleShownRecordProp getBundleShownRecordV(Integer k) {
        if (this.bundleShownRecord == null || !this.bundleShownRecord.containsKey(k)) {
            return null;
        }
        return this.bundleShownRecord.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBundleShownRecord() {
        if (this.bundleShownRecord != null) {
            this.bundleShownRecord.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBundleShownRecordV(Integer k) {
        if (this.bundleShownRecord != null) {
            this.bundleShownRecord.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleModelPB.Builder getCopyCsBuilder() {
        final PlayerTriggerBundleModelPB.Builder builder = PlayerTriggerBundleModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerTriggerBundleModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bundles != null) {
            StructPB.Int64PlayerTriggerBundleMapPB.Builder tmpBuilder = StructPB.Int64PlayerTriggerBundleMapPB.newBuilder();
            final int tmpFieldCnt = this.bundles.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBundles();
            }
        }  else if (builder.hasBundles()) {
            // 清理Bundles
            builder.clearBundles();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerTriggerBundleModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLES) && this.bundles != null) {
            final boolean needClear = !builder.hasBundles();
            final int tmpFieldCnt = this.bundles.copyChangeToCs(builder.getBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundles();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerTriggerBundleModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLES) && this.bundles != null) {
            final boolean needClear = !builder.hasBundles();
            final int tmpFieldCnt = this.bundles.copyChangeToAndClearDeleteKeysCs(builder.getBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundles();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerTriggerBundleModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBundles()) {
            this.getBundles().mergeFromCs(proto.getBundles());
        } else {
            if (this.bundles != null) {
                this.bundles.mergeFromCs(proto.getBundles());
            }
        }
        this.markAll();
        return PlayerTriggerBundleModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerTriggerBundleModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBundles()) {
            this.getBundles().mergeChangeFromCs(proto.getBundles());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleModel.Builder getCopyDbBuilder() {
        final PlayerTriggerBundleModel.Builder builder = PlayerTriggerBundleModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerTriggerBundleModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bundles != null) {
            Struct.Int64PlayerTriggerBundleMap.Builder tmpBuilder = Struct.Int64PlayerTriggerBundleMap.newBuilder();
            final int tmpFieldCnt = this.bundles.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBundles();
            }
        }  else if (builder.hasBundles()) {
            // 清理Bundles
            builder.clearBundles();
            fieldCnt++;
        }
        if (this.expiredBundles != null) {
            Struct.Int64PlayerTriggerBundleMap.Builder tmpBuilder = Struct.Int64PlayerTriggerBundleMap.newBuilder();
            final int tmpFieldCnt = this.expiredBundles.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpiredBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpiredBundles();
            }
        }  else if (builder.hasExpiredBundles()) {
            // 清理ExpiredBundles
            builder.clearExpiredBundles();
            fieldCnt++;
        }
        if (this.bundleRecord != null) {
            Struct.Int32PlayerTriggerBundleRecordMap.Builder tmpBuilder = Struct.Int32PlayerTriggerBundleRecordMap.newBuilder();
            final int tmpFieldCnt = this.bundleRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBundleRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBundleRecord();
            }
        }  else if (builder.hasBundleRecord()) {
            // 清理BundleRecord
            builder.clearBundleRecord();
            fieldCnt++;
        }
        if (this.bundleShownRecord != null) {
            Struct.Int32PlayerTriggerBundleShownRecordMap.Builder tmpBuilder = Struct.Int32PlayerTriggerBundleShownRecordMap.newBuilder();
            final int tmpFieldCnt = this.bundleShownRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBundleShownRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBundleShownRecord();
            }
        }  else if (builder.hasBundleShownRecord()) {
            // 清理BundleShownRecord
            builder.clearBundleShownRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerTriggerBundleModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLES) && this.bundles != null) {
            final boolean needClear = !builder.hasBundles();
            final int tmpFieldCnt = this.bundles.copyChangeToDb(builder.getBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundles();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDBUNDLES) && this.expiredBundles != null) {
            final boolean needClear = !builder.hasExpiredBundles();
            final int tmpFieldCnt = this.expiredBundles.copyChangeToDb(builder.getExpiredBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpiredBundles();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUNDLERECORD) && this.bundleRecord != null) {
            final boolean needClear = !builder.hasBundleRecord();
            final int tmpFieldCnt = this.bundleRecord.copyChangeToDb(builder.getBundleRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundleRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUNDLESHOWNRECORD) && this.bundleShownRecord != null) {
            final boolean needClear = !builder.hasBundleShownRecord();
            final int tmpFieldCnt = this.bundleShownRecord.copyChangeToDb(builder.getBundleShownRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundleShownRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerTriggerBundleModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBundles()) {
            this.getBundles().mergeFromDb(proto.getBundles());
        } else {
            if (this.bundles != null) {
                this.bundles.mergeFromDb(proto.getBundles());
            }
        }
        if (proto.hasExpiredBundles()) {
            this.getExpiredBundles().mergeFromDb(proto.getExpiredBundles());
        } else {
            if (this.expiredBundles != null) {
                this.expiredBundles.mergeFromDb(proto.getExpiredBundles());
            }
        }
        if (proto.hasBundleRecord()) {
            this.getBundleRecord().mergeFromDb(proto.getBundleRecord());
        } else {
            if (this.bundleRecord != null) {
                this.bundleRecord.mergeFromDb(proto.getBundleRecord());
            }
        }
        if (proto.hasBundleShownRecord()) {
            this.getBundleShownRecord().mergeFromDb(proto.getBundleShownRecord());
        } else {
            if (this.bundleShownRecord != null) {
                this.bundleShownRecord.mergeFromDb(proto.getBundleShownRecord());
            }
        }
        this.markAll();
        return PlayerTriggerBundleModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerTriggerBundleModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBundles()) {
            this.getBundles().mergeChangeFromDb(proto.getBundles());
            fieldCnt++;
        }
        if (proto.hasExpiredBundles()) {
            this.getExpiredBundles().mergeChangeFromDb(proto.getExpiredBundles());
            fieldCnt++;
        }
        if (proto.hasBundleRecord()) {
            this.getBundleRecord().mergeChangeFromDb(proto.getBundleRecord());
            fieldCnt++;
        }
        if (proto.hasBundleShownRecord()) {
            this.getBundleShownRecord().mergeChangeFromDb(proto.getBundleShownRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleModel.Builder getCopySsBuilder() {
        final PlayerTriggerBundleModel.Builder builder = PlayerTriggerBundleModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerTriggerBundleModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bundles != null) {
            Struct.Int64PlayerTriggerBundleMap.Builder tmpBuilder = Struct.Int64PlayerTriggerBundleMap.newBuilder();
            final int tmpFieldCnt = this.bundles.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBundles();
            }
        }  else if (builder.hasBundles()) {
            // 清理Bundles
            builder.clearBundles();
            fieldCnt++;
        }
        if (this.expiredBundles != null) {
            Struct.Int64PlayerTriggerBundleMap.Builder tmpBuilder = Struct.Int64PlayerTriggerBundleMap.newBuilder();
            final int tmpFieldCnt = this.expiredBundles.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExpiredBundles(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExpiredBundles();
            }
        }  else if (builder.hasExpiredBundles()) {
            // 清理ExpiredBundles
            builder.clearExpiredBundles();
            fieldCnt++;
        }
        if (this.bundleRecord != null) {
            Struct.Int32PlayerTriggerBundleRecordMap.Builder tmpBuilder = Struct.Int32PlayerTriggerBundleRecordMap.newBuilder();
            final int tmpFieldCnt = this.bundleRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBundleRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBundleRecord();
            }
        }  else if (builder.hasBundleRecord()) {
            // 清理BundleRecord
            builder.clearBundleRecord();
            fieldCnt++;
        }
        if (this.bundleShownRecord != null) {
            Struct.Int32PlayerTriggerBundleShownRecordMap.Builder tmpBuilder = Struct.Int32PlayerTriggerBundleShownRecordMap.newBuilder();
            final int tmpFieldCnt = this.bundleShownRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBundleShownRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBundleShownRecord();
            }
        }  else if (builder.hasBundleShownRecord()) {
            // 清理BundleShownRecord
            builder.clearBundleShownRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerTriggerBundleModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLES) && this.bundles != null) {
            final boolean needClear = !builder.hasBundles();
            final int tmpFieldCnt = this.bundles.copyChangeToSs(builder.getBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundles();
            }
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDBUNDLES) && this.expiredBundles != null) {
            final boolean needClear = !builder.hasExpiredBundles();
            final int tmpFieldCnt = this.expiredBundles.copyChangeToSs(builder.getExpiredBundlesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExpiredBundles();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUNDLERECORD) && this.bundleRecord != null) {
            final boolean needClear = !builder.hasBundleRecord();
            final int tmpFieldCnt = this.bundleRecord.copyChangeToSs(builder.getBundleRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundleRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_BUNDLESHOWNRECORD) && this.bundleShownRecord != null) {
            final boolean needClear = !builder.hasBundleShownRecord();
            final int tmpFieldCnt = this.bundleShownRecord.copyChangeToSs(builder.getBundleShownRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBundleShownRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerTriggerBundleModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBundles()) {
            this.getBundles().mergeFromSs(proto.getBundles());
        } else {
            if (this.bundles != null) {
                this.bundles.mergeFromSs(proto.getBundles());
            }
        }
        if (proto.hasExpiredBundles()) {
            this.getExpiredBundles().mergeFromSs(proto.getExpiredBundles());
        } else {
            if (this.expiredBundles != null) {
                this.expiredBundles.mergeFromSs(proto.getExpiredBundles());
            }
        }
        if (proto.hasBundleRecord()) {
            this.getBundleRecord().mergeFromSs(proto.getBundleRecord());
        } else {
            if (this.bundleRecord != null) {
                this.bundleRecord.mergeFromSs(proto.getBundleRecord());
            }
        }
        if (proto.hasBundleShownRecord()) {
            this.getBundleShownRecord().mergeFromSs(proto.getBundleShownRecord());
        } else {
            if (this.bundleShownRecord != null) {
                this.bundleShownRecord.mergeFromSs(proto.getBundleShownRecord());
            }
        }
        this.markAll();
        return PlayerTriggerBundleModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerTriggerBundleModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBundles()) {
            this.getBundles().mergeChangeFromSs(proto.getBundles());
            fieldCnt++;
        }
        if (proto.hasExpiredBundles()) {
            this.getExpiredBundles().mergeChangeFromSs(proto.getExpiredBundles());
            fieldCnt++;
        }
        if (proto.hasBundleRecord()) {
            this.getBundleRecord().mergeChangeFromSs(proto.getBundleRecord());
            fieldCnt++;
        }
        if (proto.hasBundleShownRecord()) {
            this.getBundleShownRecord().mergeChangeFromSs(proto.getBundleShownRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerTriggerBundleModel.Builder builder = PlayerTriggerBundleModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BUNDLES) && this.bundles != null) {
            this.bundles.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPIREDBUNDLES) && this.expiredBundles != null) {
            this.expiredBundles.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUNDLERECORD) && this.bundleRecord != null) {
            this.bundleRecord.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BUNDLESHOWNRECORD) && this.bundleShownRecord != null) {
            this.bundleShownRecord.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.bundles != null) {
            this.bundles.markAll();
        }
        if (this.expiredBundles != null) {
            this.expiredBundles.markAll();
        }
        if (this.bundleRecord != null) {
            this.bundleRecord.markAll();
        }
        if (this.bundleShownRecord != null) {
            this.bundleShownRecord.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerTriggerBundleModelProp)) {
            return false;
        }
        final PlayerTriggerBundleModelProp otherNode = (PlayerTriggerBundleModelProp) node;
        if (!this.getBundles().compareDataTo(otherNode.getBundles())) {
            return false;
        }
        if (!this.getExpiredBundles().compareDataTo(otherNode.getExpiredBundles())) {
            return false;
        }
        if (!this.getBundleRecord().compareDataTo(otherNode.getBundleRecord())) {
            return false;
        }
        if (!this.getBundleShownRecord().compareDataTo(otherNode.getBundleShownRecord())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
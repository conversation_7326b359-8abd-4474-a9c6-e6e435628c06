package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityHeroComeUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityHeroComeUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityHeroComeUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BORNPOINT = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private PointProp bornPoint = null;

    public ActivityHeroComeUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityHeroComeUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get bornPoint
     *
     * @return bornPoint value
     */
    public PointProp getBornPoint() {
        if (this.bornPoint == null) {
            this.bornPoint = new PointProp(this, FIELD_INDEX_BORNPOINT);
        }
        return this.bornPoint;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityHeroComeUnitPB.Builder getCopyCsBuilder() {
        final ActivityHeroComeUnitPB.Builder builder = ActivityHeroComeUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityHeroComeUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bornPoint != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.bornPoint.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBornPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBornPoint();
            }
        }  else if (builder.hasBornPoint()) {
            // 清理BornPoint
            builder.clearBornPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityHeroComeUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BORNPOINT) && this.bornPoint != null) {
            final boolean needClear = !builder.hasBornPoint();
            final int tmpFieldCnt = this.bornPoint.copyChangeToCs(builder.getBornPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBornPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityHeroComeUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BORNPOINT) && this.bornPoint != null) {
            final boolean needClear = !builder.hasBornPoint();
            final int tmpFieldCnt = this.bornPoint.copyChangeToAndClearDeleteKeysCs(builder.getBornPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBornPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityHeroComeUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBornPoint()) {
            this.getBornPoint().mergeFromCs(proto.getBornPoint());
        } else {
            if (this.bornPoint != null) {
                this.bornPoint.mergeFromCs(proto.getBornPoint());
            }
        }
        this.markAll();
        return ActivityHeroComeUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityHeroComeUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBornPoint()) {
            this.getBornPoint().mergeChangeFromCs(proto.getBornPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityHeroComeUnit.Builder getCopyDbBuilder() {
        final ActivityHeroComeUnit.Builder builder = ActivityHeroComeUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityHeroComeUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bornPoint != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.bornPoint.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBornPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBornPoint();
            }
        }  else if (builder.hasBornPoint()) {
            // 清理BornPoint
            builder.clearBornPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityHeroComeUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BORNPOINT) && this.bornPoint != null) {
            final boolean needClear = !builder.hasBornPoint();
            final int tmpFieldCnt = this.bornPoint.copyChangeToDb(builder.getBornPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBornPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityHeroComeUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBornPoint()) {
            this.getBornPoint().mergeFromDb(proto.getBornPoint());
        } else {
            if (this.bornPoint != null) {
                this.bornPoint.mergeFromDb(proto.getBornPoint());
            }
        }
        this.markAll();
        return ActivityHeroComeUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityHeroComeUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBornPoint()) {
            this.getBornPoint().mergeChangeFromDb(proto.getBornPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityHeroComeUnit.Builder getCopySsBuilder() {
        final ActivityHeroComeUnit.Builder builder = ActivityHeroComeUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityHeroComeUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.bornPoint != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.bornPoint.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBornPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBornPoint();
            }
        }  else if (builder.hasBornPoint()) {
            // 清理BornPoint
            builder.clearBornPoint();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityHeroComeUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BORNPOINT) && this.bornPoint != null) {
            final boolean needClear = !builder.hasBornPoint();
            final int tmpFieldCnt = this.bornPoint.copyChangeToSs(builder.getBornPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBornPoint();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityHeroComeUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBornPoint()) {
            this.getBornPoint().mergeFromSs(proto.getBornPoint());
        } else {
            if (this.bornPoint != null) {
                this.bornPoint.mergeFromSs(proto.getBornPoint());
            }
        }
        this.markAll();
        return ActivityHeroComeUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityHeroComeUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBornPoint()) {
            this.getBornPoint().mergeChangeFromSs(proto.getBornPoint());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityHeroComeUnit.Builder builder = ActivityHeroComeUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BORNPOINT) && this.bornPoint != null) {
            this.bornPoint.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.bornPoint != null) {
            this.bornPoint.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityHeroComeUnitProp)) {
            return false;
        }
        final ActivityHeroComeUnitProp otherNode = (ActivityHeroComeUnitProp) node;
        if (!this.getBornPoint().compareDataTo(otherNode.getBornPoint())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
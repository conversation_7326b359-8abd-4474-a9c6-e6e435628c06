package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneRank;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.MileStoneRankPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneRankProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RANKINFOMAP = 0;
    public static final int FIELD_INDEX_ZONERANKINFOMAP = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int64MileStoneClanInfoMapProp rankInfoMap = null;
    private Int32MileStoneZoneInfoMapProp zoneRankInfoMap = null;

    public MileStoneRankProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneRankProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rankInfoMap
     *
     * @return rankInfoMap value
     */
    public Int64MileStoneClanInfoMapProp getRankInfoMap() {
        if (this.rankInfoMap == null) {
            this.rankInfoMap = new Int64MileStoneClanInfoMapProp(this, FIELD_INDEX_RANKINFOMAP);
        }
        return this.rankInfoMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRankInfoMapV(MileStoneClanInfoProp v) {
        this.getRankInfoMap().put(v.getClanId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public MileStoneClanInfoProp addEmptyRankInfoMap(Long k) {
        return this.getRankInfoMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRankInfoMapSize() {
        if (this.rankInfoMap == null) {
            return 0;
        }
        return this.rankInfoMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRankInfoMapEmpty() {
        if (this.rankInfoMap == null) {
            return true;
        }
        return this.rankInfoMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public MileStoneClanInfoProp getRankInfoMapV(Long k) {
        if (this.rankInfoMap == null || !this.rankInfoMap.containsKey(k)) {
            return null;
        }
        return this.rankInfoMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRankInfoMap() {
        if (this.rankInfoMap != null) {
            this.rankInfoMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRankInfoMapV(Long k) {
        if (this.rankInfoMap != null) {
            this.rankInfoMap.remove(k);
        }
    }
    /**
     * get zoneRankInfoMap
     *
     * @return zoneRankInfoMap value
     */
    public Int32MileStoneZoneInfoMapProp getZoneRankInfoMap() {
        if (this.zoneRankInfoMap == null) {
            this.zoneRankInfoMap = new Int32MileStoneZoneInfoMapProp(this, FIELD_INDEX_ZONERANKINFOMAP);
        }
        return this.zoneRankInfoMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putZoneRankInfoMapV(MileStoneZoneInfoProp v) {
        this.getZoneRankInfoMap().put(v.getZoneId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public MileStoneZoneInfoProp addEmptyZoneRankInfoMap(Integer k) {
        return this.getZoneRankInfoMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getZoneRankInfoMapSize() {
        if (this.zoneRankInfoMap == null) {
            return 0;
        }
        return this.zoneRankInfoMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isZoneRankInfoMapEmpty() {
        if (this.zoneRankInfoMap == null) {
            return true;
        }
        return this.zoneRankInfoMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public MileStoneZoneInfoProp getZoneRankInfoMapV(Integer k) {
        if (this.zoneRankInfoMap == null || !this.zoneRankInfoMap.containsKey(k)) {
            return null;
        }
        return this.zoneRankInfoMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearZoneRankInfoMap() {
        if (this.zoneRankInfoMap != null) {
            this.zoneRankInfoMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeZoneRankInfoMapV(Integer k) {
        if (this.zoneRankInfoMap != null) {
            this.zoneRankInfoMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRankPB.Builder getCopyCsBuilder() {
        final MileStoneRankPB.Builder builder = MileStoneRankPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneRankPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rankInfoMap != null) {
            StructPB.Int64MileStoneClanInfoMapPB.Builder tmpBuilder = StructPB.Int64MileStoneClanInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.rankInfoMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankInfoMap();
            }
        }  else if (builder.hasRankInfoMap()) {
            // 清理RankInfoMap
            builder.clearRankInfoMap();
            fieldCnt++;
        }
        if (this.zoneRankInfoMap != null) {
            StructPB.Int32MileStoneZoneInfoMapPB.Builder tmpBuilder = StructPB.Int32MileStoneZoneInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.zoneRankInfoMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneRankInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneRankInfoMap();
            }
        }  else if (builder.hasZoneRankInfoMap()) {
            // 清理ZoneRankInfoMap
            builder.clearZoneRankInfoMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneRankPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANKINFOMAP) && this.rankInfoMap != null) {
            final boolean needClear = !builder.hasRankInfoMap();
            final int tmpFieldCnt = this.rankInfoMap.copyChangeToCs(builder.getRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfoMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONERANKINFOMAP) && this.zoneRankInfoMap != null) {
            final boolean needClear = !builder.hasZoneRankInfoMap();
            final int tmpFieldCnt = this.zoneRankInfoMap.copyChangeToCs(builder.getZoneRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneRankInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneRankPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANKINFOMAP) && this.rankInfoMap != null) {
            final boolean needClear = !builder.hasRankInfoMap();
            final int tmpFieldCnt = this.rankInfoMap.copyChangeToAndClearDeleteKeysCs(builder.getRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfoMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONERANKINFOMAP) && this.zoneRankInfoMap != null) {
            final boolean needClear = !builder.hasZoneRankInfoMap();
            final int tmpFieldCnt = this.zoneRankInfoMap.copyChangeToAndClearDeleteKeysCs(builder.getZoneRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneRankInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneRankPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRankInfoMap()) {
            this.getRankInfoMap().mergeFromCs(proto.getRankInfoMap());
        } else {
            if (this.rankInfoMap != null) {
                this.rankInfoMap.mergeFromCs(proto.getRankInfoMap());
            }
        }
        if (proto.hasZoneRankInfoMap()) {
            this.getZoneRankInfoMap().mergeFromCs(proto.getZoneRankInfoMap());
        } else {
            if (this.zoneRankInfoMap != null) {
                this.zoneRankInfoMap.mergeFromCs(proto.getZoneRankInfoMap());
            }
        }
        this.markAll();
        return MileStoneRankProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneRankPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRankInfoMap()) {
            this.getRankInfoMap().mergeChangeFromCs(proto.getRankInfoMap());
            fieldCnt++;
        }
        if (proto.hasZoneRankInfoMap()) {
            this.getZoneRankInfoMap().mergeChangeFromCs(proto.getZoneRankInfoMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRank.Builder getCopyDbBuilder() {
        final MileStoneRank.Builder builder = MileStoneRank.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneRank.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rankInfoMap != null) {
            Struct.Int64MileStoneClanInfoMap.Builder tmpBuilder = Struct.Int64MileStoneClanInfoMap.newBuilder();
            final int tmpFieldCnt = this.rankInfoMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankInfoMap();
            }
        }  else if (builder.hasRankInfoMap()) {
            // 清理RankInfoMap
            builder.clearRankInfoMap();
            fieldCnt++;
        }
        if (this.zoneRankInfoMap != null) {
            Struct.Int32MileStoneZoneInfoMap.Builder tmpBuilder = Struct.Int32MileStoneZoneInfoMap.newBuilder();
            final int tmpFieldCnt = this.zoneRankInfoMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneRankInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneRankInfoMap();
            }
        }  else if (builder.hasZoneRankInfoMap()) {
            // 清理ZoneRankInfoMap
            builder.clearZoneRankInfoMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneRank.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANKINFOMAP) && this.rankInfoMap != null) {
            final boolean needClear = !builder.hasRankInfoMap();
            final int tmpFieldCnt = this.rankInfoMap.copyChangeToDb(builder.getRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfoMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONERANKINFOMAP) && this.zoneRankInfoMap != null) {
            final boolean needClear = !builder.hasZoneRankInfoMap();
            final int tmpFieldCnt = this.zoneRankInfoMap.copyChangeToDb(builder.getZoneRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneRankInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneRank proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRankInfoMap()) {
            this.getRankInfoMap().mergeFromDb(proto.getRankInfoMap());
        } else {
            if (this.rankInfoMap != null) {
                this.rankInfoMap.mergeFromDb(proto.getRankInfoMap());
            }
        }
        if (proto.hasZoneRankInfoMap()) {
            this.getZoneRankInfoMap().mergeFromDb(proto.getZoneRankInfoMap());
        } else {
            if (this.zoneRankInfoMap != null) {
                this.zoneRankInfoMap.mergeFromDb(proto.getZoneRankInfoMap());
            }
        }
        this.markAll();
        return MileStoneRankProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneRank proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRankInfoMap()) {
            this.getRankInfoMap().mergeChangeFromDb(proto.getRankInfoMap());
            fieldCnt++;
        }
        if (proto.hasZoneRankInfoMap()) {
            this.getZoneRankInfoMap().mergeChangeFromDb(proto.getZoneRankInfoMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRank.Builder getCopySsBuilder() {
        final MileStoneRank.Builder builder = MileStoneRank.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneRank.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rankInfoMap != null) {
            Struct.Int64MileStoneClanInfoMap.Builder tmpBuilder = Struct.Int64MileStoneClanInfoMap.newBuilder();
            final int tmpFieldCnt = this.rankInfoMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRankInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRankInfoMap();
            }
        }  else if (builder.hasRankInfoMap()) {
            // 清理RankInfoMap
            builder.clearRankInfoMap();
            fieldCnt++;
        }
        if (this.zoneRankInfoMap != null) {
            Struct.Int32MileStoneZoneInfoMap.Builder tmpBuilder = Struct.Int32MileStoneZoneInfoMap.newBuilder();
            final int tmpFieldCnt = this.zoneRankInfoMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZoneRankInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZoneRankInfoMap();
            }
        }  else if (builder.hasZoneRankInfoMap()) {
            // 清理ZoneRankInfoMap
            builder.clearZoneRankInfoMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneRank.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANKINFOMAP) && this.rankInfoMap != null) {
            final boolean needClear = !builder.hasRankInfoMap();
            final int tmpFieldCnt = this.rankInfoMap.copyChangeToSs(builder.getRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRankInfoMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZONERANKINFOMAP) && this.zoneRankInfoMap != null) {
            final boolean needClear = !builder.hasZoneRankInfoMap();
            final int tmpFieldCnt = this.zoneRankInfoMap.copyChangeToSs(builder.getZoneRankInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZoneRankInfoMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneRank proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRankInfoMap()) {
            this.getRankInfoMap().mergeFromSs(proto.getRankInfoMap());
        } else {
            if (this.rankInfoMap != null) {
                this.rankInfoMap.mergeFromSs(proto.getRankInfoMap());
            }
        }
        if (proto.hasZoneRankInfoMap()) {
            this.getZoneRankInfoMap().mergeFromSs(proto.getZoneRankInfoMap());
        } else {
            if (this.zoneRankInfoMap != null) {
                this.zoneRankInfoMap.mergeFromSs(proto.getZoneRankInfoMap());
            }
        }
        this.markAll();
        return MileStoneRankProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneRank proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRankInfoMap()) {
            this.getRankInfoMap().mergeChangeFromSs(proto.getRankInfoMap());
            fieldCnt++;
        }
        if (proto.hasZoneRankInfoMap()) {
            this.getZoneRankInfoMap().mergeChangeFromSs(proto.getZoneRankInfoMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneRank.Builder builder = MileStoneRank.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RANKINFOMAP) && this.rankInfoMap != null) {
            this.rankInfoMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ZONERANKINFOMAP) && this.zoneRankInfoMap != null) {
            this.zoneRankInfoMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rankInfoMap != null) {
            this.rankInfoMap.markAll();
        }
        if (this.zoneRankInfoMap != null) {
            this.zoneRankInfoMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneRankProp)) {
            return false;
        }
        final MileStoneRankProp otherNode = (MileStoneRankProp) node;
        if (!this.getRankInfoMap().compareDataTo(otherNode.getRankInfoMap())) {
            return false;
        }
        if (!this.getZoneRankInfoMap().compareDataTo(otherNode.getZoneRankInfoMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
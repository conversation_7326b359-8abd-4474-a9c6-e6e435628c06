package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.SceneDevBuff;
import com.yorha.proto.StructBattlePB.SceneDevBuffPB;


/**
 * <AUTHOR> auto gen
 */
public class SceneDevBuffProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_DEVBUFFID = 0;
    public static final int FIELD_INDEX_ENDTIME = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int devBuffId = Constant.DEFAULT_INT_VALUE;
    private long endTime = Constant.DEFAULT_LONG_VALUE;

    public SceneDevBuffProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SceneDevBuffProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get devBuffId
     *
     * @return devBuffId value
     */
    public int getDevBuffId() {
        return this.devBuffId;
    }

    /**
     * set devBuffId && set marked
     *
     * @param devBuffId new value
     * @return current object
     */
    public SceneDevBuffProp setDevBuffId(int devBuffId) {
        if (this.devBuffId != devBuffId) {
            this.mark(FIELD_INDEX_DEVBUFFID);
            this.devBuffId = devBuffId;
        }
        return this;
    }

    /**
     * inner set devBuffId
     *
     * @param devBuffId new value
     */
    private void innerSetDevBuffId(int devBuffId) {
        this.devBuffId = devBuffId;
    }

    /**
     * get endTime
     *
     * @return endTime value
     */
    public long getEndTime() {
        return this.endTime;
    }

    /**
     * set endTime && set marked
     *
     * @param endTime new value
     * @return current object
     */
    public SceneDevBuffProp setEndTime(long endTime) {
        if (this.endTime != endTime) {
            this.mark(FIELD_INDEX_ENDTIME);
            this.endTime = endTime;
        }
        return this;
    }

    /**
     * inner set endTime
     *
     * @param endTime new value
     */
    private void innerSetEndTime(long endTime) {
        this.endTime = endTime;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneDevBuffPB.Builder getCopyCsBuilder() {
        final SceneDevBuffPB.Builder builder = SceneDevBuffPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SceneDevBuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDevBuffId() != 0) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }  else if (builder.hasDevBuffId()) {
            // 清理DevBuffId
            builder.clearDevBuffId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SceneDevBuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SceneDevBuffPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SceneDevBuffPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffId()) {
            this.innerSetDevBuffId(proto.getDevBuffId());
        } else {
            this.innerSetDevBuffId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SceneDevBuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SceneDevBuffPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffId()) {
            this.setDevBuffId(proto.getDevBuffId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneDevBuff.Builder getCopyDbBuilder() {
        final SceneDevBuff.Builder builder = SceneDevBuff.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SceneDevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDevBuffId() != 0) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }  else if (builder.hasDevBuffId()) {
            // 清理DevBuffId
            builder.clearDevBuffId();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SceneDevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SceneDevBuff proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffId()) {
            this.innerSetDevBuffId(proto.getDevBuffId());
        } else {
            this.innerSetDevBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SceneDevBuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SceneDevBuff proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffId()) {
            this.setDevBuffId(proto.getDevBuffId());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SceneDevBuff.Builder getCopySsBuilder() {
        final SceneDevBuff.Builder builder = SceneDevBuff.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SceneDevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDevBuffId() != 0) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }  else if (builder.hasDevBuffId()) {
            // 清理DevBuffId
            builder.clearDevBuffId();
            fieldCnt++;
        }
        if (this.getEndTime() != 0L) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }  else if (builder.hasEndTime()) {
            // 清理EndTime
            builder.clearEndTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SceneDevBuff.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DEVBUFFID)) {
            builder.setDevBuffId(this.getDevBuffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENDTIME)) {
            builder.setEndTime(this.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SceneDevBuff proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDevBuffId()) {
            this.innerSetDevBuffId(proto.getDevBuffId());
        } else {
            this.innerSetDevBuffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasEndTime()) {
            this.innerSetEndTime(proto.getEndTime());
        } else {
            this.innerSetEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return SceneDevBuffProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SceneDevBuff proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDevBuffId()) {
            this.setDevBuffId(proto.getDevBuffId());
            fieldCnt++;
        }
        if (proto.hasEndTime()) {
            this.setEndTime(proto.getEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SceneDevBuff.Builder builder = SceneDevBuff.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.devBuffId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SceneDevBuffProp)) {
            return false;
        }
        final SceneDevBuffProp otherNode = (SceneDevBuffProp) node;
        if (this.devBuffId != otherNode.devBuffId) {
            return false;
        }
        if (this.endTime != otherNode.endTime) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ActivityLotteryUnit;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityLotteryUnitPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityLotteryUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DRAWDATA = 0;
    public static final int FIELD_INDEX_TOTALTIMES = 1;
    public static final int FIELD_INDEX_TAKENSTAGEREWARDIDS = 2;
    public static final int FIELD_INDEX_VOUCHERBUYTIMES = 3;
    public static final int FIELD_INDEX_SETTLEHERO = 4;
    public static final int FIELD_INDEX_FREETIMES = 5;
    public static final int FIELD_INDEX_CURVOLUME = 6;
    public static final int FIELD_INDEX_CURACTIVITYSTAGE = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private Int32ActLotteryDrawDataMapProp drawData = null;
    private int totalTimes = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp takenStageRewardIds = null;
    private int voucherBuyTimes = Constant.DEFAULT_INT_VALUE;
    private int settleHero = Constant.DEFAULT_INT_VALUE;
    private int freeTimes = Constant.DEFAULT_INT_VALUE;
    private int curVolume = Constant.DEFAULT_INT_VALUE;
    private CommanderActivtyStage curActivityStage = CommanderActivtyStage.forNumber(0);

    public ActivityLotteryUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityLotteryUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get drawData
     *
     * @return drawData value
     */
    public Int32ActLotteryDrawDataMapProp getDrawData() {
        if (this.drawData == null) {
            this.drawData = new Int32ActLotteryDrawDataMapProp(this, FIELD_INDEX_DRAWDATA);
        }
        return this.drawData;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putDrawDataV(ActLotteryDrawDataProp v) {
        this.getDrawData().put(v.getDrawId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ActLotteryDrawDataProp addEmptyDrawData(Integer k) {
        return this.getDrawData().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getDrawDataSize() {
        if (this.drawData == null) {
            return 0;
        }
        return this.drawData.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isDrawDataEmpty() {
        if (this.drawData == null) {
            return true;
        }
        return this.drawData.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ActLotteryDrawDataProp getDrawDataV(Integer k) {
        if (this.drawData == null || !this.drawData.containsKey(k)) {
            return null;
        }
        return this.drawData.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearDrawData() {
        if (this.drawData != null) {
            this.drawData.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeDrawDataV(Integer k) {
        if (this.drawData != null) {
            this.drawData.remove(k);
        }
    }
    /**
     * get totalTimes
     *
     * @return totalTimes value
     */
    public int getTotalTimes() {
        return this.totalTimes;
    }

    /**
     * set totalTimes && set marked
     *
     * @param totalTimes new value
     * @return current object
     */
    public ActivityLotteryUnitProp setTotalTimes(int totalTimes) {
        if (this.totalTimes != totalTimes) {
            this.mark(FIELD_INDEX_TOTALTIMES);
            this.totalTimes = totalTimes;
        }
        return this;
    }

    /**
     * inner set totalTimes
     *
     * @param totalTimes new value
     */
    private void innerSetTotalTimes(int totalTimes) {
        this.totalTimes = totalTimes;
    }

    /**
     * get takenStageRewardIds
     *
     * @return takenStageRewardIds value
     */
    public Int32ListProp getTakenStageRewardIds() {
        if (this.takenStageRewardIds == null) {
            this.takenStageRewardIds = new Int32ListProp(this, FIELD_INDEX_TAKENSTAGEREWARDIDS);
        }
        return this.takenStageRewardIds;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addTakenStageRewardIds(Integer v) {
        this.getTakenStageRewardIds().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getTakenStageRewardIdsIndex(int index) {
        return this.getTakenStageRewardIds().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeTakenStageRewardIds(Integer v) {
        if (this.takenStageRewardIds == null) {
            return null;
        }
        if(this.takenStageRewardIds.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getTakenStageRewardIdsSize() {
        if (this.takenStageRewardIds == null) {
            return 0;
        }
        return this.takenStageRewardIds.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isTakenStageRewardIdsEmpty() {
        if (this.takenStageRewardIds == null) {
            return true;
        }
        return this.getTakenStageRewardIds().isEmpty();
    }

    /**
     * clear list
     */
    public void clearTakenStageRewardIds() {
        this.getTakenStageRewardIds().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeTakenStageRewardIdsIndex(int index) {
        return this.getTakenStageRewardIds().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setTakenStageRewardIdsIndex(int index, Integer v) {
        return this.getTakenStageRewardIds().set(index, v);
    }
    /**
     * get voucherBuyTimes
     *
     * @return voucherBuyTimes value
     */
    public int getVoucherBuyTimes() {
        return this.voucherBuyTimes;
    }

    /**
     * set voucherBuyTimes && set marked
     *
     * @param voucherBuyTimes new value
     * @return current object
     */
    public ActivityLotteryUnitProp setVoucherBuyTimes(int voucherBuyTimes) {
        if (this.voucherBuyTimes != voucherBuyTimes) {
            this.mark(FIELD_INDEX_VOUCHERBUYTIMES);
            this.voucherBuyTimes = voucherBuyTimes;
        }
        return this;
    }

    /**
     * inner set voucherBuyTimes
     *
     * @param voucherBuyTimes new value
     */
    private void innerSetVoucherBuyTimes(int voucherBuyTimes) {
        this.voucherBuyTimes = voucherBuyTimes;
    }

    /**
     * get settleHero
     *
     * @return settleHero value
     */
    public int getSettleHero() {
        return this.settleHero;
    }

    /**
     * set settleHero && set marked
     *
     * @param settleHero new value
     * @return current object
     */
    public ActivityLotteryUnitProp setSettleHero(int settleHero) {
        if (this.settleHero != settleHero) {
            this.mark(FIELD_INDEX_SETTLEHERO);
            this.settleHero = settleHero;
        }
        return this;
    }

    /**
     * inner set settleHero
     *
     * @param settleHero new value
     */
    private void innerSetSettleHero(int settleHero) {
        this.settleHero = settleHero;
    }

    /**
     * get freeTimes
     *
     * @return freeTimes value
     */
    public int getFreeTimes() {
        return this.freeTimes;
    }

    /**
     * set freeTimes && set marked
     *
     * @param freeTimes new value
     * @return current object
     */
    public ActivityLotteryUnitProp setFreeTimes(int freeTimes) {
        if (this.freeTimes != freeTimes) {
            this.mark(FIELD_INDEX_FREETIMES);
            this.freeTimes = freeTimes;
        }
        return this;
    }

    /**
     * inner set freeTimes
     *
     * @param freeTimes new value
     */
    private void innerSetFreeTimes(int freeTimes) {
        this.freeTimes = freeTimes;
    }

    /**
     * get curVolume
     *
     * @return curVolume value
     */
    public int getCurVolume() {
        return this.curVolume;
    }

    /**
     * set curVolume && set marked
     *
     * @param curVolume new value
     * @return current object
     */
    public ActivityLotteryUnitProp setCurVolume(int curVolume) {
        if (this.curVolume != curVolume) {
            this.mark(FIELD_INDEX_CURVOLUME);
            this.curVolume = curVolume;
        }
        return this;
    }

    /**
     * inner set curVolume
     *
     * @param curVolume new value
     */
    private void innerSetCurVolume(int curVolume) {
        this.curVolume = curVolume;
    }

    /**
     * get curActivityStage
     *
     * @return curActivityStage value
     */
    public CommanderActivtyStage getCurActivityStage() {
        return this.curActivityStage;
    }

    /**
     * set curActivityStage && set marked
     *
     * @param curActivityStage new value
     * @return current object
     */
    public ActivityLotteryUnitProp setCurActivityStage(CommanderActivtyStage curActivityStage) {
        if (curActivityStage == null) {
            throw new NullPointerException();
        }
        if (this.curActivityStage != curActivityStage) {
            this.mark(FIELD_INDEX_CURACTIVITYSTAGE);
            this.curActivityStage = curActivityStage;
        }
        return this;
    }

    /**
     * inner set curActivityStage
     *
     * @param curActivityStage new value
     */
    private void innerSetCurActivityStage(CommanderActivtyStage curActivityStage) {
        this.curActivityStage = curActivityStage;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLotteryUnitPB.Builder getCopyCsBuilder() {
        final ActivityLotteryUnitPB.Builder builder = ActivityLotteryUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityLotteryUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.drawData != null) {
            StructPB.Int32ActLotteryDrawDataMapPB.Builder tmpBuilder = StructPB.Int32ActLotteryDrawDataMapPB.newBuilder();
            final int tmpFieldCnt = this.drawData.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDrawData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDrawData();
            }
        }  else if (builder.hasDrawData()) {
            // 清理DrawData
            builder.clearDrawData();
            fieldCnt++;
        }
        if (this.getTotalTimes() != 0) {
            builder.setTotalTimes(this.getTotalTimes());
            fieldCnt++;
        }  else if (builder.hasTotalTimes()) {
            // 清理TotalTimes
            builder.clearTotalTimes();
            fieldCnt++;
        }
        if (this.takenStageRewardIds != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.takenStageRewardIds.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTakenStageRewardIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTakenStageRewardIds();
            }
        }  else if (builder.hasTakenStageRewardIds()) {
            // 清理TakenStageRewardIds
            builder.clearTakenStageRewardIds();
            fieldCnt++;
        }
        if (this.getVoucherBuyTimes() != 0) {
            builder.setVoucherBuyTimes(this.getVoucherBuyTimes());
            fieldCnt++;
        }  else if (builder.hasVoucherBuyTimes()) {
            // 清理VoucherBuyTimes
            builder.clearVoucherBuyTimes();
            fieldCnt++;
        }
        if (this.getSettleHero() != 0) {
            builder.setSettleHero(this.getSettleHero());
            fieldCnt++;
        }  else if (builder.hasSettleHero()) {
            // 清理SettleHero
            builder.clearSettleHero();
            fieldCnt++;
        }
        if (this.getFreeTimes() != 0) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }  else if (builder.hasFreeTimes()) {
            // 清理FreeTimes
            builder.clearFreeTimes();
            fieldCnt++;
        }
        if (this.getCurVolume() != 0) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }  else if (builder.hasCurVolume()) {
            // 清理CurVolume
            builder.clearCurVolume();
            fieldCnt++;
        }
        if (this.getCurActivityStage() != CommanderActivtyStage.forNumber(0)) {
            builder.setCurActivityStage(this.getCurActivityStage());
            fieldCnt++;
        }  else if (builder.hasCurActivityStage()) {
            // 清理CurActivityStage
            builder.clearCurActivityStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityLotteryUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWDATA) && this.drawData != null) {
            final boolean needClear = !builder.hasDrawData();
            final int tmpFieldCnt = this.drawData.copyChangeToCs(builder.getDrawDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDrawData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALTIMES)) {
            builder.setTotalTimes(this.getTotalTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENSTAGEREWARDIDS) && this.takenStageRewardIds != null) {
            final boolean needClear = !builder.hasTakenStageRewardIds();
            final int tmpFieldCnt = this.takenStageRewardIds.copyChangeToCs(builder.getTakenStageRewardIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenStageRewardIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOUCHERBUYTIMES)) {
            builder.setVoucherBuyTimes(this.getVoucherBuyTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETTLEHERO)) {
            builder.setSettleHero(this.getSettleHero());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURACTIVITYSTAGE)) {
            builder.setCurActivityStage(this.getCurActivityStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityLotteryUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWDATA) && this.drawData != null) {
            final boolean needClear = !builder.hasDrawData();
            final int tmpFieldCnt = this.drawData.copyChangeToAndClearDeleteKeysCs(builder.getDrawDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDrawData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALTIMES)) {
            builder.setTotalTimes(this.getTotalTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENSTAGEREWARDIDS) && this.takenStageRewardIds != null) {
            final boolean needClear = !builder.hasTakenStageRewardIds();
            final int tmpFieldCnt = this.takenStageRewardIds.copyChangeToCs(builder.getTakenStageRewardIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenStageRewardIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOUCHERBUYTIMES)) {
            builder.setVoucherBuyTimes(this.getVoucherBuyTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETTLEHERO)) {
            builder.setSettleHero(this.getSettleHero());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURACTIVITYSTAGE)) {
            builder.setCurActivityStage(this.getCurActivityStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityLotteryUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDrawData()) {
            this.getDrawData().mergeFromCs(proto.getDrawData());
        } else {
            if (this.drawData != null) {
                this.drawData.mergeFromCs(proto.getDrawData());
            }
        }
        if (proto.hasTotalTimes()) {
            this.innerSetTotalTimes(proto.getTotalTimes());
        } else {
            this.innerSetTotalTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTakenStageRewardIds()) {
            this.getTakenStageRewardIds().mergeFromCs(proto.getTakenStageRewardIds());
        } else {
            if (this.takenStageRewardIds != null) {
                this.takenStageRewardIds.mergeFromCs(proto.getTakenStageRewardIds());
            }
        }
        if (proto.hasVoucherBuyTimes()) {
            this.innerSetVoucherBuyTimes(proto.getVoucherBuyTimes());
        } else {
            this.innerSetVoucherBuyTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSettleHero()) {
            this.innerSetSettleHero(proto.getSettleHero());
        } else {
            this.innerSetSettleHero(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeTimes()) {
            this.innerSetFreeTimes(proto.getFreeTimes());
        } else {
            this.innerSetFreeTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurVolume()) {
            this.innerSetCurVolume(proto.getCurVolume());
        } else {
            this.innerSetCurVolume(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurActivityStage()) {
            this.innerSetCurActivityStage(proto.getCurActivityStage());
        } else {
            this.innerSetCurActivityStage(CommanderActivtyStage.forNumber(0));
        }
        this.markAll();
        return ActivityLotteryUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityLotteryUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDrawData()) {
            this.getDrawData().mergeChangeFromCs(proto.getDrawData());
            fieldCnt++;
        }
        if (proto.hasTotalTimes()) {
            this.setTotalTimes(proto.getTotalTimes());
            fieldCnt++;
        }
        if (proto.hasTakenStageRewardIds()) {
            this.getTakenStageRewardIds().mergeChangeFromCs(proto.getTakenStageRewardIds());
            fieldCnt++;
        }
        if (proto.hasVoucherBuyTimes()) {
            this.setVoucherBuyTimes(proto.getVoucherBuyTimes());
            fieldCnt++;
        }
        if (proto.hasSettleHero()) {
            this.setSettleHero(proto.getSettleHero());
            fieldCnt++;
        }
        if (proto.hasFreeTimes()) {
            this.setFreeTimes(proto.getFreeTimes());
            fieldCnt++;
        }
        if (proto.hasCurVolume()) {
            this.setCurVolume(proto.getCurVolume());
            fieldCnt++;
        }
        if (proto.hasCurActivityStage()) {
            this.setCurActivityStage(proto.getCurActivityStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLotteryUnit.Builder getCopyDbBuilder() {
        final ActivityLotteryUnit.Builder builder = ActivityLotteryUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityLotteryUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.drawData != null) {
            Struct.Int32ActLotteryDrawDataMap.Builder tmpBuilder = Struct.Int32ActLotteryDrawDataMap.newBuilder();
            final int tmpFieldCnt = this.drawData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDrawData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDrawData();
            }
        }  else if (builder.hasDrawData()) {
            // 清理DrawData
            builder.clearDrawData();
            fieldCnt++;
        }
        if (this.getTotalTimes() != 0) {
            builder.setTotalTimes(this.getTotalTimes());
            fieldCnt++;
        }  else if (builder.hasTotalTimes()) {
            // 清理TotalTimes
            builder.clearTotalTimes();
            fieldCnt++;
        }
        if (this.takenStageRewardIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.takenStageRewardIds.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTakenStageRewardIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTakenStageRewardIds();
            }
        }  else if (builder.hasTakenStageRewardIds()) {
            // 清理TakenStageRewardIds
            builder.clearTakenStageRewardIds();
            fieldCnt++;
        }
        if (this.getVoucherBuyTimes() != 0) {
            builder.setVoucherBuyTimes(this.getVoucherBuyTimes());
            fieldCnt++;
        }  else if (builder.hasVoucherBuyTimes()) {
            // 清理VoucherBuyTimes
            builder.clearVoucherBuyTimes();
            fieldCnt++;
        }
        if (this.getSettleHero() != 0) {
            builder.setSettleHero(this.getSettleHero());
            fieldCnt++;
        }  else if (builder.hasSettleHero()) {
            // 清理SettleHero
            builder.clearSettleHero();
            fieldCnt++;
        }
        if (this.getFreeTimes() != 0) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }  else if (builder.hasFreeTimes()) {
            // 清理FreeTimes
            builder.clearFreeTimes();
            fieldCnt++;
        }
        if (this.getCurVolume() != 0) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }  else if (builder.hasCurVolume()) {
            // 清理CurVolume
            builder.clearCurVolume();
            fieldCnt++;
        }
        if (this.getCurActivityStage() != CommanderActivtyStage.forNumber(0)) {
            builder.setCurActivityStage(this.getCurActivityStage());
            fieldCnt++;
        }  else if (builder.hasCurActivityStage()) {
            // 清理CurActivityStage
            builder.clearCurActivityStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityLotteryUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWDATA) && this.drawData != null) {
            final boolean needClear = !builder.hasDrawData();
            final int tmpFieldCnt = this.drawData.copyChangeToDb(builder.getDrawDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDrawData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALTIMES)) {
            builder.setTotalTimes(this.getTotalTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENSTAGEREWARDIDS) && this.takenStageRewardIds != null) {
            final boolean needClear = !builder.hasTakenStageRewardIds();
            final int tmpFieldCnt = this.takenStageRewardIds.copyChangeToDb(builder.getTakenStageRewardIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenStageRewardIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOUCHERBUYTIMES)) {
            builder.setVoucherBuyTimes(this.getVoucherBuyTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETTLEHERO)) {
            builder.setSettleHero(this.getSettleHero());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURACTIVITYSTAGE)) {
            builder.setCurActivityStage(this.getCurActivityStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityLotteryUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDrawData()) {
            this.getDrawData().mergeFromDb(proto.getDrawData());
        } else {
            if (this.drawData != null) {
                this.drawData.mergeFromDb(proto.getDrawData());
            }
        }
        if (proto.hasTotalTimes()) {
            this.innerSetTotalTimes(proto.getTotalTimes());
        } else {
            this.innerSetTotalTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTakenStageRewardIds()) {
            this.getTakenStageRewardIds().mergeFromDb(proto.getTakenStageRewardIds());
        } else {
            if (this.takenStageRewardIds != null) {
                this.takenStageRewardIds.mergeFromDb(proto.getTakenStageRewardIds());
            }
        }
        if (proto.hasVoucherBuyTimes()) {
            this.innerSetVoucherBuyTimes(proto.getVoucherBuyTimes());
        } else {
            this.innerSetVoucherBuyTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSettleHero()) {
            this.innerSetSettleHero(proto.getSettleHero());
        } else {
            this.innerSetSettleHero(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeTimes()) {
            this.innerSetFreeTimes(proto.getFreeTimes());
        } else {
            this.innerSetFreeTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurVolume()) {
            this.innerSetCurVolume(proto.getCurVolume());
        } else {
            this.innerSetCurVolume(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurActivityStage()) {
            this.innerSetCurActivityStage(proto.getCurActivityStage());
        } else {
            this.innerSetCurActivityStage(CommanderActivtyStage.forNumber(0));
        }
        this.markAll();
        return ActivityLotteryUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityLotteryUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDrawData()) {
            this.getDrawData().mergeChangeFromDb(proto.getDrawData());
            fieldCnt++;
        }
        if (proto.hasTotalTimes()) {
            this.setTotalTimes(proto.getTotalTimes());
            fieldCnt++;
        }
        if (proto.hasTakenStageRewardIds()) {
            this.getTakenStageRewardIds().mergeChangeFromDb(proto.getTakenStageRewardIds());
            fieldCnt++;
        }
        if (proto.hasVoucherBuyTimes()) {
            this.setVoucherBuyTimes(proto.getVoucherBuyTimes());
            fieldCnt++;
        }
        if (proto.hasSettleHero()) {
            this.setSettleHero(proto.getSettleHero());
            fieldCnt++;
        }
        if (proto.hasFreeTimes()) {
            this.setFreeTimes(proto.getFreeTimes());
            fieldCnt++;
        }
        if (proto.hasCurVolume()) {
            this.setCurVolume(proto.getCurVolume());
            fieldCnt++;
        }
        if (proto.hasCurActivityStage()) {
            this.setCurActivityStage(proto.getCurActivityStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityLotteryUnit.Builder getCopySsBuilder() {
        final ActivityLotteryUnit.Builder builder = ActivityLotteryUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityLotteryUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.drawData != null) {
            Struct.Int32ActLotteryDrawDataMap.Builder tmpBuilder = Struct.Int32ActLotteryDrawDataMap.newBuilder();
            final int tmpFieldCnt = this.drawData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDrawData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDrawData();
            }
        }  else if (builder.hasDrawData()) {
            // 清理DrawData
            builder.clearDrawData();
            fieldCnt++;
        }
        if (this.getTotalTimes() != 0) {
            builder.setTotalTimes(this.getTotalTimes());
            fieldCnt++;
        }  else if (builder.hasTotalTimes()) {
            // 清理TotalTimes
            builder.clearTotalTimes();
            fieldCnt++;
        }
        if (this.takenStageRewardIds != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.takenStageRewardIds.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTakenStageRewardIds(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTakenStageRewardIds();
            }
        }  else if (builder.hasTakenStageRewardIds()) {
            // 清理TakenStageRewardIds
            builder.clearTakenStageRewardIds();
            fieldCnt++;
        }
        if (this.getVoucherBuyTimes() != 0) {
            builder.setVoucherBuyTimes(this.getVoucherBuyTimes());
            fieldCnt++;
        }  else if (builder.hasVoucherBuyTimes()) {
            // 清理VoucherBuyTimes
            builder.clearVoucherBuyTimes();
            fieldCnt++;
        }
        if (this.getSettleHero() != 0) {
            builder.setSettleHero(this.getSettleHero());
            fieldCnt++;
        }  else if (builder.hasSettleHero()) {
            // 清理SettleHero
            builder.clearSettleHero();
            fieldCnt++;
        }
        if (this.getFreeTimes() != 0) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }  else if (builder.hasFreeTimes()) {
            // 清理FreeTimes
            builder.clearFreeTimes();
            fieldCnt++;
        }
        if (this.getCurVolume() != 0) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }  else if (builder.hasCurVolume()) {
            // 清理CurVolume
            builder.clearCurVolume();
            fieldCnt++;
        }
        if (this.getCurActivityStage() != CommanderActivtyStage.forNumber(0)) {
            builder.setCurActivityStage(this.getCurActivityStage());
            fieldCnt++;
        }  else if (builder.hasCurActivityStage()) {
            // 清理CurActivityStage
            builder.clearCurActivityStage();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityLotteryUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DRAWDATA) && this.drawData != null) {
            final boolean needClear = !builder.hasDrawData();
            final int tmpFieldCnt = this.drawData.copyChangeToSs(builder.getDrawDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDrawData();
            }
        }
        if (this.hasMark(FIELD_INDEX_TOTALTIMES)) {
            builder.setTotalTimes(this.getTotalTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TAKENSTAGEREWARDIDS) && this.takenStageRewardIds != null) {
            final boolean needClear = !builder.hasTakenStageRewardIds();
            final int tmpFieldCnt = this.takenStageRewardIds.copyChangeToSs(builder.getTakenStageRewardIdsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTakenStageRewardIds();
            }
        }
        if (this.hasMark(FIELD_INDEX_VOUCHERBUYTIMES)) {
            builder.setVoucherBuyTimes(this.getVoucherBuyTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SETTLEHERO)) {
            builder.setSettleHero(this.getSettleHero());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREETIMES)) {
            builder.setFreeTimes(this.getFreeTimes());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURVOLUME)) {
            builder.setCurVolume(this.getCurVolume());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURACTIVITYSTAGE)) {
            builder.setCurActivityStage(this.getCurActivityStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityLotteryUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDrawData()) {
            this.getDrawData().mergeFromSs(proto.getDrawData());
        } else {
            if (this.drawData != null) {
                this.drawData.mergeFromSs(proto.getDrawData());
            }
        }
        if (proto.hasTotalTimes()) {
            this.innerSetTotalTimes(proto.getTotalTimes());
        } else {
            this.innerSetTotalTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTakenStageRewardIds()) {
            this.getTakenStageRewardIds().mergeFromSs(proto.getTakenStageRewardIds());
        } else {
            if (this.takenStageRewardIds != null) {
                this.takenStageRewardIds.mergeFromSs(proto.getTakenStageRewardIds());
            }
        }
        if (proto.hasVoucherBuyTimes()) {
            this.innerSetVoucherBuyTimes(proto.getVoucherBuyTimes());
        } else {
            this.innerSetVoucherBuyTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSettleHero()) {
            this.innerSetSettleHero(proto.getSettleHero());
        } else {
            this.innerSetSettleHero(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeTimes()) {
            this.innerSetFreeTimes(proto.getFreeTimes());
        } else {
            this.innerSetFreeTimes(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurVolume()) {
            this.innerSetCurVolume(proto.getCurVolume());
        } else {
            this.innerSetCurVolume(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurActivityStage()) {
            this.innerSetCurActivityStage(proto.getCurActivityStage());
        } else {
            this.innerSetCurActivityStage(CommanderActivtyStage.forNumber(0));
        }
        this.markAll();
        return ActivityLotteryUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityLotteryUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDrawData()) {
            this.getDrawData().mergeChangeFromSs(proto.getDrawData());
            fieldCnt++;
        }
        if (proto.hasTotalTimes()) {
            this.setTotalTimes(proto.getTotalTimes());
            fieldCnt++;
        }
        if (proto.hasTakenStageRewardIds()) {
            this.getTakenStageRewardIds().mergeChangeFromSs(proto.getTakenStageRewardIds());
            fieldCnt++;
        }
        if (proto.hasVoucherBuyTimes()) {
            this.setVoucherBuyTimes(proto.getVoucherBuyTimes());
            fieldCnt++;
        }
        if (proto.hasSettleHero()) {
            this.setSettleHero(proto.getSettleHero());
            fieldCnt++;
        }
        if (proto.hasFreeTimes()) {
            this.setFreeTimes(proto.getFreeTimes());
            fieldCnt++;
        }
        if (proto.hasCurVolume()) {
            this.setCurVolume(proto.getCurVolume());
            fieldCnt++;
        }
        if (proto.hasCurActivityStage()) {
            this.setCurActivityStage(proto.getCurActivityStage());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityLotteryUnit.Builder builder = ActivityLotteryUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_DRAWDATA) && this.drawData != null) {
            this.drawData.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TAKENSTAGEREWARDIDS) && this.takenStageRewardIds != null) {
            this.takenStageRewardIds.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.drawData != null) {
            this.drawData.markAll();
        }
        if (this.takenStageRewardIds != null) {
            this.takenStageRewardIds.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityLotteryUnitProp)) {
            return false;
        }
        final ActivityLotteryUnitProp otherNode = (ActivityLotteryUnitProp) node;
        if (!this.getDrawData().compareDataTo(otherNode.getDrawData())) {
            return false;
        }
        if (this.totalTimes != otherNode.totalTimes) {
            return false;
        }
        if (!this.getTakenStageRewardIds().compareDataTo(otherNode.getTakenStageRewardIds())) {
            return false;
        }
        if (this.voucherBuyTimes != otherNode.voucherBuyTimes) {
            return false;
        }
        if (this.settleHero != otherNode.settleHero) {
            return false;
        }
        if (this.freeTimes != otherNode.freeTimes) {
            return false;
        }
        if (this.curVolume != otherNode.curVolume) {
            return false;
        }
        if (this.curActivityStage != otherNode.curActivityStage) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Zone.KingdomSkillModel;
import com.yorha.proto.Zone;
import com.yorha.proto.ZonePB.KingdomSkillModelPB;
import com.yorha.proto.ZonePB;


/**
 * <AUTHOR> auto gen
 */
public class KingdomSkillModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SKILLINFO = 0;
    public static final int FIELD_INDEX_TAXSKILLINFO = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32KingdomSkillInfoMapProp skillInfo = null;
    private TaxSkillInfoProp taxSkillInfo = null;

    public KingdomSkillModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public KingdomSkillModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get skillInfo
     *
     * @return skillInfo value
     */
    public Int32KingdomSkillInfoMapProp getSkillInfo() {
        if (this.skillInfo == null) {
            this.skillInfo = new Int32KingdomSkillInfoMapProp(this, FIELD_INDEX_SKILLINFO);
        }
        return this.skillInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSkillInfoV(KingdomSkillInfoProp v) {
        this.getSkillInfo().put(v.getSkillId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public KingdomSkillInfoProp addEmptySkillInfo(Integer k) {
        return this.getSkillInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSkillInfoSize() {
        if (this.skillInfo == null) {
            return 0;
        }
        return this.skillInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSkillInfoEmpty() {
        if (this.skillInfo == null) {
            return true;
        }
        return this.skillInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public KingdomSkillInfoProp getSkillInfoV(Integer k) {
        if (this.skillInfo == null || !this.skillInfo.containsKey(k)) {
            return null;
        }
        return this.skillInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSkillInfo() {
        if (this.skillInfo != null) {
            this.skillInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSkillInfoV(Integer k) {
        if (this.skillInfo != null) {
            this.skillInfo.remove(k);
        }
    }
    /**
     * get taxSkillInfo
     *
     * @return taxSkillInfo value
     */
    public TaxSkillInfoProp getTaxSkillInfo() {
        if (this.taxSkillInfo == null) {
            this.taxSkillInfo = new TaxSkillInfoProp(this, FIELD_INDEX_TAXSKILLINFO);
        }
        return this.taxSkillInfo;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSkillModelPB.Builder getCopyCsBuilder() {
        final KingdomSkillModelPB.Builder builder = KingdomSkillModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(KingdomSkillModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.skillInfo != null) {
            ZonePB.Int32KingdomSkillInfoMapPB.Builder tmpBuilder = ZonePB.Int32KingdomSkillInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.skillInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillInfo();
            }
        }  else if (builder.hasSkillInfo()) {
            // 清理SkillInfo
            builder.clearSkillInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(KingdomSkillModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLINFO) && this.skillInfo != null) {
            final boolean needClear = !builder.hasSkillInfo();
            final int tmpFieldCnt = this.skillInfo.copyChangeToCs(builder.getSkillInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(KingdomSkillModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLINFO) && this.skillInfo != null) {
            final boolean needClear = !builder.hasSkillInfo();
            final int tmpFieldCnt = this.skillInfo.copyChangeToAndClearDeleteKeysCs(builder.getSkillInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(KingdomSkillModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillInfo()) {
            this.getSkillInfo().mergeFromCs(proto.getSkillInfo());
        } else {
            if (this.skillInfo != null) {
                this.skillInfo.mergeFromCs(proto.getSkillInfo());
            }
        }
        this.markAll();
        return KingdomSkillModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(KingdomSkillModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillInfo()) {
            this.getSkillInfo().mergeChangeFromCs(proto.getSkillInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSkillModel.Builder getCopyDbBuilder() {
        final KingdomSkillModel.Builder builder = KingdomSkillModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(KingdomSkillModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.skillInfo != null) {
            Zone.Int32KingdomSkillInfoMap.Builder tmpBuilder = Zone.Int32KingdomSkillInfoMap.newBuilder();
            final int tmpFieldCnt = this.skillInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillInfo();
            }
        }  else if (builder.hasSkillInfo()) {
            // 清理SkillInfo
            builder.clearSkillInfo();
            fieldCnt++;
        }
        if (this.taxSkillInfo != null) {
            Zone.TaxSkillInfo.Builder tmpBuilder = Zone.TaxSkillInfo.newBuilder();
            final int tmpFieldCnt = this.taxSkillInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaxSkillInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaxSkillInfo();
            }
        }  else if (builder.hasTaxSkillInfo()) {
            // 清理TaxSkillInfo
            builder.clearTaxSkillInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(KingdomSkillModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLINFO) && this.skillInfo != null) {
            final boolean needClear = !builder.hasSkillInfo();
            final int tmpFieldCnt = this.skillInfo.copyChangeToDb(builder.getSkillInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_TAXSKILLINFO) && this.taxSkillInfo != null) {
            final boolean needClear = !builder.hasTaxSkillInfo();
            final int tmpFieldCnt = this.taxSkillInfo.copyChangeToDb(builder.getTaxSkillInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaxSkillInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(KingdomSkillModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillInfo()) {
            this.getSkillInfo().mergeFromDb(proto.getSkillInfo());
        } else {
            if (this.skillInfo != null) {
                this.skillInfo.mergeFromDb(proto.getSkillInfo());
            }
        }
        if (proto.hasTaxSkillInfo()) {
            this.getTaxSkillInfo().mergeFromDb(proto.getTaxSkillInfo());
        } else {
            if (this.taxSkillInfo != null) {
                this.taxSkillInfo.mergeFromDb(proto.getTaxSkillInfo());
            }
        }
        this.markAll();
        return KingdomSkillModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(KingdomSkillModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillInfo()) {
            this.getSkillInfo().mergeChangeFromDb(proto.getSkillInfo());
            fieldCnt++;
        }
        if (proto.hasTaxSkillInfo()) {
            this.getTaxSkillInfo().mergeChangeFromDb(proto.getTaxSkillInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public KingdomSkillModel.Builder getCopySsBuilder() {
        final KingdomSkillModel.Builder builder = KingdomSkillModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(KingdomSkillModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.skillInfo != null) {
            Zone.Int32KingdomSkillInfoMap.Builder tmpBuilder = Zone.Int32KingdomSkillInfoMap.newBuilder();
            final int tmpFieldCnt = this.skillInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSkillInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSkillInfo();
            }
        }  else if (builder.hasSkillInfo()) {
            // 清理SkillInfo
            builder.clearSkillInfo();
            fieldCnt++;
        }
        if (this.taxSkillInfo != null) {
            Zone.TaxSkillInfo.Builder tmpBuilder = Zone.TaxSkillInfo.newBuilder();
            final int tmpFieldCnt = this.taxSkillInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaxSkillInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaxSkillInfo();
            }
        }  else if (builder.hasTaxSkillInfo()) {
            // 清理TaxSkillInfo
            builder.clearTaxSkillInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(KingdomSkillModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SKILLINFO) && this.skillInfo != null) {
            final boolean needClear = !builder.hasSkillInfo();
            final int tmpFieldCnt = this.skillInfo.copyChangeToSs(builder.getSkillInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSkillInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_TAXSKILLINFO) && this.taxSkillInfo != null) {
            final boolean needClear = !builder.hasTaxSkillInfo();
            final int tmpFieldCnt = this.taxSkillInfo.copyChangeToSs(builder.getTaxSkillInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaxSkillInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(KingdomSkillModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSkillInfo()) {
            this.getSkillInfo().mergeFromSs(proto.getSkillInfo());
        } else {
            if (this.skillInfo != null) {
                this.skillInfo.mergeFromSs(proto.getSkillInfo());
            }
        }
        if (proto.hasTaxSkillInfo()) {
            this.getTaxSkillInfo().mergeFromSs(proto.getTaxSkillInfo());
        } else {
            if (this.taxSkillInfo != null) {
                this.taxSkillInfo.mergeFromSs(proto.getTaxSkillInfo());
            }
        }
        this.markAll();
        return KingdomSkillModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(KingdomSkillModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSkillInfo()) {
            this.getSkillInfo().mergeChangeFromSs(proto.getSkillInfo());
            fieldCnt++;
        }
        if (proto.hasTaxSkillInfo()) {
            this.getTaxSkillInfo().mergeChangeFromSs(proto.getTaxSkillInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        KingdomSkillModel.Builder builder = KingdomSkillModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SKILLINFO) && this.skillInfo != null) {
            this.skillInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TAXSKILLINFO) && this.taxSkillInfo != null) {
            this.taxSkillInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.skillInfo != null) {
            this.skillInfo.markAll();
        }
        if (this.taxSkillInfo != null) {
            this.taxSkillInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof KingdomSkillModelProp)) {
            return false;
        }
        final KingdomSkillModelProp otherNode = (KingdomSkillModelProp) node;
        if (!this.getSkillInfo().compareDataTo(otherNode.getSkillInfo())) {
            return false;
        }
        if (!this.getTaxSkillInfo().compareDataTo(otherNode.getTaxSkillInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.WallInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.WallInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class WallInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_HPMAX = 0;
    public static final int FIELD_INDEX_HP = 1;
    public static final int FIELD_INDEX_MAINHERO = 2;
    public static final int FIELD_INDEX_DEPUTYHERO = 3;
    public static final int FIELD_INDEX_BURNINGCALTSMS = 4;
    public static final int FIELD_INDEX_CANREPAIRTSMS = 5;
    public static final int FIELD_INDEX_STATEENDTSMS = 6;
    public static final int FIELD_INDEX_BURNSPEED = 7;
    public static final int FIELD_INDEX_TOWERID = 8;
    public static final int FIELD_INDEX_TOWERHPMAX = 9;
    public static final int FIELD_INDEX_TOWERHP = 10;
    public static final int FIELD_INDEX_TOWERHPCALTSMS = 11;
    public static final int FIELD_INDEX_BATTLEPLANE = 12;

    public static final int FIELD_COUNT = 13;

    private long markBits0 = 0L;

    private int hpMax = Constant.DEFAULT_INT_VALUE;
    private int hp = Constant.DEFAULT_INT_VALUE;
    private HeroProp mainHero = null;
    private HeroProp deputyHero = null;
    private long burningCalTsMs = Constant.DEFAULT_LONG_VALUE;
    private long canRepairTsMs = Constant.DEFAULT_LONG_VALUE;
    private long stateEndTsMs = Constant.DEFAULT_LONG_VALUE;
    private int burnSpeed = Constant.DEFAULT_INT_VALUE;
    private int towerId = Constant.DEFAULT_INT_VALUE;
    private int towerHpMax = Constant.DEFAULT_INT_VALUE;
    private int towerHp = Constant.DEFAULT_INT_VALUE;
    private long towerHpCalTsMs = Constant.DEFAULT_LONG_VALUE;
    private SceneBattlePlaneProp battlePlane = null;

    public WallInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public WallInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get hpMax
     *
     * @return hpMax value
     */
    public int getHpMax() {
        return this.hpMax;
    }

    /**
     * set hpMax && set marked
     *
     * @param hpMax new value
     * @return current object
     */
    public WallInfoProp setHpMax(int hpMax) {
        if (this.hpMax != hpMax) {
            this.mark(FIELD_INDEX_HPMAX);
            this.hpMax = hpMax;
        }
        return this;
    }

    /**
     * inner set hpMax
     *
     * @param hpMax new value
     */
    private void innerSetHpMax(int hpMax) {
        this.hpMax = hpMax;
    }

    /**
     * get hp
     *
     * @return hp value
     */
    public int getHp() {
        return this.hp;
    }

    /**
     * set hp && set marked
     *
     * @param hp new value
     * @return current object
     */
    public WallInfoProp setHp(int hp) {
        if (this.hp != hp) {
            this.mark(FIELD_INDEX_HP);
            this.hp = hp;
        }
        return this;
    }

    /**
     * inner set hp
     *
     * @param hp new value
     */
    private void innerSetHp(int hp) {
        this.hp = hp;
    }

    /**
     * get mainHero
     *
     * @return mainHero value
     */
    public HeroProp getMainHero() {
        if (this.mainHero == null) {
            this.mainHero = new HeroProp(this, FIELD_INDEX_MAINHERO);
        }
        return this.mainHero;
    }

    /**
     * get deputyHero
     *
     * @return deputyHero value
     */
    public HeroProp getDeputyHero() {
        if (this.deputyHero == null) {
            this.deputyHero = new HeroProp(this, FIELD_INDEX_DEPUTYHERO);
        }
        return this.deputyHero;
    }

    /**
     * get burningCalTsMs
     *
     * @return burningCalTsMs value
     */
    public long getBurningCalTsMs() {
        return this.burningCalTsMs;
    }

    /**
     * set burningCalTsMs && set marked
     *
     * @param burningCalTsMs new value
     * @return current object
     */
    public WallInfoProp setBurningCalTsMs(long burningCalTsMs) {
        if (this.burningCalTsMs != burningCalTsMs) {
            this.mark(FIELD_INDEX_BURNINGCALTSMS);
            this.burningCalTsMs = burningCalTsMs;
        }
        return this;
    }

    /**
     * inner set burningCalTsMs
     *
     * @param burningCalTsMs new value
     */
    private void innerSetBurningCalTsMs(long burningCalTsMs) {
        this.burningCalTsMs = burningCalTsMs;
    }

    /**
     * get canRepairTsMs
     *
     * @return canRepairTsMs value
     */
    public long getCanRepairTsMs() {
        return this.canRepairTsMs;
    }

    /**
     * set canRepairTsMs && set marked
     *
     * @param canRepairTsMs new value
     * @return current object
     */
    public WallInfoProp setCanRepairTsMs(long canRepairTsMs) {
        if (this.canRepairTsMs != canRepairTsMs) {
            this.mark(FIELD_INDEX_CANREPAIRTSMS);
            this.canRepairTsMs = canRepairTsMs;
        }
        return this;
    }

    /**
     * inner set canRepairTsMs
     *
     * @param canRepairTsMs new value
     */
    private void innerSetCanRepairTsMs(long canRepairTsMs) {
        this.canRepairTsMs = canRepairTsMs;
    }

    /**
     * get stateEndTsMs
     *
     * @return stateEndTsMs value
     */
    public long getStateEndTsMs() {
        return this.stateEndTsMs;
    }

    /**
     * set stateEndTsMs && set marked
     *
     * @param stateEndTsMs new value
     * @return current object
     */
    public WallInfoProp setStateEndTsMs(long stateEndTsMs) {
        if (this.stateEndTsMs != stateEndTsMs) {
            this.mark(FIELD_INDEX_STATEENDTSMS);
            this.stateEndTsMs = stateEndTsMs;
        }
        return this;
    }

    /**
     * inner set stateEndTsMs
     *
     * @param stateEndTsMs new value
     */
    private void innerSetStateEndTsMs(long stateEndTsMs) {
        this.stateEndTsMs = stateEndTsMs;
    }

    /**
     * get burnSpeed
     *
     * @return burnSpeed value
     */
    public int getBurnSpeed() {
        return this.burnSpeed;
    }

    /**
     * set burnSpeed && set marked
     *
     * @param burnSpeed new value
     * @return current object
     */
    public WallInfoProp setBurnSpeed(int burnSpeed) {
        if (this.burnSpeed != burnSpeed) {
            this.mark(FIELD_INDEX_BURNSPEED);
            this.burnSpeed = burnSpeed;
        }
        return this;
    }

    /**
     * inner set burnSpeed
     *
     * @param burnSpeed new value
     */
    private void innerSetBurnSpeed(int burnSpeed) {
        this.burnSpeed = burnSpeed;
    }

    /**
     * get towerId
     *
     * @return towerId value
     */
    public int getTowerId() {
        return this.towerId;
    }

    /**
     * set towerId && set marked
     *
     * @param towerId new value
     * @return current object
     */
    public WallInfoProp setTowerId(int towerId) {
        if (this.towerId != towerId) {
            this.mark(FIELD_INDEX_TOWERID);
            this.towerId = towerId;
        }
        return this;
    }

    /**
     * inner set towerId
     *
     * @param towerId new value
     */
    private void innerSetTowerId(int towerId) {
        this.towerId = towerId;
    }

    /**
     * get towerHpMax
     *
     * @return towerHpMax value
     */
    public int getTowerHpMax() {
        return this.towerHpMax;
    }

    /**
     * set towerHpMax && set marked
     *
     * @param towerHpMax new value
     * @return current object
     */
    public WallInfoProp setTowerHpMax(int towerHpMax) {
        if (this.towerHpMax != towerHpMax) {
            this.mark(FIELD_INDEX_TOWERHPMAX);
            this.towerHpMax = towerHpMax;
        }
        return this;
    }

    /**
     * inner set towerHpMax
     *
     * @param towerHpMax new value
     */
    private void innerSetTowerHpMax(int towerHpMax) {
        this.towerHpMax = towerHpMax;
    }

    /**
     * get towerHp
     *
     * @return towerHp value
     */
    public int getTowerHp() {
        return this.towerHp;
    }

    /**
     * set towerHp && set marked
     *
     * @param towerHp new value
     * @return current object
     */
    public WallInfoProp setTowerHp(int towerHp) {
        if (this.towerHp != towerHp) {
            this.mark(FIELD_INDEX_TOWERHP);
            this.towerHp = towerHp;
        }
        return this;
    }

    /**
     * inner set towerHp
     *
     * @param towerHp new value
     */
    private void innerSetTowerHp(int towerHp) {
        this.towerHp = towerHp;
    }

    /**
     * get towerHpCalTsMs
     *
     * @return towerHpCalTsMs value
     */
    public long getTowerHpCalTsMs() {
        return this.towerHpCalTsMs;
    }

    /**
     * set towerHpCalTsMs && set marked
     *
     * @param towerHpCalTsMs new value
     * @return current object
     */
    public WallInfoProp setTowerHpCalTsMs(long towerHpCalTsMs) {
        if (this.towerHpCalTsMs != towerHpCalTsMs) {
            this.mark(FIELD_INDEX_TOWERHPCALTSMS);
            this.towerHpCalTsMs = towerHpCalTsMs;
        }
        return this;
    }

    /**
     * inner set towerHpCalTsMs
     *
     * @param towerHpCalTsMs new value
     */
    private void innerSetTowerHpCalTsMs(long towerHpCalTsMs) {
        this.towerHpCalTsMs = towerHpCalTsMs;
    }

    /**
     * get battlePlane
     *
     * @return battlePlane value
     */
    public SceneBattlePlaneProp getBattlePlane() {
        if (this.battlePlane == null) {
            this.battlePlane = new SceneBattlePlaneProp(this, FIELD_INDEX_BATTLEPLANE);
        }
        return this.battlePlane;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WallInfoPB.Builder getCopyCsBuilder() {
        final WallInfoPB.Builder builder = WallInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(WallInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHpMax() != 0) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.getHp() != 0) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }  else if (builder.hasHp()) {
            // 清理Hp
            builder.clearHp();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            StructPB.HeroPB.Builder tmpBuilder = StructPB.HeroPB.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            StructPB.HeroPB.Builder tmpBuilder = StructPB.HeroPB.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.getBurningCalTsMs() != 0L) {
            builder.setBurningCalTsMs(this.getBurningCalTsMs());
            fieldCnt++;
        }  else if (builder.hasBurningCalTsMs()) {
            // 清理BurningCalTsMs
            builder.clearBurningCalTsMs();
            fieldCnt++;
        }
        if (this.getCanRepairTsMs() != 0L) {
            builder.setCanRepairTsMs(this.getCanRepairTsMs());
            fieldCnt++;
        }  else if (builder.hasCanRepairTsMs()) {
            // 清理CanRepairTsMs
            builder.clearCanRepairTsMs();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getBurnSpeed() != 0) {
            builder.setBurnSpeed(this.getBurnSpeed());
            fieldCnt++;
        }  else if (builder.hasBurnSpeed()) {
            // 清理BurnSpeed
            builder.clearBurnSpeed();
            fieldCnt++;
        }
        if (this.getTowerId() != 0) {
            builder.setTowerId(this.getTowerId());
            fieldCnt++;
        }  else if (builder.hasTowerId()) {
            // 清理TowerId
            builder.clearTowerId();
            fieldCnt++;
        }
        if (this.getTowerHpMax() != 0) {
            builder.setTowerHpMax(this.getTowerHpMax());
            fieldCnt++;
        }  else if (builder.hasTowerHpMax()) {
            // 清理TowerHpMax
            builder.clearTowerHpMax();
            fieldCnt++;
        }
        if (this.getTowerHp() != 0) {
            builder.setTowerHp(this.getTowerHp());
            fieldCnt++;
        }  else if (builder.hasTowerHp()) {
            // 清理TowerHp
            builder.clearTowerHp();
            fieldCnt++;
        }
        if (this.getTowerHpCalTsMs() != 0L) {
            builder.setTowerHpCalTsMs(this.getTowerHpCalTsMs());
            fieldCnt++;
        }  else if (builder.hasTowerHpCalTsMs()) {
            // 清理TowerHpCalTsMs
            builder.clearTowerHpCalTsMs();
            fieldCnt++;
        }
        if (this.battlePlane != null) {
            StructPB.SceneBattlePlanePB.Builder tmpBuilder = StructPB.SceneBattlePlanePB.newBuilder();
            final int tmpFieldCnt = this.battlePlane.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattlePlane();
            }
        }  else if (builder.hasBattlePlane()) {
            // 清理BattlePlane
            builder.clearBattlePlane();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(WallInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_BURNINGCALTSMS)) {
            builder.setBurningCalTsMs(this.getBurningCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANREPAIRTSMS)) {
            builder.setCanRepairTsMs(this.getCanRepairTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BURNSPEED)) {
            builder.setBurnSpeed(this.getBurnSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERID)) {
            builder.setTowerId(this.getTowerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPMAX)) {
            builder.setTowerHpMax(this.getTowerHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHP)) {
            builder.setTowerHp(this.getTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPCALTSMS)) {
            builder.setTowerHpCalTsMs(this.getTowerHpCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPLANE) && this.battlePlane != null) {
            final boolean needClear = !builder.hasBattlePlane();
            final int tmpFieldCnt = this.battlePlane.copyChangeToCs(builder.getBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePlane();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(WallInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToAndClearDeleteKeysCs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToAndClearDeleteKeysCs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_BURNINGCALTSMS)) {
            builder.setBurningCalTsMs(this.getBurningCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANREPAIRTSMS)) {
            builder.setCanRepairTsMs(this.getCanRepairTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BURNSPEED)) {
            builder.setBurnSpeed(this.getBurnSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERID)) {
            builder.setTowerId(this.getTowerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPMAX)) {
            builder.setTowerHpMax(this.getTowerHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHP)) {
            builder.setTowerHp(this.getTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPCALTSMS)) {
            builder.setTowerHpCalTsMs(this.getTowerHpCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPLANE) && this.battlePlane != null) {
            final boolean needClear = !builder.hasBattlePlane();
            final int tmpFieldCnt = this.battlePlane.copyChangeToAndClearDeleteKeysCs(builder.getBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePlane();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(WallInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHp()) {
            this.innerSetHp(proto.getHp());
        } else {
            this.innerSetHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromCs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromCs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromCs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromCs(proto.getDeputyHero());
            }
        }
        if (proto.hasBurningCalTsMs()) {
            this.innerSetBurningCalTsMs(proto.getBurningCalTsMs());
        } else {
            this.innerSetBurningCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCanRepairTsMs()) {
            this.innerSetCanRepairTsMs(proto.getCanRepairTsMs());
        } else {
            this.innerSetCanRepairTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBurnSpeed()) {
            this.innerSetBurnSpeed(proto.getBurnSpeed());
        } else {
            this.innerSetBurnSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerId()) {
            this.innerSetTowerId(proto.getTowerId());
        } else {
            this.innerSetTowerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHpMax()) {
            this.innerSetTowerHpMax(proto.getTowerHpMax());
        } else {
            this.innerSetTowerHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHp()) {
            this.innerSetTowerHp(proto.getTowerHp());
        } else {
            this.innerSetTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHpCalTsMs()) {
            this.innerSetTowerHpCalTsMs(proto.getTowerHpCalTsMs());
        } else {
            this.innerSetTowerHpCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBattlePlane()) {
            this.getBattlePlane().mergeFromCs(proto.getBattlePlane());
        } else {
            if (this.battlePlane != null) {
                this.battlePlane.mergeFromCs(proto.getBattlePlane());
            }
        }
        this.markAll();
        return WallInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(WallInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasHp()) {
            this.setHp(proto.getHp());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromCs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromCs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasBurningCalTsMs()) {
            this.setBurningCalTsMs(proto.getBurningCalTsMs());
            fieldCnt++;
        }
        if (proto.hasCanRepairTsMs()) {
            this.setCanRepairTsMs(proto.getCanRepairTsMs());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasBurnSpeed()) {
            this.setBurnSpeed(proto.getBurnSpeed());
            fieldCnt++;
        }
        if (proto.hasTowerId()) {
            this.setTowerId(proto.getTowerId());
            fieldCnt++;
        }
        if (proto.hasTowerHpMax()) {
            this.setTowerHpMax(proto.getTowerHpMax());
            fieldCnt++;
        }
        if (proto.hasTowerHp()) {
            this.setTowerHp(proto.getTowerHp());
            fieldCnt++;
        }
        if (proto.hasTowerHpCalTsMs()) {
            this.setTowerHpCalTsMs(proto.getTowerHpCalTsMs());
            fieldCnt++;
        }
        if (proto.hasBattlePlane()) {
            this.getBattlePlane().mergeChangeFromCs(proto.getBattlePlane());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WallInfo.Builder getCopyDbBuilder() {
        final WallInfo.Builder builder = WallInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(WallInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHpMax() != 0) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.getHp() != 0) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }  else if (builder.hasHp()) {
            // 清理Hp
            builder.clearHp();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.getBurningCalTsMs() != 0L) {
            builder.setBurningCalTsMs(this.getBurningCalTsMs());
            fieldCnt++;
        }  else if (builder.hasBurningCalTsMs()) {
            // 清理BurningCalTsMs
            builder.clearBurningCalTsMs();
            fieldCnt++;
        }
        if (this.getCanRepairTsMs() != 0L) {
            builder.setCanRepairTsMs(this.getCanRepairTsMs());
            fieldCnt++;
        }  else if (builder.hasCanRepairTsMs()) {
            // 清理CanRepairTsMs
            builder.clearCanRepairTsMs();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getBurnSpeed() != 0) {
            builder.setBurnSpeed(this.getBurnSpeed());
            fieldCnt++;
        }  else if (builder.hasBurnSpeed()) {
            // 清理BurnSpeed
            builder.clearBurnSpeed();
            fieldCnt++;
        }
        if (this.getTowerId() != 0) {
            builder.setTowerId(this.getTowerId());
            fieldCnt++;
        }  else if (builder.hasTowerId()) {
            // 清理TowerId
            builder.clearTowerId();
            fieldCnt++;
        }
        if (this.getTowerHpMax() != 0) {
            builder.setTowerHpMax(this.getTowerHpMax());
            fieldCnt++;
        }  else if (builder.hasTowerHpMax()) {
            // 清理TowerHpMax
            builder.clearTowerHpMax();
            fieldCnt++;
        }
        if (this.getTowerHp() != 0) {
            builder.setTowerHp(this.getTowerHp());
            fieldCnt++;
        }  else if (builder.hasTowerHp()) {
            // 清理TowerHp
            builder.clearTowerHp();
            fieldCnt++;
        }
        if (this.getTowerHpCalTsMs() != 0L) {
            builder.setTowerHpCalTsMs(this.getTowerHpCalTsMs());
            fieldCnt++;
        }  else if (builder.hasTowerHpCalTsMs()) {
            // 清理TowerHpCalTsMs
            builder.clearTowerHpCalTsMs();
            fieldCnt++;
        }
        if (this.battlePlane != null) {
            Struct.SceneBattlePlane.Builder tmpBuilder = Struct.SceneBattlePlane.newBuilder();
            final int tmpFieldCnt = this.battlePlane.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattlePlane();
            }
        }  else if (builder.hasBattlePlane()) {
            // 清理BattlePlane
            builder.clearBattlePlane();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(WallInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToDb(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToDb(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_BURNINGCALTSMS)) {
            builder.setBurningCalTsMs(this.getBurningCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANREPAIRTSMS)) {
            builder.setCanRepairTsMs(this.getCanRepairTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BURNSPEED)) {
            builder.setBurnSpeed(this.getBurnSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERID)) {
            builder.setTowerId(this.getTowerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPMAX)) {
            builder.setTowerHpMax(this.getTowerHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHP)) {
            builder.setTowerHp(this.getTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPCALTSMS)) {
            builder.setTowerHpCalTsMs(this.getTowerHpCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPLANE) && this.battlePlane != null) {
            final boolean needClear = !builder.hasBattlePlane();
            final int tmpFieldCnt = this.battlePlane.copyChangeToDb(builder.getBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePlane();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(WallInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHp()) {
            this.innerSetHp(proto.getHp());
        } else {
            this.innerSetHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromDb(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromDb(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromDb(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromDb(proto.getDeputyHero());
            }
        }
        if (proto.hasBurningCalTsMs()) {
            this.innerSetBurningCalTsMs(proto.getBurningCalTsMs());
        } else {
            this.innerSetBurningCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCanRepairTsMs()) {
            this.innerSetCanRepairTsMs(proto.getCanRepairTsMs());
        } else {
            this.innerSetCanRepairTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBurnSpeed()) {
            this.innerSetBurnSpeed(proto.getBurnSpeed());
        } else {
            this.innerSetBurnSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerId()) {
            this.innerSetTowerId(proto.getTowerId());
        } else {
            this.innerSetTowerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHpMax()) {
            this.innerSetTowerHpMax(proto.getTowerHpMax());
        } else {
            this.innerSetTowerHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHp()) {
            this.innerSetTowerHp(proto.getTowerHp());
        } else {
            this.innerSetTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHpCalTsMs()) {
            this.innerSetTowerHpCalTsMs(proto.getTowerHpCalTsMs());
        } else {
            this.innerSetTowerHpCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBattlePlane()) {
            this.getBattlePlane().mergeFromDb(proto.getBattlePlane());
        } else {
            if (this.battlePlane != null) {
                this.battlePlane.mergeFromDb(proto.getBattlePlane());
            }
        }
        this.markAll();
        return WallInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(WallInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasHp()) {
            this.setHp(proto.getHp());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromDb(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromDb(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasBurningCalTsMs()) {
            this.setBurningCalTsMs(proto.getBurningCalTsMs());
            fieldCnt++;
        }
        if (proto.hasCanRepairTsMs()) {
            this.setCanRepairTsMs(proto.getCanRepairTsMs());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasBurnSpeed()) {
            this.setBurnSpeed(proto.getBurnSpeed());
            fieldCnt++;
        }
        if (proto.hasTowerId()) {
            this.setTowerId(proto.getTowerId());
            fieldCnt++;
        }
        if (proto.hasTowerHpMax()) {
            this.setTowerHpMax(proto.getTowerHpMax());
            fieldCnt++;
        }
        if (proto.hasTowerHp()) {
            this.setTowerHp(proto.getTowerHp());
            fieldCnt++;
        }
        if (proto.hasTowerHpCalTsMs()) {
            this.setTowerHpCalTsMs(proto.getTowerHpCalTsMs());
            fieldCnt++;
        }
        if (proto.hasBattlePlane()) {
            this.getBattlePlane().mergeChangeFromDb(proto.getBattlePlane());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WallInfo.Builder getCopySsBuilder() {
        final WallInfo.Builder builder = WallInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(WallInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getHpMax() != 0) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }  else if (builder.hasHpMax()) {
            // 清理HpMax
            builder.clearHpMax();
            fieldCnt++;
        }
        if (this.getHp() != 0) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }  else if (builder.hasHp()) {
            // 清理Hp
            builder.clearHp();
            fieldCnt++;
        }
        if (this.mainHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.mainHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMainHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMainHero();
            }
        }  else if (builder.hasMainHero()) {
            // 清理MainHero
            builder.clearMainHero();
            fieldCnt++;
        }
        if (this.deputyHero != null) {
            Struct.Hero.Builder tmpBuilder = Struct.Hero.newBuilder();
            final int tmpFieldCnt = this.deputyHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDeputyHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDeputyHero();
            }
        }  else if (builder.hasDeputyHero()) {
            // 清理DeputyHero
            builder.clearDeputyHero();
            fieldCnt++;
        }
        if (this.getBurningCalTsMs() != 0L) {
            builder.setBurningCalTsMs(this.getBurningCalTsMs());
            fieldCnt++;
        }  else if (builder.hasBurningCalTsMs()) {
            // 清理BurningCalTsMs
            builder.clearBurningCalTsMs();
            fieldCnt++;
        }
        if (this.getCanRepairTsMs() != 0L) {
            builder.setCanRepairTsMs(this.getCanRepairTsMs());
            fieldCnt++;
        }  else if (builder.hasCanRepairTsMs()) {
            // 清理CanRepairTsMs
            builder.clearCanRepairTsMs();
            fieldCnt++;
        }
        if (this.getStateEndTsMs() != 0L) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }  else if (builder.hasStateEndTsMs()) {
            // 清理StateEndTsMs
            builder.clearStateEndTsMs();
            fieldCnt++;
        }
        if (this.getBurnSpeed() != 0) {
            builder.setBurnSpeed(this.getBurnSpeed());
            fieldCnt++;
        }  else if (builder.hasBurnSpeed()) {
            // 清理BurnSpeed
            builder.clearBurnSpeed();
            fieldCnt++;
        }
        if (this.getTowerId() != 0) {
            builder.setTowerId(this.getTowerId());
            fieldCnt++;
        }  else if (builder.hasTowerId()) {
            // 清理TowerId
            builder.clearTowerId();
            fieldCnt++;
        }
        if (this.getTowerHpMax() != 0) {
            builder.setTowerHpMax(this.getTowerHpMax());
            fieldCnt++;
        }  else if (builder.hasTowerHpMax()) {
            // 清理TowerHpMax
            builder.clearTowerHpMax();
            fieldCnt++;
        }
        if (this.getTowerHp() != 0) {
            builder.setTowerHp(this.getTowerHp());
            fieldCnt++;
        }  else if (builder.hasTowerHp()) {
            // 清理TowerHp
            builder.clearTowerHp();
            fieldCnt++;
        }
        if (this.getTowerHpCalTsMs() != 0L) {
            builder.setTowerHpCalTsMs(this.getTowerHpCalTsMs());
            fieldCnt++;
        }  else if (builder.hasTowerHpCalTsMs()) {
            // 清理TowerHpCalTsMs
            builder.clearTowerHpCalTsMs();
            fieldCnt++;
        }
        if (this.battlePlane != null) {
            Struct.SceneBattlePlane.Builder tmpBuilder = Struct.SceneBattlePlane.newBuilder();
            final int tmpFieldCnt = this.battlePlane.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBattlePlane(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBattlePlane();
            }
        }  else if (builder.hasBattlePlane()) {
            // 清理BattlePlane
            builder.clearBattlePlane();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(WallInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_HPMAX)) {
            builder.setHpMax(this.getHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_HP)) {
            builder.setHp(this.getHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            final boolean needClear = !builder.hasMainHero();
            final int tmpFieldCnt = this.mainHero.copyChangeToSs(builder.getMainHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMainHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            final boolean needClear = !builder.hasDeputyHero();
            final int tmpFieldCnt = this.deputyHero.copyChangeToSs(builder.getDeputyHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDeputyHero();
            }
        }
        if (this.hasMark(FIELD_INDEX_BURNINGCALTSMS)) {
            builder.setBurningCalTsMs(this.getBurningCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CANREPAIRTSMS)) {
            builder.setCanRepairTsMs(this.getCanRepairTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTSMS)) {
            builder.setStateEndTsMs(this.getStateEndTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BURNSPEED)) {
            builder.setBurnSpeed(this.getBurnSpeed());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERID)) {
            builder.setTowerId(this.getTowerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPMAX)) {
            builder.setTowerHpMax(this.getTowerHpMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHP)) {
            builder.setTowerHp(this.getTowerHp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOWERHPCALTSMS)) {
            builder.setTowerHpCalTsMs(this.getTowerHpCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPLANE) && this.battlePlane != null) {
            final boolean needClear = !builder.hasBattlePlane();
            final int tmpFieldCnt = this.battlePlane.copyChangeToSs(builder.getBattlePlaneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBattlePlane();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(WallInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasHpMax()) {
            this.innerSetHpMax(proto.getHpMax());
        } else {
            this.innerSetHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasHp()) {
            this.innerSetHp(proto.getHp());
        } else {
            this.innerSetHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeFromSs(proto.getMainHero());
        } else {
            if (this.mainHero != null) {
                this.mainHero.mergeFromSs(proto.getMainHero());
            }
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeFromSs(proto.getDeputyHero());
        } else {
            if (this.deputyHero != null) {
                this.deputyHero.mergeFromSs(proto.getDeputyHero());
            }
        }
        if (proto.hasBurningCalTsMs()) {
            this.innerSetBurningCalTsMs(proto.getBurningCalTsMs());
        } else {
            this.innerSetBurningCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCanRepairTsMs()) {
            this.innerSetCanRepairTsMs(proto.getCanRepairTsMs());
        } else {
            this.innerSetCanRepairTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTsMs()) {
            this.innerSetStateEndTsMs(proto.getStateEndTsMs());
        } else {
            this.innerSetStateEndTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBurnSpeed()) {
            this.innerSetBurnSpeed(proto.getBurnSpeed());
        } else {
            this.innerSetBurnSpeed(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerId()) {
            this.innerSetTowerId(proto.getTowerId());
        } else {
            this.innerSetTowerId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHpMax()) {
            this.innerSetTowerHpMax(proto.getTowerHpMax());
        } else {
            this.innerSetTowerHpMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHp()) {
            this.innerSetTowerHp(proto.getTowerHp());
        } else {
            this.innerSetTowerHp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTowerHpCalTsMs()) {
            this.innerSetTowerHpCalTsMs(proto.getTowerHpCalTsMs());
        } else {
            this.innerSetTowerHpCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBattlePlane()) {
            this.getBattlePlane().mergeFromSs(proto.getBattlePlane());
        } else {
            if (this.battlePlane != null) {
                this.battlePlane.mergeFromSs(proto.getBattlePlane());
            }
        }
        this.markAll();
        return WallInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(WallInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasHpMax()) {
            this.setHpMax(proto.getHpMax());
            fieldCnt++;
        }
        if (proto.hasHp()) {
            this.setHp(proto.getHp());
            fieldCnt++;
        }
        if (proto.hasMainHero()) {
            this.getMainHero().mergeChangeFromSs(proto.getMainHero());
            fieldCnt++;
        }
        if (proto.hasDeputyHero()) {
            this.getDeputyHero().mergeChangeFromSs(proto.getDeputyHero());
            fieldCnt++;
        }
        if (proto.hasBurningCalTsMs()) {
            this.setBurningCalTsMs(proto.getBurningCalTsMs());
            fieldCnt++;
        }
        if (proto.hasCanRepairTsMs()) {
            this.setCanRepairTsMs(proto.getCanRepairTsMs());
            fieldCnt++;
        }
        if (proto.hasStateEndTsMs()) {
            this.setStateEndTsMs(proto.getStateEndTsMs());
            fieldCnt++;
        }
        if (proto.hasBurnSpeed()) {
            this.setBurnSpeed(proto.getBurnSpeed());
            fieldCnt++;
        }
        if (proto.hasTowerId()) {
            this.setTowerId(proto.getTowerId());
            fieldCnt++;
        }
        if (proto.hasTowerHpMax()) {
            this.setTowerHpMax(proto.getTowerHpMax());
            fieldCnt++;
        }
        if (proto.hasTowerHp()) {
            this.setTowerHp(proto.getTowerHp());
            fieldCnt++;
        }
        if (proto.hasTowerHpCalTsMs()) {
            this.setTowerHpCalTsMs(proto.getTowerHpCalTsMs());
            fieldCnt++;
        }
        if (proto.hasBattlePlane()) {
            this.getBattlePlane().mergeChangeFromSs(proto.getBattlePlane());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        WallInfo.Builder builder = WallInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MAINHERO) && this.mainHero != null) {
            this.mainHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEPUTYHERO) && this.deputyHero != null) {
            this.deputyHero.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BATTLEPLANE) && this.battlePlane != null) {
            this.battlePlane.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.mainHero != null) {
            this.mainHero.markAll();
        }
        if (this.deputyHero != null) {
            this.deputyHero.markAll();
        }
        if (this.battlePlane != null) {
            this.battlePlane.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof WallInfoProp)) {
            return false;
        }
        final WallInfoProp otherNode = (WallInfoProp) node;
        if (this.hpMax != otherNode.hpMax) {
            return false;
        }
        if (this.hp != otherNode.hp) {
            return false;
        }
        if (!this.getMainHero().compareDataTo(otherNode.getMainHero())) {
            return false;
        }
        if (!this.getDeputyHero().compareDataTo(otherNode.getDeputyHero())) {
            return false;
        }
        if (this.burningCalTsMs != otherNode.burningCalTsMs) {
            return false;
        }
        if (this.canRepairTsMs != otherNode.canRepairTsMs) {
            return false;
        }
        if (this.stateEndTsMs != otherNode.stateEndTsMs) {
            return false;
        }
        if (this.burnSpeed != otherNode.burnSpeed) {
            return false;
        }
        if (this.towerId != otherNode.towerId) {
            return false;
        }
        if (this.towerHpMax != otherNode.towerHpMax) {
            return false;
        }
        if (this.towerHp != otherNode.towerHp) {
            return false;
        }
        if (this.towerHpCalTsMs != otherNode.towerHpCalTsMs) {
            return false;
        }
        if (!this.getBattlePlane().compareDataTo(otherNode.getBattlePlane())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 51;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
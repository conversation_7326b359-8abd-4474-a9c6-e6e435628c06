package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.ScenePlayerSoldier;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayerPB.ScenePlayerSoldierPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ScenePlayerSoldierProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SOLDIERMAP = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32SoldierMapProp soldierMap = null;

    public ScenePlayerSoldierProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ScenePlayerSoldierProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get soldierMap
     *
     * @return soldierMap value
     */
    public Int32SoldierMapProp getSoldierMap() {
        if (this.soldierMap == null) {
            this.soldierMap = new Int32SoldierMapProp(this, FIELD_INDEX_SOLDIERMAP);
        }
        return this.soldierMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSoldierMapV(SoldierProp v) {
        this.getSoldierMap().put(v.getSoldierId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SoldierProp addEmptySoldierMap(Integer k) {
        return this.getSoldierMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSoldierMapSize() {
        if (this.soldierMap == null) {
            return 0;
        }
        return this.soldierMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSoldierMapEmpty() {
        if (this.soldierMap == null) {
            return true;
        }
        return this.soldierMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SoldierProp getSoldierMapV(Integer k) {
        if (this.soldierMap == null || !this.soldierMap.containsKey(k)) {
            return null;
        }
        return this.soldierMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSoldierMap() {
        if (this.soldierMap != null) {
            this.soldierMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSoldierMapV(Integer k) {
        if (this.soldierMap != null) {
            this.soldierMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerSoldierPB.Builder getCopyCsBuilder() {
        final ScenePlayerSoldierPB.Builder builder = ScenePlayerSoldierPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ScenePlayerSoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.soldierMap != null) {
            StructPB.Int32SoldierMapPB.Builder tmpBuilder = StructPB.Int32SoldierMapPB.newBuilder();
            final int tmpFieldCnt = this.soldierMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierMap();
            }
        }  else if (builder.hasSoldierMap()) {
            // 清理SoldierMap
            builder.clearSoldierMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ScenePlayerSoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERMAP) && this.soldierMap != null) {
            final boolean needClear = !builder.hasSoldierMap();
            final int tmpFieldCnt = this.soldierMap.copyChangeToCs(builder.getSoldierMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ScenePlayerSoldierPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERMAP) && this.soldierMap != null) {
            final boolean needClear = !builder.hasSoldierMap();
            final int tmpFieldCnt = this.soldierMap.copyChangeToAndClearDeleteKeysCs(builder.getSoldierMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ScenePlayerSoldierPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierMap()) {
            this.getSoldierMap().mergeFromCs(proto.getSoldierMap());
        } else {
            if (this.soldierMap != null) {
                this.soldierMap.mergeFromCs(proto.getSoldierMap());
            }
        }
        this.markAll();
        return ScenePlayerSoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ScenePlayerSoldierPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierMap()) {
            this.getSoldierMap().mergeChangeFromCs(proto.getSoldierMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerSoldier.Builder getCopyDbBuilder() {
        final ScenePlayerSoldier.Builder builder = ScenePlayerSoldier.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ScenePlayerSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.soldierMap != null) {
            Struct.Int32SoldierMap.Builder tmpBuilder = Struct.Int32SoldierMap.newBuilder();
            final int tmpFieldCnt = this.soldierMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierMap();
            }
        }  else if (builder.hasSoldierMap()) {
            // 清理SoldierMap
            builder.clearSoldierMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ScenePlayerSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERMAP) && this.soldierMap != null) {
            final boolean needClear = !builder.hasSoldierMap();
            final int tmpFieldCnt = this.soldierMap.copyChangeToDb(builder.getSoldierMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ScenePlayerSoldier proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierMap()) {
            this.getSoldierMap().mergeFromDb(proto.getSoldierMap());
        } else {
            if (this.soldierMap != null) {
                this.soldierMap.mergeFromDb(proto.getSoldierMap());
            }
        }
        this.markAll();
        return ScenePlayerSoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ScenePlayerSoldier proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierMap()) {
            this.getSoldierMap().mergeChangeFromDb(proto.getSoldierMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ScenePlayerSoldier.Builder getCopySsBuilder() {
        final ScenePlayerSoldier.Builder builder = ScenePlayerSoldier.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ScenePlayerSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.soldierMap != null) {
            Struct.Int32SoldierMap.Builder tmpBuilder = Struct.Int32SoldierMap.newBuilder();
            final int tmpFieldCnt = this.soldierMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSoldierMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSoldierMap();
            }
        }  else if (builder.hasSoldierMap()) {
            // 清理SoldierMap
            builder.clearSoldierMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ScenePlayerSoldier.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SOLDIERMAP) && this.soldierMap != null) {
            final boolean needClear = !builder.hasSoldierMap();
            final int tmpFieldCnt = this.soldierMap.copyChangeToSs(builder.getSoldierMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSoldierMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ScenePlayerSoldier proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSoldierMap()) {
            this.getSoldierMap().mergeFromSs(proto.getSoldierMap());
        } else {
            if (this.soldierMap != null) {
                this.soldierMap.mergeFromSs(proto.getSoldierMap());
            }
        }
        this.markAll();
        return ScenePlayerSoldierProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ScenePlayerSoldier proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSoldierMap()) {
            this.getSoldierMap().mergeChangeFromSs(proto.getSoldierMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ScenePlayerSoldier.Builder builder = ScenePlayerSoldier.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SOLDIERMAP) && this.soldierMap != null) {
            this.soldierMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.soldierMap != null) {
            this.soldierMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ScenePlayerSoldierProp)) {
            return false;
        }
        final ScenePlayerSoldierProp otherNode = (ScenePlayerSoldierProp) node;
        if (!this.getSoldierMap().compareDataTo(otherNode.getSoldierMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityContinuesUnit;
import com.yorha.proto.Basic;
import com.yorha.proto.StructPB.ActivityContinuesUnitPB;
import com.yorha.proto.BasicPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityContinuesUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_UNLOCKPAYPOOL = 0;
    public static final int FIELD_INDEX_SCORE = 1;
    public static final int FIELD_INDEX_UNLOCKLEVEL = 2;
    public static final int FIELD_INDEX_FREEREWARDEDLEVEL = 3;
    public static final int FIELD_INDEX_PAYREWARDEDLEVEL = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private boolean unlockPayPool = Constant.DEFAULT_BOOLEAN_VALUE;
    private int score = Constant.DEFAULT_INT_VALUE;
    private int unlockLevel = Constant.DEFAULT_INT_VALUE;
    private Int32SetProp freeRewardedLevel = null;
    private Int32SetProp payRewardedLevel = null;

    public ActivityContinuesUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityContinuesUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get unlockPayPool
     *
     * @return unlockPayPool value
     */
    public boolean getUnlockPayPool() {
        return this.unlockPayPool;
    }

    /**
     * set unlockPayPool && set marked
     *
     * @param unlockPayPool new value
     * @return current object
     */
    public ActivityContinuesUnitProp setUnlockPayPool(boolean unlockPayPool) {
        if (this.unlockPayPool != unlockPayPool) {
            this.mark(FIELD_INDEX_UNLOCKPAYPOOL);
            this.unlockPayPool = unlockPayPool;
        }
        return this;
    }

    /**
     * inner set unlockPayPool
     *
     * @param unlockPayPool new value
     */
    private void innerSetUnlockPayPool(boolean unlockPayPool) {
        this.unlockPayPool = unlockPayPool;
    }

    /**
     * get score
     *
     * @return score value
     */
    public int getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public ActivityContinuesUnitProp setScore(int score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(int score) {
        this.score = score;
    }

    /**
     * get unlockLevel
     *
     * @return unlockLevel value
     */
    public int getUnlockLevel() {
        return this.unlockLevel;
    }

    /**
     * set unlockLevel && set marked
     *
     * @param unlockLevel new value
     * @return current object
     */
    public ActivityContinuesUnitProp setUnlockLevel(int unlockLevel) {
        if (this.unlockLevel != unlockLevel) {
            this.mark(FIELD_INDEX_UNLOCKLEVEL);
            this.unlockLevel = unlockLevel;
        }
        return this;
    }

    /**
     * inner set unlockLevel
     *
     * @param unlockLevel new value
     */
    private void innerSetUnlockLevel(int unlockLevel) {
        this.unlockLevel = unlockLevel;
    }

    /**
     * get freeRewardedLevel
     *
     * @return freeRewardedLevel value
     */
    public Int32SetProp getFreeRewardedLevel() {
        if (this.freeRewardedLevel == null) {
            this.freeRewardedLevel = new Int32SetProp(this, FIELD_INDEX_FREEREWARDEDLEVEL);
        }
        return this.freeRewardedLevel;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addFreeRewardedLevel(Integer e) {
        this.getFreeRewardedLevel().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeFreeRewardedLevel(Integer e) {
        if (this.freeRewardedLevel == null) {
            return null;
        }
        if(this.freeRewardedLevel.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getFreeRewardedLevelSize() {
        if (this.freeRewardedLevel == null) {
            return 0;
        }
        return this.freeRewardedLevel.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isFreeRewardedLevelEmpty() {
        if (this.freeRewardedLevel == null) {
            return true;
        }
        return this.getFreeRewardedLevel().isEmpty();
    }

    /**
     * clear set
     */
    public void clearFreeRewardedLevel() {
        this.getFreeRewardedLevel().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isFreeRewardedLevelContains(Integer e) {
        return this.freeRewardedLevel != null && this.freeRewardedLevel.contains(e);
    }

    /**
     * get payRewardedLevel
     *
     * @return payRewardedLevel value
     */
    public Int32SetProp getPayRewardedLevel() {
        if (this.payRewardedLevel == null) {
            this.payRewardedLevel = new Int32SetProp(this, FIELD_INDEX_PAYREWARDEDLEVEL);
        }
        return this.payRewardedLevel;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addPayRewardedLevel(Integer e) {
        this.getPayRewardedLevel().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removePayRewardedLevel(Integer e) {
        if (this.payRewardedLevel == null) {
            return null;
        }
        if(this.payRewardedLevel.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getPayRewardedLevelSize() {
        if (this.payRewardedLevel == null) {
            return 0;
        }
        return this.payRewardedLevel.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isPayRewardedLevelEmpty() {
        if (this.payRewardedLevel == null) {
            return true;
        }
        return this.getPayRewardedLevel().isEmpty();
    }

    /**
     * clear set
     */
    public void clearPayRewardedLevel() {
        this.getPayRewardedLevel().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isPayRewardedLevelContains(Integer e) {
        return this.payRewardedLevel != null && this.payRewardedLevel.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityContinuesUnitPB.Builder getCopyCsBuilder() {
        final ActivityContinuesUnitPB.Builder builder = ActivityContinuesUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityContinuesUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnlockPayPool()) {
            builder.setUnlockPayPool(this.getUnlockPayPool());
            fieldCnt++;
        }  else if (builder.hasUnlockPayPool()) {
            // 清理UnlockPayPool
            builder.clearUnlockPayPool();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getUnlockLevel() != 0) {
            builder.setUnlockLevel(this.getUnlockLevel());
            fieldCnt++;
        }  else if (builder.hasUnlockLevel()) {
            // 清理UnlockLevel
            builder.clearUnlockLevel();
            fieldCnt++;
        }
        if (this.freeRewardedLevel != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.freeRewardedLevel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFreeRewardedLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFreeRewardedLevel();
            }
        }  else if (builder.hasFreeRewardedLevel()) {
            // 清理FreeRewardedLevel
            builder.clearFreeRewardedLevel();
            fieldCnt++;
        }
        if (this.payRewardedLevel != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.payRewardedLevel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPayRewardedLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPayRewardedLevel();
            }
        }  else if (builder.hasPayRewardedLevel()) {
            // 清理PayRewardedLevel
            builder.clearPayRewardedLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityContinuesUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKPAYPOOL)) {
            builder.setUnlockPayPool(this.getUnlockPayPool());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKLEVEL)) {
            builder.setUnlockLevel(this.getUnlockLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREEREWARDEDLEVEL) && this.freeRewardedLevel != null) {
            final boolean needClear = !builder.hasFreeRewardedLevel();
            final int tmpFieldCnt = this.freeRewardedLevel.copyChangeToCs(builder.getFreeRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFreeRewardedLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYREWARDEDLEVEL) && this.payRewardedLevel != null) {
            final boolean needClear = !builder.hasPayRewardedLevel();
            final int tmpFieldCnt = this.payRewardedLevel.copyChangeToCs(builder.getPayRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPayRewardedLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityContinuesUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKPAYPOOL)) {
            builder.setUnlockPayPool(this.getUnlockPayPool());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKLEVEL)) {
            builder.setUnlockLevel(this.getUnlockLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREEREWARDEDLEVEL) && this.freeRewardedLevel != null) {
            final boolean needClear = !builder.hasFreeRewardedLevel();
            final int tmpFieldCnt = this.freeRewardedLevel.copyChangeToAndClearDeleteKeysCs(builder.getFreeRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFreeRewardedLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYREWARDEDLEVEL) && this.payRewardedLevel != null) {
            final boolean needClear = !builder.hasPayRewardedLevel();
            final int tmpFieldCnt = this.payRewardedLevel.copyChangeToAndClearDeleteKeysCs(builder.getPayRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPayRewardedLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityContinuesUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockPayPool()) {
            this.innerSetUnlockPayPool(proto.getUnlockPayPool());
        } else {
            this.innerSetUnlockPayPool(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnlockLevel()) {
            this.innerSetUnlockLevel(proto.getUnlockLevel());
        } else {
            this.innerSetUnlockLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeRewardedLevel()) {
            this.getFreeRewardedLevel().mergeFromCs(proto.getFreeRewardedLevel());
        } else {
            if (this.freeRewardedLevel != null) {
                this.freeRewardedLevel.mergeFromCs(proto.getFreeRewardedLevel());
            }
        }
        if (proto.hasPayRewardedLevel()) {
            this.getPayRewardedLevel().mergeFromCs(proto.getPayRewardedLevel());
        } else {
            if (this.payRewardedLevel != null) {
                this.payRewardedLevel.mergeFromCs(proto.getPayRewardedLevel());
            }
        }
        this.markAll();
        return ActivityContinuesUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityContinuesUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockPayPool()) {
            this.setUnlockPayPool(proto.getUnlockPayPool());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasUnlockLevel()) {
            this.setUnlockLevel(proto.getUnlockLevel());
            fieldCnt++;
        }
        if (proto.hasFreeRewardedLevel()) {
            this.getFreeRewardedLevel().mergeChangeFromCs(proto.getFreeRewardedLevel());
            fieldCnt++;
        }
        if (proto.hasPayRewardedLevel()) {
            this.getPayRewardedLevel().mergeChangeFromCs(proto.getPayRewardedLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityContinuesUnit.Builder getCopyDbBuilder() {
        final ActivityContinuesUnit.Builder builder = ActivityContinuesUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityContinuesUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnlockPayPool()) {
            builder.setUnlockPayPool(this.getUnlockPayPool());
            fieldCnt++;
        }  else if (builder.hasUnlockPayPool()) {
            // 清理UnlockPayPool
            builder.clearUnlockPayPool();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getUnlockLevel() != 0) {
            builder.setUnlockLevel(this.getUnlockLevel());
            fieldCnt++;
        }  else if (builder.hasUnlockLevel()) {
            // 清理UnlockLevel
            builder.clearUnlockLevel();
            fieldCnt++;
        }
        if (this.freeRewardedLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.freeRewardedLevel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFreeRewardedLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFreeRewardedLevel();
            }
        }  else if (builder.hasFreeRewardedLevel()) {
            // 清理FreeRewardedLevel
            builder.clearFreeRewardedLevel();
            fieldCnt++;
        }
        if (this.payRewardedLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.payRewardedLevel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPayRewardedLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPayRewardedLevel();
            }
        }  else if (builder.hasPayRewardedLevel()) {
            // 清理PayRewardedLevel
            builder.clearPayRewardedLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityContinuesUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKPAYPOOL)) {
            builder.setUnlockPayPool(this.getUnlockPayPool());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKLEVEL)) {
            builder.setUnlockLevel(this.getUnlockLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREEREWARDEDLEVEL) && this.freeRewardedLevel != null) {
            final boolean needClear = !builder.hasFreeRewardedLevel();
            final int tmpFieldCnt = this.freeRewardedLevel.copyChangeToDb(builder.getFreeRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFreeRewardedLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYREWARDEDLEVEL) && this.payRewardedLevel != null) {
            final boolean needClear = !builder.hasPayRewardedLevel();
            final int tmpFieldCnt = this.payRewardedLevel.copyChangeToDb(builder.getPayRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPayRewardedLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityContinuesUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockPayPool()) {
            this.innerSetUnlockPayPool(proto.getUnlockPayPool());
        } else {
            this.innerSetUnlockPayPool(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnlockLevel()) {
            this.innerSetUnlockLevel(proto.getUnlockLevel());
        } else {
            this.innerSetUnlockLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeRewardedLevel()) {
            this.getFreeRewardedLevel().mergeFromDb(proto.getFreeRewardedLevel());
        } else {
            if (this.freeRewardedLevel != null) {
                this.freeRewardedLevel.mergeFromDb(proto.getFreeRewardedLevel());
            }
        }
        if (proto.hasPayRewardedLevel()) {
            this.getPayRewardedLevel().mergeFromDb(proto.getPayRewardedLevel());
        } else {
            if (this.payRewardedLevel != null) {
                this.payRewardedLevel.mergeFromDb(proto.getPayRewardedLevel());
            }
        }
        this.markAll();
        return ActivityContinuesUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityContinuesUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockPayPool()) {
            this.setUnlockPayPool(proto.getUnlockPayPool());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasUnlockLevel()) {
            this.setUnlockLevel(proto.getUnlockLevel());
            fieldCnt++;
        }
        if (proto.hasFreeRewardedLevel()) {
            this.getFreeRewardedLevel().mergeChangeFromDb(proto.getFreeRewardedLevel());
            fieldCnt++;
        }
        if (proto.hasPayRewardedLevel()) {
            this.getPayRewardedLevel().mergeChangeFromDb(proto.getPayRewardedLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityContinuesUnit.Builder getCopySsBuilder() {
        final ActivityContinuesUnit.Builder builder = ActivityContinuesUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityContinuesUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnlockPayPool()) {
            builder.setUnlockPayPool(this.getUnlockPayPool());
            fieldCnt++;
        }  else if (builder.hasUnlockPayPool()) {
            // 清理UnlockPayPool
            builder.clearUnlockPayPool();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getUnlockLevel() != 0) {
            builder.setUnlockLevel(this.getUnlockLevel());
            fieldCnt++;
        }  else if (builder.hasUnlockLevel()) {
            // 清理UnlockLevel
            builder.clearUnlockLevel();
            fieldCnt++;
        }
        if (this.freeRewardedLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.freeRewardedLevel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFreeRewardedLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFreeRewardedLevel();
            }
        }  else if (builder.hasFreeRewardedLevel()) {
            // 清理FreeRewardedLevel
            builder.clearFreeRewardedLevel();
            fieldCnt++;
        }
        if (this.payRewardedLevel != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.payRewardedLevel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPayRewardedLevel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPayRewardedLevel();
            }
        }  else if (builder.hasPayRewardedLevel()) {
            // 清理PayRewardedLevel
            builder.clearPayRewardedLevel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityContinuesUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNLOCKPAYPOOL)) {
            builder.setUnlockPayPool(this.getUnlockPayPool());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNLOCKLEVEL)) {
            builder.setUnlockLevel(this.getUnlockLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FREEREWARDEDLEVEL) && this.freeRewardedLevel != null) {
            final boolean needClear = !builder.hasFreeRewardedLevel();
            final int tmpFieldCnt = this.freeRewardedLevel.copyChangeToSs(builder.getFreeRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFreeRewardedLevel();
            }
        }
        if (this.hasMark(FIELD_INDEX_PAYREWARDEDLEVEL) && this.payRewardedLevel != null) {
            final boolean needClear = !builder.hasPayRewardedLevel();
            final int tmpFieldCnt = this.payRewardedLevel.copyChangeToSs(builder.getPayRewardedLevelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPayRewardedLevel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityContinuesUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnlockPayPool()) {
            this.innerSetUnlockPayPool(proto.getUnlockPayPool());
        } else {
            this.innerSetUnlockPayPool(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnlockLevel()) {
            this.innerSetUnlockLevel(proto.getUnlockLevel());
        } else {
            this.innerSetUnlockLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFreeRewardedLevel()) {
            this.getFreeRewardedLevel().mergeFromSs(proto.getFreeRewardedLevel());
        } else {
            if (this.freeRewardedLevel != null) {
                this.freeRewardedLevel.mergeFromSs(proto.getFreeRewardedLevel());
            }
        }
        if (proto.hasPayRewardedLevel()) {
            this.getPayRewardedLevel().mergeFromSs(proto.getPayRewardedLevel());
        } else {
            if (this.payRewardedLevel != null) {
                this.payRewardedLevel.mergeFromSs(proto.getPayRewardedLevel());
            }
        }
        this.markAll();
        return ActivityContinuesUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityContinuesUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnlockPayPool()) {
            this.setUnlockPayPool(proto.getUnlockPayPool());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasUnlockLevel()) {
            this.setUnlockLevel(proto.getUnlockLevel());
            fieldCnt++;
        }
        if (proto.hasFreeRewardedLevel()) {
            this.getFreeRewardedLevel().mergeChangeFromSs(proto.getFreeRewardedLevel());
            fieldCnt++;
        }
        if (proto.hasPayRewardedLevel()) {
            this.getPayRewardedLevel().mergeChangeFromSs(proto.getPayRewardedLevel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityContinuesUnit.Builder builder = ActivityContinuesUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_FREEREWARDEDLEVEL) && this.freeRewardedLevel != null) {
            this.freeRewardedLevel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_PAYREWARDEDLEVEL) && this.payRewardedLevel != null) {
            this.payRewardedLevel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.freeRewardedLevel != null) {
            this.freeRewardedLevel.markAll();
        }
        if (this.payRewardedLevel != null) {
            this.payRewardedLevel.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityContinuesUnitProp)) {
            return false;
        }
        final ActivityContinuesUnitProp otherNode = (ActivityContinuesUnitProp) node;
        if (this.unlockPayPool != otherNode.unlockPayPool) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        if (this.unlockLevel != otherNode.unlockLevel) {
            return false;
        }
        if (!this.getFreeRewardedLevel().compareDataTo(otherNode.getFreeRewardedLevel())) {
            return false;
        }
        if (!this.getPayRewardedLevel().compareDataTo(otherNode.getPayRewardedLevel())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
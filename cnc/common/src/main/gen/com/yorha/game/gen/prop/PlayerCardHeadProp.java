package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerCardHead;
import com.yorha.proto.StructPB.PlayerCardHeadPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerCardHeadProp extends AbstractPropNode {

    public static final int FIELD_INDEX_NAME = 0;
    public static final int FIELD_INDEX_PIC = 1;
    public static final int FIELD_INDEX_PICFRAME = 2;
    public static final int FIELD_INDEX_PICURL = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private String name = Constant.DEFAULT_STR_VALUE;
    private int pic = Constant.DEFAULT_INT_VALUE;
    private int picFrame = Constant.DEFAULT_INT_VALUE;
    private String picUrl = Constant.DEFAULT_STR_VALUE;

    public PlayerCardHeadProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerCardHeadProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get name
     *
     * @return name value
     */
    public String getName() {
        return this.name;
    }

    /**
     * set name && set marked
     *
     * @param name new value
     * @return current object
     */
    public PlayerCardHeadProp setName(String name) {
        if (name == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, name)) {
            this.mark(FIELD_INDEX_NAME);
            this.name = name;
        }
        return this;
    }

    /**
     * inner set name
     *
     * @param name new value
     */
    private void innerSetName(String name) {
        this.name = name;
    }

    /**
     * get pic
     *
     * @return pic value
     */
    public int getPic() {
        return this.pic;
    }

    /**
     * set pic && set marked
     *
     * @param pic new value
     * @return current object
     */
    public PlayerCardHeadProp setPic(int pic) {
        if (this.pic != pic) {
            this.mark(FIELD_INDEX_PIC);
            this.pic = pic;
        }
        return this;
    }

    /**
     * inner set pic
     *
     * @param pic new value
     */
    private void innerSetPic(int pic) {
        this.pic = pic;
    }

    /**
     * get picFrame
     *
     * @return picFrame value
     */
    public int getPicFrame() {
        return this.picFrame;
    }

    /**
     * set picFrame && set marked
     *
     * @param picFrame new value
     * @return current object
     */
    public PlayerCardHeadProp setPicFrame(int picFrame) {
        if (this.picFrame != picFrame) {
            this.mark(FIELD_INDEX_PICFRAME);
            this.picFrame = picFrame;
        }
        return this;
    }

    /**
     * inner set picFrame
     *
     * @param picFrame new value
     */
    private void innerSetPicFrame(int picFrame) {
        this.picFrame = picFrame;
    }

    /**
     * get picUrl
     *
     * @return picUrl value
     */
    public String getPicUrl() {
        return this.picUrl;
    }

    /**
     * set picUrl && set marked
     *
     * @param picUrl new value
     * @return current object
     */
    public PlayerCardHeadProp setPicUrl(String picUrl) {
        if (picUrl == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.picUrl, picUrl)) {
            this.mark(FIELD_INDEX_PICURL);
            this.picUrl = picUrl;
        }
        return this;
    }

    /**
     * inner set picUrl
     *
     * @param picUrl new value
     */
    private void innerSetPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCardHeadPB.Builder getCopyCsBuilder() {
        final PlayerCardHeadPB.Builder builder = PlayerCardHeadPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerCardHeadPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getPic() != 0) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }  else if (builder.hasPic()) {
            // 清理Pic
            builder.clearPic();
            fieldCnt++;
        }
        if (this.getPicFrame() != 0) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }  else if (builder.hasPicFrame()) {
            // 清理PicFrame
            builder.clearPicFrame();
            fieldCnt++;
        }
        if (!this.getPicUrl().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }  else if (builder.hasPicUrl()) {
            // 清理PicUrl
            builder.clearPicUrl();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerCardHeadPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerCardHeadPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerCardHeadPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPic()) {
            this.innerSetPic(proto.getPic());
        } else {
            this.innerSetPic(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicFrame()) {
            this.innerSetPicFrame(proto.getPicFrame());
        } else {
            this.innerSetPicFrame(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicUrl()) {
            this.innerSetPicUrl(proto.getPicUrl());
        } else {
            this.innerSetPicUrl(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return PlayerCardHeadProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerCardHeadPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasPic()) {
            this.setPic(proto.getPic());
            fieldCnt++;
        }
        if (proto.hasPicFrame()) {
            this.setPicFrame(proto.getPicFrame());
            fieldCnt++;
        }
        if (proto.hasPicUrl()) {
            this.setPicUrl(proto.getPicUrl());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCardHead.Builder getCopyDbBuilder() {
        final PlayerCardHead.Builder builder = PlayerCardHead.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerCardHead.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getPic() != 0) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }  else if (builder.hasPic()) {
            // 清理Pic
            builder.clearPic();
            fieldCnt++;
        }
        if (this.getPicFrame() != 0) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }  else if (builder.hasPicFrame()) {
            // 清理PicFrame
            builder.clearPicFrame();
            fieldCnt++;
        }
        if (!this.getPicUrl().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }  else if (builder.hasPicUrl()) {
            // 清理PicUrl
            builder.clearPicUrl();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerCardHead.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerCardHead proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPic()) {
            this.innerSetPic(proto.getPic());
        } else {
            this.innerSetPic(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicFrame()) {
            this.innerSetPicFrame(proto.getPicFrame());
        } else {
            this.innerSetPicFrame(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicUrl()) {
            this.innerSetPicUrl(proto.getPicUrl());
        } else {
            this.innerSetPicUrl(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return PlayerCardHeadProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerCardHead proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasPic()) {
            this.setPic(proto.getPic());
            fieldCnt++;
        }
        if (proto.hasPicFrame()) {
            this.setPicFrame(proto.getPicFrame());
            fieldCnt++;
        }
        if (proto.hasPicUrl()) {
            this.setPicUrl(proto.getPicUrl());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerCardHead.Builder getCopySsBuilder() {
        final PlayerCardHead.Builder builder = PlayerCardHead.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerCardHead.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (this.getPic() != 0) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }  else if (builder.hasPic()) {
            // 清理Pic
            builder.clearPic();
            fieldCnt++;
        }
        if (this.getPicFrame() != 0) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }  else if (builder.hasPicFrame()) {
            // 清理PicFrame
            builder.clearPicFrame();
            fieldCnt++;
        }
        if (!this.getPicUrl().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }  else if (builder.hasPicUrl()) {
            // 清理PicUrl
            builder.clearPicUrl();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerCardHead.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PIC)) {
            builder.setPic(this.getPic());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICFRAME)) {
            builder.setPicFrame(this.getPicFrame());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PICURL)) {
            builder.setPicUrl(this.getPicUrl());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerCardHead proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPic()) {
            this.innerSetPic(proto.getPic());
        } else {
            this.innerSetPic(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicFrame()) {
            this.innerSetPicFrame(proto.getPicFrame());
        } else {
            this.innerSetPicFrame(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPicUrl()) {
            this.innerSetPicUrl(proto.getPicUrl());
        } else {
            this.innerSetPicUrl(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return PlayerCardHeadProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerCardHead proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasPic()) {
            this.setPic(proto.getPic());
            fieldCnt++;
        }
        if (proto.hasPicFrame()) {
            this.setPicFrame(proto.getPicFrame());
            fieldCnt++;
        }
        if (proto.hasPicUrl()) {
            this.setPicUrl(proto.getPicUrl());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerCardHead.Builder builder = PlayerCardHead.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerCardHeadProp)) {
            return false;
        }
        final PlayerCardHeadProp otherNode = (PlayerCardHeadProp) node;
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, otherNode.name)) {
            return false;
        }
        if (this.pic != otherNode.pic) {
            return false;
        }
        if (this.picFrame != otherNode.picFrame) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.picUrl, otherNode.picUrl)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
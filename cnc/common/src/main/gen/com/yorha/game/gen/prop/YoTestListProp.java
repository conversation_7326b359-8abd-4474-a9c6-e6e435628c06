package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.YoTest.YoTestList;
import com.yorha.proto.YoTestPB.YoTestListPB;


/**
 * <AUTHOR> auto gen
 */
public class YoTestListProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TESTLISTINTFIELD = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private int testListIntField = Constant.DEFAULT_INT_VALUE;

    public YoTestListProp() {
        super(null, 0, FIELD_COUNT);
    }

    public YoTestListProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get testListIntField
     *
     * @return testListIntField value
     */
    public int getTestListIntField() {
        return this.testListIntField;
    }

    /**
     * set testListIntField && set marked
     *
     * @param testListIntField new value
     * @return current object
     */
    public YoTestListProp setTestListIntField(int testListIntField) {
        if (this.testListIntField != testListIntField) {
            this.mark(FIELD_INDEX_TESTLISTINTFIELD);
            this.testListIntField = testListIntField;
        }
        return this;
    }

    /**
     * inner set testListIntField
     *
     * @param testListIntField new value
     */
    private void innerSetTestListIntField(int testListIntField) {
        this.testListIntField = testListIntField;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestListPB.Builder getCopyCsBuilder() {
        final YoTestListPB.Builder builder = YoTestListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(YoTestListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTestListIntField() != 0) {
            builder.setTestListIntField(this.getTestListIntField());
            fieldCnt++;
        }  else if (builder.hasTestListIntField()) {
            // 清理TestListIntField
            builder.clearTestListIntField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(YoTestListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTLISTINTFIELD)) {
            builder.setTestListIntField(this.getTestListIntField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(YoTestListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTLISTINTFIELD)) {
            builder.setTestListIntField(this.getTestListIntField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(YoTestListPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTestListIntField()) {
            this.innerSetTestListIntField(proto.getTestListIntField());
        } else {
            this.innerSetTestListIntField(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return YoTestListProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(YoTestListPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTestListIntField()) {
            this.setTestListIntField(proto.getTestListIntField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestList.Builder getCopyDbBuilder() {
        final YoTestList.Builder builder = YoTestList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(YoTestList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTestListIntField() != 0) {
            builder.setTestListIntField(this.getTestListIntField());
            fieldCnt++;
        }  else if (builder.hasTestListIntField()) {
            // 清理TestListIntField
            builder.clearTestListIntField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(YoTestList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTLISTINTFIELD)) {
            builder.setTestListIntField(this.getTestListIntField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(YoTestList proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTestListIntField()) {
            this.innerSetTestListIntField(proto.getTestListIntField());
        } else {
            this.innerSetTestListIntField(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return YoTestListProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(YoTestList proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTestListIntField()) {
            this.setTestListIntField(proto.getTestListIntField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public YoTestList.Builder getCopySsBuilder() {
        final YoTestList.Builder builder = YoTestList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(YoTestList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTestListIntField() != 0) {
            builder.setTestListIntField(this.getTestListIntField());
            fieldCnt++;
        }  else if (builder.hasTestListIntField()) {
            // 清理TestListIntField
            builder.clearTestListIntField();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(YoTestList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TESTLISTINTFIELD)) {
            builder.setTestListIntField(this.getTestListIntField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(YoTestList proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTestListIntField()) {
            this.innerSetTestListIntField(proto.getTestListIntField());
        } else {
            this.innerSetTestListIntField(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return YoTestListProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(YoTestList proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTestListIntField()) {
            this.setTestListIntField(proto.getTestListIntField());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        YoTestList.Builder builder = YoTestList.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof YoTestListProp)) {
            return false;
        }
        final YoTestListProp otherNode = (YoTestListProp) node;
        if (this.testListIntField != otherNode.testListIntField) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ActNormalGoodsExtInfo;
import com.yorha.proto.PlayerPB.ActNormalGoodsExtInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ActNormalGoodsExtInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ACTID = 0;
    public static final int FIELD_INDEX_UNITID = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int actId = Constant.DEFAULT_INT_VALUE;
    private int unitId = Constant.DEFAULT_INT_VALUE;

    public ActNormalGoodsExtInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActNormalGoodsExtInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get actId
     *
     * @return actId value
     */
    public int getActId() {
        return this.actId;
    }

    /**
     * set actId && set marked
     *
     * @param actId new value
     * @return current object
     */
    public ActNormalGoodsExtInfoProp setActId(int actId) {
        if (this.actId != actId) {
            this.mark(FIELD_INDEX_ACTID);
            this.actId = actId;
        }
        return this;
    }

    /**
     * inner set actId
     *
     * @param actId new value
     */
    private void innerSetActId(int actId) {
        this.actId = actId;
    }

    /**
     * get unitId
     *
     * @return unitId value
     */
    public int getUnitId() {
        return this.unitId;
    }

    /**
     * set unitId && set marked
     *
     * @param unitId new value
     * @return current object
     */
    public ActNormalGoodsExtInfoProp setUnitId(int unitId) {
        if (this.unitId != unitId) {
            this.mark(FIELD_INDEX_UNITID);
            this.unitId = unitId;
        }
        return this;
    }

    /**
     * inner set unitId
     *
     * @param unitId new value
     */
    private void innerSetUnitId(int unitId) {
        this.unitId = unitId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActNormalGoodsExtInfoPB.Builder getCopyCsBuilder() {
        final ActNormalGoodsExtInfoPB.Builder builder = ActNormalGoodsExtInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActNormalGoodsExtInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActNormalGoodsExtInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActNormalGoodsExtInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActNormalGoodsExtInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActNormalGoodsExtInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActNormalGoodsExtInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActNormalGoodsExtInfo.Builder getCopyDbBuilder() {
        final ActNormalGoodsExtInfo.Builder builder = ActNormalGoodsExtInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActNormalGoodsExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActNormalGoodsExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActNormalGoodsExtInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActNormalGoodsExtInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActNormalGoodsExtInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActNormalGoodsExtInfo.Builder getCopySsBuilder() {
        final ActNormalGoodsExtInfo.Builder builder = ActNormalGoodsExtInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActNormalGoodsExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getActId() != 0) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }  else if (builder.hasActId()) {
            // 清理ActId
            builder.clearActId();
            fieldCnt++;
        }
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActNormalGoodsExtInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ACTID)) {
            builder.setActId(this.getActId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActNormalGoodsExtInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasActId()) {
            this.innerSetActId(proto.getActId());
        } else {
            this.innerSetActId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActNormalGoodsExtInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActNormalGoodsExtInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasActId()) {
            this.setActId(proto.getActId());
            fieldCnt++;
        }
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActNormalGoodsExtInfo.Builder builder = ActNormalGoodsExtInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActNormalGoodsExtInfoProp)) {
            return false;
        }
        final ActNormalGoodsExtInfoProp otherNode = (ActNormalGoodsExtInfoProp) node;
        if (this.actId != otherNode.actId) {
            return false;
        }
        if (this.unitId != otherNode.unitId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
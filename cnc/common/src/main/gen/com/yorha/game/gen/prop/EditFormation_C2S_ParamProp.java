package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.EditFormation_C2S_Param;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.EditFormation_C2S_ParamPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class EditFormation_C2S_ParamProp extends AbstractPropNode {

    public static final int FIELD_INDEX_FORMATION = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private FormationProp formation = null;

    public EditFormation_C2S_ParamProp() {
        super(null, 0, FIELD_COUNT);
    }

    public EditFormation_C2S_ParamProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get formation
     *
     * @return formation value
     */
    public FormationProp getFormation() {
        if (this.formation == null) {
            this.formation = new FormationProp(this, FIELD_INDEX_FORMATION);
        }
        return this.formation;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public EditFormation_C2S_ParamPB.Builder getCopyCsBuilder() {
        final EditFormation_C2S_ParamPB.Builder builder = EditFormation_C2S_ParamPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(EditFormation_C2S_ParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.formation != null) {
            StructPlayerPB.FormationPB.Builder tmpBuilder = StructPlayerPB.FormationPB.newBuilder();
            final int tmpFieldCnt = this.formation.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormation();
            }
        }  else if (builder.hasFormation()) {
            // 清理Formation
            builder.clearFormation();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(EditFormation_C2S_ParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            final boolean needClear = !builder.hasFormation();
            final int tmpFieldCnt = this.formation.copyChangeToCs(builder.getFormationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormation();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(EditFormation_C2S_ParamPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            final boolean needClear = !builder.hasFormation();
            final int tmpFieldCnt = this.formation.copyChangeToAndClearDeleteKeysCs(builder.getFormationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormation();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(EditFormation_C2S_ParamPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeFromCs(proto.getFormation());
        } else {
            if (this.formation != null) {
                this.formation.mergeFromCs(proto.getFormation());
            }
        }
        this.markAll();
        return EditFormation_C2S_ParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(EditFormation_C2S_ParamPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormation()) {
            this.getFormation().mergeChangeFromCs(proto.getFormation());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public EditFormation_C2S_Param.Builder getCopySsBuilder() {
        final EditFormation_C2S_Param.Builder builder = EditFormation_C2S_Param.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(EditFormation_C2S_Param.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.formation != null) {
            StructPlayer.Formation.Builder tmpBuilder = StructPlayer.Formation.newBuilder();
            final int tmpFieldCnt = this.formation.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFormation(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFormation();
            }
        }  else if (builder.hasFormation()) {
            // 清理Formation
            builder.clearFormation();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(EditFormation_C2S_Param.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            final boolean needClear = !builder.hasFormation();
            final int tmpFieldCnt = this.formation.copyChangeToSs(builder.getFormationBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFormation();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(EditFormation_C2S_Param proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasFormation()) {
            this.getFormation().mergeFromSs(proto.getFormation());
        } else {
            if (this.formation != null) {
                this.formation.mergeFromSs(proto.getFormation());
            }
        }
        this.markAll();
        return EditFormation_C2S_ParamProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(EditFormation_C2S_Param proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasFormation()) {
            this.getFormation().mergeChangeFromSs(proto.getFormation());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        EditFormation_C2S_Param.Builder builder = EditFormation_C2S_Param.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_FORMATION) && this.formation != null) {
            this.formation.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.formation != null) {
            this.formation.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof EditFormation_C2S_ParamProp)) {
            return false;
        }
        final EditFormation_C2S_ParamProp otherNode = (EditFormation_C2S_ParamProp) node;
        if (!this.getFormation().compareDataTo(otherNode.getFormation())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
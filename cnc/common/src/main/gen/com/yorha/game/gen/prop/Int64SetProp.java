package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractSetNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.BasicPB.Int64SetPB;
import com.yorha.proto.Basic.Int64Set;

import java.util.Collections;
import java.util.List;
import java.util.TreeSet;
import java.util.Set;
import java.util.ArrayList;

/**
 * <AUTHOR> auto gen
 */
public class Int64SetProp extends AbstractSetNode<Long> {
    /**
     * Create a Int64SetProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public Int64SetProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64SetPB.Builder getCopyCsBuilder() {
        final Int64SetPB.Builder builder = Int64SetPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(Int64SetPB.Builder builder) {
        if (builder == null) {
            return 0;
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int64SetProp.FIELD_COUNT;
            } 
            return 0;
        }
        builder.clear();
        List<Long> tempList = new ArrayList<>(this.getValues());
        Collections.sort(tempList);
        builder.addAllDatas(tempList);
        return Int64SetProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(Int64SetPB.Builder builder) {
        if (builder == null || !this.hasAnyMark()) {
            return 0;
        }
        // has clear flag, clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            builder.setClearFlag(true);
        }
        // there is also new data even clear flag is true
        // refresh data when not clear
        final Set<Long> builderData = new TreeSet<>(builder.getDatasList());
        // always clear builder data
        builder.clearDatas();

        // no clear flag
        if (!this.isClearFlag()) {
            final Set<Long> deleteKeys = !builder.getClearFlag() ? new TreeSet<>(builder.getDeleteKeysList()) : null;
            final Set<Long> refreshKeys = new TreeSet<>(this.getDeleteKeys());
            builderData.removeAll(refreshKeys);
            if (deleteKeys != null) {
                builder.clearDeleteKeys();
                deleteKeys.addAll(refreshKeys);
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // compute intersect
        final Set<Long> intersect = new TreeSet<>(this.getAddKeys());
        intersect.removeAll(builderData);

        // if there is intersected, add to builder
        if (!intersect.isEmpty()) {
            builderData.addAll(intersect);
        }
        builder.addAllDatas(builderData);
        return Int64SetProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last. it wll clear clearFlag and deleteKeys.
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToAndClearDeleteKeysCs(Int64SetPB.Builder builder) {
        if (builder == null || !this.hasAnyMark()) {
            return 0;
        }

        // has clear flag, clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
        }

        // always clear builder data
        builder.clearDatas();

        // always add all keys into builder
        builder.addAllDatas(this.getValues());

        // clear clear flag, clear delete keys
        builder.clearClearFlag();
        builder.clearDeleteKeys();
        return Int64SetProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(Int64SetPB proto) {
        if(proto == null) {
            return 0;
        }
        clear();
        addAll(proto.getDatasList());
        markAll();
        return Int64SetProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(Int64SetPB proto) {
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            clear();
            changeCnt = Int64SetProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            removeAll(proto.getDeleteKeysList());
            changeCnt = Int64SetProp.FIELD_COUNT;
        }
        for (Long key : proto.getDatasList()) {
            if (!contains(key)) {
                add(key);
                changeCnt = Int64SetProp.FIELD_COUNT;
            }
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64Set.Builder getCopyDbBuilder() {
        final Int64Set.Builder builder = Int64Set.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(Int64Set.Builder builder) {
        if (builder == null) {
            return 0;
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int64SetProp.FIELD_COUNT;
            } 
            return 0;
        }
        builder.clear();
        List<Long> tempList = new ArrayList<>(this.getValues());
        Collections.sort(tempList);
        builder.addAllDatas(tempList);
        return Int64SetProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(Int64Set.Builder builder) {
        if (builder == null || !this.hasAnyMark()) {
            return 0;
        }
        // has clear flag, clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            builder.setClearFlag(true);
        }
        // there is also new data even clear flag is true
        // refresh data when not clear
        final Set<Long> builderData = new TreeSet<>(builder.getDatasList());
        // always clear builder data
        builder.clearDatas();

        // no clear flag
        if (!this.isClearFlag()) {
            final Set<Long> deleteKeys = !builder.getClearFlag() ? new TreeSet<>(builder.getDeleteKeysList()) : null;
            final Set<Long> refreshKeys = new TreeSet<>(this.getDeleteKeys());
            builderData.removeAll(refreshKeys);
            if (deleteKeys != null) {
                builder.clearDeleteKeys();
                deleteKeys.addAll(refreshKeys);
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // compute intersect
        final Set<Long> intersect = new TreeSet<>(this.getAddKeys());
        intersect.removeAll(builderData);

        // if there is intersected, add to builder
        if (!intersect.isEmpty()) {
            builderData.addAll(intersect);
        }
        builder.addAllDatas(builderData);
        return Int64SetProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(Int64Set proto) {
        if(proto == null) {
            return 0;
        }
        clear();
        addAll(proto.getDatasList());
        markAll();
        return Int64SetProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(Int64Set proto) {
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            clear();
            changeCnt = Int64SetProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            removeAll(proto.getDeleteKeysList());
            changeCnt = Int64SetProp.FIELD_COUNT;
        }
        for (Long key : proto.getDatasList()) {
            if (!contains(key)) {
                add(key);
                changeCnt = Int64SetProp.FIELD_COUNT;
            }
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Int64Set.Builder getCopySsBuilder() {
        final Int64Set.Builder builder = Int64Set.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(Int64Set.Builder builder) {
        if (builder == null) {
            return 0;
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return Int64SetProp.FIELD_COUNT;
            } 
            return 0;
        }
        builder.clear();
        List<Long> tempList = new ArrayList<>(this.getValues());
        Collections.sort(tempList);
        builder.addAllDatas(tempList);
        return Int64SetProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(Int64Set.Builder builder) {
        if (builder == null || !this.hasAnyMark()) {
            return 0;
        }
        // has clear flag, clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            builder.setClearFlag(true);
        }
        // there is also new data even clear flag is true
        // refresh data when not clear
        final Set<Long> builderData = new TreeSet<>(builder.getDatasList());
        // always clear builder data
        builder.clearDatas();

        // no clear flag
        if (!this.isClearFlag()) {
            final Set<Long> deleteKeys = !builder.getClearFlag() ? new TreeSet<>(builder.getDeleteKeysList()) : null;
            final Set<Long> refreshKeys = new TreeSet<>(this.getDeleteKeys());
            builderData.removeAll(refreshKeys);
            if (deleteKeys != null) {
                builder.clearDeleteKeys();
                deleteKeys.addAll(refreshKeys);
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // compute intersect
        final Set<Long> intersect = new TreeSet<>(this.getAddKeys());
        intersect.removeAll(builderData);

        // if there is intersected, add to builder
        if (!intersect.isEmpty()) {
            builderData.addAll(intersect);
        }
        builder.addAllDatas(builderData);
        return Int64SetProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(Int64Set proto) {
        if(proto == null) {
            return 0;
        }
        clear();
        addAll(proto.getDatasList());
        markAll();
        return Int64SetProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(Int64Set proto) {
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            clear();
            changeCnt = Int64SetProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            removeAll(proto.getDeleteKeysList());
            changeCnt = Int64SetProp.FIELD_COUNT;
        }
        for (Long key : proto.getDatasList()) {
            if (!contains(key)) {
                add(key);
                changeCnt = Int64SetProp.FIELD_COUNT;
            }
        }
        return changeCnt;
    }

    @Override
    public String toString() {
        Int64Set.Builder builder = Int64Set.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerStatisticModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerStatisticModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerStatisticModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_SINGLESTATISTICMAP = 0;
    public static final int FIELD_INDEX_SECONDSTATISTICMAP = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32SingleStatisticMapProp singleStatisticMap = null;
    private Int32SecondStatisticMapProp secondStatisticMap = null;

    public PlayerStatisticModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerStatisticModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get singleStatisticMap
     *
     * @return singleStatisticMap value
     */
    public Int32SingleStatisticMapProp getSingleStatisticMap() {
        if (this.singleStatisticMap == null) {
            this.singleStatisticMap = new Int32SingleStatisticMapProp(this, FIELD_INDEX_SINGLESTATISTICMAP);
        }
        return this.singleStatisticMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSingleStatisticMapV(SingleStatisticProp v) {
        this.getSingleStatisticMap().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SingleStatisticProp addEmptySingleStatisticMap(Integer k) {
        return this.getSingleStatisticMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSingleStatisticMapSize() {
        if (this.singleStatisticMap == null) {
            return 0;
        }
        return this.singleStatisticMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSingleStatisticMapEmpty() {
        if (this.singleStatisticMap == null) {
            return true;
        }
        return this.singleStatisticMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SingleStatisticProp getSingleStatisticMapV(Integer k) {
        if (this.singleStatisticMap == null || !this.singleStatisticMap.containsKey(k)) {
            return null;
        }
        return this.singleStatisticMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSingleStatisticMap() {
        if (this.singleStatisticMap != null) {
            this.singleStatisticMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSingleStatisticMapV(Integer k) {
        if (this.singleStatisticMap != null) {
            this.singleStatisticMap.remove(k);
        }
    }
    /**
     * get secondStatisticMap
     *
     * @return secondStatisticMap value
     */
    public Int32SecondStatisticMapProp getSecondStatisticMap() {
        if (this.secondStatisticMap == null) {
            this.secondStatisticMap = new Int32SecondStatisticMapProp(this, FIELD_INDEX_SECONDSTATISTICMAP);
        }
        return this.secondStatisticMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSecondStatisticMapV(SecondStatisticProp v) {
        this.getSecondStatisticMap().put(v.getEnumId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SecondStatisticProp addEmptySecondStatisticMap(Integer k) {
        return this.getSecondStatisticMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSecondStatisticMapSize() {
        if (this.secondStatisticMap == null) {
            return 0;
        }
        return this.secondStatisticMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSecondStatisticMapEmpty() {
        if (this.secondStatisticMap == null) {
            return true;
        }
        return this.secondStatisticMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SecondStatisticProp getSecondStatisticMapV(Integer k) {
        if (this.secondStatisticMap == null || !this.secondStatisticMap.containsKey(k)) {
            return null;
        }
        return this.secondStatisticMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSecondStatisticMap() {
        if (this.secondStatisticMap != null) {
            this.secondStatisticMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSecondStatisticMapV(Integer k) {
        if (this.secondStatisticMap != null) {
            this.secondStatisticMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStatisticModelPB.Builder getCopyCsBuilder() {
        final PlayerStatisticModelPB.Builder builder = PlayerStatisticModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerStatisticModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.singleStatisticMap != null) {
            StructPB.Int32SingleStatisticMapPB.Builder tmpBuilder = StructPB.Int32SingleStatisticMapPB.newBuilder();
            final int tmpFieldCnt = this.singleStatisticMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleStatisticMap();
            }
        }  else if (builder.hasSingleStatisticMap()) {
            // 清理SingleStatisticMap
            builder.clearSingleStatisticMap();
            fieldCnt++;
        }
        if (this.secondStatisticMap != null) {
            StructPB.Int32SecondStatisticMapPB.Builder tmpBuilder = StructPB.Int32SecondStatisticMapPB.newBuilder();
            final int tmpFieldCnt = this.secondStatisticMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSecondStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSecondStatisticMap();
            }
        }  else if (builder.hasSecondStatisticMap()) {
            // 清理SecondStatisticMap
            builder.clearSecondStatisticMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerStatisticModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToCs(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_SECONDSTATISTICMAP) && this.secondStatisticMap != null) {
            final boolean needClear = !builder.hasSecondStatisticMap();
            final int tmpFieldCnt = this.secondStatisticMap.copyChangeToCs(builder.getSecondStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSecondStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerStatisticModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToAndClearDeleteKeysCs(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_SECONDSTATISTICMAP) && this.secondStatisticMap != null) {
            final boolean needClear = !builder.hasSecondStatisticMap();
            final int tmpFieldCnt = this.secondStatisticMap.copyChangeToAndClearDeleteKeysCs(builder.getSecondStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSecondStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerStatisticModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeFromCs(proto.getSingleStatisticMap());
        } else {
            if (this.singleStatisticMap != null) {
                this.singleStatisticMap.mergeFromCs(proto.getSingleStatisticMap());
            }
        }
        if (proto.hasSecondStatisticMap()) {
            this.getSecondStatisticMap().mergeFromCs(proto.getSecondStatisticMap());
        } else {
            if (this.secondStatisticMap != null) {
                this.secondStatisticMap.mergeFromCs(proto.getSecondStatisticMap());
            }
        }
        this.markAll();
        return PlayerStatisticModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerStatisticModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeChangeFromCs(proto.getSingleStatisticMap());
            fieldCnt++;
        }
        if (proto.hasSecondStatisticMap()) {
            this.getSecondStatisticMap().mergeChangeFromCs(proto.getSecondStatisticMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStatisticModel.Builder getCopyDbBuilder() {
        final PlayerStatisticModel.Builder builder = PlayerStatisticModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerStatisticModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.singleStatisticMap != null) {
            Struct.Int32SingleStatisticMap.Builder tmpBuilder = Struct.Int32SingleStatisticMap.newBuilder();
            final int tmpFieldCnt = this.singleStatisticMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleStatisticMap();
            }
        }  else if (builder.hasSingleStatisticMap()) {
            // 清理SingleStatisticMap
            builder.clearSingleStatisticMap();
            fieldCnt++;
        }
        if (this.secondStatisticMap != null) {
            Struct.Int32SecondStatisticMap.Builder tmpBuilder = Struct.Int32SecondStatisticMap.newBuilder();
            final int tmpFieldCnt = this.secondStatisticMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSecondStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSecondStatisticMap();
            }
        }  else if (builder.hasSecondStatisticMap()) {
            // 清理SecondStatisticMap
            builder.clearSecondStatisticMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerStatisticModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToDb(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_SECONDSTATISTICMAP) && this.secondStatisticMap != null) {
            final boolean needClear = !builder.hasSecondStatisticMap();
            final int tmpFieldCnt = this.secondStatisticMap.copyChangeToDb(builder.getSecondStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSecondStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerStatisticModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeFromDb(proto.getSingleStatisticMap());
        } else {
            if (this.singleStatisticMap != null) {
                this.singleStatisticMap.mergeFromDb(proto.getSingleStatisticMap());
            }
        }
        if (proto.hasSecondStatisticMap()) {
            this.getSecondStatisticMap().mergeFromDb(proto.getSecondStatisticMap());
        } else {
            if (this.secondStatisticMap != null) {
                this.secondStatisticMap.mergeFromDb(proto.getSecondStatisticMap());
            }
        }
        this.markAll();
        return PlayerStatisticModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerStatisticModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeChangeFromDb(proto.getSingleStatisticMap());
            fieldCnt++;
        }
        if (proto.hasSecondStatisticMap()) {
            this.getSecondStatisticMap().mergeChangeFromDb(proto.getSecondStatisticMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerStatisticModel.Builder getCopySsBuilder() {
        final PlayerStatisticModel.Builder builder = PlayerStatisticModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerStatisticModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.singleStatisticMap != null) {
            Struct.Int32SingleStatisticMap.Builder tmpBuilder = Struct.Int32SingleStatisticMap.newBuilder();
            final int tmpFieldCnt = this.singleStatisticMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSingleStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSingleStatisticMap();
            }
        }  else if (builder.hasSingleStatisticMap()) {
            // 清理SingleStatisticMap
            builder.clearSingleStatisticMap();
            fieldCnt++;
        }
        if (this.secondStatisticMap != null) {
            Struct.Int32SecondStatisticMap.Builder tmpBuilder = Struct.Int32SecondStatisticMap.newBuilder();
            final int tmpFieldCnt = this.secondStatisticMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSecondStatisticMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSecondStatisticMap();
            }
        }  else if (builder.hasSecondStatisticMap()) {
            // 清理SecondStatisticMap
            builder.clearSecondStatisticMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerStatisticModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            final boolean needClear = !builder.hasSingleStatisticMap();
            final int tmpFieldCnt = this.singleStatisticMap.copyChangeToSs(builder.getSingleStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSingleStatisticMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_SECONDSTATISTICMAP) && this.secondStatisticMap != null) {
            final boolean needClear = !builder.hasSecondStatisticMap();
            final int tmpFieldCnt = this.secondStatisticMap.copyChangeToSs(builder.getSecondStatisticMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSecondStatisticMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerStatisticModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeFromSs(proto.getSingleStatisticMap());
        } else {
            if (this.singleStatisticMap != null) {
                this.singleStatisticMap.mergeFromSs(proto.getSingleStatisticMap());
            }
        }
        if (proto.hasSecondStatisticMap()) {
            this.getSecondStatisticMap().mergeFromSs(proto.getSecondStatisticMap());
        } else {
            if (this.secondStatisticMap != null) {
                this.secondStatisticMap.mergeFromSs(proto.getSecondStatisticMap());
            }
        }
        this.markAll();
        return PlayerStatisticModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerStatisticModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasSingleStatisticMap()) {
            this.getSingleStatisticMap().mergeChangeFromSs(proto.getSingleStatisticMap());
            fieldCnt++;
        }
        if (proto.hasSecondStatisticMap()) {
            this.getSecondStatisticMap().mergeChangeFromSs(proto.getSecondStatisticMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerStatisticModel.Builder builder = PlayerStatisticModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_SINGLESTATISTICMAP) && this.singleStatisticMap != null) {
            this.singleStatisticMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SECONDSTATISTICMAP) && this.secondStatisticMap != null) {
            this.secondStatisticMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.singleStatisticMap != null) {
            this.singleStatisticMap.markAll();
        }
        if (this.secondStatisticMap != null) {
            this.secondStatisticMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerStatisticModelProp)) {
            return false;
        }
        final PlayerStatisticModelProp otherNode = (PlayerStatisticModelProp) node;
        if (!this.getSingleStatisticMap().compareDataTo(otherNode.getSingleStatisticMap())) {
            return false;
        }
        if (!this.getSecondStatisticMap().compareDataTo(otherNode.getSecondStatisticMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
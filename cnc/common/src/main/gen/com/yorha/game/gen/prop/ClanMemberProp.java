package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructClan.ClanMember;
import com.yorha.proto.Struct;
import com.yorha.proto.StructClanPB.ClanMemberPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanMemberProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_COMBOAT = 1;
    public static final int FIELD_INDEX_ENTERTIME = 2;
    public static final int FIELD_INDEX_STAFFID = 3;
    public static final int FIELD_INDEX_ISONLINE = 4;
    public static final int FIELD_INDEX_RESLASTCALTSMS = 5;
    public static final int FIELD_INDEX_RESOURCES = 6;
    public static final int FIELD_INDEX_CARDHEAD = 7;
    public static final int FIELD_INDEX_NEXTCANBEOWNERTSMS = 8;
    public static final int FIELD_INDEX_KILLSCORE = 9;
    public static final int FIELD_INDEX_REDDOTMAP = 10;
    public static final int FIELD_INDEX_LASTONLINETSMS = 11;
    public static final int FIELD_INDEX_TOPHERO = 12;

    public static final int FIELD_COUNT = 13;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private long comboat = Constant.DEFAULT_LONG_VALUE;
    private long enterTime = Constant.DEFAULT_LONG_VALUE;
    private int staffId = Constant.DEFAULT_INT_VALUE;
    private boolean isOnline = Constant.DEFAULT_BOOLEAN_VALUE;
    private long resLastCalTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32CurrencyMapProp resources = null;
    private PlayerCardHeadProp cardHead = null;
    private long nextCanBeOwnerTsMs = Constant.DEFAULT_LONG_VALUE;
    private long killScore = Constant.DEFAULT_LONG_VALUE;
    private Int32RedDotDataMapProp redDotMap = null;
    private long lastOnlineTsMs = Constant.DEFAULT_LONG_VALUE;
    private SimpleHeroListProp topHero = null;

    public ClanMemberProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanMemberProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ClanMemberProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get comboat
     *
     * @return comboat value
     */
    public long getComboat() {
        return this.comboat;
    }

    /**
     * set comboat && set marked
     *
     * @param comboat new value
     * @return current object
     */
    public ClanMemberProp setComboat(long comboat) {
        if (this.comboat != comboat) {
            this.mark(FIELD_INDEX_COMBOAT);
            this.comboat = comboat;
        }
        return this;
    }

    /**
     * inner set comboat
     *
     * @param comboat new value
     */
    private void innerSetComboat(long comboat) {
        this.comboat = comboat;
    }

    /**
     * get enterTime
     *
     * @return enterTime value
     */
    public long getEnterTime() {
        return this.enterTime;
    }

    /**
     * set enterTime && set marked
     *
     * @param enterTime new value
     * @return current object
     */
    public ClanMemberProp setEnterTime(long enterTime) {
        if (this.enterTime != enterTime) {
            this.mark(FIELD_INDEX_ENTERTIME);
            this.enterTime = enterTime;
        }
        return this;
    }

    /**
     * inner set enterTime
     *
     * @param enterTime new value
     */
    private void innerSetEnterTime(long enterTime) {
        this.enterTime = enterTime;
    }

    /**
     * get staffId
     *
     * @return staffId value
     */
    public int getStaffId() {
        return this.staffId;
    }

    /**
     * set staffId && set marked
     *
     * @param staffId new value
     * @return current object
     */
    public ClanMemberProp setStaffId(int staffId) {
        if (this.staffId != staffId) {
            this.mark(FIELD_INDEX_STAFFID);
            this.staffId = staffId;
        }
        return this;
    }

    /**
     * inner set staffId
     *
     * @param staffId new value
     */
    private void innerSetStaffId(int staffId) {
        this.staffId = staffId;
    }

    /**
     * get isOnline
     *
     * @return isOnline value
     */
    public boolean getIsOnline() {
        return this.isOnline;
    }

    /**
     * set isOnline && set marked
     *
     * @param isOnline new value
     * @return current object
     */
    public ClanMemberProp setIsOnline(boolean isOnline) {
        if (this.isOnline != isOnline) {
            this.mark(FIELD_INDEX_ISONLINE);
            this.isOnline = isOnline;
        }
        return this;
    }

    /**
     * inner set isOnline
     *
     * @param isOnline new value
     */
    private void innerSetIsOnline(boolean isOnline) {
        this.isOnline = isOnline;
    }

    /**
     * get resLastCalTsMs
     *
     * @return resLastCalTsMs value
     */
    public long getResLastCalTsMs() {
        return this.resLastCalTsMs;
    }

    /**
     * set resLastCalTsMs && set marked
     *
     * @param resLastCalTsMs new value
     * @return current object
     */
    public ClanMemberProp setResLastCalTsMs(long resLastCalTsMs) {
        if (this.resLastCalTsMs != resLastCalTsMs) {
            this.mark(FIELD_INDEX_RESLASTCALTSMS);
            this.resLastCalTsMs = resLastCalTsMs;
        }
        return this;
    }

    /**
     * inner set resLastCalTsMs
     *
     * @param resLastCalTsMs new value
     */
    private void innerSetResLastCalTsMs(long resLastCalTsMs) {
        this.resLastCalTsMs = resLastCalTsMs;
    }

    /**
     * get resources
     *
     * @return resources value
     */
    public Int32CurrencyMapProp getResources() {
        if (this.resources == null) {
            this.resources = new Int32CurrencyMapProp(this, FIELD_INDEX_RESOURCES);
        }
        return this.resources;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putResourcesV(CurrencyProp v) {
        this.getResources().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public CurrencyProp addEmptyResources(Integer k) {
        return this.getResources().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getResourcesSize() {
        if (this.resources == null) {
            return 0;
        }
        return this.resources.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isResourcesEmpty() {
        if (this.resources == null) {
            return true;
        }
        return this.resources.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public CurrencyProp getResourcesV(Integer k) {
        if (this.resources == null || !this.resources.containsKey(k)) {
            return null;
        }
        return this.resources.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearResources() {
        if (this.resources != null) {
            this.resources.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeResourcesV(Integer k) {
        if (this.resources != null) {
            this.resources.remove(k);
        }
    }
    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get nextCanBeOwnerTsMs
     *
     * @return nextCanBeOwnerTsMs value
     */
    public long getNextCanBeOwnerTsMs() {
        return this.nextCanBeOwnerTsMs;
    }

    /**
     * set nextCanBeOwnerTsMs && set marked
     *
     * @param nextCanBeOwnerTsMs new value
     * @return current object
     */
    public ClanMemberProp setNextCanBeOwnerTsMs(long nextCanBeOwnerTsMs) {
        if (this.nextCanBeOwnerTsMs != nextCanBeOwnerTsMs) {
            this.mark(FIELD_INDEX_NEXTCANBEOWNERTSMS);
            this.nextCanBeOwnerTsMs = nextCanBeOwnerTsMs;
        }
        return this;
    }

    /**
     * inner set nextCanBeOwnerTsMs
     *
     * @param nextCanBeOwnerTsMs new value
     */
    private void innerSetNextCanBeOwnerTsMs(long nextCanBeOwnerTsMs) {
        this.nextCanBeOwnerTsMs = nextCanBeOwnerTsMs;
    }

    /**
     * get killScore
     *
     * @return killScore value
     */
    public long getKillScore() {
        return this.killScore;
    }

    /**
     * set killScore && set marked
     *
     * @param killScore new value
     * @return current object
     */
    public ClanMemberProp setKillScore(long killScore) {
        if (this.killScore != killScore) {
            this.mark(FIELD_INDEX_KILLSCORE);
            this.killScore = killScore;
        }
        return this;
    }

    /**
     * inner set killScore
     *
     * @param killScore new value
     */
    private void innerSetKillScore(long killScore) {
        this.killScore = killScore;
    }

    /**
     * get redDotMap
     *
     * @return redDotMap value
     */
    public Int32RedDotDataMapProp getRedDotMap() {
        if (this.redDotMap == null) {
            this.redDotMap = new Int32RedDotDataMapProp(this, FIELD_INDEX_REDDOTMAP);
        }
        return this.redDotMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRedDotMapV(RedDotDataProp v) {
        this.getRedDotMap().put(v.getKey(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public RedDotDataProp addEmptyRedDotMap(Integer k) {
        return this.getRedDotMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRedDotMapSize() {
        if (this.redDotMap == null) {
            return 0;
        }
        return this.redDotMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRedDotMapEmpty() {
        if (this.redDotMap == null) {
            return true;
        }
        return this.redDotMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public RedDotDataProp getRedDotMapV(Integer k) {
        if (this.redDotMap == null || !this.redDotMap.containsKey(k)) {
            return null;
        }
        return this.redDotMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRedDotMap() {
        if (this.redDotMap != null) {
            this.redDotMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRedDotMapV(Integer k) {
        if (this.redDotMap != null) {
            this.redDotMap.remove(k);
        }
    }
    /**
     * get lastOnlineTsMs
     *
     * @return lastOnlineTsMs value
     */
    public long getLastOnlineTsMs() {
        return this.lastOnlineTsMs;
    }

    /**
     * set lastOnlineTsMs && set marked
     *
     * @param lastOnlineTsMs new value
     * @return current object
     */
    public ClanMemberProp setLastOnlineTsMs(long lastOnlineTsMs) {
        if (this.lastOnlineTsMs != lastOnlineTsMs) {
            this.mark(FIELD_INDEX_LASTONLINETSMS);
            this.lastOnlineTsMs = lastOnlineTsMs;
        }
        return this;
    }

    /**
     * inner set lastOnlineTsMs
     *
     * @param lastOnlineTsMs new value
     */
    private void innerSetLastOnlineTsMs(long lastOnlineTsMs) {
        this.lastOnlineTsMs = lastOnlineTsMs;
    }

    /**
     * get topHero
     *
     * @return topHero value
     */
    public SimpleHeroListProp getTopHero() {
        if (this.topHero == null) {
            this.topHero = new SimpleHeroListProp(this, FIELD_INDEX_TOPHERO);
        }
        return this.topHero;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addTopHero(SimpleHeroProp v) {
        this.getTopHero().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SimpleHeroProp getTopHeroIndex(int index) {
        return this.getTopHero().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public SimpleHeroProp removeTopHero(SimpleHeroProp v) {
        if (this.topHero == null) {
            return null;
        }
        if(this.topHero.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getTopHeroSize() {
        if (this.topHero == null) {
            return 0;
        }
        return this.topHero.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isTopHeroEmpty() {
        if (this.topHero == null) {
            return true;
        }
        return this.getTopHero().isEmpty();
    }

    /**
     * clear list
     */
    public void clearTopHero() {
        this.getTopHero().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public SimpleHeroProp removeTopHeroIndex(int index) {
        return this.getTopHero().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public SimpleHeroProp setTopHeroIndex(int index, SimpleHeroProp v) {
        return this.getTopHero().set(index, v);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMemberPB.Builder getCopyCsBuilder() {
        final ClanMemberPB.Builder builder = ClanMemberPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanMemberPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getComboat() != 0L) {
            builder.setComboat(this.getComboat());
            fieldCnt++;
        }  else if (builder.hasComboat()) {
            // 清理Comboat
            builder.clearComboat();
            fieldCnt++;
        }
        if (this.getEnterTime() != 0L) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }  else if (builder.hasEnterTime()) {
            // 清理EnterTime
            builder.clearEnterTime();
            fieldCnt++;
        }
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getIsOnline()) {
            builder.setIsOnline(this.getIsOnline());
            fieldCnt++;
        }  else if (builder.hasIsOnline()) {
            // 清理IsOnline
            builder.clearIsOnline();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getNextCanBeOwnerTsMs() != 0L) {
            builder.setNextCanBeOwnerTsMs(this.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }  else if (builder.hasNextCanBeOwnerTsMs()) {
            // 清理NextCanBeOwnerTsMs
            builder.clearNextCanBeOwnerTsMs();
            fieldCnt++;
        }
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.topHero != null) {
            StructPB.SimpleHeroListPB.Builder tmpBuilder = StructPB.SimpleHeroListPB.newBuilder();
            final int tmpFieldCnt = this.topHero.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTopHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTopHero();
            }
        }  else if (builder.hasTopHero()) {
            // 清理TopHero
            builder.clearTopHero();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanMemberPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBOAT)) {
            builder.setComboat(this.getComboat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONLINE)) {
            builder.setIsOnline(this.getIsOnline());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEXTCANBEOWNERTSMS)) {
            builder.setNextCanBeOwnerTsMs(this.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOPHERO) && this.topHero != null) {
            final boolean needClear = !builder.hasTopHero();
            final int tmpFieldCnt = this.topHero.copyChangeToCs(builder.getTopHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTopHero();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanMemberPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBOAT)) {
            builder.setComboat(this.getComboat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONLINE)) {
            builder.setIsOnline(this.getIsOnline());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEXTCANBEOWNERTSMS)) {
            builder.setNextCanBeOwnerTsMs(this.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOPHERO) && this.topHero != null) {
            final boolean needClear = !builder.hasTopHero();
            final int tmpFieldCnt = this.topHero.copyChangeToCs(builder.getTopHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTopHero();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanMemberPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasComboat()) {
            this.innerSetComboat(proto.getComboat());
        } else {
            this.innerSetComboat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterTime()) {
            this.innerSetEnterTime(proto.getEnterTime());
        } else {
            this.innerSetEnterTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOnline()) {
            this.innerSetIsOnline(proto.getIsOnline());
        } else {
            this.innerSetIsOnline(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasNextCanBeOwnerTsMs()) {
            this.innerSetNextCanBeOwnerTsMs(proto.getNextCanBeOwnerTsMs());
        } else {
            this.innerSetNextCanBeOwnerTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTopHero()) {
            this.getTopHero().mergeFromCs(proto.getTopHero());
        } else {
            if (this.topHero != null) {
                this.topHero.mergeFromCs(proto.getTopHero());
            }
        }
        this.markAll();
        return ClanMemberProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanMemberPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasComboat()) {
            this.setComboat(proto.getComboat());
            fieldCnt++;
        }
        if (proto.hasEnterTime()) {
            this.setEnterTime(proto.getEnterTime());
            fieldCnt++;
        }
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasIsOnline()) {
            this.setIsOnline(proto.getIsOnline());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasNextCanBeOwnerTsMs()) {
            this.setNextCanBeOwnerTsMs(proto.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasTopHero()) {
            this.getTopHero().mergeChangeFromCs(proto.getTopHero());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMember.Builder getCopyDbBuilder() {
        final ClanMember.Builder builder = ClanMember.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanMember.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getComboat() != 0L) {
            builder.setComboat(this.getComboat());
            fieldCnt++;
        }  else if (builder.hasComboat()) {
            // 清理Comboat
            builder.clearComboat();
            fieldCnt++;
        }
        if (this.getEnterTime() != 0L) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }  else if (builder.hasEnterTime()) {
            // 清理EnterTime
            builder.clearEnterTime();
            fieldCnt++;
        }
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getResLastCalTsMs() != 0L) {
            builder.setResLastCalTsMs(this.getResLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasResLastCalTsMs()) {
            // 清理ResLastCalTsMs
            builder.clearResLastCalTsMs();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getNextCanBeOwnerTsMs() != 0L) {
            builder.setNextCanBeOwnerTsMs(this.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }  else if (builder.hasNextCanBeOwnerTsMs()) {
            // 清理NextCanBeOwnerTsMs
            builder.clearNextCanBeOwnerTsMs();
            fieldCnt++;
        }
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.redDotMap != null) {
            Struct.Int32RedDotDataMap.Builder tmpBuilder = Struct.Int32RedDotDataMap.newBuilder();
            final int tmpFieldCnt = this.redDotMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotMap();
            }
        }  else if (builder.hasRedDotMap()) {
            // 清理RedDotMap
            builder.clearRedDotMap();
            fieldCnt++;
        }
        if (this.getLastOnlineTsMs() != 0L) {
            builder.setLastOnlineTsMs(this.getLastOnlineTsMs());
            fieldCnt++;
        }  else if (builder.hasLastOnlineTsMs()) {
            // 清理LastOnlineTsMs
            builder.clearLastOnlineTsMs();
            fieldCnt++;
        }
        if (this.topHero != null) {
            Struct.SimpleHeroList.Builder tmpBuilder = Struct.SimpleHeroList.newBuilder();
            final int tmpFieldCnt = this.topHero.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTopHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTopHero();
            }
        }  else if (builder.hasTopHero()) {
            // 清理TopHero
            builder.clearTopHero();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanMember.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBOAT)) {
            builder.setComboat(this.getComboat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESLASTCALTSMS)) {
            builder.setResLastCalTsMs(this.getResLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToDb(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEXTCANBEOWNERTSMS)) {
            builder.setNextCanBeOwnerTsMs(this.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            final boolean needClear = !builder.hasRedDotMap();
            final int tmpFieldCnt = this.redDotMap.copyChangeToDb(builder.getRedDotMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTONLINETSMS)) {
            builder.setLastOnlineTsMs(this.getLastOnlineTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOPHERO) && this.topHero != null) {
            final boolean needClear = !builder.hasTopHero();
            final int tmpFieldCnt = this.topHero.copyChangeToDb(builder.getTopHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTopHero();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanMember proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasComboat()) {
            this.innerSetComboat(proto.getComboat());
        } else {
            this.innerSetComboat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterTime()) {
            this.innerSetEnterTime(proto.getEnterTime());
        } else {
            this.innerSetEnterTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasResLastCalTsMs()) {
            this.innerSetResLastCalTsMs(proto.getResLastCalTsMs());
        } else {
            this.innerSetResLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromDb(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromDb(proto.getResources());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasNextCanBeOwnerTsMs()) {
            this.innerSetNextCanBeOwnerTsMs(proto.getNextCanBeOwnerTsMs());
        } else {
            this.innerSetNextCanBeOwnerTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeFromDb(proto.getRedDotMap());
        } else {
            if (this.redDotMap != null) {
                this.redDotMap.mergeFromDb(proto.getRedDotMap());
            }
        }
        if (proto.hasLastOnlineTsMs()) {
            this.innerSetLastOnlineTsMs(proto.getLastOnlineTsMs());
        } else {
            this.innerSetLastOnlineTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTopHero()) {
            this.getTopHero().mergeFromDb(proto.getTopHero());
        } else {
            if (this.topHero != null) {
                this.topHero.mergeFromDb(proto.getTopHero());
            }
        }
        this.markAll();
        return ClanMemberProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanMember proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasComboat()) {
            this.setComboat(proto.getComboat());
            fieldCnt++;
        }
        if (proto.hasEnterTime()) {
            this.setEnterTime(proto.getEnterTime());
            fieldCnt++;
        }
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasResLastCalTsMs()) {
            this.setResLastCalTsMs(proto.getResLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromDb(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasNextCanBeOwnerTsMs()) {
            this.setNextCanBeOwnerTsMs(proto.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeChangeFromDb(proto.getRedDotMap());
            fieldCnt++;
        }
        if (proto.hasLastOnlineTsMs()) {
            this.setLastOnlineTsMs(proto.getLastOnlineTsMs());
            fieldCnt++;
        }
        if (proto.hasTopHero()) {
            this.getTopHero().mergeChangeFromDb(proto.getTopHero());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanMember.Builder getCopySsBuilder() {
        final ClanMember.Builder builder = ClanMember.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanMember.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getComboat() != 0L) {
            builder.setComboat(this.getComboat());
            fieldCnt++;
        }  else if (builder.hasComboat()) {
            // 清理Comboat
            builder.clearComboat();
            fieldCnt++;
        }
        if (this.getEnterTime() != 0L) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }  else if (builder.hasEnterTime()) {
            // 清理EnterTime
            builder.clearEnterTime();
            fieldCnt++;
        }
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getIsOnline()) {
            builder.setIsOnline(this.getIsOnline());
            fieldCnt++;
        }  else if (builder.hasIsOnline()) {
            // 清理IsOnline
            builder.clearIsOnline();
            fieldCnt++;
        }
        if (this.getResLastCalTsMs() != 0L) {
            builder.setResLastCalTsMs(this.getResLastCalTsMs());
            fieldCnt++;
        }  else if (builder.hasResLastCalTsMs()) {
            // 清理ResLastCalTsMs
            builder.clearResLastCalTsMs();
            fieldCnt++;
        }
        if (this.resources != null) {
            Struct.Int32CurrencyMap.Builder tmpBuilder = Struct.Int32CurrencyMap.newBuilder();
            final int tmpFieldCnt = this.resources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.getNextCanBeOwnerTsMs() != 0L) {
            builder.setNextCanBeOwnerTsMs(this.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }  else if (builder.hasNextCanBeOwnerTsMs()) {
            // 清理NextCanBeOwnerTsMs
            builder.clearNextCanBeOwnerTsMs();
            fieldCnt++;
        }
        if (this.getKillScore() != 0L) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }  else if (builder.hasKillScore()) {
            // 清理KillScore
            builder.clearKillScore();
            fieldCnt++;
        }
        if (this.redDotMap != null) {
            Struct.Int32RedDotDataMap.Builder tmpBuilder = Struct.Int32RedDotDataMap.newBuilder();
            final int tmpFieldCnt = this.redDotMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotMap();
            }
        }  else if (builder.hasRedDotMap()) {
            // 清理RedDotMap
            builder.clearRedDotMap();
            fieldCnt++;
        }
        if (this.getLastOnlineTsMs() != 0L) {
            builder.setLastOnlineTsMs(this.getLastOnlineTsMs());
            fieldCnt++;
        }  else if (builder.hasLastOnlineTsMs()) {
            // 清理LastOnlineTsMs
            builder.clearLastOnlineTsMs();
            fieldCnt++;
        }
        if (this.topHero != null) {
            Struct.SimpleHeroList.Builder tmpBuilder = Struct.SimpleHeroList.newBuilder();
            final int tmpFieldCnt = this.topHero.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTopHero(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTopHero();
            }
        }  else if (builder.hasTopHero()) {
            // 清理TopHero
            builder.clearTopHero();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanMember.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBOAT)) {
            builder.setComboat(this.getComboat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ENTERTIME)) {
            builder.setEnterTime(this.getEnterTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISONLINE)) {
            builder.setIsOnline(this.getIsOnline());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESLASTCALTSMS)) {
            builder.setResLastCalTsMs(this.getResLastCalTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToSs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_NEXTCANBEOWNERTSMS)) {
            builder.setNextCanBeOwnerTsMs(this.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_KILLSCORE)) {
            builder.setKillScore(this.getKillScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            final boolean needClear = !builder.hasRedDotMap();
            final int tmpFieldCnt = this.redDotMap.copyChangeToSs(builder.getRedDotMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTONLINETSMS)) {
            builder.setLastOnlineTsMs(this.getLastOnlineTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOPHERO) && this.topHero != null) {
            final boolean needClear = !builder.hasTopHero();
            final int tmpFieldCnt = this.topHero.copyChangeToSs(builder.getTopHeroBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTopHero();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanMember proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasComboat()) {
            this.innerSetComboat(proto.getComboat());
        } else {
            this.innerSetComboat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasEnterTime()) {
            this.innerSetEnterTime(proto.getEnterTime());
        } else {
            this.innerSetEnterTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIsOnline()) {
            this.innerSetIsOnline(proto.getIsOnline());
        } else {
            this.innerSetIsOnline(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasResLastCalTsMs()) {
            this.innerSetResLastCalTsMs(proto.getResLastCalTsMs());
        } else {
            this.innerSetResLastCalTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromSs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromSs(proto.getResources());
            }
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasNextCanBeOwnerTsMs()) {
            this.innerSetNextCanBeOwnerTsMs(proto.getNextCanBeOwnerTsMs());
        } else {
            this.innerSetNextCanBeOwnerTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasKillScore()) {
            this.innerSetKillScore(proto.getKillScore());
        } else {
            this.innerSetKillScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeFromSs(proto.getRedDotMap());
        } else {
            if (this.redDotMap != null) {
                this.redDotMap.mergeFromSs(proto.getRedDotMap());
            }
        }
        if (proto.hasLastOnlineTsMs()) {
            this.innerSetLastOnlineTsMs(proto.getLastOnlineTsMs());
        } else {
            this.innerSetLastOnlineTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTopHero()) {
            this.getTopHero().mergeFromSs(proto.getTopHero());
        } else {
            if (this.topHero != null) {
                this.topHero.mergeFromSs(proto.getTopHero());
            }
        }
        this.markAll();
        return ClanMemberProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanMember proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasComboat()) {
            this.setComboat(proto.getComboat());
            fieldCnt++;
        }
        if (proto.hasEnterTime()) {
            this.setEnterTime(proto.getEnterTime());
            fieldCnt++;
        }
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasIsOnline()) {
            this.setIsOnline(proto.getIsOnline());
            fieldCnt++;
        }
        if (proto.hasResLastCalTsMs()) {
            this.setResLastCalTsMs(proto.getResLastCalTsMs());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromSs(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasNextCanBeOwnerTsMs()) {
            this.setNextCanBeOwnerTsMs(proto.getNextCanBeOwnerTsMs());
            fieldCnt++;
        }
        if (proto.hasKillScore()) {
            this.setKillScore(proto.getKillScore());
            fieldCnt++;
        }
        if (proto.hasRedDotMap()) {
            this.getRedDotMap().mergeChangeFromSs(proto.getRedDotMap());
            fieldCnt++;
        }
        if (proto.hasLastOnlineTsMs()) {
            this.setLastOnlineTsMs(proto.getLastOnlineTsMs());
            fieldCnt++;
        }
        if (proto.hasTopHero()) {
            this.getTopHero().mergeChangeFromSs(proto.getTopHero());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanMember.Builder builder = ClanMember.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            this.resources.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMAP) && this.redDotMap != null) {
            this.redDotMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TOPHERO) && this.topHero != null) {
            this.topHero.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.resources != null) {
            this.resources.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.redDotMap != null) {
            this.redDotMap.markAll();
        }
        if (this.topHero != null) {
            this.topHero.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanMemberProp)) {
            return false;
        }
        final ClanMemberProp otherNode = (ClanMemberProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.comboat != otherNode.comboat) {
            return false;
        }
        if (this.enterTime != otherNode.enterTime) {
            return false;
        }
        if (this.staffId != otherNode.staffId) {
            return false;
        }
        if (this.isOnline != otherNode.isOnline) {
            return false;
        }
        if (this.resLastCalTsMs != otherNode.resLastCalTsMs) {
            return false;
        }
        if (!this.getResources().compareDataTo(otherNode.getResources())) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (this.nextCanBeOwnerTsMs != otherNode.nextCanBeOwnerTsMs) {
            return false;
        }
        if (this.killScore != otherNode.killScore) {
            return false;
        }
        if (!this.getRedDotMap().compareDataTo(otherNode.getRedDotMap())) {
            return false;
        }
        if (this.lastOnlineTsMs != otherNode.lastOnlineTsMs) {
            return false;
        }
        if (!this.getTopHero().compareDataTo(otherNode.getTopHero())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 51;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructBattlePB.BattleSoldierDetailListPB;
import com.yorha.proto.StructBattle.BattleSoldierDetailList;
import com.yorha.proto.StructBattlePB.BattleSoldierDetailPB;
import com.yorha.proto.StructBattle.BattleSoldierDetail;

/**
 * <AUTHOR> auto gen
 */
public class BattleSoldierDetailListProp extends AbstractListNode<BattleSoldierDetailProp> {
    /**
     * Create a BattleSoldierDetailListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public BattleSoldierDetailListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to BattleSoldierDetailListProp
     *
     * @return new object
     */
    @Override
    public BattleSoldierDetailProp addEmptyValue() {
        final BattleSoldierDetailProp newProp = new BattleSoldierDetailProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleSoldierDetailListPB.Builder getCopyCsBuilder() {
        final BattleSoldierDetailListPB.Builder builder = BattleSoldierDetailListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(BattleSoldierDetailListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleSoldierDetailListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleSoldierDetailProp v : this) {
            final BattleSoldierDetailPB.Builder itemBuilder = BattleSoldierDetailPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleSoldierDetailListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(BattleSoldierDetailListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return BattleSoldierDetailListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(BattleSoldierDetailListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleSoldierDetailPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return BattleSoldierDetailListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(BattleSoldierDetailListPB proto) {
        return mergeFromCs(proto);
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleSoldierDetailList.Builder getCopySsBuilder() {
        final BattleSoldierDetailList.Builder builder = BattleSoldierDetailList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(BattleSoldierDetailList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleSoldierDetailListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleSoldierDetailProp v : this) {
            final BattleSoldierDetail.Builder itemBuilder = BattleSoldierDetail.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleSoldierDetailListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(BattleSoldierDetailList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return BattleSoldierDetailListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(BattleSoldierDetailList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleSoldierDetail v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return BattleSoldierDetailListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(BattleSoldierDetailList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        BattleSoldierDetailList.Builder builder = BattleSoldierDetailList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.ClientStageInfo;
import com.yorha.proto.PlayerPB.ClientStageInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClientStageInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_GROUPID = 0;
    public static final int FIELD_INDEX_STAGEID = 1;
    public static final int FIELD_INDEX_FLAG = 2;

    public static final int FIELD_COUNT = 3;

    private long markBits0 = 0L;

    private int groupId = Constant.DEFAULT_INT_VALUE;
    private int stageId = Constant.DEFAULT_INT_VALUE;
    private boolean flag = Constant.DEFAULT_BOOLEAN_VALUE;

    public ClientStageInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClientStageInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get groupId
     *
     * @return groupId value
     */
    public int getGroupId() {
        return this.groupId;
    }

    /**
     * set groupId && set marked
     *
     * @param groupId new value
     * @return current object
     */
    public ClientStageInfoProp setGroupId(int groupId) {
        if (this.groupId != groupId) {
            this.mark(FIELD_INDEX_GROUPID);
            this.groupId = groupId;
        }
        return this;
    }

    /**
     * inner set groupId
     *
     * @param groupId new value
     */
    private void innerSetGroupId(int groupId) {
        this.groupId = groupId;
    }

    /**
     * get stageId
     *
     * @return stageId value
     */
    public int getStageId() {
        return this.stageId;
    }

    /**
     * set stageId && set marked
     *
     * @param stageId new value
     * @return current object
     */
    public ClientStageInfoProp setStageId(int stageId) {
        if (this.stageId != stageId) {
            this.mark(FIELD_INDEX_STAGEID);
            this.stageId = stageId;
        }
        return this;
    }

    /**
     * inner set stageId
     *
     * @param stageId new value
     */
    private void innerSetStageId(int stageId) {
        this.stageId = stageId;
    }

    /**
     * get flag
     *
     * @return flag value
     */
    public boolean getFlag() {
        return this.flag;
    }

    /**
     * set flag && set marked
     *
     * @param flag new value
     * @return current object
     */
    public ClientStageInfoProp setFlag(boolean flag) {
        if (this.flag != flag) {
            this.mark(FIELD_INDEX_FLAG);
            this.flag = flag;
        }
        return this;
    }

    /**
     * inner set flag
     *
     * @param flag new value
     */
    private void innerSetFlag(boolean flag) {
        this.flag = flag;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClientStageInfoPB.Builder getCopyCsBuilder() {
        final ClientStageInfoPB.Builder builder = ClientStageInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClientStageInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getStageId() != 0) {
            builder.setStageId(this.getStageId());
            fieldCnt++;
        }  else if (builder.hasStageId()) {
            // 清理StageId
            builder.clearStageId();
            fieldCnt++;
        }
        if (this.getFlag()) {
            builder.setFlag(this.getFlag());
            fieldCnt++;
        }  else if (builder.hasFlag()) {
            // 清理Flag
            builder.clearFlag();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClientStageInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEID)) {
            builder.setStageId(this.getStageId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAG)) {
            builder.setFlag(this.getFlag());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClientStageInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEID)) {
            builder.setStageId(this.getStageId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAG)) {
            builder.setFlag(this.getFlag());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClientStageInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStageId()) {
            this.innerSetStageId(proto.getStageId());
        } else {
            this.innerSetStageId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlag()) {
            this.innerSetFlag(proto.getFlag());
        } else {
            this.innerSetFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ClientStageInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClientStageInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasStageId()) {
            this.setStageId(proto.getStageId());
            fieldCnt++;
        }
        if (proto.hasFlag()) {
            this.setFlag(proto.getFlag());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClientStageInfo.Builder getCopyDbBuilder() {
        final ClientStageInfo.Builder builder = ClientStageInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClientStageInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getStageId() != 0) {
            builder.setStageId(this.getStageId());
            fieldCnt++;
        }  else if (builder.hasStageId()) {
            // 清理StageId
            builder.clearStageId();
            fieldCnt++;
        }
        if (this.getFlag()) {
            builder.setFlag(this.getFlag());
            fieldCnt++;
        }  else if (builder.hasFlag()) {
            // 清理Flag
            builder.clearFlag();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClientStageInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEID)) {
            builder.setStageId(this.getStageId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAG)) {
            builder.setFlag(this.getFlag());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClientStageInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStageId()) {
            this.innerSetStageId(proto.getStageId());
        } else {
            this.innerSetStageId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlag()) {
            this.innerSetFlag(proto.getFlag());
        } else {
            this.innerSetFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ClientStageInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClientStageInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasStageId()) {
            this.setStageId(proto.getStageId());
            fieldCnt++;
        }
        if (proto.hasFlag()) {
            this.setFlag(proto.getFlag());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClientStageInfo.Builder getCopySsBuilder() {
        final ClientStageInfo.Builder builder = ClientStageInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClientStageInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGroupId() != 0) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }  else if (builder.hasGroupId()) {
            // 清理GroupId
            builder.clearGroupId();
            fieldCnt++;
        }
        if (this.getStageId() != 0) {
            builder.setStageId(this.getStageId());
            fieldCnt++;
        }  else if (builder.hasStageId()) {
            // 清理StageId
            builder.clearStageId();
            fieldCnt++;
        }
        if (this.getFlag()) {
            builder.setFlag(this.getFlag());
            fieldCnt++;
        }  else if (builder.hasFlag()) {
            // 清理Flag
            builder.clearFlag();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClientStageInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GROUPID)) {
            builder.setGroupId(this.getGroupId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAGEID)) {
            builder.setStageId(this.getStageId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAG)) {
            builder.setFlag(this.getFlag());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClientStageInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGroupId()) {
            this.innerSetGroupId(proto.getGroupId());
        } else {
            this.innerSetGroupId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStageId()) {
            this.innerSetStageId(proto.getStageId());
        } else {
            this.innerSetStageId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlag()) {
            this.innerSetFlag(proto.getFlag());
        } else {
            this.innerSetFlag(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return ClientStageInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClientStageInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGroupId()) {
            this.setGroupId(proto.getGroupId());
            fieldCnt++;
        }
        if (proto.hasStageId()) {
            this.setStageId(proto.getStageId());
            fieldCnt++;
        }
        if (proto.hasFlag()) {
            this.setFlag(proto.getFlag());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClientStageInfo.Builder builder = ClientStageInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.groupId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClientStageInfoProp)) {
            return false;
        }
        final ClientStageInfoProp otherNode = (ClientStageInfoProp) node;
        if (this.groupId != otherNode.groupId) {
            return false;
        }
        if (this.stageId != otherNode.stageId) {
            return false;
        }
        if (this.flag != otherNode.flag) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 61;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
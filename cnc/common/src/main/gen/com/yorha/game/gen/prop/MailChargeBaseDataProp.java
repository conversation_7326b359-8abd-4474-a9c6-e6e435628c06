package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructMail.MailChargeBaseData;
import com.yorha.proto.StructMailPB.MailChargeBaseDataPB;


/**
 * <AUTHOR> auto gen
 */
public class MailChargeBaseDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CHARGEBASEID = 0;
    public static final int FIELD_INDEX_IFDOUBLE = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int chargeBaseId = Constant.DEFAULT_INT_VALUE;
    private boolean ifDouble = Constant.DEFAULT_BOOLEAN_VALUE;

    public MailChargeBaseDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MailChargeBaseDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get chargeBaseId
     *
     * @return chargeBaseId value
     */
    public int getChargeBaseId() {
        return this.chargeBaseId;
    }

    /**
     * set chargeBaseId && set marked
     *
     * @param chargeBaseId new value
     * @return current object
     */
    public MailChargeBaseDataProp setChargeBaseId(int chargeBaseId) {
        if (this.chargeBaseId != chargeBaseId) {
            this.mark(FIELD_INDEX_CHARGEBASEID);
            this.chargeBaseId = chargeBaseId;
        }
        return this;
    }

    /**
     * inner set chargeBaseId
     *
     * @param chargeBaseId new value
     */
    private void innerSetChargeBaseId(int chargeBaseId) {
        this.chargeBaseId = chargeBaseId;
    }

    /**
     * get ifDouble
     *
     * @return ifDouble value
     */
    public boolean getIfDouble() {
        return this.ifDouble;
    }

    /**
     * set ifDouble && set marked
     *
     * @param ifDouble new value
     * @return current object
     */
    public MailChargeBaseDataProp setIfDouble(boolean ifDouble) {
        if (this.ifDouble != ifDouble) {
            this.mark(FIELD_INDEX_IFDOUBLE);
            this.ifDouble = ifDouble;
        }
        return this;
    }

    /**
     * inner set ifDouble
     *
     * @param ifDouble new value
     */
    private void innerSetIfDouble(boolean ifDouble) {
        this.ifDouble = ifDouble;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailChargeBaseDataPB.Builder getCopyCsBuilder() {
        final MailChargeBaseDataPB.Builder builder = MailChargeBaseDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MailChargeBaseDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChargeBaseId() != 0) {
            builder.setChargeBaseId(this.getChargeBaseId());
            fieldCnt++;
        }  else if (builder.hasChargeBaseId()) {
            // 清理ChargeBaseId
            builder.clearChargeBaseId();
            fieldCnt++;
        }
        if (this.getIfDouble()) {
            builder.setIfDouble(this.getIfDouble());
            fieldCnt++;
        }  else if (builder.hasIfDouble()) {
            // 清理IfDouble
            builder.clearIfDouble();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MailChargeBaseDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEBASEID)) {
            builder.setChargeBaseId(this.getChargeBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IFDOUBLE)) {
            builder.setIfDouble(this.getIfDouble());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MailChargeBaseDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEBASEID)) {
            builder.setChargeBaseId(this.getChargeBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IFDOUBLE)) {
            builder.setIfDouble(this.getIfDouble());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MailChargeBaseDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChargeBaseId()) {
            this.innerSetChargeBaseId(proto.getChargeBaseId());
        } else {
            this.innerSetChargeBaseId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIfDouble()) {
            this.innerSetIfDouble(proto.getIfDouble());
        } else {
            this.innerSetIfDouble(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MailChargeBaseDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MailChargeBaseDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChargeBaseId()) {
            this.setChargeBaseId(proto.getChargeBaseId());
            fieldCnt++;
        }
        if (proto.hasIfDouble()) {
            this.setIfDouble(proto.getIfDouble());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailChargeBaseData.Builder getCopyDbBuilder() {
        final MailChargeBaseData.Builder builder = MailChargeBaseData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MailChargeBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChargeBaseId() != 0) {
            builder.setChargeBaseId(this.getChargeBaseId());
            fieldCnt++;
        }  else if (builder.hasChargeBaseId()) {
            // 清理ChargeBaseId
            builder.clearChargeBaseId();
            fieldCnt++;
        }
        if (this.getIfDouble()) {
            builder.setIfDouble(this.getIfDouble());
            fieldCnt++;
        }  else if (builder.hasIfDouble()) {
            // 清理IfDouble
            builder.clearIfDouble();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MailChargeBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEBASEID)) {
            builder.setChargeBaseId(this.getChargeBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IFDOUBLE)) {
            builder.setIfDouble(this.getIfDouble());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MailChargeBaseData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChargeBaseId()) {
            this.innerSetChargeBaseId(proto.getChargeBaseId());
        } else {
            this.innerSetChargeBaseId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIfDouble()) {
            this.innerSetIfDouble(proto.getIfDouble());
        } else {
            this.innerSetIfDouble(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MailChargeBaseDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MailChargeBaseData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChargeBaseId()) {
            this.setChargeBaseId(proto.getChargeBaseId());
            fieldCnt++;
        }
        if (proto.hasIfDouble()) {
            this.setIfDouble(proto.getIfDouble());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MailChargeBaseData.Builder getCopySsBuilder() {
        final MailChargeBaseData.Builder builder = MailChargeBaseData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MailChargeBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getChargeBaseId() != 0) {
            builder.setChargeBaseId(this.getChargeBaseId());
            fieldCnt++;
        }  else if (builder.hasChargeBaseId()) {
            // 清理ChargeBaseId
            builder.clearChargeBaseId();
            fieldCnt++;
        }
        if (this.getIfDouble()) {
            builder.setIfDouble(this.getIfDouble());
            fieldCnt++;
        }  else if (builder.hasIfDouble()) {
            // 清理IfDouble
            builder.clearIfDouble();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MailChargeBaseData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CHARGEBASEID)) {
            builder.setChargeBaseId(this.getChargeBaseId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_IFDOUBLE)) {
            builder.setIfDouble(this.getIfDouble());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MailChargeBaseData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasChargeBaseId()) {
            this.innerSetChargeBaseId(proto.getChargeBaseId());
        } else {
            this.innerSetChargeBaseId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasIfDouble()) {
            this.innerSetIfDouble(proto.getIfDouble());
        } else {
            this.innerSetIfDouble(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return MailChargeBaseDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MailChargeBaseData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasChargeBaseId()) {
            this.setChargeBaseId(proto.getChargeBaseId());
            fieldCnt++;
        }
        if (proto.hasIfDouble()) {
            this.setIfDouble(proto.getIfDouble());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MailChargeBaseData.Builder builder = MailChargeBaseData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MailChargeBaseDataProp)) {
            return false;
        }
        final MailChargeBaseDataProp otherNode = (MailChargeBaseDataProp) node;
        if (this.chargeBaseId != otherNode.chargeBaseId) {
            return false;
        }
        if (this.ifDouble != otherNode.ifDouble) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerBattlePassModel;
import com.yorha.proto.Basic;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerBattlePassModelPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerBattlePassModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BPTYPE = 0;
    public static final int FIELD_INDEX_BPCONFIGID = 1;
    public static final int FIELD_INDEX_EXP = 2;
    public static final int FIELD_INDEX_DAILYEXP = 3;
    public static final int FIELD_INDEX_LEVEL = 4;
    public static final int FIELD_INDEX_OPENTIME = 5;
    public static final int FIELD_INDEX_DURATION = 6;
    public static final int FIELD_INDEX_CLAIMABLESILVER = 7;
    public static final int FIELD_INDEX_CLAIMABLEGOLD = 8;
    public static final int FIELD_INDEX_OPENDAILYEXPBOX = 9;
    public static final int FIELD_INDEX_LASTDAILYREFRESHTSMS = 10;
    public static final int FIELD_INDEX_TASKS = 11;
    public static final int FIELD_INDEX_TSMS = 12;

    public static final int FIELD_COUNT = 13;

    private long markBits0 = 0L;

    private BattltPassType bpType = BattltPassType.forNumber(0);
    private int bpConfigId = Constant.DEFAULT_INT_VALUE;
    private int exp = Constant.DEFAULT_INT_VALUE;
    private int dailyExp = Constant.DEFAULT_INT_VALUE;
    private int level = Constant.DEFAULT_INT_VALUE;
    private int openTime = Constant.DEFAULT_INT_VALUE;
    private int duration = Constant.DEFAULT_INT_VALUE;
    private Int64ListProp claimableSilver = null;
    private Int64ListProp claimableGold = null;
    private boolean openDailyExpBox = Constant.DEFAULT_BOOLEAN_VALUE;
    private long lastDailyRefreshTsMs = Constant.DEFAULT_LONG_VALUE;
    private Int32BattlePassTaskInfoMapProp tasks = null;
    private long tsMs = Constant.DEFAULT_LONG_VALUE;

    public PlayerBattlePassModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerBattlePassModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get bpType
     *
     * @return bpType value
     */
    public BattltPassType getBpType() {
        return this.bpType;
    }

    /**
     * set bpType && set marked
     *
     * @param bpType new value
     * @return current object
     */
    public PlayerBattlePassModelProp setBpType(BattltPassType bpType) {
        if (bpType == null) {
            throw new NullPointerException();
        }
        if (this.bpType != bpType) {
            this.mark(FIELD_INDEX_BPTYPE);
            this.bpType = bpType;
        }
        return this;
    }

    /**
     * inner set bpType
     *
     * @param bpType new value
     */
    private void innerSetBpType(BattltPassType bpType) {
        this.bpType = bpType;
    }

    /**
     * get bpConfigId
     *
     * @return bpConfigId value
     */
    public int getBpConfigId() {
        return this.bpConfigId;
    }

    /**
     * set bpConfigId && set marked
     *
     * @param bpConfigId new value
     * @return current object
     */
    public PlayerBattlePassModelProp setBpConfigId(int bpConfigId) {
        if (this.bpConfigId != bpConfigId) {
            this.mark(FIELD_INDEX_BPCONFIGID);
            this.bpConfigId = bpConfigId;
        }
        return this;
    }

    /**
     * inner set bpConfigId
     *
     * @param bpConfigId new value
     */
    private void innerSetBpConfigId(int bpConfigId) {
        this.bpConfigId = bpConfigId;
    }

    /**
     * get exp
     *
     * @return exp value
     */
    public int getExp() {
        return this.exp;
    }

    /**
     * set exp && set marked
     *
     * @param exp new value
     * @return current object
     */
    public PlayerBattlePassModelProp setExp(int exp) {
        if (this.exp != exp) {
            this.mark(FIELD_INDEX_EXP);
            this.exp = exp;
        }
        return this;
    }

    /**
     * inner set exp
     *
     * @param exp new value
     */
    private void innerSetExp(int exp) {
        this.exp = exp;
    }

    /**
     * get dailyExp
     *
     * @return dailyExp value
     */
    public int getDailyExp() {
        return this.dailyExp;
    }

    /**
     * set dailyExp && set marked
     *
     * @param dailyExp new value
     * @return current object
     */
    public PlayerBattlePassModelProp setDailyExp(int dailyExp) {
        if (this.dailyExp != dailyExp) {
            this.mark(FIELD_INDEX_DAILYEXP);
            this.dailyExp = dailyExp;
        }
        return this;
    }

    /**
     * inner set dailyExp
     *
     * @param dailyExp new value
     */
    private void innerSetDailyExp(int dailyExp) {
        this.dailyExp = dailyExp;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public PlayerBattlePassModelProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get openTime
     *
     * @return openTime value
     */
    public int getOpenTime() {
        return this.openTime;
    }

    /**
     * set openTime && set marked
     *
     * @param openTime new value
     * @return current object
     */
    public PlayerBattlePassModelProp setOpenTime(int openTime) {
        if (this.openTime != openTime) {
            this.mark(FIELD_INDEX_OPENTIME);
            this.openTime = openTime;
        }
        return this;
    }

    /**
     * inner set openTime
     *
     * @param openTime new value
     */
    private void innerSetOpenTime(int openTime) {
        this.openTime = openTime;
    }

    /**
     * get duration
     *
     * @return duration value
     */
    public int getDuration() {
        return this.duration;
    }

    /**
     * set duration && set marked
     *
     * @param duration new value
     * @return current object
     */
    public PlayerBattlePassModelProp setDuration(int duration) {
        if (this.duration != duration) {
            this.mark(FIELD_INDEX_DURATION);
            this.duration = duration;
        }
        return this;
    }

    /**
     * inner set duration
     *
     * @param duration new value
     */
    private void innerSetDuration(int duration) {
        this.duration = duration;
    }

    /**
     * get claimableSilver
     *
     * @return claimableSilver value
     */
    public Int64ListProp getClaimableSilver() {
        if (this.claimableSilver == null) {
            this.claimableSilver = new Int64ListProp(this, FIELD_INDEX_CLAIMABLESILVER);
        }
        return this.claimableSilver;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addClaimableSilver(Long v) {
        this.getClaimableSilver().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getClaimableSilverIndex(int index) {
        return this.getClaimableSilver().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeClaimableSilver(Long v) {
        if (this.claimableSilver == null) {
            return null;
        }
        if(this.claimableSilver.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getClaimableSilverSize() {
        if (this.claimableSilver == null) {
            return 0;
        }
        return this.claimableSilver.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isClaimableSilverEmpty() {
        if (this.claimableSilver == null) {
            return true;
        }
        return this.getClaimableSilver().isEmpty();
    }

    /**
     * clear list
     */
    public void clearClaimableSilver() {
        this.getClaimableSilver().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeClaimableSilverIndex(int index) {
        return this.getClaimableSilver().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setClaimableSilverIndex(int index, Long v) {
        return this.getClaimableSilver().set(index, v);
    }
    /**
     * get claimableGold
     *
     * @return claimableGold value
     */
    public Int64ListProp getClaimableGold() {
        if (this.claimableGold == null) {
            this.claimableGold = new Int64ListProp(this, FIELD_INDEX_CLAIMABLEGOLD);
        }
        return this.claimableGold;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addClaimableGold(Long v) {
        this.getClaimableGold().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getClaimableGoldIndex(int index) {
        return this.getClaimableGold().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeClaimableGold(Long v) {
        if (this.claimableGold == null) {
            return null;
        }
        if(this.claimableGold.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getClaimableGoldSize() {
        if (this.claimableGold == null) {
            return 0;
        }
        return this.claimableGold.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isClaimableGoldEmpty() {
        if (this.claimableGold == null) {
            return true;
        }
        return this.getClaimableGold().isEmpty();
    }

    /**
     * clear list
     */
    public void clearClaimableGold() {
        this.getClaimableGold().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeClaimableGoldIndex(int index) {
        return this.getClaimableGold().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setClaimableGoldIndex(int index, Long v) {
        return this.getClaimableGold().set(index, v);
    }
    /**
     * get openDailyExpBox
     *
     * @return openDailyExpBox value
     */
    public boolean getOpenDailyExpBox() {
        return this.openDailyExpBox;
    }

    /**
     * set openDailyExpBox && set marked
     *
     * @param openDailyExpBox new value
     * @return current object
     */
    public PlayerBattlePassModelProp setOpenDailyExpBox(boolean openDailyExpBox) {
        if (this.openDailyExpBox != openDailyExpBox) {
            this.mark(FIELD_INDEX_OPENDAILYEXPBOX);
            this.openDailyExpBox = openDailyExpBox;
        }
        return this;
    }

    /**
     * inner set openDailyExpBox
     *
     * @param openDailyExpBox new value
     */
    private void innerSetOpenDailyExpBox(boolean openDailyExpBox) {
        this.openDailyExpBox = openDailyExpBox;
    }

    /**
     * get lastDailyRefreshTsMs
     *
     * @return lastDailyRefreshTsMs value
     */
    public long getLastDailyRefreshTsMs() {
        return this.lastDailyRefreshTsMs;
    }

    /**
     * set lastDailyRefreshTsMs && set marked
     *
     * @param lastDailyRefreshTsMs new value
     * @return current object
     */
    public PlayerBattlePassModelProp setLastDailyRefreshTsMs(long lastDailyRefreshTsMs) {
        if (this.lastDailyRefreshTsMs != lastDailyRefreshTsMs) {
            this.mark(FIELD_INDEX_LASTDAILYREFRESHTSMS);
            this.lastDailyRefreshTsMs = lastDailyRefreshTsMs;
        }
        return this;
    }

    /**
     * inner set lastDailyRefreshTsMs
     *
     * @param lastDailyRefreshTsMs new value
     */
    private void innerSetLastDailyRefreshTsMs(long lastDailyRefreshTsMs) {
        this.lastDailyRefreshTsMs = lastDailyRefreshTsMs;
    }

    /**
     * get tasks
     *
     * @return tasks value
     */
    public Int32BattlePassTaskInfoMapProp getTasks() {
        if (this.tasks == null) {
            this.tasks = new Int32BattlePassTaskInfoMapProp(this, FIELD_INDEX_TASKS);
        }
        return this.tasks;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTasksV(BattlePassTaskInfoProp v) {
        this.getTasks().put(v.getGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public BattlePassTaskInfoProp addEmptyTasks(Integer k) {
        return this.getTasks().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTasksSize() {
        if (this.tasks == null) {
            return 0;
        }
        return this.tasks.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTasksEmpty() {
        if (this.tasks == null) {
            return true;
        }
        return this.tasks.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public BattlePassTaskInfoProp getTasksV(Integer k) {
        if (this.tasks == null || !this.tasks.containsKey(k)) {
            return null;
        }
        return this.tasks.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTasks() {
        if (this.tasks != null) {
            this.tasks.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTasksV(Integer k) {
        if (this.tasks != null) {
            this.tasks.remove(k);
        }
    }
    /**
     * get tsMs
     *
     * @return tsMs value
     */
    public long getTsMs() {
        return this.tsMs;
    }

    /**
     * set tsMs && set marked
     *
     * @param tsMs new value
     * @return current object
     */
    public PlayerBattlePassModelProp setTsMs(long tsMs) {
        if (this.tsMs != tsMs) {
            this.mark(FIELD_INDEX_TSMS);
            this.tsMs = tsMs;
        }
        return this;
    }

    /**
     * inner set tsMs
     *
     * @param tsMs new value
     */
    private void innerSetTsMs(long tsMs) {
        this.tsMs = tsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBattlePassModelPB.Builder getCopyCsBuilder() {
        final PlayerBattlePassModelPB.Builder builder = PlayerBattlePassModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerBattlePassModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBpType() != BattltPassType.forNumber(0)) {
            builder.setBpType(this.getBpType());
            fieldCnt++;
        }  else if (builder.hasBpType()) {
            // 清理BpType
            builder.clearBpType();
            fieldCnt++;
        }
        if (this.getBpConfigId() != 0) {
            builder.setBpConfigId(this.getBpConfigId());
            fieldCnt++;
        }  else if (builder.hasBpConfigId()) {
            // 清理BpConfigId
            builder.clearBpConfigId();
            fieldCnt++;
        }
        if (this.getExp() != 0) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }  else if (builder.hasExp()) {
            // 清理Exp
            builder.clearExp();
            fieldCnt++;
        }
        if (this.getDailyExp() != 0) {
            builder.setDailyExp(this.getDailyExp());
            fieldCnt++;
        }  else if (builder.hasDailyExp()) {
            // 清理DailyExp
            builder.clearDailyExp();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getOpenTime() != 0) {
            builder.setOpenTime(this.getOpenTime());
            fieldCnt++;
        }  else if (builder.hasOpenTime()) {
            // 清理OpenTime
            builder.clearOpenTime();
            fieldCnt++;
        }
        if (this.getDuration() != 0) {
            builder.setDuration(this.getDuration());
            fieldCnt++;
        }  else if (builder.hasDuration()) {
            // 清理Duration
            builder.clearDuration();
            fieldCnt++;
        }
        if (this.claimableSilver != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.claimableSilver.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClaimableSilver(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClaimableSilver();
            }
        }  else if (builder.hasClaimableSilver()) {
            // 清理ClaimableSilver
            builder.clearClaimableSilver();
            fieldCnt++;
        }
        if (this.claimableGold != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.claimableGold.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClaimableGold(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClaimableGold();
            }
        }  else if (builder.hasClaimableGold()) {
            // 清理ClaimableGold
            builder.clearClaimableGold();
            fieldCnt++;
        }
        if (this.getOpenDailyExpBox()) {
            builder.setOpenDailyExpBox(this.getOpenDailyExpBox());
            fieldCnt++;
        }  else if (builder.hasOpenDailyExpBox()) {
            // 清理OpenDailyExpBox
            builder.clearOpenDailyExpBox();
            fieldCnt++;
        }
        if (this.tasks != null) {
            PlayerPB.Int32BattlePassTaskInfoMapPB.Builder tmpBuilder = PlayerPB.Int32BattlePassTaskInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.tasks.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTasks();
            }
        }  else if (builder.hasTasks()) {
            // 清理Tasks
            builder.clearTasks();
            fieldCnt++;
        }
        if (this.getTsMs() != 0L) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }  else if (builder.hasTsMs()) {
            // 清理TsMs
            builder.clearTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerBattlePassModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPTYPE)) {
            builder.setBpType(this.getBpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPCONFIGID)) {
            builder.setBpConfigId(this.getBpConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYEXP)) {
            builder.setDailyExp(this.getDailyExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTIME)) {
            builder.setOpenTime(this.getOpenTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DURATION)) {
            builder.setDuration(this.getDuration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLESILVER) && this.claimableSilver != null) {
            final boolean needClear = !builder.hasClaimableSilver();
            final int tmpFieldCnt = this.claimableSilver.copyChangeToCs(builder.getClaimableSilverBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableSilver();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLEGOLD) && this.claimableGold != null) {
            final boolean needClear = !builder.hasClaimableGold();
            final int tmpFieldCnt = this.claimableGold.copyChangeToCs(builder.getClaimableGoldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableGold();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENDAILYEXPBOX)) {
            builder.setOpenDailyExpBox(this.getOpenDailyExpBox());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToCs(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerBattlePassModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPTYPE)) {
            builder.setBpType(this.getBpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPCONFIGID)) {
            builder.setBpConfigId(this.getBpConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYEXP)) {
            builder.setDailyExp(this.getDailyExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTIME)) {
            builder.setOpenTime(this.getOpenTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DURATION)) {
            builder.setDuration(this.getDuration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLESILVER) && this.claimableSilver != null) {
            final boolean needClear = !builder.hasClaimableSilver();
            final int tmpFieldCnt = this.claimableSilver.copyChangeToCs(builder.getClaimableSilverBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableSilver();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLEGOLD) && this.claimableGold != null) {
            final boolean needClear = !builder.hasClaimableGold();
            final int tmpFieldCnt = this.claimableGold.copyChangeToCs(builder.getClaimableGoldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableGold();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENDAILYEXPBOX)) {
            builder.setOpenDailyExpBox(this.getOpenDailyExpBox());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToAndClearDeleteKeysCs(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerBattlePassModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBpType()) {
            this.innerSetBpType(proto.getBpType());
        } else {
            this.innerSetBpType(BattltPassType.forNumber(0));
        }
        if (proto.hasBpConfigId()) {
            this.innerSetBpConfigId(proto.getBpConfigId());
        } else {
            this.innerSetBpConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExp()) {
            this.innerSetExp(proto.getExp());
        } else {
            this.innerSetExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyExp()) {
            this.innerSetDailyExp(proto.getDailyExp());
        } else {
            this.innerSetDailyExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpenTime()) {
            this.innerSetOpenTime(proto.getOpenTime());
        } else {
            this.innerSetOpenTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDuration()) {
            this.innerSetDuration(proto.getDuration());
        } else {
            this.innerSetDuration(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClaimableSilver()) {
            this.getClaimableSilver().mergeFromCs(proto.getClaimableSilver());
        } else {
            if (this.claimableSilver != null) {
                this.claimableSilver.mergeFromCs(proto.getClaimableSilver());
            }
        }
        if (proto.hasClaimableGold()) {
            this.getClaimableGold().mergeFromCs(proto.getClaimableGold());
        } else {
            if (this.claimableGold != null) {
                this.claimableGold.mergeFromCs(proto.getClaimableGold());
            }
        }
        if (proto.hasOpenDailyExpBox()) {
            this.innerSetOpenDailyExpBox(proto.getOpenDailyExpBox());
        } else {
            this.innerSetOpenDailyExpBox(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeFromCs(proto.getTasks());
        } else {
            if (this.tasks != null) {
                this.tasks.mergeFromCs(proto.getTasks());
            }
        }
        if (proto.hasTsMs()) {
            this.innerSetTsMs(proto.getTsMs());
        } else {
            this.innerSetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerBattlePassModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerBattlePassModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBpType()) {
            this.setBpType(proto.getBpType());
            fieldCnt++;
        }
        if (proto.hasBpConfigId()) {
            this.setBpConfigId(proto.getBpConfigId());
            fieldCnt++;
        }
        if (proto.hasExp()) {
            this.setExp(proto.getExp());
            fieldCnt++;
        }
        if (proto.hasDailyExp()) {
            this.setDailyExp(proto.getDailyExp());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasOpenTime()) {
            this.setOpenTime(proto.getOpenTime());
            fieldCnt++;
        }
        if (proto.hasDuration()) {
            this.setDuration(proto.getDuration());
            fieldCnt++;
        }
        if (proto.hasClaimableSilver()) {
            this.getClaimableSilver().mergeChangeFromCs(proto.getClaimableSilver());
            fieldCnt++;
        }
        if (proto.hasClaimableGold()) {
            this.getClaimableGold().mergeChangeFromCs(proto.getClaimableGold());
            fieldCnt++;
        }
        if (proto.hasOpenDailyExpBox()) {
            this.setOpenDailyExpBox(proto.getOpenDailyExpBox());
            fieldCnt++;
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeChangeFromCs(proto.getTasks());
            fieldCnt++;
        }
        if (proto.hasTsMs()) {
            this.setTsMs(proto.getTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBattlePassModel.Builder getCopyDbBuilder() {
        final PlayerBattlePassModel.Builder builder = PlayerBattlePassModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerBattlePassModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBpType() != BattltPassType.forNumber(0)) {
            builder.setBpType(this.getBpType());
            fieldCnt++;
        }  else if (builder.hasBpType()) {
            // 清理BpType
            builder.clearBpType();
            fieldCnt++;
        }
        if (this.getBpConfigId() != 0) {
            builder.setBpConfigId(this.getBpConfigId());
            fieldCnt++;
        }  else if (builder.hasBpConfigId()) {
            // 清理BpConfigId
            builder.clearBpConfigId();
            fieldCnt++;
        }
        if (this.getExp() != 0) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }  else if (builder.hasExp()) {
            // 清理Exp
            builder.clearExp();
            fieldCnt++;
        }
        if (this.getDailyExp() != 0) {
            builder.setDailyExp(this.getDailyExp());
            fieldCnt++;
        }  else if (builder.hasDailyExp()) {
            // 清理DailyExp
            builder.clearDailyExp();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getOpenTime() != 0) {
            builder.setOpenTime(this.getOpenTime());
            fieldCnt++;
        }  else if (builder.hasOpenTime()) {
            // 清理OpenTime
            builder.clearOpenTime();
            fieldCnt++;
        }
        if (this.getDuration() != 0) {
            builder.setDuration(this.getDuration());
            fieldCnt++;
        }  else if (builder.hasDuration()) {
            // 清理Duration
            builder.clearDuration();
            fieldCnt++;
        }
        if (this.claimableSilver != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.claimableSilver.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClaimableSilver(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClaimableSilver();
            }
        }  else if (builder.hasClaimableSilver()) {
            // 清理ClaimableSilver
            builder.clearClaimableSilver();
            fieldCnt++;
        }
        if (this.claimableGold != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.claimableGold.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClaimableGold(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClaimableGold();
            }
        }  else if (builder.hasClaimableGold()) {
            // 清理ClaimableGold
            builder.clearClaimableGold();
            fieldCnt++;
        }
        if (this.getOpenDailyExpBox()) {
            builder.setOpenDailyExpBox(this.getOpenDailyExpBox());
            fieldCnt++;
        }  else if (builder.hasOpenDailyExpBox()) {
            // 清理OpenDailyExpBox
            builder.clearOpenDailyExpBox();
            fieldCnt++;
        }
        if (this.getLastDailyRefreshTsMs() != 0L) {
            builder.setLastDailyRefreshTsMs(this.getLastDailyRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastDailyRefreshTsMs()) {
            // 清理LastDailyRefreshTsMs
            builder.clearLastDailyRefreshTsMs();
            fieldCnt++;
        }
        if (this.tasks != null) {
            Player.Int32BattlePassTaskInfoMap.Builder tmpBuilder = Player.Int32BattlePassTaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.tasks.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTasks();
            }
        }  else if (builder.hasTasks()) {
            // 清理Tasks
            builder.clearTasks();
            fieldCnt++;
        }
        if (this.getTsMs() != 0L) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }  else if (builder.hasTsMs()) {
            // 清理TsMs
            builder.clearTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerBattlePassModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPTYPE)) {
            builder.setBpType(this.getBpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPCONFIGID)) {
            builder.setBpConfigId(this.getBpConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYEXP)) {
            builder.setDailyExp(this.getDailyExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTIME)) {
            builder.setOpenTime(this.getOpenTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DURATION)) {
            builder.setDuration(this.getDuration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLESILVER) && this.claimableSilver != null) {
            final boolean needClear = !builder.hasClaimableSilver();
            final int tmpFieldCnt = this.claimableSilver.copyChangeToDb(builder.getClaimableSilverBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableSilver();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLEGOLD) && this.claimableGold != null) {
            final boolean needClear = !builder.hasClaimableGold();
            final int tmpFieldCnt = this.claimableGold.copyChangeToDb(builder.getClaimableGoldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableGold();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENDAILYEXPBOX)) {
            builder.setOpenDailyExpBox(this.getOpenDailyExpBox());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTDAILYREFRESHTSMS)) {
            builder.setLastDailyRefreshTsMs(this.getLastDailyRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToDb(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerBattlePassModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBpType()) {
            this.innerSetBpType(proto.getBpType());
        } else {
            this.innerSetBpType(BattltPassType.forNumber(0));
        }
        if (proto.hasBpConfigId()) {
            this.innerSetBpConfigId(proto.getBpConfigId());
        } else {
            this.innerSetBpConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExp()) {
            this.innerSetExp(proto.getExp());
        } else {
            this.innerSetExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyExp()) {
            this.innerSetDailyExp(proto.getDailyExp());
        } else {
            this.innerSetDailyExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpenTime()) {
            this.innerSetOpenTime(proto.getOpenTime());
        } else {
            this.innerSetOpenTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDuration()) {
            this.innerSetDuration(proto.getDuration());
        } else {
            this.innerSetDuration(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClaimableSilver()) {
            this.getClaimableSilver().mergeFromDb(proto.getClaimableSilver());
        } else {
            if (this.claimableSilver != null) {
                this.claimableSilver.mergeFromDb(proto.getClaimableSilver());
            }
        }
        if (proto.hasClaimableGold()) {
            this.getClaimableGold().mergeFromDb(proto.getClaimableGold());
        } else {
            if (this.claimableGold != null) {
                this.claimableGold.mergeFromDb(proto.getClaimableGold());
            }
        }
        if (proto.hasOpenDailyExpBox()) {
            this.innerSetOpenDailyExpBox(proto.getOpenDailyExpBox());
        } else {
            this.innerSetOpenDailyExpBox(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasLastDailyRefreshTsMs()) {
            this.innerSetLastDailyRefreshTsMs(proto.getLastDailyRefreshTsMs());
        } else {
            this.innerSetLastDailyRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeFromDb(proto.getTasks());
        } else {
            if (this.tasks != null) {
                this.tasks.mergeFromDb(proto.getTasks());
            }
        }
        if (proto.hasTsMs()) {
            this.innerSetTsMs(proto.getTsMs());
        } else {
            this.innerSetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerBattlePassModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerBattlePassModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBpType()) {
            this.setBpType(proto.getBpType());
            fieldCnt++;
        }
        if (proto.hasBpConfigId()) {
            this.setBpConfigId(proto.getBpConfigId());
            fieldCnt++;
        }
        if (proto.hasExp()) {
            this.setExp(proto.getExp());
            fieldCnt++;
        }
        if (proto.hasDailyExp()) {
            this.setDailyExp(proto.getDailyExp());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasOpenTime()) {
            this.setOpenTime(proto.getOpenTime());
            fieldCnt++;
        }
        if (proto.hasDuration()) {
            this.setDuration(proto.getDuration());
            fieldCnt++;
        }
        if (proto.hasClaimableSilver()) {
            this.getClaimableSilver().mergeChangeFromDb(proto.getClaimableSilver());
            fieldCnt++;
        }
        if (proto.hasClaimableGold()) {
            this.getClaimableGold().mergeChangeFromDb(proto.getClaimableGold());
            fieldCnt++;
        }
        if (proto.hasOpenDailyExpBox()) {
            this.setOpenDailyExpBox(proto.getOpenDailyExpBox());
            fieldCnt++;
        }
        if (proto.hasLastDailyRefreshTsMs()) {
            this.setLastDailyRefreshTsMs(proto.getLastDailyRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeChangeFromDb(proto.getTasks());
            fieldCnt++;
        }
        if (proto.hasTsMs()) {
            this.setTsMs(proto.getTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerBattlePassModel.Builder getCopySsBuilder() {
        final PlayerBattlePassModel.Builder builder = PlayerBattlePassModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerBattlePassModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBpType() != BattltPassType.forNumber(0)) {
            builder.setBpType(this.getBpType());
            fieldCnt++;
        }  else if (builder.hasBpType()) {
            // 清理BpType
            builder.clearBpType();
            fieldCnt++;
        }
        if (this.getBpConfigId() != 0) {
            builder.setBpConfigId(this.getBpConfigId());
            fieldCnt++;
        }  else if (builder.hasBpConfigId()) {
            // 清理BpConfigId
            builder.clearBpConfigId();
            fieldCnt++;
        }
        if (this.getExp() != 0) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }  else if (builder.hasExp()) {
            // 清理Exp
            builder.clearExp();
            fieldCnt++;
        }
        if (this.getDailyExp() != 0) {
            builder.setDailyExp(this.getDailyExp());
            fieldCnt++;
        }  else if (builder.hasDailyExp()) {
            // 清理DailyExp
            builder.clearDailyExp();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.getOpenTime() != 0) {
            builder.setOpenTime(this.getOpenTime());
            fieldCnt++;
        }  else if (builder.hasOpenTime()) {
            // 清理OpenTime
            builder.clearOpenTime();
            fieldCnt++;
        }
        if (this.getDuration() != 0) {
            builder.setDuration(this.getDuration());
            fieldCnt++;
        }  else if (builder.hasDuration()) {
            // 清理Duration
            builder.clearDuration();
            fieldCnt++;
        }
        if (this.claimableSilver != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.claimableSilver.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClaimableSilver(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClaimableSilver();
            }
        }  else if (builder.hasClaimableSilver()) {
            // 清理ClaimableSilver
            builder.clearClaimableSilver();
            fieldCnt++;
        }
        if (this.claimableGold != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.claimableGold.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClaimableGold(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClaimableGold();
            }
        }  else if (builder.hasClaimableGold()) {
            // 清理ClaimableGold
            builder.clearClaimableGold();
            fieldCnt++;
        }
        if (this.getOpenDailyExpBox()) {
            builder.setOpenDailyExpBox(this.getOpenDailyExpBox());
            fieldCnt++;
        }  else if (builder.hasOpenDailyExpBox()) {
            // 清理OpenDailyExpBox
            builder.clearOpenDailyExpBox();
            fieldCnt++;
        }
        if (this.getLastDailyRefreshTsMs() != 0L) {
            builder.setLastDailyRefreshTsMs(this.getLastDailyRefreshTsMs());
            fieldCnt++;
        }  else if (builder.hasLastDailyRefreshTsMs()) {
            // 清理LastDailyRefreshTsMs
            builder.clearLastDailyRefreshTsMs();
            fieldCnt++;
        }
        if (this.tasks != null) {
            Player.Int32BattlePassTaskInfoMap.Builder tmpBuilder = Player.Int32BattlePassTaskInfoMap.newBuilder();
            final int tmpFieldCnt = this.tasks.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTasks(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTasks();
            }
        }  else if (builder.hasTasks()) {
            // 清理Tasks
            builder.clearTasks();
            fieldCnt++;
        }
        if (this.getTsMs() != 0L) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }  else if (builder.hasTsMs()) {
            // 清理TsMs
            builder.clearTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerBattlePassModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BPTYPE)) {
            builder.setBpType(this.getBpType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BPCONFIGID)) {
            builder.setBpConfigId(this.getBpConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXP)) {
            builder.setExp(this.getExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DAILYEXP)) {
            builder.setDailyExp(this.getDailyExp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPENTIME)) {
            builder.setOpenTime(this.getOpenTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DURATION)) {
            builder.setDuration(this.getDuration());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLESILVER) && this.claimableSilver != null) {
            final boolean needClear = !builder.hasClaimableSilver();
            final int tmpFieldCnt = this.claimableSilver.copyChangeToSs(builder.getClaimableSilverBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableSilver();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLEGOLD) && this.claimableGold != null) {
            final boolean needClear = !builder.hasClaimableGold();
            final int tmpFieldCnt = this.claimableGold.copyChangeToSs(builder.getClaimableGoldBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClaimableGold();
            }
        }
        if (this.hasMark(FIELD_INDEX_OPENDAILYEXPBOX)) {
            builder.setOpenDailyExpBox(this.getOpenDailyExpBox());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTDAILYREFRESHTSMS)) {
            builder.setLastDailyRefreshTsMs(this.getLastDailyRefreshTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            final boolean needClear = !builder.hasTasks();
            final int tmpFieldCnt = this.tasks.copyChangeToSs(builder.getTasksBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTasks();
            }
        }
        if (this.hasMark(FIELD_INDEX_TSMS)) {
            builder.setTsMs(this.getTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerBattlePassModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBpType()) {
            this.innerSetBpType(proto.getBpType());
        } else {
            this.innerSetBpType(BattltPassType.forNumber(0));
        }
        if (proto.hasBpConfigId()) {
            this.innerSetBpConfigId(proto.getBpConfigId());
        } else {
            this.innerSetBpConfigId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExp()) {
            this.innerSetExp(proto.getExp());
        } else {
            this.innerSetExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDailyExp()) {
            this.innerSetDailyExp(proto.getDailyExp());
        } else {
            this.innerSetDailyExp(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOpenTime()) {
            this.innerSetOpenTime(proto.getOpenTime());
        } else {
            this.innerSetOpenTime(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDuration()) {
            this.innerSetDuration(proto.getDuration());
        } else {
            this.innerSetDuration(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasClaimableSilver()) {
            this.getClaimableSilver().mergeFromSs(proto.getClaimableSilver());
        } else {
            if (this.claimableSilver != null) {
                this.claimableSilver.mergeFromSs(proto.getClaimableSilver());
            }
        }
        if (proto.hasClaimableGold()) {
            this.getClaimableGold().mergeFromSs(proto.getClaimableGold());
        } else {
            if (this.claimableGold != null) {
                this.claimableGold.mergeFromSs(proto.getClaimableGold());
            }
        }
        if (proto.hasOpenDailyExpBox()) {
            this.innerSetOpenDailyExpBox(proto.getOpenDailyExpBox());
        } else {
            this.innerSetOpenDailyExpBox(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasLastDailyRefreshTsMs()) {
            this.innerSetLastDailyRefreshTsMs(proto.getLastDailyRefreshTsMs());
        } else {
            this.innerSetLastDailyRefreshTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeFromSs(proto.getTasks());
        } else {
            if (this.tasks != null) {
                this.tasks.mergeFromSs(proto.getTasks());
            }
        }
        if (proto.hasTsMs()) {
            this.innerSetTsMs(proto.getTsMs());
        } else {
            this.innerSetTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerBattlePassModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerBattlePassModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBpType()) {
            this.setBpType(proto.getBpType());
            fieldCnt++;
        }
        if (proto.hasBpConfigId()) {
            this.setBpConfigId(proto.getBpConfigId());
            fieldCnt++;
        }
        if (proto.hasExp()) {
            this.setExp(proto.getExp());
            fieldCnt++;
        }
        if (proto.hasDailyExp()) {
            this.setDailyExp(proto.getDailyExp());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasOpenTime()) {
            this.setOpenTime(proto.getOpenTime());
            fieldCnt++;
        }
        if (proto.hasDuration()) {
            this.setDuration(proto.getDuration());
            fieldCnt++;
        }
        if (proto.hasClaimableSilver()) {
            this.getClaimableSilver().mergeChangeFromSs(proto.getClaimableSilver());
            fieldCnt++;
        }
        if (proto.hasClaimableGold()) {
            this.getClaimableGold().mergeChangeFromSs(proto.getClaimableGold());
            fieldCnt++;
        }
        if (proto.hasOpenDailyExpBox()) {
            this.setOpenDailyExpBox(proto.getOpenDailyExpBox());
            fieldCnt++;
        }
        if (proto.hasLastDailyRefreshTsMs()) {
            this.setLastDailyRefreshTsMs(proto.getLastDailyRefreshTsMs());
            fieldCnt++;
        }
        if (proto.hasTasks()) {
            this.getTasks().mergeChangeFromSs(proto.getTasks());
            fieldCnt++;
        }
        if (proto.hasTsMs()) {
            this.setTsMs(proto.getTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerBattlePassModel.Builder builder = PlayerBattlePassModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLESILVER) && this.claimableSilver != null) {
            this.claimableSilver.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLAIMABLEGOLD) && this.claimableGold != null) {
            this.claimableGold.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TASKS) && this.tasks != null) {
            this.tasks.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.claimableSilver != null) {
            this.claimableSilver.markAll();
        }
        if (this.claimableGold != null) {
            this.claimableGold.markAll();
        }
        if (this.tasks != null) {
            this.tasks.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerBattlePassModelProp)) {
            return false;
        }
        final PlayerBattlePassModelProp otherNode = (PlayerBattlePassModelProp) node;
        if (this.bpType != otherNode.bpType) {
            return false;
        }
        if (this.bpConfigId != otherNode.bpConfigId) {
            return false;
        }
        if (this.exp != otherNode.exp) {
            return false;
        }
        if (this.dailyExp != otherNode.dailyExp) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (this.openTime != otherNode.openTime) {
            return false;
        }
        if (this.duration != otherNode.duration) {
            return false;
        }
        if (!this.getClaimableSilver().compareDataTo(otherNode.getClaimableSilver())) {
            return false;
        }
        if (!this.getClaimableGold().compareDataTo(otherNode.getClaimableGold())) {
            return false;
        }
        if (this.openDailyExpBox != otherNode.openDailyExpBox) {
            return false;
        }
        if (this.lastDailyRefreshTsMs != otherNode.lastDailyRefreshTsMs) {
            return false;
        }
        if (!this.getTasks().compareDataTo(otherNode.getTasks())) {
            return false;
        }
        if (this.tsMs != otherNode.tsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 51;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
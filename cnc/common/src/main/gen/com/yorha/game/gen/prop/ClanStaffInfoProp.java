package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanStaffInfo;
import com.yorha.proto.ClanPB.ClanStaffInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanStaffInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_STAFFID = 0;
    public static final int FIELD_INDEX_LASTGRANTTSMS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int staffId = Constant.DEFAULT_INT_VALUE;
    private long lastGrantTsMs = Constant.DEFAULT_LONG_VALUE;

    public ClanStaffInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanStaffInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get staffId
     *
     * @return staffId value
     */
    public int getStaffId() {
        return this.staffId;
    }

    /**
     * set staffId && set marked
     *
     * @param staffId new value
     * @return current object
     */
    public ClanStaffInfoProp setStaffId(int staffId) {
        if (this.staffId != staffId) {
            this.mark(FIELD_INDEX_STAFFID);
            this.staffId = staffId;
        }
        return this;
    }

    /**
     * inner set staffId
     *
     * @param staffId new value
     */
    private void innerSetStaffId(int staffId) {
        this.staffId = staffId;
    }

    /**
     * get lastGrantTsMs
     *
     * @return lastGrantTsMs value
     */
    public long getLastGrantTsMs() {
        return this.lastGrantTsMs;
    }

    /**
     * set lastGrantTsMs && set marked
     *
     * @param lastGrantTsMs new value
     * @return current object
     */
    public ClanStaffInfoProp setLastGrantTsMs(long lastGrantTsMs) {
        if (this.lastGrantTsMs != lastGrantTsMs) {
            this.mark(FIELD_INDEX_LASTGRANTTSMS);
            this.lastGrantTsMs = lastGrantTsMs;
        }
        return this;
    }

    /**
     * inner set lastGrantTsMs
     *
     * @param lastGrantTsMs new value
     */
    private void innerSetLastGrantTsMs(long lastGrantTsMs) {
        this.lastGrantTsMs = lastGrantTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStaffInfoPB.Builder getCopyCsBuilder() {
        final ClanStaffInfoPB.Builder builder = ClanStaffInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanStaffInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getLastGrantTsMs() != 0L) {
            builder.setLastGrantTsMs(this.getLastGrantTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGrantTsMs()) {
            // 清理LastGrantTsMs
            builder.clearLastGrantTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanStaffInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGRANTTSMS)) {
            builder.setLastGrantTsMs(this.getLastGrantTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanStaffInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGRANTTSMS)) {
            builder.setLastGrantTsMs(this.getLastGrantTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanStaffInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastGrantTsMs()) {
            this.innerSetLastGrantTsMs(proto.getLastGrantTsMs());
        } else {
            this.innerSetLastGrantTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanStaffInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanStaffInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasLastGrantTsMs()) {
            this.setLastGrantTsMs(proto.getLastGrantTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStaffInfo.Builder getCopyDbBuilder() {
        final ClanStaffInfo.Builder builder = ClanStaffInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanStaffInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getLastGrantTsMs() != 0L) {
            builder.setLastGrantTsMs(this.getLastGrantTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGrantTsMs()) {
            // 清理LastGrantTsMs
            builder.clearLastGrantTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanStaffInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGRANTTSMS)) {
            builder.setLastGrantTsMs(this.getLastGrantTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanStaffInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastGrantTsMs()) {
            this.innerSetLastGrantTsMs(proto.getLastGrantTsMs());
        } else {
            this.innerSetLastGrantTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanStaffInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanStaffInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasLastGrantTsMs()) {
            this.setLastGrantTsMs(proto.getLastGrantTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanStaffInfo.Builder getCopySsBuilder() {
        final ClanStaffInfo.Builder builder = ClanStaffInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanStaffInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getStaffId() != 0) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }  else if (builder.hasStaffId()) {
            // 清理StaffId
            builder.clearStaffId();
            fieldCnt++;
        }
        if (this.getLastGrantTsMs() != 0L) {
            builder.setLastGrantTsMs(this.getLastGrantTsMs());
            fieldCnt++;
        }  else if (builder.hasLastGrantTsMs()) {
            // 清理LastGrantTsMs
            builder.clearLastGrantTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanStaffInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_STAFFID)) {
            builder.setStaffId(this.getStaffId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTGRANTTSMS)) {
            builder.setLastGrantTsMs(this.getLastGrantTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanStaffInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasStaffId()) {
            this.innerSetStaffId(proto.getStaffId());
        } else {
            this.innerSetStaffId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasLastGrantTsMs()) {
            this.innerSetLastGrantTsMs(proto.getLastGrantTsMs());
        } else {
            this.innerSetLastGrantTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ClanStaffInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanStaffInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasStaffId()) {
            this.setStaffId(proto.getStaffId());
            fieldCnt++;
        }
        if (proto.hasLastGrantTsMs()) {
            this.setLastGrantTsMs(proto.getLastGrantTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanStaffInfo.Builder builder = ClanStaffInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.staffId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanStaffInfoProp)) {
            return false;
        }
        final ClanStaffInfoProp otherNode = (ClanStaffInfoProp) node;
        if (this.staffId != otherNode.staffId) {
            return false;
        }
        if (this.lastGrantTsMs != otherNode.lastGrantTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityTriggerUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityTriggerUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityTriggerUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TRIGGERS = 0;
    public static final int FIELD_INDEX_REWARDRECORDS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private Int32TriggerInfoMapProp triggers = null;
    private Int32TriggerRecordMapProp rewardRecords = null;

    public ActivityTriggerUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityTriggerUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get triggers
     *
     * @return triggers value
     */
    public Int32TriggerInfoMapProp getTriggers() {
        if (this.triggers == null) {
            this.triggers = new Int32TriggerInfoMapProp(this, FIELD_INDEX_TRIGGERS);
        }
        return this.triggers;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putTriggersV(TriggerInfoProp v) {
        this.getTriggers().put(v.getTriggerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TriggerInfoProp addEmptyTriggers(Integer k) {
        return this.getTriggers().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getTriggersSize() {
        if (this.triggers == null) {
            return 0;
        }
        return this.triggers.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isTriggersEmpty() {
        if (this.triggers == null) {
            return true;
        }
        return this.triggers.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TriggerInfoProp getTriggersV(Integer k) {
        if (this.triggers == null || !this.triggers.containsKey(k)) {
            return null;
        }
        return this.triggers.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearTriggers() {
        if (this.triggers != null) {
            this.triggers.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeTriggersV(Integer k) {
        if (this.triggers != null) {
            this.triggers.remove(k);
        }
    }
    /**
     * get rewardRecords
     *
     * @return rewardRecords value
     */
    public Int32TriggerRecordMapProp getRewardRecords() {
        if (this.rewardRecords == null) {
            this.rewardRecords = new Int32TriggerRecordMapProp(this, FIELD_INDEX_REWARDRECORDS);
        }
        return this.rewardRecords;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardRecordsV(TriggerRecordProp v) {
        this.getRewardRecords().put(v.getTriggerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public TriggerRecordProp addEmptyRewardRecords(Integer k) {
        return this.getRewardRecords().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardRecordsSize() {
        if (this.rewardRecords == null) {
            return 0;
        }
        return this.rewardRecords.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardRecordsEmpty() {
        if (this.rewardRecords == null) {
            return true;
        }
        return this.rewardRecords.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public TriggerRecordProp getRewardRecordsV(Integer k) {
        if (this.rewardRecords == null || !this.rewardRecords.containsKey(k)) {
            return null;
        }
        return this.rewardRecords.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardRecords() {
        if (this.rewardRecords != null) {
            this.rewardRecords.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardRecordsV(Integer k) {
        if (this.rewardRecords != null) {
            this.rewardRecords.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityTriggerUnitPB.Builder getCopyCsBuilder() {
        final ActivityTriggerUnitPB.Builder builder = ActivityTriggerUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityTriggerUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.triggers != null) {
            StructPB.Int32TriggerInfoMapPB.Builder tmpBuilder = StructPB.Int32TriggerInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.triggers.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggers();
            }
        }  else if (builder.hasTriggers()) {
            // 清理Triggers
            builder.clearTriggers();
            fieldCnt++;
        }
        if (this.rewardRecords != null) {
            StructPB.Int32TriggerRecordMapPB.Builder tmpBuilder = StructPB.Int32TriggerRecordMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityTriggerUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERS) && this.triggers != null) {
            final boolean needClear = !builder.hasTriggers();
            final int tmpFieldCnt = this.triggers.copyChangeToCs(builder.getTriggersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggers();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToCs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityTriggerUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERS) && this.triggers != null) {
            final boolean needClear = !builder.hasTriggers();
            final int tmpFieldCnt = this.triggers.copyChangeToAndClearDeleteKeysCs(builder.getTriggersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggers();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToAndClearDeleteKeysCs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityTriggerUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggers()) {
            this.getTriggers().mergeFromCs(proto.getTriggers());
        } else {
            if (this.triggers != null) {
                this.triggers.mergeFromCs(proto.getTriggers());
            }
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromCs(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromCs(proto.getRewardRecords());
            }
        }
        this.markAll();
        return ActivityTriggerUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityTriggerUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggers()) {
            this.getTriggers().mergeChangeFromCs(proto.getTriggers());
            fieldCnt++;
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromCs(proto.getRewardRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityTriggerUnit.Builder getCopyDbBuilder() {
        final ActivityTriggerUnit.Builder builder = ActivityTriggerUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityTriggerUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.triggers != null) {
            Struct.Int32TriggerInfoMap.Builder tmpBuilder = Struct.Int32TriggerInfoMap.newBuilder();
            final int tmpFieldCnt = this.triggers.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggers();
            }
        }  else if (builder.hasTriggers()) {
            // 清理Triggers
            builder.clearTriggers();
            fieldCnt++;
        }
        if (this.rewardRecords != null) {
            Struct.Int32TriggerRecordMap.Builder tmpBuilder = Struct.Int32TriggerRecordMap.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityTriggerUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERS) && this.triggers != null) {
            final boolean needClear = !builder.hasTriggers();
            final int tmpFieldCnt = this.triggers.copyChangeToDb(builder.getTriggersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggers();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToDb(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityTriggerUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggers()) {
            this.getTriggers().mergeFromDb(proto.getTriggers());
        } else {
            if (this.triggers != null) {
                this.triggers.mergeFromDb(proto.getTriggers());
            }
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromDb(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromDb(proto.getRewardRecords());
            }
        }
        this.markAll();
        return ActivityTriggerUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityTriggerUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggers()) {
            this.getTriggers().mergeChangeFromDb(proto.getTriggers());
            fieldCnt++;
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromDb(proto.getRewardRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityTriggerUnit.Builder getCopySsBuilder() {
        final ActivityTriggerUnit.Builder builder = ActivityTriggerUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityTriggerUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.triggers != null) {
            Struct.Int32TriggerInfoMap.Builder tmpBuilder = Struct.Int32TriggerInfoMap.newBuilder();
            final int tmpFieldCnt = this.triggers.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggers(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggers();
            }
        }  else if (builder.hasTriggers()) {
            // 清理Triggers
            builder.clearTriggers();
            fieldCnt++;
        }
        if (this.rewardRecords != null) {
            Struct.Int32TriggerRecordMap.Builder tmpBuilder = Struct.Int32TriggerRecordMap.newBuilder();
            final int tmpFieldCnt = this.rewardRecords.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardRecords(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardRecords();
            }
        }  else if (builder.hasRewardRecords()) {
            // 清理RewardRecords
            builder.clearRewardRecords();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityTriggerUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TRIGGERS) && this.triggers != null) {
            final boolean needClear = !builder.hasTriggers();
            final int tmpFieldCnt = this.triggers.copyChangeToSs(builder.getTriggersBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggers();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            final boolean needClear = !builder.hasRewardRecords();
            final int tmpFieldCnt = this.rewardRecords.copyChangeToSs(builder.getRewardRecordsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardRecords();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityTriggerUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTriggers()) {
            this.getTriggers().mergeFromSs(proto.getTriggers());
        } else {
            if (this.triggers != null) {
                this.triggers.mergeFromSs(proto.getTriggers());
            }
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeFromSs(proto.getRewardRecords());
        } else {
            if (this.rewardRecords != null) {
                this.rewardRecords.mergeFromSs(proto.getRewardRecords());
            }
        }
        this.markAll();
        return ActivityTriggerUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityTriggerUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTriggers()) {
            this.getTriggers().mergeChangeFromSs(proto.getTriggers());
            fieldCnt++;
        }
        if (proto.hasRewardRecords()) {
            this.getRewardRecords().mergeChangeFromSs(proto.getRewardRecords());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityTriggerUnit.Builder builder = ActivityTriggerUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERS) && this.triggers != null) {
            this.triggers.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REWARDRECORDS) && this.rewardRecords != null) {
            this.rewardRecords.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.triggers != null) {
            this.triggers.markAll();
        }
        if (this.rewardRecords != null) {
            this.rewardRecords.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityTriggerUnitProp)) {
            return false;
        }
        final ActivityTriggerUnitProp otherNode = (ActivityTriggerUnitProp) node;
        if (!this.getTriggers().compareDataTo(otherNode.getTriggers())) {
            return false;
        }
        if (!this.getRewardRecords().compareDataTo(otherNode.getRewardRecords())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
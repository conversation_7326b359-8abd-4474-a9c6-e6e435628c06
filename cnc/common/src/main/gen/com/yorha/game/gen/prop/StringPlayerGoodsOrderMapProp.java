 
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.AbstractMapNode;
import com.yorha.proto.PlayerPB.StringPlayerGoodsOrderMapPB;
import com.yorha.proto.PlayerPB.PlayerGoodsOrderPB;
import com.yorha.proto.Player.StringPlayerGoodsOrderMap;
import com.yorha.proto.Player.PlayerGoodsOrder;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> auto gen
 */
public class StringPlayerGoodsOrderMapProp extends AbstractMapNode<String, PlayerGoodsOrderProp> {
    /**
     * Creates a StringPlayerGoodsOrderMapProp container
     *
     * @param parent     parent node
     * @param fieldIndex field index in parent node
     */
    public StringPlayerGoodsOrderMapProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to StringPlayerGoodsOrderMapProp
     *
     * @param k map key
     * @return new object
     */
    @Override
    public PlayerGoodsOrderProp addEmptyValue(String k) {
        PlayerGoodsOrderProp newProp = new PlayerGoodsOrderProp(null, DEFAULT_FIELD_INDEX);
        newProp.setToken(k);
        this.put(k, newProp);
        return newProp;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringPlayerGoodsOrderMapPB.Builder getCopyCsBuilder() {
        final StringPlayerGoodsOrderMapPB.Builder builder = StringPlayerGoodsOrderMapPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyToCs(StringPlayerGoodsOrderMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, PlayerGoodsOrderProp> entry : this.entrySet()) {
            PlayerGoodsOrderPB.Builder itemBuilder = PlayerGoodsOrderPB.newBuilder();
            entry.getValue().copyToCs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToCs(StringPlayerGoodsOrderMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsOrderProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsOrderPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsOrderPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringPlayerGoodsOrderMapProp.FIELD_COUNT: 0;
    }

    /**
     * copy data change to protobuf PB. clear first, then refresh, add at last. it wll clear clearFlag and deleteKeys.
     *
     * @param builder builder for protobuf PB
     * @return copied field count
     */
    public int copyChangeToAndClearDeleteKeysCs(StringPlayerGoodsOrderMapPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        // clear builder, when clear flag true
        if (this.isClearFlag()) {
            builder.clear();
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            isChanged = true;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsOrderProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsOrderPB.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsOrderPB.newBuilder();
            final int changeCnt = oldValue.copyChangeToAndClearDeleteKeysCs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        if (isChanged) {
            builder.clearDeleteKeys();
            builder.clearClearFlag();
        }
        return isChanged? StringPlayerGoodsOrderMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(StringPlayerGoodsOrderMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, PlayerGoodsOrderPB> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromCs(entry.getValue());
        }
        this.markAll();
        return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeChangeFromCs(StringPlayerGoodsOrderMapPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, PlayerGoodsOrderPB> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromCs(entry.getValue());
                changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromCs(entry.getValue());
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringPlayerGoodsOrderMap.Builder getCopyDbBuilder() {
        final StringPlayerGoodsOrderMap.Builder builder = StringPlayerGoodsOrderMap.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToDb(StringPlayerGoodsOrderMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, PlayerGoodsOrderProp> entry : this.entrySet()) {
            PlayerGoodsOrder.Builder itemBuilder = PlayerGoodsOrder.newBuilder();
            entry.getValue().copyToDb(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToDb(StringPlayerGoodsOrderMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsOrderProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsOrder.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsOrder.newBuilder();
            final int changeCnt = oldValue.copyChangeToDb(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringPlayerGoodsOrderMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(StringPlayerGoodsOrderMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, PlayerGoodsOrder> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromDb(entry.getValue());
        }
        this.markAll();
        return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromDb(StringPlayerGoodsOrderMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, PlayerGoodsOrder> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromDb(entry.getValue());
                changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromDb(entry.getValue());
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public StringPlayerGoodsOrderMap.Builder getCopySsBuilder() {
        final StringPlayerGoodsOrderMap.Builder builder = StringPlayerGoodsOrderMap.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf .
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyToSs(StringPlayerGoodsOrderMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0 || builder.hasClearFlag() || builder.getDeleteKeysCount() != 0) {
                builder.clear();
                return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final Map.Entry<String, PlayerGoodsOrderProp> entry : this.entrySet()) {
            PlayerGoodsOrder.Builder itemBuilder = PlayerGoodsOrder.newBuilder();
            entry.getValue().copyToSs(itemBuilder);
            builder.putDatas(entry.getKey(), itemBuilder.build());
        }
        return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
    }

    /**
     * copy data change to protobuf . clear first, then refresh, add at last.
     *
     * @param builder builder for protobuf 
     * @return copied field count
     */
    public int copyChangeToSs(StringPlayerGoodsOrderMap.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        boolean isChanged = false;
        if (this.isClearFlag()) {
            // clear builder, when clear flag true
            builder.clear();
            // set clear flag when not clear.
            builder.setClearFlag(this.isClearFlag());
            isChanged = true;
        }
        // refresh data when not clear
        if (!this.isClearFlag() && !this.getRefreshKeys().isEmpty()) {
            final Set<String> deleteKeys = !builder.getClearFlag() ? new HashSet<>(builder.getDeleteKeysList()) : null;
            isChanged = true;
            boolean isDeleteKeysChanged = false;
            for(final String key : this.getRefreshKeys()) {
                builder.removeDatas(key);
                if (deleteKeys != null) {
                    isDeleteKeysChanged = deleteKeys.add(key) || isDeleteKeysChanged;
                }
            }
            // update delete key when builder not clear.
            if (isDeleteKeysChanged) {
                builder.clearDeleteKeys();
                if (!deleteKeys.isEmpty()) {
                    builder.addAllDeleteKeys(deleteKeys);
                }
            }
        }
        // put data when dirty
        for (final String key : this.getExistDirtyKeys()) {
            final PlayerGoodsOrderProp oldValue = this.get(key);
            if (oldValue == null) {
                throw new RuntimeException("key " + key + " not exits!");
            }
            final PlayerGoodsOrder.Builder dataBuilder = builder.getDatasMap().containsKey(key) ? builder.getDatasMap().get(key).toBuilder() : PlayerGoodsOrder.newBuilder();
            final int changeCnt = oldValue.copyChangeToSs(dataBuilder);
            if (changeCnt <= 0) {
                continue;
            }
            builder.putDatas(key, dataBuilder.build());
            isChanged = true;
        }
        return isChanged? StringPlayerGoodsOrderMapProp.FIELD_COUNT: 0;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(StringPlayerGoodsOrderMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (final Map.Entry<String, PlayerGoodsOrder> entry : proto.getDatasMap().entrySet()) {
            this.addEmptyValue(entry.getKey()).mergeFromSs(entry.getValue());
        }
        this.markAll();
        return StringPlayerGoodsOrderMapProp.FIELD_COUNT;
    }

    /**
     * merge data change from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeChangeFromSs(StringPlayerGoodsOrderMap proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int changeCnt = 0;
        if (proto.hasClearFlag() && proto.getClearFlag()) {
            this.clear();
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        if (proto.getDeleteKeysCount() > 0) {
            this.removeAll(proto.getDeleteKeysList());
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        for (final Map.Entry<String, PlayerGoodsOrder> entry : proto.getDatasMap().entrySet()) {
            if (this.containsKey(entry.getKey())) {
                this.get(entry.getKey()).mergeChangeFromSs(entry.getValue());
                changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
                continue;
            }
            this.addEmptyValue(entry.getKey()).mergeChangeFromSs(entry.getValue());
            changeCnt = StringPlayerGoodsOrderMapProp.FIELD_COUNT;
        }
        return changeCnt;
    }

    @Override
    public String toString() {
        StringPlayerGoodsOrderMap.Builder builder = StringPlayerGoodsOrderMap.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
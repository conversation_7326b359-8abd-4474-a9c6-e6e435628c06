package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.CityInnerArmy;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.CityInnerArmyPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class CityInnerArmyProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LEADERARMYID = 0;
    public static final int FIELD_INDEX_ARMY = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private long leaderArmyId = Constant.DEFAULT_LONG_VALUE;
    private Int64InnerArmyInfoMapProp army = null;

    public CityInnerArmyProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CityInnerArmyProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get leaderArmyId
     *
     * @return leaderArmyId value
     */
    public long getLeaderArmyId() {
        return this.leaderArmyId;
    }

    /**
     * set leaderArmyId && set marked
     *
     * @param leaderArmyId new value
     * @return current object
     */
    public CityInnerArmyProp setLeaderArmyId(long leaderArmyId) {
        if (this.leaderArmyId != leaderArmyId) {
            this.mark(FIELD_INDEX_LEADERARMYID);
            this.leaderArmyId = leaderArmyId;
        }
        return this;
    }

    /**
     * inner set leaderArmyId
     *
     * @param leaderArmyId new value
     */
    private void innerSetLeaderArmyId(long leaderArmyId) {
        this.leaderArmyId = leaderArmyId;
    }

    /**
     * get army
     *
     * @return army value
     */
    public Int64InnerArmyInfoMapProp getArmy() {
        if (this.army == null) {
            this.army = new Int64InnerArmyInfoMapProp(this, FIELD_INDEX_ARMY);
        }
        return this.army;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putArmyV(InnerArmyInfoProp v) {
        this.getArmy().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public InnerArmyInfoProp addEmptyArmy(Long k) {
        return this.getArmy().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getArmySize() {
        if (this.army == null) {
            return 0;
        }
        return this.army.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isArmyEmpty() {
        if (this.army == null) {
            return true;
        }
        return this.army.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public InnerArmyInfoProp getArmyV(Long k) {
        if (this.army == null || !this.army.containsKey(k)) {
            return null;
        }
        return this.army.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearArmy() {
        if (this.army != null) {
            this.army.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeArmyV(Long k) {
        if (this.army != null) {
            this.army.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityInnerArmyPB.Builder getCopyCsBuilder() {
        final CityInnerArmyPB.Builder builder = CityInnerArmyPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CityInnerArmyPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLeaderArmyId() != 0L) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }  else if (builder.hasLeaderArmyId()) {
            // 清理LeaderArmyId
            builder.clearLeaderArmyId();
            fieldCnt++;
        }
        if (this.army != null) {
            StructPB.Int64InnerArmyInfoMapPB.Builder tmpBuilder = StructPB.Int64InnerArmyInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.army.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CityInnerArmyPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEADERARMYID)) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToCs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CityInnerArmyPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEADERARMYID)) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToAndClearDeleteKeysCs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CityInnerArmyPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLeaderArmyId()) {
            this.innerSetLeaderArmyId(proto.getLeaderArmyId());
        } else {
            this.innerSetLeaderArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromCs(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromCs(proto.getArmy());
            }
        }
        this.markAll();
        return CityInnerArmyProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CityInnerArmyPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLeaderArmyId()) {
            this.setLeaderArmyId(proto.getLeaderArmyId());
            fieldCnt++;
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromCs(proto.getArmy());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityInnerArmy.Builder getCopyDbBuilder() {
        final CityInnerArmy.Builder builder = CityInnerArmy.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CityInnerArmy.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLeaderArmyId() != 0L) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }  else if (builder.hasLeaderArmyId()) {
            // 清理LeaderArmyId
            builder.clearLeaderArmyId();
            fieldCnt++;
        }
        if (this.army != null) {
            Struct.Int64InnerArmyInfoMap.Builder tmpBuilder = Struct.Int64InnerArmyInfoMap.newBuilder();
            final int tmpFieldCnt = this.army.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CityInnerArmy.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEADERARMYID)) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToDb(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CityInnerArmy proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLeaderArmyId()) {
            this.innerSetLeaderArmyId(proto.getLeaderArmyId());
        } else {
            this.innerSetLeaderArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromDb(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromDb(proto.getArmy());
            }
        }
        this.markAll();
        return CityInnerArmyProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CityInnerArmy proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLeaderArmyId()) {
            this.setLeaderArmyId(proto.getLeaderArmyId());
            fieldCnt++;
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromDb(proto.getArmy());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityInnerArmy.Builder getCopySsBuilder() {
        final CityInnerArmy.Builder builder = CityInnerArmy.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CityInnerArmy.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLeaderArmyId() != 0L) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }  else if (builder.hasLeaderArmyId()) {
            // 清理LeaderArmyId
            builder.clearLeaderArmyId();
            fieldCnt++;
        }
        if (this.army != null) {
            Struct.Int64InnerArmyInfoMap.Builder tmpBuilder = Struct.Int64InnerArmyInfoMap.newBuilder();
            final int tmpFieldCnt = this.army.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CityInnerArmy.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LEADERARMYID)) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToSs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CityInnerArmy proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLeaderArmyId()) {
            this.innerSetLeaderArmyId(proto.getLeaderArmyId());
        } else {
            this.innerSetLeaderArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromSs(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromSs(proto.getArmy());
            }
        }
        this.markAll();
        return CityInnerArmyProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CityInnerArmy proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLeaderArmyId()) {
            this.setLeaderArmyId(proto.getLeaderArmyId());
            fieldCnt++;
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromSs(proto.getArmy());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CityInnerArmy.Builder builder = CityInnerArmy.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            this.army.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.army != null) {
            this.army.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CityInnerArmyProp)) {
            return false;
        }
        final CityInnerArmyProp otherNode = (CityInnerArmyProp) node;
        if (this.leaderArmyId != otherNode.leaderArmyId) {
            return false;
        }
        if (!this.getArmy().compareDataTo(otherNode.getArmy())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
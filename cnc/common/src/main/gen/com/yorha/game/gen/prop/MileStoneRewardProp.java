package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneReward;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.MileStoneRewardPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneRewardProp extends AbstractPropNode {

    public static final int FIELD_INDEX_REWARDPLAYERINFO = 0;
    public static final int FIELD_INDEX_REWARDCLANINFO = 1;
    public static final int FIELD_INDEX_ISALLPLAYER = 2;
    public static final int FIELD_INDEX_REWARDZONE = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private Int32MileStoneRewardPlayerInfoMapProp rewardPlayerInfo = null;
    private Int32MileStoneRewardClanInfoMapProp rewardClanInfo = null;
    private boolean isAllPlayer = Constant.DEFAULT_BOOLEAN_VALUE;
    private Int32SetProp rewardZone = null;

    public MileStoneRewardProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneRewardProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardPlayerInfo
     *
     * @return rewardPlayerInfo value
     */
    public Int32MileStoneRewardPlayerInfoMapProp getRewardPlayerInfo() {
        if (this.rewardPlayerInfo == null) {
            this.rewardPlayerInfo = new Int32MileStoneRewardPlayerInfoMapProp(this, FIELD_INDEX_REWARDPLAYERINFO);
        }
        return this.rewardPlayerInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardPlayerInfoV(MileStoneRewardPlayerInfoProp v) {
        this.getRewardPlayerInfo().put(v.getRewardLevel(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public MileStoneRewardPlayerInfoProp addEmptyRewardPlayerInfo(Integer k) {
        return this.getRewardPlayerInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardPlayerInfoSize() {
        if (this.rewardPlayerInfo == null) {
            return 0;
        }
        return this.rewardPlayerInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardPlayerInfoEmpty() {
        if (this.rewardPlayerInfo == null) {
            return true;
        }
        return this.rewardPlayerInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public MileStoneRewardPlayerInfoProp getRewardPlayerInfoV(Integer k) {
        if (this.rewardPlayerInfo == null || !this.rewardPlayerInfo.containsKey(k)) {
            return null;
        }
        return this.rewardPlayerInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardPlayerInfo() {
        if (this.rewardPlayerInfo != null) {
            this.rewardPlayerInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardPlayerInfoV(Integer k) {
        if (this.rewardPlayerInfo != null) {
            this.rewardPlayerInfo.remove(k);
        }
    }
    /**
     * get rewardClanInfo
     *
     * @return rewardClanInfo value
     */
    public Int32MileStoneRewardClanInfoMapProp getRewardClanInfo() {
        if (this.rewardClanInfo == null) {
            this.rewardClanInfo = new Int32MileStoneRewardClanInfoMapProp(this, FIELD_INDEX_REWARDCLANINFO);
        }
        return this.rewardClanInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardClanInfoV(MileStoneRewardClanInfoProp v) {
        this.getRewardClanInfo().put(v.getRewardLevel(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public MileStoneRewardClanInfoProp addEmptyRewardClanInfo(Integer k) {
        return this.getRewardClanInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardClanInfoSize() {
        if (this.rewardClanInfo == null) {
            return 0;
        }
        return this.rewardClanInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardClanInfoEmpty() {
        if (this.rewardClanInfo == null) {
            return true;
        }
        return this.rewardClanInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public MileStoneRewardClanInfoProp getRewardClanInfoV(Integer k) {
        if (this.rewardClanInfo == null || !this.rewardClanInfo.containsKey(k)) {
            return null;
        }
        return this.rewardClanInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardClanInfo() {
        if (this.rewardClanInfo != null) {
            this.rewardClanInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardClanInfoV(Integer k) {
        if (this.rewardClanInfo != null) {
            this.rewardClanInfo.remove(k);
        }
    }
    /**
     * get isAllPlayer
     *
     * @return isAllPlayer value
     */
    public boolean getIsAllPlayer() {
        return this.isAllPlayer;
    }

    /**
     * set isAllPlayer && set marked
     *
     * @param isAllPlayer new value
     * @return current object
     */
    public MileStoneRewardProp setIsAllPlayer(boolean isAllPlayer) {
        if (this.isAllPlayer != isAllPlayer) {
            this.mark(FIELD_INDEX_ISALLPLAYER);
            this.isAllPlayer = isAllPlayer;
        }
        return this;
    }

    /**
     * inner set isAllPlayer
     *
     * @param isAllPlayer new value
     */
    private void innerSetIsAllPlayer(boolean isAllPlayer) {
        this.isAllPlayer = isAllPlayer;
    }

    /**
     * get rewardZone
     *
     * @return rewardZone value
     */
    public Int32SetProp getRewardZone() {
        if (this.rewardZone == null) {
            this.rewardZone = new Int32SetProp(this, FIELD_INDEX_REWARDZONE);
        }
        return this.rewardZone;
    }


    /**
     * add value to set
     *
     * @param e value
     */
    public void addRewardZone(Integer e) {
        this.getRewardZone().add(e);
    }

    /**
     * remove the specified element in set
     *
     * @param e element
     * @return e if success or null by fail
     */
    public Integer removeRewardZone(Integer e) {
        if (this.rewardZone == null) {
            return null;
        }
        if(this.rewardZone.remove(e)) {
            return e;
        }
        return null;
    }

    /**
     * get set size
     *
     * @return set size
     */
    public int getRewardZoneSize() {
        if (this.rewardZone == null) {
            return 0;
        }
        return this.rewardZone.size();
    }

    /**
     * get is a empty set
     *
     * @return true if empty
     */
    public boolean isRewardZoneEmpty() {
        if (this.rewardZone == null) {
            return true;
        }
        return this.getRewardZone().isEmpty();
    }

    /**
     * clear set
     */
    public void clearRewardZone() {
        this.getRewardZone().clear();
    }

    /**
     * Returns true if this set contains the specified element.
     *
     * @param e elem
     * @return true if this set contains the specified element, false otherwise
     */
    public boolean isRewardZoneContains(Integer e) {
        return this.rewardZone != null && this.rewardZone.contains(e);
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRewardPB.Builder getCopyCsBuilder() {
        final MileStoneRewardPB.Builder builder = MileStoneRewardPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneRewardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardPlayerInfo != null) {
            StructPB.Int32MileStoneRewardPlayerInfoMapPB.Builder tmpBuilder = StructPB.Int32MileStoneRewardPlayerInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardPlayerInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardPlayerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardPlayerInfo();
            }
        }  else if (builder.hasRewardPlayerInfo()) {
            // 清理RewardPlayerInfo
            builder.clearRewardPlayerInfo();
            fieldCnt++;
        }
        if (this.rewardClanInfo != null) {
            StructPB.Int32MileStoneRewardClanInfoMapPB.Builder tmpBuilder = StructPB.Int32MileStoneRewardClanInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardClanInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardClanInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardClanInfo();
            }
        }  else if (builder.hasRewardClanInfo()) {
            // 清理RewardClanInfo
            builder.clearRewardClanInfo();
            fieldCnt++;
        }
        if (this.getIsAllPlayer()) {
            builder.setIsAllPlayer(this.getIsAllPlayer());
            fieldCnt++;
        }  else if (builder.hasIsAllPlayer()) {
            // 清理IsAllPlayer
            builder.clearIsAllPlayer();
            fieldCnt++;
        }
        if (this.rewardZone != null) {
            BasicPB.Int32SetPB.Builder tmpBuilder = BasicPB.Int32SetPB.newBuilder();
            final int tmpFieldCnt = this.rewardZone.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardZone(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardZone();
            }
        }  else if (builder.hasRewardZone()) {
            // 清理RewardZone
            builder.clearRewardZone();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneRewardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERINFO) && this.rewardPlayerInfo != null) {
            final boolean needClear = !builder.hasRewardPlayerInfo();
            final int tmpFieldCnt = this.rewardPlayerInfo.copyChangeToCs(builder.getRewardPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANINFO) && this.rewardClanInfo != null) {
            final boolean needClear = !builder.hasRewardClanInfo();
            final int tmpFieldCnt = this.rewardClanInfo.copyChangeToCs(builder.getRewardClanInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClanInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISALLPLAYER)) {
            builder.setIsAllPlayer(this.getIsAllPlayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDZONE) && this.rewardZone != null) {
            final boolean needClear = !builder.hasRewardZone();
            final int tmpFieldCnt = this.rewardZone.copyChangeToCs(builder.getRewardZoneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardZone();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneRewardPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERINFO) && this.rewardPlayerInfo != null) {
            final boolean needClear = !builder.hasRewardPlayerInfo();
            final int tmpFieldCnt = this.rewardPlayerInfo.copyChangeToAndClearDeleteKeysCs(builder.getRewardPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANINFO) && this.rewardClanInfo != null) {
            final boolean needClear = !builder.hasRewardClanInfo();
            final int tmpFieldCnt = this.rewardClanInfo.copyChangeToAndClearDeleteKeysCs(builder.getRewardClanInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClanInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISALLPLAYER)) {
            builder.setIsAllPlayer(this.getIsAllPlayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDZONE) && this.rewardZone != null) {
            final boolean needClear = !builder.hasRewardZone();
            final int tmpFieldCnt = this.rewardZone.copyChangeToAndClearDeleteKeysCs(builder.getRewardZoneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardZone();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneRewardPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardPlayerInfo()) {
            this.getRewardPlayerInfo().mergeFromCs(proto.getRewardPlayerInfo());
        } else {
            if (this.rewardPlayerInfo != null) {
                this.rewardPlayerInfo.mergeFromCs(proto.getRewardPlayerInfo());
            }
        }
        if (proto.hasRewardClanInfo()) {
            this.getRewardClanInfo().mergeFromCs(proto.getRewardClanInfo());
        } else {
            if (this.rewardClanInfo != null) {
                this.rewardClanInfo.mergeFromCs(proto.getRewardClanInfo());
            }
        }
        if (proto.hasIsAllPlayer()) {
            this.innerSetIsAllPlayer(proto.getIsAllPlayer());
        } else {
            this.innerSetIsAllPlayer(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRewardZone()) {
            this.getRewardZone().mergeFromCs(proto.getRewardZone());
        } else {
            if (this.rewardZone != null) {
                this.rewardZone.mergeFromCs(proto.getRewardZone());
            }
        }
        this.markAll();
        return MileStoneRewardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneRewardPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardPlayerInfo()) {
            this.getRewardPlayerInfo().mergeChangeFromCs(proto.getRewardPlayerInfo());
            fieldCnt++;
        }
        if (proto.hasRewardClanInfo()) {
            this.getRewardClanInfo().mergeChangeFromCs(proto.getRewardClanInfo());
            fieldCnt++;
        }
        if (proto.hasIsAllPlayer()) {
            this.setIsAllPlayer(proto.getIsAllPlayer());
            fieldCnt++;
        }
        if (proto.hasRewardZone()) {
            this.getRewardZone().mergeChangeFromCs(proto.getRewardZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneReward.Builder getCopyDbBuilder() {
        final MileStoneReward.Builder builder = MileStoneReward.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardPlayerInfo != null) {
            Struct.Int32MileStoneRewardPlayerInfoMap.Builder tmpBuilder = Struct.Int32MileStoneRewardPlayerInfoMap.newBuilder();
            final int tmpFieldCnt = this.rewardPlayerInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardPlayerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardPlayerInfo();
            }
        }  else if (builder.hasRewardPlayerInfo()) {
            // 清理RewardPlayerInfo
            builder.clearRewardPlayerInfo();
            fieldCnt++;
        }
        if (this.rewardClanInfo != null) {
            Struct.Int32MileStoneRewardClanInfoMap.Builder tmpBuilder = Struct.Int32MileStoneRewardClanInfoMap.newBuilder();
            final int tmpFieldCnt = this.rewardClanInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardClanInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardClanInfo();
            }
        }  else if (builder.hasRewardClanInfo()) {
            // 清理RewardClanInfo
            builder.clearRewardClanInfo();
            fieldCnt++;
        }
        if (this.getIsAllPlayer()) {
            builder.setIsAllPlayer(this.getIsAllPlayer());
            fieldCnt++;
        }  else if (builder.hasIsAllPlayer()) {
            // 清理IsAllPlayer
            builder.clearIsAllPlayer();
            fieldCnt++;
        }
        if (this.rewardZone != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.rewardZone.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardZone(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardZone();
            }
        }  else if (builder.hasRewardZone()) {
            // 清理RewardZone
            builder.clearRewardZone();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERINFO) && this.rewardPlayerInfo != null) {
            final boolean needClear = !builder.hasRewardPlayerInfo();
            final int tmpFieldCnt = this.rewardPlayerInfo.copyChangeToDb(builder.getRewardPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANINFO) && this.rewardClanInfo != null) {
            final boolean needClear = !builder.hasRewardClanInfo();
            final int tmpFieldCnt = this.rewardClanInfo.copyChangeToDb(builder.getRewardClanInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClanInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISALLPLAYER)) {
            builder.setIsAllPlayer(this.getIsAllPlayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDZONE) && this.rewardZone != null) {
            final boolean needClear = !builder.hasRewardZone();
            final int tmpFieldCnt = this.rewardZone.copyChangeToDb(builder.getRewardZoneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardZone();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneReward proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardPlayerInfo()) {
            this.getRewardPlayerInfo().mergeFromDb(proto.getRewardPlayerInfo());
        } else {
            if (this.rewardPlayerInfo != null) {
                this.rewardPlayerInfo.mergeFromDb(proto.getRewardPlayerInfo());
            }
        }
        if (proto.hasRewardClanInfo()) {
            this.getRewardClanInfo().mergeFromDb(proto.getRewardClanInfo());
        } else {
            if (this.rewardClanInfo != null) {
                this.rewardClanInfo.mergeFromDb(proto.getRewardClanInfo());
            }
        }
        if (proto.hasIsAllPlayer()) {
            this.innerSetIsAllPlayer(proto.getIsAllPlayer());
        } else {
            this.innerSetIsAllPlayer(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRewardZone()) {
            this.getRewardZone().mergeFromDb(proto.getRewardZone());
        } else {
            if (this.rewardZone != null) {
                this.rewardZone.mergeFromDb(proto.getRewardZone());
            }
        }
        this.markAll();
        return MileStoneRewardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneReward proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardPlayerInfo()) {
            this.getRewardPlayerInfo().mergeChangeFromDb(proto.getRewardPlayerInfo());
            fieldCnt++;
        }
        if (proto.hasRewardClanInfo()) {
            this.getRewardClanInfo().mergeChangeFromDb(proto.getRewardClanInfo());
            fieldCnt++;
        }
        if (proto.hasIsAllPlayer()) {
            this.setIsAllPlayer(proto.getIsAllPlayer());
            fieldCnt++;
        }
        if (proto.hasRewardZone()) {
            this.getRewardZone().mergeChangeFromDb(proto.getRewardZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneReward.Builder getCopySsBuilder() {
        final MileStoneReward.Builder builder = MileStoneReward.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.rewardPlayerInfo != null) {
            Struct.Int32MileStoneRewardPlayerInfoMap.Builder tmpBuilder = Struct.Int32MileStoneRewardPlayerInfoMap.newBuilder();
            final int tmpFieldCnt = this.rewardPlayerInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardPlayerInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardPlayerInfo();
            }
        }  else if (builder.hasRewardPlayerInfo()) {
            // 清理RewardPlayerInfo
            builder.clearRewardPlayerInfo();
            fieldCnt++;
        }
        if (this.rewardClanInfo != null) {
            Struct.Int32MileStoneRewardClanInfoMap.Builder tmpBuilder = Struct.Int32MileStoneRewardClanInfoMap.newBuilder();
            final int tmpFieldCnt = this.rewardClanInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardClanInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardClanInfo();
            }
        }  else if (builder.hasRewardClanInfo()) {
            // 清理RewardClanInfo
            builder.clearRewardClanInfo();
            fieldCnt++;
        }
        if (this.getIsAllPlayer()) {
            builder.setIsAllPlayer(this.getIsAllPlayer());
            fieldCnt++;
        }  else if (builder.hasIsAllPlayer()) {
            // 清理IsAllPlayer
            builder.clearIsAllPlayer();
            fieldCnt++;
        }
        if (this.rewardZone != null) {
            Basic.Int32Set.Builder tmpBuilder = Basic.Int32Set.newBuilder();
            final int tmpFieldCnt = this.rewardZone.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardZone(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardZone();
            }
        }  else if (builder.hasRewardZone()) {
            // 清理RewardZone
            builder.clearRewardZone();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneReward.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERINFO) && this.rewardPlayerInfo != null) {
            final boolean needClear = !builder.hasRewardPlayerInfo();
            final int tmpFieldCnt = this.rewardPlayerInfo.copyChangeToSs(builder.getRewardPlayerInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardPlayerInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANINFO) && this.rewardClanInfo != null) {
            final boolean needClear = !builder.hasRewardClanInfo();
            final int tmpFieldCnt = this.rewardClanInfo.copyChangeToSs(builder.getRewardClanInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClanInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_ISALLPLAYER)) {
            builder.setIsAllPlayer(this.getIsAllPlayer());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDZONE) && this.rewardZone != null) {
            final boolean needClear = !builder.hasRewardZone();
            final int tmpFieldCnt = this.rewardZone.copyChangeToSs(builder.getRewardZoneBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardZone();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneReward proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardPlayerInfo()) {
            this.getRewardPlayerInfo().mergeFromSs(proto.getRewardPlayerInfo());
        } else {
            if (this.rewardPlayerInfo != null) {
                this.rewardPlayerInfo.mergeFromSs(proto.getRewardPlayerInfo());
            }
        }
        if (proto.hasRewardClanInfo()) {
            this.getRewardClanInfo().mergeFromSs(proto.getRewardClanInfo());
        } else {
            if (this.rewardClanInfo != null) {
                this.rewardClanInfo.mergeFromSs(proto.getRewardClanInfo());
            }
        }
        if (proto.hasIsAllPlayer()) {
            this.innerSetIsAllPlayer(proto.getIsAllPlayer());
        } else {
            this.innerSetIsAllPlayer(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasRewardZone()) {
            this.getRewardZone().mergeFromSs(proto.getRewardZone());
        } else {
            if (this.rewardZone != null) {
                this.rewardZone.mergeFromSs(proto.getRewardZone());
            }
        }
        this.markAll();
        return MileStoneRewardProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneReward proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardPlayerInfo()) {
            this.getRewardPlayerInfo().mergeChangeFromSs(proto.getRewardPlayerInfo());
            fieldCnt++;
        }
        if (proto.hasRewardClanInfo()) {
            this.getRewardClanInfo().mergeChangeFromSs(proto.getRewardClanInfo());
            fieldCnt++;
        }
        if (proto.hasIsAllPlayer()) {
            this.setIsAllPlayer(proto.getIsAllPlayer());
            fieldCnt++;
        }
        if (proto.hasRewardZone()) {
            this.getRewardZone().mergeChangeFromSs(proto.getRewardZone());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneReward.Builder builder = MileStoneReward.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REWARDPLAYERINFO) && this.rewardPlayerInfo != null) {
            this.rewardPlayerInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANINFO) && this.rewardClanInfo != null) {
            this.rewardClanInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REWARDZONE) && this.rewardZone != null) {
            this.rewardZone.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rewardPlayerInfo != null) {
            this.rewardPlayerInfo.markAll();
        }
        if (this.rewardClanInfo != null) {
            this.rewardClanInfo.markAll();
        }
        if (this.rewardZone != null) {
            this.rewardZone.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneRewardProp)) {
            return false;
        }
        final MileStoneRewardProp otherNode = (MileStoneRewardProp) node;
        if (!this.getRewardPlayerInfo().compareDataTo(otherNode.getRewardPlayerInfo())) {
            return false;
        }
        if (!this.getRewardClanInfo().compareDataTo(otherNode.getRewardClanInfo())) {
            return false;
        }
        if (this.isAllPlayer != otherNode.isAllPlayer) {
            return false;
        }
        if (!this.getRewardZone().compareDataTo(otherNode.getRewardZone())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
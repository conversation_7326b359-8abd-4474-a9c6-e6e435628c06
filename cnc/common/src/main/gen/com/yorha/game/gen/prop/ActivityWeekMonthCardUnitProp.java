package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityWeekMonthCardUnit;
import com.yorha.proto.StructPB.ActivityWeekMonthCardUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityWeekMonthCardUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CARDID = 0;
    public static final int FIELD_INDEX_EXPIRETSMS = 1;
    public static final int FIELD_INDEX_LASTDAILYREWARDTSMS = 2;
    public static final int FIELD_INDEX_LASTNTFEXPIRETSMS = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int cardId = Constant.DEFAULT_INT_VALUE;
    private long expireTsMs = Constant.DEFAULT_LONG_VALUE;
    private long lastDailyRewardTsMs = Constant.DEFAULT_LONG_VALUE;
    private long lastNtfExpireTsMs = Constant.DEFAULT_LONG_VALUE;

    public ActivityWeekMonthCardUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityWeekMonthCardUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get cardId
     *
     * @return cardId value
     */
    public int getCardId() {
        return this.cardId;
    }

    /**
     * set cardId && set marked
     *
     * @param cardId new value
     * @return current object
     */
    public ActivityWeekMonthCardUnitProp setCardId(int cardId) {
        if (this.cardId != cardId) {
            this.mark(FIELD_INDEX_CARDID);
            this.cardId = cardId;
        }
        return this;
    }

    /**
     * inner set cardId
     *
     * @param cardId new value
     */
    private void innerSetCardId(int cardId) {
        this.cardId = cardId;
    }

    /**
     * get expireTsMs
     *
     * @return expireTsMs value
     */
    public long getExpireTsMs() {
        return this.expireTsMs;
    }

    /**
     * set expireTsMs && set marked
     *
     * @param expireTsMs new value
     * @return current object
     */
    public ActivityWeekMonthCardUnitProp setExpireTsMs(long expireTsMs) {
        if (this.expireTsMs != expireTsMs) {
            this.mark(FIELD_INDEX_EXPIRETSMS);
            this.expireTsMs = expireTsMs;
        }
        return this;
    }

    /**
     * inner set expireTsMs
     *
     * @param expireTsMs new value
     */
    private void innerSetExpireTsMs(long expireTsMs) {
        this.expireTsMs = expireTsMs;
    }

    /**
     * get lastDailyRewardTsMs
     *
     * @return lastDailyRewardTsMs value
     */
    public long getLastDailyRewardTsMs() {
        return this.lastDailyRewardTsMs;
    }

    /**
     * set lastDailyRewardTsMs && set marked
     *
     * @param lastDailyRewardTsMs new value
     * @return current object
     */
    public ActivityWeekMonthCardUnitProp setLastDailyRewardTsMs(long lastDailyRewardTsMs) {
        if (this.lastDailyRewardTsMs != lastDailyRewardTsMs) {
            this.mark(FIELD_INDEX_LASTDAILYREWARDTSMS);
            this.lastDailyRewardTsMs = lastDailyRewardTsMs;
        }
        return this;
    }

    /**
     * inner set lastDailyRewardTsMs
     *
     * @param lastDailyRewardTsMs new value
     */
    private void innerSetLastDailyRewardTsMs(long lastDailyRewardTsMs) {
        this.lastDailyRewardTsMs = lastDailyRewardTsMs;
    }

    /**
     * get lastNtfExpireTsMs
     *
     * @return lastNtfExpireTsMs value
     */
    public long getLastNtfExpireTsMs() {
        return this.lastNtfExpireTsMs;
    }

    /**
     * set lastNtfExpireTsMs && set marked
     *
     * @param lastNtfExpireTsMs new value
     * @return current object
     */
    public ActivityWeekMonthCardUnitProp setLastNtfExpireTsMs(long lastNtfExpireTsMs) {
        if (this.lastNtfExpireTsMs != lastNtfExpireTsMs) {
            this.mark(FIELD_INDEX_LASTNTFEXPIRETSMS);
            this.lastNtfExpireTsMs = lastNtfExpireTsMs;
        }
        return this;
    }

    /**
     * inner set lastNtfExpireTsMs
     *
     * @param lastNtfExpireTsMs new value
     */
    private void innerSetLastNtfExpireTsMs(long lastNtfExpireTsMs) {
        this.lastNtfExpireTsMs = lastNtfExpireTsMs;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityWeekMonthCardUnitPB.Builder getCopyCsBuilder() {
        final ActivityWeekMonthCardUnitPB.Builder builder = ActivityWeekMonthCardUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityWeekMonthCardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCardId() != 0) {
            builder.setCardId(this.getCardId());
            fieldCnt++;
        }  else if (builder.hasCardId()) {
            // 清理CardId
            builder.clearCardId();
            fieldCnt++;
        }
        if (this.getExpireTsMs() != 0L) {
            builder.setExpireTsMs(this.getExpireTsMs());
            fieldCnt++;
        }  else if (builder.hasExpireTsMs()) {
            // 清理ExpireTsMs
            builder.clearExpireTsMs();
            fieldCnt++;
        }
        if (this.getLastDailyRewardTsMs() != 0L) {
            builder.setLastDailyRewardTsMs(this.getLastDailyRewardTsMs());
            fieldCnt++;
        }  else if (builder.hasLastDailyRewardTsMs()) {
            // 清理LastDailyRewardTsMs
            builder.clearLastDailyRewardTsMs();
            fieldCnt++;
        }
        if (this.getLastNtfExpireTsMs() != 0L) {
            builder.setLastNtfExpireTsMs(this.getLastNtfExpireTsMs());
            fieldCnt++;
        }  else if (builder.hasLastNtfExpireTsMs()) {
            // 清理LastNtfExpireTsMs
            builder.clearLastNtfExpireTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityWeekMonthCardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CARDID)) {
            builder.setCardId(this.getCardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIRETSMS)) {
            builder.setExpireTsMs(this.getExpireTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTDAILYREWARDTSMS)) {
            builder.setLastDailyRewardTsMs(this.getLastDailyRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTNTFEXPIRETSMS)) {
            builder.setLastNtfExpireTsMs(this.getLastNtfExpireTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityWeekMonthCardUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CARDID)) {
            builder.setCardId(this.getCardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIRETSMS)) {
            builder.setExpireTsMs(this.getExpireTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTDAILYREWARDTSMS)) {
            builder.setLastDailyRewardTsMs(this.getLastDailyRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTNTFEXPIRETSMS)) {
            builder.setLastNtfExpireTsMs(this.getLastNtfExpireTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityWeekMonthCardUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCardId()) {
            this.innerSetCardId(proto.getCardId());
        } else {
            this.innerSetCardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpireTsMs()) {
            this.innerSetExpireTsMs(proto.getExpireTsMs());
        } else {
            this.innerSetExpireTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastDailyRewardTsMs()) {
            this.innerSetLastDailyRewardTsMs(proto.getLastDailyRewardTsMs());
        } else {
            this.innerSetLastDailyRewardTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastNtfExpireTsMs()) {
            this.innerSetLastNtfExpireTsMs(proto.getLastNtfExpireTsMs());
        } else {
            this.innerSetLastNtfExpireTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityWeekMonthCardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityWeekMonthCardUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCardId()) {
            this.setCardId(proto.getCardId());
            fieldCnt++;
        }
        if (proto.hasExpireTsMs()) {
            this.setExpireTsMs(proto.getExpireTsMs());
            fieldCnt++;
        }
        if (proto.hasLastDailyRewardTsMs()) {
            this.setLastDailyRewardTsMs(proto.getLastDailyRewardTsMs());
            fieldCnt++;
        }
        if (proto.hasLastNtfExpireTsMs()) {
            this.setLastNtfExpireTsMs(proto.getLastNtfExpireTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityWeekMonthCardUnit.Builder getCopyDbBuilder() {
        final ActivityWeekMonthCardUnit.Builder builder = ActivityWeekMonthCardUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityWeekMonthCardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCardId() != 0) {
            builder.setCardId(this.getCardId());
            fieldCnt++;
        }  else if (builder.hasCardId()) {
            // 清理CardId
            builder.clearCardId();
            fieldCnt++;
        }
        if (this.getExpireTsMs() != 0L) {
            builder.setExpireTsMs(this.getExpireTsMs());
            fieldCnt++;
        }  else if (builder.hasExpireTsMs()) {
            // 清理ExpireTsMs
            builder.clearExpireTsMs();
            fieldCnt++;
        }
        if (this.getLastDailyRewardTsMs() != 0L) {
            builder.setLastDailyRewardTsMs(this.getLastDailyRewardTsMs());
            fieldCnt++;
        }  else if (builder.hasLastDailyRewardTsMs()) {
            // 清理LastDailyRewardTsMs
            builder.clearLastDailyRewardTsMs();
            fieldCnt++;
        }
        if (this.getLastNtfExpireTsMs() != 0L) {
            builder.setLastNtfExpireTsMs(this.getLastNtfExpireTsMs());
            fieldCnt++;
        }  else if (builder.hasLastNtfExpireTsMs()) {
            // 清理LastNtfExpireTsMs
            builder.clearLastNtfExpireTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityWeekMonthCardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CARDID)) {
            builder.setCardId(this.getCardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIRETSMS)) {
            builder.setExpireTsMs(this.getExpireTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTDAILYREWARDTSMS)) {
            builder.setLastDailyRewardTsMs(this.getLastDailyRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTNTFEXPIRETSMS)) {
            builder.setLastNtfExpireTsMs(this.getLastNtfExpireTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityWeekMonthCardUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCardId()) {
            this.innerSetCardId(proto.getCardId());
        } else {
            this.innerSetCardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpireTsMs()) {
            this.innerSetExpireTsMs(proto.getExpireTsMs());
        } else {
            this.innerSetExpireTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastDailyRewardTsMs()) {
            this.innerSetLastDailyRewardTsMs(proto.getLastDailyRewardTsMs());
        } else {
            this.innerSetLastDailyRewardTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastNtfExpireTsMs()) {
            this.innerSetLastNtfExpireTsMs(proto.getLastNtfExpireTsMs());
        } else {
            this.innerSetLastNtfExpireTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityWeekMonthCardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityWeekMonthCardUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCardId()) {
            this.setCardId(proto.getCardId());
            fieldCnt++;
        }
        if (proto.hasExpireTsMs()) {
            this.setExpireTsMs(proto.getExpireTsMs());
            fieldCnt++;
        }
        if (proto.hasLastDailyRewardTsMs()) {
            this.setLastDailyRewardTsMs(proto.getLastDailyRewardTsMs());
            fieldCnt++;
        }
        if (proto.hasLastNtfExpireTsMs()) {
            this.setLastNtfExpireTsMs(proto.getLastNtfExpireTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityWeekMonthCardUnit.Builder getCopySsBuilder() {
        final ActivityWeekMonthCardUnit.Builder builder = ActivityWeekMonthCardUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityWeekMonthCardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCardId() != 0) {
            builder.setCardId(this.getCardId());
            fieldCnt++;
        }  else if (builder.hasCardId()) {
            // 清理CardId
            builder.clearCardId();
            fieldCnt++;
        }
        if (this.getExpireTsMs() != 0L) {
            builder.setExpireTsMs(this.getExpireTsMs());
            fieldCnt++;
        }  else if (builder.hasExpireTsMs()) {
            // 清理ExpireTsMs
            builder.clearExpireTsMs();
            fieldCnt++;
        }
        if (this.getLastDailyRewardTsMs() != 0L) {
            builder.setLastDailyRewardTsMs(this.getLastDailyRewardTsMs());
            fieldCnt++;
        }  else if (builder.hasLastDailyRewardTsMs()) {
            // 清理LastDailyRewardTsMs
            builder.clearLastDailyRewardTsMs();
            fieldCnt++;
        }
        if (this.getLastNtfExpireTsMs() != 0L) {
            builder.setLastNtfExpireTsMs(this.getLastNtfExpireTsMs());
            fieldCnt++;
        }  else if (builder.hasLastNtfExpireTsMs()) {
            // 清理LastNtfExpireTsMs
            builder.clearLastNtfExpireTsMs();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityWeekMonthCardUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CARDID)) {
            builder.setCardId(this.getCardId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPIRETSMS)) {
            builder.setExpireTsMs(this.getExpireTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTDAILYREWARDTSMS)) {
            builder.setLastDailyRewardTsMs(this.getLastDailyRewardTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LASTNTFEXPIRETSMS)) {
            builder.setLastNtfExpireTsMs(this.getLastNtfExpireTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityWeekMonthCardUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCardId()) {
            this.innerSetCardId(proto.getCardId());
        } else {
            this.innerSetCardId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExpireTsMs()) {
            this.innerSetExpireTsMs(proto.getExpireTsMs());
        } else {
            this.innerSetExpireTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastDailyRewardTsMs()) {
            this.innerSetLastDailyRewardTsMs(proto.getLastDailyRewardTsMs());
        } else {
            this.innerSetLastDailyRewardTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasLastNtfExpireTsMs()) {
            this.innerSetLastNtfExpireTsMs(proto.getLastNtfExpireTsMs());
        } else {
            this.innerSetLastNtfExpireTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return ActivityWeekMonthCardUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityWeekMonthCardUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCardId()) {
            this.setCardId(proto.getCardId());
            fieldCnt++;
        }
        if (proto.hasExpireTsMs()) {
            this.setExpireTsMs(proto.getExpireTsMs());
            fieldCnt++;
        }
        if (proto.hasLastDailyRewardTsMs()) {
            this.setLastDailyRewardTsMs(proto.getLastDailyRewardTsMs());
            fieldCnt++;
        }
        if (proto.hasLastNtfExpireTsMs()) {
            this.setLastNtfExpireTsMs(proto.getLastNtfExpireTsMs());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityWeekMonthCardUnit.Builder builder = ActivityWeekMonthCardUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityWeekMonthCardUnitProp)) {
            return false;
        }
        final ActivityWeekMonthCardUnitProp otherNode = (ActivityWeekMonthCardUnitProp) node;
        if (this.cardId != otherNode.cardId) {
            return false;
        }
        if (this.expireTsMs != otherNode.expireTsMs) {
            return false;
        }
        if (this.lastDailyRewardTsMs != otherNode.lastDailyRewardTsMs) {
            return false;
        }
        if (this.lastNtfExpireTsMs != otherNode.lastNtfExpireTsMs) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
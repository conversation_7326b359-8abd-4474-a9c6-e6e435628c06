package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.GoodInfo;
import com.yorha.proto.StructPB.GoodInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class GoodInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_COUNT = 1;
    public static final int FIELD_INDEX_ADDTIME = 2;
    public static final int FIELD_INDEX_BUYTIME = 3;
    public static final int FIELD_INDEX_SELLOUT = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int id = Constant.DEFAULT_INT_VALUE;
    private int count = Constant.DEFAULT_INT_VALUE;
    private long addTime = Constant.DEFAULT_LONG_VALUE;
    private long buyTime = Constant.DEFAULT_LONG_VALUE;
    private boolean sellOut = Constant.DEFAULT_BOOLEAN_VALUE;

    public GoodInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public GoodInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public int getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public GoodInfoProp setId(int id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(int id) {
        this.id = id;
    }

    /**
     * get count
     *
     * @return count value
     */
    public int getCount() {
        return this.count;
    }

    /**
     * set count && set marked
     *
     * @param count new value
     * @return current object
     */
    public GoodInfoProp setCount(int count) {
        if (this.count != count) {
            this.mark(FIELD_INDEX_COUNT);
            this.count = count;
        }
        return this;
    }

    /**
     * inner set count
     *
     * @param count new value
     */
    private void innerSetCount(int count) {
        this.count = count;
    }

    /**
     * get addTime
     *
     * @return addTime value
     */
    public long getAddTime() {
        return this.addTime;
    }

    /**
     * set addTime && set marked
     *
     * @param addTime new value
     * @return current object
     */
    public GoodInfoProp setAddTime(long addTime) {
        if (this.addTime != addTime) {
            this.mark(FIELD_INDEX_ADDTIME);
            this.addTime = addTime;
        }
        return this;
    }

    /**
     * inner set addTime
     *
     * @param addTime new value
     */
    private void innerSetAddTime(long addTime) {
        this.addTime = addTime;
    }

    /**
     * get buyTime
     *
     * @return buyTime value
     */
    public long getBuyTime() {
        return this.buyTime;
    }

    /**
     * set buyTime && set marked
     *
     * @param buyTime new value
     * @return current object
     */
    public GoodInfoProp setBuyTime(long buyTime) {
        if (this.buyTime != buyTime) {
            this.mark(FIELD_INDEX_BUYTIME);
            this.buyTime = buyTime;
        }
        return this;
    }

    /**
     * inner set buyTime
     *
     * @param buyTime new value
     */
    private void innerSetBuyTime(long buyTime) {
        this.buyTime = buyTime;
    }

    /**
     * get sellOut
     *
     * @return sellOut value
     */
    public boolean getSellOut() {
        return this.sellOut;
    }

    /**
     * set sellOut && set marked
     *
     * @param sellOut new value
     * @return current object
     */
    public GoodInfoProp setSellOut(boolean sellOut) {
        if (this.sellOut != sellOut) {
            this.mark(FIELD_INDEX_SELLOUT);
            this.sellOut = sellOut;
        }
        return this;
    }

    /**
     * inner set sellOut
     *
     * @param sellOut new value
     */
    private void innerSetSellOut(boolean sellOut) {
        this.sellOut = sellOut;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodInfoPB.Builder getCopyCsBuilder() {
        final GoodInfoPB.Builder builder = GoodInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(GoodInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        if (this.getAddTime() != 0L) {
            builder.setAddTime(this.getAddTime());
            fieldCnt++;
        }  else if (builder.hasAddTime()) {
            // 清理AddTime
            builder.clearAddTime();
            fieldCnt++;
        }
        if (this.getBuyTime() != 0L) {
            builder.setBuyTime(this.getBuyTime());
            fieldCnt++;
        }  else if (builder.hasBuyTime()) {
            // 清理BuyTime
            builder.clearBuyTime();
            fieldCnt++;
        }
        if (this.getSellOut()) {
            builder.setSellOut(this.getSellOut());
            fieldCnt++;
        }  else if (builder.hasSellOut()) {
            // 清理SellOut
            builder.clearSellOut();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(GoodInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDTIME)) {
            builder.setAddTime(this.getAddTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYTIME)) {
            builder.setBuyTime(this.getBuyTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELLOUT)) {
            builder.setSellOut(this.getSellOut());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(GoodInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDTIME)) {
            builder.setAddTime(this.getAddTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYTIME)) {
            builder.setBuyTime(this.getBuyTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELLOUT)) {
            builder.setSellOut(this.getSellOut());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(GoodInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAddTime()) {
            this.innerSetAddTime(proto.getAddTime());
        } else {
            this.innerSetAddTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuyTime()) {
            this.innerSetBuyTime(proto.getBuyTime());
        } else {
            this.innerSetBuyTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSellOut()) {
            this.innerSetSellOut(proto.getSellOut());
        } else {
            this.innerSetSellOut(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return GoodInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(GoodInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        if (proto.hasAddTime()) {
            this.setAddTime(proto.getAddTime());
            fieldCnt++;
        }
        if (proto.hasBuyTime()) {
            this.setBuyTime(proto.getBuyTime());
            fieldCnt++;
        }
        if (proto.hasSellOut()) {
            this.setSellOut(proto.getSellOut());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodInfo.Builder getCopyDbBuilder() {
        final GoodInfo.Builder builder = GoodInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(GoodInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        if (this.getAddTime() != 0L) {
            builder.setAddTime(this.getAddTime());
            fieldCnt++;
        }  else if (builder.hasAddTime()) {
            // 清理AddTime
            builder.clearAddTime();
            fieldCnt++;
        }
        if (this.getBuyTime() != 0L) {
            builder.setBuyTime(this.getBuyTime());
            fieldCnt++;
        }  else if (builder.hasBuyTime()) {
            // 清理BuyTime
            builder.clearBuyTime();
            fieldCnt++;
        }
        if (this.getSellOut()) {
            builder.setSellOut(this.getSellOut());
            fieldCnt++;
        }  else if (builder.hasSellOut()) {
            // 清理SellOut
            builder.clearSellOut();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(GoodInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDTIME)) {
            builder.setAddTime(this.getAddTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYTIME)) {
            builder.setBuyTime(this.getBuyTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELLOUT)) {
            builder.setSellOut(this.getSellOut());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(GoodInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAddTime()) {
            this.innerSetAddTime(proto.getAddTime());
        } else {
            this.innerSetAddTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuyTime()) {
            this.innerSetBuyTime(proto.getBuyTime());
        } else {
            this.innerSetBuyTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSellOut()) {
            this.innerSetSellOut(proto.getSellOut());
        } else {
            this.innerSetSellOut(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return GoodInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(GoodInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        if (proto.hasAddTime()) {
            this.setAddTime(proto.getAddTime());
            fieldCnt++;
        }
        if (proto.hasBuyTime()) {
            this.setBuyTime(proto.getBuyTime());
            fieldCnt++;
        }
        if (proto.hasSellOut()) {
            this.setSellOut(proto.getSellOut());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public GoodInfo.Builder getCopySsBuilder() {
        final GoodInfo.Builder builder = GoodInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(GoodInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getCount() != 0) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        if (this.getAddTime() != 0L) {
            builder.setAddTime(this.getAddTime());
            fieldCnt++;
        }  else if (builder.hasAddTime()) {
            // 清理AddTime
            builder.clearAddTime();
            fieldCnt++;
        }
        if (this.getBuyTime() != 0L) {
            builder.setBuyTime(this.getBuyTime());
            fieldCnt++;
        }  else if (builder.hasBuyTime()) {
            // 清理BuyTime
            builder.clearBuyTime();
            fieldCnt++;
        }
        if (this.getSellOut()) {
            builder.setSellOut(this.getSellOut());
            fieldCnt++;
        }  else if (builder.hasSellOut()) {
            // 清理SellOut
            builder.clearSellOut();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(GoodInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDTIME)) {
            builder.setAddTime(this.getAddTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BUYTIME)) {
            builder.setBuyTime(this.getBuyTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELLOUT)) {
            builder.setSellOut(this.getSellOut());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(GoodInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasAddTime()) {
            this.innerSetAddTime(proto.getAddTime());
        } else {
            this.innerSetAddTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBuyTime()) {
            this.innerSetBuyTime(proto.getBuyTime());
        } else {
            this.innerSetBuyTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSellOut()) {
            this.innerSetSellOut(proto.getSellOut());
        } else {
            this.innerSetSellOut(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return GoodInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(GoodInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        if (proto.hasAddTime()) {
            this.setAddTime(proto.getAddTime());
            fieldCnt++;
        }
        if (proto.hasBuyTime()) {
            this.setBuyTime(proto.getBuyTime());
            fieldCnt++;
        }
        if (proto.hasSellOut()) {
            this.setSellOut(proto.getSellOut());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        GoodInfo.Builder builder = GoodInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof GoodInfoProp)) {
            return false;
        }
        final GoodInfoProp otherNode = (GoodInfoProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.count != otherNode.count) {
            return false;
        }
        if (this.addTime != otherNode.addTime) {
            return false;
        }
        if (this.buyTime != otherNode.buyTime) {
            return false;
        }
        if (this.sellOut != otherNode.sellOut) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
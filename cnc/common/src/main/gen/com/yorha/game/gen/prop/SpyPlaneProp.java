package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.SpyPlane.SpyPlaneEntity;
import com.yorha.proto.Struct;
import com.yorha.proto.SpyPlanePB.SpyPlaneEntityPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class SpyPlaneProp extends AbstractPropNode {

    public static final int FIELD_INDEX_OWNERID = 0;
    public static final int FIELD_INDEX_MOVE = 1;
    public static final int FIELD_INDEX_TEMPLATEID = 2;
    public static final int FIELD_INDEX_EXPLOREGRIDID = 3;
    public static final int FIELD_INDEX_ACTION = 4;
    public static final int FIELD_INDEX_STATE = 5;
    public static final int FIELD_INDEX_OWNERNAME = 6;
    public static final int FIELD_INDEX_CLANID = 7;
    public static final int FIELD_INDEX_BRIEFCLANNAME = 8;
    public static final int FIELD_INDEX_PLAYERPLANEID = 9;
    public static final int FIELD_INDEX_FINISHNEEDRETURN = 10;
    public static final int FIELD_INDEX_TARGETID = 11;
    public static final int FIELD_INDEX_SPYTYPE = 12;
    public static final int FIELD_INDEX_TARGETPLAYERID = 13;
    public static final int FIELD_INDEX_TOTALEXPLOREGRIDSIZE = 14;
    public static final int FIELD_INDEX_STAYSTARTSTAMP = 15;
    public static final int FIELD_INDEX_SURVEYS = 16;
    public static final int FIELD_INDEX_SURVEYE = 17;
    public static final int FIELD_INDEX_TARGETCONFIGID = 18;
    public static final int FIELD_INDEX_CAMP = 19;
    public static final int FIELD_INDEX_ZONEID = 20;

    public static final int FIELD_COUNT = 21;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private MoveProp move = null;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private PointListProp exploreGridId = null;
    private SpyPlaneActionType action = SpyPlaneActionType.forNumber(0);
    private SpyPlaneState state = SpyPlaneState.forNumber(0);
    private String ownerName = Constant.DEFAULT_STR_VALUE;
    private long clanId = Constant.DEFAULT_LONG_VALUE;
    private String briefClanName = Constant.DEFAULT_STR_VALUE;
    private long playerPlaneId = Constant.DEFAULT_LONG_VALUE;
    private boolean finishNeedReturn = Constant.DEFAULT_BOOLEAN_VALUE;
    private long targetId = Constant.DEFAULT_LONG_VALUE;
    private SpyType spyType = SpyType.forNumber(0);
    private long targetPlayerId = Constant.DEFAULT_LONG_VALUE;
    private int totalExploreGridSize = Constant.DEFAULT_INT_VALUE;
    private long stayStartStamp = Constant.DEFAULT_LONG_VALUE;
    private long surveyS = Constant.DEFAULT_LONG_VALUE;
    private long surveyE = Constant.DEFAULT_LONG_VALUE;
    private long targetConfigId = Constant.DEFAULT_LONG_VALUE;
    private Camp camp = Camp.forNumber(0);
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public SpyPlaneProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SpyPlaneProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public SpyPlaneProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get move
     *
     * @return move value
     */
    public MoveProp getMove() {
        if (this.move == null) {
            this.move = new MoveProp(this, FIELD_INDEX_MOVE);
        }
        return this.move;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public SpyPlaneProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get exploreGridId
     *
     * @return exploreGridId value
     */
    public PointListProp getExploreGridId() {
        if (this.exploreGridId == null) {
            this.exploreGridId = new PointListProp(this, FIELD_INDEX_EXPLOREGRIDID);
        }
        return this.exploreGridId;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addExploreGridId(PointProp v) {
        this.getExploreGridId().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PointProp getExploreGridIdIndex(int index) {
        return this.getExploreGridId().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public PointProp removeExploreGridId(PointProp v) {
        if (this.exploreGridId == null) {
            return null;
        }
        if(this.exploreGridId.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getExploreGridIdSize() {
        if (this.exploreGridId == null) {
            return 0;
        }
        return this.exploreGridId.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isExploreGridIdEmpty() {
        if (this.exploreGridId == null) {
            return true;
        }
        return this.getExploreGridId().isEmpty();
    }

    /**
     * clear list
     */
    public void clearExploreGridId() {
        this.getExploreGridId().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public PointProp removeExploreGridIdIndex(int index) {
        return this.getExploreGridId().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public PointProp setExploreGridIdIndex(int index, PointProp v) {
        return this.getExploreGridId().set(index, v);
    }
    /**
     * get action
     *
     * @return action value
     */
    public SpyPlaneActionType getAction() {
        return this.action;
    }

    /**
     * set action && set marked
     *
     * @param action new value
     * @return current object
     */
    public SpyPlaneProp setAction(SpyPlaneActionType action) {
        if (action == null) {
            throw new NullPointerException();
        }
        if (this.action != action) {
            this.mark(FIELD_INDEX_ACTION);
            this.action = action;
        }
        return this;
    }

    /**
     * inner set action
     *
     * @param action new value
     */
    private void innerSetAction(SpyPlaneActionType action) {
        this.action = action;
    }

    /**
     * get state
     *
     * @return state value
     */
    public SpyPlaneState getState() {
        return this.state;
    }

    /**
     * set state && set marked
     *
     * @param state new value
     * @return current object
     */
    public SpyPlaneProp setState(SpyPlaneState state) {
        if (state == null) {
            throw new NullPointerException();
        }
        if (this.state != state) {
            this.mark(FIELD_INDEX_STATE);
            this.state = state;
        }
        return this;
    }

    /**
     * inner set state
     *
     * @param state new value
     */
    private void innerSetState(SpyPlaneState state) {
        this.state = state;
    }

    /**
     * get ownerName
     *
     * @return ownerName value
     */
    public String getOwnerName() {
        return this.ownerName;
    }

    /**
     * set ownerName && set marked
     *
     * @param ownerName new value
     * @return current object
     */
    public SpyPlaneProp setOwnerName(String ownerName) {
        if (ownerName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.ownerName, ownerName)) {
            this.mark(FIELD_INDEX_OWNERNAME);
            this.ownerName = ownerName;
        }
        return this;
    }

    /**
     * inner set ownerName
     *
     * @param ownerName new value
     */
    private void innerSetOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     * get clanId
     *
     * @return clanId value
     */
    public long getClanId() {
        return this.clanId;
    }

    /**
     * set clanId && set marked
     *
     * @param clanId new value
     * @return current object
     */
    public SpyPlaneProp setClanId(long clanId) {
        if (this.clanId != clanId) {
            this.mark(FIELD_INDEX_CLANID);
            this.clanId = clanId;
        }
        return this;
    }

    /**
     * inner set clanId
     *
     * @param clanId new value
     */
    private void innerSetClanId(long clanId) {
        this.clanId = clanId;
    }

    /**
     * get briefClanName
     *
     * @return briefClanName value
     */
    public String getBriefClanName() {
        return this.briefClanName;
    }

    /**
     * set briefClanName && set marked
     *
     * @param briefClanName new value
     * @return current object
     */
    public SpyPlaneProp setBriefClanName(String briefClanName) {
        if (briefClanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.briefClanName, briefClanName)) {
            this.mark(FIELD_INDEX_BRIEFCLANNAME);
            this.briefClanName = briefClanName;
        }
        return this;
    }

    /**
     * inner set briefClanName
     *
     * @param briefClanName new value
     */
    private void innerSetBriefClanName(String briefClanName) {
        this.briefClanName = briefClanName;
    }

    /**
     * get playerPlaneId
     *
     * @return playerPlaneId value
     */
    public long getPlayerPlaneId() {
        return this.playerPlaneId;
    }

    /**
     * set playerPlaneId && set marked
     *
     * @param playerPlaneId new value
     * @return current object
     */
    public SpyPlaneProp setPlayerPlaneId(long playerPlaneId) {
        if (this.playerPlaneId != playerPlaneId) {
            this.mark(FIELD_INDEX_PLAYERPLANEID);
            this.playerPlaneId = playerPlaneId;
        }
        return this;
    }

    /**
     * inner set playerPlaneId
     *
     * @param playerPlaneId new value
     */
    private void innerSetPlayerPlaneId(long playerPlaneId) {
        this.playerPlaneId = playerPlaneId;
    }

    /**
     * get finishNeedReturn
     *
     * @return finishNeedReturn value
     */
    public boolean getFinishNeedReturn() {
        return this.finishNeedReturn;
    }

    /**
     * set finishNeedReturn && set marked
     *
     * @param finishNeedReturn new value
     * @return current object
     */
    public SpyPlaneProp setFinishNeedReturn(boolean finishNeedReturn) {
        if (this.finishNeedReturn != finishNeedReturn) {
            this.mark(FIELD_INDEX_FINISHNEEDRETURN);
            this.finishNeedReturn = finishNeedReturn;
        }
        return this;
    }

    /**
     * inner set finishNeedReturn
     *
     * @param finishNeedReturn new value
     */
    private void innerSetFinishNeedReturn(boolean finishNeedReturn) {
        this.finishNeedReturn = finishNeedReturn;
    }

    /**
     * get targetId
     *
     * @return targetId value
     */
    public long getTargetId() {
        return this.targetId;
    }

    /**
     * set targetId && set marked
     *
     * @param targetId new value
     * @return current object
     */
    public SpyPlaneProp setTargetId(long targetId) {
        if (this.targetId != targetId) {
            this.mark(FIELD_INDEX_TARGETID);
            this.targetId = targetId;
        }
        return this;
    }

    /**
     * inner set targetId
     *
     * @param targetId new value
     */
    private void innerSetTargetId(long targetId) {
        this.targetId = targetId;
    }

    /**
     * get spyType
     *
     * @return spyType value
     */
    public SpyType getSpyType() {
        return this.spyType;
    }

    /**
     * set spyType && set marked
     *
     * @param spyType new value
     * @return current object
     */
    public SpyPlaneProp setSpyType(SpyType spyType) {
        if (spyType == null) {
            throw new NullPointerException();
        }
        if (this.spyType != spyType) {
            this.mark(FIELD_INDEX_SPYTYPE);
            this.spyType = spyType;
        }
        return this;
    }

    /**
     * inner set spyType
     *
     * @param spyType new value
     */
    private void innerSetSpyType(SpyType spyType) {
        this.spyType = spyType;
    }

    /**
     * get targetPlayerId
     *
     * @return targetPlayerId value
     */
    public long getTargetPlayerId() {
        return this.targetPlayerId;
    }

    /**
     * set targetPlayerId && set marked
     *
     * @param targetPlayerId new value
     * @return current object
     */
    public SpyPlaneProp setTargetPlayerId(long targetPlayerId) {
        if (this.targetPlayerId != targetPlayerId) {
            this.mark(FIELD_INDEX_TARGETPLAYERID);
            this.targetPlayerId = targetPlayerId;
        }
        return this;
    }

    /**
     * inner set targetPlayerId
     *
     * @param targetPlayerId new value
     */
    private void innerSetTargetPlayerId(long targetPlayerId) {
        this.targetPlayerId = targetPlayerId;
    }

    /**
     * get totalExploreGridSize
     *
     * @return totalExploreGridSize value
     */
    public int getTotalExploreGridSize() {
        return this.totalExploreGridSize;
    }

    /**
     * set totalExploreGridSize && set marked
     *
     * @param totalExploreGridSize new value
     * @return current object
     */
    public SpyPlaneProp setTotalExploreGridSize(int totalExploreGridSize) {
        if (this.totalExploreGridSize != totalExploreGridSize) {
            this.mark(FIELD_INDEX_TOTALEXPLOREGRIDSIZE);
            this.totalExploreGridSize = totalExploreGridSize;
        }
        return this;
    }

    /**
     * inner set totalExploreGridSize
     *
     * @param totalExploreGridSize new value
     */
    private void innerSetTotalExploreGridSize(int totalExploreGridSize) {
        this.totalExploreGridSize = totalExploreGridSize;
    }

    /**
     * get stayStartStamp
     *
     * @return stayStartStamp value
     */
    public long getStayStartStamp() {
        return this.stayStartStamp;
    }

    /**
     * set stayStartStamp && set marked
     *
     * @param stayStartStamp new value
     * @return current object
     */
    public SpyPlaneProp setStayStartStamp(long stayStartStamp) {
        if (this.stayStartStamp != stayStartStamp) {
            this.mark(FIELD_INDEX_STAYSTARTSTAMP);
            this.stayStartStamp = stayStartStamp;
        }
        return this;
    }

    /**
     * inner set stayStartStamp
     *
     * @param stayStartStamp new value
     */
    private void innerSetStayStartStamp(long stayStartStamp) {
        this.stayStartStamp = stayStartStamp;
    }

    /**
     * get surveyS
     *
     * @return surveyS value
     */
    public long getSurveyS() {
        return this.surveyS;
    }

    /**
     * set surveyS && set marked
     *
     * @param surveyS new value
     * @return current object
     */
    public SpyPlaneProp setSurveyS(long surveyS) {
        if (this.surveyS != surveyS) {
            this.mark(FIELD_INDEX_SURVEYS);
            this.surveyS = surveyS;
        }
        return this;
    }

    /**
     * inner set surveyS
     *
     * @param surveyS new value
     */
    private void innerSetSurveyS(long surveyS) {
        this.surveyS = surveyS;
    }

    /**
     * get surveyE
     *
     * @return surveyE value
     */
    public long getSurveyE() {
        return this.surveyE;
    }

    /**
     * set surveyE && set marked
     *
     * @param surveyE new value
     * @return current object
     */
    public SpyPlaneProp setSurveyE(long surveyE) {
        if (this.surveyE != surveyE) {
            this.mark(FIELD_INDEX_SURVEYE);
            this.surveyE = surveyE;
        }
        return this;
    }

    /**
     * inner set surveyE
     *
     * @param surveyE new value
     */
    private void innerSetSurveyE(long surveyE) {
        this.surveyE = surveyE;
    }

    /**
     * get targetConfigId
     *
     * @return targetConfigId value
     */
    public long getTargetConfigId() {
        return this.targetConfigId;
    }

    /**
     * set targetConfigId && set marked
     *
     * @param targetConfigId new value
     * @return current object
     */
    public SpyPlaneProp setTargetConfigId(long targetConfigId) {
        if (this.targetConfigId != targetConfigId) {
            this.mark(FIELD_INDEX_TARGETCONFIGID);
            this.targetConfigId = targetConfigId;
        }
        return this;
    }

    /**
     * inner set targetConfigId
     *
     * @param targetConfigId new value
     */
    private void innerSetTargetConfigId(long targetConfigId) {
        this.targetConfigId = targetConfigId;
    }

    /**
     * get camp
     *
     * @return camp value
     */
    public Camp getCamp() {
        return this.camp;
    }

    /**
     * set camp && set marked
     *
     * @param camp new value
     * @return current object
     */
    public SpyPlaneProp setCamp(Camp camp) {
        if (camp == null) {
            throw new NullPointerException();
        }
        if (this.camp != camp) {
            this.mark(FIELD_INDEX_CAMP);
            this.camp = camp;
        }
        return this;
    }

    /**
     * inner set camp
     *
     * @param camp new value
     */
    private void innerSetCamp(Camp camp) {
        this.camp = camp;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public SpyPlaneProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyPlaneEntityPB.Builder getCopyCsBuilder() {
        final SpyPlaneEntityPB.Builder builder = SpyPlaneEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SpyPlaneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.move != null) {
            StructPB.MovePB.Builder tmpBuilder = StructPB.MovePB.newBuilder();
            final int tmpFieldCnt = this.move.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.exploreGridId != null) {
            StructPB.PointListPB.Builder tmpBuilder = StructPB.PointListPB.newBuilder();
            final int tmpFieldCnt = this.exploreGridId.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExploreGridId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExploreGridId();
            }
        }  else if (builder.hasExploreGridId()) {
            // 清理ExploreGridId
            builder.clearExploreGridId();
            fieldCnt++;
        }
        if (this.getAction() != SpyPlaneActionType.forNumber(0)) {
            builder.setAction(this.getAction());
            fieldCnt++;
        }  else if (builder.hasAction()) {
            // 清理Action
            builder.clearAction();
            fieldCnt++;
        }
        if (this.getState() != SpyPlaneState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getBriefClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }  else if (builder.hasBriefClanName()) {
            // 清理BriefClanName
            builder.clearBriefClanName();
            fieldCnt++;
        }
        if (this.getPlayerPlaneId() != 0L) {
            builder.setPlayerPlaneId(this.getPlayerPlaneId());
            fieldCnt++;
        }  else if (builder.hasPlayerPlaneId()) {
            // 清理PlayerPlaneId
            builder.clearPlayerPlaneId();
            fieldCnt++;
        }
        if (this.getFinishNeedReturn()) {
            builder.setFinishNeedReturn(this.getFinishNeedReturn());
            fieldCnt++;
        }  else if (builder.hasFinishNeedReturn()) {
            // 清理FinishNeedReturn
            builder.clearFinishNeedReturn();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.getSpyType() != SpyType.forNumber(0)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }  else if (builder.hasSpyType()) {
            // 清理SpyType
            builder.clearSpyType();
            fieldCnt++;
        }
        if (this.getTargetPlayerId() != 0L) {
            builder.setTargetPlayerId(this.getTargetPlayerId());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerId()) {
            // 清理TargetPlayerId
            builder.clearTargetPlayerId();
            fieldCnt++;
        }
        if (this.getTotalExploreGridSize() != 0) {
            builder.setTotalExploreGridSize(this.getTotalExploreGridSize());
            fieldCnt++;
        }  else if (builder.hasTotalExploreGridSize()) {
            // 清理TotalExploreGridSize
            builder.clearTotalExploreGridSize();
            fieldCnt++;
        }
        if (this.getStayStartStamp() != 0L) {
            builder.setStayStartStamp(this.getStayStartStamp());
            fieldCnt++;
        }  else if (builder.hasStayStartStamp()) {
            // 清理StayStartStamp
            builder.clearStayStartStamp();
            fieldCnt++;
        }
        if (this.getSurveyS() != 0L) {
            builder.setSurveyS(this.getSurveyS());
            fieldCnt++;
        }  else if (builder.hasSurveyS()) {
            // 清理SurveyS
            builder.clearSurveyS();
            fieldCnt++;
        }
        if (this.getSurveyE() != 0L) {
            builder.setSurveyE(this.getSurveyE());
            fieldCnt++;
        }  else if (builder.hasSurveyE()) {
            // 清理SurveyE
            builder.clearSurveyE();
            fieldCnt++;
        }
        if (this.getTargetConfigId() != 0L) {
            builder.setTargetConfigId(this.getTargetConfigId());
            fieldCnt++;
        }  else if (builder.hasTargetConfigId()) {
            // 清理TargetConfigId
            builder.clearTargetConfigId();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SpyPlaneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREGRIDID) && this.exploreGridId != null) {
            final boolean needClear = !builder.hasExploreGridId();
            final int tmpFieldCnt = this.exploreGridId.copyChangeToCs(builder.getExploreGridIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreGridId();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTION)) {
            builder.setAction(this.getAction());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEID)) {
            builder.setPlayerPlaneId(this.getPlayerPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHNEEDRETURN)) {
            builder.setFinishNeedReturn(this.getFinishNeedReturn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPYTYPE)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERID)) {
            builder.setTargetPlayerId(this.getTargetPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALEXPLOREGRIDSIZE)) {
            builder.setTotalExploreGridSize(this.getTotalExploreGridSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAYSTARTSTAMP)) {
            builder.setStayStartStamp(this.getStayStartStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYS)) {
            builder.setSurveyS(this.getSurveyS());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYE)) {
            builder.setSurveyE(this.getSurveyE());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCONFIGID)) {
            builder.setTargetConfigId(this.getTargetConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SpyPlaneEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToAndClearDeleteKeysCs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREGRIDID) && this.exploreGridId != null) {
            final boolean needClear = !builder.hasExploreGridId();
            final int tmpFieldCnt = this.exploreGridId.copyChangeToCs(builder.getExploreGridIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreGridId();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTION)) {
            builder.setAction(this.getAction());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEID)) {
            builder.setPlayerPlaneId(this.getPlayerPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHNEEDRETURN)) {
            builder.setFinishNeedReturn(this.getFinishNeedReturn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPYTYPE)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERID)) {
            builder.setTargetPlayerId(this.getTargetPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALEXPLOREGRIDSIZE)) {
            builder.setTotalExploreGridSize(this.getTotalExploreGridSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAYSTARTSTAMP)) {
            builder.setStayStartStamp(this.getStayStartStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYS)) {
            builder.setSurveyS(this.getSurveyS());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYE)) {
            builder.setSurveyE(this.getSurveyE());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCONFIGID)) {
            builder.setTargetConfigId(this.getTargetConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SpyPlaneEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromCs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromCs(proto.getMove());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExploreGridId()) {
            this.getExploreGridId().mergeFromCs(proto.getExploreGridId());
        } else {
            if (this.exploreGridId != null) {
                this.exploreGridId.mergeFromCs(proto.getExploreGridId());
            }
        }
        if (proto.hasAction()) {
            this.innerSetAction(proto.getAction());
        } else {
            this.innerSetAction(SpyPlaneActionType.forNumber(0));
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(SpyPlaneState.forNumber(0));
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBriefClanName()) {
            this.innerSetBriefClanName(proto.getBriefClanName());
        } else {
            this.innerSetBriefClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPlayerPlaneId()) {
            this.innerSetPlayerPlaneId(proto.getPlayerPlaneId());
        } else {
            this.innerSetPlayerPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFinishNeedReturn()) {
            this.innerSetFinishNeedReturn(proto.getFinishNeedReturn());
        } else {
            this.innerSetFinishNeedReturn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpyType()) {
            this.innerSetSpyType(proto.getSpyType());
        } else {
            this.innerSetSpyType(SpyType.forNumber(0));
        }
        if (proto.hasTargetPlayerId()) {
            this.innerSetTargetPlayerId(proto.getTargetPlayerId());
        } else {
            this.innerSetTargetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTotalExploreGridSize()) {
            this.innerSetTotalExploreGridSize(proto.getTotalExploreGridSize());
        } else {
            this.innerSetTotalExploreGridSize(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStayStartStamp()) {
            this.innerSetStayStartStamp(proto.getStayStartStamp());
        } else {
            this.innerSetStayStartStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSurveyS()) {
            this.innerSetSurveyS(proto.getSurveyS());
        } else {
            this.innerSetSurveyS(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSurveyE()) {
            this.innerSetSurveyE(proto.getSurveyE());
        } else {
            this.innerSetSurveyE(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetConfigId()) {
            this.innerSetTargetConfigId(proto.getTargetConfigId());
        } else {
            this.innerSetTargetConfigId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SpyPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SpyPlaneEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromCs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasExploreGridId()) {
            this.getExploreGridId().mergeChangeFromCs(proto.getExploreGridId());
            fieldCnt++;
        }
        if (proto.hasAction()) {
            this.setAction(proto.getAction());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasBriefClanName()) {
            this.setBriefClanName(proto.getBriefClanName());
            fieldCnt++;
        }
        if (proto.hasPlayerPlaneId()) {
            this.setPlayerPlaneId(proto.getPlayerPlaneId());
            fieldCnt++;
        }
        if (proto.hasFinishNeedReturn()) {
            this.setFinishNeedReturn(proto.getFinishNeedReturn());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasSpyType()) {
            this.setSpyType(proto.getSpyType());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerId()) {
            this.setTargetPlayerId(proto.getTargetPlayerId());
            fieldCnt++;
        }
        if (proto.hasTotalExploreGridSize()) {
            this.setTotalExploreGridSize(proto.getTotalExploreGridSize());
            fieldCnt++;
        }
        if (proto.hasStayStartStamp()) {
            this.setStayStartStamp(proto.getStayStartStamp());
            fieldCnt++;
        }
        if (proto.hasSurveyS()) {
            this.setSurveyS(proto.getSurveyS());
            fieldCnt++;
        }
        if (proto.hasSurveyE()) {
            this.setSurveyE(proto.getSurveyE());
            fieldCnt++;
        }
        if (proto.hasTargetConfigId()) {
            this.setTargetConfigId(proto.getTargetConfigId());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyPlaneEntity.Builder getCopyDbBuilder() {
        final SpyPlaneEntity.Builder builder = SpyPlaneEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SpyPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.exploreGridId != null) {
            Struct.PointList.Builder tmpBuilder = Struct.PointList.newBuilder();
            final int tmpFieldCnt = this.exploreGridId.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExploreGridId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExploreGridId();
            }
        }  else if (builder.hasExploreGridId()) {
            // 清理ExploreGridId
            builder.clearExploreGridId();
            fieldCnt++;
        }
        if (this.getAction() != SpyPlaneActionType.forNumber(0)) {
            builder.setAction(this.getAction());
            fieldCnt++;
        }  else if (builder.hasAction()) {
            // 清理Action
            builder.clearAction();
            fieldCnt++;
        }
        if (this.getState() != SpyPlaneState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getBriefClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }  else if (builder.hasBriefClanName()) {
            // 清理BriefClanName
            builder.clearBriefClanName();
            fieldCnt++;
        }
        if (this.getPlayerPlaneId() != 0L) {
            builder.setPlayerPlaneId(this.getPlayerPlaneId());
            fieldCnt++;
        }  else if (builder.hasPlayerPlaneId()) {
            // 清理PlayerPlaneId
            builder.clearPlayerPlaneId();
            fieldCnt++;
        }
        if (this.getFinishNeedReturn()) {
            builder.setFinishNeedReturn(this.getFinishNeedReturn());
            fieldCnt++;
        }  else if (builder.hasFinishNeedReturn()) {
            // 清理FinishNeedReturn
            builder.clearFinishNeedReturn();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.getSpyType() != SpyType.forNumber(0)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }  else if (builder.hasSpyType()) {
            // 清理SpyType
            builder.clearSpyType();
            fieldCnt++;
        }
        if (this.getTargetPlayerId() != 0L) {
            builder.setTargetPlayerId(this.getTargetPlayerId());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerId()) {
            // 清理TargetPlayerId
            builder.clearTargetPlayerId();
            fieldCnt++;
        }
        if (this.getTotalExploreGridSize() != 0) {
            builder.setTotalExploreGridSize(this.getTotalExploreGridSize());
            fieldCnt++;
        }  else if (builder.hasTotalExploreGridSize()) {
            // 清理TotalExploreGridSize
            builder.clearTotalExploreGridSize();
            fieldCnt++;
        }
        if (this.getStayStartStamp() != 0L) {
            builder.setStayStartStamp(this.getStayStartStamp());
            fieldCnt++;
        }  else if (builder.hasStayStartStamp()) {
            // 清理StayStartStamp
            builder.clearStayStartStamp();
            fieldCnt++;
        }
        if (this.getSurveyS() != 0L) {
            builder.setSurveyS(this.getSurveyS());
            fieldCnt++;
        }  else if (builder.hasSurveyS()) {
            // 清理SurveyS
            builder.clearSurveyS();
            fieldCnt++;
        }
        if (this.getSurveyE() != 0L) {
            builder.setSurveyE(this.getSurveyE());
            fieldCnt++;
        }  else if (builder.hasSurveyE()) {
            // 清理SurveyE
            builder.clearSurveyE();
            fieldCnt++;
        }
        if (this.getTargetConfigId() != 0L) {
            builder.setTargetConfigId(this.getTargetConfigId());
            fieldCnt++;
        }  else if (builder.hasTargetConfigId()) {
            // 清理TargetConfigId
            builder.clearTargetConfigId();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SpyPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToDb(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREGRIDID) && this.exploreGridId != null) {
            final boolean needClear = !builder.hasExploreGridId();
            final int tmpFieldCnt = this.exploreGridId.copyChangeToDb(builder.getExploreGridIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreGridId();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTION)) {
            builder.setAction(this.getAction());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEID)) {
            builder.setPlayerPlaneId(this.getPlayerPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHNEEDRETURN)) {
            builder.setFinishNeedReturn(this.getFinishNeedReturn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPYTYPE)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERID)) {
            builder.setTargetPlayerId(this.getTargetPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALEXPLOREGRIDSIZE)) {
            builder.setTotalExploreGridSize(this.getTotalExploreGridSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAYSTARTSTAMP)) {
            builder.setStayStartStamp(this.getStayStartStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYS)) {
            builder.setSurveyS(this.getSurveyS());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYE)) {
            builder.setSurveyE(this.getSurveyE());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCONFIGID)) {
            builder.setTargetConfigId(this.getTargetConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SpyPlaneEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromDb(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromDb(proto.getMove());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExploreGridId()) {
            this.getExploreGridId().mergeFromDb(proto.getExploreGridId());
        } else {
            if (this.exploreGridId != null) {
                this.exploreGridId.mergeFromDb(proto.getExploreGridId());
            }
        }
        if (proto.hasAction()) {
            this.innerSetAction(proto.getAction());
        } else {
            this.innerSetAction(SpyPlaneActionType.forNumber(0));
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(SpyPlaneState.forNumber(0));
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBriefClanName()) {
            this.innerSetBriefClanName(proto.getBriefClanName());
        } else {
            this.innerSetBriefClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPlayerPlaneId()) {
            this.innerSetPlayerPlaneId(proto.getPlayerPlaneId());
        } else {
            this.innerSetPlayerPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFinishNeedReturn()) {
            this.innerSetFinishNeedReturn(proto.getFinishNeedReturn());
        } else {
            this.innerSetFinishNeedReturn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpyType()) {
            this.innerSetSpyType(proto.getSpyType());
        } else {
            this.innerSetSpyType(SpyType.forNumber(0));
        }
        if (proto.hasTargetPlayerId()) {
            this.innerSetTargetPlayerId(proto.getTargetPlayerId());
        } else {
            this.innerSetTargetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTotalExploreGridSize()) {
            this.innerSetTotalExploreGridSize(proto.getTotalExploreGridSize());
        } else {
            this.innerSetTotalExploreGridSize(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStayStartStamp()) {
            this.innerSetStayStartStamp(proto.getStayStartStamp());
        } else {
            this.innerSetStayStartStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSurveyS()) {
            this.innerSetSurveyS(proto.getSurveyS());
        } else {
            this.innerSetSurveyS(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSurveyE()) {
            this.innerSetSurveyE(proto.getSurveyE());
        } else {
            this.innerSetSurveyE(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetConfigId()) {
            this.innerSetTargetConfigId(proto.getTargetConfigId());
        } else {
            this.innerSetTargetConfigId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SpyPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SpyPlaneEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromDb(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasExploreGridId()) {
            this.getExploreGridId().mergeChangeFromDb(proto.getExploreGridId());
            fieldCnt++;
        }
        if (proto.hasAction()) {
            this.setAction(proto.getAction());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasBriefClanName()) {
            this.setBriefClanName(proto.getBriefClanName());
            fieldCnt++;
        }
        if (proto.hasPlayerPlaneId()) {
            this.setPlayerPlaneId(proto.getPlayerPlaneId());
            fieldCnt++;
        }
        if (proto.hasFinishNeedReturn()) {
            this.setFinishNeedReturn(proto.getFinishNeedReturn());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasSpyType()) {
            this.setSpyType(proto.getSpyType());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerId()) {
            this.setTargetPlayerId(proto.getTargetPlayerId());
            fieldCnt++;
        }
        if (proto.hasTotalExploreGridSize()) {
            this.setTotalExploreGridSize(proto.getTotalExploreGridSize());
            fieldCnt++;
        }
        if (proto.hasStayStartStamp()) {
            this.setStayStartStamp(proto.getStayStartStamp());
            fieldCnt++;
        }
        if (proto.hasSurveyS()) {
            this.setSurveyS(proto.getSurveyS());
            fieldCnt++;
        }
        if (proto.hasSurveyE()) {
            this.setSurveyE(proto.getSurveyE());
            fieldCnt++;
        }
        if (proto.hasTargetConfigId()) {
            this.setTargetConfigId(proto.getTargetConfigId());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SpyPlaneEntity.Builder getCopySsBuilder() {
        final SpyPlaneEntity.Builder builder = SpyPlaneEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SpyPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.move != null) {
            Struct.Move.Builder tmpBuilder = Struct.Move.newBuilder();
            final int tmpFieldCnt = this.move.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMove(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMove();
            }
        }  else if (builder.hasMove()) {
            // 清理Move
            builder.clearMove();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.exploreGridId != null) {
            Struct.PointList.Builder tmpBuilder = Struct.PointList.newBuilder();
            final int tmpFieldCnt = this.exploreGridId.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setExploreGridId(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearExploreGridId();
            }
        }  else if (builder.hasExploreGridId()) {
            // 清理ExploreGridId
            builder.clearExploreGridId();
            fieldCnt++;
        }
        if (this.getAction() != SpyPlaneActionType.forNumber(0)) {
            builder.setAction(this.getAction());
            fieldCnt++;
        }  else if (builder.hasAction()) {
            // 清理Action
            builder.clearAction();
            fieldCnt++;
        }
        if (this.getState() != SpyPlaneState.forNumber(0)) {
            builder.setState(this.getState());
            fieldCnt++;
        }  else if (builder.hasState()) {
            // 清理State
            builder.clearState();
            fieldCnt++;
        }
        if (!this.getOwnerName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }  else if (builder.hasOwnerName()) {
            // 清理OwnerName
            builder.clearOwnerName();
            fieldCnt++;
        }
        if (this.getClanId() != 0L) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }  else if (builder.hasClanId()) {
            // 清理ClanId
            builder.clearClanId();
            fieldCnt++;
        }
        if (!this.getBriefClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }  else if (builder.hasBriefClanName()) {
            // 清理BriefClanName
            builder.clearBriefClanName();
            fieldCnt++;
        }
        if (this.getPlayerPlaneId() != 0L) {
            builder.setPlayerPlaneId(this.getPlayerPlaneId());
            fieldCnt++;
        }  else if (builder.hasPlayerPlaneId()) {
            // 清理PlayerPlaneId
            builder.clearPlayerPlaneId();
            fieldCnt++;
        }
        if (this.getFinishNeedReturn()) {
            builder.setFinishNeedReturn(this.getFinishNeedReturn());
            fieldCnt++;
        }  else if (builder.hasFinishNeedReturn()) {
            // 清理FinishNeedReturn
            builder.clearFinishNeedReturn();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.getSpyType() != SpyType.forNumber(0)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }  else if (builder.hasSpyType()) {
            // 清理SpyType
            builder.clearSpyType();
            fieldCnt++;
        }
        if (this.getTargetPlayerId() != 0L) {
            builder.setTargetPlayerId(this.getTargetPlayerId());
            fieldCnt++;
        }  else if (builder.hasTargetPlayerId()) {
            // 清理TargetPlayerId
            builder.clearTargetPlayerId();
            fieldCnt++;
        }
        if (this.getTotalExploreGridSize() != 0) {
            builder.setTotalExploreGridSize(this.getTotalExploreGridSize());
            fieldCnt++;
        }  else if (builder.hasTotalExploreGridSize()) {
            // 清理TotalExploreGridSize
            builder.clearTotalExploreGridSize();
            fieldCnt++;
        }
        if (this.getStayStartStamp() != 0L) {
            builder.setStayStartStamp(this.getStayStartStamp());
            fieldCnt++;
        }  else if (builder.hasStayStartStamp()) {
            // 清理StayStartStamp
            builder.clearStayStartStamp();
            fieldCnt++;
        }
        if (this.getSurveyS() != 0L) {
            builder.setSurveyS(this.getSurveyS());
            fieldCnt++;
        }  else if (builder.hasSurveyS()) {
            // 清理SurveyS
            builder.clearSurveyS();
            fieldCnt++;
        }
        if (this.getSurveyE() != 0L) {
            builder.setSurveyE(this.getSurveyE());
            fieldCnt++;
        }  else if (builder.hasSurveyE()) {
            // 清理SurveyE
            builder.clearSurveyE();
            fieldCnt++;
        }
        if (this.getTargetConfigId() != 0L) {
            builder.setTargetConfigId(this.getTargetConfigId());
            fieldCnt++;
        }  else if (builder.hasTargetConfigId()) {
            // 清理TargetConfigId
            builder.clearTargetConfigId();
            fieldCnt++;
        }
        if (this.getCamp() != Camp.forNumber(0)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }  else if (builder.hasCamp()) {
            // 清理Camp
            builder.clearCamp();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SpyPlaneEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            final boolean needClear = !builder.hasMove();
            final int tmpFieldCnt = this.move.copyChangeToSs(builder.getMoveBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMove();
            }
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREGRIDID) && this.exploreGridId != null) {
            final boolean needClear = !builder.hasExploreGridId();
            final int tmpFieldCnt = this.exploreGridId.copyChangeToSs(builder.getExploreGridIdBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearExploreGridId();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTION)) {
            builder.setAction(this.getAction());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATE)) {
            builder.setState(this.getState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERNAME)) {
            builder.setOwnerName(this.getOwnerName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CLANID)) {
            builder.setClanId(this.getClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BRIEFCLANNAME)) {
            builder.setBriefClanName(this.getBriefClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERPLANEID)) {
            builder.setPlayerPlaneId(this.getPlayerPlaneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FINISHNEEDRETURN)) {
            builder.setFinishNeedReturn(this.getFinishNeedReturn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SPYTYPE)) {
            builder.setSpyType(this.getSpyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPLAYERID)) {
            builder.setTargetPlayerId(this.getTargetPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TOTALEXPLOREGRIDSIZE)) {
            builder.setTotalExploreGridSize(this.getTotalExploreGridSize());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STAYSTARTSTAMP)) {
            builder.setStayStartStamp(this.getStayStartStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYS)) {
            builder.setSurveyS(this.getSurveyS());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SURVEYE)) {
            builder.setSurveyE(this.getSurveyE());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCONFIGID)) {
            builder.setTargetConfigId(this.getTargetConfigId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CAMP)) {
            builder.setCamp(this.getCamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SpyPlaneEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMove()) {
            this.getMove().mergeFromSs(proto.getMove());
        } else {
            if (this.move != null) {
                this.move.mergeFromSs(proto.getMove());
            }
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasExploreGridId()) {
            this.getExploreGridId().mergeFromSs(proto.getExploreGridId());
        } else {
            if (this.exploreGridId != null) {
                this.exploreGridId.mergeFromSs(proto.getExploreGridId());
            }
        }
        if (proto.hasAction()) {
            this.innerSetAction(proto.getAction());
        } else {
            this.innerSetAction(SpyPlaneActionType.forNumber(0));
        }
        if (proto.hasState()) {
            this.innerSetState(proto.getState());
        } else {
            this.innerSetState(SpyPlaneState.forNumber(0));
        }
        if (proto.hasOwnerName()) {
            this.innerSetOwnerName(proto.getOwnerName());
        } else {
            this.innerSetOwnerName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasClanId()) {
            this.innerSetClanId(proto.getClanId());
        } else {
            this.innerSetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBriefClanName()) {
            this.innerSetBriefClanName(proto.getBriefClanName());
        } else {
            this.innerSetBriefClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasPlayerPlaneId()) {
            this.innerSetPlayerPlaneId(proto.getPlayerPlaneId());
        } else {
            this.innerSetPlayerPlaneId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasFinishNeedReturn()) {
            this.innerSetFinishNeedReturn(proto.getFinishNeedReturn());
        } else {
            this.innerSetFinishNeedReturn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSpyType()) {
            this.innerSetSpyType(proto.getSpyType());
        } else {
            this.innerSetSpyType(SpyType.forNumber(0));
        }
        if (proto.hasTargetPlayerId()) {
            this.innerSetTargetPlayerId(proto.getTargetPlayerId());
        } else {
            this.innerSetTargetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTotalExploreGridSize()) {
            this.innerSetTotalExploreGridSize(proto.getTotalExploreGridSize());
        } else {
            this.innerSetTotalExploreGridSize(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasStayStartStamp()) {
            this.innerSetStayStartStamp(proto.getStayStartStamp());
        } else {
            this.innerSetStayStartStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSurveyS()) {
            this.innerSetSurveyS(proto.getSurveyS());
        } else {
            this.innerSetSurveyS(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasSurveyE()) {
            this.innerSetSurveyE(proto.getSurveyE());
        } else {
            this.innerSetSurveyE(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetConfigId()) {
            this.innerSetTargetConfigId(proto.getTargetConfigId());
        } else {
            this.innerSetTargetConfigId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCamp()) {
            this.innerSetCamp(proto.getCamp());
        } else {
            this.innerSetCamp(Camp.forNumber(0));
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return SpyPlaneProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SpyPlaneEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasMove()) {
            this.getMove().mergeChangeFromSs(proto.getMove());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasExploreGridId()) {
            this.getExploreGridId().mergeChangeFromSs(proto.getExploreGridId());
            fieldCnt++;
        }
        if (proto.hasAction()) {
            this.setAction(proto.getAction());
            fieldCnt++;
        }
        if (proto.hasState()) {
            this.setState(proto.getState());
            fieldCnt++;
        }
        if (proto.hasOwnerName()) {
            this.setOwnerName(proto.getOwnerName());
            fieldCnt++;
        }
        if (proto.hasClanId()) {
            this.setClanId(proto.getClanId());
            fieldCnt++;
        }
        if (proto.hasBriefClanName()) {
            this.setBriefClanName(proto.getBriefClanName());
            fieldCnt++;
        }
        if (proto.hasPlayerPlaneId()) {
            this.setPlayerPlaneId(proto.getPlayerPlaneId());
            fieldCnt++;
        }
        if (proto.hasFinishNeedReturn()) {
            this.setFinishNeedReturn(proto.getFinishNeedReturn());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasSpyType()) {
            this.setSpyType(proto.getSpyType());
            fieldCnt++;
        }
        if (proto.hasTargetPlayerId()) {
            this.setTargetPlayerId(proto.getTargetPlayerId());
            fieldCnt++;
        }
        if (proto.hasTotalExploreGridSize()) {
            this.setTotalExploreGridSize(proto.getTotalExploreGridSize());
            fieldCnt++;
        }
        if (proto.hasStayStartStamp()) {
            this.setStayStartStamp(proto.getStayStartStamp());
            fieldCnt++;
        }
        if (proto.hasSurveyS()) {
            this.setSurveyS(proto.getSurveyS());
            fieldCnt++;
        }
        if (proto.hasSurveyE()) {
            this.setSurveyE(proto.getSurveyE());
            fieldCnt++;
        }
        if (proto.hasTargetConfigId()) {
            this.setTargetConfigId(proto.getTargetConfigId());
            fieldCnt++;
        }
        if (proto.hasCamp()) {
            this.setCamp(proto.getCamp());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SpyPlaneEntity.Builder builder = SpyPlaneEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_MOVE) && this.move != null) {
            this.move.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_EXPLOREGRIDID) && this.exploreGridId != null) {
            this.exploreGridId.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.move != null) {
            this.move.markAll();
        }
        if (this.exploreGridId != null) {
            this.exploreGridId.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("SpyPlaneProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("SpyPlaneProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SpyPlaneProp)) {
            return false;
        }
        final SpyPlaneProp otherNode = (SpyPlaneProp) node;
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (!this.getMove().compareDataTo(otherNode.getMove())) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (!this.getExploreGridId().compareDataTo(otherNode.getExploreGridId())) {
            return false;
        }
        if (this.action != otherNode.action) {
            return false;
        }
        if (this.state != otherNode.state) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.ownerName, otherNode.ownerName)) {
            return false;
        }
        if (this.clanId != otherNode.clanId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.briefClanName, otherNode.briefClanName)) {
            return false;
        }
        if (this.playerPlaneId != otherNode.playerPlaneId) {
            return false;
        }
        if (this.finishNeedReturn != otherNode.finishNeedReturn) {
            return false;
        }
        if (this.targetId != otherNode.targetId) {
            return false;
        }
        if (this.spyType != otherNode.spyType) {
            return false;
        }
        if (this.targetPlayerId != otherNode.targetPlayerId) {
            return false;
        }
        if (this.totalExploreGridSize != otherNode.totalExploreGridSize) {
            return false;
        }
        if (this.stayStartStamp != otherNode.stayStartStamp) {
            return false;
        }
        if (this.surveyS != otherNode.surveyS) {
            return false;
        }
        if (this.surveyE != otherNode.surveyE) {
            return false;
        }
        if (this.targetConfigId != otherNode.targetConfigId) {
            return false;
        }
        if (this.camp != otherNode.camp) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static SpyPlaneProp of(SpyPlaneEntity fullAttrDb, SpyPlaneEntity changeAttrDb) {
        SpyPlaneProp prop = new SpyPlaneProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 43;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
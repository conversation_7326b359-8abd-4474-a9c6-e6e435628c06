package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructBattle.Battle;
import com.yorha.proto.Basic;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BattlePB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BattleProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BATTLESTATE = 0;
    public static final int FIELD_INDEX_TARGETID = 1;
    public static final int FIELD_INDEX_MANY = 2;
    public static final int FIELD_INDEX_ACTIVETARGETID = 3;
    public static final int FIELD_INDEX_ROLESTATE = 4;
    public static final int FIELD_INDEX_RELATIONIDLIST = 5;
    public static final int FIELD_INDEX_HOSPITALFULLCAUSEDEADCOUNT = 6;
    public static final int FIELD_INDEX_SIEGEINFO = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private BattleState battleState = BattleState.forNumber(0);
    private long targetId = Constant.DEFAULT_LONG_VALUE;
    private int many = Constant.DEFAULT_INT_VALUE;
    private long activeTargetId = Constant.DEFAULT_LONG_VALUE;
    private BattleRoleState roleState = BattleRoleState.forNumber(0);
    private Int64ListProp relationIdList = null;
    private int hospitalFullCauseDeadCount = Constant.DEFAULT_INT_VALUE;
    private Int64SiegeInfoMapProp siegeInfo = null;

    public BattleProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BattleProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get battleState
     *
     * @return battleState value
     */
    public BattleState getBattleState() {
        return this.battleState;
    }

    /**
     * set battleState && set marked
     *
     * @param battleState new value
     * @return current object
     */
    public BattleProp setBattleState(BattleState battleState) {
        if (battleState == null) {
            throw new NullPointerException();
        }
        if (this.battleState != battleState) {
            this.mark(FIELD_INDEX_BATTLESTATE);
            this.battleState = battleState;
        }
        return this;
    }

    /**
     * inner set battleState
     *
     * @param battleState new value
     */
    private void innerSetBattleState(BattleState battleState) {
        this.battleState = battleState;
    }

    /**
     * get targetId
     *
     * @return targetId value
     */
    public long getTargetId() {
        return this.targetId;
    }

    /**
     * set targetId && set marked
     *
     * @param targetId new value
     * @return current object
     */
    public BattleProp setTargetId(long targetId) {
        if (this.targetId != targetId) {
            this.mark(FIELD_INDEX_TARGETID);
            this.targetId = targetId;
        }
        return this;
    }

    /**
     * inner set targetId
     *
     * @param targetId new value
     */
    private void innerSetTargetId(long targetId) {
        this.targetId = targetId;
    }

    /**
     * get many
     *
     * @return many value
     */
    public int getMany() {
        return this.many;
    }

    /**
     * set many && set marked
     *
     * @param many new value
     * @return current object
     */
    public BattleProp setMany(int many) {
        if (this.many != many) {
            this.mark(FIELD_INDEX_MANY);
            this.many = many;
        }
        return this;
    }

    /**
     * inner set many
     *
     * @param many new value
     */
    private void innerSetMany(int many) {
        this.many = many;
    }

    /**
     * get activeTargetId
     *
     * @return activeTargetId value
     */
    public long getActiveTargetId() {
        return this.activeTargetId;
    }

    /**
     * set activeTargetId && set marked
     *
     * @param activeTargetId new value
     * @return current object
     */
    public BattleProp setActiveTargetId(long activeTargetId) {
        if (this.activeTargetId != activeTargetId) {
            this.mark(FIELD_INDEX_ACTIVETARGETID);
            this.activeTargetId = activeTargetId;
        }
        return this;
    }

    /**
     * inner set activeTargetId
     *
     * @param activeTargetId new value
     */
    private void innerSetActiveTargetId(long activeTargetId) {
        this.activeTargetId = activeTargetId;
    }

    /**
     * get roleState
     *
     * @return roleState value
     */
    public BattleRoleState getRoleState() {
        return this.roleState;
    }

    /**
     * set roleState && set marked
     *
     * @param roleState new value
     * @return current object
     */
    public BattleProp setRoleState(BattleRoleState roleState) {
        if (roleState == null) {
            throw new NullPointerException();
        }
        if (this.roleState != roleState) {
            this.mark(FIELD_INDEX_ROLESTATE);
            this.roleState = roleState;
        }
        return this;
    }

    /**
     * inner set roleState
     *
     * @param roleState new value
     */
    private void innerSetRoleState(BattleRoleState roleState) {
        this.roleState = roleState;
    }

    /**
     * get relationIdList
     *
     * @return relationIdList value
     */
    public Int64ListProp getRelationIdList() {
        if (this.relationIdList == null) {
            this.relationIdList = new Int64ListProp(this, FIELD_INDEX_RELATIONIDLIST);
        }
        return this.relationIdList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRelationIdList(Long v) {
        this.getRelationIdList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long getRelationIdListIndex(int index) {
        return this.getRelationIdList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Long removeRelationIdList(Long v) {
        if (this.relationIdList == null) {
            return null;
        }
        if(this.relationIdList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRelationIdListSize() {
        if (this.relationIdList == null) {
            return 0;
        }
        return this.relationIdList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRelationIdListEmpty() {
        if (this.relationIdList == null) {
            return true;
        }
        return this.getRelationIdList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRelationIdList() {
        this.getRelationIdList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Long removeRelationIdListIndex(int index) {
        return this.getRelationIdList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Long setRelationIdListIndex(int index, Long v) {
        return this.getRelationIdList().set(index, v);
    }
    /**
     * get hospitalFullCauseDeadCount
     *
     * @return hospitalFullCauseDeadCount value
     */
    public int getHospitalFullCauseDeadCount() {
        return this.hospitalFullCauseDeadCount;
    }

    /**
     * set hospitalFullCauseDeadCount && set marked
     *
     * @param hospitalFullCauseDeadCount new value
     * @return current object
     */
    public BattleProp setHospitalFullCauseDeadCount(int hospitalFullCauseDeadCount) {
        if (this.hospitalFullCauseDeadCount != hospitalFullCauseDeadCount) {
            this.mark(FIELD_INDEX_HOSPITALFULLCAUSEDEADCOUNT);
            this.hospitalFullCauseDeadCount = hospitalFullCauseDeadCount;
        }
        return this;
    }

    /**
     * inner set hospitalFullCauseDeadCount
     *
     * @param hospitalFullCauseDeadCount new value
     */
    private void innerSetHospitalFullCauseDeadCount(int hospitalFullCauseDeadCount) {
        this.hospitalFullCauseDeadCount = hospitalFullCauseDeadCount;
    }

    /**
     * get siegeInfo
     *
     * @return siegeInfo value
     */
    public Int64SiegeInfoMapProp getSiegeInfo() {
        if (this.siegeInfo == null) {
            this.siegeInfo = new Int64SiegeInfoMapProp(this, FIELD_INDEX_SIEGEINFO);
        }
        return this.siegeInfo;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putSiegeInfoV(SiegeInfoProp v) {
        this.getSiegeInfo().put(v.getClanId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SiegeInfoProp addEmptySiegeInfo(Long k) {
        return this.getSiegeInfo().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getSiegeInfoSize() {
        if (this.siegeInfo == null) {
            return 0;
        }
        return this.siegeInfo.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isSiegeInfoEmpty() {
        if (this.siegeInfo == null) {
            return true;
        }
        return this.siegeInfo.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SiegeInfoProp getSiegeInfoV(Long k) {
        if (this.siegeInfo == null || !this.siegeInfo.containsKey(k)) {
            return null;
        }
        return this.siegeInfo.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearSiegeInfo() {
        if (this.siegeInfo != null) {
            this.siegeInfo.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeSiegeInfoV(Long k) {
        if (this.siegeInfo != null) {
            this.siegeInfo.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattlePB.Builder getCopyCsBuilder() {
        final BattlePB.Builder builder = BattlePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BattlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBattleState() != BattleState.forNumber(0)) {
            builder.setBattleState(this.getBattleState());
            fieldCnt++;
        }  else if (builder.hasBattleState()) {
            // 清理BattleState
            builder.clearBattleState();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.getMany() != 0) {
            builder.setMany(this.getMany());
            fieldCnt++;
        }  else if (builder.hasMany()) {
            // 清理Many
            builder.clearMany();
            fieldCnt++;
        }
        if (this.getActiveTargetId() != 0L) {
            builder.setActiveTargetId(this.getActiveTargetId());
            fieldCnt++;
        }  else if (builder.hasActiveTargetId()) {
            // 清理ActiveTargetId
            builder.clearActiveTargetId();
            fieldCnt++;
        }
        if (this.getRoleState() != BattleRoleState.forNumber(0)) {
            builder.setRoleState(this.getRoleState());
            fieldCnt++;
        }  else if (builder.hasRoleState()) {
            // 清理RoleState
            builder.clearRoleState();
            fieldCnt++;
        }
        if (this.relationIdList != null) {
            BasicPB.Int64ListPB.Builder tmpBuilder = BasicPB.Int64ListPB.newBuilder();
            final int tmpFieldCnt = this.relationIdList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRelationIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRelationIdList();
            }
        }  else if (builder.hasRelationIdList()) {
            // 清理RelationIdList
            builder.clearRelationIdList();
            fieldCnt++;
        }
        if (this.siegeInfo != null) {
            StructBattlePB.Int64SiegeInfoMapPB.Builder tmpBuilder = StructBattlePB.Int64SiegeInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.siegeInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSiegeInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSiegeInfo();
            }
        }  else if (builder.hasSiegeInfo()) {
            // 清理SiegeInfo
            builder.clearSiegeInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BattlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BATTLESTATE)) {
            builder.setBattleState(this.getBattleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MANY)) {
            builder.setMany(this.getMany());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVETARGETID)) {
            builder.setActiveTargetId(this.getActiveTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESTATE)) {
            builder.setRoleState(this.getRoleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RELATIONIDLIST) && this.relationIdList != null) {
            final boolean needClear = !builder.hasRelationIdList();
            final int tmpFieldCnt = this.relationIdList.copyChangeToCs(builder.getRelationIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRelationIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SIEGEINFO) && this.siegeInfo != null) {
            final boolean needClear = !builder.hasSiegeInfo();
            final int tmpFieldCnt = this.siegeInfo.copyChangeToCs(builder.getSiegeInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSiegeInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BattlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BATTLESTATE)) {
            builder.setBattleState(this.getBattleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MANY)) {
            builder.setMany(this.getMany());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVETARGETID)) {
            builder.setActiveTargetId(this.getActiveTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESTATE)) {
            builder.setRoleState(this.getRoleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RELATIONIDLIST) && this.relationIdList != null) {
            final boolean needClear = !builder.hasRelationIdList();
            final int tmpFieldCnt = this.relationIdList.copyChangeToCs(builder.getRelationIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRelationIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_SIEGEINFO) && this.siegeInfo != null) {
            final boolean needClear = !builder.hasSiegeInfo();
            final int tmpFieldCnt = this.siegeInfo.copyChangeToAndClearDeleteKeysCs(builder.getSiegeInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSiegeInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BattlePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBattleState()) {
            this.innerSetBattleState(proto.getBattleState());
        } else {
            this.innerSetBattleState(BattleState.forNumber(0));
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMany()) {
            this.innerSetMany(proto.getMany());
        } else {
            this.innerSetMany(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActiveTargetId()) {
            this.innerSetActiveTargetId(proto.getActiveTargetId());
        } else {
            this.innerSetActiveTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleState()) {
            this.innerSetRoleState(proto.getRoleState());
        } else {
            this.innerSetRoleState(BattleRoleState.forNumber(0));
        }
        if (proto.hasRelationIdList()) {
            this.getRelationIdList().mergeFromCs(proto.getRelationIdList());
        } else {
            if (this.relationIdList != null) {
                this.relationIdList.mergeFromCs(proto.getRelationIdList());
            }
        }
        if (proto.hasSiegeInfo()) {
            this.getSiegeInfo().mergeFromCs(proto.getSiegeInfo());
        } else {
            if (this.siegeInfo != null) {
                this.siegeInfo.mergeFromCs(proto.getSiegeInfo());
            }
        }
        this.markAll();
        return BattleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BattlePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBattleState()) {
            this.setBattleState(proto.getBattleState());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasMany()) {
            this.setMany(proto.getMany());
            fieldCnt++;
        }
        if (proto.hasActiveTargetId()) {
            this.setActiveTargetId(proto.getActiveTargetId());
            fieldCnt++;
        }
        if (proto.hasRoleState()) {
            this.setRoleState(proto.getRoleState());
            fieldCnt++;
        }
        if (proto.hasRelationIdList()) {
            this.getRelationIdList().mergeChangeFromCs(proto.getRelationIdList());
            fieldCnt++;
        }
        if (proto.hasSiegeInfo()) {
            this.getSiegeInfo().mergeChangeFromCs(proto.getSiegeInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Battle.Builder getCopyDbBuilder() {
        final Battle.Builder builder = Battle.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(Battle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBattleState() != BattleState.forNumber(0)) {
            builder.setBattleState(this.getBattleState());
            fieldCnt++;
        }  else if (builder.hasBattleState()) {
            // 清理BattleState
            builder.clearBattleState();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.getMany() != 0) {
            builder.setMany(this.getMany());
            fieldCnt++;
        }  else if (builder.hasMany()) {
            // 清理Many
            builder.clearMany();
            fieldCnt++;
        }
        if (this.getActiveTargetId() != 0L) {
            builder.setActiveTargetId(this.getActiveTargetId());
            fieldCnt++;
        }  else if (builder.hasActiveTargetId()) {
            // 清理ActiveTargetId
            builder.clearActiveTargetId();
            fieldCnt++;
        }
        if (this.getRoleState() != BattleRoleState.forNumber(0)) {
            builder.setRoleState(this.getRoleState());
            fieldCnt++;
        }  else if (builder.hasRoleState()) {
            // 清理RoleState
            builder.clearRoleState();
            fieldCnt++;
        }
        if (this.relationIdList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.relationIdList.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRelationIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRelationIdList();
            }
        }  else if (builder.hasRelationIdList()) {
            // 清理RelationIdList
            builder.clearRelationIdList();
            fieldCnt++;
        }
        if (this.getHospitalFullCauseDeadCount() != 0) {
            builder.setHospitalFullCauseDeadCount(this.getHospitalFullCauseDeadCount());
            fieldCnt++;
        }  else if (builder.hasHospitalFullCauseDeadCount()) {
            // 清理HospitalFullCauseDeadCount
            builder.clearHospitalFullCauseDeadCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(Battle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BATTLESTATE)) {
            builder.setBattleState(this.getBattleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MANY)) {
            builder.setMany(this.getMany());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVETARGETID)) {
            builder.setActiveTargetId(this.getActiveTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESTATE)) {
            builder.setRoleState(this.getRoleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RELATIONIDLIST) && this.relationIdList != null) {
            final boolean needClear = !builder.hasRelationIdList();
            final int tmpFieldCnt = this.relationIdList.copyChangeToDb(builder.getRelationIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRelationIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALFULLCAUSEDEADCOUNT)) {
            builder.setHospitalFullCauseDeadCount(this.getHospitalFullCauseDeadCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(Battle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBattleState()) {
            this.innerSetBattleState(proto.getBattleState());
        } else {
            this.innerSetBattleState(BattleState.forNumber(0));
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMany()) {
            this.innerSetMany(proto.getMany());
        } else {
            this.innerSetMany(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActiveTargetId()) {
            this.innerSetActiveTargetId(proto.getActiveTargetId());
        } else {
            this.innerSetActiveTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleState()) {
            this.innerSetRoleState(proto.getRoleState());
        } else {
            this.innerSetRoleState(BattleRoleState.forNumber(0));
        }
        if (proto.hasRelationIdList()) {
            this.getRelationIdList().mergeFromDb(proto.getRelationIdList());
        } else {
            if (this.relationIdList != null) {
                this.relationIdList.mergeFromDb(proto.getRelationIdList());
            }
        }
        if (proto.hasHospitalFullCauseDeadCount()) {
            this.innerSetHospitalFullCauseDeadCount(proto.getHospitalFullCauseDeadCount());
        } else {
            this.innerSetHospitalFullCauseDeadCount(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return BattleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(Battle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBattleState()) {
            this.setBattleState(proto.getBattleState());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasMany()) {
            this.setMany(proto.getMany());
            fieldCnt++;
        }
        if (proto.hasActiveTargetId()) {
            this.setActiveTargetId(proto.getActiveTargetId());
            fieldCnt++;
        }
        if (proto.hasRoleState()) {
            this.setRoleState(proto.getRoleState());
            fieldCnt++;
        }
        if (proto.hasRelationIdList()) {
            this.getRelationIdList().mergeChangeFromDb(proto.getRelationIdList());
            fieldCnt++;
        }
        if (proto.hasHospitalFullCauseDeadCount()) {
            this.setHospitalFullCauseDeadCount(proto.getHospitalFullCauseDeadCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public Battle.Builder getCopySsBuilder() {
        final Battle.Builder builder = Battle.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(Battle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBattleState() != BattleState.forNumber(0)) {
            builder.setBattleState(this.getBattleState());
            fieldCnt++;
        }  else if (builder.hasBattleState()) {
            // 清理BattleState
            builder.clearBattleState();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (this.getMany() != 0) {
            builder.setMany(this.getMany());
            fieldCnt++;
        }  else if (builder.hasMany()) {
            // 清理Many
            builder.clearMany();
            fieldCnt++;
        }
        if (this.getActiveTargetId() != 0L) {
            builder.setActiveTargetId(this.getActiveTargetId());
            fieldCnt++;
        }  else if (builder.hasActiveTargetId()) {
            // 清理ActiveTargetId
            builder.clearActiveTargetId();
            fieldCnt++;
        }
        if (this.getRoleState() != BattleRoleState.forNumber(0)) {
            builder.setRoleState(this.getRoleState());
            fieldCnt++;
        }  else if (builder.hasRoleState()) {
            // 清理RoleState
            builder.clearRoleState();
            fieldCnt++;
        }
        if (this.relationIdList != null) {
            Basic.Int64List.Builder tmpBuilder = Basic.Int64List.newBuilder();
            final int tmpFieldCnt = this.relationIdList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRelationIdList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRelationIdList();
            }
        }  else if (builder.hasRelationIdList()) {
            // 清理RelationIdList
            builder.clearRelationIdList();
            fieldCnt++;
        }
        if (this.getHospitalFullCauseDeadCount() != 0) {
            builder.setHospitalFullCauseDeadCount(this.getHospitalFullCauseDeadCount());
            fieldCnt++;
        }  else if (builder.hasHospitalFullCauseDeadCount()) {
            // 清理HospitalFullCauseDeadCount
            builder.clearHospitalFullCauseDeadCount();
            fieldCnt++;
        }
        if (this.siegeInfo != null) {
            StructBattle.Int64SiegeInfoMap.Builder tmpBuilder = StructBattle.Int64SiegeInfoMap.newBuilder();
            final int tmpFieldCnt = this.siegeInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setSiegeInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearSiegeInfo();
            }
        }  else if (builder.hasSiegeInfo()) {
            // 清理SiegeInfo
            builder.clearSiegeInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(Battle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BATTLESTATE)) {
            builder.setBattleState(this.getBattleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MANY)) {
            builder.setMany(this.getMany());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVETARGETID)) {
            builder.setActiveTargetId(this.getActiveTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ROLESTATE)) {
            builder.setRoleState(this.getRoleState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RELATIONIDLIST) && this.relationIdList != null) {
            final boolean needClear = !builder.hasRelationIdList();
            final int tmpFieldCnt = this.relationIdList.copyChangeToSs(builder.getRelationIdListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRelationIdList();
            }
        }
        if (this.hasMark(FIELD_INDEX_HOSPITALFULLCAUSEDEADCOUNT)) {
            builder.setHospitalFullCauseDeadCount(this.getHospitalFullCauseDeadCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SIEGEINFO) && this.siegeInfo != null) {
            final boolean needClear = !builder.hasSiegeInfo();
            final int tmpFieldCnt = this.siegeInfo.copyChangeToSs(builder.getSiegeInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearSiegeInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(Battle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBattleState()) {
            this.innerSetBattleState(proto.getBattleState());
        } else {
            this.innerSetBattleState(BattleState.forNumber(0));
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMany()) {
            this.innerSetMany(proto.getMany());
        } else {
            this.innerSetMany(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActiveTargetId()) {
            this.innerSetActiveTargetId(proto.getActiveTargetId());
        } else {
            this.innerSetActiveTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRoleState()) {
            this.innerSetRoleState(proto.getRoleState());
        } else {
            this.innerSetRoleState(BattleRoleState.forNumber(0));
        }
        if (proto.hasRelationIdList()) {
            this.getRelationIdList().mergeFromSs(proto.getRelationIdList());
        } else {
            if (this.relationIdList != null) {
                this.relationIdList.mergeFromSs(proto.getRelationIdList());
            }
        }
        if (proto.hasHospitalFullCauseDeadCount()) {
            this.innerSetHospitalFullCauseDeadCount(proto.getHospitalFullCauseDeadCount());
        } else {
            this.innerSetHospitalFullCauseDeadCount(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSiegeInfo()) {
            this.getSiegeInfo().mergeFromSs(proto.getSiegeInfo());
        } else {
            if (this.siegeInfo != null) {
                this.siegeInfo.mergeFromSs(proto.getSiegeInfo());
            }
        }
        this.markAll();
        return BattleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(Battle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBattleState()) {
            this.setBattleState(proto.getBattleState());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasMany()) {
            this.setMany(proto.getMany());
            fieldCnt++;
        }
        if (proto.hasActiveTargetId()) {
            this.setActiveTargetId(proto.getActiveTargetId());
            fieldCnt++;
        }
        if (proto.hasRoleState()) {
            this.setRoleState(proto.getRoleState());
            fieldCnt++;
        }
        if (proto.hasRelationIdList()) {
            this.getRelationIdList().mergeChangeFromSs(proto.getRelationIdList());
            fieldCnt++;
        }
        if (proto.hasHospitalFullCauseDeadCount()) {
            this.setHospitalFullCauseDeadCount(proto.getHospitalFullCauseDeadCount());
            fieldCnt++;
        }
        if (proto.hasSiegeInfo()) {
            this.getSiegeInfo().mergeChangeFromSs(proto.getSiegeInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        Battle.Builder builder = Battle.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_RELATIONIDLIST) && this.relationIdList != null) {
            this.relationIdList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SIEGEINFO) && this.siegeInfo != null) {
            this.siegeInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.relationIdList != null) {
            this.relationIdList.markAll();
        }
        if (this.siegeInfo != null) {
            this.siegeInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BattleProp)) {
            return false;
        }
        final BattleProp otherNode = (BattleProp) node;
        if (this.battleState != otherNode.battleState) {
            return false;
        }
        if (this.targetId != otherNode.targetId) {
            return false;
        }
        if (this.many != otherNode.many) {
            return false;
        }
        if (this.activeTargetId != otherNode.activeTargetId) {
            return false;
        }
        if (this.roleState != otherNode.roleState) {
            return false;
        }
        if (!this.getRelationIdList().compareDataTo(otherNode.getRelationIdList())) {
            return false;
        }
        if (this.hospitalFullCauseDeadCount != otherNode.hospitalFullCauseDeadCount) {
            return false;
        }
        if (!this.getSiegeInfo().compareDataTo(otherNode.getSiegeInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ActivityUnit;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivityUnitPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityUnitProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_UNITID = 0;
    public static final int FIELD_INDEX_UNITTYPE = 1;
    public static final int FIELD_INDEX_TASKUNIT = 2;
    public static final int FIELD_INDEX_SCOREREWARDUNIT = 3;
    public static final int FIELD_INDEX_FIVEDAYSUNIT = 4;
    public static final int FIELD_INDEX_DISCORDGOTOUNIT = 5;
    public static final int FIELD_INDEX_ONLINEREWARDUNIT = 6;
    public static final int FIELD_INDEX_TIMERREWARDUNIT = 7;
    public static final int FIELD_INDEX_HEROCOMEUNIT = 8;
    public static final int FIELD_INDEX_CHARGEGOODSCHAIN = 9;
    public static final int FIELD_INDEX_STOREUNIT = 10;
    public static final int FIELD_INDEX_ZLCBUNIT = 11;
    public static final int FIELD_INDEX_SCORERANKUNIT = 12;
    public static final int FIELD_INDEX_BESTCOMMANDERSUBRANKUNIT = 13;
    public static final int FIELD_INDEX_BESTCOMMANDERTOTALRANKUNIT = 14;
    public static final int FIELD_INDEX_BESTCOMMANDERUNIT = 15;
    public static final int FIELD_INDEX_LOTTERYUNIT = 16;
    public static final int FIELD_INDEX_GROWTHFUNDUNIT = 17;
    public static final int FIELD_INDEX_WEEKMONTHCARDUNIT = 18;
    public static final int FIELD_INDEX_CYCLECHARGEGOODS = 19;
    public static final int FIELD_INDEX_LOSTTRIGGERBUNDLEUNIT = 20;
    public static final int FIELD_INDEX_ONLINEGIFTUNIT = 21;
    public static final int FIELD_INDEX_STORERATINGUNIT = 22;
    public static final int FIELD_INDEX_TRIGGERUNIT = 23;
    public static final int FIELD_INDEX_FESTIVALUNIT = 24;
    public static final int FIELD_INDEX_BPUNIT = 25;
    public static final int FIELD_INDEX_CONTINUEUNIT = 26;
    public static final int FIELD_INDEX_CONTINUESGIFT = 27;
    public static final int FIELD_INDEX_LOTTERYNATASHA = 28;

    public static final int FIELD_COUNT = 29;

    private long markBits0 = 0L;

    private int unitId = Constant.DEFAULT_INT_VALUE;
    private ActivityUnitType unitType = ActivityUnitType.forNumber(0);
    private ActivityTaskUnitProp taskUnit = null;
    private ActivityScoreRewardUnitProp scoreRewardUnit = null;
    private ActivityFiveDaysUnitProp fiveDaysUnit = null;
    private ActivityDiscordGotoUnitProp discordGotoUnit = null;
    private ActivityOnlineRewardUnitProp onlineRewardUnit = null;
    private ActivityTimerRewardUnitProp timerRewardUnit = null;
    private ActivityHeroComeUnitProp heroComeUnit = null;
    private ActivityChargeGoodsChainUnitProp chargeGoodsChain = null;
    private ActivityStoreUnitProp storeUnit = null;
    private ActivityZlcbUnitProp zlcbUnit = null;
    private ActivityScoreRankUnitProp scoreRankUnit = null;
    private ActivityBestCommanderSubRankUnitProp bestCommanderSubRankUnit = null;
    private ActivityBestCommanderTotalRankUnitProp bestCommanderTotalRankUnit = null;
    private ActivityBestCommanderUnitProp bestCommanderUnit = null;
    private ActivityLotteryUnitProp lotteryUnit = null;
    private ActivityGrowthFundUnitProp growthFundUnit = null;
    private ActivityWeekMonthCardUnitProp weekMonthCardUnit = null;
    private ActivityCycleChargeGoodsUnitProp cycleChargeGoods = null;
    private ActivityLostTriggerBundleUnitProp lostTriggerBundleUnit = null;
    private ActivityOnlineGiftUnitProp onlineGiftUnit = null;
    private ActivityStoreRatingUnitProp storeRatingUnit = null;
    private ActivityTriggerUnitProp triggerunit = null;
    private ActivityFestivalUnitProp festivalUnit = null;
    private ActivityFestivalBpUnitProp bpUnit = null;
    private ActivityContinuesUnitProp continueUnit = null;
    private ActivityContinuesGiftUnitProp continuesGift = null;
    private ActivityLotteryNatashaUnitProp lotteryNatasha = null;

    public ActivityUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get unitId
     *
     * @return unitId value
     */
    public int getUnitId() {
        return this.unitId;
    }

    /**
     * set unitId && set marked
     *
     * @param unitId new value
     * @return current object
     */
    public ActivityUnitProp setUnitId(int unitId) {
        if (this.unitId != unitId) {
            this.mark(FIELD_INDEX_UNITID);
            this.unitId = unitId;
        }
        return this;
    }

    /**
     * inner set unitId
     *
     * @param unitId new value
     */
    private void innerSetUnitId(int unitId) {
        this.unitId = unitId;
    }

    /**
     * get unitType
     *
     * @return unitType value
     */
    public ActivityUnitType getUnitType() {
        return this.unitType;
    }

    /**
     * set unitType && set marked
     *
     * @param unitType new value
     * @return current object
     */
    public ActivityUnitProp setUnitType(ActivityUnitType unitType) {
        if (unitType == null) {
            throw new NullPointerException();
        }
        if (this.unitType != unitType) {
            this.mark(FIELD_INDEX_UNITTYPE);
            this.unitType = unitType;
        }
        return this;
    }

    /**
     * inner set unitType
     *
     * @param unitType new value
     */
    private void innerSetUnitType(ActivityUnitType unitType) {
        this.unitType = unitType;
    }

    /**
     * get taskUnit
     *
     * @return taskUnit value
     */
    public ActivityTaskUnitProp getTaskUnit() {
        if (this.taskUnit == null) {
            this.taskUnit = new ActivityTaskUnitProp(this, FIELD_INDEX_TASKUNIT);
        }
        return this.taskUnit;
    }

    /**
     * get scoreRewardUnit
     *
     * @return scoreRewardUnit value
     */
    public ActivityScoreRewardUnitProp getScoreRewardUnit() {
        if (this.scoreRewardUnit == null) {
            this.scoreRewardUnit = new ActivityScoreRewardUnitProp(this, FIELD_INDEX_SCOREREWARDUNIT);
        }
        return this.scoreRewardUnit;
    }

    /**
     * get fiveDaysUnit
     *
     * @return fiveDaysUnit value
     */
    public ActivityFiveDaysUnitProp getFiveDaysUnit() {
        if (this.fiveDaysUnit == null) {
            this.fiveDaysUnit = new ActivityFiveDaysUnitProp(this, FIELD_INDEX_FIVEDAYSUNIT);
        }
        return this.fiveDaysUnit;
    }

    /**
     * get discordGotoUnit
     *
     * @return discordGotoUnit value
     */
    public ActivityDiscordGotoUnitProp getDiscordGotoUnit() {
        if (this.discordGotoUnit == null) {
            this.discordGotoUnit = new ActivityDiscordGotoUnitProp(this, FIELD_INDEX_DISCORDGOTOUNIT);
        }
        return this.discordGotoUnit;
    }

    /**
     * get onlineRewardUnit
     *
     * @return onlineRewardUnit value
     */
    public ActivityOnlineRewardUnitProp getOnlineRewardUnit() {
        if (this.onlineRewardUnit == null) {
            this.onlineRewardUnit = new ActivityOnlineRewardUnitProp(this, FIELD_INDEX_ONLINEREWARDUNIT);
        }
        return this.onlineRewardUnit;
    }

    /**
     * get timerRewardUnit
     *
     * @return timerRewardUnit value
     */
    public ActivityTimerRewardUnitProp getTimerRewardUnit() {
        if (this.timerRewardUnit == null) {
            this.timerRewardUnit = new ActivityTimerRewardUnitProp(this, FIELD_INDEX_TIMERREWARDUNIT);
        }
        return this.timerRewardUnit;
    }

    /**
     * get heroComeUnit
     *
     * @return heroComeUnit value
     */
    public ActivityHeroComeUnitProp getHeroComeUnit() {
        if (this.heroComeUnit == null) {
            this.heroComeUnit = new ActivityHeroComeUnitProp(this, FIELD_INDEX_HEROCOMEUNIT);
        }
        return this.heroComeUnit;
    }

    /**
     * get chargeGoodsChain
     *
     * @return chargeGoodsChain value
     */
    public ActivityChargeGoodsChainUnitProp getChargeGoodsChain() {
        if (this.chargeGoodsChain == null) {
            this.chargeGoodsChain = new ActivityChargeGoodsChainUnitProp(this, FIELD_INDEX_CHARGEGOODSCHAIN);
        }
        return this.chargeGoodsChain;
    }

    /**
     * get storeUnit
     *
     * @return storeUnit value
     */
    public ActivityStoreUnitProp getStoreUnit() {
        if (this.storeUnit == null) {
            this.storeUnit = new ActivityStoreUnitProp(this, FIELD_INDEX_STOREUNIT);
        }
        return this.storeUnit;
    }

    /**
     * get zlcbUnit
     *
     * @return zlcbUnit value
     */
    public ActivityZlcbUnitProp getZlcbUnit() {
        if (this.zlcbUnit == null) {
            this.zlcbUnit = new ActivityZlcbUnitProp(this, FIELD_INDEX_ZLCBUNIT);
        }
        return this.zlcbUnit;
    }

    /**
     * get scoreRankUnit
     *
     * @return scoreRankUnit value
     */
    public ActivityScoreRankUnitProp getScoreRankUnit() {
        if (this.scoreRankUnit == null) {
            this.scoreRankUnit = new ActivityScoreRankUnitProp(this, FIELD_INDEX_SCORERANKUNIT);
        }
        return this.scoreRankUnit;
    }

    /**
     * get bestCommanderSubRankUnit
     *
     * @return bestCommanderSubRankUnit value
     */
    public ActivityBestCommanderSubRankUnitProp getBestCommanderSubRankUnit() {
        if (this.bestCommanderSubRankUnit == null) {
            this.bestCommanderSubRankUnit = new ActivityBestCommanderSubRankUnitProp(this, FIELD_INDEX_BESTCOMMANDERSUBRANKUNIT);
        }
        return this.bestCommanderSubRankUnit;
    }

    /**
     * get bestCommanderTotalRankUnit
     *
     * @return bestCommanderTotalRankUnit value
     */
    public ActivityBestCommanderTotalRankUnitProp getBestCommanderTotalRankUnit() {
        if (this.bestCommanderTotalRankUnit == null) {
            this.bestCommanderTotalRankUnit = new ActivityBestCommanderTotalRankUnitProp(this, FIELD_INDEX_BESTCOMMANDERTOTALRANKUNIT);
        }
        return this.bestCommanderTotalRankUnit;
    }

    /**
     * get bestCommanderUnit
     *
     * @return bestCommanderUnit value
     */
    public ActivityBestCommanderUnitProp getBestCommanderUnit() {
        if (this.bestCommanderUnit == null) {
            this.bestCommanderUnit = new ActivityBestCommanderUnitProp(this, FIELD_INDEX_BESTCOMMANDERUNIT);
        }
        return this.bestCommanderUnit;
    }

    /**
     * get lotteryUnit
     *
     * @return lotteryUnit value
     */
    public ActivityLotteryUnitProp getLotteryUnit() {
        if (this.lotteryUnit == null) {
            this.lotteryUnit = new ActivityLotteryUnitProp(this, FIELD_INDEX_LOTTERYUNIT);
        }
        return this.lotteryUnit;
    }

    /**
     * get growthFundUnit
     *
     * @return growthFundUnit value
     */
    public ActivityGrowthFundUnitProp getGrowthFundUnit() {
        if (this.growthFundUnit == null) {
            this.growthFundUnit = new ActivityGrowthFundUnitProp(this, FIELD_INDEX_GROWTHFUNDUNIT);
        }
        return this.growthFundUnit;
    }

    /**
     * get weekMonthCardUnit
     *
     * @return weekMonthCardUnit value
     */
    public ActivityWeekMonthCardUnitProp getWeekMonthCardUnit() {
        if (this.weekMonthCardUnit == null) {
            this.weekMonthCardUnit = new ActivityWeekMonthCardUnitProp(this, FIELD_INDEX_WEEKMONTHCARDUNIT);
        }
        return this.weekMonthCardUnit;
    }

    /**
     * get cycleChargeGoods
     *
     * @return cycleChargeGoods value
     */
    public ActivityCycleChargeGoodsUnitProp getCycleChargeGoods() {
        if (this.cycleChargeGoods == null) {
            this.cycleChargeGoods = new ActivityCycleChargeGoodsUnitProp(this, FIELD_INDEX_CYCLECHARGEGOODS);
        }
        return this.cycleChargeGoods;
    }

    /**
     * get lostTriggerBundleUnit
     *
     * @return lostTriggerBundleUnit value
     */
    public ActivityLostTriggerBundleUnitProp getLostTriggerBundleUnit() {
        if (this.lostTriggerBundleUnit == null) {
            this.lostTriggerBundleUnit = new ActivityLostTriggerBundleUnitProp(this, FIELD_INDEX_LOSTTRIGGERBUNDLEUNIT);
        }
        return this.lostTriggerBundleUnit;
    }

    /**
     * get onlineGiftUnit
     *
     * @return onlineGiftUnit value
     */
    public ActivityOnlineGiftUnitProp getOnlineGiftUnit() {
        if (this.onlineGiftUnit == null) {
            this.onlineGiftUnit = new ActivityOnlineGiftUnitProp(this, FIELD_INDEX_ONLINEGIFTUNIT);
        }
        return this.onlineGiftUnit;
    }

    /**
     * get storeRatingUnit
     *
     * @return storeRatingUnit value
     */
    public ActivityStoreRatingUnitProp getStoreRatingUnit() {
        if (this.storeRatingUnit == null) {
            this.storeRatingUnit = new ActivityStoreRatingUnitProp(this, FIELD_INDEX_STORERATINGUNIT);
        }
        return this.storeRatingUnit;
    }

    /**
     * get triggerunit
     *
     * @return triggerunit value
     */
    public ActivityTriggerUnitProp getTriggerunit() {
        if (this.triggerunit == null) {
            this.triggerunit = new ActivityTriggerUnitProp(this, FIELD_INDEX_TRIGGERUNIT);
        }
        return this.triggerunit;
    }

    /**
     * get festivalUnit
     *
     * @return festivalUnit value
     */
    public ActivityFestivalUnitProp getFestivalUnit() {
        if (this.festivalUnit == null) {
            this.festivalUnit = new ActivityFestivalUnitProp(this, FIELD_INDEX_FESTIVALUNIT);
        }
        return this.festivalUnit;
    }

    /**
     * get bpUnit
     *
     * @return bpUnit value
     */
    public ActivityFestivalBpUnitProp getBpUnit() {
        if (this.bpUnit == null) {
            this.bpUnit = new ActivityFestivalBpUnitProp(this, FIELD_INDEX_BPUNIT);
        }
        return this.bpUnit;
    }

    /**
     * get continueUnit
     *
     * @return continueUnit value
     */
    public ActivityContinuesUnitProp getContinueUnit() {
        if (this.continueUnit == null) {
            this.continueUnit = new ActivityContinuesUnitProp(this, FIELD_INDEX_CONTINUEUNIT);
        }
        return this.continueUnit;
    }

    /**
     * get continuesGift
     *
     * @return continuesGift value
     */
    public ActivityContinuesGiftUnitProp getContinuesGift() {
        if (this.continuesGift == null) {
            this.continuesGift = new ActivityContinuesGiftUnitProp(this, FIELD_INDEX_CONTINUESGIFT);
        }
        return this.continuesGift;
    }

    /**
     * get lotteryNatasha
     *
     * @return lotteryNatasha value
     */
    public ActivityLotteryNatashaUnitProp getLotteryNatasha() {
        if (this.lotteryNatasha == null) {
            this.lotteryNatasha = new ActivityLotteryNatashaUnitProp(this, FIELD_INDEX_LOTTERYNATASHA);
        }
        return this.lotteryNatasha;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityUnitPB.Builder getCopyCsBuilder() {
        final ActivityUnitPB.Builder builder = ActivityUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getUnitType() != ActivityUnitType.forNumber(0)) {
            builder.setUnitType(this.getUnitType());
            fieldCnt++;
        }  else if (builder.hasUnitType()) {
            // 清理UnitType
            builder.clearUnitType();
            fieldCnt++;
        }
        if (this.taskUnit != null) {
            StructPB.ActivityTaskUnitPB.Builder tmpBuilder = StructPB.ActivityTaskUnitPB.newBuilder();
            final int tmpFieldCnt = this.taskUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskUnit();
            }
        }  else if (builder.hasTaskUnit()) {
            // 清理TaskUnit
            builder.clearTaskUnit();
            fieldCnt++;
        }
        if (this.scoreRewardUnit != null) {
            StructPB.ActivityScoreRewardUnitPB.Builder tmpBuilder = StructPB.ActivityScoreRewardUnitPB.newBuilder();
            final int tmpFieldCnt = this.scoreRewardUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRewardUnit();
            }
        }  else if (builder.hasScoreRewardUnit()) {
            // 清理ScoreRewardUnit
            builder.clearScoreRewardUnit();
            fieldCnt++;
        }
        if (this.fiveDaysUnit != null) {
            StructPB.ActivityFiveDaysUnitPB.Builder tmpBuilder = StructPB.ActivityFiveDaysUnitPB.newBuilder();
            final int tmpFieldCnt = this.fiveDaysUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFiveDaysUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFiveDaysUnit();
            }
        }  else if (builder.hasFiveDaysUnit()) {
            // 清理FiveDaysUnit
            builder.clearFiveDaysUnit();
            fieldCnt++;
        }
        if (this.discordGotoUnit != null) {
            StructPB.ActivityDiscordGotoUnitPB.Builder tmpBuilder = StructPB.ActivityDiscordGotoUnitPB.newBuilder();
            final int tmpFieldCnt = this.discordGotoUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDiscordGotoUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDiscordGotoUnit();
            }
        }  else if (builder.hasDiscordGotoUnit()) {
            // 清理DiscordGotoUnit
            builder.clearDiscordGotoUnit();
            fieldCnt++;
        }
        if (this.onlineRewardUnit != null) {
            StructPB.ActivityOnlineRewardUnitPB.Builder tmpBuilder = StructPB.ActivityOnlineRewardUnitPB.newBuilder();
            final int tmpFieldCnt = this.onlineRewardUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOnlineRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOnlineRewardUnit();
            }
        }  else if (builder.hasOnlineRewardUnit()) {
            // 清理OnlineRewardUnit
            builder.clearOnlineRewardUnit();
            fieldCnt++;
        }
        if (this.timerRewardUnit != null) {
            StructPB.ActivityTimerRewardUnitPB.Builder tmpBuilder = StructPB.ActivityTimerRewardUnitPB.newBuilder();
            final int tmpFieldCnt = this.timerRewardUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTimerRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTimerRewardUnit();
            }
        }  else if (builder.hasTimerRewardUnit()) {
            // 清理TimerRewardUnit
            builder.clearTimerRewardUnit();
            fieldCnt++;
        }
        if (this.heroComeUnit != null) {
            StructPB.ActivityHeroComeUnitPB.Builder tmpBuilder = StructPB.ActivityHeroComeUnitPB.newBuilder();
            final int tmpFieldCnt = this.heroComeUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroComeUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroComeUnit();
            }
        }  else if (builder.hasHeroComeUnit()) {
            // 清理HeroComeUnit
            builder.clearHeroComeUnit();
            fieldCnt++;
        }
        if (this.chargeGoodsChain != null) {
            StructPB.ActivityChargeGoodsChainUnitPB.Builder tmpBuilder = StructPB.ActivityChargeGoodsChainUnitPB.newBuilder();
            final int tmpFieldCnt = this.chargeGoodsChain.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeGoodsChain(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeGoodsChain();
            }
        }  else if (builder.hasChargeGoodsChain()) {
            // 清理ChargeGoodsChain
            builder.clearChargeGoodsChain();
            fieldCnt++;
        }
        if (this.storeUnit != null) {
            StructPB.ActivityStoreUnitPB.Builder tmpBuilder = StructPB.ActivityStoreUnitPB.newBuilder();
            final int tmpFieldCnt = this.storeUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreUnit();
            }
        }  else if (builder.hasStoreUnit()) {
            // 清理StoreUnit
            builder.clearStoreUnit();
            fieldCnt++;
        }
        if (this.scoreRankUnit != null) {
            StructPB.ActivityScoreRankUnitPB.Builder tmpBuilder = StructPB.ActivityScoreRankUnitPB.newBuilder();
            final int tmpFieldCnt = this.scoreRankUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRankUnit();
            }
        }  else if (builder.hasScoreRankUnit()) {
            // 清理ScoreRankUnit
            builder.clearScoreRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderSubRankUnit != null) {
            StructPB.ActivityBestCommanderSubRankUnitPB.Builder tmpBuilder = StructPB.ActivityBestCommanderSubRankUnitPB.newBuilder();
            final int tmpFieldCnt = this.bestCommanderSubRankUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderSubRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderSubRankUnit();
            }
        }  else if (builder.hasBestCommanderSubRankUnit()) {
            // 清理BestCommanderSubRankUnit
            builder.clearBestCommanderSubRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderTotalRankUnit != null) {
            StructPB.ActivityBestCommanderTotalRankUnitPB.Builder tmpBuilder = StructPB.ActivityBestCommanderTotalRankUnitPB.newBuilder();
            final int tmpFieldCnt = this.bestCommanderTotalRankUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderTotalRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderTotalRankUnit();
            }
        }  else if (builder.hasBestCommanderTotalRankUnit()) {
            // 清理BestCommanderTotalRankUnit
            builder.clearBestCommanderTotalRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderUnit != null) {
            StructPB.ActivityBestCommanderUnitPB.Builder tmpBuilder = StructPB.ActivityBestCommanderUnitPB.newBuilder();
            final int tmpFieldCnt = this.bestCommanderUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderUnit();
            }
        }  else if (builder.hasBestCommanderUnit()) {
            // 清理BestCommanderUnit
            builder.clearBestCommanderUnit();
            fieldCnt++;
        }
        if (this.lotteryUnit != null) {
            StructPB.ActivityLotteryUnitPB.Builder tmpBuilder = StructPB.ActivityLotteryUnitPB.newBuilder();
            final int tmpFieldCnt = this.lotteryUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryUnit();
            }
        }  else if (builder.hasLotteryUnit()) {
            // 清理LotteryUnit
            builder.clearLotteryUnit();
            fieldCnt++;
        }
        if (this.growthFundUnit != null) {
            StructPB.ActivityGrowthFundUnitPB.Builder tmpBuilder = StructPB.ActivityGrowthFundUnitPB.newBuilder();
            final int tmpFieldCnt = this.growthFundUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGrowthFundUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGrowthFundUnit();
            }
        }  else if (builder.hasGrowthFundUnit()) {
            // 清理GrowthFundUnit
            builder.clearGrowthFundUnit();
            fieldCnt++;
        }
        if (this.weekMonthCardUnit != null) {
            StructPB.ActivityWeekMonthCardUnitPB.Builder tmpBuilder = StructPB.ActivityWeekMonthCardUnitPB.newBuilder();
            final int tmpFieldCnt = this.weekMonthCardUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWeekMonthCardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWeekMonthCardUnit();
            }
        }  else if (builder.hasWeekMonthCardUnit()) {
            // 清理WeekMonthCardUnit
            builder.clearWeekMonthCardUnit();
            fieldCnt++;
        }
        if (this.cycleChargeGoods != null) {
            StructPB.ActivityCycleChargeGoodsUnitPB.Builder tmpBuilder = StructPB.ActivityCycleChargeGoodsUnitPB.newBuilder();
            final int tmpFieldCnt = this.cycleChargeGoods.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCycleChargeGoods(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCycleChargeGoods();
            }
        }  else if (builder.hasCycleChargeGoods()) {
            // 清理CycleChargeGoods
            builder.clearCycleChargeGoods();
            fieldCnt++;
        }
        if (this.lostTriggerBundleUnit != null) {
            StructPB.ActivityLostTriggerBundleUnitPB.Builder tmpBuilder = StructPB.ActivityLostTriggerBundleUnitPB.newBuilder();
            final int tmpFieldCnt = this.lostTriggerBundleUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLostTriggerBundleUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLostTriggerBundleUnit();
            }
        }  else if (builder.hasLostTriggerBundleUnit()) {
            // 清理LostTriggerBundleUnit
            builder.clearLostTriggerBundleUnit();
            fieldCnt++;
        }
        if (this.onlineGiftUnit != null) {
            StructPB.ActivityOnlineGiftUnitPB.Builder tmpBuilder = StructPB.ActivityOnlineGiftUnitPB.newBuilder();
            final int tmpFieldCnt = this.onlineGiftUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOnlineGiftUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOnlineGiftUnit();
            }
        }  else if (builder.hasOnlineGiftUnit()) {
            // 清理OnlineGiftUnit
            builder.clearOnlineGiftUnit();
            fieldCnt++;
        }
        if (this.storeRatingUnit != null) {
            StructPB.ActivityStoreRatingUnitPB.Builder tmpBuilder = StructPB.ActivityStoreRatingUnitPB.newBuilder();
            final int tmpFieldCnt = this.storeRatingUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreRatingUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreRatingUnit();
            }
        }  else if (builder.hasStoreRatingUnit()) {
            // 清理StoreRatingUnit
            builder.clearStoreRatingUnit();
            fieldCnt++;
        }
        if (this.triggerunit != null) {
            StructPB.ActivityTriggerUnitPB.Builder tmpBuilder = StructPB.ActivityTriggerUnitPB.newBuilder();
            final int tmpFieldCnt = this.triggerunit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerunit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerunit();
            }
        }  else if (builder.hasTriggerunit()) {
            // 清理Triggerunit
            builder.clearTriggerunit();
            fieldCnt++;
        }
        if (this.festivalUnit != null) {
            StructPB.ActivityFestivalUnitPB.Builder tmpBuilder = StructPB.ActivityFestivalUnitPB.newBuilder();
            final int tmpFieldCnt = this.festivalUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFestivalUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFestivalUnit();
            }
        }  else if (builder.hasFestivalUnit()) {
            // 清理FestivalUnit
            builder.clearFestivalUnit();
            fieldCnt++;
        }
        if (this.bpUnit != null) {
            StructPB.ActivityFestivalBpUnitPB.Builder tmpBuilder = StructPB.ActivityFestivalBpUnitPB.newBuilder();
            final int tmpFieldCnt = this.bpUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpUnit();
            }
        }  else if (builder.hasBpUnit()) {
            // 清理BpUnit
            builder.clearBpUnit();
            fieldCnt++;
        }
        if (this.continueUnit != null) {
            StructPB.ActivityContinuesUnitPB.Builder tmpBuilder = StructPB.ActivityContinuesUnitPB.newBuilder();
            final int tmpFieldCnt = this.continueUnit.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContinueUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContinueUnit();
            }
        }  else if (builder.hasContinueUnit()) {
            // 清理ContinueUnit
            builder.clearContinueUnit();
            fieldCnt++;
        }
        if (this.continuesGift != null) {
            StructPB.ActivityContinuesGiftUnitPB.Builder tmpBuilder = StructPB.ActivityContinuesGiftUnitPB.newBuilder();
            final int tmpFieldCnt = this.continuesGift.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContinuesGift(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContinuesGift();
            }
        }  else if (builder.hasContinuesGift()) {
            // 清理ContinuesGift
            builder.clearContinuesGift();
            fieldCnt++;
        }
        if (this.lotteryNatasha != null) {
            StructPB.ActivityLotteryNatashaUnitPB.Builder tmpBuilder = StructPB.ActivityLotteryNatashaUnitPB.newBuilder();
            final int tmpFieldCnt = this.lotteryNatasha.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryNatasha(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryNatasha();
            }
        }  else if (builder.hasLotteryNatasha()) {
            // 清理LotteryNatasha
            builder.clearLotteryNatasha();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITTYPE)) {
            builder.setUnitType(this.getUnitType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToCs(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCOREREWARDUNIT) && this.scoreRewardUnit != null) {
            final boolean needClear = !builder.hasScoreRewardUnit();
            final int tmpFieldCnt = this.scoreRewardUnit.copyChangeToCs(builder.getScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIVEDAYSUNIT) && this.fiveDaysUnit != null) {
            final boolean needClear = !builder.hasFiveDaysUnit();
            final int tmpFieldCnt = this.fiveDaysUnit.copyChangeToCs(builder.getFiveDaysUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFiveDaysUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCORDGOTOUNIT) && this.discordGotoUnit != null) {
            final boolean needClear = !builder.hasDiscordGotoUnit();
            final int tmpFieldCnt = this.discordGotoUnit.copyChangeToCs(builder.getDiscordGotoUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscordGotoUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEREWARDUNIT) && this.onlineRewardUnit != null) {
            final boolean needClear = !builder.hasOnlineRewardUnit();
            final int tmpFieldCnt = this.onlineRewardUnit.copyChangeToCs(builder.getOnlineRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToCs(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROCOMEUNIT) && this.heroComeUnit != null) {
            final boolean needClear = !builder.hasHeroComeUnit();
            final int tmpFieldCnt = this.heroComeUnit.copyChangeToCs(builder.getHeroComeUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroComeUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAIN) && this.chargeGoodsChain != null) {
            final boolean needClear = !builder.hasChargeGoodsChain();
            final int tmpFieldCnt = this.chargeGoodsChain.copyChangeToCs(builder.getChargeGoodsChainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChain();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREUNIT) && this.storeUnit != null) {
            final boolean needClear = !builder.hasStoreUnit();
            final int tmpFieldCnt = this.storeUnit.copyChangeToCs(builder.getStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToCs(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERSUBRANKUNIT) && this.bestCommanderSubRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderSubRankUnit();
            final int tmpFieldCnt = this.bestCommanderSubRankUnit.copyChangeToCs(builder.getBestCommanderSubRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderSubRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERTOTALRANKUNIT) && this.bestCommanderTotalRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderTotalRankUnit();
            final int tmpFieldCnt = this.bestCommanderTotalRankUnit.copyChangeToCs(builder.getBestCommanderTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderTotalRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToCs(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYUNIT) && this.lotteryUnit != null) {
            final boolean needClear = !builder.hasLotteryUnit();
            final int tmpFieldCnt = this.lotteryUnit.copyChangeToCs(builder.getLotteryUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROWTHFUNDUNIT) && this.growthFundUnit != null) {
            final boolean needClear = !builder.hasGrowthFundUnit();
            final int tmpFieldCnt = this.growthFundUnit.copyChangeToCs(builder.getGrowthFundUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGrowthFundUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_WEEKMONTHCARDUNIT) && this.weekMonthCardUnit != null) {
            final boolean needClear = !builder.hasWeekMonthCardUnit();
            final int tmpFieldCnt = this.weekMonthCardUnit.copyChangeToCs(builder.getWeekMonthCardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWeekMonthCardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CYCLECHARGEGOODS) && this.cycleChargeGoods != null) {
            final boolean needClear = !builder.hasCycleChargeGoods();
            final int tmpFieldCnt = this.cycleChargeGoods.copyChangeToCs(builder.getCycleChargeGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCycleChargeGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTTRIGGERBUNDLEUNIT) && this.lostTriggerBundleUnit != null) {
            final boolean needClear = !builder.hasLostTriggerBundleUnit();
            final int tmpFieldCnt = this.lostTriggerBundleUnit.copyChangeToCs(builder.getLostTriggerBundleUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLostTriggerBundleUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEGIFTUNIT) && this.onlineGiftUnit != null) {
            final boolean needClear = !builder.hasOnlineGiftUnit();
            final int tmpFieldCnt = this.onlineGiftUnit.copyChangeToCs(builder.getOnlineGiftUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineGiftUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_STORERATINGUNIT) && this.storeRatingUnit != null) {
            final boolean needClear = !builder.hasStoreRatingUnit();
            final int tmpFieldCnt = this.storeRatingUnit.copyChangeToCs(builder.getStoreRatingUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreRatingUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERUNIT) && this.triggerunit != null) {
            final boolean needClear = !builder.hasTriggerunit();
            final int tmpFieldCnt = this.triggerunit.copyChangeToCs(builder.getTriggerunitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerunit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FESTIVALUNIT) && this.festivalUnit != null) {
            final boolean needClear = !builder.hasFestivalUnit();
            final int tmpFieldCnt = this.festivalUnit.copyChangeToCs(builder.getFestivalUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFestivalUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPUNIT) && this.bpUnit != null) {
            final boolean needClear = !builder.hasBpUnit();
            final int tmpFieldCnt = this.bpUnit.copyChangeToCs(builder.getBpUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUEUNIT) && this.continueUnit != null) {
            final boolean needClear = !builder.hasContinueUnit();
            final int tmpFieldCnt = this.continueUnit.copyChangeToCs(builder.getContinueUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinueUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUESGIFT) && this.continuesGift != null) {
            final boolean needClear = !builder.hasContinuesGift();
            final int tmpFieldCnt = this.continuesGift.copyChangeToCs(builder.getContinuesGiftBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinuesGift();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYNATASHA) && this.lotteryNatasha != null) {
            final boolean needClear = !builder.hasLotteryNatasha();
            final int tmpFieldCnt = this.lotteryNatasha.copyChangeToCs(builder.getLotteryNatashaBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryNatasha();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITTYPE)) {
            builder.setUnitType(this.getUnitType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToAndClearDeleteKeysCs(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCOREREWARDUNIT) && this.scoreRewardUnit != null) {
            final boolean needClear = !builder.hasScoreRewardUnit();
            final int tmpFieldCnt = this.scoreRewardUnit.copyChangeToAndClearDeleteKeysCs(builder.getScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIVEDAYSUNIT) && this.fiveDaysUnit != null) {
            final boolean needClear = !builder.hasFiveDaysUnit();
            final int tmpFieldCnt = this.fiveDaysUnit.copyChangeToAndClearDeleteKeysCs(builder.getFiveDaysUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFiveDaysUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCORDGOTOUNIT) && this.discordGotoUnit != null) {
            final boolean needClear = !builder.hasDiscordGotoUnit();
            final int tmpFieldCnt = this.discordGotoUnit.copyChangeToAndClearDeleteKeysCs(builder.getDiscordGotoUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscordGotoUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEREWARDUNIT) && this.onlineRewardUnit != null) {
            final boolean needClear = !builder.hasOnlineRewardUnit();
            final int tmpFieldCnt = this.onlineRewardUnit.copyChangeToAndClearDeleteKeysCs(builder.getOnlineRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToAndClearDeleteKeysCs(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROCOMEUNIT) && this.heroComeUnit != null) {
            final boolean needClear = !builder.hasHeroComeUnit();
            final int tmpFieldCnt = this.heroComeUnit.copyChangeToAndClearDeleteKeysCs(builder.getHeroComeUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroComeUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAIN) && this.chargeGoodsChain != null) {
            final boolean needClear = !builder.hasChargeGoodsChain();
            final int tmpFieldCnt = this.chargeGoodsChain.copyChangeToAndClearDeleteKeysCs(builder.getChargeGoodsChainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChain();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREUNIT) && this.storeUnit != null) {
            final boolean needClear = !builder.hasStoreUnit();
            final int tmpFieldCnt = this.storeUnit.copyChangeToAndClearDeleteKeysCs(builder.getStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToAndClearDeleteKeysCs(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERSUBRANKUNIT) && this.bestCommanderSubRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderSubRankUnit();
            final int tmpFieldCnt = this.bestCommanderSubRankUnit.copyChangeToAndClearDeleteKeysCs(builder.getBestCommanderSubRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderSubRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERTOTALRANKUNIT) && this.bestCommanderTotalRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderTotalRankUnit();
            final int tmpFieldCnt = this.bestCommanderTotalRankUnit.copyChangeToAndClearDeleteKeysCs(builder.getBestCommanderTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderTotalRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToAndClearDeleteKeysCs(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYUNIT) && this.lotteryUnit != null) {
            final boolean needClear = !builder.hasLotteryUnit();
            final int tmpFieldCnt = this.lotteryUnit.copyChangeToAndClearDeleteKeysCs(builder.getLotteryUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROWTHFUNDUNIT) && this.growthFundUnit != null) {
            final boolean needClear = !builder.hasGrowthFundUnit();
            final int tmpFieldCnt = this.growthFundUnit.copyChangeToAndClearDeleteKeysCs(builder.getGrowthFundUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGrowthFundUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_WEEKMONTHCARDUNIT) && this.weekMonthCardUnit != null) {
            final boolean needClear = !builder.hasWeekMonthCardUnit();
            final int tmpFieldCnt = this.weekMonthCardUnit.copyChangeToAndClearDeleteKeysCs(builder.getWeekMonthCardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWeekMonthCardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CYCLECHARGEGOODS) && this.cycleChargeGoods != null) {
            final boolean needClear = !builder.hasCycleChargeGoods();
            final int tmpFieldCnt = this.cycleChargeGoods.copyChangeToAndClearDeleteKeysCs(builder.getCycleChargeGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCycleChargeGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTTRIGGERBUNDLEUNIT) && this.lostTriggerBundleUnit != null) {
            final boolean needClear = !builder.hasLostTriggerBundleUnit();
            final int tmpFieldCnt = this.lostTriggerBundleUnit.copyChangeToAndClearDeleteKeysCs(builder.getLostTriggerBundleUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLostTriggerBundleUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEGIFTUNIT) && this.onlineGiftUnit != null) {
            final boolean needClear = !builder.hasOnlineGiftUnit();
            final int tmpFieldCnt = this.onlineGiftUnit.copyChangeToAndClearDeleteKeysCs(builder.getOnlineGiftUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineGiftUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_STORERATINGUNIT) && this.storeRatingUnit != null) {
            final boolean needClear = !builder.hasStoreRatingUnit();
            final int tmpFieldCnt = this.storeRatingUnit.copyChangeToAndClearDeleteKeysCs(builder.getStoreRatingUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreRatingUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERUNIT) && this.triggerunit != null) {
            final boolean needClear = !builder.hasTriggerunit();
            final int tmpFieldCnt = this.triggerunit.copyChangeToAndClearDeleteKeysCs(builder.getTriggerunitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerunit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FESTIVALUNIT) && this.festivalUnit != null) {
            final boolean needClear = !builder.hasFestivalUnit();
            final int tmpFieldCnt = this.festivalUnit.copyChangeToAndClearDeleteKeysCs(builder.getFestivalUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFestivalUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPUNIT) && this.bpUnit != null) {
            final boolean needClear = !builder.hasBpUnit();
            final int tmpFieldCnt = this.bpUnit.copyChangeToAndClearDeleteKeysCs(builder.getBpUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUEUNIT) && this.continueUnit != null) {
            final boolean needClear = !builder.hasContinueUnit();
            final int tmpFieldCnt = this.continueUnit.copyChangeToAndClearDeleteKeysCs(builder.getContinueUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinueUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUESGIFT) && this.continuesGift != null) {
            final boolean needClear = !builder.hasContinuesGift();
            final int tmpFieldCnt = this.continuesGift.copyChangeToAndClearDeleteKeysCs(builder.getContinuesGiftBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinuesGift();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYNATASHA) && this.lotteryNatasha != null) {
            final boolean needClear = !builder.hasLotteryNatasha();
            final int tmpFieldCnt = this.lotteryNatasha.copyChangeToAndClearDeleteKeysCs(builder.getLotteryNatashaBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryNatasha();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitType()) {
            this.innerSetUnitType(proto.getUnitType());
        } else {
            this.innerSetUnitType(ActivityUnitType.forNumber(0));
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeFromCs(proto.getTaskUnit());
        } else {
            if (this.taskUnit != null) {
                this.taskUnit.mergeFromCs(proto.getTaskUnit());
            }
        }
        if (proto.hasScoreRewardUnit()) {
            this.getScoreRewardUnit().mergeFromCs(proto.getScoreRewardUnit());
        } else {
            if (this.scoreRewardUnit != null) {
                this.scoreRewardUnit.mergeFromCs(proto.getScoreRewardUnit());
            }
        }
        if (proto.hasFiveDaysUnit()) {
            this.getFiveDaysUnit().mergeFromCs(proto.getFiveDaysUnit());
        } else {
            if (this.fiveDaysUnit != null) {
                this.fiveDaysUnit.mergeFromCs(proto.getFiveDaysUnit());
            }
        }
        if (proto.hasDiscordGotoUnit()) {
            this.getDiscordGotoUnit().mergeFromCs(proto.getDiscordGotoUnit());
        } else {
            if (this.discordGotoUnit != null) {
                this.discordGotoUnit.mergeFromCs(proto.getDiscordGotoUnit());
            }
        }
        if (proto.hasOnlineRewardUnit()) {
            this.getOnlineRewardUnit().mergeFromCs(proto.getOnlineRewardUnit());
        } else {
            if (this.onlineRewardUnit != null) {
                this.onlineRewardUnit.mergeFromCs(proto.getOnlineRewardUnit());
            }
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeFromCs(proto.getTimerRewardUnit());
        } else {
            if (this.timerRewardUnit != null) {
                this.timerRewardUnit.mergeFromCs(proto.getTimerRewardUnit());
            }
        }
        if (proto.hasHeroComeUnit()) {
            this.getHeroComeUnit().mergeFromCs(proto.getHeroComeUnit());
        } else {
            if (this.heroComeUnit != null) {
                this.heroComeUnit.mergeFromCs(proto.getHeroComeUnit());
            }
        }
        if (proto.hasChargeGoodsChain()) {
            this.getChargeGoodsChain().mergeFromCs(proto.getChargeGoodsChain());
        } else {
            if (this.chargeGoodsChain != null) {
                this.chargeGoodsChain.mergeFromCs(proto.getChargeGoodsChain());
            }
        }
        if (proto.hasStoreUnit()) {
            this.getStoreUnit().mergeFromCs(proto.getStoreUnit());
        } else {
            if (this.storeUnit != null) {
                this.storeUnit.mergeFromCs(proto.getStoreUnit());
            }
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeFromCs(proto.getScoreRankUnit());
        } else {
            if (this.scoreRankUnit != null) {
                this.scoreRankUnit.mergeFromCs(proto.getScoreRankUnit());
            }
        }
        if (proto.hasBestCommanderSubRankUnit()) {
            this.getBestCommanderSubRankUnit().mergeFromCs(proto.getBestCommanderSubRankUnit());
        } else {
            if (this.bestCommanderSubRankUnit != null) {
                this.bestCommanderSubRankUnit.mergeFromCs(proto.getBestCommanderSubRankUnit());
            }
        }
        if (proto.hasBestCommanderTotalRankUnit()) {
            this.getBestCommanderTotalRankUnit().mergeFromCs(proto.getBestCommanderTotalRankUnit());
        } else {
            if (this.bestCommanderTotalRankUnit != null) {
                this.bestCommanderTotalRankUnit.mergeFromCs(proto.getBestCommanderTotalRankUnit());
            }
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeFromCs(proto.getBestCommanderUnit());
        } else {
            if (this.bestCommanderUnit != null) {
                this.bestCommanderUnit.mergeFromCs(proto.getBestCommanderUnit());
            }
        }
        if (proto.hasLotteryUnit()) {
            this.getLotteryUnit().mergeFromCs(proto.getLotteryUnit());
        } else {
            if (this.lotteryUnit != null) {
                this.lotteryUnit.mergeFromCs(proto.getLotteryUnit());
            }
        }
        if (proto.hasGrowthFundUnit()) {
            this.getGrowthFundUnit().mergeFromCs(proto.getGrowthFundUnit());
        } else {
            if (this.growthFundUnit != null) {
                this.growthFundUnit.mergeFromCs(proto.getGrowthFundUnit());
            }
        }
        if (proto.hasWeekMonthCardUnit()) {
            this.getWeekMonthCardUnit().mergeFromCs(proto.getWeekMonthCardUnit());
        } else {
            if (this.weekMonthCardUnit != null) {
                this.weekMonthCardUnit.mergeFromCs(proto.getWeekMonthCardUnit());
            }
        }
        if (proto.hasCycleChargeGoods()) {
            this.getCycleChargeGoods().mergeFromCs(proto.getCycleChargeGoods());
        } else {
            if (this.cycleChargeGoods != null) {
                this.cycleChargeGoods.mergeFromCs(proto.getCycleChargeGoods());
            }
        }
        if (proto.hasLostTriggerBundleUnit()) {
            this.getLostTriggerBundleUnit().mergeFromCs(proto.getLostTriggerBundleUnit());
        } else {
            if (this.lostTriggerBundleUnit != null) {
                this.lostTriggerBundleUnit.mergeFromCs(proto.getLostTriggerBundleUnit());
            }
        }
        if (proto.hasOnlineGiftUnit()) {
            this.getOnlineGiftUnit().mergeFromCs(proto.getOnlineGiftUnit());
        } else {
            if (this.onlineGiftUnit != null) {
                this.onlineGiftUnit.mergeFromCs(proto.getOnlineGiftUnit());
            }
        }
        if (proto.hasStoreRatingUnit()) {
            this.getStoreRatingUnit().mergeFromCs(proto.getStoreRatingUnit());
        } else {
            if (this.storeRatingUnit != null) {
                this.storeRatingUnit.mergeFromCs(proto.getStoreRatingUnit());
            }
        }
        if (proto.hasTriggerunit()) {
            this.getTriggerunit().mergeFromCs(proto.getTriggerunit());
        } else {
            if (this.triggerunit != null) {
                this.triggerunit.mergeFromCs(proto.getTriggerunit());
            }
        }
        if (proto.hasFestivalUnit()) {
            this.getFestivalUnit().mergeFromCs(proto.getFestivalUnit());
        } else {
            if (this.festivalUnit != null) {
                this.festivalUnit.mergeFromCs(proto.getFestivalUnit());
            }
        }
        if (proto.hasBpUnit()) {
            this.getBpUnit().mergeFromCs(proto.getBpUnit());
        } else {
            if (this.bpUnit != null) {
                this.bpUnit.mergeFromCs(proto.getBpUnit());
            }
        }
        if (proto.hasContinueUnit()) {
            this.getContinueUnit().mergeFromCs(proto.getContinueUnit());
        } else {
            if (this.continueUnit != null) {
                this.continueUnit.mergeFromCs(proto.getContinueUnit());
            }
        }
        if (proto.hasContinuesGift()) {
            this.getContinuesGift().mergeFromCs(proto.getContinuesGift());
        } else {
            if (this.continuesGift != null) {
                this.continuesGift.mergeFromCs(proto.getContinuesGift());
            }
        }
        if (proto.hasLotteryNatasha()) {
            this.getLotteryNatasha().mergeFromCs(proto.getLotteryNatasha());
        } else {
            if (this.lotteryNatasha != null) {
                this.lotteryNatasha.mergeFromCs(proto.getLotteryNatasha());
            }
        }
        this.markAll();
        return ActivityUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasUnitType()) {
            this.setUnitType(proto.getUnitType());
            fieldCnt++;
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeChangeFromCs(proto.getTaskUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRewardUnit()) {
            this.getScoreRewardUnit().mergeChangeFromCs(proto.getScoreRewardUnit());
            fieldCnt++;
        }
        if (proto.hasFiveDaysUnit()) {
            this.getFiveDaysUnit().mergeChangeFromCs(proto.getFiveDaysUnit());
            fieldCnt++;
        }
        if (proto.hasDiscordGotoUnit()) {
            this.getDiscordGotoUnit().mergeChangeFromCs(proto.getDiscordGotoUnit());
            fieldCnt++;
        }
        if (proto.hasOnlineRewardUnit()) {
            this.getOnlineRewardUnit().mergeChangeFromCs(proto.getOnlineRewardUnit());
            fieldCnt++;
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeChangeFromCs(proto.getTimerRewardUnit());
            fieldCnt++;
        }
        if (proto.hasHeroComeUnit()) {
            this.getHeroComeUnit().mergeChangeFromCs(proto.getHeroComeUnit());
            fieldCnt++;
        }
        if (proto.hasChargeGoodsChain()) {
            this.getChargeGoodsChain().mergeChangeFromCs(proto.getChargeGoodsChain());
            fieldCnt++;
        }
        if (proto.hasStoreUnit()) {
            this.getStoreUnit().mergeChangeFromCs(proto.getStoreUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeChangeFromCs(proto.getScoreRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderSubRankUnit()) {
            this.getBestCommanderSubRankUnit().mergeChangeFromCs(proto.getBestCommanderSubRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderTotalRankUnit()) {
            this.getBestCommanderTotalRankUnit().mergeChangeFromCs(proto.getBestCommanderTotalRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeChangeFromCs(proto.getBestCommanderUnit());
            fieldCnt++;
        }
        if (proto.hasLotteryUnit()) {
            this.getLotteryUnit().mergeChangeFromCs(proto.getLotteryUnit());
            fieldCnt++;
        }
        if (proto.hasGrowthFundUnit()) {
            this.getGrowthFundUnit().mergeChangeFromCs(proto.getGrowthFundUnit());
            fieldCnt++;
        }
        if (proto.hasWeekMonthCardUnit()) {
            this.getWeekMonthCardUnit().mergeChangeFromCs(proto.getWeekMonthCardUnit());
            fieldCnt++;
        }
        if (proto.hasCycleChargeGoods()) {
            this.getCycleChargeGoods().mergeChangeFromCs(proto.getCycleChargeGoods());
            fieldCnt++;
        }
        if (proto.hasLostTriggerBundleUnit()) {
            this.getLostTriggerBundleUnit().mergeChangeFromCs(proto.getLostTriggerBundleUnit());
            fieldCnt++;
        }
        if (proto.hasOnlineGiftUnit()) {
            this.getOnlineGiftUnit().mergeChangeFromCs(proto.getOnlineGiftUnit());
            fieldCnt++;
        }
        if (proto.hasStoreRatingUnit()) {
            this.getStoreRatingUnit().mergeChangeFromCs(proto.getStoreRatingUnit());
            fieldCnt++;
        }
        if (proto.hasTriggerunit()) {
            this.getTriggerunit().mergeChangeFromCs(proto.getTriggerunit());
            fieldCnt++;
        }
        if (proto.hasFestivalUnit()) {
            this.getFestivalUnit().mergeChangeFromCs(proto.getFestivalUnit());
            fieldCnt++;
        }
        if (proto.hasBpUnit()) {
            this.getBpUnit().mergeChangeFromCs(proto.getBpUnit());
            fieldCnt++;
        }
        if (proto.hasContinueUnit()) {
            this.getContinueUnit().mergeChangeFromCs(proto.getContinueUnit());
            fieldCnt++;
        }
        if (proto.hasContinuesGift()) {
            this.getContinuesGift().mergeChangeFromCs(proto.getContinuesGift());
            fieldCnt++;
        }
        if (proto.hasLotteryNatasha()) {
            this.getLotteryNatasha().mergeChangeFromCs(proto.getLotteryNatasha());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityUnit.Builder getCopyDbBuilder() {
        final ActivityUnit.Builder builder = ActivityUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getUnitType() != ActivityUnitType.forNumber(0)) {
            builder.setUnitType(this.getUnitType());
            fieldCnt++;
        }  else if (builder.hasUnitType()) {
            // 清理UnitType
            builder.clearUnitType();
            fieldCnt++;
        }
        if (this.taskUnit != null) {
            Struct.ActivityTaskUnit.Builder tmpBuilder = Struct.ActivityTaskUnit.newBuilder();
            final int tmpFieldCnt = this.taskUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskUnit();
            }
        }  else if (builder.hasTaskUnit()) {
            // 清理TaskUnit
            builder.clearTaskUnit();
            fieldCnt++;
        }
        if (this.scoreRewardUnit != null) {
            Struct.ActivityScoreRewardUnit.Builder tmpBuilder = Struct.ActivityScoreRewardUnit.newBuilder();
            final int tmpFieldCnt = this.scoreRewardUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRewardUnit();
            }
        }  else if (builder.hasScoreRewardUnit()) {
            // 清理ScoreRewardUnit
            builder.clearScoreRewardUnit();
            fieldCnt++;
        }
        if (this.fiveDaysUnit != null) {
            Struct.ActivityFiveDaysUnit.Builder tmpBuilder = Struct.ActivityFiveDaysUnit.newBuilder();
            final int tmpFieldCnt = this.fiveDaysUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFiveDaysUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFiveDaysUnit();
            }
        }  else if (builder.hasFiveDaysUnit()) {
            // 清理FiveDaysUnit
            builder.clearFiveDaysUnit();
            fieldCnt++;
        }
        if (this.discordGotoUnit != null) {
            Struct.ActivityDiscordGotoUnit.Builder tmpBuilder = Struct.ActivityDiscordGotoUnit.newBuilder();
            final int tmpFieldCnt = this.discordGotoUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDiscordGotoUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDiscordGotoUnit();
            }
        }  else if (builder.hasDiscordGotoUnit()) {
            // 清理DiscordGotoUnit
            builder.clearDiscordGotoUnit();
            fieldCnt++;
        }
        if (this.onlineRewardUnit != null) {
            Struct.ActivityOnlineRewardUnit.Builder tmpBuilder = Struct.ActivityOnlineRewardUnit.newBuilder();
            final int tmpFieldCnt = this.onlineRewardUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOnlineRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOnlineRewardUnit();
            }
        }  else if (builder.hasOnlineRewardUnit()) {
            // 清理OnlineRewardUnit
            builder.clearOnlineRewardUnit();
            fieldCnt++;
        }
        if (this.timerRewardUnit != null) {
            Struct.ActivityTimerRewardUnit.Builder tmpBuilder = Struct.ActivityTimerRewardUnit.newBuilder();
            final int tmpFieldCnt = this.timerRewardUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTimerRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTimerRewardUnit();
            }
        }  else if (builder.hasTimerRewardUnit()) {
            // 清理TimerRewardUnit
            builder.clearTimerRewardUnit();
            fieldCnt++;
        }
        if (this.heroComeUnit != null) {
            Struct.ActivityHeroComeUnit.Builder tmpBuilder = Struct.ActivityHeroComeUnit.newBuilder();
            final int tmpFieldCnt = this.heroComeUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroComeUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroComeUnit();
            }
        }  else if (builder.hasHeroComeUnit()) {
            // 清理HeroComeUnit
            builder.clearHeroComeUnit();
            fieldCnt++;
        }
        if (this.chargeGoodsChain != null) {
            Struct.ActivityChargeGoodsChainUnit.Builder tmpBuilder = Struct.ActivityChargeGoodsChainUnit.newBuilder();
            final int tmpFieldCnt = this.chargeGoodsChain.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeGoodsChain(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeGoodsChain();
            }
        }  else if (builder.hasChargeGoodsChain()) {
            // 清理ChargeGoodsChain
            builder.clearChargeGoodsChain();
            fieldCnt++;
        }
        if (this.storeUnit != null) {
            Struct.ActivityStoreUnit.Builder tmpBuilder = Struct.ActivityStoreUnit.newBuilder();
            final int tmpFieldCnt = this.storeUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreUnit();
            }
        }  else if (builder.hasStoreUnit()) {
            // 清理StoreUnit
            builder.clearStoreUnit();
            fieldCnt++;
        }
        if (this.zlcbUnit != null) {
            Struct.ActivityZlcbUnit.Builder tmpBuilder = Struct.ActivityZlcbUnit.newBuilder();
            final int tmpFieldCnt = this.zlcbUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZlcbUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZlcbUnit();
            }
        }  else if (builder.hasZlcbUnit()) {
            // 清理ZlcbUnit
            builder.clearZlcbUnit();
            fieldCnt++;
        }
        if (this.scoreRankUnit != null) {
            Struct.ActivityScoreRankUnit.Builder tmpBuilder = Struct.ActivityScoreRankUnit.newBuilder();
            final int tmpFieldCnt = this.scoreRankUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRankUnit();
            }
        }  else if (builder.hasScoreRankUnit()) {
            // 清理ScoreRankUnit
            builder.clearScoreRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderSubRankUnit != null) {
            Struct.ActivityBestCommanderSubRankUnit.Builder tmpBuilder = Struct.ActivityBestCommanderSubRankUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderSubRankUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderSubRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderSubRankUnit();
            }
        }  else if (builder.hasBestCommanderSubRankUnit()) {
            // 清理BestCommanderSubRankUnit
            builder.clearBestCommanderSubRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderTotalRankUnit != null) {
            Struct.ActivityBestCommanderTotalRankUnit.Builder tmpBuilder = Struct.ActivityBestCommanderTotalRankUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderTotalRankUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderTotalRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderTotalRankUnit();
            }
        }  else if (builder.hasBestCommanderTotalRankUnit()) {
            // 清理BestCommanderTotalRankUnit
            builder.clearBestCommanderTotalRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderUnit != null) {
            Struct.ActivityBestCommanderUnit.Builder tmpBuilder = Struct.ActivityBestCommanderUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderUnit();
            }
        }  else if (builder.hasBestCommanderUnit()) {
            // 清理BestCommanderUnit
            builder.clearBestCommanderUnit();
            fieldCnt++;
        }
        if (this.lotteryUnit != null) {
            Struct.ActivityLotteryUnit.Builder tmpBuilder = Struct.ActivityLotteryUnit.newBuilder();
            final int tmpFieldCnt = this.lotteryUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryUnit();
            }
        }  else if (builder.hasLotteryUnit()) {
            // 清理LotteryUnit
            builder.clearLotteryUnit();
            fieldCnt++;
        }
        if (this.growthFundUnit != null) {
            Struct.ActivityGrowthFundUnit.Builder tmpBuilder = Struct.ActivityGrowthFundUnit.newBuilder();
            final int tmpFieldCnt = this.growthFundUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGrowthFundUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGrowthFundUnit();
            }
        }  else if (builder.hasGrowthFundUnit()) {
            // 清理GrowthFundUnit
            builder.clearGrowthFundUnit();
            fieldCnt++;
        }
        if (this.weekMonthCardUnit != null) {
            Struct.ActivityWeekMonthCardUnit.Builder tmpBuilder = Struct.ActivityWeekMonthCardUnit.newBuilder();
            final int tmpFieldCnt = this.weekMonthCardUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWeekMonthCardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWeekMonthCardUnit();
            }
        }  else if (builder.hasWeekMonthCardUnit()) {
            // 清理WeekMonthCardUnit
            builder.clearWeekMonthCardUnit();
            fieldCnt++;
        }
        if (this.cycleChargeGoods != null) {
            Struct.ActivityCycleChargeGoodsUnit.Builder tmpBuilder = Struct.ActivityCycleChargeGoodsUnit.newBuilder();
            final int tmpFieldCnt = this.cycleChargeGoods.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCycleChargeGoods(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCycleChargeGoods();
            }
        }  else if (builder.hasCycleChargeGoods()) {
            // 清理CycleChargeGoods
            builder.clearCycleChargeGoods();
            fieldCnt++;
        }
        if (this.lostTriggerBundleUnit != null) {
            Struct.ActivityLostTriggerBundleUnit.Builder tmpBuilder = Struct.ActivityLostTriggerBundleUnit.newBuilder();
            final int tmpFieldCnt = this.lostTriggerBundleUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLostTriggerBundleUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLostTriggerBundleUnit();
            }
        }  else if (builder.hasLostTriggerBundleUnit()) {
            // 清理LostTriggerBundleUnit
            builder.clearLostTriggerBundleUnit();
            fieldCnt++;
        }
        if (this.onlineGiftUnit != null) {
            Struct.ActivityOnlineGiftUnit.Builder tmpBuilder = Struct.ActivityOnlineGiftUnit.newBuilder();
            final int tmpFieldCnt = this.onlineGiftUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOnlineGiftUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOnlineGiftUnit();
            }
        }  else if (builder.hasOnlineGiftUnit()) {
            // 清理OnlineGiftUnit
            builder.clearOnlineGiftUnit();
            fieldCnt++;
        }
        if (this.storeRatingUnit != null) {
            Struct.ActivityStoreRatingUnit.Builder tmpBuilder = Struct.ActivityStoreRatingUnit.newBuilder();
            final int tmpFieldCnt = this.storeRatingUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreRatingUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreRatingUnit();
            }
        }  else if (builder.hasStoreRatingUnit()) {
            // 清理StoreRatingUnit
            builder.clearStoreRatingUnit();
            fieldCnt++;
        }
        if (this.triggerunit != null) {
            Struct.ActivityTriggerUnit.Builder tmpBuilder = Struct.ActivityTriggerUnit.newBuilder();
            final int tmpFieldCnt = this.triggerunit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerunit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerunit();
            }
        }  else if (builder.hasTriggerunit()) {
            // 清理Triggerunit
            builder.clearTriggerunit();
            fieldCnt++;
        }
        if (this.festivalUnit != null) {
            Struct.ActivityFestivalUnit.Builder tmpBuilder = Struct.ActivityFestivalUnit.newBuilder();
            final int tmpFieldCnt = this.festivalUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFestivalUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFestivalUnit();
            }
        }  else if (builder.hasFestivalUnit()) {
            // 清理FestivalUnit
            builder.clearFestivalUnit();
            fieldCnt++;
        }
        if (this.bpUnit != null) {
            Struct.ActivityFestivalBpUnit.Builder tmpBuilder = Struct.ActivityFestivalBpUnit.newBuilder();
            final int tmpFieldCnt = this.bpUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpUnit();
            }
        }  else if (builder.hasBpUnit()) {
            // 清理BpUnit
            builder.clearBpUnit();
            fieldCnt++;
        }
        if (this.continueUnit != null) {
            Struct.ActivityContinuesUnit.Builder tmpBuilder = Struct.ActivityContinuesUnit.newBuilder();
            final int tmpFieldCnt = this.continueUnit.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContinueUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContinueUnit();
            }
        }  else if (builder.hasContinueUnit()) {
            // 清理ContinueUnit
            builder.clearContinueUnit();
            fieldCnt++;
        }
        if (this.continuesGift != null) {
            Struct.ActivityContinuesGiftUnit.Builder tmpBuilder = Struct.ActivityContinuesGiftUnit.newBuilder();
            final int tmpFieldCnt = this.continuesGift.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContinuesGift(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContinuesGift();
            }
        }  else if (builder.hasContinuesGift()) {
            // 清理ContinuesGift
            builder.clearContinuesGift();
            fieldCnt++;
        }
        if (this.lotteryNatasha != null) {
            Struct.ActivityLotteryNatashaUnit.Builder tmpBuilder = Struct.ActivityLotteryNatashaUnit.newBuilder();
            final int tmpFieldCnt = this.lotteryNatasha.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryNatasha(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryNatasha();
            }
        }  else if (builder.hasLotteryNatasha()) {
            // 清理LotteryNatasha
            builder.clearLotteryNatasha();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITTYPE)) {
            builder.setUnitType(this.getUnitType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToDb(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCOREREWARDUNIT) && this.scoreRewardUnit != null) {
            final boolean needClear = !builder.hasScoreRewardUnit();
            final int tmpFieldCnt = this.scoreRewardUnit.copyChangeToDb(builder.getScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIVEDAYSUNIT) && this.fiveDaysUnit != null) {
            final boolean needClear = !builder.hasFiveDaysUnit();
            final int tmpFieldCnt = this.fiveDaysUnit.copyChangeToDb(builder.getFiveDaysUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFiveDaysUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCORDGOTOUNIT) && this.discordGotoUnit != null) {
            final boolean needClear = !builder.hasDiscordGotoUnit();
            final int tmpFieldCnt = this.discordGotoUnit.copyChangeToDb(builder.getDiscordGotoUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscordGotoUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEREWARDUNIT) && this.onlineRewardUnit != null) {
            final boolean needClear = !builder.hasOnlineRewardUnit();
            final int tmpFieldCnt = this.onlineRewardUnit.copyChangeToDb(builder.getOnlineRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToDb(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROCOMEUNIT) && this.heroComeUnit != null) {
            final boolean needClear = !builder.hasHeroComeUnit();
            final int tmpFieldCnt = this.heroComeUnit.copyChangeToDb(builder.getHeroComeUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroComeUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAIN) && this.chargeGoodsChain != null) {
            final boolean needClear = !builder.hasChargeGoodsChain();
            final int tmpFieldCnt = this.chargeGoodsChain.copyChangeToDb(builder.getChargeGoodsChainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChain();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREUNIT) && this.storeUnit != null) {
            final boolean needClear = !builder.hasStoreUnit();
            final int tmpFieldCnt = this.storeUnit.copyChangeToDb(builder.getStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZLCBUNIT) && this.zlcbUnit != null) {
            final boolean needClear = !builder.hasZlcbUnit();
            final int tmpFieldCnt = this.zlcbUnit.copyChangeToDb(builder.getZlcbUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZlcbUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToDb(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERSUBRANKUNIT) && this.bestCommanderSubRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderSubRankUnit();
            final int tmpFieldCnt = this.bestCommanderSubRankUnit.copyChangeToDb(builder.getBestCommanderSubRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderSubRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERTOTALRANKUNIT) && this.bestCommanderTotalRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderTotalRankUnit();
            final int tmpFieldCnt = this.bestCommanderTotalRankUnit.copyChangeToDb(builder.getBestCommanderTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderTotalRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToDb(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYUNIT) && this.lotteryUnit != null) {
            final boolean needClear = !builder.hasLotteryUnit();
            final int tmpFieldCnt = this.lotteryUnit.copyChangeToDb(builder.getLotteryUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROWTHFUNDUNIT) && this.growthFundUnit != null) {
            final boolean needClear = !builder.hasGrowthFundUnit();
            final int tmpFieldCnt = this.growthFundUnit.copyChangeToDb(builder.getGrowthFundUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGrowthFundUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_WEEKMONTHCARDUNIT) && this.weekMonthCardUnit != null) {
            final boolean needClear = !builder.hasWeekMonthCardUnit();
            final int tmpFieldCnt = this.weekMonthCardUnit.copyChangeToDb(builder.getWeekMonthCardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWeekMonthCardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CYCLECHARGEGOODS) && this.cycleChargeGoods != null) {
            final boolean needClear = !builder.hasCycleChargeGoods();
            final int tmpFieldCnt = this.cycleChargeGoods.copyChangeToDb(builder.getCycleChargeGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCycleChargeGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTTRIGGERBUNDLEUNIT) && this.lostTriggerBundleUnit != null) {
            final boolean needClear = !builder.hasLostTriggerBundleUnit();
            final int tmpFieldCnt = this.lostTriggerBundleUnit.copyChangeToDb(builder.getLostTriggerBundleUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLostTriggerBundleUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEGIFTUNIT) && this.onlineGiftUnit != null) {
            final boolean needClear = !builder.hasOnlineGiftUnit();
            final int tmpFieldCnt = this.onlineGiftUnit.copyChangeToDb(builder.getOnlineGiftUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineGiftUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_STORERATINGUNIT) && this.storeRatingUnit != null) {
            final boolean needClear = !builder.hasStoreRatingUnit();
            final int tmpFieldCnt = this.storeRatingUnit.copyChangeToDb(builder.getStoreRatingUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreRatingUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERUNIT) && this.triggerunit != null) {
            final boolean needClear = !builder.hasTriggerunit();
            final int tmpFieldCnt = this.triggerunit.copyChangeToDb(builder.getTriggerunitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerunit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FESTIVALUNIT) && this.festivalUnit != null) {
            final boolean needClear = !builder.hasFestivalUnit();
            final int tmpFieldCnt = this.festivalUnit.copyChangeToDb(builder.getFestivalUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFestivalUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPUNIT) && this.bpUnit != null) {
            final boolean needClear = !builder.hasBpUnit();
            final int tmpFieldCnt = this.bpUnit.copyChangeToDb(builder.getBpUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUEUNIT) && this.continueUnit != null) {
            final boolean needClear = !builder.hasContinueUnit();
            final int tmpFieldCnt = this.continueUnit.copyChangeToDb(builder.getContinueUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinueUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUESGIFT) && this.continuesGift != null) {
            final boolean needClear = !builder.hasContinuesGift();
            final int tmpFieldCnt = this.continuesGift.copyChangeToDb(builder.getContinuesGiftBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinuesGift();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYNATASHA) && this.lotteryNatasha != null) {
            final boolean needClear = !builder.hasLotteryNatasha();
            final int tmpFieldCnt = this.lotteryNatasha.copyChangeToDb(builder.getLotteryNatashaBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryNatasha();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitType()) {
            this.innerSetUnitType(proto.getUnitType());
        } else {
            this.innerSetUnitType(ActivityUnitType.forNumber(0));
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeFromDb(proto.getTaskUnit());
        } else {
            if (this.taskUnit != null) {
                this.taskUnit.mergeFromDb(proto.getTaskUnit());
            }
        }
        if (proto.hasScoreRewardUnit()) {
            this.getScoreRewardUnit().mergeFromDb(proto.getScoreRewardUnit());
        } else {
            if (this.scoreRewardUnit != null) {
                this.scoreRewardUnit.mergeFromDb(proto.getScoreRewardUnit());
            }
        }
        if (proto.hasFiveDaysUnit()) {
            this.getFiveDaysUnit().mergeFromDb(proto.getFiveDaysUnit());
        } else {
            if (this.fiveDaysUnit != null) {
                this.fiveDaysUnit.mergeFromDb(proto.getFiveDaysUnit());
            }
        }
        if (proto.hasDiscordGotoUnit()) {
            this.getDiscordGotoUnit().mergeFromDb(proto.getDiscordGotoUnit());
        } else {
            if (this.discordGotoUnit != null) {
                this.discordGotoUnit.mergeFromDb(proto.getDiscordGotoUnit());
            }
        }
        if (proto.hasOnlineRewardUnit()) {
            this.getOnlineRewardUnit().mergeFromDb(proto.getOnlineRewardUnit());
        } else {
            if (this.onlineRewardUnit != null) {
                this.onlineRewardUnit.mergeFromDb(proto.getOnlineRewardUnit());
            }
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeFromDb(proto.getTimerRewardUnit());
        } else {
            if (this.timerRewardUnit != null) {
                this.timerRewardUnit.mergeFromDb(proto.getTimerRewardUnit());
            }
        }
        if (proto.hasHeroComeUnit()) {
            this.getHeroComeUnit().mergeFromDb(proto.getHeroComeUnit());
        } else {
            if (this.heroComeUnit != null) {
                this.heroComeUnit.mergeFromDb(proto.getHeroComeUnit());
            }
        }
        if (proto.hasChargeGoodsChain()) {
            this.getChargeGoodsChain().mergeFromDb(proto.getChargeGoodsChain());
        } else {
            if (this.chargeGoodsChain != null) {
                this.chargeGoodsChain.mergeFromDb(proto.getChargeGoodsChain());
            }
        }
        if (proto.hasStoreUnit()) {
            this.getStoreUnit().mergeFromDb(proto.getStoreUnit());
        } else {
            if (this.storeUnit != null) {
                this.storeUnit.mergeFromDb(proto.getStoreUnit());
            }
        }
        if (proto.hasZlcbUnit()) {
            this.getZlcbUnit().mergeFromDb(proto.getZlcbUnit());
        } else {
            if (this.zlcbUnit != null) {
                this.zlcbUnit.mergeFromDb(proto.getZlcbUnit());
            }
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeFromDb(proto.getScoreRankUnit());
        } else {
            if (this.scoreRankUnit != null) {
                this.scoreRankUnit.mergeFromDb(proto.getScoreRankUnit());
            }
        }
        if (proto.hasBestCommanderSubRankUnit()) {
            this.getBestCommanderSubRankUnit().mergeFromDb(proto.getBestCommanderSubRankUnit());
        } else {
            if (this.bestCommanderSubRankUnit != null) {
                this.bestCommanderSubRankUnit.mergeFromDb(proto.getBestCommanderSubRankUnit());
            }
        }
        if (proto.hasBestCommanderTotalRankUnit()) {
            this.getBestCommanderTotalRankUnit().mergeFromDb(proto.getBestCommanderTotalRankUnit());
        } else {
            if (this.bestCommanderTotalRankUnit != null) {
                this.bestCommanderTotalRankUnit.mergeFromDb(proto.getBestCommanderTotalRankUnit());
            }
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeFromDb(proto.getBestCommanderUnit());
        } else {
            if (this.bestCommanderUnit != null) {
                this.bestCommanderUnit.mergeFromDb(proto.getBestCommanderUnit());
            }
        }
        if (proto.hasLotteryUnit()) {
            this.getLotteryUnit().mergeFromDb(proto.getLotteryUnit());
        } else {
            if (this.lotteryUnit != null) {
                this.lotteryUnit.mergeFromDb(proto.getLotteryUnit());
            }
        }
        if (proto.hasGrowthFundUnit()) {
            this.getGrowthFundUnit().mergeFromDb(proto.getGrowthFundUnit());
        } else {
            if (this.growthFundUnit != null) {
                this.growthFundUnit.mergeFromDb(proto.getGrowthFundUnit());
            }
        }
        if (proto.hasWeekMonthCardUnit()) {
            this.getWeekMonthCardUnit().mergeFromDb(proto.getWeekMonthCardUnit());
        } else {
            if (this.weekMonthCardUnit != null) {
                this.weekMonthCardUnit.mergeFromDb(proto.getWeekMonthCardUnit());
            }
        }
        if (proto.hasCycleChargeGoods()) {
            this.getCycleChargeGoods().mergeFromDb(proto.getCycleChargeGoods());
        } else {
            if (this.cycleChargeGoods != null) {
                this.cycleChargeGoods.mergeFromDb(proto.getCycleChargeGoods());
            }
        }
        if (proto.hasLostTriggerBundleUnit()) {
            this.getLostTriggerBundleUnit().mergeFromDb(proto.getLostTriggerBundleUnit());
        } else {
            if (this.lostTriggerBundleUnit != null) {
                this.lostTriggerBundleUnit.mergeFromDb(proto.getLostTriggerBundleUnit());
            }
        }
        if (proto.hasOnlineGiftUnit()) {
            this.getOnlineGiftUnit().mergeFromDb(proto.getOnlineGiftUnit());
        } else {
            if (this.onlineGiftUnit != null) {
                this.onlineGiftUnit.mergeFromDb(proto.getOnlineGiftUnit());
            }
        }
        if (proto.hasStoreRatingUnit()) {
            this.getStoreRatingUnit().mergeFromDb(proto.getStoreRatingUnit());
        } else {
            if (this.storeRatingUnit != null) {
                this.storeRatingUnit.mergeFromDb(proto.getStoreRatingUnit());
            }
        }
        if (proto.hasTriggerunit()) {
            this.getTriggerunit().mergeFromDb(proto.getTriggerunit());
        } else {
            if (this.triggerunit != null) {
                this.triggerunit.mergeFromDb(proto.getTriggerunit());
            }
        }
        if (proto.hasFestivalUnit()) {
            this.getFestivalUnit().mergeFromDb(proto.getFestivalUnit());
        } else {
            if (this.festivalUnit != null) {
                this.festivalUnit.mergeFromDb(proto.getFestivalUnit());
            }
        }
        if (proto.hasBpUnit()) {
            this.getBpUnit().mergeFromDb(proto.getBpUnit());
        } else {
            if (this.bpUnit != null) {
                this.bpUnit.mergeFromDb(proto.getBpUnit());
            }
        }
        if (proto.hasContinueUnit()) {
            this.getContinueUnit().mergeFromDb(proto.getContinueUnit());
        } else {
            if (this.continueUnit != null) {
                this.continueUnit.mergeFromDb(proto.getContinueUnit());
            }
        }
        if (proto.hasContinuesGift()) {
            this.getContinuesGift().mergeFromDb(proto.getContinuesGift());
        } else {
            if (this.continuesGift != null) {
                this.continuesGift.mergeFromDb(proto.getContinuesGift());
            }
        }
        if (proto.hasLotteryNatasha()) {
            this.getLotteryNatasha().mergeFromDb(proto.getLotteryNatasha());
        } else {
            if (this.lotteryNatasha != null) {
                this.lotteryNatasha.mergeFromDb(proto.getLotteryNatasha());
            }
        }
        this.markAll();
        return ActivityUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasUnitType()) {
            this.setUnitType(proto.getUnitType());
            fieldCnt++;
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeChangeFromDb(proto.getTaskUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRewardUnit()) {
            this.getScoreRewardUnit().mergeChangeFromDb(proto.getScoreRewardUnit());
            fieldCnt++;
        }
        if (proto.hasFiveDaysUnit()) {
            this.getFiveDaysUnit().mergeChangeFromDb(proto.getFiveDaysUnit());
            fieldCnt++;
        }
        if (proto.hasDiscordGotoUnit()) {
            this.getDiscordGotoUnit().mergeChangeFromDb(proto.getDiscordGotoUnit());
            fieldCnt++;
        }
        if (proto.hasOnlineRewardUnit()) {
            this.getOnlineRewardUnit().mergeChangeFromDb(proto.getOnlineRewardUnit());
            fieldCnt++;
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeChangeFromDb(proto.getTimerRewardUnit());
            fieldCnt++;
        }
        if (proto.hasHeroComeUnit()) {
            this.getHeroComeUnit().mergeChangeFromDb(proto.getHeroComeUnit());
            fieldCnt++;
        }
        if (proto.hasChargeGoodsChain()) {
            this.getChargeGoodsChain().mergeChangeFromDb(proto.getChargeGoodsChain());
            fieldCnt++;
        }
        if (proto.hasStoreUnit()) {
            this.getStoreUnit().mergeChangeFromDb(proto.getStoreUnit());
            fieldCnt++;
        }
        if (proto.hasZlcbUnit()) {
            this.getZlcbUnit().mergeChangeFromDb(proto.getZlcbUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeChangeFromDb(proto.getScoreRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderSubRankUnit()) {
            this.getBestCommanderSubRankUnit().mergeChangeFromDb(proto.getBestCommanderSubRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderTotalRankUnit()) {
            this.getBestCommanderTotalRankUnit().mergeChangeFromDb(proto.getBestCommanderTotalRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeChangeFromDb(proto.getBestCommanderUnit());
            fieldCnt++;
        }
        if (proto.hasLotteryUnit()) {
            this.getLotteryUnit().mergeChangeFromDb(proto.getLotteryUnit());
            fieldCnt++;
        }
        if (proto.hasGrowthFundUnit()) {
            this.getGrowthFundUnit().mergeChangeFromDb(proto.getGrowthFundUnit());
            fieldCnt++;
        }
        if (proto.hasWeekMonthCardUnit()) {
            this.getWeekMonthCardUnit().mergeChangeFromDb(proto.getWeekMonthCardUnit());
            fieldCnt++;
        }
        if (proto.hasCycleChargeGoods()) {
            this.getCycleChargeGoods().mergeChangeFromDb(proto.getCycleChargeGoods());
            fieldCnt++;
        }
        if (proto.hasLostTriggerBundleUnit()) {
            this.getLostTriggerBundleUnit().mergeChangeFromDb(proto.getLostTriggerBundleUnit());
            fieldCnt++;
        }
        if (proto.hasOnlineGiftUnit()) {
            this.getOnlineGiftUnit().mergeChangeFromDb(proto.getOnlineGiftUnit());
            fieldCnt++;
        }
        if (proto.hasStoreRatingUnit()) {
            this.getStoreRatingUnit().mergeChangeFromDb(proto.getStoreRatingUnit());
            fieldCnt++;
        }
        if (proto.hasTriggerunit()) {
            this.getTriggerunit().mergeChangeFromDb(proto.getTriggerunit());
            fieldCnt++;
        }
        if (proto.hasFestivalUnit()) {
            this.getFestivalUnit().mergeChangeFromDb(proto.getFestivalUnit());
            fieldCnt++;
        }
        if (proto.hasBpUnit()) {
            this.getBpUnit().mergeChangeFromDb(proto.getBpUnit());
            fieldCnt++;
        }
        if (proto.hasContinueUnit()) {
            this.getContinueUnit().mergeChangeFromDb(proto.getContinueUnit());
            fieldCnt++;
        }
        if (proto.hasContinuesGift()) {
            this.getContinuesGift().mergeChangeFromDb(proto.getContinuesGift());
            fieldCnt++;
        }
        if (proto.hasLotteryNatasha()) {
            this.getLotteryNatasha().mergeChangeFromDb(proto.getLotteryNatasha());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityUnit.Builder getCopySsBuilder() {
        final ActivityUnit.Builder builder = ActivityUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getUnitId() != 0) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }  else if (builder.hasUnitId()) {
            // 清理UnitId
            builder.clearUnitId();
            fieldCnt++;
        }
        if (this.getUnitType() != ActivityUnitType.forNumber(0)) {
            builder.setUnitType(this.getUnitType());
            fieldCnt++;
        }  else if (builder.hasUnitType()) {
            // 清理UnitType
            builder.clearUnitType();
            fieldCnt++;
        }
        if (this.taskUnit != null) {
            Struct.ActivityTaskUnit.Builder tmpBuilder = Struct.ActivityTaskUnit.newBuilder();
            final int tmpFieldCnt = this.taskUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTaskUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTaskUnit();
            }
        }  else if (builder.hasTaskUnit()) {
            // 清理TaskUnit
            builder.clearTaskUnit();
            fieldCnt++;
        }
        if (this.scoreRewardUnit != null) {
            Struct.ActivityScoreRewardUnit.Builder tmpBuilder = Struct.ActivityScoreRewardUnit.newBuilder();
            final int tmpFieldCnt = this.scoreRewardUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRewardUnit();
            }
        }  else if (builder.hasScoreRewardUnit()) {
            // 清理ScoreRewardUnit
            builder.clearScoreRewardUnit();
            fieldCnt++;
        }
        if (this.fiveDaysUnit != null) {
            Struct.ActivityFiveDaysUnit.Builder tmpBuilder = Struct.ActivityFiveDaysUnit.newBuilder();
            final int tmpFieldCnt = this.fiveDaysUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFiveDaysUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFiveDaysUnit();
            }
        }  else if (builder.hasFiveDaysUnit()) {
            // 清理FiveDaysUnit
            builder.clearFiveDaysUnit();
            fieldCnt++;
        }
        if (this.discordGotoUnit != null) {
            Struct.ActivityDiscordGotoUnit.Builder tmpBuilder = Struct.ActivityDiscordGotoUnit.newBuilder();
            final int tmpFieldCnt = this.discordGotoUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDiscordGotoUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDiscordGotoUnit();
            }
        }  else if (builder.hasDiscordGotoUnit()) {
            // 清理DiscordGotoUnit
            builder.clearDiscordGotoUnit();
            fieldCnt++;
        }
        if (this.onlineRewardUnit != null) {
            Struct.ActivityOnlineRewardUnit.Builder tmpBuilder = Struct.ActivityOnlineRewardUnit.newBuilder();
            final int tmpFieldCnt = this.onlineRewardUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOnlineRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOnlineRewardUnit();
            }
        }  else if (builder.hasOnlineRewardUnit()) {
            // 清理OnlineRewardUnit
            builder.clearOnlineRewardUnit();
            fieldCnt++;
        }
        if (this.timerRewardUnit != null) {
            Struct.ActivityTimerRewardUnit.Builder tmpBuilder = Struct.ActivityTimerRewardUnit.newBuilder();
            final int tmpFieldCnt = this.timerRewardUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTimerRewardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTimerRewardUnit();
            }
        }  else if (builder.hasTimerRewardUnit()) {
            // 清理TimerRewardUnit
            builder.clearTimerRewardUnit();
            fieldCnt++;
        }
        if (this.heroComeUnit != null) {
            Struct.ActivityHeroComeUnit.Builder tmpBuilder = Struct.ActivityHeroComeUnit.newBuilder();
            final int tmpFieldCnt = this.heroComeUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHeroComeUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHeroComeUnit();
            }
        }  else if (builder.hasHeroComeUnit()) {
            // 清理HeroComeUnit
            builder.clearHeroComeUnit();
            fieldCnt++;
        }
        if (this.chargeGoodsChain != null) {
            Struct.ActivityChargeGoodsChainUnit.Builder tmpBuilder = Struct.ActivityChargeGoodsChainUnit.newBuilder();
            final int tmpFieldCnt = this.chargeGoodsChain.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setChargeGoodsChain(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearChargeGoodsChain();
            }
        }  else if (builder.hasChargeGoodsChain()) {
            // 清理ChargeGoodsChain
            builder.clearChargeGoodsChain();
            fieldCnt++;
        }
        if (this.storeUnit != null) {
            Struct.ActivityStoreUnit.Builder tmpBuilder = Struct.ActivityStoreUnit.newBuilder();
            final int tmpFieldCnt = this.storeUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreUnit();
            }
        }  else if (builder.hasStoreUnit()) {
            // 清理StoreUnit
            builder.clearStoreUnit();
            fieldCnt++;
        }
        if (this.zlcbUnit != null) {
            Struct.ActivityZlcbUnit.Builder tmpBuilder = Struct.ActivityZlcbUnit.newBuilder();
            final int tmpFieldCnt = this.zlcbUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setZlcbUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearZlcbUnit();
            }
        }  else if (builder.hasZlcbUnit()) {
            // 清理ZlcbUnit
            builder.clearZlcbUnit();
            fieldCnt++;
        }
        if (this.scoreRankUnit != null) {
            Struct.ActivityScoreRankUnit.Builder tmpBuilder = Struct.ActivityScoreRankUnit.newBuilder();
            final int tmpFieldCnt = this.scoreRankUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setScoreRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearScoreRankUnit();
            }
        }  else if (builder.hasScoreRankUnit()) {
            // 清理ScoreRankUnit
            builder.clearScoreRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderSubRankUnit != null) {
            Struct.ActivityBestCommanderSubRankUnit.Builder tmpBuilder = Struct.ActivityBestCommanderSubRankUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderSubRankUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderSubRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderSubRankUnit();
            }
        }  else if (builder.hasBestCommanderSubRankUnit()) {
            // 清理BestCommanderSubRankUnit
            builder.clearBestCommanderSubRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderTotalRankUnit != null) {
            Struct.ActivityBestCommanderTotalRankUnit.Builder tmpBuilder = Struct.ActivityBestCommanderTotalRankUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderTotalRankUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderTotalRankUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderTotalRankUnit();
            }
        }  else if (builder.hasBestCommanderTotalRankUnit()) {
            // 清理BestCommanderTotalRankUnit
            builder.clearBestCommanderTotalRankUnit();
            fieldCnt++;
        }
        if (this.bestCommanderUnit != null) {
            Struct.ActivityBestCommanderUnit.Builder tmpBuilder = Struct.ActivityBestCommanderUnit.newBuilder();
            final int tmpFieldCnt = this.bestCommanderUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBestCommanderUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBestCommanderUnit();
            }
        }  else if (builder.hasBestCommanderUnit()) {
            // 清理BestCommanderUnit
            builder.clearBestCommanderUnit();
            fieldCnt++;
        }
        if (this.lotteryUnit != null) {
            Struct.ActivityLotteryUnit.Builder tmpBuilder = Struct.ActivityLotteryUnit.newBuilder();
            final int tmpFieldCnt = this.lotteryUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryUnit();
            }
        }  else if (builder.hasLotteryUnit()) {
            // 清理LotteryUnit
            builder.clearLotteryUnit();
            fieldCnt++;
        }
        if (this.growthFundUnit != null) {
            Struct.ActivityGrowthFundUnit.Builder tmpBuilder = Struct.ActivityGrowthFundUnit.newBuilder();
            final int tmpFieldCnt = this.growthFundUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGrowthFundUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGrowthFundUnit();
            }
        }  else if (builder.hasGrowthFundUnit()) {
            // 清理GrowthFundUnit
            builder.clearGrowthFundUnit();
            fieldCnt++;
        }
        if (this.weekMonthCardUnit != null) {
            Struct.ActivityWeekMonthCardUnit.Builder tmpBuilder = Struct.ActivityWeekMonthCardUnit.newBuilder();
            final int tmpFieldCnt = this.weekMonthCardUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setWeekMonthCardUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearWeekMonthCardUnit();
            }
        }  else if (builder.hasWeekMonthCardUnit()) {
            // 清理WeekMonthCardUnit
            builder.clearWeekMonthCardUnit();
            fieldCnt++;
        }
        if (this.cycleChargeGoods != null) {
            Struct.ActivityCycleChargeGoodsUnit.Builder tmpBuilder = Struct.ActivityCycleChargeGoodsUnit.newBuilder();
            final int tmpFieldCnt = this.cycleChargeGoods.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCycleChargeGoods(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCycleChargeGoods();
            }
        }  else if (builder.hasCycleChargeGoods()) {
            // 清理CycleChargeGoods
            builder.clearCycleChargeGoods();
            fieldCnt++;
        }
        if (this.lostTriggerBundleUnit != null) {
            Struct.ActivityLostTriggerBundleUnit.Builder tmpBuilder = Struct.ActivityLostTriggerBundleUnit.newBuilder();
            final int tmpFieldCnt = this.lostTriggerBundleUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLostTriggerBundleUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLostTriggerBundleUnit();
            }
        }  else if (builder.hasLostTriggerBundleUnit()) {
            // 清理LostTriggerBundleUnit
            builder.clearLostTriggerBundleUnit();
            fieldCnt++;
        }
        if (this.onlineGiftUnit != null) {
            Struct.ActivityOnlineGiftUnit.Builder tmpBuilder = Struct.ActivityOnlineGiftUnit.newBuilder();
            final int tmpFieldCnt = this.onlineGiftUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOnlineGiftUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOnlineGiftUnit();
            }
        }  else if (builder.hasOnlineGiftUnit()) {
            // 清理OnlineGiftUnit
            builder.clearOnlineGiftUnit();
            fieldCnt++;
        }
        if (this.storeRatingUnit != null) {
            Struct.ActivityStoreRatingUnit.Builder tmpBuilder = Struct.ActivityStoreRatingUnit.newBuilder();
            final int tmpFieldCnt = this.storeRatingUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreRatingUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreRatingUnit();
            }
        }  else if (builder.hasStoreRatingUnit()) {
            // 清理StoreRatingUnit
            builder.clearStoreRatingUnit();
            fieldCnt++;
        }
        if (this.triggerunit != null) {
            Struct.ActivityTriggerUnit.Builder tmpBuilder = Struct.ActivityTriggerUnit.newBuilder();
            final int tmpFieldCnt = this.triggerunit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTriggerunit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTriggerunit();
            }
        }  else if (builder.hasTriggerunit()) {
            // 清理Triggerunit
            builder.clearTriggerunit();
            fieldCnt++;
        }
        if (this.festivalUnit != null) {
            Struct.ActivityFestivalUnit.Builder tmpBuilder = Struct.ActivityFestivalUnit.newBuilder();
            final int tmpFieldCnt = this.festivalUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setFestivalUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearFestivalUnit();
            }
        }  else if (builder.hasFestivalUnit()) {
            // 清理FestivalUnit
            builder.clearFestivalUnit();
            fieldCnt++;
        }
        if (this.bpUnit != null) {
            Struct.ActivityFestivalBpUnit.Builder tmpBuilder = Struct.ActivityFestivalBpUnit.newBuilder();
            final int tmpFieldCnt = this.bpUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBpUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBpUnit();
            }
        }  else if (builder.hasBpUnit()) {
            // 清理BpUnit
            builder.clearBpUnit();
            fieldCnt++;
        }
        if (this.continueUnit != null) {
            Struct.ActivityContinuesUnit.Builder tmpBuilder = Struct.ActivityContinuesUnit.newBuilder();
            final int tmpFieldCnt = this.continueUnit.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContinueUnit(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContinueUnit();
            }
        }  else if (builder.hasContinueUnit()) {
            // 清理ContinueUnit
            builder.clearContinueUnit();
            fieldCnt++;
        }
        if (this.continuesGift != null) {
            Struct.ActivityContinuesGiftUnit.Builder tmpBuilder = Struct.ActivityContinuesGiftUnit.newBuilder();
            final int tmpFieldCnt = this.continuesGift.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContinuesGift(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContinuesGift();
            }
        }  else if (builder.hasContinuesGift()) {
            // 清理ContinuesGift
            builder.clearContinuesGift();
            fieldCnt++;
        }
        if (this.lotteryNatasha != null) {
            Struct.ActivityLotteryNatashaUnit.Builder tmpBuilder = Struct.ActivityLotteryNatashaUnit.newBuilder();
            final int tmpFieldCnt = this.lotteryNatasha.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLotteryNatasha(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLotteryNatasha();
            }
        }  else if (builder.hasLotteryNatasha()) {
            // 清理LotteryNatasha
            builder.clearLotteryNatasha();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_UNITID)) {
            builder.setUnitId(this.getUnitId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_UNITTYPE)) {
            builder.setUnitType(this.getUnitType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            final boolean needClear = !builder.hasTaskUnit();
            final int tmpFieldCnt = this.taskUnit.copyChangeToSs(builder.getTaskUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTaskUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCOREREWARDUNIT) && this.scoreRewardUnit != null) {
            final boolean needClear = !builder.hasScoreRewardUnit();
            final int tmpFieldCnt = this.scoreRewardUnit.copyChangeToSs(builder.getScoreRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FIVEDAYSUNIT) && this.fiveDaysUnit != null) {
            final boolean needClear = !builder.hasFiveDaysUnit();
            final int tmpFieldCnt = this.fiveDaysUnit.copyChangeToSs(builder.getFiveDaysUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFiveDaysUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_DISCORDGOTOUNIT) && this.discordGotoUnit != null) {
            final boolean needClear = !builder.hasDiscordGotoUnit();
            final int tmpFieldCnt = this.discordGotoUnit.copyChangeToSs(builder.getDiscordGotoUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDiscordGotoUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEREWARDUNIT) && this.onlineRewardUnit != null) {
            final boolean needClear = !builder.hasOnlineRewardUnit();
            final int tmpFieldCnt = this.onlineRewardUnit.copyChangeToSs(builder.getOnlineRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            final boolean needClear = !builder.hasTimerRewardUnit();
            final int tmpFieldCnt = this.timerRewardUnit.copyChangeToSs(builder.getTimerRewardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTimerRewardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_HEROCOMEUNIT) && this.heroComeUnit != null) {
            final boolean needClear = !builder.hasHeroComeUnit();
            final int tmpFieldCnt = this.heroComeUnit.copyChangeToSs(builder.getHeroComeUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHeroComeUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAIN) && this.chargeGoodsChain != null) {
            final boolean needClear = !builder.hasChargeGoodsChain();
            final int tmpFieldCnt = this.chargeGoodsChain.copyChangeToSs(builder.getChargeGoodsChainBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearChargeGoodsChain();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREUNIT) && this.storeUnit != null) {
            final boolean needClear = !builder.hasStoreUnit();
            final int tmpFieldCnt = this.storeUnit.copyChangeToSs(builder.getStoreUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ZLCBUNIT) && this.zlcbUnit != null) {
            final boolean needClear = !builder.hasZlcbUnit();
            final int tmpFieldCnt = this.zlcbUnit.copyChangeToSs(builder.getZlcbUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearZlcbUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            final boolean needClear = !builder.hasScoreRankUnit();
            final int tmpFieldCnt = this.scoreRankUnit.copyChangeToSs(builder.getScoreRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearScoreRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERSUBRANKUNIT) && this.bestCommanderSubRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderSubRankUnit();
            final int tmpFieldCnt = this.bestCommanderSubRankUnit.copyChangeToSs(builder.getBestCommanderSubRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderSubRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERTOTALRANKUNIT) && this.bestCommanderTotalRankUnit != null) {
            final boolean needClear = !builder.hasBestCommanderTotalRankUnit();
            final int tmpFieldCnt = this.bestCommanderTotalRankUnit.copyChangeToSs(builder.getBestCommanderTotalRankUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderTotalRankUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            final boolean needClear = !builder.hasBestCommanderUnit();
            final int tmpFieldCnt = this.bestCommanderUnit.copyChangeToSs(builder.getBestCommanderUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBestCommanderUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYUNIT) && this.lotteryUnit != null) {
            final boolean needClear = !builder.hasLotteryUnit();
            final int tmpFieldCnt = this.lotteryUnit.copyChangeToSs(builder.getLotteryUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_GROWTHFUNDUNIT) && this.growthFundUnit != null) {
            final boolean needClear = !builder.hasGrowthFundUnit();
            final int tmpFieldCnt = this.growthFundUnit.copyChangeToSs(builder.getGrowthFundUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGrowthFundUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_WEEKMONTHCARDUNIT) && this.weekMonthCardUnit != null) {
            final boolean needClear = !builder.hasWeekMonthCardUnit();
            final int tmpFieldCnt = this.weekMonthCardUnit.copyChangeToSs(builder.getWeekMonthCardUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearWeekMonthCardUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CYCLECHARGEGOODS) && this.cycleChargeGoods != null) {
            final boolean needClear = !builder.hasCycleChargeGoods();
            final int tmpFieldCnt = this.cycleChargeGoods.copyChangeToSs(builder.getCycleChargeGoodsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCycleChargeGoods();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOSTTRIGGERBUNDLEUNIT) && this.lostTriggerBundleUnit != null) {
            final boolean needClear = !builder.hasLostTriggerBundleUnit();
            final int tmpFieldCnt = this.lostTriggerBundleUnit.copyChangeToSs(builder.getLostTriggerBundleUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLostTriggerBundleUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_ONLINEGIFTUNIT) && this.onlineGiftUnit != null) {
            final boolean needClear = !builder.hasOnlineGiftUnit();
            final int tmpFieldCnt = this.onlineGiftUnit.copyChangeToSs(builder.getOnlineGiftUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOnlineGiftUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_STORERATINGUNIT) && this.storeRatingUnit != null) {
            final boolean needClear = !builder.hasStoreRatingUnit();
            final int tmpFieldCnt = this.storeRatingUnit.copyChangeToSs(builder.getStoreRatingUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreRatingUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERUNIT) && this.triggerunit != null) {
            final boolean needClear = !builder.hasTriggerunit();
            final int tmpFieldCnt = this.triggerunit.copyChangeToSs(builder.getTriggerunitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTriggerunit();
            }
        }
        if (this.hasMark(FIELD_INDEX_FESTIVALUNIT) && this.festivalUnit != null) {
            final boolean needClear = !builder.hasFestivalUnit();
            final int tmpFieldCnt = this.festivalUnit.copyChangeToSs(builder.getFestivalUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearFestivalUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_BPUNIT) && this.bpUnit != null) {
            final boolean needClear = !builder.hasBpUnit();
            final int tmpFieldCnt = this.bpUnit.copyChangeToSs(builder.getBpUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBpUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUEUNIT) && this.continueUnit != null) {
            final boolean needClear = !builder.hasContinueUnit();
            final int tmpFieldCnt = this.continueUnit.copyChangeToSs(builder.getContinueUnitBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinueUnit();
            }
        }
        if (this.hasMark(FIELD_INDEX_CONTINUESGIFT) && this.continuesGift != null) {
            final boolean needClear = !builder.hasContinuesGift();
            final int tmpFieldCnt = this.continuesGift.copyChangeToSs(builder.getContinuesGiftBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContinuesGift();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYNATASHA) && this.lotteryNatasha != null) {
            final boolean needClear = !builder.hasLotteryNatasha();
            final int tmpFieldCnt = this.lotteryNatasha.copyChangeToSs(builder.getLotteryNatashaBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLotteryNatasha();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasUnitId()) {
            this.innerSetUnitId(proto.getUnitId());
        } else {
            this.innerSetUnitId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasUnitType()) {
            this.innerSetUnitType(proto.getUnitType());
        } else {
            this.innerSetUnitType(ActivityUnitType.forNumber(0));
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeFromSs(proto.getTaskUnit());
        } else {
            if (this.taskUnit != null) {
                this.taskUnit.mergeFromSs(proto.getTaskUnit());
            }
        }
        if (proto.hasScoreRewardUnit()) {
            this.getScoreRewardUnit().mergeFromSs(proto.getScoreRewardUnit());
        } else {
            if (this.scoreRewardUnit != null) {
                this.scoreRewardUnit.mergeFromSs(proto.getScoreRewardUnit());
            }
        }
        if (proto.hasFiveDaysUnit()) {
            this.getFiveDaysUnit().mergeFromSs(proto.getFiveDaysUnit());
        } else {
            if (this.fiveDaysUnit != null) {
                this.fiveDaysUnit.mergeFromSs(proto.getFiveDaysUnit());
            }
        }
        if (proto.hasDiscordGotoUnit()) {
            this.getDiscordGotoUnit().mergeFromSs(proto.getDiscordGotoUnit());
        } else {
            if (this.discordGotoUnit != null) {
                this.discordGotoUnit.mergeFromSs(proto.getDiscordGotoUnit());
            }
        }
        if (proto.hasOnlineRewardUnit()) {
            this.getOnlineRewardUnit().mergeFromSs(proto.getOnlineRewardUnit());
        } else {
            if (this.onlineRewardUnit != null) {
                this.onlineRewardUnit.mergeFromSs(proto.getOnlineRewardUnit());
            }
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeFromSs(proto.getTimerRewardUnit());
        } else {
            if (this.timerRewardUnit != null) {
                this.timerRewardUnit.mergeFromSs(proto.getTimerRewardUnit());
            }
        }
        if (proto.hasHeroComeUnit()) {
            this.getHeroComeUnit().mergeFromSs(proto.getHeroComeUnit());
        } else {
            if (this.heroComeUnit != null) {
                this.heroComeUnit.mergeFromSs(proto.getHeroComeUnit());
            }
        }
        if (proto.hasChargeGoodsChain()) {
            this.getChargeGoodsChain().mergeFromSs(proto.getChargeGoodsChain());
        } else {
            if (this.chargeGoodsChain != null) {
                this.chargeGoodsChain.mergeFromSs(proto.getChargeGoodsChain());
            }
        }
        if (proto.hasStoreUnit()) {
            this.getStoreUnit().mergeFromSs(proto.getStoreUnit());
        } else {
            if (this.storeUnit != null) {
                this.storeUnit.mergeFromSs(proto.getStoreUnit());
            }
        }
        if (proto.hasZlcbUnit()) {
            this.getZlcbUnit().mergeFromSs(proto.getZlcbUnit());
        } else {
            if (this.zlcbUnit != null) {
                this.zlcbUnit.mergeFromSs(proto.getZlcbUnit());
            }
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeFromSs(proto.getScoreRankUnit());
        } else {
            if (this.scoreRankUnit != null) {
                this.scoreRankUnit.mergeFromSs(proto.getScoreRankUnit());
            }
        }
        if (proto.hasBestCommanderSubRankUnit()) {
            this.getBestCommanderSubRankUnit().mergeFromSs(proto.getBestCommanderSubRankUnit());
        } else {
            if (this.bestCommanderSubRankUnit != null) {
                this.bestCommanderSubRankUnit.mergeFromSs(proto.getBestCommanderSubRankUnit());
            }
        }
        if (proto.hasBestCommanderTotalRankUnit()) {
            this.getBestCommanderTotalRankUnit().mergeFromSs(proto.getBestCommanderTotalRankUnit());
        } else {
            if (this.bestCommanderTotalRankUnit != null) {
                this.bestCommanderTotalRankUnit.mergeFromSs(proto.getBestCommanderTotalRankUnit());
            }
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeFromSs(proto.getBestCommanderUnit());
        } else {
            if (this.bestCommanderUnit != null) {
                this.bestCommanderUnit.mergeFromSs(proto.getBestCommanderUnit());
            }
        }
        if (proto.hasLotteryUnit()) {
            this.getLotteryUnit().mergeFromSs(proto.getLotteryUnit());
        } else {
            if (this.lotteryUnit != null) {
                this.lotteryUnit.mergeFromSs(proto.getLotteryUnit());
            }
        }
        if (proto.hasGrowthFundUnit()) {
            this.getGrowthFundUnit().mergeFromSs(proto.getGrowthFundUnit());
        } else {
            if (this.growthFundUnit != null) {
                this.growthFundUnit.mergeFromSs(proto.getGrowthFundUnit());
            }
        }
        if (proto.hasWeekMonthCardUnit()) {
            this.getWeekMonthCardUnit().mergeFromSs(proto.getWeekMonthCardUnit());
        } else {
            if (this.weekMonthCardUnit != null) {
                this.weekMonthCardUnit.mergeFromSs(proto.getWeekMonthCardUnit());
            }
        }
        if (proto.hasCycleChargeGoods()) {
            this.getCycleChargeGoods().mergeFromSs(proto.getCycleChargeGoods());
        } else {
            if (this.cycleChargeGoods != null) {
                this.cycleChargeGoods.mergeFromSs(proto.getCycleChargeGoods());
            }
        }
        if (proto.hasLostTriggerBundleUnit()) {
            this.getLostTriggerBundleUnit().mergeFromSs(proto.getLostTriggerBundleUnit());
        } else {
            if (this.lostTriggerBundleUnit != null) {
                this.lostTriggerBundleUnit.mergeFromSs(proto.getLostTriggerBundleUnit());
            }
        }
        if (proto.hasOnlineGiftUnit()) {
            this.getOnlineGiftUnit().mergeFromSs(proto.getOnlineGiftUnit());
        } else {
            if (this.onlineGiftUnit != null) {
                this.onlineGiftUnit.mergeFromSs(proto.getOnlineGiftUnit());
            }
        }
        if (proto.hasStoreRatingUnit()) {
            this.getStoreRatingUnit().mergeFromSs(proto.getStoreRatingUnit());
        } else {
            if (this.storeRatingUnit != null) {
                this.storeRatingUnit.mergeFromSs(proto.getStoreRatingUnit());
            }
        }
        if (proto.hasTriggerunit()) {
            this.getTriggerunit().mergeFromSs(proto.getTriggerunit());
        } else {
            if (this.triggerunit != null) {
                this.triggerunit.mergeFromSs(proto.getTriggerunit());
            }
        }
        if (proto.hasFestivalUnit()) {
            this.getFestivalUnit().mergeFromSs(proto.getFestivalUnit());
        } else {
            if (this.festivalUnit != null) {
                this.festivalUnit.mergeFromSs(proto.getFestivalUnit());
            }
        }
        if (proto.hasBpUnit()) {
            this.getBpUnit().mergeFromSs(proto.getBpUnit());
        } else {
            if (this.bpUnit != null) {
                this.bpUnit.mergeFromSs(proto.getBpUnit());
            }
        }
        if (proto.hasContinueUnit()) {
            this.getContinueUnit().mergeFromSs(proto.getContinueUnit());
        } else {
            if (this.continueUnit != null) {
                this.continueUnit.mergeFromSs(proto.getContinueUnit());
            }
        }
        if (proto.hasContinuesGift()) {
            this.getContinuesGift().mergeFromSs(proto.getContinuesGift());
        } else {
            if (this.continuesGift != null) {
                this.continuesGift.mergeFromSs(proto.getContinuesGift());
            }
        }
        if (proto.hasLotteryNatasha()) {
            this.getLotteryNatasha().mergeFromSs(proto.getLotteryNatasha());
        } else {
            if (this.lotteryNatasha != null) {
                this.lotteryNatasha.mergeFromSs(proto.getLotteryNatasha());
            }
        }
        this.markAll();
        return ActivityUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasUnitId()) {
            this.setUnitId(proto.getUnitId());
            fieldCnt++;
        }
        if (proto.hasUnitType()) {
            this.setUnitType(proto.getUnitType());
            fieldCnt++;
        }
        if (proto.hasTaskUnit()) {
            this.getTaskUnit().mergeChangeFromSs(proto.getTaskUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRewardUnit()) {
            this.getScoreRewardUnit().mergeChangeFromSs(proto.getScoreRewardUnit());
            fieldCnt++;
        }
        if (proto.hasFiveDaysUnit()) {
            this.getFiveDaysUnit().mergeChangeFromSs(proto.getFiveDaysUnit());
            fieldCnt++;
        }
        if (proto.hasDiscordGotoUnit()) {
            this.getDiscordGotoUnit().mergeChangeFromSs(proto.getDiscordGotoUnit());
            fieldCnt++;
        }
        if (proto.hasOnlineRewardUnit()) {
            this.getOnlineRewardUnit().mergeChangeFromSs(proto.getOnlineRewardUnit());
            fieldCnt++;
        }
        if (proto.hasTimerRewardUnit()) {
            this.getTimerRewardUnit().mergeChangeFromSs(proto.getTimerRewardUnit());
            fieldCnt++;
        }
        if (proto.hasHeroComeUnit()) {
            this.getHeroComeUnit().mergeChangeFromSs(proto.getHeroComeUnit());
            fieldCnt++;
        }
        if (proto.hasChargeGoodsChain()) {
            this.getChargeGoodsChain().mergeChangeFromSs(proto.getChargeGoodsChain());
            fieldCnt++;
        }
        if (proto.hasStoreUnit()) {
            this.getStoreUnit().mergeChangeFromSs(proto.getStoreUnit());
            fieldCnt++;
        }
        if (proto.hasZlcbUnit()) {
            this.getZlcbUnit().mergeChangeFromSs(proto.getZlcbUnit());
            fieldCnt++;
        }
        if (proto.hasScoreRankUnit()) {
            this.getScoreRankUnit().mergeChangeFromSs(proto.getScoreRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderSubRankUnit()) {
            this.getBestCommanderSubRankUnit().mergeChangeFromSs(proto.getBestCommanderSubRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderTotalRankUnit()) {
            this.getBestCommanderTotalRankUnit().mergeChangeFromSs(proto.getBestCommanderTotalRankUnit());
            fieldCnt++;
        }
        if (proto.hasBestCommanderUnit()) {
            this.getBestCommanderUnit().mergeChangeFromSs(proto.getBestCommanderUnit());
            fieldCnt++;
        }
        if (proto.hasLotteryUnit()) {
            this.getLotteryUnit().mergeChangeFromSs(proto.getLotteryUnit());
            fieldCnt++;
        }
        if (proto.hasGrowthFundUnit()) {
            this.getGrowthFundUnit().mergeChangeFromSs(proto.getGrowthFundUnit());
            fieldCnt++;
        }
        if (proto.hasWeekMonthCardUnit()) {
            this.getWeekMonthCardUnit().mergeChangeFromSs(proto.getWeekMonthCardUnit());
            fieldCnt++;
        }
        if (proto.hasCycleChargeGoods()) {
            this.getCycleChargeGoods().mergeChangeFromSs(proto.getCycleChargeGoods());
            fieldCnt++;
        }
        if (proto.hasLostTriggerBundleUnit()) {
            this.getLostTriggerBundleUnit().mergeChangeFromSs(proto.getLostTriggerBundleUnit());
            fieldCnt++;
        }
        if (proto.hasOnlineGiftUnit()) {
            this.getOnlineGiftUnit().mergeChangeFromSs(proto.getOnlineGiftUnit());
            fieldCnt++;
        }
        if (proto.hasStoreRatingUnit()) {
            this.getStoreRatingUnit().mergeChangeFromSs(proto.getStoreRatingUnit());
            fieldCnt++;
        }
        if (proto.hasTriggerunit()) {
            this.getTriggerunit().mergeChangeFromSs(proto.getTriggerunit());
            fieldCnt++;
        }
        if (proto.hasFestivalUnit()) {
            this.getFestivalUnit().mergeChangeFromSs(proto.getFestivalUnit());
            fieldCnt++;
        }
        if (proto.hasBpUnit()) {
            this.getBpUnit().mergeChangeFromSs(proto.getBpUnit());
            fieldCnt++;
        }
        if (proto.hasContinueUnit()) {
            this.getContinueUnit().mergeChangeFromSs(proto.getContinueUnit());
            fieldCnt++;
        }
        if (proto.hasContinuesGift()) {
            this.getContinuesGift().mergeChangeFromSs(proto.getContinuesGift());
            fieldCnt++;
        }
        if (proto.hasLotteryNatasha()) {
            this.getLotteryNatasha().mergeChangeFromSs(proto.getLotteryNatasha());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityUnit.Builder builder = ActivityUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_TASKUNIT) && this.taskUnit != null) {
            this.taskUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCOREREWARDUNIT) && this.scoreRewardUnit != null) {
            this.scoreRewardUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FIVEDAYSUNIT) && this.fiveDaysUnit != null) {
            this.fiveDaysUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DISCORDGOTOUNIT) && this.discordGotoUnit != null) {
            this.discordGotoUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ONLINEREWARDUNIT) && this.onlineRewardUnit != null) {
            this.onlineRewardUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TIMERREWARDUNIT) && this.timerRewardUnit != null) {
            this.timerRewardUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_HEROCOMEUNIT) && this.heroComeUnit != null) {
            this.heroComeUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CHARGEGOODSCHAIN) && this.chargeGoodsChain != null) {
            this.chargeGoodsChain.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STOREUNIT) && this.storeUnit != null) {
            this.storeUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ZLCBUNIT) && this.zlcbUnit != null) {
            this.zlcbUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_SCORERANKUNIT) && this.scoreRankUnit != null) {
            this.scoreRankUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERSUBRANKUNIT) && this.bestCommanderSubRankUnit != null) {
            this.bestCommanderSubRankUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERTOTALRANKUNIT) && this.bestCommanderTotalRankUnit != null) {
            this.bestCommanderTotalRankUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BESTCOMMANDERUNIT) && this.bestCommanderUnit != null) {
            this.bestCommanderUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYUNIT) && this.lotteryUnit != null) {
            this.lotteryUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GROWTHFUNDUNIT) && this.growthFundUnit != null) {
            this.growthFundUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_WEEKMONTHCARDUNIT) && this.weekMonthCardUnit != null) {
            this.weekMonthCardUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CYCLECHARGEGOODS) && this.cycleChargeGoods != null) {
            this.cycleChargeGoods.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOSTTRIGGERBUNDLEUNIT) && this.lostTriggerBundleUnit != null) {
            this.lostTriggerBundleUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ONLINEGIFTUNIT) && this.onlineGiftUnit != null) {
            this.onlineGiftUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STORERATINGUNIT) && this.storeRatingUnit != null) {
            this.storeRatingUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERUNIT) && this.triggerunit != null) {
            this.triggerunit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_FESTIVALUNIT) && this.festivalUnit != null) {
            this.festivalUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_BPUNIT) && this.bpUnit != null) {
            this.bpUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CONTINUEUNIT) && this.continueUnit != null) {
            this.continueUnit.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CONTINUESGIFT) && this.continuesGift != null) {
            this.continuesGift.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOTTERYNATASHA) && this.lotteryNatasha != null) {
            this.lotteryNatasha.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.taskUnit != null) {
            this.taskUnit.markAll();
        }
        if (this.scoreRewardUnit != null) {
            this.scoreRewardUnit.markAll();
        }
        if (this.fiveDaysUnit != null) {
            this.fiveDaysUnit.markAll();
        }
        if (this.discordGotoUnit != null) {
            this.discordGotoUnit.markAll();
        }
        if (this.onlineRewardUnit != null) {
            this.onlineRewardUnit.markAll();
        }
        if (this.timerRewardUnit != null) {
            this.timerRewardUnit.markAll();
        }
        if (this.heroComeUnit != null) {
            this.heroComeUnit.markAll();
        }
        if (this.chargeGoodsChain != null) {
            this.chargeGoodsChain.markAll();
        }
        if (this.storeUnit != null) {
            this.storeUnit.markAll();
        }
        if (this.zlcbUnit != null) {
            this.zlcbUnit.markAll();
        }
        if (this.scoreRankUnit != null) {
            this.scoreRankUnit.markAll();
        }
        if (this.bestCommanderSubRankUnit != null) {
            this.bestCommanderSubRankUnit.markAll();
        }
        if (this.bestCommanderTotalRankUnit != null) {
            this.bestCommanderTotalRankUnit.markAll();
        }
        if (this.bestCommanderUnit != null) {
            this.bestCommanderUnit.markAll();
        }
        if (this.lotteryUnit != null) {
            this.lotteryUnit.markAll();
        }
        if (this.growthFundUnit != null) {
            this.growthFundUnit.markAll();
        }
        if (this.weekMonthCardUnit != null) {
            this.weekMonthCardUnit.markAll();
        }
        if (this.cycleChargeGoods != null) {
            this.cycleChargeGoods.markAll();
        }
        if (this.lostTriggerBundleUnit != null) {
            this.lostTriggerBundleUnit.markAll();
        }
        if (this.onlineGiftUnit != null) {
            this.onlineGiftUnit.markAll();
        }
        if (this.storeRatingUnit != null) {
            this.storeRatingUnit.markAll();
        }
        if (this.triggerunit != null) {
            this.triggerunit.markAll();
        }
        if (this.festivalUnit != null) {
            this.festivalUnit.markAll();
        }
        if (this.bpUnit != null) {
            this.bpUnit.markAll();
        }
        if (this.continueUnit != null) {
            this.continueUnit.markAll();
        }
        if (this.continuesGift != null) {
            this.continuesGift.markAll();
        }
        if (this.lotteryNatasha != null) {
            this.lotteryNatasha.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.unitId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityUnitProp)) {
            return false;
        }
        final ActivityUnitProp otherNode = (ActivityUnitProp) node;
        if (this.unitId != otherNode.unitId) {
            return false;
        }
        if (this.unitType != otherNode.unitType) {
            return false;
        }
        if (!this.getTaskUnit().compareDataTo(otherNode.getTaskUnit())) {
            return false;
        }
        if (!this.getScoreRewardUnit().compareDataTo(otherNode.getScoreRewardUnit())) {
            return false;
        }
        if (!this.getFiveDaysUnit().compareDataTo(otherNode.getFiveDaysUnit())) {
            return false;
        }
        if (!this.getDiscordGotoUnit().compareDataTo(otherNode.getDiscordGotoUnit())) {
            return false;
        }
        if (!this.getOnlineRewardUnit().compareDataTo(otherNode.getOnlineRewardUnit())) {
            return false;
        }
        if (!this.getTimerRewardUnit().compareDataTo(otherNode.getTimerRewardUnit())) {
            return false;
        }
        if (!this.getHeroComeUnit().compareDataTo(otherNode.getHeroComeUnit())) {
            return false;
        }
        if (!this.getChargeGoodsChain().compareDataTo(otherNode.getChargeGoodsChain())) {
            return false;
        }
        if (!this.getStoreUnit().compareDataTo(otherNode.getStoreUnit())) {
            return false;
        }
        if (!this.getZlcbUnit().compareDataTo(otherNode.getZlcbUnit())) {
            return false;
        }
        if (!this.getScoreRankUnit().compareDataTo(otherNode.getScoreRankUnit())) {
            return false;
        }
        if (!this.getBestCommanderSubRankUnit().compareDataTo(otherNode.getBestCommanderSubRankUnit())) {
            return false;
        }
        if (!this.getBestCommanderTotalRankUnit().compareDataTo(otherNode.getBestCommanderTotalRankUnit())) {
            return false;
        }
        if (!this.getBestCommanderUnit().compareDataTo(otherNode.getBestCommanderUnit())) {
            return false;
        }
        if (!this.getLotteryUnit().compareDataTo(otherNode.getLotteryUnit())) {
            return false;
        }
        if (!this.getGrowthFundUnit().compareDataTo(otherNode.getGrowthFundUnit())) {
            return false;
        }
        if (!this.getWeekMonthCardUnit().compareDataTo(otherNode.getWeekMonthCardUnit())) {
            return false;
        }
        if (!this.getCycleChargeGoods().compareDataTo(otherNode.getCycleChargeGoods())) {
            return false;
        }
        if (!this.getLostTriggerBundleUnit().compareDataTo(otherNode.getLostTriggerBundleUnit())) {
            return false;
        }
        if (!this.getOnlineGiftUnit().compareDataTo(otherNode.getOnlineGiftUnit())) {
            return false;
        }
        if (!this.getStoreRatingUnit().compareDataTo(otherNode.getStoreRatingUnit())) {
            return false;
        }
        if (!this.getTriggerunit().compareDataTo(otherNode.getTriggerunit())) {
            return false;
        }
        if (!this.getFestivalUnit().compareDataTo(otherNode.getFestivalUnit())) {
            return false;
        }
        if (!this.getBpUnit().compareDataTo(otherNode.getBpUnit())) {
            return false;
        }
        if (!this.getContinueUnit().compareDataTo(otherNode.getContinueUnit())) {
            return false;
        }
        if (!this.getContinuesGift().compareDataTo(otherNode.getContinuesGift())) {
            return false;
        }
        if (!this.getLotteryNatasha().compareDataTo(otherNode.getLotteryNatasha())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 35;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
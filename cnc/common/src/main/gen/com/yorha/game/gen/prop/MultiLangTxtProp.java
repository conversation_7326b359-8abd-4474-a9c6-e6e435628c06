package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MultiLangTxt;
import com.yorha.proto.StructPB.MultiLangTxtPB;


/**
 * <AUTHOR> auto gen
 */
public class MultiLangTxtProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_LANGUAGE = 0;
    public static final int FIELD_INDEX_TEXT = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int language = Constant.DEFAULT_INT_VALUE;
    private String text = Constant.DEFAULT_STR_VALUE;

    public MultiLangTxtProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MultiLangTxtProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get language
     *
     * @return language value
     */
    public int getLanguage() {
        return this.language;
    }

    /**
     * set language && set marked
     *
     * @param language new value
     * @return current object
     */
    public MultiLangTxtProp setLanguage(int language) {
        if (this.language != language) {
            this.mark(FIELD_INDEX_LANGUAGE);
            this.language = language;
        }
        return this;
    }

    /**
     * inner set language
     *
     * @param language new value
     */
    private void innerSetLanguage(int language) {
        this.language = language;
    }

    /**
     * get text
     *
     * @return text value
     */
    public String getText() {
        return this.text;
    }

    /**
     * set text && set marked
     *
     * @param text new value
     * @return current object
     */
    public MultiLangTxtProp setText(String text) {
        if (text == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.text, text)) {
            this.mark(FIELD_INDEX_TEXT);
            this.text = text;
        }
        return this;
    }

    /**
     * inner set text
     *
     * @param text new value
     */
    private void innerSetText(String text) {
        this.text = text;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MultiLangTxtPB.Builder getCopyCsBuilder() {
        final MultiLangTxtPB.Builder builder = MultiLangTxtPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MultiLangTxtPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLanguage() != 0) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MultiLangTxtPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MultiLangTxtPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MultiLangTxtPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return MultiLangTxtProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MultiLangTxtPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MultiLangTxt.Builder getCopyDbBuilder() {
        final MultiLangTxt.Builder builder = MultiLangTxt.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MultiLangTxt.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLanguage() != 0) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MultiLangTxt.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MultiLangTxt proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return MultiLangTxtProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MultiLangTxt proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MultiLangTxt.Builder getCopySsBuilder() {
        final MultiLangTxt.Builder builder = MultiLangTxt.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MultiLangTxt.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getLanguage() != 0) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }  else if (builder.hasLanguage()) {
            // 清理Language
            builder.clearLanguage();
            fieldCnt++;
        }
        if (!this.getText().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setText(this.getText());
            fieldCnt++;
        }  else if (builder.hasText()) {
            // 清理Text
            builder.clearText();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MultiLangTxt.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LANGUAGE)) {
            builder.setLanguage(this.getLanguage());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEXT)) {
            builder.setText(this.getText());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MultiLangTxt proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLanguage()) {
            this.innerSetLanguage(proto.getLanguage());
        } else {
            this.innerSetLanguage(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasText()) {
            this.innerSetText(proto.getText());
        } else {
            this.innerSetText(Constant.DEFAULT_STR_VALUE);
        }
        this.markAll();
        return MultiLangTxtProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MultiLangTxt proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLanguage()) {
            this.setLanguage(proto.getLanguage());
            fieldCnt++;
        }
        if (proto.hasText()) {
            this.setText(proto.getText());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MultiLangTxt.Builder builder = MultiLangTxt.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.language;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MultiLangTxtProp)) {
            return false;
        }
        final MultiLangTxtProp otherNode = (MultiLangTxtProp) node;
        if (this.language != otherNode.language) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.text, otherNode.text)) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.ArenaMailPlayerInfo;
import com.yorha.proto.StructPB.ArenaMailPlayerInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ArenaMailPlayerInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_RANK = 0;
    public static final int FIELD_INDEX_NAME = 1;
    public static final int FIELD_INDEX_SCLANNAME = 2;
    public static final int FIELD_INDEX_SCORE = 3;
    public static final int FIELD_INDEX_SCORETYPE = 4;
    public static final int FIELD_INDEX_OPTIONALITEMID = 5;
    public static final int FIELD_INDEX_OPTIONALITEMNUM = 6;
    public static final int FIELD_INDEX_ZONEID = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private int rank = Constant.DEFAULT_INT_VALUE;
    private String name = Constant.DEFAULT_STR_VALUE;
    private String sClanName = Constant.DEFAULT_STR_VALUE;
    private int score = Constant.DEFAULT_INT_VALUE;
    private RankScoreType scoreType = RankScoreType.forNumber(0);
    private int optionalItemId = Constant.DEFAULT_INT_VALUE;
    private int optionalItemNum = Constant.DEFAULT_INT_VALUE;
    private int zoneId = Constant.DEFAULT_INT_VALUE;

    public ArenaMailPlayerInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ArenaMailPlayerInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rank
     *
     * @return rank value
     */
    public int getRank() {
        return this.rank;
    }

    /**
     * set rank && set marked
     *
     * @param rank new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setRank(int rank) {
        if (this.rank != rank) {
            this.mark(FIELD_INDEX_RANK);
            this.rank = rank;
        }
        return this;
    }

    /**
     * inner set rank
     *
     * @param rank new value
     */
    private void innerSetRank(int rank) {
        this.rank = rank;
    }

    /**
     * get name
     *
     * @return name value
     */
    public String getName() {
        return this.name;
    }

    /**
     * set name && set marked
     *
     * @param name new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setName(String name) {
        if (name == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, name)) {
            this.mark(FIELD_INDEX_NAME);
            this.name = name;
        }
        return this;
    }

    /**
     * inner set name
     *
     * @param name new value
     */
    private void innerSetName(String name) {
        this.name = name;
    }

    /**
     * get sClanName
     *
     * @return sClanName value
     */
    public String getSClanName() {
        return this.sClanName;
    }

    /**
     * set sClanName && set marked
     *
     * @param sClanName new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setSClanName(String sClanName) {
        if (sClanName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sClanName, sClanName)) {
            this.mark(FIELD_INDEX_SCLANNAME);
            this.sClanName = sClanName;
        }
        return this;
    }

    /**
     * inner set sClanName
     *
     * @param sClanName new value
     */
    private void innerSetSClanName(String sClanName) {
        this.sClanName = sClanName;
    }

    /**
     * get score
     *
     * @return score value
     */
    public int getScore() {
        return this.score;
    }

    /**
     * set score && set marked
     *
     * @param score new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setScore(int score) {
        if (this.score != score) {
            this.mark(FIELD_INDEX_SCORE);
            this.score = score;
        }
        return this;
    }

    /**
     * inner set score
     *
     * @param score new value
     */
    private void innerSetScore(int score) {
        this.score = score;
    }

    /**
     * get scoreType
     *
     * @return scoreType value
     */
    public RankScoreType getScoreType() {
        return this.scoreType;
    }

    /**
     * set scoreType && set marked
     *
     * @param scoreType new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setScoreType(RankScoreType scoreType) {
        if (scoreType == null) {
            throw new NullPointerException();
        }
        if (this.scoreType != scoreType) {
            this.mark(FIELD_INDEX_SCORETYPE);
            this.scoreType = scoreType;
        }
        return this;
    }

    /**
     * inner set scoreType
     *
     * @param scoreType new value
     */
    private void innerSetScoreType(RankScoreType scoreType) {
        this.scoreType = scoreType;
    }

    /**
     * get optionalItemId
     *
     * @return optionalItemId value
     */
    public int getOptionalItemId() {
        return this.optionalItemId;
    }

    /**
     * set optionalItemId && set marked
     *
     * @param optionalItemId new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setOptionalItemId(int optionalItemId) {
        if (this.optionalItemId != optionalItemId) {
            this.mark(FIELD_INDEX_OPTIONALITEMID);
            this.optionalItemId = optionalItemId;
        }
        return this;
    }

    /**
     * inner set optionalItemId
     *
     * @param optionalItemId new value
     */
    private void innerSetOptionalItemId(int optionalItemId) {
        this.optionalItemId = optionalItemId;
    }

    /**
     * get optionalItemNum
     *
     * @return optionalItemNum value
     */
    public int getOptionalItemNum() {
        return this.optionalItemNum;
    }

    /**
     * set optionalItemNum && set marked
     *
     * @param optionalItemNum new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setOptionalItemNum(int optionalItemNum) {
        if (this.optionalItemNum != optionalItemNum) {
            this.mark(FIELD_INDEX_OPTIONALITEMNUM);
            this.optionalItemNum = optionalItemNum;
        }
        return this;
    }

    /**
     * inner set optionalItemNum
     *
     * @param optionalItemNum new value
     */
    private void innerSetOptionalItemNum(int optionalItemNum) {
        this.optionalItemNum = optionalItemNum;
    }

    /**
     * get zoneId
     *
     * @return zoneId value
     */
    public int getZoneId() {
        return this.zoneId;
    }

    /**
     * set zoneId && set marked
     *
     * @param zoneId new value
     * @return current object
     */
    public ArenaMailPlayerInfoProp setZoneId(int zoneId) {
        if (this.zoneId != zoneId) {
            this.mark(FIELD_INDEX_ZONEID);
            this.zoneId = zoneId;
        }
        return this;
    }

    /**
     * inner set zoneId
     *
     * @param zoneId new value
     */
    private void innerSetZoneId(int zoneId) {
        this.zoneId = zoneId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailPlayerInfoPB.Builder getCopyCsBuilder() {
        final ArenaMailPlayerInfoPB.Builder builder = ArenaMailPlayerInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ArenaMailPlayerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRank() != 0) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }  else if (builder.hasRank()) {
            // 清理Rank
            builder.clearRank();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getSClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSClanName(this.getSClanName());
            fieldCnt++;
        }  else if (builder.hasSClanName()) {
            // 清理SClanName
            builder.clearSClanName();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getScoreType() != RankScoreType.forNumber(0)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }  else if (builder.hasScoreType()) {
            // 清理ScoreType
            builder.clearScoreType();
            fieldCnt++;
        }
        if (this.getOptionalItemId() != 0) {
            builder.setOptionalItemId(this.getOptionalItemId());
            fieldCnt++;
        }  else if (builder.hasOptionalItemId()) {
            // 清理OptionalItemId
            builder.clearOptionalItemId();
            fieldCnt++;
        }
        if (this.getOptionalItemNum() != 0) {
            builder.setOptionalItemNum(this.getOptionalItemNum());
            fieldCnt++;
        }  else if (builder.hasOptionalItemNum()) {
            // 清理OptionalItemNum
            builder.clearOptionalItemNum();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ArenaMailPlayerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCLANNAME)) {
            builder.setSClanName(this.getSClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMID)) {
            builder.setOptionalItemId(this.getOptionalItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMNUM)) {
            builder.setOptionalItemNum(this.getOptionalItemNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ArenaMailPlayerInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCLANNAME)) {
            builder.setSClanName(this.getSClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMID)) {
            builder.setOptionalItemId(this.getOptionalItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMNUM)) {
            builder.setOptionalItemNum(this.getOptionalItemNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ArenaMailPlayerInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRank()) {
            this.innerSetRank(proto.getRank());
        } else {
            this.innerSetRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSClanName()) {
            this.innerSetSClanName(proto.getSClanName());
        } else {
            this.innerSetSClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScoreType()) {
            this.innerSetScoreType(proto.getScoreType());
        } else {
            this.innerSetScoreType(RankScoreType.forNumber(0));
        }
        if (proto.hasOptionalItemId()) {
            this.innerSetOptionalItemId(proto.getOptionalItemId());
        } else {
            this.innerSetOptionalItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOptionalItemNum()) {
            this.innerSetOptionalItemNum(proto.getOptionalItemNum());
        } else {
            this.innerSetOptionalItemNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArenaMailPlayerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ArenaMailPlayerInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRank()) {
            this.setRank(proto.getRank());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasSClanName()) {
            this.setSClanName(proto.getSClanName());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasScoreType()) {
            this.setScoreType(proto.getScoreType());
            fieldCnt++;
        }
        if (proto.hasOptionalItemId()) {
            this.setOptionalItemId(proto.getOptionalItemId());
            fieldCnt++;
        }
        if (proto.hasOptionalItemNum()) {
            this.setOptionalItemNum(proto.getOptionalItemNum());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailPlayerInfo.Builder getCopyDbBuilder() {
        final ArenaMailPlayerInfo.Builder builder = ArenaMailPlayerInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ArenaMailPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRank() != 0) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }  else if (builder.hasRank()) {
            // 清理Rank
            builder.clearRank();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getSClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSClanName(this.getSClanName());
            fieldCnt++;
        }  else if (builder.hasSClanName()) {
            // 清理SClanName
            builder.clearSClanName();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getScoreType() != RankScoreType.forNumber(0)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }  else if (builder.hasScoreType()) {
            // 清理ScoreType
            builder.clearScoreType();
            fieldCnt++;
        }
        if (this.getOptionalItemId() != 0) {
            builder.setOptionalItemId(this.getOptionalItemId());
            fieldCnt++;
        }  else if (builder.hasOptionalItemId()) {
            // 清理OptionalItemId
            builder.clearOptionalItemId();
            fieldCnt++;
        }
        if (this.getOptionalItemNum() != 0) {
            builder.setOptionalItemNum(this.getOptionalItemNum());
            fieldCnt++;
        }  else if (builder.hasOptionalItemNum()) {
            // 清理OptionalItemNum
            builder.clearOptionalItemNum();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ArenaMailPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCLANNAME)) {
            builder.setSClanName(this.getSClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMID)) {
            builder.setOptionalItemId(this.getOptionalItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMNUM)) {
            builder.setOptionalItemNum(this.getOptionalItemNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ArenaMailPlayerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRank()) {
            this.innerSetRank(proto.getRank());
        } else {
            this.innerSetRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSClanName()) {
            this.innerSetSClanName(proto.getSClanName());
        } else {
            this.innerSetSClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScoreType()) {
            this.innerSetScoreType(proto.getScoreType());
        } else {
            this.innerSetScoreType(RankScoreType.forNumber(0));
        }
        if (proto.hasOptionalItemId()) {
            this.innerSetOptionalItemId(proto.getOptionalItemId());
        } else {
            this.innerSetOptionalItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOptionalItemNum()) {
            this.innerSetOptionalItemNum(proto.getOptionalItemNum());
        } else {
            this.innerSetOptionalItemNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArenaMailPlayerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ArenaMailPlayerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRank()) {
            this.setRank(proto.getRank());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasSClanName()) {
            this.setSClanName(proto.getSClanName());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasScoreType()) {
            this.setScoreType(proto.getScoreType());
            fieldCnt++;
        }
        if (proto.hasOptionalItemId()) {
            this.setOptionalItemId(proto.getOptionalItemId());
            fieldCnt++;
        }
        if (proto.hasOptionalItemNum()) {
            this.setOptionalItemNum(proto.getOptionalItemNum());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ArenaMailPlayerInfo.Builder getCopySsBuilder() {
        final ArenaMailPlayerInfo.Builder builder = ArenaMailPlayerInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ArenaMailPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRank() != 0) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }  else if (builder.hasRank()) {
            // 清理Rank
            builder.clearRank();
            fieldCnt++;
        }
        if (!this.getName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setName(this.getName());
            fieldCnt++;
        }  else if (builder.hasName()) {
            // 清理Name
            builder.clearName();
            fieldCnt++;
        }
        if (!this.getSClanName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setSClanName(this.getSClanName());
            fieldCnt++;
        }  else if (builder.hasSClanName()) {
            // 清理SClanName
            builder.clearSClanName();
            fieldCnt++;
        }
        if (this.getScore() != 0) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }  else if (builder.hasScore()) {
            // 清理Score
            builder.clearScore();
            fieldCnt++;
        }
        if (this.getScoreType() != RankScoreType.forNumber(0)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }  else if (builder.hasScoreType()) {
            // 清理ScoreType
            builder.clearScoreType();
            fieldCnt++;
        }
        if (this.getOptionalItemId() != 0) {
            builder.setOptionalItemId(this.getOptionalItemId());
            fieldCnt++;
        }  else if (builder.hasOptionalItemId()) {
            // 清理OptionalItemId
            builder.clearOptionalItemId();
            fieldCnt++;
        }
        if (this.getOptionalItemNum() != 0) {
            builder.setOptionalItemNum(this.getOptionalItemNum());
            fieldCnt++;
        }  else if (builder.hasOptionalItemNum()) {
            // 清理OptionalItemNum
            builder.clearOptionalItemNum();
            fieldCnt++;
        }
        if (this.getZoneId() != 0) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }  else if (builder.hasZoneId()) {
            // 清理ZoneId
            builder.clearZoneId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ArenaMailPlayerInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RANK)) {
            builder.setRank(this.getRank());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NAME)) {
            builder.setName(this.getName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCLANNAME)) {
            builder.setSClanName(this.getSClanName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORE)) {
            builder.setScore(this.getScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SCORETYPE)) {
            builder.setScoreType(this.getScoreType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMID)) {
            builder.setOptionalItemId(this.getOptionalItemId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OPTIONALITEMNUM)) {
            builder.setOptionalItemNum(this.getOptionalItemNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ZONEID)) {
            builder.setZoneId(this.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ArenaMailPlayerInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRank()) {
            this.innerSetRank(proto.getRank());
        } else {
            this.innerSetRank(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasName()) {
            this.innerSetName(proto.getName());
        } else {
            this.innerSetName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasSClanName()) {
            this.innerSetSClanName(proto.getSClanName());
        } else {
            this.innerSetSClanName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasScore()) {
            this.innerSetScore(proto.getScore());
        } else {
            this.innerSetScore(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasScoreType()) {
            this.innerSetScoreType(proto.getScoreType());
        } else {
            this.innerSetScoreType(RankScoreType.forNumber(0));
        }
        if (proto.hasOptionalItemId()) {
            this.innerSetOptionalItemId(proto.getOptionalItemId());
        } else {
            this.innerSetOptionalItemId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasOptionalItemNum()) {
            this.innerSetOptionalItemNum(proto.getOptionalItemNum());
        } else {
            this.innerSetOptionalItemNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasZoneId()) {
            this.innerSetZoneId(proto.getZoneId());
        } else {
            this.innerSetZoneId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ArenaMailPlayerInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ArenaMailPlayerInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRank()) {
            this.setRank(proto.getRank());
            fieldCnt++;
        }
        if (proto.hasName()) {
            this.setName(proto.getName());
            fieldCnt++;
        }
        if (proto.hasSClanName()) {
            this.setSClanName(proto.getSClanName());
            fieldCnt++;
        }
        if (proto.hasScore()) {
            this.setScore(proto.getScore());
            fieldCnt++;
        }
        if (proto.hasScoreType()) {
            this.setScoreType(proto.getScoreType());
            fieldCnt++;
        }
        if (proto.hasOptionalItemId()) {
            this.setOptionalItemId(proto.getOptionalItemId());
            fieldCnt++;
        }
        if (proto.hasOptionalItemNum()) {
            this.setOptionalItemNum(proto.getOptionalItemNum());
            fieldCnt++;
        }
        if (proto.hasZoneId()) {
            this.setZoneId(proto.getZoneId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ArenaMailPlayerInfo.Builder builder = ArenaMailPlayerInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ArenaMailPlayerInfoProp)) {
            return false;
        }
        final ArenaMailPlayerInfoProp otherNode = (ArenaMailPlayerInfoProp) node;
        if (this.rank != otherNode.rank) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.name, otherNode.name)) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.sClanName, otherNode.sClanName)) {
            return false;
        }
        if (this.score != otherNode.score) {
            return false;
        }
        if (this.scoreType != otherNode.scoreType) {
            return false;
        }
        if (this.optionalItemId != otherNode.optionalItemId) {
            return false;
        }
        if (this.optionalItemNum != otherNode.optionalItemNum) {
            return false;
        }
        if (this.zoneId != otherNode.zoneId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerKillMonsterModel;
import com.yorha.proto.PlayerPB.PlayerKillMonsterModelPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerKillMonsterModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GUARDKILLNUMDAY = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private int GuardKillNumDay = Constant.DEFAULT_INT_VALUE;

    public PlayerKillMonsterModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerKillMonsterModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get GuardKillNumDay
     *
     * @return GuardKillNumDay value
     */
    public int getGuardKillNumDay() {
        return this.GuardKillNumDay;
    }

    /**
     * set GuardKillNumDay && set marked
     *
     * @param GuardKillNumDay new value
     * @return current object
     */
    public PlayerKillMonsterModelProp setGuardKillNumDay(int GuardKillNumDay) {
        if (this.GuardKillNumDay != GuardKillNumDay) {
            this.mark(FIELD_INDEX_GUARDKILLNUMDAY);
            this.GuardKillNumDay = GuardKillNumDay;
        }
        return this;
    }

    /**
     * inner set GuardKillNumDay
     *
     * @param GuardKillNumDay new value
     */
    private void innerSetGuardKillNumDay(int GuardKillNumDay) {
        this.GuardKillNumDay = GuardKillNumDay;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerKillMonsterModelPB.Builder getCopyCsBuilder() {
        final PlayerKillMonsterModelPB.Builder builder = PlayerKillMonsterModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerKillMonsterModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGuardKillNumDay() != 0) {
            builder.setGuardKillNumDay(this.getGuardKillNumDay());
            fieldCnt++;
        }  else if (builder.hasGuardKillNumDay()) {
            // 清理GuardKillNumDay
            builder.clearGuardKillNumDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerKillMonsterModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDKILLNUMDAY)) {
            builder.setGuardKillNumDay(this.getGuardKillNumDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerKillMonsterModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDKILLNUMDAY)) {
            builder.setGuardKillNumDay(this.getGuardKillNumDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerKillMonsterModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGuardKillNumDay()) {
            this.innerSetGuardKillNumDay(proto.getGuardKillNumDay());
        } else {
            this.innerSetGuardKillNumDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerKillMonsterModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerKillMonsterModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGuardKillNumDay()) {
            this.setGuardKillNumDay(proto.getGuardKillNumDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerKillMonsterModel.Builder getCopyDbBuilder() {
        final PlayerKillMonsterModel.Builder builder = PlayerKillMonsterModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerKillMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGuardKillNumDay() != 0) {
            builder.setGuardKillNumDay(this.getGuardKillNumDay());
            fieldCnt++;
        }  else if (builder.hasGuardKillNumDay()) {
            // 清理GuardKillNumDay
            builder.clearGuardKillNumDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerKillMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDKILLNUMDAY)) {
            builder.setGuardKillNumDay(this.getGuardKillNumDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerKillMonsterModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGuardKillNumDay()) {
            this.innerSetGuardKillNumDay(proto.getGuardKillNumDay());
        } else {
            this.innerSetGuardKillNumDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerKillMonsterModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerKillMonsterModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGuardKillNumDay()) {
            this.setGuardKillNumDay(proto.getGuardKillNumDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerKillMonsterModel.Builder getCopySsBuilder() {
        final PlayerKillMonsterModel.Builder builder = PlayerKillMonsterModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerKillMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getGuardKillNumDay() != 0) {
            builder.setGuardKillNumDay(this.getGuardKillNumDay());
            fieldCnt++;
        }  else if (builder.hasGuardKillNumDay()) {
            // 清理GuardKillNumDay
            builder.clearGuardKillNumDay();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerKillMonsterModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GUARDKILLNUMDAY)) {
            builder.setGuardKillNumDay(this.getGuardKillNumDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerKillMonsterModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGuardKillNumDay()) {
            this.innerSetGuardKillNumDay(proto.getGuardKillNumDay());
        } else {
            this.innerSetGuardKillNumDay(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerKillMonsterModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerKillMonsterModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGuardKillNumDay()) {
            this.setGuardKillNumDay(proto.getGuardKillNumDay());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerKillMonsterModel.Builder builder = PlayerKillMonsterModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerKillMonsterModelProp)) {
            return false;
        }
        final PlayerKillMonsterModelProp otherNode = (PlayerKillMonsterModelProp) node;
        if (this.GuardKillNumDay != otherNode.GuardKillNumDay) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
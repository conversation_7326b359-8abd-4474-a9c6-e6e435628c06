package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Struct.CollectData;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.CollectDataPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class CollectDataProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CURRENCYTYPE = 0;
    public static final int FIELD_INDEX_LEVEL = 1;
    public static final int FIELD_INDEX_POINT = 2;
    public static final int FIELD_INDEX_LASTREFRESHCOLLECTSTAMP = 3;
    public static final int FIELD_INDEX_COUNT = 4;
    public static final int FIELD_INDEX_EXTRACOUNT = 5;
    public static final int FIELD_INDEX_TEMPLATEID = 6;
    public static final int FIELD_INDEX_DECCOUNT = 7;

    public static final int FIELD_COUNT = 8;

    private long markBits0 = 0L;

    private CurrencyType currencyType = CurrencyType.forNumber(0);
    private int level = Constant.DEFAULT_INT_VALUE;
    private PointProp point = null;
    private long lastRefreshCollectStamp = Constant.DEFAULT_LONG_VALUE;
    private long count = Constant.DEFAULT_LONG_VALUE;
    private long extraCount = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private long decCount = Constant.DEFAULT_LONG_VALUE;

    public CollectDataProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CollectDataProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get currencyType
     *
     * @return currencyType value
     */
    public CurrencyType getCurrencyType() {
        return this.currencyType;
    }

    /**
     * set currencyType && set marked
     *
     * @param currencyType new value
     * @return current object
     */
    public CollectDataProp setCurrencyType(CurrencyType currencyType) {
        if (currencyType == null) {
            throw new NullPointerException();
        }
        if (this.currencyType != currencyType) {
            this.mark(FIELD_INDEX_CURRENCYTYPE);
            this.currencyType = currencyType;
        }
        return this;
    }

    /**
     * inner set currencyType
     *
     * @param currencyType new value
     */
    private void innerSetCurrencyType(CurrencyType currencyType) {
        this.currencyType = currencyType;
    }

    /**
     * get level
     *
     * @return level value
     */
    public int getLevel() {
        return this.level;
    }

    /**
     * set level && set marked
     *
     * @param level new value
     * @return current object
     */
    public CollectDataProp setLevel(int level) {
        if (this.level != level) {
            this.mark(FIELD_INDEX_LEVEL);
            this.level = level;
        }
        return this;
    }

    /**
     * inner set level
     *
     * @param level new value
     */
    private void innerSetLevel(int level) {
        this.level = level;
    }

    /**
     * get point
     *
     * @return point value
     */
    public PointProp getPoint() {
        if (this.point == null) {
            this.point = new PointProp(this, FIELD_INDEX_POINT);
        }
        return this.point;
    }

    /**
     * get lastRefreshCollectStamp
     *
     * @return lastRefreshCollectStamp value
     */
    public long getLastRefreshCollectStamp() {
        return this.lastRefreshCollectStamp;
    }

    /**
     * set lastRefreshCollectStamp && set marked
     *
     * @param lastRefreshCollectStamp new value
     * @return current object
     */
    public CollectDataProp setLastRefreshCollectStamp(long lastRefreshCollectStamp) {
        if (this.lastRefreshCollectStamp != lastRefreshCollectStamp) {
            this.mark(FIELD_INDEX_LASTREFRESHCOLLECTSTAMP);
            this.lastRefreshCollectStamp = lastRefreshCollectStamp;
        }
        return this;
    }

    /**
     * inner set lastRefreshCollectStamp
     *
     * @param lastRefreshCollectStamp new value
     */
    private void innerSetLastRefreshCollectStamp(long lastRefreshCollectStamp) {
        this.lastRefreshCollectStamp = lastRefreshCollectStamp;
    }

    /**
     * get count
     *
     * @return count value
     */
    public long getCount() {
        return this.count;
    }

    /**
     * set count && set marked
     *
     * @param count new value
     * @return current object
     */
    public CollectDataProp setCount(long count) {
        if (this.count != count) {
            this.mark(FIELD_INDEX_COUNT);
            this.count = count;
        }
        return this;
    }

    /**
     * inner set count
     *
     * @param count new value
     */
    private void innerSetCount(long count) {
        this.count = count;
    }

    /**
     * get extraCount
     *
     * @return extraCount value
     */
    public long getExtraCount() {
        return this.extraCount;
    }

    /**
     * set extraCount && set marked
     *
     * @param extraCount new value
     * @return current object
     */
    public CollectDataProp setExtraCount(long extraCount) {
        if (this.extraCount != extraCount) {
            this.mark(FIELD_INDEX_EXTRACOUNT);
            this.extraCount = extraCount;
        }
        return this;
    }

    /**
     * inner set extraCount
     *
     * @param extraCount new value
     */
    private void innerSetExtraCount(long extraCount) {
        this.extraCount = extraCount;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public CollectDataProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get decCount
     *
     * @return decCount value
     */
    public long getDecCount() {
        return this.decCount;
    }

    /**
     * set decCount && set marked
     *
     * @param decCount new value
     * @return current object
     */
    public CollectDataProp setDecCount(long decCount) {
        if (this.decCount != decCount) {
            this.mark(FIELD_INDEX_DECCOUNT);
            this.decCount = decCount;
        }
        return this;
    }

    /**
     * inner set decCount
     *
     * @param decCount new value
     */
    private void innerSetDecCount(long decCount) {
        this.decCount = decCount;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CollectDataPB.Builder getCopyCsBuilder() {
        final CollectDataPB.Builder builder = CollectDataPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CollectDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurrencyType() != CurrencyType.forNumber(0)) {
            builder.setCurrencyType(this.getCurrencyType());
            fieldCnt++;
        }  else if (builder.hasCurrencyType()) {
            // 清理CurrencyType
            builder.clearCurrencyType();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.point != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.point.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getLastRefreshCollectStamp() != 0L) {
            builder.setLastRefreshCollectStamp(this.getLastRefreshCollectStamp());
            fieldCnt++;
        }  else if (builder.hasLastRefreshCollectStamp()) {
            // 清理LastRefreshCollectStamp
            builder.clearLastRefreshCollectStamp();
            fieldCnt++;
        }
        if (this.getCount() != 0L) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        if (this.getExtraCount() != 0L) {
            builder.setExtraCount(this.getExtraCount());
            fieldCnt++;
        }  else if (builder.hasExtraCount()) {
            // 清理ExtraCount
            builder.clearExtraCount();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getDecCount() != 0L) {
            builder.setDecCount(this.getDecCount());
            fieldCnt++;
        }  else if (builder.hasDecCount()) {
            // 清理DecCount
            builder.clearDecCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CollectDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRENCYTYPE)) {
            builder.setCurrencyType(this.getCurrencyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHCOLLECTSTAMP)) {
            builder.setLastRefreshCollectStamp(this.getLastRefreshCollectStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACOUNT)) {
            builder.setExtraCount(this.getExtraCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DECCOUNT)) {
            builder.setDecCount(this.getDecCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CollectDataPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRENCYTYPE)) {
            builder.setCurrencyType(this.getCurrencyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToAndClearDeleteKeysCs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHCOLLECTSTAMP)) {
            builder.setLastRefreshCollectStamp(this.getLastRefreshCollectStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACOUNT)) {
            builder.setExtraCount(this.getExtraCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DECCOUNT)) {
            builder.setDecCount(this.getDecCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CollectDataPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurrencyType()) {
            this.innerSetCurrencyType(proto.getCurrencyType());
        } else {
            this.innerSetCurrencyType(CurrencyType.forNumber(0));
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromCs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromCs(proto.getPoint());
            }
        }
        if (proto.hasLastRefreshCollectStamp()) {
            this.innerSetLastRefreshCollectStamp(proto.getLastRefreshCollectStamp());
        } else {
            this.innerSetLastRefreshCollectStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExtraCount()) {
            this.innerSetExtraCount(proto.getExtraCount());
        } else {
            this.innerSetExtraCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDecCount()) {
            this.innerSetDecCount(proto.getDecCount());
        } else {
            this.innerSetDecCount(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CollectDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CollectDataPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurrencyType()) {
            this.setCurrencyType(proto.getCurrencyType());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromCs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasLastRefreshCollectStamp()) {
            this.setLastRefreshCollectStamp(proto.getLastRefreshCollectStamp());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        if (proto.hasExtraCount()) {
            this.setExtraCount(proto.getExtraCount());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasDecCount()) {
            this.setDecCount(proto.getDecCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CollectData.Builder getCopyDbBuilder() {
        final CollectData.Builder builder = CollectData.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CollectData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurrencyType() != CurrencyType.forNumber(0)) {
            builder.setCurrencyType(this.getCurrencyType());
            fieldCnt++;
        }  else if (builder.hasCurrencyType()) {
            // 清理CurrencyType
            builder.clearCurrencyType();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getLastRefreshCollectStamp() != 0L) {
            builder.setLastRefreshCollectStamp(this.getLastRefreshCollectStamp());
            fieldCnt++;
        }  else if (builder.hasLastRefreshCollectStamp()) {
            // 清理LastRefreshCollectStamp
            builder.clearLastRefreshCollectStamp();
            fieldCnt++;
        }
        if (this.getCount() != 0L) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        if (this.getExtraCount() != 0L) {
            builder.setExtraCount(this.getExtraCount());
            fieldCnt++;
        }  else if (builder.hasExtraCount()) {
            // 清理ExtraCount
            builder.clearExtraCount();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getDecCount() != 0L) {
            builder.setDecCount(this.getDecCount());
            fieldCnt++;
        }  else if (builder.hasDecCount()) {
            // 清理DecCount
            builder.clearDecCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CollectData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRENCYTYPE)) {
            builder.setCurrencyType(this.getCurrencyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToDb(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHCOLLECTSTAMP)) {
            builder.setLastRefreshCollectStamp(this.getLastRefreshCollectStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACOUNT)) {
            builder.setExtraCount(this.getExtraCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DECCOUNT)) {
            builder.setDecCount(this.getDecCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CollectData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurrencyType()) {
            this.innerSetCurrencyType(proto.getCurrencyType());
        } else {
            this.innerSetCurrencyType(CurrencyType.forNumber(0));
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromDb(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromDb(proto.getPoint());
            }
        }
        if (proto.hasLastRefreshCollectStamp()) {
            this.innerSetLastRefreshCollectStamp(proto.getLastRefreshCollectStamp());
        } else {
            this.innerSetLastRefreshCollectStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExtraCount()) {
            this.innerSetExtraCount(proto.getExtraCount());
        } else {
            this.innerSetExtraCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDecCount()) {
            this.innerSetDecCount(proto.getDecCount());
        } else {
            this.innerSetDecCount(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CollectDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CollectData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurrencyType()) {
            this.setCurrencyType(proto.getCurrencyType());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromDb(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasLastRefreshCollectStamp()) {
            this.setLastRefreshCollectStamp(proto.getLastRefreshCollectStamp());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        if (proto.hasExtraCount()) {
            this.setExtraCount(proto.getExtraCount());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasDecCount()) {
            this.setDecCount(proto.getDecCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CollectData.Builder getCopySsBuilder() {
        final CollectData.Builder builder = CollectData.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CollectData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getCurrencyType() != CurrencyType.forNumber(0)) {
            builder.setCurrencyType(this.getCurrencyType());
            fieldCnt++;
        }  else if (builder.hasCurrencyType()) {
            // 清理CurrencyType
            builder.clearCurrencyType();
            fieldCnt++;
        }
        if (this.getLevel() != 0) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }  else if (builder.hasLevel()) {
            // 清理Level
            builder.clearLevel();
            fieldCnt++;
        }
        if (this.point != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.point.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPoint(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPoint();
            }
        }  else if (builder.hasPoint()) {
            // 清理Point
            builder.clearPoint();
            fieldCnt++;
        }
        if (this.getLastRefreshCollectStamp() != 0L) {
            builder.setLastRefreshCollectStamp(this.getLastRefreshCollectStamp());
            fieldCnt++;
        }  else if (builder.hasLastRefreshCollectStamp()) {
            // 清理LastRefreshCollectStamp
            builder.clearLastRefreshCollectStamp();
            fieldCnt++;
        }
        if (this.getCount() != 0L) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }  else if (builder.hasCount()) {
            // 清理Count
            builder.clearCount();
            fieldCnt++;
        }
        if (this.getExtraCount() != 0L) {
            builder.setExtraCount(this.getExtraCount());
            fieldCnt++;
        }  else if (builder.hasExtraCount()) {
            // 清理ExtraCount
            builder.clearExtraCount();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getDecCount() != 0L) {
            builder.setDecCount(this.getDecCount());
            fieldCnt++;
        }  else if (builder.hasDecCount()) {
            // 清理DecCount
            builder.clearDecCount();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CollectData.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CURRENCYTYPE)) {
            builder.setCurrencyType(this.getCurrencyType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_LEVEL)) {
            builder.setLevel(this.getLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            final boolean needClear = !builder.hasPoint();
            final int tmpFieldCnt = this.point.copyChangeToSs(builder.getPointBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPoint();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTREFRESHCOLLECTSTAMP)) {
            builder.setLastRefreshCollectStamp(this.getLastRefreshCollectStamp());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COUNT)) {
            builder.setCount(this.getCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_EXTRACOUNT)) {
            builder.setExtraCount(this.getExtraCount());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DECCOUNT)) {
            builder.setDecCount(this.getDecCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CollectData proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasCurrencyType()) {
            this.innerSetCurrencyType(proto.getCurrencyType());
        } else {
            this.innerSetCurrencyType(CurrencyType.forNumber(0));
        }
        if (proto.hasLevel()) {
            this.innerSetLevel(proto.getLevel());
        } else {
            this.innerSetLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeFromSs(proto.getPoint());
        } else {
            if (this.point != null) {
                this.point.mergeFromSs(proto.getPoint());
            }
        }
        if (proto.hasLastRefreshCollectStamp()) {
            this.innerSetLastRefreshCollectStamp(proto.getLastRefreshCollectStamp());
        } else {
            this.innerSetLastRefreshCollectStamp(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCount()) {
            this.innerSetCount(proto.getCount());
        } else {
            this.innerSetCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasExtraCount()) {
            this.innerSetExtraCount(proto.getExtraCount());
        } else {
            this.innerSetExtraCount(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasDecCount()) {
            this.innerSetDecCount(proto.getDecCount());
        } else {
            this.innerSetDecCount(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CollectDataProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CollectData proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasCurrencyType()) {
            this.setCurrencyType(proto.getCurrencyType());
            fieldCnt++;
        }
        if (proto.hasLevel()) {
            this.setLevel(proto.getLevel());
            fieldCnt++;
        }
        if (proto.hasPoint()) {
            this.getPoint().mergeChangeFromSs(proto.getPoint());
            fieldCnt++;
        }
        if (proto.hasLastRefreshCollectStamp()) {
            this.setLastRefreshCollectStamp(proto.getLastRefreshCollectStamp());
            fieldCnt++;
        }
        if (proto.hasCount()) {
            this.setCount(proto.getCount());
            fieldCnt++;
        }
        if (proto.hasExtraCount()) {
            this.setExtraCount(proto.getExtraCount());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasDecCount()) {
            this.setDecCount(proto.getDecCount());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CollectData.Builder builder = CollectData.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_POINT) && this.point != null) {
            this.point.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.point != null) {
            this.point.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CollectDataProp)) {
            return false;
        }
        final CollectDataProp otherNode = (CollectDataProp) node;
        if (this.currencyType != otherNode.currencyType) {
            return false;
        }
        if (this.level != otherNode.level) {
            return false;
        }
        if (!this.getPoint().compareDataTo(otherNode.getPoint())) {
            return false;
        }
        if (this.lastRefreshCollectStamp != otherNode.lastRefreshCollectStamp) {
            return false;
        }
        if (this.count != otherNode.count) {
            return false;
        }
        if (this.extraCount != otherNode.extraCount) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.decCount != otherNode.decCount) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 56;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructPlayer.CityAssistInfo;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.CityAssistInfoPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class CityAssistInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_MAXSOLDIERNUM = 0;
    public static final int FIELD_INDEX_CURSOLDIERNUM = 1;
    public static final int FIELD_INDEX_ARMY = 2;
    public static final int FIELD_INDEX_LEADERARMYID = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private long maxSoldierNum = Constant.DEFAULT_LONG_VALUE;
    private long curSoldierNum = Constant.DEFAULT_LONG_VALUE;
    private Int64RallyArmyInfoMapProp army = null;
    private long leaderArmyId = Constant.DEFAULT_LONG_VALUE;

    public CityAssistInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CityAssistInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get maxSoldierNum
     *
     * @return maxSoldierNum value
     */
    public long getMaxSoldierNum() {
        return this.maxSoldierNum;
    }

    /**
     * set maxSoldierNum && set marked
     *
     * @param maxSoldierNum new value
     * @return current object
     */
    public CityAssistInfoProp setMaxSoldierNum(long maxSoldierNum) {
        if (this.maxSoldierNum != maxSoldierNum) {
            this.mark(FIELD_INDEX_MAXSOLDIERNUM);
            this.maxSoldierNum = maxSoldierNum;
        }
        return this;
    }

    /**
     * inner set maxSoldierNum
     *
     * @param maxSoldierNum new value
     */
    private void innerSetMaxSoldierNum(long maxSoldierNum) {
        this.maxSoldierNum = maxSoldierNum;
    }

    /**
     * get curSoldierNum
     *
     * @return curSoldierNum value
     */
    public long getCurSoldierNum() {
        return this.curSoldierNum;
    }

    /**
     * set curSoldierNum && set marked
     *
     * @param curSoldierNum new value
     * @return current object
     */
    public CityAssistInfoProp setCurSoldierNum(long curSoldierNum) {
        if (this.curSoldierNum != curSoldierNum) {
            this.mark(FIELD_INDEX_CURSOLDIERNUM);
            this.curSoldierNum = curSoldierNum;
        }
        return this;
    }

    /**
     * inner set curSoldierNum
     *
     * @param curSoldierNum new value
     */
    private void innerSetCurSoldierNum(long curSoldierNum) {
        this.curSoldierNum = curSoldierNum;
    }

    /**
     * get army
     *
     * @return army value
     */
    public Int64RallyArmyInfoMapProp getArmy() {
        if (this.army == null) {
            this.army = new Int64RallyArmyInfoMapProp(this, FIELD_INDEX_ARMY);
        }
        return this.army;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putArmyV(RallyArmyInfoProp v) {
        this.getArmy().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public RallyArmyInfoProp addEmptyArmy(Long k) {
        return this.getArmy().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getArmySize() {
        if (this.army == null) {
            return 0;
        }
        return this.army.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isArmyEmpty() {
        if (this.army == null) {
            return true;
        }
        return this.army.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public RallyArmyInfoProp getArmyV(Long k) {
        if (this.army == null || !this.army.containsKey(k)) {
            return null;
        }
        return this.army.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearArmy() {
        if (this.army != null) {
            this.army.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeArmyV(Long k) {
        if (this.army != null) {
            this.army.remove(k);
        }
    }
    /**
     * get leaderArmyId
     *
     * @return leaderArmyId value
     */
    public long getLeaderArmyId() {
        return this.leaderArmyId;
    }

    /**
     * set leaderArmyId && set marked
     *
     * @param leaderArmyId new value
     * @return current object
     */
    public CityAssistInfoProp setLeaderArmyId(long leaderArmyId) {
        if (this.leaderArmyId != leaderArmyId) {
            this.mark(FIELD_INDEX_LEADERARMYID);
            this.leaderArmyId = leaderArmyId;
        }
        return this;
    }

    /**
     * inner set leaderArmyId
     *
     * @param leaderArmyId new value
     */
    private void innerSetLeaderArmyId(long leaderArmyId) {
        this.leaderArmyId = leaderArmyId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityAssistInfoPB.Builder getCopyCsBuilder() {
        final CityAssistInfoPB.Builder builder = CityAssistInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CityAssistInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMaxSoldierNum() != 0L) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }  else if (builder.hasMaxSoldierNum()) {
            // 清理MaxSoldierNum
            builder.clearMaxSoldierNum();
            fieldCnt++;
        }
        if (this.getCurSoldierNum() != 0L) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }  else if (builder.hasCurSoldierNum()) {
            // 清理CurSoldierNum
            builder.clearCurSoldierNum();
            fieldCnt++;
        }
        if (this.army != null) {
            StructPlayerPB.Int64RallyArmyInfoMapPB.Builder tmpBuilder = StructPlayerPB.Int64RallyArmyInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.army.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        if (this.getLeaderArmyId() != 0L) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }  else if (builder.hasLeaderArmyId()) {
            // 清理LeaderArmyId
            builder.clearLeaderArmyId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CityAssistInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAXSOLDIERNUM)) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERNUM)) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToCs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEADERARMYID)) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CityAssistInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAXSOLDIERNUM)) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERNUM)) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToAndClearDeleteKeysCs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEADERARMYID)) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CityAssistInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMaxSoldierNum()) {
            this.innerSetMaxSoldierNum(proto.getMaxSoldierNum());
        } else {
            this.innerSetMaxSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurSoldierNum()) {
            this.innerSetCurSoldierNum(proto.getCurSoldierNum());
        } else {
            this.innerSetCurSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromCs(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromCs(proto.getArmy());
            }
        }
        if (proto.hasLeaderArmyId()) {
            this.innerSetLeaderArmyId(proto.getLeaderArmyId());
        } else {
            this.innerSetLeaderArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityAssistInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CityAssistInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMaxSoldierNum()) {
            this.setMaxSoldierNum(proto.getMaxSoldierNum());
            fieldCnt++;
        }
        if (proto.hasCurSoldierNum()) {
            this.setCurSoldierNum(proto.getCurSoldierNum());
            fieldCnt++;
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromCs(proto.getArmy());
            fieldCnt++;
        }
        if (proto.hasLeaderArmyId()) {
            this.setLeaderArmyId(proto.getLeaderArmyId());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CityAssistInfo.Builder getCopySsBuilder() {
        final CityAssistInfo.Builder builder = CityAssistInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CityAssistInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMaxSoldierNum() != 0L) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }  else if (builder.hasMaxSoldierNum()) {
            // 清理MaxSoldierNum
            builder.clearMaxSoldierNum();
            fieldCnt++;
        }
        if (this.getCurSoldierNum() != 0L) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }  else if (builder.hasCurSoldierNum()) {
            // 清理CurSoldierNum
            builder.clearCurSoldierNum();
            fieldCnt++;
        }
        if (this.army != null) {
            StructPlayer.Int64RallyArmyInfoMap.Builder tmpBuilder = StructPlayer.Int64RallyArmyInfoMap.newBuilder();
            final int tmpFieldCnt = this.army.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setArmy(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearArmy();
            }
        }  else if (builder.hasArmy()) {
            // 清理Army
            builder.clearArmy();
            fieldCnt++;
        }
        if (this.getLeaderArmyId() != 0L) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }  else if (builder.hasLeaderArmyId()) {
            // 清理LeaderArmyId
            builder.clearLeaderArmyId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CityAssistInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MAXSOLDIERNUM)) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERNUM)) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            final boolean needClear = !builder.hasArmy();
            final int tmpFieldCnt = this.army.copyChangeToSs(builder.getArmyBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearArmy();
            }
        }
        if (this.hasMark(FIELD_INDEX_LEADERARMYID)) {
            builder.setLeaderArmyId(this.getLeaderArmyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CityAssistInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMaxSoldierNum()) {
            this.innerSetMaxSoldierNum(proto.getMaxSoldierNum());
        } else {
            this.innerSetMaxSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurSoldierNum()) {
            this.innerSetCurSoldierNum(proto.getCurSoldierNum());
        } else {
            this.innerSetCurSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeFromSs(proto.getArmy());
        } else {
            if (this.army != null) {
                this.army.mergeFromSs(proto.getArmy());
            }
        }
        if (proto.hasLeaderArmyId()) {
            this.innerSetLeaderArmyId(proto.getLeaderArmyId());
        } else {
            this.innerSetLeaderArmyId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CityAssistInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CityAssistInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMaxSoldierNum()) {
            this.setMaxSoldierNum(proto.getMaxSoldierNum());
            fieldCnt++;
        }
        if (proto.hasCurSoldierNum()) {
            this.setCurSoldierNum(proto.getCurSoldierNum());
            fieldCnt++;
        }
        if (proto.hasArmy()) {
            this.getArmy().mergeChangeFromSs(proto.getArmy());
            fieldCnt++;
        }
        if (proto.hasLeaderArmyId()) {
            this.setLeaderArmyId(proto.getLeaderArmyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CityAssistInfo.Builder builder = CityAssistInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ARMY) && this.army != null) {
            this.army.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.army != null) {
            this.army.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CityAssistInfoProp)) {
            return false;
        }
        final CityAssistInfoProp otherNode = (CityAssistInfoProp) node;
        if (this.maxSoldierNum != otherNode.maxSoldierNum) {
            return false;
        }
        if (this.curSoldierNum != otherNode.curSoldierNum) {
            return false;
        }
        if (!this.getArmy().compareDataTo(otherNode.getArmy())) {
            return false;
        }
        if (this.leaderArmyId != otherNode.leaderArmyId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
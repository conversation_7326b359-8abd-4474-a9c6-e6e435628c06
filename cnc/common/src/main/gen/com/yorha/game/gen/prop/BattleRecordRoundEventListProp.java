package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructBattlePB.BattleRecordRoundEventListPB;
import com.yorha.proto.StructBattle.BattleRecordRoundEventList;
import com.yorha.proto.StructBattlePB.BattleRecordRoundEventPB;
import com.yorha.proto.StructBattle.BattleRecordRoundEvent;

/**
 * <AUTHOR> auto gen
 */
public class BattleRecordRoundEventListProp extends AbstractListNode<BattleRecordRoundEventProp> {
    /**
     * Create a BattleRecordRoundEventListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public BattleRecordRoundEventListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to BattleRecordRoundEventListProp
     *
     * @return new object
     */
    @Override
    public BattleRecordRoundEventProp addEmptyValue() {
        final BattleRecordRoundEventProp newProp = new BattleRecordRoundEventProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundEventListPB.Builder getCopyCsBuilder() {
        final BattleRecordRoundEventListPB.Builder builder = BattleRecordRoundEventListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(BattleRecordRoundEventListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordRoundEventListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordRoundEventProp v : this) {
            final BattleRecordRoundEventPB.Builder itemBuilder = BattleRecordRoundEventPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(BattleRecordRoundEventListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(BattleRecordRoundEventListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordRoundEventPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(BattleRecordRoundEventListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundEventList.Builder getCopyDbBuilder() {
        final BattleRecordRoundEventList.Builder builder = BattleRecordRoundEventList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(BattleRecordRoundEventList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordRoundEventListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordRoundEventProp v : this) {
            final BattleRecordRoundEvent.Builder itemBuilder = BattleRecordRoundEvent.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(BattleRecordRoundEventList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(BattleRecordRoundEventList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordRoundEvent v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(BattleRecordRoundEventList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BattleRecordRoundEventList.Builder getCopySsBuilder() {
        final BattleRecordRoundEventList.Builder builder = BattleRecordRoundEventList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(BattleRecordRoundEventList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return BattleRecordRoundEventListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final BattleRecordRoundEventProp v : this) {
            final BattleRecordRoundEvent.Builder itemBuilder = BattleRecordRoundEvent.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(BattleRecordRoundEventList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(BattleRecordRoundEventList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (BattleRecordRoundEvent v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return BattleRecordRoundEventListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(BattleRecordRoundEventList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        BattleRecordRoundEventList.Builder builder = BattleRecordRoundEventList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
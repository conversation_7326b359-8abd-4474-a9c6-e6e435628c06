package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructSkynet.ShopHistory;
import com.yorha.proto.StructSkynetPB.ShopHistoryPB;


/**
 * <AUTHOR> auto gen
 */
public class ShopHistoryProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SHOPID = 0;
    public static final int FIELD_INDEX_NUM = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int shopId = Constant.DEFAULT_INT_VALUE;
    private int num = Constant.DEFAULT_INT_VALUE;

    public ShopHistoryProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ShopHistoryProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get shopId
     *
     * @return shopId value
     */
    public int getShopId() {
        return this.shopId;
    }

    /**
     * set shopId && set marked
     *
     * @param shopId new value
     * @return current object
     */
    public ShopHistoryProp setShopId(int shopId) {
        if (this.shopId != shopId) {
            this.mark(FIELD_INDEX_SHOPID);
            this.shopId = shopId;
        }
        return this;
    }

    /**
     * inner set shopId
     *
     * @param shopId new value
     */
    private void innerSetShopId(int shopId) {
        this.shopId = shopId;
    }

    /**
     * get num
     *
     * @return num value
     */
    public int getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public ShopHistoryProp setNum(int num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(int num) {
        this.num = num;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ShopHistoryPB.Builder getCopyCsBuilder() {
        final ShopHistoryPB.Builder builder = ShopHistoryPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ShopHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getShopId() != 0) {
            builder.setShopId(this.getShopId());
            fieldCnt++;
        }  else if (builder.hasShopId()) {
            // 清理ShopId
            builder.clearShopId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ShopHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHOPID)) {
            builder.setShopId(this.getShopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ShopHistoryPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHOPID)) {
            builder.setShopId(this.getShopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ShopHistoryPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShopId()) {
            this.innerSetShopId(proto.getShopId());
        } else {
            this.innerSetShopId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ShopHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ShopHistoryPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShopId()) {
            this.setShopId(proto.getShopId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ShopHistory.Builder getCopyDbBuilder() {
        final ShopHistory.Builder builder = ShopHistory.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ShopHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getShopId() != 0) {
            builder.setShopId(this.getShopId());
            fieldCnt++;
        }  else if (builder.hasShopId()) {
            // 清理ShopId
            builder.clearShopId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ShopHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHOPID)) {
            builder.setShopId(this.getShopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ShopHistory proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShopId()) {
            this.innerSetShopId(proto.getShopId());
        } else {
            this.innerSetShopId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ShopHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ShopHistory proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShopId()) {
            this.setShopId(proto.getShopId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ShopHistory.Builder getCopySsBuilder() {
        final ShopHistory.Builder builder = ShopHistory.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ShopHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getShopId() != 0) {
            builder.setShopId(this.getShopId());
            fieldCnt++;
        }  else if (builder.hasShopId()) {
            // 清理ShopId
            builder.clearShopId();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ShopHistory.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SHOPID)) {
            builder.setShopId(this.getShopId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ShopHistory proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasShopId()) {
            this.innerSetShopId(proto.getShopId());
        } else {
            this.innerSetShopId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ShopHistoryProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ShopHistory proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasShopId()) {
            this.setShopId(proto.getShopId());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ShopHistory.Builder builder = ShopHistory.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.shopId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ShopHistoryProp)) {
            return false;
        }
        final ShopHistoryProp otherNode = (ShopHistoryProp) node;
        if (this.shopId != otherNode.shopId) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
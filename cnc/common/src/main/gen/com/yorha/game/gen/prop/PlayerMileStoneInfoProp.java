package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.Player.PlayerMileStoneInfo;
import com.yorha.proto.PlayerPB.PlayerMileStoneInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerMileStoneInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_MILESTONEID = 0;
    public static final int FIELD_INDEX_SELFSCORE = 1;
    public static final int FIELD_INDEX_REWARDLEVEL = 2;
    public static final int FIELD_INDEX_REWARDSTATUS = 3;
    public static final int FIELD_INDEX_ISOVER = 4;
    public static final int FIELD_INDEX_DISSATISFIEDREASON = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private int mileStoneId = Constant.DEFAULT_INT_VALUE;
    private long selfScore = Constant.DEFAULT_LONG_VALUE;
    private int rewardLevel = Constant.DEFAULT_INT_VALUE;
    private MileStoneRewardStatus rewardStatus = MileStoneRewardStatus.forNumber(0);
    private boolean isOver = Constant.DEFAULT_BOOLEAN_VALUE;
    private MileStoneDissatisfiedReaSon dissatisfiedReason = MileStoneDissatisfiedReaSon.forNumber(0);

    public PlayerMileStoneInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerMileStoneInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get mileStoneId
     *
     * @return mileStoneId value
     */
    public int getMileStoneId() {
        return this.mileStoneId;
    }

    /**
     * set mileStoneId && set marked
     *
     * @param mileStoneId new value
     * @return current object
     */
    public PlayerMileStoneInfoProp setMileStoneId(int mileStoneId) {
        if (this.mileStoneId != mileStoneId) {
            this.mark(FIELD_INDEX_MILESTONEID);
            this.mileStoneId = mileStoneId;
        }
        return this;
    }

    /**
     * inner set mileStoneId
     *
     * @param mileStoneId new value
     */
    private void innerSetMileStoneId(int mileStoneId) {
        this.mileStoneId = mileStoneId;
    }

    /**
     * get selfScore
     *
     * @return selfScore value
     */
    public long getSelfScore() {
        return this.selfScore;
    }

    /**
     * set selfScore && set marked
     *
     * @param selfScore new value
     * @return current object
     */
    public PlayerMileStoneInfoProp setSelfScore(long selfScore) {
        if (this.selfScore != selfScore) {
            this.mark(FIELD_INDEX_SELFSCORE);
            this.selfScore = selfScore;
        }
        return this;
    }

    /**
     * inner set selfScore
     *
     * @param selfScore new value
     */
    private void innerSetSelfScore(long selfScore) {
        this.selfScore = selfScore;
    }

    /**
     * get rewardLevel
     *
     * @return rewardLevel value
     */
    public int getRewardLevel() {
        return this.rewardLevel;
    }

    /**
     * set rewardLevel && set marked
     *
     * @param rewardLevel new value
     * @return current object
     */
    public PlayerMileStoneInfoProp setRewardLevel(int rewardLevel) {
        if (this.rewardLevel != rewardLevel) {
            this.mark(FIELD_INDEX_REWARDLEVEL);
            this.rewardLevel = rewardLevel;
        }
        return this;
    }

    /**
     * inner set rewardLevel
     *
     * @param rewardLevel new value
     */
    private void innerSetRewardLevel(int rewardLevel) {
        this.rewardLevel = rewardLevel;
    }

    /**
     * get rewardStatus
     *
     * @return rewardStatus value
     */
    public MileStoneRewardStatus getRewardStatus() {
        return this.rewardStatus;
    }

    /**
     * set rewardStatus && set marked
     *
     * @param rewardStatus new value
     * @return current object
     */
    public PlayerMileStoneInfoProp setRewardStatus(MileStoneRewardStatus rewardStatus) {
        if (rewardStatus == null) {
            throw new NullPointerException();
        }
        if (this.rewardStatus != rewardStatus) {
            this.mark(FIELD_INDEX_REWARDSTATUS);
            this.rewardStatus = rewardStatus;
        }
        return this;
    }

    /**
     * inner set rewardStatus
     *
     * @param rewardStatus new value
     */
    private void innerSetRewardStatus(MileStoneRewardStatus rewardStatus) {
        this.rewardStatus = rewardStatus;
    }

    /**
     * get isOver
     *
     * @return isOver value
     */
    public boolean getIsOver() {
        return this.isOver;
    }

    /**
     * set isOver && set marked
     *
     * @param isOver new value
     * @return current object
     */
    public PlayerMileStoneInfoProp setIsOver(boolean isOver) {
        if (this.isOver != isOver) {
            this.mark(FIELD_INDEX_ISOVER);
            this.isOver = isOver;
        }
        return this;
    }

    /**
     * inner set isOver
     *
     * @param isOver new value
     */
    private void innerSetIsOver(boolean isOver) {
        this.isOver = isOver;
    }

    /**
     * get dissatisfiedReason
     *
     * @return dissatisfiedReason value
     */
    public MileStoneDissatisfiedReaSon getDissatisfiedReason() {
        return this.dissatisfiedReason;
    }

    /**
     * set dissatisfiedReason && set marked
     *
     * @param dissatisfiedReason new value
     * @return current object
     */
    public PlayerMileStoneInfoProp setDissatisfiedReason(MileStoneDissatisfiedReaSon dissatisfiedReason) {
        if (dissatisfiedReason == null) {
            throw new NullPointerException();
        }
        if (this.dissatisfiedReason != dissatisfiedReason) {
            this.mark(FIELD_INDEX_DISSATISFIEDREASON);
            this.dissatisfiedReason = dissatisfiedReason;
        }
        return this;
    }

    /**
     * inner set dissatisfiedReason
     *
     * @param dissatisfiedReason new value
     */
    private void innerSetDissatisfiedReason(MileStoneDissatisfiedReaSon dissatisfiedReason) {
        this.dissatisfiedReason = dissatisfiedReason;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMileStoneInfoPB.Builder getCopyCsBuilder() {
        final PlayerMileStoneInfoPB.Builder builder = PlayerMileStoneInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerMileStoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMileStoneId() != 0) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }  else if (builder.hasMileStoneId()) {
            // 清理MileStoneId
            builder.clearMileStoneId();
            fieldCnt++;
        }
        if (this.getSelfScore() != 0L) {
            builder.setSelfScore(this.getSelfScore());
            fieldCnt++;
        }  else if (builder.hasSelfScore()) {
            // 清理SelfScore
            builder.clearSelfScore();
            fieldCnt++;
        }
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.getRewardStatus() != MileStoneRewardStatus.forNumber(0)) {
            builder.setRewardStatus(this.getRewardStatus());
            fieldCnt++;
        }  else if (builder.hasRewardStatus()) {
            // 清理RewardStatus
            builder.clearRewardStatus();
            fieldCnt++;
        }
        if (this.getIsOver()) {
            builder.setIsOver(this.getIsOver());
            fieldCnt++;
        }  else if (builder.hasIsOver()) {
            // 清理IsOver
            builder.clearIsOver();
            fieldCnt++;
        }
        if (this.getDissatisfiedReason() != MileStoneDissatisfiedReaSon.forNumber(0)) {
            builder.setDissatisfiedReason(this.getDissatisfiedReason());
            fieldCnt++;
        }  else if (builder.hasDissatisfiedReason()) {
            // 清理DissatisfiedReason
            builder.clearDissatisfiedReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerMileStoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELFSCORE)) {
            builder.setSelfScore(this.getSelfScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATUS)) {
            builder.setRewardStatus(this.getRewardStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOVER)) {
            builder.setIsOver(this.getIsOver());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSATISFIEDREASON)) {
            builder.setDissatisfiedReason(this.getDissatisfiedReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerMileStoneInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELFSCORE)) {
            builder.setSelfScore(this.getSelfScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATUS)) {
            builder.setRewardStatus(this.getRewardStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOVER)) {
            builder.setIsOver(this.getIsOver());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSATISFIEDREASON)) {
            builder.setDissatisfiedReason(this.getDissatisfiedReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerMileStoneInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMileStoneId()) {
            this.innerSetMileStoneId(proto.getMileStoneId());
        } else {
            this.innerSetMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelfScore()) {
            this.innerSetSelfScore(proto.getSelfScore());
        } else {
            this.innerSetSelfScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardStatus()) {
            this.innerSetRewardStatus(proto.getRewardStatus());
        } else {
            this.innerSetRewardStatus(MileStoneRewardStatus.forNumber(0));
        }
        if (proto.hasIsOver()) {
            this.innerSetIsOver(proto.getIsOver());
        } else {
            this.innerSetIsOver(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasDissatisfiedReason()) {
            this.innerSetDissatisfiedReason(proto.getDissatisfiedReason());
        } else {
            this.innerSetDissatisfiedReason(MileStoneDissatisfiedReaSon.forNumber(0));
        }
        this.markAll();
        return PlayerMileStoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerMileStoneInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMileStoneId()) {
            this.setMileStoneId(proto.getMileStoneId());
            fieldCnt++;
        }
        if (proto.hasSelfScore()) {
            this.setSelfScore(proto.getSelfScore());
            fieldCnt++;
        }
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardStatus()) {
            this.setRewardStatus(proto.getRewardStatus());
            fieldCnt++;
        }
        if (proto.hasIsOver()) {
            this.setIsOver(proto.getIsOver());
            fieldCnt++;
        }
        if (proto.hasDissatisfiedReason()) {
            this.setDissatisfiedReason(proto.getDissatisfiedReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMileStoneInfo.Builder getCopyDbBuilder() {
        final PlayerMileStoneInfo.Builder builder = PlayerMileStoneInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerMileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMileStoneId() != 0) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }  else if (builder.hasMileStoneId()) {
            // 清理MileStoneId
            builder.clearMileStoneId();
            fieldCnt++;
        }
        if (this.getSelfScore() != 0L) {
            builder.setSelfScore(this.getSelfScore());
            fieldCnt++;
        }  else if (builder.hasSelfScore()) {
            // 清理SelfScore
            builder.clearSelfScore();
            fieldCnt++;
        }
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.getRewardStatus() != MileStoneRewardStatus.forNumber(0)) {
            builder.setRewardStatus(this.getRewardStatus());
            fieldCnt++;
        }  else if (builder.hasRewardStatus()) {
            // 清理RewardStatus
            builder.clearRewardStatus();
            fieldCnt++;
        }
        if (this.getIsOver()) {
            builder.setIsOver(this.getIsOver());
            fieldCnt++;
        }  else if (builder.hasIsOver()) {
            // 清理IsOver
            builder.clearIsOver();
            fieldCnt++;
        }
        if (this.getDissatisfiedReason() != MileStoneDissatisfiedReaSon.forNumber(0)) {
            builder.setDissatisfiedReason(this.getDissatisfiedReason());
            fieldCnt++;
        }  else if (builder.hasDissatisfiedReason()) {
            // 清理DissatisfiedReason
            builder.clearDissatisfiedReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerMileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELFSCORE)) {
            builder.setSelfScore(this.getSelfScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATUS)) {
            builder.setRewardStatus(this.getRewardStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOVER)) {
            builder.setIsOver(this.getIsOver());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSATISFIEDREASON)) {
            builder.setDissatisfiedReason(this.getDissatisfiedReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerMileStoneInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMileStoneId()) {
            this.innerSetMileStoneId(proto.getMileStoneId());
        } else {
            this.innerSetMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelfScore()) {
            this.innerSetSelfScore(proto.getSelfScore());
        } else {
            this.innerSetSelfScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardStatus()) {
            this.innerSetRewardStatus(proto.getRewardStatus());
        } else {
            this.innerSetRewardStatus(MileStoneRewardStatus.forNumber(0));
        }
        if (proto.hasIsOver()) {
            this.innerSetIsOver(proto.getIsOver());
        } else {
            this.innerSetIsOver(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasDissatisfiedReason()) {
            this.innerSetDissatisfiedReason(proto.getDissatisfiedReason());
        } else {
            this.innerSetDissatisfiedReason(MileStoneDissatisfiedReaSon.forNumber(0));
        }
        this.markAll();
        return PlayerMileStoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerMileStoneInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMileStoneId()) {
            this.setMileStoneId(proto.getMileStoneId());
            fieldCnt++;
        }
        if (proto.hasSelfScore()) {
            this.setSelfScore(proto.getSelfScore());
            fieldCnt++;
        }
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardStatus()) {
            this.setRewardStatus(proto.getRewardStatus());
            fieldCnt++;
        }
        if (proto.hasIsOver()) {
            this.setIsOver(proto.getIsOver());
            fieldCnt++;
        }
        if (proto.hasDissatisfiedReason()) {
            this.setDissatisfiedReason(proto.getDissatisfiedReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerMileStoneInfo.Builder getCopySsBuilder() {
        final PlayerMileStoneInfo.Builder builder = PlayerMileStoneInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerMileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getMileStoneId() != 0) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }  else if (builder.hasMileStoneId()) {
            // 清理MileStoneId
            builder.clearMileStoneId();
            fieldCnt++;
        }
        if (this.getSelfScore() != 0L) {
            builder.setSelfScore(this.getSelfScore());
            fieldCnt++;
        }  else if (builder.hasSelfScore()) {
            // 清理SelfScore
            builder.clearSelfScore();
            fieldCnt++;
        }
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.getRewardStatus() != MileStoneRewardStatus.forNumber(0)) {
            builder.setRewardStatus(this.getRewardStatus());
            fieldCnt++;
        }  else if (builder.hasRewardStatus()) {
            // 清理RewardStatus
            builder.clearRewardStatus();
            fieldCnt++;
        }
        if (this.getIsOver()) {
            builder.setIsOver(this.getIsOver());
            fieldCnt++;
        }  else if (builder.hasIsOver()) {
            // 清理IsOver
            builder.clearIsOver();
            fieldCnt++;
        }
        if (this.getDissatisfiedReason() != MileStoneDissatisfiedReaSon.forNumber(0)) {
            builder.setDissatisfiedReason(this.getDissatisfiedReason());
            fieldCnt++;
        }  else if (builder.hasDissatisfiedReason()) {
            // 清理DissatisfiedReason
            builder.clearDissatisfiedReason();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerMileStoneInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_MILESTONEID)) {
            builder.setMileStoneId(this.getMileStoneId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SELFSCORE)) {
            builder.setSelfScore(this.getSelfScore());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDSTATUS)) {
            builder.setRewardStatus(this.getRewardStatus());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ISOVER)) {
            builder.setIsOver(this.getIsOver());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_DISSATISFIEDREASON)) {
            builder.setDissatisfiedReason(this.getDissatisfiedReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerMileStoneInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasMileStoneId()) {
            this.innerSetMileStoneId(proto.getMileStoneId());
        } else {
            this.innerSetMileStoneId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasSelfScore()) {
            this.innerSetSelfScore(proto.getSelfScore());
        } else {
            this.innerSetSelfScore(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardStatus()) {
            this.innerSetRewardStatus(proto.getRewardStatus());
        } else {
            this.innerSetRewardStatus(MileStoneRewardStatus.forNumber(0));
        }
        if (proto.hasIsOver()) {
            this.innerSetIsOver(proto.getIsOver());
        } else {
            this.innerSetIsOver(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasDissatisfiedReason()) {
            this.innerSetDissatisfiedReason(proto.getDissatisfiedReason());
        } else {
            this.innerSetDissatisfiedReason(MileStoneDissatisfiedReaSon.forNumber(0));
        }
        this.markAll();
        return PlayerMileStoneInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerMileStoneInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasMileStoneId()) {
            this.setMileStoneId(proto.getMileStoneId());
            fieldCnt++;
        }
        if (proto.hasSelfScore()) {
            this.setSelfScore(proto.getSelfScore());
            fieldCnt++;
        }
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardStatus()) {
            this.setRewardStatus(proto.getRewardStatus());
            fieldCnt++;
        }
        if (proto.hasIsOver()) {
            this.setIsOver(proto.getIsOver());
            fieldCnt++;
        }
        if (proto.hasDissatisfiedReason()) {
            this.setDissatisfiedReason(proto.getDissatisfiedReason());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerMileStoneInfo.Builder builder = PlayerMileStoneInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.mileStoneId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerMileStoneInfoProp)) {
            return false;
        }
        final PlayerMileStoneInfoProp otherNode = (PlayerMileStoneInfoProp) node;
        if (this.mileStoneId != otherNode.mileStoneId) {
            return false;
        }
        if (this.selfScore != otherNode.selfScore) {
            return false;
        }
        if (this.rewardLevel != otherNode.rewardLevel) {
            return false;
        }
        if (this.rewardStatus != otherNode.rewardStatus) {
            return false;
        }
        if (this.isOver != otherNode.isOver) {
            return false;
        }
        if (this.dissatisfiedReason != otherNode.dissatisfiedReason) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
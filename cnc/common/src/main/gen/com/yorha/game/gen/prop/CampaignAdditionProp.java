package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.CampaignAddition;
import com.yorha.proto.StructPB.CampaignAdditionPB;


/**
 * <AUTHOR> auto gen
 */
public class CampaignAdditionProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_ADDITIONID = 0;
    public static final int FIELD_INDEX_VALUE = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int additionId = Constant.DEFAULT_INT_VALUE;
    private long value = Constant.DEFAULT_LONG_VALUE;

    public CampaignAdditionProp() {
        super(null, 0, FIELD_COUNT);
    }

    public CampaignAdditionProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get additionId
     *
     * @return additionId value
     */
    public int getAdditionId() {
        return this.additionId;
    }

    /**
     * set additionId && set marked
     *
     * @param additionId new value
     * @return current object
     */
    public CampaignAdditionProp setAdditionId(int additionId) {
        if (this.additionId != additionId) {
            this.mark(FIELD_INDEX_ADDITIONID);
            this.additionId = additionId;
        }
        return this;
    }

    /**
     * inner set additionId
     *
     * @param additionId new value
     */
    private void innerSetAdditionId(int additionId) {
        this.additionId = additionId;
    }

    /**
     * get value
     *
     * @return value value
     */
    public long getValue() {
        return this.value;
    }

    /**
     * set value && set marked
     *
     * @param value new value
     * @return current object
     */
    public CampaignAdditionProp setValue(long value) {
        if (this.value != value) {
            this.mark(FIELD_INDEX_VALUE);
            this.value = value;
        }
        return this;
    }

    /**
     * inner set value
     *
     * @param value new value
     */
    private void innerSetValue(long value) {
        this.value = value;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignAdditionPB.Builder getCopyCsBuilder() {
        final CampaignAdditionPB.Builder builder = CampaignAdditionPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(CampaignAdditionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAdditionId() != 0) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }  else if (builder.hasAdditionId()) {
            // 清理AdditionId
            builder.clearAdditionId();
            fieldCnt++;
        }
        if (this.getValue() != 0L) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }  else if (builder.hasValue()) {
            // 清理Value
            builder.clearValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(CampaignAdditionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(CampaignAdditionPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(CampaignAdditionPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAdditionId()) {
            this.innerSetAdditionId(proto.getAdditionId());
        } else {
            this.innerSetAdditionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasValue()) {
            this.innerSetValue(proto.getValue());
        } else {
            this.innerSetValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CampaignAdditionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(CampaignAdditionPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAdditionId()) {
            this.setAdditionId(proto.getAdditionId());
            fieldCnt++;
        }
        if (proto.hasValue()) {
            this.setValue(proto.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignAddition.Builder getCopyDbBuilder() {
        final CampaignAddition.Builder builder = CampaignAddition.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(CampaignAddition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAdditionId() != 0) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }  else if (builder.hasAdditionId()) {
            // 清理AdditionId
            builder.clearAdditionId();
            fieldCnt++;
        }
        if (this.getValue() != 0L) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }  else if (builder.hasValue()) {
            // 清理Value
            builder.clearValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(CampaignAddition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(CampaignAddition proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAdditionId()) {
            this.innerSetAdditionId(proto.getAdditionId());
        } else {
            this.innerSetAdditionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasValue()) {
            this.innerSetValue(proto.getValue());
        } else {
            this.innerSetValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CampaignAdditionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(CampaignAddition proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAdditionId()) {
            this.setAdditionId(proto.getAdditionId());
            fieldCnt++;
        }
        if (proto.hasValue()) {
            this.setValue(proto.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CampaignAddition.Builder getCopySsBuilder() {
        final CampaignAddition.Builder builder = CampaignAddition.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(CampaignAddition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getAdditionId() != 0) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }  else if (builder.hasAdditionId()) {
            // 清理AdditionId
            builder.clearAdditionId();
            fieldCnt++;
        }
        if (this.getValue() != 0L) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }  else if (builder.hasValue()) {
            // 清理Value
            builder.clearValue();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(CampaignAddition.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ADDITIONID)) {
            builder.setAdditionId(this.getAdditionId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_VALUE)) {
            builder.setValue(this.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(CampaignAddition proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasAdditionId()) {
            this.innerSetAdditionId(proto.getAdditionId());
        } else {
            this.innerSetAdditionId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasValue()) {
            this.innerSetValue(proto.getValue());
        } else {
            this.innerSetValue(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return CampaignAdditionProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(CampaignAddition proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasAdditionId()) {
            this.setAdditionId(proto.getAdditionId());
            fieldCnt++;
        }
        if (proto.hasValue()) {
            this.setValue(proto.getValue());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        CampaignAddition.Builder builder = CampaignAddition.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.additionId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof CampaignAdditionProp)) {
            return false;
        }
        final CampaignAdditionProp otherNode = (CampaignAdditionProp) node;
        if (this.additionId != otherNode.additionId) {
            return false;
        }
        if (this.value != otherNode.value) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
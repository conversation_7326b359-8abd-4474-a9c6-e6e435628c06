package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructSkynet.SkynetShop;
import com.yorha.proto.StructSkynet;
import com.yorha.proto.StructSkynetPB.SkynetShopPB;
import com.yorha.proto.StructSkynetPB;


/**
 * <AUTHOR> auto gen
 */
public class SkynetShopProp extends AbstractPropNode {

    public static final int FIELD_INDEX_GOODSHISTORY = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32ShopHistoryMapProp goodsHistory = null;

    public SkynetShopProp() {
        super(null, 0, FIELD_COUNT);
    }

    public SkynetShopProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get goodsHistory
     *
     * @return goodsHistory value
     */
    public Int32ShopHistoryMapProp getGoodsHistory() {
        if (this.goodsHistory == null) {
            this.goodsHistory = new Int32ShopHistoryMapProp(this, FIELD_INDEX_GOODSHISTORY);
        }
        return this.goodsHistory;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putGoodsHistoryV(ShopHistoryProp v) {
        this.getGoodsHistory().put(v.getShopId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ShopHistoryProp addEmptyGoodsHistory(Integer k) {
        return this.getGoodsHistory().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getGoodsHistorySize() {
        if (this.goodsHistory == null) {
            return 0;
        }
        return this.goodsHistory.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isGoodsHistoryEmpty() {
        if (this.goodsHistory == null) {
            return true;
        }
        return this.goodsHistory.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ShopHistoryProp getGoodsHistoryV(Integer k) {
        if (this.goodsHistory == null || !this.goodsHistory.containsKey(k)) {
            return null;
        }
        return this.goodsHistory.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearGoodsHistory() {
        if (this.goodsHistory != null) {
            this.goodsHistory.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeGoodsHistoryV(Integer k) {
        if (this.goodsHistory != null) {
            this.goodsHistory.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetShopPB.Builder getCopyCsBuilder() {
        final SkynetShopPB.Builder builder = SkynetShopPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(SkynetShopPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.goodsHistory != null) {
            StructSkynetPB.Int32ShopHistoryMapPB.Builder tmpBuilder = StructSkynetPB.Int32ShopHistoryMapPB.newBuilder();
            final int tmpFieldCnt = this.goodsHistory.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodsHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodsHistory();
            }
        }  else if (builder.hasGoodsHistory()) {
            // 清理GoodsHistory
            builder.clearGoodsHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(SkynetShopPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            final boolean needClear = !builder.hasGoodsHistory();
            final int tmpFieldCnt = this.goodsHistory.copyChangeToCs(builder.getGoodsHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(SkynetShopPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            final boolean needClear = !builder.hasGoodsHistory();
            final int tmpFieldCnt = this.goodsHistory.copyChangeToAndClearDeleteKeysCs(builder.getGoodsHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(SkynetShopPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeFromCs(proto.getGoodsHistory());
        } else {
            if (this.goodsHistory != null) {
                this.goodsHistory.mergeFromCs(proto.getGoodsHistory());
            }
        }
        this.markAll();
        return SkynetShopProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(SkynetShopPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeChangeFromCs(proto.getGoodsHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetShop.Builder getCopyDbBuilder() {
        final SkynetShop.Builder builder = SkynetShop.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(SkynetShop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.goodsHistory != null) {
            StructSkynet.Int32ShopHistoryMap.Builder tmpBuilder = StructSkynet.Int32ShopHistoryMap.newBuilder();
            final int tmpFieldCnt = this.goodsHistory.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodsHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodsHistory();
            }
        }  else if (builder.hasGoodsHistory()) {
            // 清理GoodsHistory
            builder.clearGoodsHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(SkynetShop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            final boolean needClear = !builder.hasGoodsHistory();
            final int tmpFieldCnt = this.goodsHistory.copyChangeToDb(builder.getGoodsHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(SkynetShop proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeFromDb(proto.getGoodsHistory());
        } else {
            if (this.goodsHistory != null) {
                this.goodsHistory.mergeFromDb(proto.getGoodsHistory());
            }
        }
        this.markAll();
        return SkynetShopProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(SkynetShop proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeChangeFromDb(proto.getGoodsHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public SkynetShop.Builder getCopySsBuilder() {
        final SkynetShop.Builder builder = SkynetShop.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(SkynetShop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.goodsHistory != null) {
            StructSkynet.Int32ShopHistoryMap.Builder tmpBuilder = StructSkynet.Int32ShopHistoryMap.newBuilder();
            final int tmpFieldCnt = this.goodsHistory.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGoodsHistory(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGoodsHistory();
            }
        }  else if (builder.hasGoodsHistory()) {
            // 清理GoodsHistory
            builder.clearGoodsHistory();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(SkynetShop.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            final boolean needClear = !builder.hasGoodsHistory();
            final int tmpFieldCnt = this.goodsHistory.copyChangeToSs(builder.getGoodsHistoryBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGoodsHistory();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(SkynetShop proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeFromSs(proto.getGoodsHistory());
        } else {
            if (this.goodsHistory != null) {
                this.goodsHistory.mergeFromSs(proto.getGoodsHistory());
            }
        }
        this.markAll();
        return SkynetShopProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(SkynetShop proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasGoodsHistory()) {
            this.getGoodsHistory().mergeChangeFromSs(proto.getGoodsHistory());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        SkynetShop.Builder builder = SkynetShop.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_GOODSHISTORY) && this.goodsHistory != null) {
            this.goodsHistory.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.goodsHistory != null) {
            this.goodsHistory.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof SkynetShopProp)) {
            return false;
        }
        final SkynetShopProp otherNode = (SkynetShopProp) node;
        if (!this.getGoodsHistory().compareDataTo(otherNode.getGoodsHistory())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivitySchedule;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.ActivitySchedulePB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityScheduleProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_SCHEDULEID = 0;
    public static final int FIELD_INDEX_ACTIVITY = 1;
    public static final int FIELD_INDEX_LOOPDATA = 2;
    public static final int FIELD_INDEX_LASTLOADTSSEC = 3;

    public static final int FIELD_COUNT = 4;

    private long markBits0 = 0L;

    private int scheduleId = Constant.DEFAULT_INT_VALUE;
    private ActivityProp activity = null;
    private ActScheduleLoopDataProp loopData = null;
    private int lastLoadTsSec = Constant.DEFAULT_INT_VALUE;

    public ActivityScheduleProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityScheduleProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get scheduleId
     *
     * @return scheduleId value
     */
    public int getScheduleId() {
        return this.scheduleId;
    }

    /**
     * set scheduleId && set marked
     *
     * @param scheduleId new value
     * @return current object
     */
    public ActivityScheduleProp setScheduleId(int scheduleId) {
        if (this.scheduleId != scheduleId) {
            this.mark(FIELD_INDEX_SCHEDULEID);
            this.scheduleId = scheduleId;
        }
        return this;
    }

    /**
     * inner set scheduleId
     *
     * @param scheduleId new value
     */
    private void innerSetScheduleId(int scheduleId) {
        this.scheduleId = scheduleId;
    }

    /**
     * get activity
     *
     * @return activity value
     */
    public ActivityProp getActivity() {
        if (this.activity == null) {
            this.activity = new ActivityProp(this, FIELD_INDEX_ACTIVITY);
        }
        return this.activity;
    }

    /**
     * get loopData
     *
     * @return loopData value
     */
    public ActScheduleLoopDataProp getLoopData() {
        if (this.loopData == null) {
            this.loopData = new ActScheduleLoopDataProp(this, FIELD_INDEX_LOOPDATA);
        }
        return this.loopData;
    }

    /**
     * get lastLoadTsSec
     *
     * @return lastLoadTsSec value
     */
    public int getLastLoadTsSec() {
        return this.lastLoadTsSec;
    }

    /**
     * set lastLoadTsSec && set marked
     *
     * @param lastLoadTsSec new value
     * @return current object
     */
    public ActivityScheduleProp setLastLoadTsSec(int lastLoadTsSec) {
        if (this.lastLoadTsSec != lastLoadTsSec) {
            this.mark(FIELD_INDEX_LASTLOADTSSEC);
            this.lastLoadTsSec = lastLoadTsSec;
        }
        return this;
    }

    /**
     * inner set lastLoadTsSec
     *
     * @param lastLoadTsSec new value
     */
    private void innerSetLastLoadTsSec(int lastLoadTsSec) {
        this.lastLoadTsSec = lastLoadTsSec;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySchedulePB.Builder getCopyCsBuilder() {
        final ActivitySchedulePB.Builder builder = ActivitySchedulePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivitySchedulePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScheduleId() != 0) {
            builder.setScheduleId(this.getScheduleId());
            fieldCnt++;
        }  else if (builder.hasScheduleId()) {
            // 清理ScheduleId
            builder.clearScheduleId();
            fieldCnt++;
        }
        if (this.activity != null) {
            StructPB.ActivityPB.Builder tmpBuilder = StructPB.ActivityPB.newBuilder();
            final int tmpFieldCnt = this.activity.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivity(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivity();
            }
        }  else if (builder.hasActivity()) {
            // 清理Activity
            builder.clearActivity();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivitySchedulePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCHEDULEID)) {
            builder.setScheduleId(this.getScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITY) && this.activity != null) {
            final boolean needClear = !builder.hasActivity();
            final int tmpFieldCnt = this.activity.copyChangeToCs(builder.getActivityBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivity();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivitySchedulePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCHEDULEID)) {
            builder.setScheduleId(this.getScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITY) && this.activity != null) {
            final boolean needClear = !builder.hasActivity();
            final int tmpFieldCnt = this.activity.copyChangeToAndClearDeleteKeysCs(builder.getActivityBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivity();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivitySchedulePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScheduleId()) {
            this.innerSetScheduleId(proto.getScheduleId());
        } else {
            this.innerSetScheduleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActivity()) {
            this.getActivity().mergeFromCs(proto.getActivity());
        } else {
            if (this.activity != null) {
                this.activity.mergeFromCs(proto.getActivity());
            }
        }
        this.markAll();
        return ActivityScheduleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivitySchedulePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScheduleId()) {
            this.setScheduleId(proto.getScheduleId());
            fieldCnt++;
        }
        if (proto.hasActivity()) {
            this.getActivity().mergeChangeFromCs(proto.getActivity());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySchedule.Builder getCopyDbBuilder() {
        final ActivitySchedule.Builder builder = ActivitySchedule.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivitySchedule.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScheduleId() != 0) {
            builder.setScheduleId(this.getScheduleId());
            fieldCnt++;
        }  else if (builder.hasScheduleId()) {
            // 清理ScheduleId
            builder.clearScheduleId();
            fieldCnt++;
        }
        if (this.activity != null) {
            Struct.Activity.Builder tmpBuilder = Struct.Activity.newBuilder();
            final int tmpFieldCnt = this.activity.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivity(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivity();
            }
        }  else if (builder.hasActivity()) {
            // 清理Activity
            builder.clearActivity();
            fieldCnt++;
        }
        if (this.loopData != null) {
            Struct.ActScheduleLoopData.Builder tmpBuilder = Struct.ActScheduleLoopData.newBuilder();
            final int tmpFieldCnt = this.loopData.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLoopData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLoopData();
            }
        }  else if (builder.hasLoopData()) {
            // 清理LoopData
            builder.clearLoopData();
            fieldCnt++;
        }
        if (this.getLastLoadTsSec() != 0) {
            builder.setLastLoadTsSec(this.getLastLoadTsSec());
            fieldCnt++;
        }  else if (builder.hasLastLoadTsSec()) {
            // 清理LastLoadTsSec
            builder.clearLastLoadTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivitySchedule.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCHEDULEID)) {
            builder.setScheduleId(this.getScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITY) && this.activity != null) {
            final boolean needClear = !builder.hasActivity();
            final int tmpFieldCnt = this.activity.copyChangeToDb(builder.getActivityBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivity();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOOPDATA) && this.loopData != null) {
            final boolean needClear = !builder.hasLoopData();
            final int tmpFieldCnt = this.loopData.copyChangeToDb(builder.getLoopDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLoopData();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTLOADTSSEC)) {
            builder.setLastLoadTsSec(this.getLastLoadTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivitySchedule proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScheduleId()) {
            this.innerSetScheduleId(proto.getScheduleId());
        } else {
            this.innerSetScheduleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActivity()) {
            this.getActivity().mergeFromDb(proto.getActivity());
        } else {
            if (this.activity != null) {
                this.activity.mergeFromDb(proto.getActivity());
            }
        }
        if (proto.hasLoopData()) {
            this.getLoopData().mergeFromDb(proto.getLoopData());
        } else {
            if (this.loopData != null) {
                this.loopData.mergeFromDb(proto.getLoopData());
            }
        }
        if (proto.hasLastLoadTsSec()) {
            this.innerSetLastLoadTsSec(proto.getLastLoadTsSec());
        } else {
            this.innerSetLastLoadTsSec(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityScheduleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivitySchedule proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScheduleId()) {
            this.setScheduleId(proto.getScheduleId());
            fieldCnt++;
        }
        if (proto.hasActivity()) {
            this.getActivity().mergeChangeFromDb(proto.getActivity());
            fieldCnt++;
        }
        if (proto.hasLoopData()) {
            this.getLoopData().mergeChangeFromDb(proto.getLoopData());
            fieldCnt++;
        }
        if (proto.hasLastLoadTsSec()) {
            this.setLastLoadTsSec(proto.getLastLoadTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivitySchedule.Builder getCopySsBuilder() {
        final ActivitySchedule.Builder builder = ActivitySchedule.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivitySchedule.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getScheduleId() != 0) {
            builder.setScheduleId(this.getScheduleId());
            fieldCnt++;
        }  else if (builder.hasScheduleId()) {
            // 清理ScheduleId
            builder.clearScheduleId();
            fieldCnt++;
        }
        if (this.activity != null) {
            Struct.Activity.Builder tmpBuilder = Struct.Activity.newBuilder();
            final int tmpFieldCnt = this.activity.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActivity(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActivity();
            }
        }  else if (builder.hasActivity()) {
            // 清理Activity
            builder.clearActivity();
            fieldCnt++;
        }
        if (this.loopData != null) {
            Struct.ActScheduleLoopData.Builder tmpBuilder = Struct.ActScheduleLoopData.newBuilder();
            final int tmpFieldCnt = this.loopData.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLoopData(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLoopData();
            }
        }  else if (builder.hasLoopData()) {
            // 清理LoopData
            builder.clearLoopData();
            fieldCnt++;
        }
        if (this.getLastLoadTsSec() != 0) {
            builder.setLastLoadTsSec(this.getLastLoadTsSec());
            fieldCnt++;
        }  else if (builder.hasLastLoadTsSec()) {
            // 清理LastLoadTsSec
            builder.clearLastLoadTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivitySchedule.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_SCHEDULEID)) {
            builder.setScheduleId(this.getScheduleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITY) && this.activity != null) {
            final boolean needClear = !builder.hasActivity();
            final int tmpFieldCnt = this.activity.copyChangeToSs(builder.getActivityBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActivity();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOOPDATA) && this.loopData != null) {
            final boolean needClear = !builder.hasLoopData();
            final int tmpFieldCnt = this.loopData.copyChangeToSs(builder.getLoopDataBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLoopData();
            }
        }
        if (this.hasMark(FIELD_INDEX_LASTLOADTSSEC)) {
            builder.setLastLoadTsSec(this.getLastLoadTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivitySchedule proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasScheduleId()) {
            this.innerSetScheduleId(proto.getScheduleId());
        } else {
            this.innerSetScheduleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasActivity()) {
            this.getActivity().mergeFromSs(proto.getActivity());
        } else {
            if (this.activity != null) {
                this.activity.mergeFromSs(proto.getActivity());
            }
        }
        if (proto.hasLoopData()) {
            this.getLoopData().mergeFromSs(proto.getLoopData());
        } else {
            if (this.loopData != null) {
                this.loopData.mergeFromSs(proto.getLoopData());
            }
        }
        if (proto.hasLastLoadTsSec()) {
            this.innerSetLastLoadTsSec(proto.getLastLoadTsSec());
        } else {
            this.innerSetLastLoadTsSec(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityScheduleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivitySchedule proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasScheduleId()) {
            this.setScheduleId(proto.getScheduleId());
            fieldCnt++;
        }
        if (proto.hasActivity()) {
            this.getActivity().mergeChangeFromSs(proto.getActivity());
            fieldCnt++;
        }
        if (proto.hasLoopData()) {
            this.getLoopData().mergeChangeFromSs(proto.getLoopData());
            fieldCnt++;
        }
        if (proto.hasLastLoadTsSec()) {
            this.setLastLoadTsSec(proto.getLastLoadTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivitySchedule.Builder builder = ActivitySchedule.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ACTIVITY) && this.activity != null) {
            this.activity.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOOPDATA) && this.loopData != null) {
            this.loopData.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.activity != null) {
            this.activity.markAll();
        }
        if (this.loopData != null) {
            this.loopData.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.scheduleId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityScheduleProp)) {
            return false;
        }
        final ActivityScheduleProp otherNode = (ActivityScheduleProp) node;
        if (this.scheduleId != otherNode.scheduleId) {
            return false;
        }
        if (!this.getActivity().compareDataTo(otherNode.getActivity())) {
            return false;
        }
        if (!this.getLoopData().compareDataTo(otherNode.getLoopData())) {
            return false;
        }
        if (this.lastLoadTsSec != otherNode.lastLoadTsSec) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 60;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerContactsModel;
import com.yorha.proto.Struct;
import com.yorha.proto.PlayerPB.PlayerContactsModelPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerContactsModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_CONTACTSBOOK = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int64PlayerContactsMapProp contactsBook = null;

    public PlayerContactsModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerContactsModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get contactsBook
     *
     * @return contactsBook value
     */
    public Int64PlayerContactsMapProp getContactsBook() {
        if (this.contactsBook == null) {
            this.contactsBook = new Int64PlayerContactsMapProp(this, FIELD_INDEX_CONTACTSBOOK);
        }
        return this.contactsBook;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putContactsBookV(PlayerContactsProp v) {
        this.getContactsBook().put(v.getPlayerId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerContactsProp addEmptyContactsBook(Long k) {
        return this.getContactsBook().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getContactsBookSize() {
        if (this.contactsBook == null) {
            return 0;
        }
        return this.contactsBook.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isContactsBookEmpty() {
        if (this.contactsBook == null) {
            return true;
        }
        return this.contactsBook.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerContactsProp getContactsBookV(Long k) {
        if (this.contactsBook == null || !this.contactsBook.containsKey(k)) {
            return null;
        }
        return this.contactsBook.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearContactsBook() {
        if (this.contactsBook != null) {
            this.contactsBook.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeContactsBookV(Long k) {
        if (this.contactsBook != null) {
            this.contactsBook.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerContactsModelPB.Builder getCopyCsBuilder() {
        final PlayerContactsModelPB.Builder builder = PlayerContactsModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerContactsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.contactsBook != null) {
            StructPB.Int64PlayerContactsMapPB.Builder tmpBuilder = StructPB.Int64PlayerContactsMapPB.newBuilder();
            final int tmpFieldCnt = this.contactsBook.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContactsBook(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContactsBook();
            }
        }  else if (builder.hasContactsBook()) {
            // 清理ContactsBook
            builder.clearContactsBook();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerContactsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTACTSBOOK) && this.contactsBook != null) {
            final boolean needClear = !builder.hasContactsBook();
            final int tmpFieldCnt = this.contactsBook.copyChangeToCs(builder.getContactsBookBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContactsBook();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerContactsModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTACTSBOOK) && this.contactsBook != null) {
            final boolean needClear = !builder.hasContactsBook();
            final int tmpFieldCnt = this.contactsBook.copyChangeToAndClearDeleteKeysCs(builder.getContactsBookBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContactsBook();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerContactsModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContactsBook()) {
            this.getContactsBook().mergeFromCs(proto.getContactsBook());
        } else {
            if (this.contactsBook != null) {
                this.contactsBook.mergeFromCs(proto.getContactsBook());
            }
        }
        this.markAll();
        return PlayerContactsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerContactsModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContactsBook()) {
            this.getContactsBook().mergeChangeFromCs(proto.getContactsBook());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerContactsModel.Builder getCopyDbBuilder() {
        final PlayerContactsModel.Builder builder = PlayerContactsModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerContactsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.contactsBook != null) {
            Struct.Int64PlayerContactsMap.Builder tmpBuilder = Struct.Int64PlayerContactsMap.newBuilder();
            final int tmpFieldCnt = this.contactsBook.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContactsBook(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContactsBook();
            }
        }  else if (builder.hasContactsBook()) {
            // 清理ContactsBook
            builder.clearContactsBook();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerContactsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTACTSBOOK) && this.contactsBook != null) {
            final boolean needClear = !builder.hasContactsBook();
            final int tmpFieldCnt = this.contactsBook.copyChangeToDb(builder.getContactsBookBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContactsBook();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerContactsModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContactsBook()) {
            this.getContactsBook().mergeFromDb(proto.getContactsBook());
        } else {
            if (this.contactsBook != null) {
                this.contactsBook.mergeFromDb(proto.getContactsBook());
            }
        }
        this.markAll();
        return PlayerContactsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerContactsModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContactsBook()) {
            this.getContactsBook().mergeChangeFromDb(proto.getContactsBook());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerContactsModel.Builder getCopySsBuilder() {
        final PlayerContactsModel.Builder builder = PlayerContactsModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerContactsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.contactsBook != null) {
            Struct.Int64PlayerContactsMap.Builder tmpBuilder = Struct.Int64PlayerContactsMap.newBuilder();
            final int tmpFieldCnt = this.contactsBook.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setContactsBook(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearContactsBook();
            }
        }  else if (builder.hasContactsBook()) {
            // 清理ContactsBook
            builder.clearContactsBook();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerContactsModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_CONTACTSBOOK) && this.contactsBook != null) {
            final boolean needClear = !builder.hasContactsBook();
            final int tmpFieldCnt = this.contactsBook.copyChangeToSs(builder.getContactsBookBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearContactsBook();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerContactsModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasContactsBook()) {
            this.getContactsBook().mergeFromSs(proto.getContactsBook());
        } else {
            if (this.contactsBook != null) {
                this.contactsBook.mergeFromSs(proto.getContactsBook());
            }
        }
        this.markAll();
        return PlayerContactsModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerContactsModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasContactsBook()) {
            this.getContactsBook().mergeChangeFromSs(proto.getContactsBook());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerContactsModel.Builder builder = PlayerContactsModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_CONTACTSBOOK) && this.contactsBook != null) {
            this.contactsBook.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.contactsBook != null) {
            this.contactsBook.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerContactsModelProp)) {
            return false;
        }
        final PlayerContactsModelProp otherNode = (PlayerContactsModelProp) node;
        if (!this.getContactsBook().compareDataTo(otherNode.getContactsBook())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructClan.ClanFlagInfo;
import com.yorha.proto.StructClanPB.ClanFlagInfoPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanFlagInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_TERRITORYCOLOR = 0;
    public static final int FIELD_INDEX_FLAGCOLOR = 1;
    public static final int FIELD_INDEX_FLAGSHADING = 2;
    public static final int FIELD_INDEX_FLAGSIGN = 3;
    public static final int FIELD_INDEX_NATIONFLAGID = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private int territoryColor = Constant.DEFAULT_INT_VALUE;
    private int flagColor = Constant.DEFAULT_INT_VALUE;
    private int flagShading = Constant.DEFAULT_INT_VALUE;
    private int flagSign = Constant.DEFAULT_INT_VALUE;
    private int nationFlagId = Constant.DEFAULT_INT_VALUE;

    public ClanFlagInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanFlagInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get territoryColor
     *
     * @return territoryColor value
     */
    public int getTerritoryColor() {
        return this.territoryColor;
    }

    /**
     * set territoryColor && set marked
     *
     * @param territoryColor new value
     * @return current object
     */
    public ClanFlagInfoProp setTerritoryColor(int territoryColor) {
        if (this.territoryColor != territoryColor) {
            this.mark(FIELD_INDEX_TERRITORYCOLOR);
            this.territoryColor = territoryColor;
        }
        return this;
    }

    /**
     * inner set territoryColor
     *
     * @param territoryColor new value
     */
    private void innerSetTerritoryColor(int territoryColor) {
        this.territoryColor = territoryColor;
    }

    /**
     * get flagColor
     *
     * @return flagColor value
     */
    public int getFlagColor() {
        return this.flagColor;
    }

    /**
     * set flagColor && set marked
     *
     * @param flagColor new value
     * @return current object
     */
    public ClanFlagInfoProp setFlagColor(int flagColor) {
        if (this.flagColor != flagColor) {
            this.mark(FIELD_INDEX_FLAGCOLOR);
            this.flagColor = flagColor;
        }
        return this;
    }

    /**
     * inner set flagColor
     *
     * @param flagColor new value
     */
    private void innerSetFlagColor(int flagColor) {
        this.flagColor = flagColor;
    }

    /**
     * get flagShading
     *
     * @return flagShading value
     */
    public int getFlagShading() {
        return this.flagShading;
    }

    /**
     * set flagShading && set marked
     *
     * @param flagShading new value
     * @return current object
     */
    public ClanFlagInfoProp setFlagShading(int flagShading) {
        if (this.flagShading != flagShading) {
            this.mark(FIELD_INDEX_FLAGSHADING);
            this.flagShading = flagShading;
        }
        return this;
    }

    /**
     * inner set flagShading
     *
     * @param flagShading new value
     */
    private void innerSetFlagShading(int flagShading) {
        this.flagShading = flagShading;
    }

    /**
     * get flagSign
     *
     * @return flagSign value
     */
    public int getFlagSign() {
        return this.flagSign;
    }

    /**
     * set flagSign && set marked
     *
     * @param flagSign new value
     * @return current object
     */
    public ClanFlagInfoProp setFlagSign(int flagSign) {
        if (this.flagSign != flagSign) {
            this.mark(FIELD_INDEX_FLAGSIGN);
            this.flagSign = flagSign;
        }
        return this;
    }

    /**
     * inner set flagSign
     *
     * @param flagSign new value
     */
    private void innerSetFlagSign(int flagSign) {
        this.flagSign = flagSign;
    }

    /**
     * get nationFlagId
     *
     * @return nationFlagId value
     */
    public int getNationFlagId() {
        return this.nationFlagId;
    }

    /**
     * set nationFlagId && set marked
     *
     * @param nationFlagId new value
     * @return current object
     */
    public ClanFlagInfoProp setNationFlagId(int nationFlagId) {
        if (this.nationFlagId != nationFlagId) {
            this.mark(FIELD_INDEX_NATIONFLAGID);
            this.nationFlagId = nationFlagId;
        }
        return this;
    }

    /**
     * inner set nationFlagId
     *
     * @param nationFlagId new value
     */
    private void innerSetNationFlagId(int nationFlagId) {
        this.nationFlagId = nationFlagId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanFlagInfoPB.Builder getCopyCsBuilder() {
        final ClanFlagInfoPB.Builder builder = ClanFlagInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanFlagInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanFlagInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanFlagInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanFlagInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanFlagInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanFlagInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanFlagInfo.Builder getCopyDbBuilder() {
        final ClanFlagInfo.Builder builder = ClanFlagInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanFlagInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanFlagInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanFlagInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanFlagInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanFlagInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanFlagInfo.Builder getCopySsBuilder() {
        final ClanFlagInfo.Builder builder = ClanFlagInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanFlagInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getTerritoryColor() != 0) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }  else if (builder.hasTerritoryColor()) {
            // 清理TerritoryColor
            builder.clearTerritoryColor();
            fieldCnt++;
        }
        if (this.getFlagColor() != 0) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }  else if (builder.hasFlagColor()) {
            // 清理FlagColor
            builder.clearFlagColor();
            fieldCnt++;
        }
        if (this.getFlagShading() != 0) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }  else if (builder.hasFlagShading()) {
            // 清理FlagShading
            builder.clearFlagShading();
            fieldCnt++;
        }
        if (this.getFlagSign() != 0) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }  else if (builder.hasFlagSign()) {
            // 清理FlagSign
            builder.clearFlagSign();
            fieldCnt++;
        }
        if (this.getNationFlagId() != 0) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }  else if (builder.hasNationFlagId()) {
            // 清理NationFlagId
            builder.clearNationFlagId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanFlagInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_TERRITORYCOLOR)) {
            builder.setTerritoryColor(this.getTerritoryColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGCOLOR)) {
            builder.setFlagColor(this.getFlagColor());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSHADING)) {
            builder.setFlagShading(this.getFlagShading());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_FLAGSIGN)) {
            builder.setFlagSign(this.getFlagSign());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NATIONFLAGID)) {
            builder.setNationFlagId(this.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanFlagInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasTerritoryColor()) {
            this.innerSetTerritoryColor(proto.getTerritoryColor());
        } else {
            this.innerSetTerritoryColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagColor()) {
            this.innerSetFlagColor(proto.getFlagColor());
        } else {
            this.innerSetFlagColor(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagShading()) {
            this.innerSetFlagShading(proto.getFlagShading());
        } else {
            this.innerSetFlagShading(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasFlagSign()) {
            this.innerSetFlagSign(proto.getFlagSign());
        } else {
            this.innerSetFlagSign(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNationFlagId()) {
            this.innerSetNationFlagId(proto.getNationFlagId());
        } else {
            this.innerSetNationFlagId(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ClanFlagInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanFlagInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasTerritoryColor()) {
            this.setTerritoryColor(proto.getTerritoryColor());
            fieldCnt++;
        }
        if (proto.hasFlagColor()) {
            this.setFlagColor(proto.getFlagColor());
            fieldCnt++;
        }
        if (proto.hasFlagShading()) {
            this.setFlagShading(proto.getFlagShading());
            fieldCnt++;
        }
        if (proto.hasFlagSign()) {
            this.setFlagSign(proto.getFlagSign());
            fieldCnt++;
        }
        if (proto.hasNationFlagId()) {
            this.setNationFlagId(proto.getNationFlagId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanFlagInfo.Builder builder = ClanFlagInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanFlagInfoProp)) {
            return false;
        }
        final ClanFlagInfoProp otherNode = (ClanFlagInfoProp) node;
        if (this.territoryColor != otherNode.territoryColor) {
            return false;
        }
        if (this.flagColor != otherNode.flagColor) {
            return false;
        }
        if (this.flagShading != otherNode.flagShading) {
            return false;
        }
        if (this.flagSign != otherNode.flagSign) {
            return false;
        }
        if (this.nationFlagId != otherNode.nationFlagId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
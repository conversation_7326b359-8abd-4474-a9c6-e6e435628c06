package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.TaskExtraInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.TaskExtraInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class TaskExtraInfoProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LOGINDAYSEXTRAINFO = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private LoginDaysExtraInfoProp loginDaysExtraInfo = null;

    public TaskExtraInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public TaskExtraInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get loginDaysExtraInfo
     *
     * @return loginDaysExtraInfo value
     */
    public LoginDaysExtraInfoProp getLoginDaysExtraInfo() {
        if (this.loginDaysExtraInfo == null) {
            this.loginDaysExtraInfo = new LoginDaysExtraInfoProp(this, FIELD_INDEX_LOGINDAYSEXTRAINFO);
        }
        return this.loginDaysExtraInfo;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskExtraInfoPB.Builder getCopyCsBuilder() {
        final TaskExtraInfoPB.Builder builder = TaskExtraInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(TaskExtraInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.loginDaysExtraInfo != null) {
            StructPB.LoginDaysExtraInfoPB.Builder tmpBuilder = StructPB.LoginDaysExtraInfoPB.newBuilder();
            final int tmpFieldCnt = this.loginDaysExtraInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLoginDaysExtraInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLoginDaysExtraInfo();
            }
        }  else if (builder.hasLoginDaysExtraInfo()) {
            // 清理LoginDaysExtraInfo
            builder.clearLoginDaysExtraInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(TaskExtraInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGINDAYSEXTRAINFO) && this.loginDaysExtraInfo != null) {
            final boolean needClear = !builder.hasLoginDaysExtraInfo();
            final int tmpFieldCnt = this.loginDaysExtraInfo.copyChangeToCs(builder.getLoginDaysExtraInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLoginDaysExtraInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(TaskExtraInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGINDAYSEXTRAINFO) && this.loginDaysExtraInfo != null) {
            final boolean needClear = !builder.hasLoginDaysExtraInfo();
            final int tmpFieldCnt = this.loginDaysExtraInfo.copyChangeToAndClearDeleteKeysCs(builder.getLoginDaysExtraInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLoginDaysExtraInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(TaskExtraInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLoginDaysExtraInfo()) {
            this.getLoginDaysExtraInfo().mergeFromCs(proto.getLoginDaysExtraInfo());
        } else {
            if (this.loginDaysExtraInfo != null) {
                this.loginDaysExtraInfo.mergeFromCs(proto.getLoginDaysExtraInfo());
            }
        }
        this.markAll();
        return TaskExtraInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(TaskExtraInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLoginDaysExtraInfo()) {
            this.getLoginDaysExtraInfo().mergeChangeFromCs(proto.getLoginDaysExtraInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskExtraInfo.Builder getCopyDbBuilder() {
        final TaskExtraInfo.Builder builder = TaskExtraInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(TaskExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.loginDaysExtraInfo != null) {
            Struct.LoginDaysExtraInfo.Builder tmpBuilder = Struct.LoginDaysExtraInfo.newBuilder();
            final int tmpFieldCnt = this.loginDaysExtraInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLoginDaysExtraInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLoginDaysExtraInfo();
            }
        }  else if (builder.hasLoginDaysExtraInfo()) {
            // 清理LoginDaysExtraInfo
            builder.clearLoginDaysExtraInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(TaskExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGINDAYSEXTRAINFO) && this.loginDaysExtraInfo != null) {
            final boolean needClear = !builder.hasLoginDaysExtraInfo();
            final int tmpFieldCnt = this.loginDaysExtraInfo.copyChangeToDb(builder.getLoginDaysExtraInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLoginDaysExtraInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(TaskExtraInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLoginDaysExtraInfo()) {
            this.getLoginDaysExtraInfo().mergeFromDb(proto.getLoginDaysExtraInfo());
        } else {
            if (this.loginDaysExtraInfo != null) {
                this.loginDaysExtraInfo.mergeFromDb(proto.getLoginDaysExtraInfo());
            }
        }
        this.markAll();
        return TaskExtraInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(TaskExtraInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLoginDaysExtraInfo()) {
            this.getLoginDaysExtraInfo().mergeChangeFromDb(proto.getLoginDaysExtraInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public TaskExtraInfo.Builder getCopySsBuilder() {
        final TaskExtraInfo.Builder builder = TaskExtraInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(TaskExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.loginDaysExtraInfo != null) {
            Struct.LoginDaysExtraInfo.Builder tmpBuilder = Struct.LoginDaysExtraInfo.newBuilder();
            final int tmpFieldCnt = this.loginDaysExtraInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLoginDaysExtraInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLoginDaysExtraInfo();
            }
        }  else if (builder.hasLoginDaysExtraInfo()) {
            // 清理LoginDaysExtraInfo
            builder.clearLoginDaysExtraInfo();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(TaskExtraInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGINDAYSEXTRAINFO) && this.loginDaysExtraInfo != null) {
            final boolean needClear = !builder.hasLoginDaysExtraInfo();
            final int tmpFieldCnt = this.loginDaysExtraInfo.copyChangeToSs(builder.getLoginDaysExtraInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLoginDaysExtraInfo();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(TaskExtraInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLoginDaysExtraInfo()) {
            this.getLoginDaysExtraInfo().mergeFromSs(proto.getLoginDaysExtraInfo());
        } else {
            if (this.loginDaysExtraInfo != null) {
                this.loginDaysExtraInfo.mergeFromSs(proto.getLoginDaysExtraInfo());
            }
        }
        this.markAll();
        return TaskExtraInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(TaskExtraInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLoginDaysExtraInfo()) {
            this.getLoginDaysExtraInfo().mergeChangeFromSs(proto.getLoginDaysExtraInfo());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        TaskExtraInfo.Builder builder = TaskExtraInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LOGINDAYSEXTRAINFO) && this.loginDaysExtraInfo != null) {
            this.loginDaysExtraInfo.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.loginDaysExtraInfo != null) {
            this.loginDaysExtraInfo.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof TaskExtraInfoProp)) {
            return false;
        }
        final TaskExtraInfoProp otherNode = (TaskExtraInfoProp) node;
        if (!this.getLoginDaysExtraInfo().compareDataTo(otherNode.getLoginDaysExtraInfo())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.ActivityFestivalUnit;
import com.yorha.proto.StructPB.ActivityFestivalUnitPB;


/**
 * <AUTHOR> auto gen
 */
public class ActivityFestivalUnitProp extends AbstractPropNode {

    public static final int FIELD_INDEX_DROPITEMNUM = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private int dropItemNum = Constant.DEFAULT_INT_VALUE;

    public ActivityFestivalUnitProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ActivityFestivalUnitProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get dropItemNum
     *
     * @return dropItemNum value
     */
    public int getDropItemNum() {
        return this.dropItemNum;
    }

    /**
     * set dropItemNum && set marked
     *
     * @param dropItemNum new value
     * @return current object
     */
    public ActivityFestivalUnitProp setDropItemNum(int dropItemNum) {
        if (this.dropItemNum != dropItemNum) {
            this.mark(FIELD_INDEX_DROPITEMNUM);
            this.dropItemNum = dropItemNum;
        }
        return this;
    }

    /**
     * inner set dropItemNum
     *
     * @param dropItemNum new value
     */
    private void innerSetDropItemNum(int dropItemNum) {
        this.dropItemNum = dropItemNum;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalUnitPB.Builder getCopyCsBuilder() {
        final ActivityFestivalUnitPB.Builder builder = ActivityFestivalUnitPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ActivityFestivalUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDropItemNum() != 0) {
            builder.setDropItemNum(this.getDropItemNum());
            fieldCnt++;
        }  else if (builder.hasDropItemNum()) {
            // 清理DropItemNum
            builder.clearDropItemNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ActivityFestivalUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPITEMNUM)) {
            builder.setDropItemNum(this.getDropItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ActivityFestivalUnitPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPITEMNUM)) {
            builder.setDropItemNum(this.getDropItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ActivityFestivalUnitPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDropItemNum()) {
            this.innerSetDropItemNum(proto.getDropItemNum());
        } else {
            this.innerSetDropItemNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityFestivalUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ActivityFestivalUnitPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDropItemNum()) {
            this.setDropItemNum(proto.getDropItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalUnit.Builder getCopyDbBuilder() {
        final ActivityFestivalUnit.Builder builder = ActivityFestivalUnit.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ActivityFestivalUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDropItemNum() != 0) {
            builder.setDropItemNum(this.getDropItemNum());
            fieldCnt++;
        }  else if (builder.hasDropItemNum()) {
            // 清理DropItemNum
            builder.clearDropItemNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ActivityFestivalUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPITEMNUM)) {
            builder.setDropItemNum(this.getDropItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ActivityFestivalUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDropItemNum()) {
            this.innerSetDropItemNum(proto.getDropItemNum());
        } else {
            this.innerSetDropItemNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityFestivalUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ActivityFestivalUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDropItemNum()) {
            this.setDropItemNum(proto.getDropItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ActivityFestivalUnit.Builder getCopySsBuilder() {
        final ActivityFestivalUnit.Builder builder = ActivityFestivalUnit.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ActivityFestivalUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getDropItemNum() != 0) {
            builder.setDropItemNum(this.getDropItemNum());
            fieldCnt++;
        }  else if (builder.hasDropItemNum()) {
            // 清理DropItemNum
            builder.clearDropItemNum();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ActivityFestivalUnit.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_DROPITEMNUM)) {
            builder.setDropItemNum(this.getDropItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ActivityFestivalUnit proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasDropItemNum()) {
            this.innerSetDropItemNum(proto.getDropItemNum());
        } else {
            this.innerSetDropItemNum(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return ActivityFestivalUnitProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ActivityFestivalUnit proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasDropItemNum()) {
            this.setDropItemNum(proto.getDropItemNum());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ActivityFestivalUnit.Builder builder = ActivityFestivalUnit.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ActivityFestivalUnitProp)) {
            return false;
        }
        final ActivityFestivalUnitProp otherNode = (ActivityFestivalUnitProp) node;
        if (this.dropItemNum != otherNode.dropItemNum) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
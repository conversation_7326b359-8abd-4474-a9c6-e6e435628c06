package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Clan.ClanLogModel;
import com.yorha.proto.Clan;
import com.yorha.proto.ClanPB.ClanLogModelPB;
import com.yorha.proto.ClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanLogModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_LOGBASICINFO = 0;
    public static final int FIELD_INDEX_RECORD = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private ClanLogBasicInfoProp logBasicInfo = null;
    private ClanLogRecordModelProp record = null;

    public ClanLogModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanLogModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get logBasicInfo
     *
     * @return logBasicInfo value
     */
    public ClanLogBasicInfoProp getLogBasicInfo() {
        if (this.logBasicInfo == null) {
            this.logBasicInfo = new ClanLogBasicInfoProp(this, FIELD_INDEX_LOGBASICINFO);
        }
        return this.logBasicInfo;
    }

    /**
     * get record
     *
     * @return record value
     */
    public ClanLogRecordModelProp getRecord() {
        if (this.record == null) {
            this.record = new ClanLogRecordModelProp(this, FIELD_INDEX_RECORD);
        }
        return this.record;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogModelPB.Builder getCopyCsBuilder() {
        final ClanLogModelPB.Builder builder = ClanLogModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanLogModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.record != null) {
            ClanPB.ClanLogRecordModelPB.Builder tmpBuilder = ClanPB.ClanLogRecordModelPB.newBuilder();
            final int tmpFieldCnt = this.record.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecord();
            }
        }  else if (builder.hasRecord()) {
            // 清理Record
            builder.clearRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanLogModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORD) && this.record != null) {
            final boolean needClear = !builder.hasRecord();
            final int tmpFieldCnt = this.record.copyChangeToCs(builder.getRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanLogModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RECORD) && this.record != null) {
            final boolean needClear = !builder.hasRecord();
            final int tmpFieldCnt = this.record.copyChangeToAndClearDeleteKeysCs(builder.getRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanLogModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRecord()) {
            this.getRecord().mergeFromCs(proto.getRecord());
        } else {
            if (this.record != null) {
                this.record.mergeFromCs(proto.getRecord());
            }
        }
        this.markAll();
        return ClanLogModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanLogModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRecord()) {
            this.getRecord().mergeChangeFromCs(proto.getRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogModel.Builder getCopyDbBuilder() {
        final ClanLogModel.Builder builder = ClanLogModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanLogModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logBasicInfo != null) {
            Clan.ClanLogBasicInfo.Builder tmpBuilder = Clan.ClanLogBasicInfo.newBuilder();
            final int tmpFieldCnt = this.logBasicInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogBasicInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogBasicInfo();
            }
        }  else if (builder.hasLogBasicInfo()) {
            // 清理LogBasicInfo
            builder.clearLogBasicInfo();
            fieldCnt++;
        }
        if (this.record != null) {
            Clan.ClanLogRecordModel.Builder tmpBuilder = Clan.ClanLogRecordModel.newBuilder();
            final int tmpFieldCnt = this.record.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecord();
            }
        }  else if (builder.hasRecord()) {
            // 清理Record
            builder.clearRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanLogModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGBASICINFO) && this.logBasicInfo != null) {
            final boolean needClear = !builder.hasLogBasicInfo();
            final int tmpFieldCnt = this.logBasicInfo.copyChangeToDb(builder.getLogBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECORD) && this.record != null) {
            final boolean needClear = !builder.hasRecord();
            final int tmpFieldCnt = this.record.copyChangeToDb(builder.getRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanLogModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogBasicInfo()) {
            this.getLogBasicInfo().mergeFromDb(proto.getLogBasicInfo());
        } else {
            if (this.logBasicInfo != null) {
                this.logBasicInfo.mergeFromDb(proto.getLogBasicInfo());
            }
        }
        if (proto.hasRecord()) {
            this.getRecord().mergeFromDb(proto.getRecord());
        } else {
            if (this.record != null) {
                this.record.mergeFromDb(proto.getRecord());
            }
        }
        this.markAll();
        return ClanLogModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanLogModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogBasicInfo()) {
            this.getLogBasicInfo().mergeChangeFromDb(proto.getLogBasicInfo());
            fieldCnt++;
        }
        if (proto.hasRecord()) {
            this.getRecord().mergeChangeFromDb(proto.getRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanLogModel.Builder getCopySsBuilder() {
        final ClanLogModel.Builder builder = ClanLogModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanLogModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.logBasicInfo != null) {
            Clan.ClanLogBasicInfo.Builder tmpBuilder = Clan.ClanLogBasicInfo.newBuilder();
            final int tmpFieldCnt = this.logBasicInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogBasicInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogBasicInfo();
            }
        }  else if (builder.hasLogBasicInfo()) {
            // 清理LogBasicInfo
            builder.clearLogBasicInfo();
            fieldCnt++;
        }
        if (this.record != null) {
            Clan.ClanLogRecordModel.Builder tmpBuilder = Clan.ClanLogRecordModel.newBuilder();
            final int tmpFieldCnt = this.record.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecord();
            }
        }  else if (builder.hasRecord()) {
            // 清理Record
            builder.clearRecord();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanLogModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_LOGBASICINFO) && this.logBasicInfo != null) {
            final boolean needClear = !builder.hasLogBasicInfo();
            final int tmpFieldCnt = this.logBasicInfo.copyChangeToSs(builder.getLogBasicInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogBasicInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECORD) && this.record != null) {
            final boolean needClear = !builder.hasRecord();
            final int tmpFieldCnt = this.record.copyChangeToSs(builder.getRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecord();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanLogModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasLogBasicInfo()) {
            this.getLogBasicInfo().mergeFromSs(proto.getLogBasicInfo());
        } else {
            if (this.logBasicInfo != null) {
                this.logBasicInfo.mergeFromSs(proto.getLogBasicInfo());
            }
        }
        if (proto.hasRecord()) {
            this.getRecord().mergeFromSs(proto.getRecord());
        } else {
            if (this.record != null) {
                this.record.mergeFromSs(proto.getRecord());
            }
        }
        this.markAll();
        return ClanLogModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanLogModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasLogBasicInfo()) {
            this.getLogBasicInfo().mergeChangeFromSs(proto.getLogBasicInfo());
            fieldCnt++;
        }
        if (proto.hasRecord()) {
            this.getRecord().mergeChangeFromSs(proto.getRecord());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanLogModel.Builder builder = ClanLogModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_LOGBASICINFO) && this.logBasicInfo != null) {
            this.logBasicInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RECORD) && this.record != null) {
            this.record.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.logBasicInfo != null) {
            this.logBasicInfo.markAll();
        }
        if (this.record != null) {
            this.record.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanLogModelProp)) {
            return false;
        }
        final ClanLogModelProp otherNode = (ClanLogModelProp) node;
        if (!this.getLogBasicInfo().compareDataTo(otherNode.getLogBasicInfo())) {
            return false;
        }
        if (!this.getRecord().compareDataTo(otherNode.getRecord())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.PeaceShield;
import com.yorha.proto.StructBattlePB.PeaceShieldPB;


/**
 * <AUTHOR> auto gen
 */
public class PeaceShieldProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ISSHIELDON = 0;
    public static final int FIELD_INDEX_SHIELDENDTIME = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private boolean isShieldOn = Constant.DEFAULT_BOOLEAN_VALUE;
    private long shieldEndTime = Constant.DEFAULT_LONG_VALUE;

    public PeaceShieldProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PeaceShieldProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get isShieldOn
     *
     * @return isShieldOn value
     */
    public boolean getIsShieldOn() {
        return this.isShieldOn;
    }

    /**
     * set isShieldOn && set marked
     *
     * @param isShieldOn new value
     * @return current object
     */
    public PeaceShieldProp setIsShieldOn(boolean isShieldOn) {
        if (this.isShieldOn != isShieldOn) {
            this.mark(FIELD_INDEX_ISSHIELDON);
            this.isShieldOn = isShieldOn;
        }
        return this;
    }

    /**
     * inner set isShieldOn
     *
     * @param isShieldOn new value
     */
    private void innerSetIsShieldOn(boolean isShieldOn) {
        this.isShieldOn = isShieldOn;
    }

    /**
     * get shieldEndTime
     *
     * @return shieldEndTime value
     */
    public long getShieldEndTime() {
        return this.shieldEndTime;
    }

    /**
     * set shieldEndTime && set marked
     *
     * @param shieldEndTime new value
     * @return current object
     */
    public PeaceShieldProp setShieldEndTime(long shieldEndTime) {
        if (this.shieldEndTime != shieldEndTime) {
            this.mark(FIELD_INDEX_SHIELDENDTIME);
            this.shieldEndTime = shieldEndTime;
        }
        return this;
    }

    /**
     * inner set shieldEndTime
     *
     * @param shieldEndTime new value
     */
    private void innerSetShieldEndTime(long shieldEndTime) {
        this.shieldEndTime = shieldEndTime;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PeaceShieldPB.Builder getCopyCsBuilder() {
        final PeaceShieldPB.Builder builder = PeaceShieldPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PeaceShieldPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsShieldOn()) {
            builder.setIsShieldOn(this.getIsShieldOn());
            fieldCnt++;
        }  else if (builder.hasIsShieldOn()) {
            // 清理IsShieldOn
            builder.clearIsShieldOn();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PeaceShieldPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSHIELDON)) {
            builder.setIsShieldOn(this.getIsShieldOn());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PeaceShieldPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSHIELDON)) {
            builder.setIsShieldOn(this.getIsShieldOn());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PeaceShieldPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsShieldOn()) {
            this.innerSetIsShieldOn(proto.getIsShieldOn());
        } else {
            this.innerSetIsShieldOn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        this.markAll();
        return PeaceShieldProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PeaceShieldPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsShieldOn()) {
            this.setIsShieldOn(proto.getIsShieldOn());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PeaceShield.Builder getCopyDbBuilder() {
        final PeaceShield.Builder builder = PeaceShield.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PeaceShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsShieldOn()) {
            builder.setIsShieldOn(this.getIsShieldOn());
            fieldCnt++;
        }  else if (builder.hasIsShieldOn()) {
            // 清理IsShieldOn
            builder.clearIsShieldOn();
            fieldCnt++;
        }
        if (this.getShieldEndTime() != 0L) {
            builder.setShieldEndTime(this.getShieldEndTime());
            fieldCnt++;
        }  else if (builder.hasShieldEndTime()) {
            // 清理ShieldEndTime
            builder.clearShieldEndTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PeaceShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSHIELDON)) {
            builder.setIsShieldOn(this.getIsShieldOn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHIELDENDTIME)) {
            builder.setShieldEndTime(this.getShieldEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PeaceShield proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsShieldOn()) {
            this.innerSetIsShieldOn(proto.getIsShieldOn());
        } else {
            this.innerSetIsShieldOn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasShieldEndTime()) {
            this.innerSetShieldEndTime(proto.getShieldEndTime());
        } else {
            this.innerSetShieldEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PeaceShieldProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PeaceShield proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsShieldOn()) {
            this.setIsShieldOn(proto.getIsShieldOn());
            fieldCnt++;
        }
        if (proto.hasShieldEndTime()) {
            this.setShieldEndTime(proto.getShieldEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PeaceShield.Builder getCopySsBuilder() {
        final PeaceShield.Builder builder = PeaceShield.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PeaceShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getIsShieldOn()) {
            builder.setIsShieldOn(this.getIsShieldOn());
            fieldCnt++;
        }  else if (builder.hasIsShieldOn()) {
            // 清理IsShieldOn
            builder.clearIsShieldOn();
            fieldCnt++;
        }
        if (this.getShieldEndTime() != 0L) {
            builder.setShieldEndTime(this.getShieldEndTime());
            fieldCnt++;
        }  else if (builder.hasShieldEndTime()) {
            // 清理ShieldEndTime
            builder.clearShieldEndTime();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PeaceShield.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ISSHIELDON)) {
            builder.setIsShieldOn(this.getIsShieldOn());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHIELDENDTIME)) {
            builder.setShieldEndTime(this.getShieldEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PeaceShield proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasIsShieldOn()) {
            this.innerSetIsShieldOn(proto.getIsShieldOn());
        } else {
            this.innerSetIsShieldOn(Constant.DEFAULT_BOOLEAN_VALUE);
        }
        if (proto.hasShieldEndTime()) {
            this.innerSetShieldEndTime(proto.getShieldEndTime());
        } else {
            this.innerSetShieldEndTime(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PeaceShieldProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PeaceShield proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasIsShieldOn()) {
            this.setIsShieldOn(proto.getIsShieldOn());
            fieldCnt++;
        }
        if (proto.hasShieldEndTime()) {
            this.setShieldEndTime(proto.getShieldEndTime());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PeaceShield.Builder builder = PeaceShield.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PeaceShieldProp)) {
            return false;
        }
        final PeaceShieldProp otherNode = (PeaceShieldProp) node;
        if (this.isShieldOn != otherNode.isShieldOn) {
            return false;
        }
        if (this.shieldEndTime != otherNode.shieldEndTime) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
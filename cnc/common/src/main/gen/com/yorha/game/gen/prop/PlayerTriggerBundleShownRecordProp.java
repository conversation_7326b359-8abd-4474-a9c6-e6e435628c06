package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerTriggerBundleShownRecord;
import com.yorha.proto.StructPB.PlayerTriggerBundleShownRecordPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerTriggerBundleShownRecordProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_BUNDLEID = 0;
    public static final int FIELD_INDEX_SHOWNTIMES = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int bundleId = Constant.DEFAULT_INT_VALUE;
    private int shownTimes = Constant.DEFAULT_INT_VALUE;

    public PlayerTriggerBundleShownRecordProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerTriggerBundleShownRecordProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get bundleId
     *
     * @return bundleId value
     */
    public int getBundleId() {
        return this.bundleId;
    }

    /**
     * set bundleId && set marked
     *
     * @param bundleId new value
     * @return current object
     */
    public PlayerTriggerBundleShownRecordProp setBundleId(int bundleId) {
        if (this.bundleId != bundleId) {
            this.mark(FIELD_INDEX_BUNDLEID);
            this.bundleId = bundleId;
        }
        return this;
    }

    /**
     * inner set bundleId
     *
     * @param bundleId new value
     */
    private void innerSetBundleId(int bundleId) {
        this.bundleId = bundleId;
    }

    /**
     * get shownTimes
     *
     * @return shownTimes value
     */
    public int getShownTimes() {
        return this.shownTimes;
    }

    /**
     * set shownTimes && set marked
     *
     * @param shownTimes new value
     * @return current object
     */
    public PlayerTriggerBundleShownRecordProp setShownTimes(int shownTimes) {
        if (this.shownTimes != shownTimes) {
            this.mark(FIELD_INDEX_SHOWNTIMES);
            this.shownTimes = shownTimes;
        }
        return this;
    }

    /**
     * inner set shownTimes
     *
     * @param shownTimes new value
     */
    private void innerSetShownTimes(int shownTimes) {
        this.shownTimes = shownTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleShownRecordPB.Builder getCopyCsBuilder() {
        final PlayerTriggerBundleShownRecordPB.Builder builder = PlayerTriggerBundleShownRecordPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerTriggerBundleShownRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBundleId() != 0) {
            builder.setBundleId(this.getBundleId());
            fieldCnt++;
        }  else if (builder.hasBundleId()) {
            // 清理BundleId
            builder.clearBundleId();
            fieldCnt++;
        }
        if (this.getShownTimes() != 0) {
            builder.setShownTimes(this.getShownTimes());
            fieldCnt++;
        }  else if (builder.hasShownTimes()) {
            // 清理ShownTimes
            builder.clearShownTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerTriggerBundleShownRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLEID)) {
            builder.setBundleId(this.getBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWNTIMES)) {
            builder.setShownTimes(this.getShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerTriggerBundleShownRecordPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLEID)) {
            builder.setBundleId(this.getBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWNTIMES)) {
            builder.setShownTimes(this.getShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerTriggerBundleShownRecordPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBundleId()) {
            this.innerSetBundleId(proto.getBundleId());
        } else {
            this.innerSetBundleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasShownTimes()) {
            this.innerSetShownTimes(proto.getShownTimes());
        } else {
            this.innerSetShownTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleShownRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerTriggerBundleShownRecordPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBundleId()) {
            this.setBundleId(proto.getBundleId());
            fieldCnt++;
        }
        if (proto.hasShownTimes()) {
            this.setShownTimes(proto.getShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleShownRecord.Builder getCopyDbBuilder() {
        final PlayerTriggerBundleShownRecord.Builder builder = PlayerTriggerBundleShownRecord.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerTriggerBundleShownRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBundleId() != 0) {
            builder.setBundleId(this.getBundleId());
            fieldCnt++;
        }  else if (builder.hasBundleId()) {
            // 清理BundleId
            builder.clearBundleId();
            fieldCnt++;
        }
        if (this.getShownTimes() != 0) {
            builder.setShownTimes(this.getShownTimes());
            fieldCnt++;
        }  else if (builder.hasShownTimes()) {
            // 清理ShownTimes
            builder.clearShownTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerTriggerBundleShownRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLEID)) {
            builder.setBundleId(this.getBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWNTIMES)) {
            builder.setShownTimes(this.getShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerTriggerBundleShownRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBundleId()) {
            this.innerSetBundleId(proto.getBundleId());
        } else {
            this.innerSetBundleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasShownTimes()) {
            this.innerSetShownTimes(proto.getShownTimes());
        } else {
            this.innerSetShownTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleShownRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerTriggerBundleShownRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBundleId()) {
            this.setBundleId(proto.getBundleId());
            fieldCnt++;
        }
        if (proto.hasShownTimes()) {
            this.setShownTimes(proto.getShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundleShownRecord.Builder getCopySsBuilder() {
        final PlayerTriggerBundleShownRecord.Builder builder = PlayerTriggerBundleShownRecord.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerTriggerBundleShownRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getBundleId() != 0) {
            builder.setBundleId(this.getBundleId());
            fieldCnt++;
        }  else if (builder.hasBundleId()) {
            // 清理BundleId
            builder.clearBundleId();
            fieldCnt++;
        }
        if (this.getShownTimes() != 0) {
            builder.setShownTimes(this.getShownTimes());
            fieldCnt++;
        }  else if (builder.hasShownTimes()) {
            // 清理ShownTimes
            builder.clearShownTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerTriggerBundleShownRecord.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUNDLEID)) {
            builder.setBundleId(this.getBundleId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWNTIMES)) {
            builder.setShownTimes(this.getShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerTriggerBundleShownRecord proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBundleId()) {
            this.innerSetBundleId(proto.getBundleId());
        } else {
            this.innerSetBundleId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasShownTimes()) {
            this.innerSetShownTimes(proto.getShownTimes());
        } else {
            this.innerSetShownTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleShownRecordProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerTriggerBundleShownRecord proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBundleId()) {
            this.setBundleId(proto.getBundleId());
            fieldCnt++;
        }
        if (proto.hasShownTimes()) {
            this.setShownTimes(proto.getShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerTriggerBundleShownRecord.Builder builder = PlayerTriggerBundleShownRecord.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.bundleId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerTriggerBundleShownRecordProp)) {
            return false;
        }
        final PlayerTriggerBundleShownRecordProp otherNode = (PlayerTriggerBundleShownRecordProp) node;
        if (this.bundleId != otherNode.bundleId) {
            return false;
        }
        if (this.shownTimes != otherNode.shownTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;

import com.yorha.gemini.props.AbstractListNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.proto.StructPB.CurrencyListPB;
import com.yorha.proto.Struct.CurrencyList;
import com.yorha.proto.StructPB.CurrencyPB;
import com.yorha.proto.Struct.Currency;

/**
 * <AUTHOR> auto gen
 */
public class CurrencyListProp extends AbstractListNode<CurrencyProp> {
    /**
     * Create a CurrencyListProp container
     *
     * @param parent parent node
     * @param fieldIndex field index in parent node
     */
    public CurrencyListProp(AbstractPropNode parent, int fieldIndex) {
        super(parent, fieldIndex);
    }

    /**
     * add empty object to CurrencyListProp
     *
     * @return new object
     */
    @Override
    public CurrencyProp addEmptyValue() {
        final CurrencyProp newProp = new CurrencyProp(null, DEFAULT_FIELD_INDEX);
        this.add(newProp);
        return newProp;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CurrencyListPB.Builder getCopyCsBuilder() {
        final CurrencyListPB.Builder builder = CurrencyListPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy data to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyToCs(CurrencyListPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return CurrencyListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final CurrencyProp v : this) {
            final CurrencyPB.Builder itemBuilder = CurrencyPB.newBuilder();
            v.copyToCs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return CurrencyListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder builder for protobuf PB
     * @return changed field count
     */
    public int copyChangeToCs(CurrencyListPB.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToCs(builder);
        return CurrencyListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf PB. clear first, then refresh, add at last.
     *
     * @param proto protobuf PB
     * @return merged field count
     */
    public int mergeFromCs(CurrencyListPB proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (CurrencyPB v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromCs(v);
        }
        this.markAll();
        return CurrencyListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf PB
   *
   * @param proto protobuf PB
   * @return merged field count
   */
    public int mergeChangeFromCs(CurrencyListPB proto) {
        return mergeFromCs(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CurrencyList.Builder getCopyDbBuilder() {
        final CurrencyList.Builder builder = CurrencyList.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToDb(CurrencyList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return CurrencyListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final CurrencyProp v : this) {
            final Currency.Builder itemBuilder = Currency.newBuilder();
            v.copyToDb(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return CurrencyListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToDb(CurrencyList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToDb(builder);
        return CurrencyListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromDb(CurrencyList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (Currency v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromDb(v);
        }
        this.markAll();
        return CurrencyListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromDb(CurrencyList proto) {
        return mergeFromDb(proto);
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public CurrencyList.Builder getCopySsBuilder() {
        final CurrencyList.Builder builder = CurrencyList.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy data to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyToSs(CurrencyList.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        // 为空，直接返回
        if (this.isEmpty()) {
            // 清理builder
            if (builder.getDatasCount() != 0) {
                builder.clear();
                return CurrencyListProp.FIELD_COUNT;
            }
            return 0;
        }
        builder.clear();
        for (final CurrencyProp v : this) {
            final Currency.Builder itemBuilder = Currency.newBuilder();
            v.copyToSs(itemBuilder);
            builder.addDatas(itemBuilder.build());
        }
        return CurrencyListProp.FIELD_COUNT;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder builder for protobuf 
     * @return changed field count
     */
    public int copyChangeToSs(CurrencyList.Builder builder) {
        if(!this.hasAnyMark()) {
            return 0;
        }
        this.copyToSs(builder);
        return CurrencyListProp.FIELD_COUNT;
    }

    /**
     * merge data from protobuf . clear first, then refresh, add at last.
     *
     * @param proto protobuf 
     * @return merged field count
     */
    public int mergeFromSs(CurrencyList proto) {
        if(proto == null) {
            throw new NullPointerException();
        }
        this.clear();
        for (Currency v : proto.getDatasList()) {
           this.addEmptyValue().mergeFromSs(v);
        }
        this.markAll();
        return CurrencyListProp.FIELD_COUNT;
    }

   /**
   * merge data change from protobuf 
   *
   * @param proto protobuf 
   * @return merged field count
   */
    public int mergeChangeFromSs(CurrencyList proto) {
        return mergeFromSs(proto);
    }

    @Override
    public String toString() {
        CurrencyList.Builder builder = CurrencyList.newBuilder();
        // 拷贝到ss结构上
        this.copyToSs(builder);
        return builder.toString();
    }
}
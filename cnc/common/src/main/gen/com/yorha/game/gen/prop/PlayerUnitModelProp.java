package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Player.PlayerUnitModel;
import com.yorha.proto.Player;
import com.yorha.proto.PlayerPB.PlayerUnitModelPB;
import com.yorha.proto.PlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerUnitModelProp extends AbstractPropNode {

    public static final int FIELD_INDEX_PLAYERUNITMAP = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32PlayerUnitMapProp playerUnitMap = null;

    public PlayerUnitModelProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerUnitModelProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get playerUnitMap
     *
     * @return playerUnitMap value
     */
    public Int32PlayerUnitMapProp getPlayerUnitMap() {
        if (this.playerUnitMap == null) {
            this.playerUnitMap = new Int32PlayerUnitMapProp(this, FIELD_INDEX_PLAYERUNITMAP);
        }
        return this.playerUnitMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putPlayerUnitMapV(PlayerUnitProp v) {
        this.getPlayerUnitMap().put(v.getUnitId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PlayerUnitProp addEmptyPlayerUnitMap(Integer k) {
        return this.getPlayerUnitMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getPlayerUnitMapSize() {
        if (this.playerUnitMap == null) {
            return 0;
        }
        return this.playerUnitMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isPlayerUnitMapEmpty() {
        if (this.playerUnitMap == null) {
            return true;
        }
        return this.playerUnitMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PlayerUnitProp getPlayerUnitMapV(Integer k) {
        if (this.playerUnitMap == null || !this.playerUnitMap.containsKey(k)) {
            return null;
        }
        return this.playerUnitMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearPlayerUnitMap() {
        if (this.playerUnitMap != null) {
            this.playerUnitMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removePlayerUnitMapV(Integer k) {
        if (this.playerUnitMap != null) {
            this.playerUnitMap.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerUnitModelPB.Builder getCopyCsBuilder() {
        final PlayerUnitModelPB.Builder builder = PlayerUnitModelPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerUnitModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerUnitMap != null) {
            PlayerPB.Int32PlayerUnitMapPB.Builder tmpBuilder = PlayerPB.Int32PlayerUnitMapPB.newBuilder();
            final int tmpFieldCnt = this.playerUnitMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerUnitMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerUnitMap();
            }
        }  else if (builder.hasPlayerUnitMap()) {
            // 清理PlayerUnitMap
            builder.clearPlayerUnitMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerUnitModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMAP) && this.playerUnitMap != null) {
            final boolean needClear = !builder.hasPlayerUnitMap();
            final int tmpFieldCnt = this.playerUnitMap.copyChangeToCs(builder.getPlayerUnitMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitMap();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerUnitModelPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMAP) && this.playerUnitMap != null) {
            final boolean needClear = !builder.hasPlayerUnitMap();
            final int tmpFieldCnt = this.playerUnitMap.copyChangeToAndClearDeleteKeysCs(builder.getPlayerUnitMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerUnitModelPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerUnitMap()) {
            this.getPlayerUnitMap().mergeFromCs(proto.getPlayerUnitMap());
        } else {
            if (this.playerUnitMap != null) {
                this.playerUnitMap.mergeFromCs(proto.getPlayerUnitMap());
            }
        }
        this.markAll();
        return PlayerUnitModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerUnitModelPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerUnitMap()) {
            this.getPlayerUnitMap().mergeChangeFromCs(proto.getPlayerUnitMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerUnitModel.Builder getCopyDbBuilder() {
        final PlayerUnitModel.Builder builder = PlayerUnitModel.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerUnitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerUnitMap != null) {
            Player.Int32PlayerUnitMap.Builder tmpBuilder = Player.Int32PlayerUnitMap.newBuilder();
            final int tmpFieldCnt = this.playerUnitMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerUnitMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerUnitMap();
            }
        }  else if (builder.hasPlayerUnitMap()) {
            // 清理PlayerUnitMap
            builder.clearPlayerUnitMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerUnitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMAP) && this.playerUnitMap != null) {
            final boolean needClear = !builder.hasPlayerUnitMap();
            final int tmpFieldCnt = this.playerUnitMap.copyChangeToDb(builder.getPlayerUnitMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerUnitModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerUnitMap()) {
            this.getPlayerUnitMap().mergeFromDb(proto.getPlayerUnitMap());
        } else {
            if (this.playerUnitMap != null) {
                this.playerUnitMap.mergeFromDb(proto.getPlayerUnitMap());
            }
        }
        this.markAll();
        return PlayerUnitModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerUnitModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerUnitMap()) {
            this.getPlayerUnitMap().mergeChangeFromDb(proto.getPlayerUnitMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerUnitModel.Builder getCopySsBuilder() {
        final PlayerUnitModel.Builder builder = PlayerUnitModel.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerUnitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.playerUnitMap != null) {
            Player.Int32PlayerUnitMap.Builder tmpBuilder = Player.Int32PlayerUnitMap.newBuilder();
            final int tmpFieldCnt = this.playerUnitMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setPlayerUnitMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearPlayerUnitMap();
            }
        }  else if (builder.hasPlayerUnitMap()) {
            // 清理PlayerUnitMap
            builder.clearPlayerUnitMap();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerUnitModel.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMAP) && this.playerUnitMap != null) {
            final boolean needClear = !builder.hasPlayerUnitMap();
            final int tmpFieldCnt = this.playerUnitMap.copyChangeToSs(builder.getPlayerUnitMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearPlayerUnitMap();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerUnitModel proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasPlayerUnitMap()) {
            this.getPlayerUnitMap().mergeFromSs(proto.getPlayerUnitMap());
        } else {
            if (this.playerUnitMap != null) {
                this.playerUnitMap.mergeFromSs(proto.getPlayerUnitMap());
            }
        }
        this.markAll();
        return PlayerUnitModelProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerUnitModel proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasPlayerUnitMap()) {
            this.getPlayerUnitMap().mergeChangeFromSs(proto.getPlayerUnitMap());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerUnitModel.Builder builder = PlayerUnitModel.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_PLAYERUNITMAP) && this.playerUnitMap != null) {
            this.playerUnitMap.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.playerUnitMap != null) {
            this.playerUnitMap.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerUnitModelProp)) {
            return false;
        }
        final PlayerUnitModelProp otherNode = (PlayerUnitModelProp) node;
        if (!this.getPlayerUnitMap().compareDataTo(otherNode.getPlayerUnitMap())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
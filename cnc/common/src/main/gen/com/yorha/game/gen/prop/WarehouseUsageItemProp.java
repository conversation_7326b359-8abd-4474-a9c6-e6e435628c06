package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.WarehouseUsageItem;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.WarehouseUsageItemPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class WarehouseUsageItemProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_USAGERECORD = 1;
    public static final int FIELD_INDEX_PLAYERID = 2;
    public static final int FIELD_INDEX_CREATETSMS = 3;
    public static final int FIELD_INDEX_CARDHEAD = 4;
    public static final int FIELD_INDEX_USERESOURCEINFOS = 5;

    public static final int FIELD_COUNT = 6;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private ClanRecordProp usageRecord = null;
    private long playerId = Constant.DEFAULT_LONG_VALUE;
    private long createTsMs = Constant.DEFAULT_LONG_VALUE;
    private PlayerCardHeadProp cardHead = null;
    private Int32SingleClanResourceInfoMapProp useResourceInfos = null;

    public WarehouseUsageItemProp() {
        super(null, 0, FIELD_COUNT);
    }

    public WarehouseUsageItemProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public WarehouseUsageItemProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get usageRecord
     *
     * @return usageRecord value
     */
    public ClanRecordProp getUsageRecord() {
        if (this.usageRecord == null) {
            this.usageRecord = new ClanRecordProp(this, FIELD_INDEX_USAGERECORD);
        }
        return this.usageRecord;
    }

    /**
     * get playerId
     *
     * @return playerId value
     */
    public long getPlayerId() {
        return this.playerId;
    }

    /**
     * set playerId && set marked
     *
     * @param playerId new value
     * @return current object
     */
    public WarehouseUsageItemProp setPlayerId(long playerId) {
        if (this.playerId != playerId) {
            this.mark(FIELD_INDEX_PLAYERID);
            this.playerId = playerId;
        }
        return this;
    }

    /**
     * inner set playerId
     *
     * @param playerId new value
     */
    private void innerSetPlayerId(long playerId) {
        this.playerId = playerId;
    }

    /**
     * get createTsMs
     *
     * @return createTsMs value
     */
    public long getCreateTsMs() {
        return this.createTsMs;
    }

    /**
     * set createTsMs && set marked
     *
     * @param createTsMs new value
     * @return current object
     */
    public WarehouseUsageItemProp setCreateTsMs(long createTsMs) {
        if (this.createTsMs != createTsMs) {
            this.mark(FIELD_INDEX_CREATETSMS);
            this.createTsMs = createTsMs;
        }
        return this;
    }

    /**
     * inner set createTsMs
     *
     * @param createTsMs new value
     */
    private void innerSetCreateTsMs(long createTsMs) {
        this.createTsMs = createTsMs;
    }

    /**
     * get cardHead
     *
     * @return cardHead value
     */
    public PlayerCardHeadProp getCardHead() {
        if (this.cardHead == null) {
            this.cardHead = new PlayerCardHeadProp(this, FIELD_INDEX_CARDHEAD);
        }
        return this.cardHead;
    }

    /**
     * get useResourceInfos
     *
     * @return useResourceInfos value
     */
    public Int32SingleClanResourceInfoMapProp getUseResourceInfos() {
        if (this.useResourceInfos == null) {
            this.useResourceInfos = new Int32SingleClanResourceInfoMapProp(this, FIELD_INDEX_USERESOURCEINFOS);
        }
        return this.useResourceInfos;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putUseResourceInfosV(SingleClanResourceInfoProp v) {
        this.getUseResourceInfos().put(v.getType(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public SingleClanResourceInfoProp addEmptyUseResourceInfos(Integer k) {
        return this.getUseResourceInfos().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getUseResourceInfosSize() {
        if (this.useResourceInfos == null) {
            return 0;
        }
        return this.useResourceInfos.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isUseResourceInfosEmpty() {
        if (this.useResourceInfos == null) {
            return true;
        }
        return this.useResourceInfos.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public SingleClanResourceInfoProp getUseResourceInfosV(Integer k) {
        if (this.useResourceInfos == null || !this.useResourceInfos.containsKey(k)) {
            return null;
        }
        return this.useResourceInfos.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearUseResourceInfos() {
        if (this.useResourceInfos != null) {
            this.useResourceInfos.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeUseResourceInfosV(Integer k) {
        if (this.useResourceInfos != null) {
            this.useResourceInfos.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarehouseUsageItemPB.Builder getCopyCsBuilder() {
        final WarehouseUsageItemPB.Builder builder = WarehouseUsageItemPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(WarehouseUsageItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            StructPB.ClanRecordPB.Builder tmpBuilder = StructPB.ClanRecordPB.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.useResourceInfos != null) {
            StructPB.Int32SingleClanResourceInfoMapPB.Builder tmpBuilder = StructPB.Int32SingleClanResourceInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.useResourceInfos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUseResourceInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUseResourceInfos();
            }
        }  else if (builder.hasUseResourceInfos()) {
            // 清理UseResourceInfos
            builder.clearUseResourceInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(WarehouseUsageItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToCs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_USERESOURCEINFOS) && this.useResourceInfos != null) {
            final boolean needClear = !builder.hasUseResourceInfos();
            final int tmpFieldCnt = this.useResourceInfos.copyChangeToCs(builder.getUseResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUseResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(WarehouseUsageItemPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToAndClearDeleteKeysCs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToAndClearDeleteKeysCs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_USERESOURCEINFOS) && this.useResourceInfos != null) {
            final boolean needClear = !builder.hasUseResourceInfos();
            final int tmpFieldCnt = this.useResourceInfos.copyChangeToAndClearDeleteKeysCs(builder.getUseResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUseResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(WarehouseUsageItemPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromCs(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromCs(proto.getUsageRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromCs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromCs(proto.getCardHead());
            }
        }
        if (proto.hasUseResourceInfos()) {
            this.getUseResourceInfos().mergeFromCs(proto.getUseResourceInfos());
        } else {
            if (this.useResourceInfos != null) {
                this.useResourceInfos.mergeFromCs(proto.getUseResourceInfos());
            }
        }
        this.markAll();
        return WarehouseUsageItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(WarehouseUsageItemPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromCs(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromCs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasUseResourceInfos()) {
            this.getUseResourceInfos().mergeChangeFromCs(proto.getUseResourceInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarehouseUsageItem.Builder getCopyDbBuilder() {
        final WarehouseUsageItem.Builder builder = WarehouseUsageItem.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(WarehouseUsageItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            Struct.ClanRecord.Builder tmpBuilder = Struct.ClanRecord.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.useResourceInfos != null) {
            Struct.Int32SingleClanResourceInfoMap.Builder tmpBuilder = Struct.Int32SingleClanResourceInfoMap.newBuilder();
            final int tmpFieldCnt = this.useResourceInfos.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUseResourceInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUseResourceInfos();
            }
        }  else if (builder.hasUseResourceInfos()) {
            // 清理UseResourceInfos
            builder.clearUseResourceInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(WarehouseUsageItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToDb(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToDb(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_USERESOURCEINFOS) && this.useResourceInfos != null) {
            final boolean needClear = !builder.hasUseResourceInfos();
            final int tmpFieldCnt = this.useResourceInfos.copyChangeToDb(builder.getUseResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUseResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(WarehouseUsageItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromDb(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromDb(proto.getUsageRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromDb(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromDb(proto.getCardHead());
            }
        }
        if (proto.hasUseResourceInfos()) {
            this.getUseResourceInfos().mergeFromDb(proto.getUseResourceInfos());
        } else {
            if (this.useResourceInfos != null) {
                this.useResourceInfos.mergeFromDb(proto.getUseResourceInfos());
            }
        }
        this.markAll();
        return WarehouseUsageItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(WarehouseUsageItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromDb(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromDb(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasUseResourceInfos()) {
            this.getUseResourceInfos().mergeChangeFromDb(proto.getUseResourceInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public WarehouseUsageItem.Builder getCopySsBuilder() {
        final WarehouseUsageItem.Builder builder = WarehouseUsageItem.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(WarehouseUsageItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.usageRecord != null) {
            Struct.ClanRecord.Builder tmpBuilder = Struct.ClanRecord.newBuilder();
            final int tmpFieldCnt = this.usageRecord.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUsageRecord(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUsageRecord();
            }
        }  else if (builder.hasUsageRecord()) {
            // 清理UsageRecord
            builder.clearUsageRecord();
            fieldCnt++;
        }
        if (this.getPlayerId() != 0L) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }  else if (builder.hasPlayerId()) {
            // 清理PlayerId
            builder.clearPlayerId();
            fieldCnt++;
        }
        if (this.getCreateTsMs() != 0L) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }  else if (builder.hasCreateTsMs()) {
            // 清理CreateTsMs
            builder.clearCreateTsMs();
            fieldCnt++;
        }
        if (this.cardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.cardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCardHead();
            }
        }  else if (builder.hasCardHead()) {
            // 清理CardHead
            builder.clearCardHead();
            fieldCnt++;
        }
        if (this.useResourceInfos != null) {
            Struct.Int32SingleClanResourceInfoMap.Builder tmpBuilder = Struct.Int32SingleClanResourceInfoMap.newBuilder();
            final int tmpFieldCnt = this.useResourceInfos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setUseResourceInfos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearUseResourceInfos();
            }
        }  else if (builder.hasUseResourceInfos()) {
            // 清理UseResourceInfos
            builder.clearUseResourceInfos();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(WarehouseUsageItem.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            final boolean needClear = !builder.hasUsageRecord();
            final int tmpFieldCnt = this.usageRecord.copyChangeToSs(builder.getUsageRecordBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUsageRecord();
            }
        }
        if (this.hasMark(FIELD_INDEX_PLAYERID)) {
            builder.setPlayerId(this.getPlayerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CREATETSMS)) {
            builder.setCreateTsMs(this.getCreateTsMs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            final boolean needClear = !builder.hasCardHead();
            final int tmpFieldCnt = this.cardHead.copyChangeToSs(builder.getCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_USERESOURCEINFOS) && this.useResourceInfos != null) {
            final boolean needClear = !builder.hasUseResourceInfos();
            final int tmpFieldCnt = this.useResourceInfos.copyChangeToSs(builder.getUseResourceInfosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearUseResourceInfos();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(WarehouseUsageItem proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeFromSs(proto.getUsageRecord());
        } else {
            if (this.usageRecord != null) {
                this.usageRecord.mergeFromSs(proto.getUsageRecord());
            }
        }
        if (proto.hasPlayerId()) {
            this.innerSetPlayerId(proto.getPlayerId());
        } else {
            this.innerSetPlayerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCreateTsMs()) {
            this.innerSetCreateTsMs(proto.getCreateTsMs());
        } else {
            this.innerSetCreateTsMs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeFromSs(proto.getCardHead());
        } else {
            if (this.cardHead != null) {
                this.cardHead.mergeFromSs(proto.getCardHead());
            }
        }
        if (proto.hasUseResourceInfos()) {
            this.getUseResourceInfos().mergeFromSs(proto.getUseResourceInfos());
        } else {
            if (this.useResourceInfos != null) {
                this.useResourceInfos.mergeFromSs(proto.getUseResourceInfos());
            }
        }
        this.markAll();
        return WarehouseUsageItemProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(WarehouseUsageItem proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasUsageRecord()) {
            this.getUsageRecord().mergeChangeFromSs(proto.getUsageRecord());
            fieldCnt++;
        }
        if (proto.hasPlayerId()) {
            this.setPlayerId(proto.getPlayerId());
            fieldCnt++;
        }
        if (proto.hasCreateTsMs()) {
            this.setCreateTsMs(proto.getCreateTsMs());
            fieldCnt++;
        }
        if (proto.hasCardHead()) {
            this.getCardHead().mergeChangeFromSs(proto.getCardHead());
            fieldCnt++;
        }
        if (proto.hasUseResourceInfos()) {
            this.getUseResourceInfos().mergeChangeFromSs(proto.getUseResourceInfos());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        WarehouseUsageItem.Builder builder = WarehouseUsageItem.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_USAGERECORD) && this.usageRecord != null) {
            this.usageRecord.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CARDHEAD) && this.cardHead != null) {
            this.cardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_USERESOURCEINFOS) && this.useResourceInfos != null) {
            this.useResourceInfos.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.usageRecord != null) {
            this.usageRecord.markAll();
        }
        if (this.cardHead != null) {
            this.cardHead.markAll();
        }
        if (this.useResourceInfos != null) {
            this.useResourceInfos.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof WarehouseUsageItemProp)) {
            return false;
        }
        final WarehouseUsageItemProp otherNode = (WarehouseUsageItemProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (!this.getUsageRecord().compareDataTo(otherNode.getUsageRecord())) {
            return false;
        }
        if (this.playerId != otherNode.playerId) {
            return false;
        }
        if (this.createTsMs != otherNode.createTsMs) {
            return false;
        }
        if (!this.getCardHead().compareDataTo(otherNode.getCardHead())) {
            return false;
        }
        if (!this.getUseResourceInfos().compareDataTo(otherNode.getUseResourceInfos())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 58;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
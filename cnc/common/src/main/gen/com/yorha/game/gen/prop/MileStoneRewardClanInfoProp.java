package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.MileStoneRewardClanInfo;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPB.MileStoneRewardClanInfoPB;
import com.yorha.proto.StructPB;


/**
 * <AUTHOR> auto gen
 */
public class MileStoneRewardClanInfoProp extends AbstractContainerElementNode<Integer> {

    public static final int FIELD_INDEX_REWARDLEVEL = 0;
    public static final int FIELD_INDEX_REWARDCLANS = 1;

    public static final int FIELD_COUNT = 2;

    private long markBits0 = 0L;

    private int rewardLevel = Constant.DEFAULT_INT_VALUE;
    private Int64ClanIdDataMapProp rewardClans = null;

    public MileStoneRewardClanInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public MileStoneRewardClanInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rewardLevel
     *
     * @return rewardLevel value
     */
    public int getRewardLevel() {
        return this.rewardLevel;
    }

    /**
     * set rewardLevel && set marked
     *
     * @param rewardLevel new value
     * @return current object
     */
    public MileStoneRewardClanInfoProp setRewardLevel(int rewardLevel) {
        if (this.rewardLevel != rewardLevel) {
            this.mark(FIELD_INDEX_REWARDLEVEL);
            this.rewardLevel = rewardLevel;
        }
        return this;
    }

    /**
     * inner set rewardLevel
     *
     * @param rewardLevel new value
     */
    private void innerSetRewardLevel(int rewardLevel) {
        this.rewardLevel = rewardLevel;
    }

    /**
     * get rewardClans
     *
     * @return rewardClans value
     */
    public Int64ClanIdDataMapProp getRewardClans() {
        if (this.rewardClans == null) {
            this.rewardClans = new Int64ClanIdDataMapProp(this, FIELD_INDEX_REWARDCLANS);
        }
        return this.rewardClans;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRewardClansV(ClanIdDataProp v) {
        this.getRewardClans().put(v.getClanId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanIdDataProp addEmptyRewardClans(Long k) {
        return this.getRewardClans().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRewardClansSize() {
        if (this.rewardClans == null) {
            return 0;
        }
        return this.rewardClans.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRewardClansEmpty() {
        if (this.rewardClans == null) {
            return true;
        }
        return this.rewardClans.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanIdDataProp getRewardClansV(Long k) {
        if (this.rewardClans == null || !this.rewardClans.containsKey(k)) {
            return null;
        }
        return this.rewardClans.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRewardClans() {
        if (this.rewardClans != null) {
            this.rewardClans.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRewardClansV(Long k) {
        if (this.rewardClans != null) {
            this.rewardClans.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRewardClanInfoPB.Builder getCopyCsBuilder() {
        final MileStoneRewardClanInfoPB.Builder builder = MileStoneRewardClanInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(MileStoneRewardClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.rewardClans != null) {
            StructPB.Int64ClanIdDataMapPB.Builder tmpBuilder = StructPB.Int64ClanIdDataMapPB.newBuilder();
            final int tmpFieldCnt = this.rewardClans.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardClans(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardClans();
            }
        }  else if (builder.hasRewardClans()) {
            // 清理RewardClans
            builder.clearRewardClans();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(MileStoneRewardClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANS) && this.rewardClans != null) {
            final boolean needClear = !builder.hasRewardClans();
            final int tmpFieldCnt = this.rewardClans.copyChangeToCs(builder.getRewardClansBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClans();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(MileStoneRewardClanInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANS) && this.rewardClans != null) {
            final boolean needClear = !builder.hasRewardClans();
            final int tmpFieldCnt = this.rewardClans.copyChangeToAndClearDeleteKeysCs(builder.getRewardClansBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClans();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(MileStoneRewardClanInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardClans()) {
            this.getRewardClans().mergeFromCs(proto.getRewardClans());
        } else {
            if (this.rewardClans != null) {
                this.rewardClans.mergeFromCs(proto.getRewardClans());
            }
        }
        this.markAll();
        return MileStoneRewardClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(MileStoneRewardClanInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardClans()) {
            this.getRewardClans().mergeChangeFromCs(proto.getRewardClans());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRewardClanInfo.Builder getCopyDbBuilder() {
        final MileStoneRewardClanInfo.Builder builder = MileStoneRewardClanInfo.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(MileStoneRewardClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.rewardClans != null) {
            Struct.Int64ClanIdDataMap.Builder tmpBuilder = Struct.Int64ClanIdDataMap.newBuilder();
            final int tmpFieldCnt = this.rewardClans.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardClans(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardClans();
            }
        }  else if (builder.hasRewardClans()) {
            // 清理RewardClans
            builder.clearRewardClans();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(MileStoneRewardClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANS) && this.rewardClans != null) {
            final boolean needClear = !builder.hasRewardClans();
            final int tmpFieldCnt = this.rewardClans.copyChangeToDb(builder.getRewardClansBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClans();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(MileStoneRewardClanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardClans()) {
            this.getRewardClans().mergeFromDb(proto.getRewardClans());
        } else {
            if (this.rewardClans != null) {
                this.rewardClans.mergeFromDb(proto.getRewardClans());
            }
        }
        this.markAll();
        return MileStoneRewardClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(MileStoneRewardClanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardClans()) {
            this.getRewardClans().mergeChangeFromDb(proto.getRewardClans());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public MileStoneRewardClanInfo.Builder getCopySsBuilder() {
        final MileStoneRewardClanInfo.Builder builder = MileStoneRewardClanInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(MileStoneRewardClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRewardLevel() != 0) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }  else if (builder.hasRewardLevel()) {
            // 清理RewardLevel
            builder.clearRewardLevel();
            fieldCnt++;
        }
        if (this.rewardClans != null) {
            Struct.Int64ClanIdDataMap.Builder tmpBuilder = Struct.Int64ClanIdDataMap.newBuilder();
            final int tmpFieldCnt = this.rewardClans.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRewardClans(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRewardClans();
            }
        }  else if (builder.hasRewardClans()) {
            // 清理RewardClans
            builder.clearRewardClans();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(MileStoneRewardClanInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_REWARDLEVEL)) {
            builder.setRewardLevel(this.getRewardLevel());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANS) && this.rewardClans != null) {
            final boolean needClear = !builder.hasRewardClans();
            final int tmpFieldCnt = this.rewardClans.copyChangeToSs(builder.getRewardClansBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRewardClans();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(MileStoneRewardClanInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRewardLevel()) {
            this.innerSetRewardLevel(proto.getRewardLevel());
        } else {
            this.innerSetRewardLevel(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasRewardClans()) {
            this.getRewardClans().mergeFromSs(proto.getRewardClans());
        } else {
            if (this.rewardClans != null) {
                this.rewardClans.mergeFromSs(proto.getRewardClans());
            }
        }
        this.markAll();
        return MileStoneRewardClanInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(MileStoneRewardClanInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRewardLevel()) {
            this.setRewardLevel(proto.getRewardLevel());
            fieldCnt++;
        }
        if (proto.hasRewardClans()) {
            this.getRewardClans().mergeChangeFromSs(proto.getRewardClans());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        MileStoneRewardClanInfo.Builder builder = MileStoneRewardClanInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_REWARDCLANS) && this.rewardClans != null) {
            this.rewardClans.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.rewardClans != null) {
            this.rewardClans.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Integer getPrivateKey() {
        return this.rewardLevel;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof MileStoneRewardClanInfoProp)) {
            return false;
        }
        final MileStoneRewardClanInfoProp otherNode = (MileStoneRewardClanInfoProp) node;
        if (this.rewardLevel != otherNode.rewardLevel) {
            return false;
        }
        if (!this.getRewardClans().compareDataTo(otherNode.getRewardClans())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 62;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
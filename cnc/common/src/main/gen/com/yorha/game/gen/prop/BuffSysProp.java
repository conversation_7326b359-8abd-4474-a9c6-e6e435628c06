package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.StructBattle.BuffSys;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructBattlePB.BuffSysPB;
import com.yorha.proto.StructBattlePB;


/**
 * <AUTHOR> auto gen
 */
public class BuffSysProp extends AbstractPropNode {

    public static final int FIELD_INDEX_BUFF = 0;

    public static final int FIELD_COUNT = 1;

    private long markBits0 = 0L;

    private Int32BuffMapProp buff = null;

    public BuffSysProp() {
        super(null, 0, FIELD_COUNT);
    }

    public BuffSysProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get buff
     *
     * @return buff value
     */
    public Int32BuffMapProp getBuff() {
        if (this.buff == null) {
            this.buff = new Int32BuffMapProp(this, FIELD_INDEX_BUFF);
        }
        return this.buff;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putBuffV(BuffProp v) {
        this.getBuff().put(v.getGroupId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public BuffProp addEmptyBuff(Integer k) {
        return this.getBuff().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getBuffSize() {
        if (this.buff == null) {
            return 0;
        }
        return this.buff.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isBuffEmpty() {
        if (this.buff == null) {
            return true;
        }
        return this.buff.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public BuffProp getBuffV(Integer k) {
        if (this.buff == null || !this.buff.containsKey(k)) {
            return null;
        }
        return this.buff.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearBuff() {
        if (this.buff != null) {
            this.buff.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeBuffV(Integer k) {
        if (this.buff != null) {
            this.buff.remove(k);
        }
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSysPB.Builder getCopyCsBuilder() {
        final BuffSysPB.Builder builder = BuffSysPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(BuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.buff != null) {
            StructBattlePB.Int32BuffMapPB.Builder tmpBuilder = StructBattlePB.Int32BuffMapPB.newBuilder();
            final int tmpFieldCnt = this.buff.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(BuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToCs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(BuffSysPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToAndClearDeleteKeysCs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(BuffSysPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromCs(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromCs(proto.getBuff());
            }
        }
        this.markAll();
        return BuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(BuffSysPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromCs(proto.getBuff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSys.Builder getCopyDbBuilder() {
        final BuffSys.Builder builder = BuffSys.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(BuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.buff != null) {
            StructBattle.Int32BuffMap.Builder tmpBuilder = StructBattle.Int32BuffMap.newBuilder();
            final int tmpFieldCnt = this.buff.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(BuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToDb(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(BuffSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromDb(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromDb(proto.getBuff());
            }
        }
        this.markAll();
        return BuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(BuffSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromDb(proto.getBuff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public BuffSys.Builder getCopySsBuilder() {
        final BuffSys.Builder builder = BuffSys.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(BuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.buff != null) {
            StructBattle.Int32BuffMap.Builder tmpBuilder = StructBattle.Int32BuffMap.newBuilder();
            final int tmpFieldCnt = this.buff.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBuff(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBuff();
            }
        }  else if (builder.hasBuff()) {
            // 清理Buff
            builder.clearBuff();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(BuffSys.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            final boolean needClear = !builder.hasBuff();
            final int tmpFieldCnt = this.buff.copyChangeToSs(builder.getBuffBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBuff();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(BuffSys proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasBuff()) {
            this.getBuff().mergeFromSs(proto.getBuff());
        } else {
            if (this.buff != null) {
                this.buff.mergeFromSs(proto.getBuff());
            }
        }
        this.markAll();
        return BuffSysProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(BuffSys proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasBuff()) {
            this.getBuff().mergeChangeFromSs(proto.getBuff());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        BuffSys.Builder builder = BuffSys.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BUFF) && this.buff != null) {
            this.buff.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.buff != null) {
            this.buff.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof BuffSysProp)) {
            return false;
        }
        final BuffSysProp otherNode = (BuffSysProp) node;
        if (!this.getBuff().compareDataTo(otherNode.getBuff())) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 63;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
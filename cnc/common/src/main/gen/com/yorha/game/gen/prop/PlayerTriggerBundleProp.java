package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.Struct.PlayerTriggerBundle;
import com.yorha.proto.StructPB.PlayerTriggerBundlePB;


/**
 * <AUTHOR> auto gen
 */
public class PlayerTriggerBundleProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_TEMPLATEID = 1;
    public static final int FIELD_INDEX_TRIGGERTSSEC = 2;
    public static final int FIELD_INDEX_SHOWTSSEC = 3;
    public static final int FIELD_INDEX_GOODSSHOWNTIMES = 4;

    public static final int FIELD_COUNT = 5;

    private long markBits0 = 0L;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private int templateId = Constant.DEFAULT_INT_VALUE;
    private long triggerTsSec = Constant.DEFAULT_LONG_VALUE;
    private long showTsSec = Constant.DEFAULT_LONG_VALUE;
    private int goodsShownTimes = Constant.DEFAULT_INT_VALUE;

    public PlayerTriggerBundleProp() {
        super(null, 0, FIELD_COUNT);
    }

    public PlayerTriggerBundleProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public PlayerTriggerBundleProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get templateId
     *
     * @return templateId value
     */
    public int getTemplateId() {
        return this.templateId;
    }

    /**
     * set templateId && set marked
     *
     * @param templateId new value
     * @return current object
     */
    public PlayerTriggerBundleProp setTemplateId(int templateId) {
        if (this.templateId != templateId) {
            this.mark(FIELD_INDEX_TEMPLATEID);
            this.templateId = templateId;
        }
        return this;
    }

    /**
     * inner set templateId
     *
     * @param templateId new value
     */
    private void innerSetTemplateId(int templateId) {
        this.templateId = templateId;
    }

    /**
     * get triggerTsSec
     *
     * @return triggerTsSec value
     */
    public long getTriggerTsSec() {
        return this.triggerTsSec;
    }

    /**
     * set triggerTsSec && set marked
     *
     * @param triggerTsSec new value
     * @return current object
     */
    public PlayerTriggerBundleProp setTriggerTsSec(long triggerTsSec) {
        if (this.triggerTsSec != triggerTsSec) {
            this.mark(FIELD_INDEX_TRIGGERTSSEC);
            this.triggerTsSec = triggerTsSec;
        }
        return this;
    }

    /**
     * inner set triggerTsSec
     *
     * @param triggerTsSec new value
     */
    private void innerSetTriggerTsSec(long triggerTsSec) {
        this.triggerTsSec = triggerTsSec;
    }

    /**
     * get showTsSec
     *
     * @return showTsSec value
     */
    public long getShowTsSec() {
        return this.showTsSec;
    }

    /**
     * set showTsSec && set marked
     *
     * @param showTsSec new value
     * @return current object
     */
    public PlayerTriggerBundleProp setShowTsSec(long showTsSec) {
        if (this.showTsSec != showTsSec) {
            this.mark(FIELD_INDEX_SHOWTSSEC);
            this.showTsSec = showTsSec;
        }
        return this;
    }

    /**
     * inner set showTsSec
     *
     * @param showTsSec new value
     */
    private void innerSetShowTsSec(long showTsSec) {
        this.showTsSec = showTsSec;
    }

    /**
     * get goodsShownTimes
     *
     * @return goodsShownTimes value
     */
    public int getGoodsShownTimes() {
        return this.goodsShownTimes;
    }

    /**
     * set goodsShownTimes && set marked
     *
     * @param goodsShownTimes new value
     * @return current object
     */
    public PlayerTriggerBundleProp setGoodsShownTimes(int goodsShownTimes) {
        if (this.goodsShownTimes != goodsShownTimes) {
            this.mark(FIELD_INDEX_GOODSSHOWNTIMES);
            this.goodsShownTimes = goodsShownTimes;
        }
        return this;
    }

    /**
     * inner set goodsShownTimes
     *
     * @param goodsShownTimes new value
     */
    private void innerSetGoodsShownTimes(int goodsShownTimes) {
        this.goodsShownTimes = goodsShownTimes;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundlePB.Builder getCopyCsBuilder() {
        final PlayerTriggerBundlePB.Builder builder = PlayerTriggerBundlePB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(PlayerTriggerBundlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getTriggerTsSec() != 0L) {
            builder.setTriggerTsSec(this.getTriggerTsSec());
            fieldCnt++;
        }  else if (builder.hasTriggerTsSec()) {
            // 清理TriggerTsSec
            builder.clearTriggerTsSec();
            fieldCnt++;
        }
        if (this.getShowTsSec() != 0L) {
            builder.setShowTsSec(this.getShowTsSec());
            fieldCnt++;
        }  else if (builder.hasShowTsSec()) {
            // 清理ShowTsSec
            builder.clearShowTsSec();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(PlayerTriggerBundlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTSSEC)) {
            builder.setTriggerTsSec(this.getTriggerTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWTSSEC)) {
            builder.setShowTsSec(this.getShowTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(PlayerTriggerBundlePB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTSSEC)) {
            builder.setTriggerTsSec(this.getTriggerTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWTSSEC)) {
            builder.setShowTsSec(this.getShowTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(PlayerTriggerBundlePB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerTsSec()) {
            this.innerSetTriggerTsSec(proto.getTriggerTsSec());
        } else {
            this.innerSetTriggerTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowTsSec()) {
            this.innerSetShowTsSec(proto.getShowTsSec());
        } else {
            this.innerSetShowTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(PlayerTriggerBundlePB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasTriggerTsSec()) {
            this.setTriggerTsSec(proto.getTriggerTsSec());
            fieldCnt++;
        }
        if (proto.hasShowTsSec()) {
            this.setShowTsSec(proto.getShowTsSec());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundle.Builder getCopyDbBuilder() {
        final PlayerTriggerBundle.Builder builder = PlayerTriggerBundle.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(PlayerTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getTriggerTsSec() != 0L) {
            builder.setTriggerTsSec(this.getTriggerTsSec());
            fieldCnt++;
        }  else if (builder.hasTriggerTsSec()) {
            // 清理TriggerTsSec
            builder.clearTriggerTsSec();
            fieldCnt++;
        }
        if (this.getShowTsSec() != 0L) {
            builder.setShowTsSec(this.getShowTsSec());
            fieldCnt++;
        }  else if (builder.hasShowTsSec()) {
            // 清理ShowTsSec
            builder.clearShowTsSec();
            fieldCnt++;
        }
        if (this.getGoodsShownTimes() != 0) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }  else if (builder.hasGoodsShownTimes()) {
            // 清理GoodsShownTimes
            builder.clearGoodsShownTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(PlayerTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTSSEC)) {
            builder.setTriggerTsSec(this.getTriggerTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWTSSEC)) {
            builder.setShowTsSec(this.getShowTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSSHOWNTIMES)) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(PlayerTriggerBundle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerTsSec()) {
            this.innerSetTriggerTsSec(proto.getTriggerTsSec());
        } else {
            this.innerSetTriggerTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowTsSec()) {
            this.innerSetShowTsSec(proto.getShowTsSec());
        } else {
            this.innerSetShowTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGoodsShownTimes()) {
            this.innerSetGoodsShownTimes(proto.getGoodsShownTimes());
        } else {
            this.innerSetGoodsShownTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(PlayerTriggerBundle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasTriggerTsSec()) {
            this.setTriggerTsSec(proto.getTriggerTsSec());
            fieldCnt++;
        }
        if (proto.hasShowTsSec()) {
            this.setShowTsSec(proto.getShowTsSec());
            fieldCnt++;
        }
        if (proto.hasGoodsShownTimes()) {
            this.setGoodsShownTimes(proto.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public PlayerTriggerBundle.Builder getCopySsBuilder() {
        final PlayerTriggerBundle.Builder builder = PlayerTriggerBundle.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(PlayerTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getTemplateId() != 0) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }  else if (builder.hasTemplateId()) {
            // 清理TemplateId
            builder.clearTemplateId();
            fieldCnt++;
        }
        if (this.getTriggerTsSec() != 0L) {
            builder.setTriggerTsSec(this.getTriggerTsSec());
            fieldCnt++;
        }  else if (builder.hasTriggerTsSec()) {
            // 清理TriggerTsSec
            builder.clearTriggerTsSec();
            fieldCnt++;
        }
        if (this.getShowTsSec() != 0L) {
            builder.setShowTsSec(this.getShowTsSec());
            fieldCnt++;
        }  else if (builder.hasShowTsSec()) {
            // 清理ShowTsSec
            builder.clearShowTsSec();
            fieldCnt++;
        }
        if (this.getGoodsShownTimes() != 0) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }  else if (builder.hasGoodsShownTimes()) {
            // 清理GoodsShownTimes
            builder.clearGoodsShownTimes();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(PlayerTriggerBundle.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TEMPLATEID)) {
            builder.setTemplateId(this.getTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TRIGGERTSSEC)) {
            builder.setTriggerTsSec(this.getTriggerTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_SHOWTSSEC)) {
            builder.setShowTsSec(this.getShowTsSec());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_GOODSSHOWNTIMES)) {
            builder.setGoodsShownTimes(this.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(PlayerTriggerBundle proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTemplateId()) {
            this.innerSetTemplateId(proto.getTemplateId());
        } else {
            this.innerSetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasTriggerTsSec()) {
            this.innerSetTriggerTsSec(proto.getTriggerTsSec());
        } else {
            this.innerSetTriggerTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasShowTsSec()) {
            this.innerSetShowTsSec(proto.getShowTsSec());
        } else {
            this.innerSetShowTsSec(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasGoodsShownTimes()) {
            this.innerSetGoodsShownTimes(proto.getGoodsShownTimes());
        } else {
            this.innerSetGoodsShownTimes(Constant.DEFAULT_INT_VALUE);
        }
        this.markAll();
        return PlayerTriggerBundleProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(PlayerTriggerBundle proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasTemplateId()) {
            this.setTemplateId(proto.getTemplateId());
            fieldCnt++;
        }
        if (proto.hasTriggerTsSec()) {
            this.setTriggerTsSec(proto.getTriggerTsSec());
            fieldCnt++;
        }
        if (proto.hasShowTsSec()) {
            this.setShowTsSec(proto.getShowTsSec());
            fieldCnt++;
        }
        if (proto.hasGoodsShownTimes()) {
            this.setGoodsShownTimes(proto.getGoodsShownTimes());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        PlayerTriggerBundle.Builder builder = PlayerTriggerBundle.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.id;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof PlayerTriggerBundleProp)) {
            return false;
        }
        final PlayerTriggerBundleProp otherNode = (PlayerTriggerBundleProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.templateId != otherNode.templateId) {
            return false;
        }
        if (this.triggerTsSec != otherNode.triggerTsSec) {
            return false;
        }
        if (this.showTsSec != otherNode.showTsSec) {
            return false;
        }
        if (this.goodsShownTimes != otherNode.goodsShownTimes) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 59;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
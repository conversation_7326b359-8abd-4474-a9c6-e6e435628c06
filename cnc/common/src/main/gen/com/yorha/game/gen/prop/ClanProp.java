package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.gemini.props.PropertyChangeListener;
import com.yorha.proto.Clan.ClanEntity;
import com.yorha.proto.Clan;
import com.yorha.proto.Struct;
import com.yorha.proto.StructBattle;
import com.yorha.proto.StructClan;
import com.yorha.proto.ClanPB.ClanEntityPB;
import com.yorha.proto.ClanPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructBattlePB;
import com.yorha.proto.StructClanPB;


/**
 * <AUTHOR> auto gen
 */
public class ClanProp extends AbstractPropNode {

    public static final int FIELD_INDEX_ID = 0;
    public static final int FIELD_INDEX_OWNERID = 1;
    public static final int FIELD_INDEX_BASE = 2;
    public static final int FIELD_INDEX_CREATETIME = 3;
    public static final int FIELD_INDEX_NUM = 4;
    public static final int FIELD_INDEX_NUMMAX = 5;
    public static final int FIELD_INDEX_COMBAT = 6;
    public static final int FIELD_INDEX_MEMBER = 7;
    public static final int FIELD_INDEX_APPLYS = 8;
    public static final int FIELD_INDEX_VERSION = 9;
    public static final int FIELD_INDEX_ADDITIONSYS = 10;
    public static final int FIELD_INDEX_DEVBUFFSYS = 11;
    public static final int FIELD_INDEX_RESOURCES = 12;
    public static final int FIELD_INDEX_TERRITORYINFO = 13;
    public static final int FIELD_INDEX_HELPS = 14;
    public static final int FIELD_INDEX_GIFTMODEL = 15;
    public static final int FIELD_INDEX_CLANPOSMARKMAP = 16;
    public static final int FIELD_INDEX_STAFFMODEL = 17;
    public static final int FIELD_INDEX_STAGEMODEL = 18;
    public static final int FIELD_INDEX_LOGMODEL = 19;
    public static final int FIELD_INDEX_REDDOTMODEL = 20;
    public static final int FIELD_INDEX_TECHMODEL = 21;
    public static final int FIELD_INDEX_REFRESHMODEL = 22;
    public static final int FIELD_INDEX_CLANMEMBERHISTORYMODEL = 23;
    public static final int FIELD_INDEX_STOREMODEL = 24;
    public static final int FIELD_INDEX_RESETQUIETPERIOD = 25;
    public static final int FIELD_INDEX_MISCMODEL = 26;
    public static final int FIELD_INDEX_INVITEMODEL = 27;
    public static final int FIELD_INDEX_ACTMODEL = 28;

    public static final int FIELD_COUNT = 29;

    private long markBits0 = 0L;
    private PropertyChangeListener listener;

    private long id = Constant.DEFAULT_LONG_VALUE;
    private long ownerId = Constant.DEFAULT_LONG_VALUE;
    private ClanBaseInfoProp base = null;
    private long createTime = Constant.DEFAULT_LONG_VALUE;
    private int num = Constant.DEFAULT_INT_VALUE;
    private int numMax = Constant.DEFAULT_INT_VALUE;
    private long combat = Constant.DEFAULT_LONG_VALUE;
    private Int64ClanMemberMapProp member = null;
    private Int64ClanMemberMapProp applys = null;
    private long version = Constant.DEFAULT_LONG_VALUE;
    private AdditionSysProp additionSys = null;
    private DevBuffSysProp devBuffSys = null;
    private ClanResourcesProp resources = null;
    private TerritoryInfoProp territoryInfo = null;
    private Int64ClanHelpItemMapProp helps = null;
    private ClanGiftModelProp giftModel = null;
    private Int64PositionMarkInfoMapProp clanPosMarkMap = null;
    private ClanStaffModelProp staffModel = null;
    private ClanStageModelProp stageModel = null;
    private ClanLogModelProp logModel = null;
    private ClanRedDotModelProp redDotModel = null;
    private ClanTechModelProp techModel = null;
    private ClanRefreshModelProp refreshModel = null;
    private ClanMemberHistoryModelProp ClanMemberHistoryModel = null;
    private ClanStoreModelProp storeModel = null;
    private ClanResetQuietPeriodProp resetQuietPeriod = null;
    private ClanMiscModelProp miscModel = null;
    private ClanInviteModelProp inviteModel = null;
    private ClanActivityModelProp actModel = null;

    public ClanProp() {
        super(null, 0, FIELD_COUNT);
    }

    public ClanProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get id
     *
     * @return id value
     */
    public long getId() {
        return this.id;
    }

    /**
     * set id && set marked
     *
     * @param id new value
     * @return current object
     */
    public ClanProp setId(long id) {
        if (this.id != id) {
            this.mark(FIELD_INDEX_ID);
            this.id = id;
        }
        return this;
    }

    /**
     * inner set id
     *
     * @param id new value
     */
    private void innerSetId(long id) {
        this.id = id;
    }

    /**
     * get ownerId
     *
     * @return ownerId value
     */
    public long getOwnerId() {
        return this.ownerId;
    }

    /**
     * set ownerId && set marked
     *
     * @param ownerId new value
     * @return current object
     */
    public ClanProp setOwnerId(long ownerId) {
        if (this.ownerId != ownerId) {
            this.mark(FIELD_INDEX_OWNERID);
            this.ownerId = ownerId;
        }
        return this;
    }

    /**
     * inner set ownerId
     *
     * @param ownerId new value
     */
    private void innerSetOwnerId(long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     * get base
     *
     * @return base value
     */
    public ClanBaseInfoProp getBase() {
        if (this.base == null) {
            this.base = new ClanBaseInfoProp(this, FIELD_INDEX_BASE);
        }
        return this.base;
    }

    /**
     * get createTime
     *
     * @return createTime value
     */
    public long getCreateTime() {
        return this.createTime;
    }

    /**
     * set createTime && set marked
     *
     * @param createTime new value
     * @return current object
     */
    public ClanProp setCreateTime(long createTime) {
        if (this.createTime != createTime) {
            this.mark(FIELD_INDEX_CREATETIME);
            this.createTime = createTime;
        }
        return this;
    }

    /**
     * inner set createTime
     *
     * @param createTime new value
     */
    private void innerSetCreateTime(long createTime) {
        this.createTime = createTime;
    }

    /**
     * get num
     *
     * @return num value
     */
    public int getNum() {
        return this.num;
    }

    /**
     * set num && set marked
     *
     * @param num new value
     * @return current object
     */
    public ClanProp setNum(int num) {
        if (this.num != num) {
            this.mark(FIELD_INDEX_NUM);
            this.num = num;
        }
        return this;
    }

    /**
     * inner set num
     *
     * @param num new value
     */
    private void innerSetNum(int num) {
        this.num = num;
    }

    /**
     * get numMax
     *
     * @return numMax value
     */
    public int getNumMax() {
        return this.numMax;
    }

    /**
     * set numMax && set marked
     *
     * @param numMax new value
     * @return current object
     */
    public ClanProp setNumMax(int numMax) {
        if (this.numMax != numMax) {
            this.mark(FIELD_INDEX_NUMMAX);
            this.numMax = numMax;
        }
        return this;
    }

    /**
     * inner set numMax
     *
     * @param numMax new value
     */
    private void innerSetNumMax(int numMax) {
        this.numMax = numMax;
    }

    /**
     * get combat
     *
     * @return combat value
     */
    public long getCombat() {
        return this.combat;
    }

    /**
     * set combat && set marked
     *
     * @param combat new value
     * @return current object
     */
    public ClanProp setCombat(long combat) {
        if (this.combat != combat) {
            this.mark(FIELD_INDEX_COMBAT);
            this.combat = combat;
        }
        return this;
    }

    /**
     * inner set combat
     *
     * @param combat new value
     */
    private void innerSetCombat(long combat) {
        this.combat = combat;
    }

    /**
     * get member
     *
     * @return member value
     */
    public Int64ClanMemberMapProp getMember() {
        if (this.member == null) {
            this.member = new Int64ClanMemberMapProp(this, FIELD_INDEX_MEMBER);
        }
        return this.member;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putMemberV(ClanMemberProp v) {
        this.getMember().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanMemberProp addEmptyMember(Long k) {
        return this.getMember().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getMemberSize() {
        if (this.member == null) {
            return 0;
        }
        return this.member.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isMemberEmpty() {
        if (this.member == null) {
            return true;
        }
        return this.member.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanMemberProp getMemberV(Long k) {
        if (this.member == null || !this.member.containsKey(k)) {
            return null;
        }
        return this.member.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearMember() {
        if (this.member != null) {
            this.member.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeMemberV(Long k) {
        if (this.member != null) {
            this.member.remove(k);
        }
    }
    /**
     * get applys
     *
     * @return applys value
     */
    public Int64ClanMemberMapProp getApplys() {
        if (this.applys == null) {
            this.applys = new Int64ClanMemberMapProp(this, FIELD_INDEX_APPLYS);
        }
        return this.applys;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putApplysV(ClanMemberProp v) {
        this.getApplys().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanMemberProp addEmptyApplys(Long k) {
        return this.getApplys().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getApplysSize() {
        if (this.applys == null) {
            return 0;
        }
        return this.applys.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isApplysEmpty() {
        if (this.applys == null) {
            return true;
        }
        return this.applys.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanMemberProp getApplysV(Long k) {
        if (this.applys == null || !this.applys.containsKey(k)) {
            return null;
        }
        return this.applys.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearApplys() {
        if (this.applys != null) {
            this.applys.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeApplysV(Long k) {
        if (this.applys != null) {
            this.applys.remove(k);
        }
    }
    /**
     * get version
     *
     * @return version value
     */
    public long getVersion() {
        return this.version;
    }

    /**
     * set version && set marked
     *
     * @param version new value
     * @return current object
     */
    public ClanProp setVersion(long version) {
        if (this.version != version) {
            this.mark(FIELD_INDEX_VERSION);
            this.version = version;
        }
        return this;
    }

    /**
     * inner set version
     *
     * @param version new value
     */
    private void innerSetVersion(long version) {
        this.version = version;
    }

    /**
     * get additionSys
     *
     * @return additionSys value
     */
    public AdditionSysProp getAdditionSys() {
        if (this.additionSys == null) {
            this.additionSys = new AdditionSysProp(this, FIELD_INDEX_ADDITIONSYS);
        }
        return this.additionSys;
    }

    /**
     * get devBuffSys
     *
     * @return devBuffSys value
     */
    public DevBuffSysProp getDevBuffSys() {
        if (this.devBuffSys == null) {
            this.devBuffSys = new DevBuffSysProp(this, FIELD_INDEX_DEVBUFFSYS);
        }
        return this.devBuffSys;
    }

    /**
     * get resources
     *
     * @return resources value
     */
    public ClanResourcesProp getResources() {
        if (this.resources == null) {
            this.resources = new ClanResourcesProp(this, FIELD_INDEX_RESOURCES);
        }
        return this.resources;
    }

    /**
     * get territoryInfo
     *
     * @return territoryInfo value
     */
    public TerritoryInfoProp getTerritoryInfo() {
        if (this.territoryInfo == null) {
            this.territoryInfo = new TerritoryInfoProp(this, FIELD_INDEX_TERRITORYINFO);
        }
        return this.territoryInfo;
    }

    /**
     * get helps
     *
     * @return helps value
     */
    public Int64ClanHelpItemMapProp getHelps() {
        if (this.helps == null) {
            this.helps = new Int64ClanHelpItemMapProp(this, FIELD_INDEX_HELPS);
        }
        return this.helps;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putHelpsV(ClanHelpItemProp v) {
        this.getHelps().put(v.getId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public ClanHelpItemProp addEmptyHelps(Long k) {
        return this.getHelps().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getHelpsSize() {
        if (this.helps == null) {
            return 0;
        }
        return this.helps.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isHelpsEmpty() {
        if (this.helps == null) {
            return true;
        }
        return this.helps.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public ClanHelpItemProp getHelpsV(Long k) {
        if (this.helps == null || !this.helps.containsKey(k)) {
            return null;
        }
        return this.helps.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearHelps() {
        if (this.helps != null) {
            this.helps.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeHelpsV(Long k) {
        if (this.helps != null) {
            this.helps.remove(k);
        }
    }
    /**
     * get giftModel
     *
     * @return giftModel value
     */
    public ClanGiftModelProp getGiftModel() {
        if (this.giftModel == null) {
            this.giftModel = new ClanGiftModelProp(this, FIELD_INDEX_GIFTMODEL);
        }
        return this.giftModel;
    }

    /**
     * get clanPosMarkMap
     *
     * @return clanPosMarkMap value
     */
    public Int64PositionMarkInfoMapProp getClanPosMarkMap() {
        if (this.clanPosMarkMap == null) {
            this.clanPosMarkMap = new Int64PositionMarkInfoMapProp(this, FIELD_INDEX_CLANPOSMARKMAP);
        }
        return this.clanPosMarkMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putClanPosMarkMapV(PositionMarkInfoProp v) {
        this.getClanPosMarkMap().put(v.getMarkId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public PositionMarkInfoProp addEmptyClanPosMarkMap(Long k) {
        return this.getClanPosMarkMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getClanPosMarkMapSize() {
        if (this.clanPosMarkMap == null) {
            return 0;
        }
        return this.clanPosMarkMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isClanPosMarkMapEmpty() {
        if (this.clanPosMarkMap == null) {
            return true;
        }
        return this.clanPosMarkMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public PositionMarkInfoProp getClanPosMarkMapV(Long k) {
        if (this.clanPosMarkMap == null || !this.clanPosMarkMap.containsKey(k)) {
            return null;
        }
        return this.clanPosMarkMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearClanPosMarkMap() {
        if (this.clanPosMarkMap != null) {
            this.clanPosMarkMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeClanPosMarkMapV(Long k) {
        if (this.clanPosMarkMap != null) {
            this.clanPosMarkMap.remove(k);
        }
    }
    /**
     * get staffModel
     *
     * @return staffModel value
     */
    public ClanStaffModelProp getStaffModel() {
        if (this.staffModel == null) {
            this.staffModel = new ClanStaffModelProp(this, FIELD_INDEX_STAFFMODEL);
        }
        return this.staffModel;
    }

    /**
     * get stageModel
     *
     * @return stageModel value
     */
    public ClanStageModelProp getStageModel() {
        if (this.stageModel == null) {
            this.stageModel = new ClanStageModelProp(this, FIELD_INDEX_STAGEMODEL);
        }
        return this.stageModel;
    }

    /**
     * get logModel
     *
     * @return logModel value
     */
    public ClanLogModelProp getLogModel() {
        if (this.logModel == null) {
            this.logModel = new ClanLogModelProp(this, FIELD_INDEX_LOGMODEL);
        }
        return this.logModel;
    }

    /**
     * get redDotModel
     *
     * @return redDotModel value
     */
    public ClanRedDotModelProp getRedDotModel() {
        if (this.redDotModel == null) {
            this.redDotModel = new ClanRedDotModelProp(this, FIELD_INDEX_REDDOTMODEL);
        }
        return this.redDotModel;
    }

    /**
     * get techModel
     *
     * @return techModel value
     */
    public ClanTechModelProp getTechModel() {
        if (this.techModel == null) {
            this.techModel = new ClanTechModelProp(this, FIELD_INDEX_TECHMODEL);
        }
        return this.techModel;
    }

    /**
     * get refreshModel
     *
     * @return refreshModel value
     */
    public ClanRefreshModelProp getRefreshModel() {
        if (this.refreshModel == null) {
            this.refreshModel = new ClanRefreshModelProp(this, FIELD_INDEX_REFRESHMODEL);
        }
        return this.refreshModel;
    }

    /**
     * get ClanMemberHistoryModel
     *
     * @return ClanMemberHistoryModel value
     */
    public ClanMemberHistoryModelProp getClanMemberHistoryModel() {
        if (this.ClanMemberHistoryModel == null) {
            this.ClanMemberHistoryModel = new ClanMemberHistoryModelProp(this, FIELD_INDEX_CLANMEMBERHISTORYMODEL);
        }
        return this.ClanMemberHistoryModel;
    }

    /**
     * get storeModel
     *
     * @return storeModel value
     */
    public ClanStoreModelProp getStoreModel() {
        if (this.storeModel == null) {
            this.storeModel = new ClanStoreModelProp(this, FIELD_INDEX_STOREMODEL);
        }
        return this.storeModel;
    }

    /**
     * get resetQuietPeriod
     *
     * @return resetQuietPeriod value
     */
    public ClanResetQuietPeriodProp getResetQuietPeriod() {
        if (this.resetQuietPeriod == null) {
            this.resetQuietPeriod = new ClanResetQuietPeriodProp(this, FIELD_INDEX_RESETQUIETPERIOD);
        }
        return this.resetQuietPeriod;
    }

    /**
     * get miscModel
     *
     * @return miscModel value
     */
    public ClanMiscModelProp getMiscModel() {
        if (this.miscModel == null) {
            this.miscModel = new ClanMiscModelProp(this, FIELD_INDEX_MISCMODEL);
        }
        return this.miscModel;
    }

    /**
     * get inviteModel
     *
     * @return inviteModel value
     */
    public ClanInviteModelProp getInviteModel() {
        if (this.inviteModel == null) {
            this.inviteModel = new ClanInviteModelProp(this, FIELD_INDEX_INVITEMODEL);
        }
        return this.inviteModel;
    }

    /**
     * get actModel
     *
     * @return actModel value
     */
    public ClanActivityModelProp getActModel() {
        if (this.actModel == null) {
            this.actModel = new ClanActivityModelProp(this, FIELD_INDEX_ACTMODEL);
        }
        return this.actModel;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanEntityPB.Builder getCopyCsBuilder() {
        final ClanEntityPB.Builder builder = ClanEntityPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(ClanEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.base != null) {
            StructClanPB.ClanBaseInfoPB.Builder tmpBuilder = StructClanPB.ClanBaseInfoPB.newBuilder();
            final int tmpFieldCnt = this.base.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBase(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBase();
            }
        }  else if (builder.hasBase()) {
            // 清理Base
            builder.clearBase();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getNumMax() != 0) {
            builder.setNumMax(this.getNumMax());
            fieldCnt++;
        }  else if (builder.hasNumMax()) {
            // 清理NumMax
            builder.clearNumMax();
            fieldCnt++;
        }
        if (this.getCombat() != 0L) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }  else if (builder.hasCombat()) {
            // 清理Combat
            builder.clearCombat();
            fieldCnt++;
        }
        if (this.member != null) {
            StructClanPB.Int64ClanMemberMapPB.Builder tmpBuilder = StructClanPB.Int64ClanMemberMapPB.newBuilder();
            final int tmpFieldCnt = this.member.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMember(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMember();
            }
        }  else if (builder.hasMember()) {
            // 清理Member
            builder.clearMember();
            fieldCnt++;
        }
        if (this.applys != null) {
            StructClanPB.Int64ClanMemberMapPB.Builder tmpBuilder = StructClanPB.Int64ClanMemberMapPB.newBuilder();
            final int tmpFieldCnt = this.applys.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplys();
            }
        }  else if (builder.hasApplys()) {
            // 清理Applys
            builder.clearApplys();
            fieldCnt++;
        }
        if (this.getVersion() != 0L) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }  else if (builder.hasVersion()) {
            // 清理Version
            builder.clearVersion();
            fieldCnt++;
        }
        if (this.territoryInfo != null) {
            ClanPB.TerritoryInfoPB.Builder tmpBuilder = ClanPB.TerritoryInfoPB.newBuilder();
            final int tmpFieldCnt = this.territoryInfo.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTerritoryInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTerritoryInfo();
            }
        }  else if (builder.hasTerritoryInfo()) {
            // 清理TerritoryInfo
            builder.clearTerritoryInfo();
            fieldCnt++;
        }
        if (this.clanPosMarkMap != null) {
            StructPB.Int64PositionMarkInfoMapPB.Builder tmpBuilder = StructPB.Int64PositionMarkInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.clanPosMarkMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanPosMarkMap();
            }
        }  else if (builder.hasClanPosMarkMap()) {
            // 清理ClanPosMarkMap
            builder.clearClanPosMarkMap();
            fieldCnt++;
        }
        if (this.staffModel != null) {
            ClanPB.ClanStaffModelPB.Builder tmpBuilder = ClanPB.ClanStaffModelPB.newBuilder();
            final int tmpFieldCnt = this.staffModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStaffModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStaffModel();
            }
        }  else if (builder.hasStaffModel()) {
            // 清理StaffModel
            builder.clearStaffModel();
            fieldCnt++;
        }
        if (this.stageModel != null) {
            StructPB.ClanStageModelPB.Builder tmpBuilder = StructPB.ClanStageModelPB.newBuilder();
            final int tmpFieldCnt = this.stageModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStageModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStageModel();
            }
        }  else if (builder.hasStageModel()) {
            // 清理StageModel
            builder.clearStageModel();
            fieldCnt++;
        }
        if (this.logModel != null) {
            ClanPB.ClanLogModelPB.Builder tmpBuilder = ClanPB.ClanLogModelPB.newBuilder();
            final int tmpFieldCnt = this.logModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogModel();
            }
        }  else if (builder.hasLogModel()) {
            // 清理LogModel
            builder.clearLogModel();
            fieldCnt++;
        }
        if (this.redDotModel != null) {
            ClanPB.ClanRedDotModelPB.Builder tmpBuilder = ClanPB.ClanRedDotModelPB.newBuilder();
            final int tmpFieldCnt = this.redDotModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotModel();
            }
        }  else if (builder.hasRedDotModel()) {
            // 清理RedDotModel
            builder.clearRedDotModel();
            fieldCnt++;
        }
        if (this.techModel != null) {
            ClanPB.ClanTechModelPB.Builder tmpBuilder = ClanPB.ClanTechModelPB.newBuilder();
            final int tmpFieldCnt = this.techModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTechModel();
            }
        }  else if (builder.hasTechModel()) {
            // 清理TechModel
            builder.clearTechModel();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            ClanPB.ClanRefreshModelPB.Builder tmpBuilder = ClanPB.ClanRefreshModelPB.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        if (this.ClanMemberHistoryModel != null) {
            ClanPB.ClanMemberHistoryModelPB.Builder tmpBuilder = ClanPB.ClanMemberHistoryModelPB.newBuilder();
            final int tmpFieldCnt = this.ClanMemberHistoryModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanMemberHistoryModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanMemberHistoryModel();
            }
        }  else if (builder.hasClanMemberHistoryModel()) {
            // 清理ClanMemberHistoryModel
            builder.clearClanMemberHistoryModel();
            fieldCnt++;
        }
        if (this.storeModel != null) {
            ClanPB.ClanStoreModelPB.Builder tmpBuilder = ClanPB.ClanStoreModelPB.newBuilder();
            final int tmpFieldCnt = this.storeModel.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreModel();
            }
        }  else if (builder.hasStoreModel()) {
            // 清理StoreModel
            builder.clearStoreModel();
            fieldCnt++;
        }
        if (this.resetQuietPeriod != null) {
            ClanPB.ClanResetQuietPeriodPB.Builder tmpBuilder = ClanPB.ClanResetQuietPeriodPB.newBuilder();
            final int tmpFieldCnt = this.resetQuietPeriod.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResetQuietPeriod(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResetQuietPeriod();
            }
        }  else if (builder.hasResetQuietPeriod()) {
            // 清理ResetQuietPeriod
            builder.clearResetQuietPeriod();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(ClanEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASE) && this.base != null) {
            final boolean needClear = !builder.hasBase();
            final int tmpFieldCnt = this.base.copyChangeToCs(builder.getBaseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBase();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUMMAX)) {
            builder.setNumMax(this.getNumMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MEMBER) && this.member != null) {
            final boolean needClear = !builder.hasMember();
            final int tmpFieldCnt = this.member.copyChangeToCs(builder.getMemberBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMember();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYS) && this.applys != null) {
            final boolean needClear = !builder.hasApplys();
            final int tmpFieldCnt = this.applys.copyChangeToCs(builder.getApplysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplys();
            }
        }
        if (this.hasMark(FIELD_INDEX_VERSION)) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYINFO) && this.territoryInfo != null) {
            final boolean needClear = !builder.hasTerritoryInfo();
            final int tmpFieldCnt = this.territoryInfo.copyChangeToCs(builder.getTerritoryInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTerritoryInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            final boolean needClear = !builder.hasClanPosMarkMap();
            final int tmpFieldCnt = this.clanPosMarkMap.copyChangeToCs(builder.getClanPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFMODEL) && this.staffModel != null) {
            final boolean needClear = !builder.hasStaffModel();
            final int tmpFieldCnt = this.staffModel.copyChangeToCs(builder.getStaffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAGEMODEL) && this.stageModel != null) {
            final boolean needClear = !builder.hasStageModel();
            final int tmpFieldCnt = this.stageModel.copyChangeToCs(builder.getStageModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGMODEL) && this.logModel != null) {
            final boolean needClear = !builder.hasLogModel();
            final int tmpFieldCnt = this.logModel.copyChangeToCs(builder.getLogModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToCs(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToCs(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToCs(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANMEMBERHISTORYMODEL) && this.ClanMemberHistoryModel != null) {
            final boolean needClear = !builder.hasClanMemberHistoryModel();
            final int tmpFieldCnt = this.ClanMemberHistoryModel.copyChangeToCs(builder.getClanMemberHistoryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMemberHistoryModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREMODEL) && this.storeModel != null) {
            final boolean needClear = !builder.hasStoreModel();
            final int tmpFieldCnt = this.storeModel.copyChangeToCs(builder.getStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETQUIETPERIOD) && this.resetQuietPeriod != null) {
            final boolean needClear = !builder.hasResetQuietPeriod();
            final int tmpFieldCnt = this.resetQuietPeriod.copyChangeToCs(builder.getResetQuietPeriodBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetQuietPeriod();
            }
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(ClanEntityPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASE) && this.base != null) {
            final boolean needClear = !builder.hasBase();
            final int tmpFieldCnt = this.base.copyChangeToAndClearDeleteKeysCs(builder.getBaseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBase();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUMMAX)) {
            builder.setNumMax(this.getNumMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MEMBER) && this.member != null) {
            final boolean needClear = !builder.hasMember();
            final int tmpFieldCnt = this.member.copyChangeToAndClearDeleteKeysCs(builder.getMemberBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMember();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYS) && this.applys != null) {
            final boolean needClear = !builder.hasApplys();
            final int tmpFieldCnt = this.applys.copyChangeToAndClearDeleteKeysCs(builder.getApplysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplys();
            }
        }
        if (this.hasMark(FIELD_INDEX_VERSION)) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYINFO) && this.territoryInfo != null) {
            final boolean needClear = !builder.hasTerritoryInfo();
            final int tmpFieldCnt = this.territoryInfo.copyChangeToAndClearDeleteKeysCs(builder.getTerritoryInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTerritoryInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            final boolean needClear = !builder.hasClanPosMarkMap();
            final int tmpFieldCnt = this.clanPosMarkMap.copyChangeToAndClearDeleteKeysCs(builder.getClanPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFMODEL) && this.staffModel != null) {
            final boolean needClear = !builder.hasStaffModel();
            final int tmpFieldCnt = this.staffModel.copyChangeToAndClearDeleteKeysCs(builder.getStaffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAGEMODEL) && this.stageModel != null) {
            final boolean needClear = !builder.hasStageModel();
            final int tmpFieldCnt = this.stageModel.copyChangeToAndClearDeleteKeysCs(builder.getStageModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGMODEL) && this.logModel != null) {
            final boolean needClear = !builder.hasLogModel();
            final int tmpFieldCnt = this.logModel.copyChangeToAndClearDeleteKeysCs(builder.getLogModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToAndClearDeleteKeysCs(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToAndClearDeleteKeysCs(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToAndClearDeleteKeysCs(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANMEMBERHISTORYMODEL) && this.ClanMemberHistoryModel != null) {
            final boolean needClear = !builder.hasClanMemberHistoryModel();
            final int tmpFieldCnt = this.ClanMemberHistoryModel.copyChangeToAndClearDeleteKeysCs(builder.getClanMemberHistoryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMemberHistoryModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREMODEL) && this.storeModel != null) {
            final boolean needClear = !builder.hasStoreModel();
            final int tmpFieldCnt = this.storeModel.copyChangeToAndClearDeleteKeysCs(builder.getStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETQUIETPERIOD) && this.resetQuietPeriod != null) {
            final boolean needClear = !builder.hasResetQuietPeriod();
            final int tmpFieldCnt = this.resetQuietPeriod.copyChangeToAndClearDeleteKeysCs(builder.getResetQuietPeriodBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetQuietPeriod();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(ClanEntityPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBase()) {
            this.getBase().mergeFromCs(proto.getBase());
        } else {
            if (this.base != null) {
                this.base.mergeFromCs(proto.getBase());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNumMax()) {
            this.innerSetNumMax(proto.getNumMax());
        } else {
            this.innerSetNumMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCombat()) {
            this.innerSetCombat(proto.getCombat());
        } else {
            this.innerSetCombat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMember()) {
            this.getMember().mergeFromCs(proto.getMember());
        } else {
            if (this.member != null) {
                this.member.mergeFromCs(proto.getMember());
            }
        }
        if (proto.hasApplys()) {
            this.getApplys().mergeFromCs(proto.getApplys());
        } else {
            if (this.applys != null) {
                this.applys.mergeFromCs(proto.getApplys());
            }
        }
        if (proto.hasVersion()) {
            this.innerSetVersion(proto.getVersion());
        } else {
            this.innerSetVersion(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTerritoryInfo()) {
            this.getTerritoryInfo().mergeFromCs(proto.getTerritoryInfo());
        } else {
            if (this.territoryInfo != null) {
                this.territoryInfo.mergeFromCs(proto.getTerritoryInfo());
            }
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeFromCs(proto.getClanPosMarkMap());
        } else {
            if (this.clanPosMarkMap != null) {
                this.clanPosMarkMap.mergeFromCs(proto.getClanPosMarkMap());
            }
        }
        if (proto.hasStaffModel()) {
            this.getStaffModel().mergeFromCs(proto.getStaffModel());
        } else {
            if (this.staffModel != null) {
                this.staffModel.mergeFromCs(proto.getStaffModel());
            }
        }
        if (proto.hasStageModel()) {
            this.getStageModel().mergeFromCs(proto.getStageModel());
        } else {
            if (this.stageModel != null) {
                this.stageModel.mergeFromCs(proto.getStageModel());
            }
        }
        if (proto.hasLogModel()) {
            this.getLogModel().mergeFromCs(proto.getLogModel());
        } else {
            if (this.logModel != null) {
                this.logModel.mergeFromCs(proto.getLogModel());
            }
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeFromCs(proto.getRedDotModel());
        } else {
            if (this.redDotModel != null) {
                this.redDotModel.mergeFromCs(proto.getRedDotModel());
            }
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeFromCs(proto.getTechModel());
        } else {
            if (this.techModel != null) {
                this.techModel.mergeFromCs(proto.getTechModel());
            }
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromCs(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromCs(proto.getRefreshModel());
            }
        }
        if (proto.hasClanMemberHistoryModel()) {
            this.getClanMemberHistoryModel().mergeFromCs(proto.getClanMemberHistoryModel());
        } else {
            if (this.ClanMemberHistoryModel != null) {
                this.ClanMemberHistoryModel.mergeFromCs(proto.getClanMemberHistoryModel());
            }
        }
        if (proto.hasStoreModel()) {
            this.getStoreModel().mergeFromCs(proto.getStoreModel());
        } else {
            if (this.storeModel != null) {
                this.storeModel.mergeFromCs(proto.getStoreModel());
            }
        }
        if (proto.hasResetQuietPeriod()) {
            this.getResetQuietPeriod().mergeFromCs(proto.getResetQuietPeriod());
        } else {
            if (this.resetQuietPeriod != null) {
                this.resetQuietPeriod.mergeFromCs(proto.getResetQuietPeriod());
            }
        }
        this.markAll();
        return ClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(ClanEntityPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasBase()) {
            this.getBase().mergeChangeFromCs(proto.getBase());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasNumMax()) {
            this.setNumMax(proto.getNumMax());
            fieldCnt++;
        }
        if (proto.hasCombat()) {
            this.setCombat(proto.getCombat());
            fieldCnt++;
        }
        if (proto.hasMember()) {
            this.getMember().mergeChangeFromCs(proto.getMember());
            fieldCnt++;
        }
        if (proto.hasApplys()) {
            this.getApplys().mergeChangeFromCs(proto.getApplys());
            fieldCnt++;
        }
        if (proto.hasVersion()) {
            this.setVersion(proto.getVersion());
            fieldCnt++;
        }
        if (proto.hasTerritoryInfo()) {
            this.getTerritoryInfo().mergeChangeFromCs(proto.getTerritoryInfo());
            fieldCnt++;
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeChangeFromCs(proto.getClanPosMarkMap());
            fieldCnt++;
        }
        if (proto.hasStaffModel()) {
            this.getStaffModel().mergeChangeFromCs(proto.getStaffModel());
            fieldCnt++;
        }
        if (proto.hasStageModel()) {
            this.getStageModel().mergeChangeFromCs(proto.getStageModel());
            fieldCnt++;
        }
        if (proto.hasLogModel()) {
            this.getLogModel().mergeChangeFromCs(proto.getLogModel());
            fieldCnt++;
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeChangeFromCs(proto.getRedDotModel());
            fieldCnt++;
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeChangeFromCs(proto.getTechModel());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromCs(proto.getRefreshModel());
            fieldCnt++;
        }
        if (proto.hasClanMemberHistoryModel()) {
            this.getClanMemberHistoryModel().mergeChangeFromCs(proto.getClanMemberHistoryModel());
            fieldCnt++;
        }
        if (proto.hasStoreModel()) {
            this.getStoreModel().mergeChangeFromCs(proto.getStoreModel());
            fieldCnt++;
        }
        if (proto.hasResetQuietPeriod()) {
            this.getResetQuietPeriod().mergeChangeFromCs(proto.getResetQuietPeriod());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanEntity.Builder getCopyDbBuilder() {
        final ClanEntity.Builder builder = ClanEntity.newBuilder();
        this.copyToDb(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToDb(ClanEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.base != null) {
            StructClan.ClanBaseInfo.Builder tmpBuilder = StructClan.ClanBaseInfo.newBuilder();
            final int tmpFieldCnt = this.base.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBase(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBase();
            }
        }  else if (builder.hasBase()) {
            // 清理Base
            builder.clearBase();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.getCombat() != 0L) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }  else if (builder.hasCombat()) {
            // 清理Combat
            builder.clearCombat();
            fieldCnt++;
        }
        if (this.member != null) {
            StructClan.Int64ClanMemberMap.Builder tmpBuilder = StructClan.Int64ClanMemberMap.newBuilder();
            final int tmpFieldCnt = this.member.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMember(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMember();
            }
        }  else if (builder.hasMember()) {
            // 清理Member
            builder.clearMember();
            fieldCnt++;
        }
        if (this.applys != null) {
            StructClan.Int64ClanMemberMap.Builder tmpBuilder = StructClan.Int64ClanMemberMap.newBuilder();
            final int tmpFieldCnt = this.applys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplys();
            }
        }  else if (builder.hasApplys()) {
            // 清理Applys
            builder.clearApplys();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.resources != null) {
            Clan.ClanResources.Builder tmpBuilder = Clan.ClanResources.newBuilder();
            final int tmpFieldCnt = this.resources.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.territoryInfo != null) {
            Clan.TerritoryInfo.Builder tmpBuilder = Clan.TerritoryInfo.newBuilder();
            final int tmpFieldCnt = this.territoryInfo.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTerritoryInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTerritoryInfo();
            }
        }  else if (builder.hasTerritoryInfo()) {
            // 清理TerritoryInfo
            builder.clearTerritoryInfo();
            fieldCnt++;
        }
        if (this.helps != null) {
            Struct.Int64ClanHelpItemMap.Builder tmpBuilder = Struct.Int64ClanHelpItemMap.newBuilder();
            final int tmpFieldCnt = this.helps.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHelps(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHelps();
            }
        }  else if (builder.hasHelps()) {
            // 清理Helps
            builder.clearHelps();
            fieldCnt++;
        }
        if (this.giftModel != null) {
            Clan.ClanGiftModel.Builder tmpBuilder = Clan.ClanGiftModel.newBuilder();
            final int tmpFieldCnt = this.giftModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftModel();
            }
        }  else if (builder.hasGiftModel()) {
            // 清理GiftModel
            builder.clearGiftModel();
            fieldCnt++;
        }
        if (this.clanPosMarkMap != null) {
            Struct.Int64PositionMarkInfoMap.Builder tmpBuilder = Struct.Int64PositionMarkInfoMap.newBuilder();
            final int tmpFieldCnt = this.clanPosMarkMap.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanPosMarkMap();
            }
        }  else if (builder.hasClanPosMarkMap()) {
            // 清理ClanPosMarkMap
            builder.clearClanPosMarkMap();
            fieldCnt++;
        }
        if (this.staffModel != null) {
            Clan.ClanStaffModel.Builder tmpBuilder = Clan.ClanStaffModel.newBuilder();
            final int tmpFieldCnt = this.staffModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStaffModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStaffModel();
            }
        }  else if (builder.hasStaffModel()) {
            // 清理StaffModel
            builder.clearStaffModel();
            fieldCnt++;
        }
        if (this.stageModel != null) {
            Struct.ClanStageModel.Builder tmpBuilder = Struct.ClanStageModel.newBuilder();
            final int tmpFieldCnt = this.stageModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStageModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStageModel();
            }
        }  else if (builder.hasStageModel()) {
            // 清理StageModel
            builder.clearStageModel();
            fieldCnt++;
        }
        if (this.logModel != null) {
            Clan.ClanLogModel.Builder tmpBuilder = Clan.ClanLogModel.newBuilder();
            final int tmpFieldCnt = this.logModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogModel();
            }
        }  else if (builder.hasLogModel()) {
            // 清理LogModel
            builder.clearLogModel();
            fieldCnt++;
        }
        if (this.redDotModel != null) {
            Clan.ClanRedDotModel.Builder tmpBuilder = Clan.ClanRedDotModel.newBuilder();
            final int tmpFieldCnt = this.redDotModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotModel();
            }
        }  else if (builder.hasRedDotModel()) {
            // 清理RedDotModel
            builder.clearRedDotModel();
            fieldCnt++;
        }
        if (this.techModel != null) {
            Clan.ClanTechModel.Builder tmpBuilder = Clan.ClanTechModel.newBuilder();
            final int tmpFieldCnt = this.techModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTechModel();
            }
        }  else if (builder.hasTechModel()) {
            // 清理TechModel
            builder.clearTechModel();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            Clan.ClanRefreshModel.Builder tmpBuilder = Clan.ClanRefreshModel.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        if (this.ClanMemberHistoryModel != null) {
            Clan.ClanMemberHistoryModel.Builder tmpBuilder = Clan.ClanMemberHistoryModel.newBuilder();
            final int tmpFieldCnt = this.ClanMemberHistoryModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanMemberHistoryModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanMemberHistoryModel();
            }
        }  else if (builder.hasClanMemberHistoryModel()) {
            // 清理ClanMemberHistoryModel
            builder.clearClanMemberHistoryModel();
            fieldCnt++;
        }
        if (this.storeModel != null) {
            Clan.ClanStoreModel.Builder tmpBuilder = Clan.ClanStoreModel.newBuilder();
            final int tmpFieldCnt = this.storeModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreModel();
            }
        }  else if (builder.hasStoreModel()) {
            // 清理StoreModel
            builder.clearStoreModel();
            fieldCnt++;
        }
        if (this.resetQuietPeriod != null) {
            Clan.ClanResetQuietPeriod.Builder tmpBuilder = Clan.ClanResetQuietPeriod.newBuilder();
            final int tmpFieldCnt = this.resetQuietPeriod.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResetQuietPeriod(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResetQuietPeriod();
            }
        }  else if (builder.hasResetQuietPeriod()) {
            // 清理ResetQuietPeriod
            builder.clearResetQuietPeriod();
            fieldCnt++;
        }
        if (this.miscModel != null) {
            Clan.ClanMiscModel.Builder tmpBuilder = Clan.ClanMiscModel.newBuilder();
            final int tmpFieldCnt = this.miscModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMiscModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMiscModel();
            }
        }  else if (builder.hasMiscModel()) {
            // 清理MiscModel
            builder.clearMiscModel();
            fieldCnt++;
        }
        if (this.inviteModel != null) {
            Clan.ClanInviteModel.Builder tmpBuilder = Clan.ClanInviteModel.newBuilder();
            final int tmpFieldCnt = this.inviteModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteModel();
            }
        }  else if (builder.hasInviteModel()) {
            // 清理InviteModel
            builder.clearInviteModel();
            fieldCnt++;
        }
        if (this.actModel != null) {
            Clan.ClanActivityModel.Builder tmpBuilder = Clan.ClanActivityModel.newBuilder();
            final int tmpFieldCnt = this.actModel.copyToDb(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActModel();
            }
        }  else if (builder.hasActModel()) {
            // 清理ActModel
            builder.clearActModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToDb(ClanEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASE) && this.base != null) {
            final boolean needClear = !builder.hasBase();
            final int tmpFieldCnt = this.base.copyChangeToDb(builder.getBaseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBase();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MEMBER) && this.member != null) {
            final boolean needClear = !builder.hasMember();
            final int tmpFieldCnt = this.member.copyChangeToDb(builder.getMemberBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMember();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYS) && this.applys != null) {
            final boolean needClear = !builder.hasApplys();
            final int tmpFieldCnt = this.applys.copyChangeToDb(builder.getApplysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplys();
            }
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToDb(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToDb(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToDb(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYINFO) && this.territoryInfo != null) {
            final boolean needClear = !builder.hasTerritoryInfo();
            final int tmpFieldCnt = this.territoryInfo.copyChangeToDb(builder.getTerritoryInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTerritoryInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_HELPS) && this.helps != null) {
            final boolean needClear = !builder.hasHelps();
            final int tmpFieldCnt = this.helps.copyChangeToDb(builder.getHelpsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHelps();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTMODEL) && this.giftModel != null) {
            final boolean needClear = !builder.hasGiftModel();
            final int tmpFieldCnt = this.giftModel.copyChangeToDb(builder.getGiftModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            final boolean needClear = !builder.hasClanPosMarkMap();
            final int tmpFieldCnt = this.clanPosMarkMap.copyChangeToDb(builder.getClanPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFMODEL) && this.staffModel != null) {
            final boolean needClear = !builder.hasStaffModel();
            final int tmpFieldCnt = this.staffModel.copyChangeToDb(builder.getStaffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAGEMODEL) && this.stageModel != null) {
            final boolean needClear = !builder.hasStageModel();
            final int tmpFieldCnt = this.stageModel.copyChangeToDb(builder.getStageModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGMODEL) && this.logModel != null) {
            final boolean needClear = !builder.hasLogModel();
            final int tmpFieldCnt = this.logModel.copyChangeToDb(builder.getLogModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToDb(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToDb(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToDb(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANMEMBERHISTORYMODEL) && this.ClanMemberHistoryModel != null) {
            final boolean needClear = !builder.hasClanMemberHistoryModel();
            final int tmpFieldCnt = this.ClanMemberHistoryModel.copyChangeToDb(builder.getClanMemberHistoryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMemberHistoryModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREMODEL) && this.storeModel != null) {
            final boolean needClear = !builder.hasStoreModel();
            final int tmpFieldCnt = this.storeModel.copyChangeToDb(builder.getStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETQUIETPERIOD) && this.resetQuietPeriod != null) {
            final boolean needClear = !builder.hasResetQuietPeriod();
            final int tmpFieldCnt = this.resetQuietPeriod.copyChangeToDb(builder.getResetQuietPeriodBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetQuietPeriod();
            }
        }
        if (this.hasMark(FIELD_INDEX_MISCMODEL) && this.miscModel != null) {
            final boolean needClear = !builder.hasMiscModel();
            final int tmpFieldCnt = this.miscModel.copyChangeToDb(builder.getMiscModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMiscModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_INVITEMODEL) && this.inviteModel != null) {
            final boolean needClear = !builder.hasInviteModel();
            final int tmpFieldCnt = this.inviteModel.copyChangeToDb(builder.getInviteModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTMODEL) && this.actModel != null) {
            final boolean needClear = !builder.hasActModel();
            final int tmpFieldCnt = this.actModel.copyChangeToDb(builder.getActModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeFromDb(ClanEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBase()) {
            this.getBase().mergeFromDb(proto.getBase());
        } else {
            if (this.base != null) {
                this.base.mergeFromDb(proto.getBase());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCombat()) {
            this.innerSetCombat(proto.getCombat());
        } else {
            this.innerSetCombat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMember()) {
            this.getMember().mergeFromDb(proto.getMember());
        } else {
            if (this.member != null) {
                this.member.mergeFromDb(proto.getMember());
            }
        }
        if (proto.hasApplys()) {
            this.getApplys().mergeFromDb(proto.getApplys());
        } else {
            if (this.applys != null) {
                this.applys.mergeFromDb(proto.getApplys());
            }
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromDb(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromDb(proto.getAdditionSys());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromDb(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromDb(proto.getDevBuffSys());
            }
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromDb(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromDb(proto.getResources());
            }
        }
        if (proto.hasTerritoryInfo()) {
            this.getTerritoryInfo().mergeFromDb(proto.getTerritoryInfo());
        } else {
            if (this.territoryInfo != null) {
                this.territoryInfo.mergeFromDb(proto.getTerritoryInfo());
            }
        }
        if (proto.hasHelps()) {
            this.getHelps().mergeFromDb(proto.getHelps());
        } else {
            if (this.helps != null) {
                this.helps.mergeFromDb(proto.getHelps());
            }
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeFromDb(proto.getGiftModel());
        } else {
            if (this.giftModel != null) {
                this.giftModel.mergeFromDb(proto.getGiftModel());
            }
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeFromDb(proto.getClanPosMarkMap());
        } else {
            if (this.clanPosMarkMap != null) {
                this.clanPosMarkMap.mergeFromDb(proto.getClanPosMarkMap());
            }
        }
        if (proto.hasStaffModel()) {
            this.getStaffModel().mergeFromDb(proto.getStaffModel());
        } else {
            if (this.staffModel != null) {
                this.staffModel.mergeFromDb(proto.getStaffModel());
            }
        }
        if (proto.hasStageModel()) {
            this.getStageModel().mergeFromDb(proto.getStageModel());
        } else {
            if (this.stageModel != null) {
                this.stageModel.mergeFromDb(proto.getStageModel());
            }
        }
        if (proto.hasLogModel()) {
            this.getLogModel().mergeFromDb(proto.getLogModel());
        } else {
            if (this.logModel != null) {
                this.logModel.mergeFromDb(proto.getLogModel());
            }
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeFromDb(proto.getRedDotModel());
        } else {
            if (this.redDotModel != null) {
                this.redDotModel.mergeFromDb(proto.getRedDotModel());
            }
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeFromDb(proto.getTechModel());
        } else {
            if (this.techModel != null) {
                this.techModel.mergeFromDb(proto.getTechModel());
            }
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromDb(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromDb(proto.getRefreshModel());
            }
        }
        if (proto.hasClanMemberHistoryModel()) {
            this.getClanMemberHistoryModel().mergeFromDb(proto.getClanMemberHistoryModel());
        } else {
            if (this.ClanMemberHistoryModel != null) {
                this.ClanMemberHistoryModel.mergeFromDb(proto.getClanMemberHistoryModel());
            }
        }
        if (proto.hasStoreModel()) {
            this.getStoreModel().mergeFromDb(proto.getStoreModel());
        } else {
            if (this.storeModel != null) {
                this.storeModel.mergeFromDb(proto.getStoreModel());
            }
        }
        if (proto.hasResetQuietPeriod()) {
            this.getResetQuietPeriod().mergeFromDb(proto.getResetQuietPeriod());
        } else {
            if (this.resetQuietPeriod != null) {
                this.resetQuietPeriod.mergeFromDb(proto.getResetQuietPeriod());
            }
        }
        if (proto.hasMiscModel()) {
            this.getMiscModel().mergeFromDb(proto.getMiscModel());
        } else {
            if (this.miscModel != null) {
                this.miscModel.mergeFromDb(proto.getMiscModel());
            }
        }
        if (proto.hasInviteModel()) {
            this.getInviteModel().mergeFromDb(proto.getInviteModel());
        } else {
            if (this.inviteModel != null) {
                this.inviteModel.mergeFromDb(proto.getInviteModel());
            }
        }
        if (proto.hasActModel()) {
            this.getActModel().mergeFromDb(proto.getActModel());
        } else {
            if (this.actModel != null) {
                this.actModel.mergeFromDb(proto.getActModel());
            }
        }
        this.markAll();
        return ClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Db
     *
     * @param proto protobuf Db
     * @return merged field count
     */
    public int mergeChangeFromDb(ClanEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasBase()) {
            this.getBase().mergeChangeFromDb(proto.getBase());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasCombat()) {
            this.setCombat(proto.getCombat());
            fieldCnt++;
        }
        if (proto.hasMember()) {
            this.getMember().mergeChangeFromDb(proto.getMember());
            fieldCnt++;
        }
        if (proto.hasApplys()) {
            this.getApplys().mergeChangeFromDb(proto.getApplys());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromDb(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromDb(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromDb(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasTerritoryInfo()) {
            this.getTerritoryInfo().mergeChangeFromDb(proto.getTerritoryInfo());
            fieldCnt++;
        }
        if (proto.hasHelps()) {
            this.getHelps().mergeChangeFromDb(proto.getHelps());
            fieldCnt++;
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeChangeFromDb(proto.getGiftModel());
            fieldCnt++;
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeChangeFromDb(proto.getClanPosMarkMap());
            fieldCnt++;
        }
        if (proto.hasStaffModel()) {
            this.getStaffModel().mergeChangeFromDb(proto.getStaffModel());
            fieldCnt++;
        }
        if (proto.hasStageModel()) {
            this.getStageModel().mergeChangeFromDb(proto.getStageModel());
            fieldCnt++;
        }
        if (proto.hasLogModel()) {
            this.getLogModel().mergeChangeFromDb(proto.getLogModel());
            fieldCnt++;
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeChangeFromDb(proto.getRedDotModel());
            fieldCnt++;
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeChangeFromDb(proto.getTechModel());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromDb(proto.getRefreshModel());
            fieldCnt++;
        }
        if (proto.hasClanMemberHistoryModel()) {
            this.getClanMemberHistoryModel().mergeChangeFromDb(proto.getClanMemberHistoryModel());
            fieldCnt++;
        }
        if (proto.hasStoreModel()) {
            this.getStoreModel().mergeChangeFromDb(proto.getStoreModel());
            fieldCnt++;
        }
        if (proto.hasResetQuietPeriod()) {
            this.getResetQuietPeriod().mergeChangeFromDb(proto.getResetQuietPeriod());
            fieldCnt++;
        }
        if (proto.hasMiscModel()) {
            this.getMiscModel().mergeChangeFromDb(proto.getMiscModel());
            fieldCnt++;
        }
        if (proto.hasInviteModel()) {
            this.getInviteModel().mergeChangeFromDb(proto.getInviteModel());
            fieldCnt++;
        }
        if (proto.hasActModel()) {
            this.getActModel().mergeChangeFromDb(proto.getActModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public ClanEntity.Builder getCopySsBuilder() {
        final ClanEntity.Builder builder = ClanEntity.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(ClanEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getId() != 0L) {
            builder.setId(this.getId());
            fieldCnt++;
        }  else if (builder.hasId()) {
            // 清理Id
            builder.clearId();
            fieldCnt++;
        }
        if (this.getOwnerId() != 0L) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }  else if (builder.hasOwnerId()) {
            // 清理OwnerId
            builder.clearOwnerId();
            fieldCnt++;
        }
        if (this.base != null) {
            StructClan.ClanBaseInfo.Builder tmpBuilder = StructClan.ClanBaseInfo.newBuilder();
            final int tmpFieldCnt = this.base.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setBase(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearBase();
            }
        }  else if (builder.hasBase()) {
            // 清理Base
            builder.clearBase();
            fieldCnt++;
        }
        if (this.getCreateTime() != 0L) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }  else if (builder.hasCreateTime()) {
            // 清理CreateTime
            builder.clearCreateTime();
            fieldCnt++;
        }
        if (this.getNum() != 0) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }  else if (builder.hasNum()) {
            // 清理Num
            builder.clearNum();
            fieldCnt++;
        }
        if (this.getNumMax() != 0) {
            builder.setNumMax(this.getNumMax());
            fieldCnt++;
        }  else if (builder.hasNumMax()) {
            // 清理NumMax
            builder.clearNumMax();
            fieldCnt++;
        }
        if (this.getCombat() != 0L) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }  else if (builder.hasCombat()) {
            // 清理Combat
            builder.clearCombat();
            fieldCnt++;
        }
        if (this.member != null) {
            StructClan.Int64ClanMemberMap.Builder tmpBuilder = StructClan.Int64ClanMemberMap.newBuilder();
            final int tmpFieldCnt = this.member.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMember(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMember();
            }
        }  else if (builder.hasMember()) {
            // 清理Member
            builder.clearMember();
            fieldCnt++;
        }
        if (this.applys != null) {
            StructClan.Int64ClanMemberMap.Builder tmpBuilder = StructClan.Int64ClanMemberMap.newBuilder();
            final int tmpFieldCnt = this.applys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setApplys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearApplys();
            }
        }  else if (builder.hasApplys()) {
            // 清理Applys
            builder.clearApplys();
            fieldCnt++;
        }
        if (this.getVersion() != 0L) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }  else if (builder.hasVersion()) {
            // 清理Version
            builder.clearVersion();
            fieldCnt++;
        }
        if (this.additionSys != null) {
            Struct.AdditionSys.Builder tmpBuilder = Struct.AdditionSys.newBuilder();
            final int tmpFieldCnt = this.additionSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setAdditionSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearAdditionSys();
            }
        }  else if (builder.hasAdditionSys()) {
            // 清理AdditionSys
            builder.clearAdditionSys();
            fieldCnt++;
        }
        if (this.devBuffSys != null) {
            StructBattle.DevBuffSys.Builder tmpBuilder = StructBattle.DevBuffSys.newBuilder();
            final int tmpFieldCnt = this.devBuffSys.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setDevBuffSys(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearDevBuffSys();
            }
        }  else if (builder.hasDevBuffSys()) {
            // 清理DevBuffSys
            builder.clearDevBuffSys();
            fieldCnt++;
        }
        if (this.resources != null) {
            Clan.ClanResources.Builder tmpBuilder = Clan.ClanResources.newBuilder();
            final int tmpFieldCnt = this.resources.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResources(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResources();
            }
        }  else if (builder.hasResources()) {
            // 清理Resources
            builder.clearResources();
            fieldCnt++;
        }
        if (this.territoryInfo != null) {
            Clan.TerritoryInfo.Builder tmpBuilder = Clan.TerritoryInfo.newBuilder();
            final int tmpFieldCnt = this.territoryInfo.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTerritoryInfo(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTerritoryInfo();
            }
        }  else if (builder.hasTerritoryInfo()) {
            // 清理TerritoryInfo
            builder.clearTerritoryInfo();
            fieldCnt++;
        }
        if (this.helps != null) {
            Struct.Int64ClanHelpItemMap.Builder tmpBuilder = Struct.Int64ClanHelpItemMap.newBuilder();
            final int tmpFieldCnt = this.helps.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setHelps(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearHelps();
            }
        }  else if (builder.hasHelps()) {
            // 清理Helps
            builder.clearHelps();
            fieldCnt++;
        }
        if (this.giftModel != null) {
            Clan.ClanGiftModel.Builder tmpBuilder = Clan.ClanGiftModel.newBuilder();
            final int tmpFieldCnt = this.giftModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setGiftModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearGiftModel();
            }
        }  else if (builder.hasGiftModel()) {
            // 清理GiftModel
            builder.clearGiftModel();
            fieldCnt++;
        }
        if (this.clanPosMarkMap != null) {
            Struct.Int64PositionMarkInfoMap.Builder tmpBuilder = Struct.Int64PositionMarkInfoMap.newBuilder();
            final int tmpFieldCnt = this.clanPosMarkMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanPosMarkMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanPosMarkMap();
            }
        }  else if (builder.hasClanPosMarkMap()) {
            // 清理ClanPosMarkMap
            builder.clearClanPosMarkMap();
            fieldCnt++;
        }
        if (this.staffModel != null) {
            Clan.ClanStaffModel.Builder tmpBuilder = Clan.ClanStaffModel.newBuilder();
            final int tmpFieldCnt = this.staffModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStaffModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStaffModel();
            }
        }  else if (builder.hasStaffModel()) {
            // 清理StaffModel
            builder.clearStaffModel();
            fieldCnt++;
        }
        if (this.stageModel != null) {
            Struct.ClanStageModel.Builder tmpBuilder = Struct.ClanStageModel.newBuilder();
            final int tmpFieldCnt = this.stageModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStageModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStageModel();
            }
        }  else if (builder.hasStageModel()) {
            // 清理StageModel
            builder.clearStageModel();
            fieldCnt++;
        }
        if (this.logModel != null) {
            Clan.ClanLogModel.Builder tmpBuilder = Clan.ClanLogModel.newBuilder();
            final int tmpFieldCnt = this.logModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setLogModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearLogModel();
            }
        }  else if (builder.hasLogModel()) {
            // 清理LogModel
            builder.clearLogModel();
            fieldCnt++;
        }
        if (this.redDotModel != null) {
            Clan.ClanRedDotModel.Builder tmpBuilder = Clan.ClanRedDotModel.newBuilder();
            final int tmpFieldCnt = this.redDotModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRedDotModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRedDotModel();
            }
        }  else if (builder.hasRedDotModel()) {
            // 清理RedDotModel
            builder.clearRedDotModel();
            fieldCnt++;
        }
        if (this.techModel != null) {
            Clan.ClanTechModel.Builder tmpBuilder = Clan.ClanTechModel.newBuilder();
            final int tmpFieldCnt = this.techModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTechModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTechModel();
            }
        }  else if (builder.hasTechModel()) {
            // 清理TechModel
            builder.clearTechModel();
            fieldCnt++;
        }
        if (this.refreshModel != null) {
            Clan.ClanRefreshModel.Builder tmpBuilder = Clan.ClanRefreshModel.newBuilder();
            final int tmpFieldCnt = this.refreshModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRefreshModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRefreshModel();
            }
        }  else if (builder.hasRefreshModel()) {
            // 清理RefreshModel
            builder.clearRefreshModel();
            fieldCnt++;
        }
        if (this.ClanMemberHistoryModel != null) {
            Clan.ClanMemberHistoryModel.Builder tmpBuilder = Clan.ClanMemberHistoryModel.newBuilder();
            final int tmpFieldCnt = this.ClanMemberHistoryModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setClanMemberHistoryModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearClanMemberHistoryModel();
            }
        }  else if (builder.hasClanMemberHistoryModel()) {
            // 清理ClanMemberHistoryModel
            builder.clearClanMemberHistoryModel();
            fieldCnt++;
        }
        if (this.storeModel != null) {
            Clan.ClanStoreModel.Builder tmpBuilder = Clan.ClanStoreModel.newBuilder();
            final int tmpFieldCnt = this.storeModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setStoreModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearStoreModel();
            }
        }  else if (builder.hasStoreModel()) {
            // 清理StoreModel
            builder.clearStoreModel();
            fieldCnt++;
        }
        if (this.resetQuietPeriod != null) {
            Clan.ClanResetQuietPeriod.Builder tmpBuilder = Clan.ClanResetQuietPeriod.newBuilder();
            final int tmpFieldCnt = this.resetQuietPeriod.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setResetQuietPeriod(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearResetQuietPeriod();
            }
        }  else if (builder.hasResetQuietPeriod()) {
            // 清理ResetQuietPeriod
            builder.clearResetQuietPeriod();
            fieldCnt++;
        }
        if (this.miscModel != null) {
            Clan.ClanMiscModel.Builder tmpBuilder = Clan.ClanMiscModel.newBuilder();
            final int tmpFieldCnt = this.miscModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setMiscModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearMiscModel();
            }
        }  else if (builder.hasMiscModel()) {
            // 清理MiscModel
            builder.clearMiscModel();
            fieldCnt++;
        }
        if (this.inviteModel != null) {
            Clan.ClanInviteModel.Builder tmpBuilder = Clan.ClanInviteModel.newBuilder();
            final int tmpFieldCnt = this.inviteModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setInviteModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearInviteModel();
            }
        }  else if (builder.hasInviteModel()) {
            // 清理InviteModel
            builder.clearInviteModel();
            fieldCnt++;
        }
        if (this.actModel != null) {
            Clan.ClanActivityModel.Builder tmpBuilder = Clan.ClanActivityModel.newBuilder();
            final int tmpFieldCnt = this.actModel.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setActModel(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearActModel();
            }
        }  else if (builder.hasActModel()) {
            // 清理ActModel
            builder.clearActModel();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(ClanEntity.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_ID)) {
            builder.setId(this.getId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_OWNERID)) {
            builder.setOwnerId(this.getOwnerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_BASE) && this.base != null) {
            final boolean needClear = !builder.hasBase();
            final int tmpFieldCnt = this.base.copyChangeToSs(builder.getBaseBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearBase();
            }
        }
        if (this.hasMark(FIELD_INDEX_CREATETIME)) {
            builder.setCreateTime(this.getCreateTime());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUM)) {
            builder.setNum(this.getNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_NUMMAX)) {
            builder.setNumMax(this.getNumMax());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_COMBAT)) {
            builder.setCombat(this.getCombat());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MEMBER) && this.member != null) {
            final boolean needClear = !builder.hasMember();
            final int tmpFieldCnt = this.member.copyChangeToSs(builder.getMemberBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMember();
            }
        }
        if (this.hasMark(FIELD_INDEX_APPLYS) && this.applys != null) {
            final boolean needClear = !builder.hasApplys();
            final int tmpFieldCnt = this.applys.copyChangeToSs(builder.getApplysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearApplys();
            }
        }
        if (this.hasMark(FIELD_INDEX_VERSION)) {
            builder.setVersion(this.getVersion());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            final boolean needClear = !builder.hasAdditionSys();
            final int tmpFieldCnt = this.additionSys.copyChangeToSs(builder.getAdditionSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearAdditionSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            final boolean needClear = !builder.hasDevBuffSys();
            final int tmpFieldCnt = this.devBuffSys.copyChangeToSs(builder.getDevBuffSysBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearDevBuffSys();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            final boolean needClear = !builder.hasResources();
            final int tmpFieldCnt = this.resources.copyChangeToSs(builder.getResourcesBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResources();
            }
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYINFO) && this.territoryInfo != null) {
            final boolean needClear = !builder.hasTerritoryInfo();
            final int tmpFieldCnt = this.territoryInfo.copyChangeToSs(builder.getTerritoryInfoBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTerritoryInfo();
            }
        }
        if (this.hasMark(FIELD_INDEX_HELPS) && this.helps != null) {
            final boolean needClear = !builder.hasHelps();
            final int tmpFieldCnt = this.helps.copyChangeToSs(builder.getHelpsBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearHelps();
            }
        }
        if (this.hasMark(FIELD_INDEX_GIFTMODEL) && this.giftModel != null) {
            final boolean needClear = !builder.hasGiftModel();
            final int tmpFieldCnt = this.giftModel.copyChangeToSs(builder.getGiftModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearGiftModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            final boolean needClear = !builder.hasClanPosMarkMap();
            final int tmpFieldCnt = this.clanPosMarkMap.copyChangeToSs(builder.getClanPosMarkMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanPosMarkMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAFFMODEL) && this.staffModel != null) {
            final boolean needClear = !builder.hasStaffModel();
            final int tmpFieldCnt = this.staffModel.copyChangeToSs(builder.getStaffModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStaffModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STAGEMODEL) && this.stageModel != null) {
            final boolean needClear = !builder.hasStageModel();
            final int tmpFieldCnt = this.stageModel.copyChangeToSs(builder.getStageModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStageModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_LOGMODEL) && this.logModel != null) {
            final boolean needClear = !builder.hasLogModel();
            final int tmpFieldCnt = this.logModel.copyChangeToSs(builder.getLogModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearLogModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            final boolean needClear = !builder.hasRedDotModel();
            final int tmpFieldCnt = this.redDotModel.copyChangeToSs(builder.getRedDotModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRedDotModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            final boolean needClear = !builder.hasTechModel();
            final int tmpFieldCnt = this.techModel.copyChangeToSs(builder.getTechModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTechModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            final boolean needClear = !builder.hasRefreshModel();
            final int tmpFieldCnt = this.refreshModel.copyChangeToSs(builder.getRefreshModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRefreshModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_CLANMEMBERHISTORYMODEL) && this.ClanMemberHistoryModel != null) {
            final boolean needClear = !builder.hasClanMemberHistoryModel();
            final int tmpFieldCnt = this.ClanMemberHistoryModel.copyChangeToSs(builder.getClanMemberHistoryModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearClanMemberHistoryModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_STOREMODEL) && this.storeModel != null) {
            final boolean needClear = !builder.hasStoreModel();
            final int tmpFieldCnt = this.storeModel.copyChangeToSs(builder.getStoreModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearStoreModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_RESETQUIETPERIOD) && this.resetQuietPeriod != null) {
            final boolean needClear = !builder.hasResetQuietPeriod();
            final int tmpFieldCnt = this.resetQuietPeriod.copyChangeToSs(builder.getResetQuietPeriodBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearResetQuietPeriod();
            }
        }
        if (this.hasMark(FIELD_INDEX_MISCMODEL) && this.miscModel != null) {
            final boolean needClear = !builder.hasMiscModel();
            final int tmpFieldCnt = this.miscModel.copyChangeToSs(builder.getMiscModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearMiscModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_INVITEMODEL) && this.inviteModel != null) {
            final boolean needClear = !builder.hasInviteModel();
            final int tmpFieldCnt = this.inviteModel.copyChangeToSs(builder.getInviteModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearInviteModel();
            }
        }
        if (this.hasMark(FIELD_INDEX_ACTMODEL) && this.actModel != null) {
            final boolean needClear = !builder.hasActModel();
            final int tmpFieldCnt = this.actModel.copyChangeToSs(builder.getActModelBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearActModel();
            }
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(ClanEntity proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasId()) {
            this.innerSetId(proto.getId());
        } else {
            this.innerSetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOwnerId()) {
            this.innerSetOwnerId(proto.getOwnerId());
        } else {
            this.innerSetOwnerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasBase()) {
            this.getBase().mergeFromSs(proto.getBase());
        } else {
            if (this.base != null) {
                this.base.mergeFromSs(proto.getBase());
            }
        }
        if (proto.hasCreateTime()) {
            this.innerSetCreateTime(proto.getCreateTime());
        } else {
            this.innerSetCreateTime(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasNum()) {
            this.innerSetNum(proto.getNum());
        } else {
            this.innerSetNum(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasNumMax()) {
            this.innerSetNumMax(proto.getNumMax());
        } else {
            this.innerSetNumMax(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCombat()) {
            this.innerSetCombat(proto.getCombat());
        } else {
            this.innerSetCombat(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasMember()) {
            this.getMember().mergeFromSs(proto.getMember());
        } else {
            if (this.member != null) {
                this.member.mergeFromSs(proto.getMember());
            }
        }
        if (proto.hasApplys()) {
            this.getApplys().mergeFromSs(proto.getApplys());
        } else {
            if (this.applys != null) {
                this.applys.mergeFromSs(proto.getApplys());
            }
        }
        if (proto.hasVersion()) {
            this.innerSetVersion(proto.getVersion());
        } else {
            this.innerSetVersion(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeFromSs(proto.getAdditionSys());
        } else {
            if (this.additionSys != null) {
                this.additionSys.mergeFromSs(proto.getAdditionSys());
            }
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeFromSs(proto.getDevBuffSys());
        } else {
            if (this.devBuffSys != null) {
                this.devBuffSys.mergeFromSs(proto.getDevBuffSys());
            }
        }
        if (proto.hasResources()) {
            this.getResources().mergeFromSs(proto.getResources());
        } else {
            if (this.resources != null) {
                this.resources.mergeFromSs(proto.getResources());
            }
        }
        if (proto.hasTerritoryInfo()) {
            this.getTerritoryInfo().mergeFromSs(proto.getTerritoryInfo());
        } else {
            if (this.territoryInfo != null) {
                this.territoryInfo.mergeFromSs(proto.getTerritoryInfo());
            }
        }
        if (proto.hasHelps()) {
            this.getHelps().mergeFromSs(proto.getHelps());
        } else {
            if (this.helps != null) {
                this.helps.mergeFromSs(proto.getHelps());
            }
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeFromSs(proto.getGiftModel());
        } else {
            if (this.giftModel != null) {
                this.giftModel.mergeFromSs(proto.getGiftModel());
            }
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeFromSs(proto.getClanPosMarkMap());
        } else {
            if (this.clanPosMarkMap != null) {
                this.clanPosMarkMap.mergeFromSs(proto.getClanPosMarkMap());
            }
        }
        if (proto.hasStaffModel()) {
            this.getStaffModel().mergeFromSs(proto.getStaffModel());
        } else {
            if (this.staffModel != null) {
                this.staffModel.mergeFromSs(proto.getStaffModel());
            }
        }
        if (proto.hasStageModel()) {
            this.getStageModel().mergeFromSs(proto.getStageModel());
        } else {
            if (this.stageModel != null) {
                this.stageModel.mergeFromSs(proto.getStageModel());
            }
        }
        if (proto.hasLogModel()) {
            this.getLogModel().mergeFromSs(proto.getLogModel());
        } else {
            if (this.logModel != null) {
                this.logModel.mergeFromSs(proto.getLogModel());
            }
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeFromSs(proto.getRedDotModel());
        } else {
            if (this.redDotModel != null) {
                this.redDotModel.mergeFromSs(proto.getRedDotModel());
            }
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeFromSs(proto.getTechModel());
        } else {
            if (this.techModel != null) {
                this.techModel.mergeFromSs(proto.getTechModel());
            }
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeFromSs(proto.getRefreshModel());
        } else {
            if (this.refreshModel != null) {
                this.refreshModel.mergeFromSs(proto.getRefreshModel());
            }
        }
        if (proto.hasClanMemberHistoryModel()) {
            this.getClanMemberHistoryModel().mergeFromSs(proto.getClanMemberHistoryModel());
        } else {
            if (this.ClanMemberHistoryModel != null) {
                this.ClanMemberHistoryModel.mergeFromSs(proto.getClanMemberHistoryModel());
            }
        }
        if (proto.hasStoreModel()) {
            this.getStoreModel().mergeFromSs(proto.getStoreModel());
        } else {
            if (this.storeModel != null) {
                this.storeModel.mergeFromSs(proto.getStoreModel());
            }
        }
        if (proto.hasResetQuietPeriod()) {
            this.getResetQuietPeriod().mergeFromSs(proto.getResetQuietPeriod());
        } else {
            if (this.resetQuietPeriod != null) {
                this.resetQuietPeriod.mergeFromSs(proto.getResetQuietPeriod());
            }
        }
        if (proto.hasMiscModel()) {
            this.getMiscModel().mergeFromSs(proto.getMiscModel());
        } else {
            if (this.miscModel != null) {
                this.miscModel.mergeFromSs(proto.getMiscModel());
            }
        }
        if (proto.hasInviteModel()) {
            this.getInviteModel().mergeFromSs(proto.getInviteModel());
        } else {
            if (this.inviteModel != null) {
                this.inviteModel.mergeFromSs(proto.getInviteModel());
            }
        }
        if (proto.hasActModel()) {
            this.getActModel().mergeFromSs(proto.getActModel());
        } else {
            if (this.actModel != null) {
                this.actModel.mergeFromSs(proto.getActModel());
            }
        }
        this.markAll();
        return ClanProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(ClanEntity proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasId()) {
            this.setId(proto.getId());
            fieldCnt++;
        }
        if (proto.hasOwnerId()) {
            this.setOwnerId(proto.getOwnerId());
            fieldCnt++;
        }
        if (proto.hasBase()) {
            this.getBase().mergeChangeFromSs(proto.getBase());
            fieldCnt++;
        }
        if (proto.hasCreateTime()) {
            this.setCreateTime(proto.getCreateTime());
            fieldCnt++;
        }
        if (proto.hasNum()) {
            this.setNum(proto.getNum());
            fieldCnt++;
        }
        if (proto.hasNumMax()) {
            this.setNumMax(proto.getNumMax());
            fieldCnt++;
        }
        if (proto.hasCombat()) {
            this.setCombat(proto.getCombat());
            fieldCnt++;
        }
        if (proto.hasMember()) {
            this.getMember().mergeChangeFromSs(proto.getMember());
            fieldCnt++;
        }
        if (proto.hasApplys()) {
            this.getApplys().mergeChangeFromSs(proto.getApplys());
            fieldCnt++;
        }
        if (proto.hasVersion()) {
            this.setVersion(proto.getVersion());
            fieldCnt++;
        }
        if (proto.hasAdditionSys()) {
            this.getAdditionSys().mergeChangeFromSs(proto.getAdditionSys());
            fieldCnt++;
        }
        if (proto.hasDevBuffSys()) {
            this.getDevBuffSys().mergeChangeFromSs(proto.getDevBuffSys());
            fieldCnt++;
        }
        if (proto.hasResources()) {
            this.getResources().mergeChangeFromSs(proto.getResources());
            fieldCnt++;
        }
        if (proto.hasTerritoryInfo()) {
            this.getTerritoryInfo().mergeChangeFromSs(proto.getTerritoryInfo());
            fieldCnt++;
        }
        if (proto.hasHelps()) {
            this.getHelps().mergeChangeFromSs(proto.getHelps());
            fieldCnt++;
        }
        if (proto.hasGiftModel()) {
            this.getGiftModel().mergeChangeFromSs(proto.getGiftModel());
            fieldCnt++;
        }
        if (proto.hasClanPosMarkMap()) {
            this.getClanPosMarkMap().mergeChangeFromSs(proto.getClanPosMarkMap());
            fieldCnt++;
        }
        if (proto.hasStaffModel()) {
            this.getStaffModel().mergeChangeFromSs(proto.getStaffModel());
            fieldCnt++;
        }
        if (proto.hasStageModel()) {
            this.getStageModel().mergeChangeFromSs(proto.getStageModel());
            fieldCnt++;
        }
        if (proto.hasLogModel()) {
            this.getLogModel().mergeChangeFromSs(proto.getLogModel());
            fieldCnt++;
        }
        if (proto.hasRedDotModel()) {
            this.getRedDotModel().mergeChangeFromSs(proto.getRedDotModel());
            fieldCnt++;
        }
        if (proto.hasTechModel()) {
            this.getTechModel().mergeChangeFromSs(proto.getTechModel());
            fieldCnt++;
        }
        if (proto.hasRefreshModel()) {
            this.getRefreshModel().mergeChangeFromSs(proto.getRefreshModel());
            fieldCnt++;
        }
        if (proto.hasClanMemberHistoryModel()) {
            this.getClanMemberHistoryModel().mergeChangeFromSs(proto.getClanMemberHistoryModel());
            fieldCnt++;
        }
        if (proto.hasStoreModel()) {
            this.getStoreModel().mergeChangeFromSs(proto.getStoreModel());
            fieldCnt++;
        }
        if (proto.hasResetQuietPeriod()) {
            this.getResetQuietPeriod().mergeChangeFromSs(proto.getResetQuietPeriod());
            fieldCnt++;
        }
        if (proto.hasMiscModel()) {
            this.getMiscModel().mergeChangeFromSs(proto.getMiscModel());
            fieldCnt++;
        }
        if (proto.hasInviteModel()) {
            this.getInviteModel().mergeChangeFromSs(proto.getInviteModel());
            fieldCnt++;
        }
        if (proto.hasActModel()) {
            this.getActModel().mergeChangeFromSs(proto.getActModel());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        ClanEntity.Builder builder = ClanEntity.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_BASE) && this.base != null) {
            this.base.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MEMBER) && this.member != null) {
            this.member.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_APPLYS) && this.applys != null) {
            this.applys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ADDITIONSYS) && this.additionSys != null) {
            this.additionSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_DEVBUFFSYS) && this.devBuffSys != null) {
            this.devBuffSys.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESOURCES) && this.resources != null) {
            this.resources.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TERRITORYINFO) && this.territoryInfo != null) {
            this.territoryInfo.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_HELPS) && this.helps != null) {
            this.helps.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_GIFTMODEL) && this.giftModel != null) {
            this.giftModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANPOSMARKMAP) && this.clanPosMarkMap != null) {
            this.clanPosMarkMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STAFFMODEL) && this.staffModel != null) {
            this.staffModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STAGEMODEL) && this.stageModel != null) {
            this.stageModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_LOGMODEL) && this.logModel != null) {
            this.logModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REDDOTMODEL) && this.redDotModel != null) {
            this.redDotModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TECHMODEL) && this.techModel != null) {
            this.techModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_REFRESHMODEL) && this.refreshModel != null) {
            this.refreshModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CLANMEMBERHISTORYMODEL) && this.ClanMemberHistoryModel != null) {
            this.ClanMemberHistoryModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_STOREMODEL) && this.storeModel != null) {
            this.storeModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RESETQUIETPERIOD) && this.resetQuietPeriod != null) {
            this.resetQuietPeriod.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_MISCMODEL) && this.miscModel != null) {
            this.miscModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_INVITEMODEL) && this.inviteModel != null) {
            this.inviteModel.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ACTMODEL) && this.actModel != null) {
            this.actModel.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.base != null) {
            this.base.markAll();
        }
        if (this.member != null) {
            this.member.markAll();
        }
        if (this.applys != null) {
            this.applys.markAll();
        }
        if (this.additionSys != null) {
            this.additionSys.markAll();
        }
        if (this.devBuffSys != null) {
            this.devBuffSys.markAll();
        }
        if (this.resources != null) {
            this.resources.markAll();
        }
        if (this.territoryInfo != null) {
            this.territoryInfo.markAll();
        }
        if (this.helps != null) {
            this.helps.markAll();
        }
        if (this.giftModel != null) {
            this.giftModel.markAll();
        }
        if (this.clanPosMarkMap != null) {
            this.clanPosMarkMap.markAll();
        }
        if (this.staffModel != null) {
            this.staffModel.markAll();
        }
        if (this.stageModel != null) {
            this.stageModel.markAll();
        }
        if (this.logModel != null) {
            this.logModel.markAll();
        }
        if (this.redDotModel != null) {
            this.redDotModel.markAll();
        }
        if (this.techModel != null) {
            this.techModel.markAll();
        }
        if (this.refreshModel != null) {
            this.refreshModel.markAll();
        }
        if (this.ClanMemberHistoryModel != null) {
            this.ClanMemberHistoryModel.markAll();
        }
        if (this.storeModel != null) {
            this.storeModel.markAll();
        }
        if (this.resetQuietPeriod != null) {
            this.resetQuietPeriod.markAll();
        }
        if (this.miscModel != null) {
            this.miscModel.markAll();
        }
        if (this.inviteModel != null) {
            this.inviteModel.markAll();
        }
        if (this.actModel != null) {
            this.actModel.markAll();
        }
        this.markAllBits();
        if (this.listener != null) {
            this.listener.trigger("ClanProp");
        }
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            if (this.listener != null) {
                this.listener.trigger("ClanProp");
            }
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof ClanProp)) {
            return false;
        }
        final ClanProp otherNode = (ClanProp) node;
        if (this.id != otherNode.id) {
            return false;
        }
        if (this.ownerId != otherNode.ownerId) {
            return false;
        }
        if (!this.getBase().compareDataTo(otherNode.getBase())) {
            return false;
        }
        if (this.createTime != otherNode.createTime) {
            return false;
        }
        if (this.num != otherNode.num) {
            return false;
        }
        if (this.numMax != otherNode.numMax) {
            return false;
        }
        if (this.combat != otherNode.combat) {
            return false;
        }
        if (!this.getMember().compareDataTo(otherNode.getMember())) {
            return false;
        }
        if (!this.getApplys().compareDataTo(otherNode.getApplys())) {
            return false;
        }
        if (this.version != otherNode.version) {
            return false;
        }
        if (!this.getAdditionSys().compareDataTo(otherNode.getAdditionSys())) {
            return false;
        }
        if (!this.getDevBuffSys().compareDataTo(otherNode.getDevBuffSys())) {
            return false;
        }
        if (!this.getResources().compareDataTo(otherNode.getResources())) {
            return false;
        }
        if (!this.getTerritoryInfo().compareDataTo(otherNode.getTerritoryInfo())) {
            return false;
        }
        if (!this.getHelps().compareDataTo(otherNode.getHelps())) {
            return false;
        }
        if (!this.getGiftModel().compareDataTo(otherNode.getGiftModel())) {
            return false;
        }
        if (!this.getClanPosMarkMap().compareDataTo(otherNode.getClanPosMarkMap())) {
            return false;
        }
        if (!this.getStaffModel().compareDataTo(otherNode.getStaffModel())) {
            return false;
        }
        if (!this.getStageModel().compareDataTo(otherNode.getStageModel())) {
            return false;
        }
        if (!this.getLogModel().compareDataTo(otherNode.getLogModel())) {
            return false;
        }
        if (!this.getRedDotModel().compareDataTo(otherNode.getRedDotModel())) {
            return false;
        }
        if (!this.getTechModel().compareDataTo(otherNode.getTechModel())) {
            return false;
        }
        if (!this.getRefreshModel().compareDataTo(otherNode.getRefreshModel())) {
            return false;
        }
        if (!this.getClanMemberHistoryModel().compareDataTo(otherNode.getClanMemberHistoryModel())) {
            return false;
        }
        if (!this.getStoreModel().compareDataTo(otherNode.getStoreModel())) {
            return false;
        }
        if (!this.getResetQuietPeriod().compareDataTo(otherNode.getResetQuietPeriod())) {
            return false;
        }
        if (!this.getMiscModel().compareDataTo(otherNode.getMiscModel())) {
            return false;
        }
        if (!this.getInviteModel().compareDataTo(otherNode.getInviteModel())) {
            return false;
        }
        if (!this.getActModel().compareDataTo(otherNode.getActModel())) {
            return false;
        }
        return true;
    }

    @Override
    public PropertyChangeListener getListener() {
        return this.listener;
    }

    @Override
    public void setListener(PropertyChangeListener listener) {
        if (this.listener != null) {
            throw new RuntimeException("already has listener, " + getClass().getSimpleName());
        }
        this.listener = listener;
    }

    public static ClanProp of(ClanEntity fullAttrDb, ClanEntity changeAttrDb) {
        ClanProp prop = new ClanProp();
        prop.mergeFromDb(fullAttrDb);
        prop.mergeChangeFromDb(changeAttrDb);
        prop.unMarkAll();
        return prop;
    }

    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 35;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
package com.yorha.game.gen.prop;


import com.yorha.gemini.props.AbstractContainerElementNode;
import com.yorha.gemini.props.AbstractPropNode;
import com.yorha.gemini.props.IMarkContainer;
import com.yorha.gemini.props.Constant;
import com.yorha.proto.CommonEnum.*;
import com.yorha.proto.StructPlayer.RallyInfo;
import com.yorha.proto.Basic;
import com.yorha.proto.Struct;
import com.yorha.proto.StructPlayer;
import com.yorha.proto.StructPlayerPB.RallyInfoPB;
import com.yorha.proto.BasicPB;
import com.yorha.proto.StructPB;
import com.yorha.proto.StructPlayerPB;


/**
 * <AUTHOR> auto gen
 */
public class RallyInfoProp extends AbstractContainerElementNode<Long> {

    public static final int FIELD_INDEX_RALLYID = 0;
    public static final int FIELD_INDEX_ORGANIZERID = 1;
    public static final int FIELD_INDEX_ORGANIZERCLANSHORTNAME = 2;
    public static final int FIELD_INDEX_ORGANIZERCITYPOS = 3;
    public static final int FIELD_INDEX_ORGANIZERCITYID = 4;
    public static final int FIELD_INDEX_TARGETID = 5;
    public static final int FIELD_INDEX_TARGETCLANSHORTNAME = 6;
    public static final int FIELD_INDEX_TARGETPOS = 7;
    public static final int FIELD_INDEX_TARGETTYPE = 8;
    public static final int FIELD_INDEX_MAXSOLDIERNUM = 9;
    public static final int FIELD_INDEX_CURSOLDIERNUM = 10;
    public static final int FIELD_INDEX_RALLYSTATE = 11;
    public static final int FIELD_INDEX_STATESTARTTS = 12;
    public static final int FIELD_INDEX_STATEENDTS = 13;
    public static final int FIELD_INDEX_RALLYARMYINFOMAP = 14;
    public static final int FIELD_INDEX_RECOMMENDSOLDIERTYPELIST = 15;
    public static final int FIELD_INDEX_ORGANIZERCLANID = 16;
    public static final int FIELD_INDEX_TARGETCLANID = 17;
    public static final int FIELD_INDEX_TARGETTEMPLATEID = 18;
    public static final int FIELD_INDEX_CURSOLDIERTYPELIST = 19;
    public static final int FIELD_INDEX_ORGANIZERCARDHEAD = 20;
    public static final int FIELD_INDEX_TARGETCARDHEAD = 21;
    public static final int FIELD_INDEX_BERALLYID = 22;

    public static final int FIELD_COUNT = 23;

    private long markBits0 = 0L;

    private long rallyId = Constant.DEFAULT_LONG_VALUE;
    private long organizerId = Constant.DEFAULT_LONG_VALUE;
    private String organizerClanShortName = Constant.DEFAULT_STR_VALUE;
    private PointProp organizerCityPos = null;
    private long organizerCityId = Constant.DEFAULT_LONG_VALUE;
    private long targetId = Constant.DEFAULT_LONG_VALUE;
    private String targetClanShortName = Constant.DEFAULT_STR_VALUE;
    private PointProp targetPos = null;
    private RallyTargetType targetType = RallyTargetType.forNumber(0);
    private long maxSoldierNum = Constant.DEFAULT_LONG_VALUE;
    private long curSoldierNum = Constant.DEFAULT_LONG_VALUE;
    private RallyState rallyState = RallyState.forNumber(0);
    private long stateStartTs = Constant.DEFAULT_LONG_VALUE;
    private long stateEndTs = Constant.DEFAULT_LONG_VALUE;
    private Int64RallyArmyInfoMapProp rallyArmyInfoMap = null;
    private Int32ListProp recommendSoldierTypeList = null;
    private long organizerClanId = Constant.DEFAULT_LONG_VALUE;
    private long targetClanId = Constant.DEFAULT_LONG_VALUE;
    private int targetTemplateId = Constant.DEFAULT_INT_VALUE;
    private Int32ListProp curSoldierTypeList = null;
    private PlayerCardHeadProp organizerCardHead = null;
    private PlayerCardHeadProp targetCardHead = null;
    private long beRallyId = Constant.DEFAULT_LONG_VALUE;

    public RallyInfoProp() {
        super(null, 0, FIELD_COUNT);
    }

    public RallyInfoProp(final AbstractPropNode parent, final int fieldIndex) {
        super(parent, fieldIndex, FIELD_COUNT);
    }

    /**
     * get rallyId
     *
     * @return rallyId value
     */
    public long getRallyId() {
        return this.rallyId;
    }

    /**
     * set rallyId && set marked
     *
     * @param rallyId new value
     * @return current object
     */
    public RallyInfoProp setRallyId(long rallyId) {
        if (this.rallyId != rallyId) {
            this.mark(FIELD_INDEX_RALLYID);
            this.rallyId = rallyId;
        }
        return this;
    }

    /**
     * inner set rallyId
     *
     * @param rallyId new value
     */
    private void innerSetRallyId(long rallyId) {
        this.rallyId = rallyId;
    }

    /**
     * get organizerId
     *
     * @return organizerId value
     */
    public long getOrganizerId() {
        return this.organizerId;
    }

    /**
     * set organizerId && set marked
     *
     * @param organizerId new value
     * @return current object
     */
    public RallyInfoProp setOrganizerId(long organizerId) {
        if (this.organizerId != organizerId) {
            this.mark(FIELD_INDEX_ORGANIZERID);
            this.organizerId = organizerId;
        }
        return this;
    }

    /**
     * inner set organizerId
     *
     * @param organizerId new value
     */
    private void innerSetOrganizerId(long organizerId) {
        this.organizerId = organizerId;
    }

    /**
     * get organizerClanShortName
     *
     * @return organizerClanShortName value
     */
    public String getOrganizerClanShortName() {
        return this.organizerClanShortName;
    }

    /**
     * set organizerClanShortName && set marked
     *
     * @param organizerClanShortName new value
     * @return current object
     */
    public RallyInfoProp setOrganizerClanShortName(String organizerClanShortName) {
        if (organizerClanShortName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.organizerClanShortName, organizerClanShortName)) {
            this.mark(FIELD_INDEX_ORGANIZERCLANSHORTNAME);
            this.organizerClanShortName = organizerClanShortName;
        }
        return this;
    }

    /**
     * inner set organizerClanShortName
     *
     * @param organizerClanShortName new value
     */
    private void innerSetOrganizerClanShortName(String organizerClanShortName) {
        this.organizerClanShortName = organizerClanShortName;
    }

    /**
     * get organizerCityPos
     *
     * @return organizerCityPos value
     */
    public PointProp getOrganizerCityPos() {
        if (this.organizerCityPos == null) {
            this.organizerCityPos = new PointProp(this, FIELD_INDEX_ORGANIZERCITYPOS);
        }
        return this.organizerCityPos;
    }

    /**
     * get organizerCityId
     *
     * @return organizerCityId value
     */
    public long getOrganizerCityId() {
        return this.organizerCityId;
    }

    /**
     * set organizerCityId && set marked
     *
     * @param organizerCityId new value
     * @return current object
     */
    public RallyInfoProp setOrganizerCityId(long organizerCityId) {
        if (this.organizerCityId != organizerCityId) {
            this.mark(FIELD_INDEX_ORGANIZERCITYID);
            this.organizerCityId = organizerCityId;
        }
        return this;
    }

    /**
     * inner set organizerCityId
     *
     * @param organizerCityId new value
     */
    private void innerSetOrganizerCityId(long organizerCityId) {
        this.organizerCityId = organizerCityId;
    }

    /**
     * get targetId
     *
     * @return targetId value
     */
    public long getTargetId() {
        return this.targetId;
    }

    /**
     * set targetId && set marked
     *
     * @param targetId new value
     * @return current object
     */
    public RallyInfoProp setTargetId(long targetId) {
        if (this.targetId != targetId) {
            this.mark(FIELD_INDEX_TARGETID);
            this.targetId = targetId;
        }
        return this;
    }

    /**
     * inner set targetId
     *
     * @param targetId new value
     */
    private void innerSetTargetId(long targetId) {
        this.targetId = targetId;
    }

    /**
     * get targetClanShortName
     *
     * @return targetClanShortName value
     */
    public String getTargetClanShortName() {
        return this.targetClanShortName;
    }

    /**
     * set targetClanShortName && set marked
     *
     * @param targetClanShortName new value
     * @return current object
     */
    public RallyInfoProp setTargetClanShortName(String targetClanShortName) {
        if (targetClanShortName == null) {
            throw new NullPointerException();
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetClanShortName, targetClanShortName)) {
            this.mark(FIELD_INDEX_TARGETCLANSHORTNAME);
            this.targetClanShortName = targetClanShortName;
        }
        return this;
    }

    /**
     * inner set targetClanShortName
     *
     * @param targetClanShortName new value
     */
    private void innerSetTargetClanShortName(String targetClanShortName) {
        this.targetClanShortName = targetClanShortName;
    }

    /**
     * get targetPos
     *
     * @return targetPos value
     */
    public PointProp getTargetPos() {
        if (this.targetPos == null) {
            this.targetPos = new PointProp(this, FIELD_INDEX_TARGETPOS);
        }
        return this.targetPos;
    }

    /**
     * get targetType
     *
     * @return targetType value
     */
    public RallyTargetType getTargetType() {
        return this.targetType;
    }

    /**
     * set targetType && set marked
     *
     * @param targetType new value
     * @return current object
     */
    public RallyInfoProp setTargetType(RallyTargetType targetType) {
        if (targetType == null) {
            throw new NullPointerException();
        }
        if (this.targetType != targetType) {
            this.mark(FIELD_INDEX_TARGETTYPE);
            this.targetType = targetType;
        }
        return this;
    }

    /**
     * inner set targetType
     *
     * @param targetType new value
     */
    private void innerSetTargetType(RallyTargetType targetType) {
        this.targetType = targetType;
    }

    /**
     * get maxSoldierNum
     *
     * @return maxSoldierNum value
     */
    public long getMaxSoldierNum() {
        return this.maxSoldierNum;
    }

    /**
     * set maxSoldierNum && set marked
     *
     * @param maxSoldierNum new value
     * @return current object
     */
    public RallyInfoProp setMaxSoldierNum(long maxSoldierNum) {
        if (this.maxSoldierNum != maxSoldierNum) {
            this.mark(FIELD_INDEX_MAXSOLDIERNUM);
            this.maxSoldierNum = maxSoldierNum;
        }
        return this;
    }

    /**
     * inner set maxSoldierNum
     *
     * @param maxSoldierNum new value
     */
    private void innerSetMaxSoldierNum(long maxSoldierNum) {
        this.maxSoldierNum = maxSoldierNum;
    }

    /**
     * get curSoldierNum
     *
     * @return curSoldierNum value
     */
    public long getCurSoldierNum() {
        return this.curSoldierNum;
    }

    /**
     * set curSoldierNum && set marked
     *
     * @param curSoldierNum new value
     * @return current object
     */
    public RallyInfoProp setCurSoldierNum(long curSoldierNum) {
        if (this.curSoldierNum != curSoldierNum) {
            this.mark(FIELD_INDEX_CURSOLDIERNUM);
            this.curSoldierNum = curSoldierNum;
        }
        return this;
    }

    /**
     * inner set curSoldierNum
     *
     * @param curSoldierNum new value
     */
    private void innerSetCurSoldierNum(long curSoldierNum) {
        this.curSoldierNum = curSoldierNum;
    }

    /**
     * get rallyState
     *
     * @return rallyState value
     */
    public RallyState getRallyState() {
        return this.rallyState;
    }

    /**
     * set rallyState && set marked
     *
     * @param rallyState new value
     * @return current object
     */
    public RallyInfoProp setRallyState(RallyState rallyState) {
        if (rallyState == null) {
            throw new NullPointerException();
        }
        if (this.rallyState != rallyState) {
            this.mark(FIELD_INDEX_RALLYSTATE);
            this.rallyState = rallyState;
        }
        return this;
    }

    /**
     * inner set rallyState
     *
     * @param rallyState new value
     */
    private void innerSetRallyState(RallyState rallyState) {
        this.rallyState = rallyState;
    }

    /**
     * get stateStartTs
     *
     * @return stateStartTs value
     */
    public long getStateStartTs() {
        return this.stateStartTs;
    }

    /**
     * set stateStartTs && set marked
     *
     * @param stateStartTs new value
     * @return current object
     */
    public RallyInfoProp setStateStartTs(long stateStartTs) {
        if (this.stateStartTs != stateStartTs) {
            this.mark(FIELD_INDEX_STATESTARTTS);
            this.stateStartTs = stateStartTs;
        }
        return this;
    }

    /**
     * inner set stateStartTs
     *
     * @param stateStartTs new value
     */
    private void innerSetStateStartTs(long stateStartTs) {
        this.stateStartTs = stateStartTs;
    }

    /**
     * get stateEndTs
     *
     * @return stateEndTs value
     */
    public long getStateEndTs() {
        return this.stateEndTs;
    }

    /**
     * set stateEndTs && set marked
     *
     * @param stateEndTs new value
     * @return current object
     */
    public RallyInfoProp setStateEndTs(long stateEndTs) {
        if (this.stateEndTs != stateEndTs) {
            this.mark(FIELD_INDEX_STATEENDTS);
            this.stateEndTs = stateEndTs;
        }
        return this;
    }

    /**
     * inner set stateEndTs
     *
     * @param stateEndTs new value
     */
    private void innerSetStateEndTs(long stateEndTs) {
        this.stateEndTs = stateEndTs;
    }

    /**
     * get rallyArmyInfoMap
     *
     * @return rallyArmyInfoMap value
     */
    public Int64RallyArmyInfoMapProp getRallyArmyInfoMap() {
        if (this.rallyArmyInfoMap == null) {
            this.rallyArmyInfoMap = new Int64RallyArmyInfoMapProp(this, FIELD_INDEX_RALLYARMYINFOMAP);
        }
        return this.rallyArmyInfoMap;
    }

    
    /**
     * Associates the specified value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the specified value
     *
     * @param v value
     */
    public void putRallyArmyInfoMapV(RallyArmyInfoProp v) {
        this.getRallyArmyInfoMap().put(v.getArmyId(), v);
    }

    /**
     * Add empty value with the specified key in this map
     * If the map previously contained a mapping for
     * the key, the old value is replaced by the empty value
     *
     * @param k specified key
     * @return empty value
     */
    public RallyArmyInfoProp addEmptyRallyArmyInfoMap(Long k) {
        return this.getRallyArmyInfoMap().addEmptyValue(k);
    }

    /**
     * Get size of the map
     *
     * @return size
     */
    public int getRallyArmyInfoMapSize() {
        if (this.rallyArmyInfoMap == null) {
            return 0;
        }
        return this.rallyArmyInfoMap.size();
    }

    /**
     * Get is empty map
     *
     * @return true if the map is empty
     */
    public boolean isRallyArmyInfoMapEmpty() {
        if (this.rallyArmyInfoMap == null) {
            return true;
        }
        return this.rallyArmyInfoMap.isEmpty();
    }

    /**
     * Returns the value to which the specified key is mapped,
     * or {@code null} if this map contains no mapping for the key.
     *
     * @param k specified key
     * @return value if success or null fail
     */
    public RallyArmyInfoProp getRallyArmyInfoMapV(Long k) {
        if (this.rallyArmyInfoMap == null || !this.rallyArmyInfoMap.containsKey(k)) {
            return null;
        }
        return this.rallyArmyInfoMap.get(k);
    }

    /**
     * Removes all of the mappings from this map (optional operation).
     * The map will be empty after this call returns.
     */
    public void clearRallyArmyInfoMap() {
        if (this.rallyArmyInfoMap != null) {
            this.rallyArmyInfoMap.clear();
        }
    } 
    
    /**
     * Removes the mapping for a key from this map if it is present
     * (optional operation).   More formally, if this map contains a mapping
     * from key {@code k} to value {@code v} such that
     * {@code java.util.Objects.equals(key, k)}, that mapping
     * is removed.  (The map can contain at most one such mapping.)
     *
     * @param k specified key
     */
    public void removeRallyArmyInfoMapV(Long k) {
        if (this.rallyArmyInfoMap != null) {
            this.rallyArmyInfoMap.remove(k);
        }
    }
    /**
     * get recommendSoldierTypeList
     *
     * @return recommendSoldierTypeList value
     */
    public Int32ListProp getRecommendSoldierTypeList() {
        if (this.recommendSoldierTypeList == null) {
            this.recommendSoldierTypeList = new Int32ListProp(this, FIELD_INDEX_RECOMMENDSOLDIERTYPELIST);
        }
        return this.recommendSoldierTypeList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addRecommendSoldierTypeList(Integer v) {
        this.getRecommendSoldierTypeList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getRecommendSoldierTypeListIndex(int index) {
        return this.getRecommendSoldierTypeList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeRecommendSoldierTypeList(Integer v) {
        if (this.recommendSoldierTypeList == null) {
            return null;
        }
        if(this.recommendSoldierTypeList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getRecommendSoldierTypeListSize() {
        if (this.recommendSoldierTypeList == null) {
            return 0;
        }
        return this.recommendSoldierTypeList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isRecommendSoldierTypeListEmpty() {
        if (this.recommendSoldierTypeList == null) {
            return true;
        }
        return this.getRecommendSoldierTypeList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearRecommendSoldierTypeList() {
        this.getRecommendSoldierTypeList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeRecommendSoldierTypeListIndex(int index) {
        return this.getRecommendSoldierTypeList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setRecommendSoldierTypeListIndex(int index, Integer v) {
        return this.getRecommendSoldierTypeList().set(index, v);
    }
    /**
     * get organizerClanId
     *
     * @return organizerClanId value
     */
    public long getOrganizerClanId() {
        return this.organizerClanId;
    }

    /**
     * set organizerClanId && set marked
     *
     * @param organizerClanId new value
     * @return current object
     */
    public RallyInfoProp setOrganizerClanId(long organizerClanId) {
        if (this.organizerClanId != organizerClanId) {
            this.mark(FIELD_INDEX_ORGANIZERCLANID);
            this.organizerClanId = organizerClanId;
        }
        return this;
    }

    /**
     * inner set organizerClanId
     *
     * @param organizerClanId new value
     */
    private void innerSetOrganizerClanId(long organizerClanId) {
        this.organizerClanId = organizerClanId;
    }

    /**
     * get targetClanId
     *
     * @return targetClanId value
     */
    public long getTargetClanId() {
        return this.targetClanId;
    }

    /**
     * set targetClanId && set marked
     *
     * @param targetClanId new value
     * @return current object
     */
    public RallyInfoProp setTargetClanId(long targetClanId) {
        if (this.targetClanId != targetClanId) {
            this.mark(FIELD_INDEX_TARGETCLANID);
            this.targetClanId = targetClanId;
        }
        return this;
    }

    /**
     * inner set targetClanId
     *
     * @param targetClanId new value
     */
    private void innerSetTargetClanId(long targetClanId) {
        this.targetClanId = targetClanId;
    }

    /**
     * get targetTemplateId
     *
     * @return targetTemplateId value
     */
    public int getTargetTemplateId() {
        return this.targetTemplateId;
    }

    /**
     * set targetTemplateId && set marked
     *
     * @param targetTemplateId new value
     * @return current object
     */
    public RallyInfoProp setTargetTemplateId(int targetTemplateId) {
        if (this.targetTemplateId != targetTemplateId) {
            this.mark(FIELD_INDEX_TARGETTEMPLATEID);
            this.targetTemplateId = targetTemplateId;
        }
        return this;
    }

    /**
     * inner set targetTemplateId
     *
     * @param targetTemplateId new value
     */
    private void innerSetTargetTemplateId(int targetTemplateId) {
        this.targetTemplateId = targetTemplateId;
    }

    /**
     * get curSoldierTypeList
     *
     * @return curSoldierTypeList value
     */
    public Int32ListProp getCurSoldierTypeList() {
        if (this.curSoldierTypeList == null) {
            this.curSoldierTypeList = new Int32ListProp(this, FIELD_INDEX_CURSOLDIERTYPELIST);
        }
        return this.curSoldierTypeList;
    }


    /**
     * append v to list
     *
     * @param v new element
     */
    public void addCurSoldierTypeList(Integer v) {
        this.getCurSoldierTypeList().add(v);
    }

    /**
     * get list element at the position index
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer getCurSoldierTypeListIndex(int index) {
        return this.getCurSoldierTypeList().get(index);
    }

    /**
     * remove the specified element in this list
     *
     * @param v element
     * @return v if success or null by fail
     */
    public Integer removeCurSoldierTypeList(Integer v) {
        if (this.curSoldierTypeList == null) {
            return null;
        }
        if(this.curSoldierTypeList.remove(v)) {
            return v;
        }
        return null;
    }

    /**
     * get list size
     *
     * @return list size
     */
    public int getCurSoldierTypeListSize() {
        if (this.curSoldierTypeList == null) {
            return 0;
        }
        return this.curSoldierTypeList.size();
    }

    /**
     * get is a empty list
     *
     * @return true if empty
     */
    public boolean isCurSoldierTypeListEmpty() {
        if (this.curSoldierTypeList == null) {
            return true;
        }
        return this.getCurSoldierTypeList().isEmpty();
    }

    /**
     * clear list
     */
    public void clearCurSoldierTypeList() {
        this.getCurSoldierTypeList().clear();
    }

    /**
     * Remove the element at the specified position in the list
     *
     * @param index the position index
     * @return element if success or null by fail
     */
    public Integer removeCurSoldierTypeListIndex(int index) {
        return this.getCurSoldierTypeList().remove(index);
    }

    /**
     * Set the specified element at the specified position in this list
     *
     * @param index the position index
     * @param v new element
     * @return elder element
     */
    public Integer setCurSoldierTypeListIndex(int index, Integer v) {
        return this.getCurSoldierTypeList().set(index, v);
    }
    /**
     * get organizerCardHead
     *
     * @return organizerCardHead value
     */
    public PlayerCardHeadProp getOrganizerCardHead() {
        if (this.organizerCardHead == null) {
            this.organizerCardHead = new PlayerCardHeadProp(this, FIELD_INDEX_ORGANIZERCARDHEAD);
        }
        return this.organizerCardHead;
    }

    /**
     * get targetCardHead
     *
     * @return targetCardHead value
     */
    public PlayerCardHeadProp getTargetCardHead() {
        if (this.targetCardHead == null) {
            this.targetCardHead = new PlayerCardHeadProp(this, FIELD_INDEX_TARGETCARDHEAD);
        }
        return this.targetCardHead;
    }

    /**
     * get beRallyId
     *
     * @return beRallyId value
     */
    public long getBeRallyId() {
        return this.beRallyId;
    }

    /**
     * set beRallyId && set marked
     *
     * @param beRallyId new value
     * @return current object
     */
    public RallyInfoProp setBeRallyId(long beRallyId) {
        if (this.beRallyId != beRallyId) {
            this.mark(FIELD_INDEX_BERALLYID);
            this.beRallyId = beRallyId;
        }
        return this;
    }

    /**
     * inner set beRallyId
     *
     * @param beRallyId new value
     */
    private void innerSetBeRallyId(long beRallyId) {
        this.beRallyId = beRallyId;
    }


    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RallyInfoPB.Builder getCopyCsBuilder() {
        final RallyInfoPB.Builder builder = RallyInfoPB.newBuilder();
        this.copyToCs(builder);
        return builder;
    }

    /**
     * copy to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
    public int copyToCs(RallyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRallyId() != 0L) {
            builder.setRallyId(this.getRallyId());
            fieldCnt++;
        }  else if (builder.hasRallyId()) {
            // 清理RallyId
            builder.clearRallyId();
            fieldCnt++;
        }
        if (this.getOrganizerId() != 0L) {
            builder.setOrganizerId(this.getOrganizerId());
            fieldCnt++;
        }  else if (builder.hasOrganizerId()) {
            // 清理OrganizerId
            builder.clearOrganizerId();
            fieldCnt++;
        }
        if (!this.getOrganizerClanShortName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOrganizerClanShortName(this.getOrganizerClanShortName());
            fieldCnt++;
        }  else if (builder.hasOrganizerClanShortName()) {
            // 清理OrganizerClanShortName
            builder.clearOrganizerClanShortName();
            fieldCnt++;
        }
        if (this.organizerCityPos != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.organizerCityPos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOrganizerCityPos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOrganizerCityPos();
            }
        }  else if (builder.hasOrganizerCityPos()) {
            // 清理OrganizerCityPos
            builder.clearOrganizerCityPos();
            fieldCnt++;
        }
        if (this.getOrganizerCityId() != 0L) {
            builder.setOrganizerCityId(this.getOrganizerCityId());
            fieldCnt++;
        }  else if (builder.hasOrganizerCityId()) {
            // 清理OrganizerCityId
            builder.clearOrganizerCityId();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (!this.getTargetClanShortName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }  else if (builder.hasTargetClanShortName()) {
            // 清理TargetClanShortName
            builder.clearTargetClanShortName();
            fieldCnt++;
        }
        if (this.targetPos != null) {
            StructPB.PointPB.Builder tmpBuilder = StructPB.PointPB.newBuilder();
            final int tmpFieldCnt = this.targetPos.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTargetPos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTargetPos();
            }
        }  else if (builder.hasTargetPos()) {
            // 清理TargetPos
            builder.clearTargetPos();
            fieldCnt++;
        }
        if (this.getTargetType() != RallyTargetType.forNumber(0)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (this.getMaxSoldierNum() != 0L) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }  else if (builder.hasMaxSoldierNum()) {
            // 清理MaxSoldierNum
            builder.clearMaxSoldierNum();
            fieldCnt++;
        }
        if (this.getCurSoldierNum() != 0L) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }  else if (builder.hasCurSoldierNum()) {
            // 清理CurSoldierNum
            builder.clearCurSoldierNum();
            fieldCnt++;
        }
        if (this.getRallyState() != RallyState.forNumber(0)) {
            builder.setRallyState(this.getRallyState());
            fieldCnt++;
        }  else if (builder.hasRallyState()) {
            // 清理RallyState
            builder.clearRallyState();
            fieldCnt++;
        }
        if (this.getStateStartTs() != 0L) {
            builder.setStateStartTs(this.getStateStartTs());
            fieldCnt++;
        }  else if (builder.hasStateStartTs()) {
            // 清理StateStartTs
            builder.clearStateStartTs();
            fieldCnt++;
        }
        if (this.getStateEndTs() != 0L) {
            builder.setStateEndTs(this.getStateEndTs());
            fieldCnt++;
        }  else if (builder.hasStateEndTs()) {
            // 清理StateEndTs
            builder.clearStateEndTs();
            fieldCnt++;
        }
        if (this.rallyArmyInfoMap != null) {
            StructPlayerPB.Int64RallyArmyInfoMapPB.Builder tmpBuilder = StructPlayerPB.Int64RallyArmyInfoMapPB.newBuilder();
            final int tmpFieldCnt = this.rallyArmyInfoMap.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyArmyInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyArmyInfoMap();
            }
        }  else if (builder.hasRallyArmyInfoMap()) {
            // 清理RallyArmyInfoMap
            builder.clearRallyArmyInfoMap();
            fieldCnt++;
        }
        if (this.recommendSoldierTypeList != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecommendSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecommendSoldierTypeList();
            }
        }  else if (builder.hasRecommendSoldierTypeList()) {
            // 清理RecommendSoldierTypeList
            builder.clearRecommendSoldierTypeList();
            fieldCnt++;
        }
        if (this.getOrganizerClanId() != 0L) {
            builder.setOrganizerClanId(this.getOrganizerClanId());
            fieldCnt++;
        }  else if (builder.hasOrganizerClanId()) {
            // 清理OrganizerClanId
            builder.clearOrganizerClanId();
            fieldCnt++;
        }
        if (this.getTargetClanId() != 0L) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }  else if (builder.hasTargetClanId()) {
            // 清理TargetClanId
            builder.clearTargetClanId();
            fieldCnt++;
        }
        if (this.getTargetTemplateId() != 0) {
            builder.setTargetTemplateId(this.getTargetTemplateId());
            fieldCnt++;
        }  else if (builder.hasTargetTemplateId()) {
            // 清理TargetTemplateId
            builder.clearTargetTemplateId();
            fieldCnt++;
        }
        if (this.curSoldierTypeList != null) {
            BasicPB.Int32ListPB.Builder tmpBuilder = BasicPB.Int32ListPB.newBuilder();
            final int tmpFieldCnt = this.curSoldierTypeList.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCurSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCurSoldierTypeList();
            }
        }  else if (builder.hasCurSoldierTypeList()) {
            // 清理CurSoldierTypeList
            builder.clearCurSoldierTypeList();
            fieldCnt++;
        }
        if (this.organizerCardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.organizerCardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOrganizerCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOrganizerCardHead();
            }
        }  else if (builder.hasOrganizerCardHead()) {
            // 清理OrganizerCardHead
            builder.clearOrganizerCardHead();
            fieldCnt++;
        }
        if (this.targetCardHead != null) {
            StructPB.PlayerCardHeadPB.Builder tmpBuilder = StructPB.PlayerCardHeadPB.newBuilder();
            final int tmpFieldCnt = this.targetCardHead.copyToCs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTargetCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTargetCardHead();
            }
        }  else if (builder.hasTargetCardHead()) {
            // 清理TargetCardHead
            builder.clearTargetCardHead();
            fieldCnt++;
        }
        if (this.getBeRallyId() != 0L) {
            builder.setBeRallyId(this.getBeRallyId());
            fieldCnt++;
        }  else if (builder.hasBeRallyId()) {
            // 清理BeRallyId
            builder.clearBeRallyId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToCs(RallyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RALLYID)) {
            builder.setRallyId(this.getRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERID)) {
            builder.setOrganizerId(this.getOrganizerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCLANSHORTNAME)) {
            builder.setOrganizerClanShortName(this.getOrganizerClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCITYPOS) && this.organizerCityPos != null) {
            final boolean needClear = !builder.hasOrganizerCityPos();
            final int tmpFieldCnt = this.organizerCityPos.copyChangeToCs(builder.getOrganizerCityPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOrganizerCityPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCITYID)) {
            builder.setOrganizerCityId(this.getOrganizerCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSHORTNAME)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOS) && this.targetPos != null) {
            final boolean needClear = !builder.hasTargetPos();
            final int tmpFieldCnt = this.targetPos.copyChangeToCs(builder.getTargetPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXSOLDIERNUM)) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERNUM)) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYSTATE)) {
            builder.setRallyState(this.getRallyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTS)) {
            builder.setStateStartTs(this.getStateStartTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTS)) {
            builder.setStateEndTs(this.getStateEndTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYINFOMAP) && this.rallyArmyInfoMap != null) {
            final boolean needClear = !builder.hasRallyArmyInfoMap();
            final int tmpFieldCnt = this.rallyArmyInfoMap.copyChangeToCs(builder.getRallyArmyInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyArmyInfoMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToCs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCLANID)) {
            builder.setOrganizerClanId(this.getOrganizerClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANID)) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETTEMPLATEID)) {
            builder.setTargetTemplateId(this.getTargetTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERTYPELIST) && this.curSoldierTypeList != null) {
            final boolean needClear = !builder.hasCurSoldierTypeList();
            final int tmpFieldCnt = this.curSoldierTypeList.copyChangeToCs(builder.getCurSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCARDHEAD) && this.organizerCardHead != null) {
            final boolean needClear = !builder.hasOrganizerCardHead();
            final int tmpFieldCnt = this.organizerCardHead.copyChangeToCs(builder.getOrganizerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOrganizerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETCARDHEAD) && this.targetCardHead != null) {
            final boolean needClear = !builder.hasTargetCardHead();
            final int tmpFieldCnt = this.targetCardHead.copyChangeToCs(builder.getTargetCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_BERALLYID)) {
            builder.setBeRallyId(this.getBeRallyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf PB, then clear map/set increment update info.
     *
     * @param builder target protobuf PB's builder
     * @return copied field count
     */
   public int copyChangeToAndClearDeleteKeysCs(RallyInfoPB.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RALLYID)) {
            builder.setRallyId(this.getRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERID)) {
            builder.setOrganizerId(this.getOrganizerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCLANSHORTNAME)) {
            builder.setOrganizerClanShortName(this.getOrganizerClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCITYPOS) && this.organizerCityPos != null) {
            final boolean needClear = !builder.hasOrganizerCityPos();
            final int tmpFieldCnt = this.organizerCityPos.copyChangeToAndClearDeleteKeysCs(builder.getOrganizerCityPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOrganizerCityPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCITYID)) {
            builder.setOrganizerCityId(this.getOrganizerCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSHORTNAME)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOS) && this.targetPos != null) {
            final boolean needClear = !builder.hasTargetPos();
            final int tmpFieldCnt = this.targetPos.copyChangeToAndClearDeleteKeysCs(builder.getTargetPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXSOLDIERNUM)) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERNUM)) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYSTATE)) {
            builder.setRallyState(this.getRallyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTS)) {
            builder.setStateStartTs(this.getStateStartTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTS)) {
            builder.setStateEndTs(this.getStateEndTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYINFOMAP) && this.rallyArmyInfoMap != null) {
            final boolean needClear = !builder.hasRallyArmyInfoMap();
            final int tmpFieldCnt = this.rallyArmyInfoMap.copyChangeToAndClearDeleteKeysCs(builder.getRallyArmyInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyArmyInfoMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToCs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCLANID)) {
            builder.setOrganizerClanId(this.getOrganizerClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANID)) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETTEMPLATEID)) {
            builder.setTargetTemplateId(this.getTargetTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERTYPELIST) && this.curSoldierTypeList != null) {
            final boolean needClear = !builder.hasCurSoldierTypeList();
            final int tmpFieldCnt = this.curSoldierTypeList.copyChangeToCs(builder.getCurSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCARDHEAD) && this.organizerCardHead != null) {
            final boolean needClear = !builder.hasOrganizerCardHead();
            final int tmpFieldCnt = this.organizerCardHead.copyChangeToAndClearDeleteKeysCs(builder.getOrganizerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOrganizerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETCARDHEAD) && this.targetCardHead != null) {
            final boolean needClear = !builder.hasTargetCardHead();
            final int tmpFieldCnt = this.targetCardHead.copyChangeToAndClearDeleteKeysCs(builder.getTargetCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_BERALLYID)) {
            builder.setBeRallyId(this.getBeRallyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeFromCs(RallyInfoPB proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRallyId()) {
            this.innerSetRallyId(proto.getRallyId());
        } else {
            this.innerSetRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOrganizerId()) {
            this.innerSetOrganizerId(proto.getOrganizerId());
        } else {
            this.innerSetOrganizerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOrganizerClanShortName()) {
            this.innerSetOrganizerClanShortName(proto.getOrganizerClanShortName());
        } else {
            this.innerSetOrganizerClanShortName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasOrganizerCityPos()) {
            this.getOrganizerCityPos().mergeFromCs(proto.getOrganizerCityPos());
        } else {
            if (this.organizerCityPos != null) {
                this.organizerCityPos.mergeFromCs(proto.getOrganizerCityPos());
            }
        }
        if (proto.hasOrganizerCityId()) {
            this.innerSetOrganizerCityId(proto.getOrganizerCityId());
        } else {
            this.innerSetOrganizerCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetClanShortName()) {
            this.innerSetTargetClanShortName(proto.getTargetClanShortName());
        } else {
            this.innerSetTargetClanShortName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTargetPos()) {
            this.getTargetPos().mergeFromCs(proto.getTargetPos());
        } else {
            if (this.targetPos != null) {
                this.targetPos.mergeFromCs(proto.getTargetPos());
            }
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(RallyTargetType.forNumber(0));
        }
        if (proto.hasMaxSoldierNum()) {
            this.innerSetMaxSoldierNum(proto.getMaxSoldierNum());
        } else {
            this.innerSetMaxSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurSoldierNum()) {
            this.innerSetCurSoldierNum(proto.getCurSoldierNum());
        } else {
            this.innerSetCurSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRallyState()) {
            this.innerSetRallyState(proto.getRallyState());
        } else {
            this.innerSetRallyState(RallyState.forNumber(0));
        }
        if (proto.hasStateStartTs()) {
            this.innerSetStateStartTs(proto.getStateStartTs());
        } else {
            this.innerSetStateStartTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTs()) {
            this.innerSetStateEndTs(proto.getStateEndTs());
        } else {
            this.innerSetStateEndTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRallyArmyInfoMap()) {
            this.getRallyArmyInfoMap().mergeFromCs(proto.getRallyArmyInfoMap());
        } else {
            if (this.rallyArmyInfoMap != null) {
                this.rallyArmyInfoMap.mergeFromCs(proto.getRallyArmyInfoMap());
            }
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeFromCs(proto.getRecommendSoldierTypeList());
        } else {
            if (this.recommendSoldierTypeList != null) {
                this.recommendSoldierTypeList.mergeFromCs(proto.getRecommendSoldierTypeList());
            }
        }
        if (proto.hasOrganizerClanId()) {
            this.innerSetOrganizerClanId(proto.getOrganizerClanId());
        } else {
            this.innerSetOrganizerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetClanId()) {
            this.innerSetTargetClanId(proto.getTargetClanId());
        } else {
            this.innerSetTargetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetTemplateId()) {
            this.innerSetTargetTemplateId(proto.getTargetTemplateId());
        } else {
            this.innerSetTargetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurSoldierTypeList()) {
            this.getCurSoldierTypeList().mergeFromCs(proto.getCurSoldierTypeList());
        } else {
            if (this.curSoldierTypeList != null) {
                this.curSoldierTypeList.mergeFromCs(proto.getCurSoldierTypeList());
            }
        }
        if (proto.hasOrganizerCardHead()) {
            this.getOrganizerCardHead().mergeFromCs(proto.getOrganizerCardHead());
        } else {
            if (this.organizerCardHead != null) {
                this.organizerCardHead.mergeFromCs(proto.getOrganizerCardHead());
            }
        }
        if (proto.hasTargetCardHead()) {
            this.getTargetCardHead().mergeFromCs(proto.getTargetCardHead());
        } else {
            if (this.targetCardHead != null) {
                this.targetCardHead.mergeFromCs(proto.getTargetCardHead());
            }
        }
        if (proto.hasBeRallyId()) {
            this.innerSetBeRallyId(proto.getBeRallyId());
        } else {
            this.innerSetBeRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RallyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Cs
     *
     * @param proto protobuf Cs
     * @return merged field count
     */
    public int mergeChangeFromCs(RallyInfoPB proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRallyId()) {
            this.setRallyId(proto.getRallyId());
            fieldCnt++;
        }
        if (proto.hasOrganizerId()) {
            this.setOrganizerId(proto.getOrganizerId());
            fieldCnt++;
        }
        if (proto.hasOrganizerClanShortName()) {
            this.setOrganizerClanShortName(proto.getOrganizerClanShortName());
            fieldCnt++;
        }
        if (proto.hasOrganizerCityPos()) {
            this.getOrganizerCityPos().mergeChangeFromCs(proto.getOrganizerCityPos());
            fieldCnt++;
        }
        if (proto.hasOrganizerCityId()) {
            this.setOrganizerCityId(proto.getOrganizerCityId());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasTargetClanShortName()) {
            this.setTargetClanShortName(proto.getTargetClanShortName());
            fieldCnt++;
        }
        if (proto.hasTargetPos()) {
            this.getTargetPos().mergeChangeFromCs(proto.getTargetPos());
            fieldCnt++;
        }
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasMaxSoldierNum()) {
            this.setMaxSoldierNum(proto.getMaxSoldierNum());
            fieldCnt++;
        }
        if (proto.hasCurSoldierNum()) {
            this.setCurSoldierNum(proto.getCurSoldierNum());
            fieldCnt++;
        }
        if (proto.hasRallyState()) {
            this.setRallyState(proto.getRallyState());
            fieldCnt++;
        }
        if (proto.hasStateStartTs()) {
            this.setStateStartTs(proto.getStateStartTs());
            fieldCnt++;
        }
        if (proto.hasStateEndTs()) {
            this.setStateEndTs(proto.getStateEndTs());
            fieldCnt++;
        }
        if (proto.hasRallyArmyInfoMap()) {
            this.getRallyArmyInfoMap().mergeChangeFromCs(proto.getRallyArmyInfoMap());
            fieldCnt++;
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeChangeFromCs(proto.getRecommendSoldierTypeList());
            fieldCnt++;
        }
        if (proto.hasOrganizerClanId()) {
            this.setOrganizerClanId(proto.getOrganizerClanId());
            fieldCnt++;
        }
        if (proto.hasTargetClanId()) {
            this.setTargetClanId(proto.getTargetClanId());
            fieldCnt++;
        }
        if (proto.hasTargetTemplateId()) {
            this.setTargetTemplateId(proto.getTargetTemplateId());
            fieldCnt++;
        }
        if (proto.hasCurSoldierTypeList()) {
            this.getCurSoldierTypeList().mergeChangeFromCs(proto.getCurSoldierTypeList());
            fieldCnt++;
        }
        if (proto.hasOrganizerCardHead()) {
            this.getOrganizerCardHead().mergeChangeFromCs(proto.getOrganizerCardHead());
            fieldCnt++;
        }
        if (proto.hasTargetCardHead()) {
            this.getTargetCardHead().mergeChangeFromCs(proto.getTargetCardHead());
            fieldCnt++;
        }
        if (proto.hasBeRallyId()) {
            this.setBeRallyId(proto.getBeRallyId());
            fieldCnt++;
        }
        return fieldCnt;
    }
        
    /**
     * copy data to protobuf builder, then return builder
     *
     * @return builder
     */
    public RallyInfo.Builder getCopySsBuilder() {
        final RallyInfo.Builder builder = RallyInfo.newBuilder();
        this.copyToSs(builder);
        return builder;
    }

    /**
     * copy to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
    public int copyToSs(RallyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (this.getRallyId() != 0L) {
            builder.setRallyId(this.getRallyId());
            fieldCnt++;
        }  else if (builder.hasRallyId()) {
            // 清理RallyId
            builder.clearRallyId();
            fieldCnt++;
        }
        if (this.getOrganizerId() != 0L) {
            builder.setOrganizerId(this.getOrganizerId());
            fieldCnt++;
        }  else if (builder.hasOrganizerId()) {
            // 清理OrganizerId
            builder.clearOrganizerId();
            fieldCnt++;
        }
        if (!this.getOrganizerClanShortName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setOrganizerClanShortName(this.getOrganizerClanShortName());
            fieldCnt++;
        }  else if (builder.hasOrganizerClanShortName()) {
            // 清理OrganizerClanShortName
            builder.clearOrganizerClanShortName();
            fieldCnt++;
        }
        if (this.organizerCityPos != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.organizerCityPos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOrganizerCityPos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOrganizerCityPos();
            }
        }  else if (builder.hasOrganizerCityPos()) {
            // 清理OrganizerCityPos
            builder.clearOrganizerCityPos();
            fieldCnt++;
        }
        if (this.getOrganizerCityId() != 0L) {
            builder.setOrganizerCityId(this.getOrganizerCityId());
            fieldCnt++;
        }  else if (builder.hasOrganizerCityId()) {
            // 清理OrganizerCityId
            builder.clearOrganizerCityId();
            fieldCnt++;
        }
        if (this.getTargetId() != 0L) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }  else if (builder.hasTargetId()) {
            // 清理TargetId
            builder.clearTargetId();
            fieldCnt++;
        }
        if (!this.getTargetClanShortName().equals(Constant.DEFAULT_STR_VALUE)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }  else if (builder.hasTargetClanShortName()) {
            // 清理TargetClanShortName
            builder.clearTargetClanShortName();
            fieldCnt++;
        }
        if (this.targetPos != null) {
            Struct.Point.Builder tmpBuilder = Struct.Point.newBuilder();
            final int tmpFieldCnt = this.targetPos.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTargetPos(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTargetPos();
            }
        }  else if (builder.hasTargetPos()) {
            // 清理TargetPos
            builder.clearTargetPos();
            fieldCnt++;
        }
        if (this.getTargetType() != RallyTargetType.forNumber(0)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }  else if (builder.hasTargetType()) {
            // 清理TargetType
            builder.clearTargetType();
            fieldCnt++;
        }
        if (this.getMaxSoldierNum() != 0L) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }  else if (builder.hasMaxSoldierNum()) {
            // 清理MaxSoldierNum
            builder.clearMaxSoldierNum();
            fieldCnt++;
        }
        if (this.getCurSoldierNum() != 0L) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }  else if (builder.hasCurSoldierNum()) {
            // 清理CurSoldierNum
            builder.clearCurSoldierNum();
            fieldCnt++;
        }
        if (this.getRallyState() != RallyState.forNumber(0)) {
            builder.setRallyState(this.getRallyState());
            fieldCnt++;
        }  else if (builder.hasRallyState()) {
            // 清理RallyState
            builder.clearRallyState();
            fieldCnt++;
        }
        if (this.getStateStartTs() != 0L) {
            builder.setStateStartTs(this.getStateStartTs());
            fieldCnt++;
        }  else if (builder.hasStateStartTs()) {
            // 清理StateStartTs
            builder.clearStateStartTs();
            fieldCnt++;
        }
        if (this.getStateEndTs() != 0L) {
            builder.setStateEndTs(this.getStateEndTs());
            fieldCnt++;
        }  else if (builder.hasStateEndTs()) {
            // 清理StateEndTs
            builder.clearStateEndTs();
            fieldCnt++;
        }
        if (this.rallyArmyInfoMap != null) {
            StructPlayer.Int64RallyArmyInfoMap.Builder tmpBuilder = StructPlayer.Int64RallyArmyInfoMap.newBuilder();
            final int tmpFieldCnt = this.rallyArmyInfoMap.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRallyArmyInfoMap(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRallyArmyInfoMap();
            }
        }  else if (builder.hasRallyArmyInfoMap()) {
            // 清理RallyArmyInfoMap
            builder.clearRallyArmyInfoMap();
            fieldCnt++;
        }
        if (this.recommendSoldierTypeList != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setRecommendSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearRecommendSoldierTypeList();
            }
        }  else if (builder.hasRecommendSoldierTypeList()) {
            // 清理RecommendSoldierTypeList
            builder.clearRecommendSoldierTypeList();
            fieldCnt++;
        }
        if (this.getOrganizerClanId() != 0L) {
            builder.setOrganizerClanId(this.getOrganizerClanId());
            fieldCnt++;
        }  else if (builder.hasOrganizerClanId()) {
            // 清理OrganizerClanId
            builder.clearOrganizerClanId();
            fieldCnt++;
        }
        if (this.getTargetClanId() != 0L) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }  else if (builder.hasTargetClanId()) {
            // 清理TargetClanId
            builder.clearTargetClanId();
            fieldCnt++;
        }
        if (this.getTargetTemplateId() != 0) {
            builder.setTargetTemplateId(this.getTargetTemplateId());
            fieldCnt++;
        }  else if (builder.hasTargetTemplateId()) {
            // 清理TargetTemplateId
            builder.clearTargetTemplateId();
            fieldCnt++;
        }
        if (this.curSoldierTypeList != null) {
            Basic.Int32List.Builder tmpBuilder = Basic.Int32List.newBuilder();
            final int tmpFieldCnt = this.curSoldierTypeList.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setCurSoldierTypeList(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearCurSoldierTypeList();
            }
        }  else if (builder.hasCurSoldierTypeList()) {
            // 清理CurSoldierTypeList
            builder.clearCurSoldierTypeList();
            fieldCnt++;
        }
        if (this.organizerCardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.organizerCardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setOrganizerCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearOrganizerCardHead();
            }
        }  else if (builder.hasOrganizerCardHead()) {
            // 清理OrganizerCardHead
            builder.clearOrganizerCardHead();
            fieldCnt++;
        }
        if (this.targetCardHead != null) {
            Struct.PlayerCardHead.Builder tmpBuilder = Struct.PlayerCardHead.newBuilder();
            final int tmpFieldCnt = this.targetCardHead.copyToSs(tmpBuilder);
            if (tmpFieldCnt > 0) {
                builder.setTargetCardHead(tmpBuilder);
                fieldCnt++;
            } else {
                builder.clearTargetCardHead();
            }
        }  else if (builder.hasTargetCardHead()) {
            // 清理TargetCardHead
            builder.clearTargetCardHead();
            fieldCnt++;
        }
        if (this.getBeRallyId() != 0L) {
            builder.setBeRallyId(this.getBeRallyId());
            fieldCnt++;
        }  else if (builder.hasBeRallyId()) {
            // 清理BeRallyId
            builder.clearBeRallyId();
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * copy change to protobuf 
     *
     * @param builder target protobuf 's builder
     * @return copied field count
     */
   public int copyChangeToSs(RallyInfo.Builder builder) {
        if (builder == null) {
            throw new NullPointerException();
        }
        if (!this.hasAnyMark()) {
            return 0;
        }
        int fieldCnt = 0;
        if (this.hasMark(FIELD_INDEX_RALLYID)) {
            builder.setRallyId(this.getRallyId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERID)) {
            builder.setOrganizerId(this.getOrganizerId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCLANSHORTNAME)) {
            builder.setOrganizerClanShortName(this.getOrganizerClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCITYPOS) && this.organizerCityPos != null) {
            final boolean needClear = !builder.hasOrganizerCityPos();
            final int tmpFieldCnt = this.organizerCityPos.copyChangeToSs(builder.getOrganizerCityPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOrganizerCityPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCITYID)) {
            builder.setOrganizerCityId(this.getOrganizerCityId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETID)) {
            builder.setTargetId(this.getTargetId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANSHORTNAME)) {
            builder.setTargetClanShortName(this.getTargetClanShortName());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOS) && this.targetPos != null) {
            final boolean needClear = !builder.hasTargetPos();
            final int tmpFieldCnt = this.targetPos.copyChangeToSs(builder.getTargetPosBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetPos();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETTYPE)) {
            builder.setTargetType(this.getTargetType());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_MAXSOLDIERNUM)) {
            builder.setMaxSoldierNum(this.getMaxSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERNUM)) {
            builder.setCurSoldierNum(this.getCurSoldierNum());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYSTATE)) {
            builder.setRallyState(this.getRallyState());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATESTARTTS)) {
            builder.setStateStartTs(this.getStateStartTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_STATEENDTS)) {
            builder.setStateEndTs(this.getStateEndTs());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYINFOMAP) && this.rallyArmyInfoMap != null) {
            final boolean needClear = !builder.hasRallyArmyInfoMap();
            final int tmpFieldCnt = this.rallyArmyInfoMap.copyChangeToSs(builder.getRallyArmyInfoMapBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRallyArmyInfoMap();
            }
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            final boolean needClear = !builder.hasRecommendSoldierTypeList();
            final int tmpFieldCnt = this.recommendSoldierTypeList.copyChangeToSs(builder.getRecommendSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearRecommendSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCLANID)) {
            builder.setOrganizerClanId(this.getOrganizerClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETCLANID)) {
            builder.setTargetClanId(this.getTargetClanId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_TARGETTEMPLATEID)) {
            builder.setTargetTemplateId(this.getTargetTemplateId());
            fieldCnt++;
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERTYPELIST) && this.curSoldierTypeList != null) {
            final boolean needClear = !builder.hasCurSoldierTypeList();
            final int tmpFieldCnt = this.curSoldierTypeList.copyChangeToSs(builder.getCurSoldierTypeListBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearCurSoldierTypeList();
            }
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCARDHEAD) && this.organizerCardHead != null) {
            final boolean needClear = !builder.hasOrganizerCardHead();
            final int tmpFieldCnt = this.organizerCardHead.copyChangeToSs(builder.getOrganizerCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearOrganizerCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_TARGETCARDHEAD) && this.targetCardHead != null) {
            final boolean needClear = !builder.hasTargetCardHead();
            final int tmpFieldCnt = this.targetCardHead.copyChangeToSs(builder.getTargetCardHeadBuilder());
            if (tmpFieldCnt > 0) {
                fieldCnt++;
            } else if (needClear) {
                builder.clearTargetCardHead();
            }
        }
        if (this.hasMark(FIELD_INDEX_BERALLYID)) {
            builder.setBeRallyId(this.getBeRallyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    /**
     * merge from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeFromSs(RallyInfo proto) {
        if (proto == null) {
            throw new RuntimeException("cannot trans null proto to mergeFrom");
        }
        if (proto.hasRallyId()) {
            this.innerSetRallyId(proto.getRallyId());
        } else {
            this.innerSetRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOrganizerId()) {
            this.innerSetOrganizerId(proto.getOrganizerId());
        } else {
            this.innerSetOrganizerId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasOrganizerClanShortName()) {
            this.innerSetOrganizerClanShortName(proto.getOrganizerClanShortName());
        } else {
            this.innerSetOrganizerClanShortName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasOrganizerCityPos()) {
            this.getOrganizerCityPos().mergeFromSs(proto.getOrganizerCityPos());
        } else {
            if (this.organizerCityPos != null) {
                this.organizerCityPos.mergeFromSs(proto.getOrganizerCityPos());
            }
        }
        if (proto.hasOrganizerCityId()) {
            this.innerSetOrganizerCityId(proto.getOrganizerCityId());
        } else {
            this.innerSetOrganizerCityId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetId()) {
            this.innerSetTargetId(proto.getTargetId());
        } else {
            this.innerSetTargetId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetClanShortName()) {
            this.innerSetTargetClanShortName(proto.getTargetClanShortName());
        } else {
            this.innerSetTargetClanShortName(Constant.DEFAULT_STR_VALUE);
        }
        if (proto.hasTargetPos()) {
            this.getTargetPos().mergeFromSs(proto.getTargetPos());
        } else {
            if (this.targetPos != null) {
                this.targetPos.mergeFromSs(proto.getTargetPos());
            }
        }
        if (proto.hasTargetType()) {
            this.innerSetTargetType(proto.getTargetType());
        } else {
            this.innerSetTargetType(RallyTargetType.forNumber(0));
        }
        if (proto.hasMaxSoldierNum()) {
            this.innerSetMaxSoldierNum(proto.getMaxSoldierNum());
        } else {
            this.innerSetMaxSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasCurSoldierNum()) {
            this.innerSetCurSoldierNum(proto.getCurSoldierNum());
        } else {
            this.innerSetCurSoldierNum(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRallyState()) {
            this.innerSetRallyState(proto.getRallyState());
        } else {
            this.innerSetRallyState(RallyState.forNumber(0));
        }
        if (proto.hasStateStartTs()) {
            this.innerSetStateStartTs(proto.getStateStartTs());
        } else {
            this.innerSetStateStartTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasStateEndTs()) {
            this.innerSetStateEndTs(proto.getStateEndTs());
        } else {
            this.innerSetStateEndTs(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasRallyArmyInfoMap()) {
            this.getRallyArmyInfoMap().mergeFromSs(proto.getRallyArmyInfoMap());
        } else {
            if (this.rallyArmyInfoMap != null) {
                this.rallyArmyInfoMap.mergeFromSs(proto.getRallyArmyInfoMap());
            }
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeFromSs(proto.getRecommendSoldierTypeList());
        } else {
            if (this.recommendSoldierTypeList != null) {
                this.recommendSoldierTypeList.mergeFromSs(proto.getRecommendSoldierTypeList());
            }
        }
        if (proto.hasOrganizerClanId()) {
            this.innerSetOrganizerClanId(proto.getOrganizerClanId());
        } else {
            this.innerSetOrganizerClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetClanId()) {
            this.innerSetTargetClanId(proto.getTargetClanId());
        } else {
            this.innerSetTargetClanId(Constant.DEFAULT_LONG_VALUE);
        }
        if (proto.hasTargetTemplateId()) {
            this.innerSetTargetTemplateId(proto.getTargetTemplateId());
        } else {
            this.innerSetTargetTemplateId(Constant.DEFAULT_INT_VALUE);
        }
        if (proto.hasCurSoldierTypeList()) {
            this.getCurSoldierTypeList().mergeFromSs(proto.getCurSoldierTypeList());
        } else {
            if (this.curSoldierTypeList != null) {
                this.curSoldierTypeList.mergeFromSs(proto.getCurSoldierTypeList());
            }
        }
        if (proto.hasOrganizerCardHead()) {
            this.getOrganizerCardHead().mergeFromSs(proto.getOrganizerCardHead());
        } else {
            if (this.organizerCardHead != null) {
                this.organizerCardHead.mergeFromSs(proto.getOrganizerCardHead());
            }
        }
        if (proto.hasTargetCardHead()) {
            this.getTargetCardHead().mergeFromSs(proto.getTargetCardHead());
        } else {
            if (this.targetCardHead != null) {
                this.targetCardHead.mergeFromSs(proto.getTargetCardHead());
            }
        }
        if (proto.hasBeRallyId()) {
            this.innerSetBeRallyId(proto.getBeRallyId());
        } else {
            this.innerSetBeRallyId(Constant.DEFAULT_LONG_VALUE);
        }
        this.markAll();
        return RallyInfoProp.FIELD_COUNT;
    }

    /**
     * merge change from protobuf Ss
     *
     * @param proto protobuf Ss
     * @return merged field count
     */
    public int mergeChangeFromSs(RallyInfo proto) {
        if (proto == null) {
            throw new NullPointerException();
        }
        int fieldCnt = 0;
        if (proto.hasRallyId()) {
            this.setRallyId(proto.getRallyId());
            fieldCnt++;
        }
        if (proto.hasOrganizerId()) {
            this.setOrganizerId(proto.getOrganizerId());
            fieldCnt++;
        }
        if (proto.hasOrganizerClanShortName()) {
            this.setOrganizerClanShortName(proto.getOrganizerClanShortName());
            fieldCnt++;
        }
        if (proto.hasOrganizerCityPos()) {
            this.getOrganizerCityPos().mergeChangeFromSs(proto.getOrganizerCityPos());
            fieldCnt++;
        }
        if (proto.hasOrganizerCityId()) {
            this.setOrganizerCityId(proto.getOrganizerCityId());
            fieldCnt++;
        }
        if (proto.hasTargetId()) {
            this.setTargetId(proto.getTargetId());
            fieldCnt++;
        }
        if (proto.hasTargetClanShortName()) {
            this.setTargetClanShortName(proto.getTargetClanShortName());
            fieldCnt++;
        }
        if (proto.hasTargetPos()) {
            this.getTargetPos().mergeChangeFromSs(proto.getTargetPos());
            fieldCnt++;
        }
        if (proto.hasTargetType()) {
            this.setTargetType(proto.getTargetType());
            fieldCnt++;
        }
        if (proto.hasMaxSoldierNum()) {
            this.setMaxSoldierNum(proto.getMaxSoldierNum());
            fieldCnt++;
        }
        if (proto.hasCurSoldierNum()) {
            this.setCurSoldierNum(proto.getCurSoldierNum());
            fieldCnt++;
        }
        if (proto.hasRallyState()) {
            this.setRallyState(proto.getRallyState());
            fieldCnt++;
        }
        if (proto.hasStateStartTs()) {
            this.setStateStartTs(proto.getStateStartTs());
            fieldCnt++;
        }
        if (proto.hasStateEndTs()) {
            this.setStateEndTs(proto.getStateEndTs());
            fieldCnt++;
        }
        if (proto.hasRallyArmyInfoMap()) {
            this.getRallyArmyInfoMap().mergeChangeFromSs(proto.getRallyArmyInfoMap());
            fieldCnt++;
        }
        if (proto.hasRecommendSoldierTypeList()) {
            this.getRecommendSoldierTypeList().mergeChangeFromSs(proto.getRecommendSoldierTypeList());
            fieldCnt++;
        }
        if (proto.hasOrganizerClanId()) {
            this.setOrganizerClanId(proto.getOrganizerClanId());
            fieldCnt++;
        }
        if (proto.hasTargetClanId()) {
            this.setTargetClanId(proto.getTargetClanId());
            fieldCnt++;
        }
        if (proto.hasTargetTemplateId()) {
            this.setTargetTemplateId(proto.getTargetTemplateId());
            fieldCnt++;
        }
        if (proto.hasCurSoldierTypeList()) {
            this.getCurSoldierTypeList().mergeChangeFromSs(proto.getCurSoldierTypeList());
            fieldCnt++;
        }
        if (proto.hasOrganizerCardHead()) {
            this.getOrganizerCardHead().mergeChangeFromSs(proto.getOrganizerCardHead());
            fieldCnt++;
        }
        if (proto.hasTargetCardHead()) {
            this.getTargetCardHead().mergeChangeFromSs(proto.getTargetCardHead());
            fieldCnt++;
        }
        if (proto.hasBeRallyId()) {
            this.setBeRallyId(proto.getBeRallyId());
            fieldCnt++;
        }
        return fieldCnt;
    }

    @Override
    public String toString() {
        RallyInfo.Builder builder = RallyInfo.newBuilder();
        // 默认打印ss协议下的字段
        this.copyToSs(builder);
        return builder.toString();
    }

    @Override
    public void unMarkAll() {
        if (!this.hasAnyMark()) {
            return;
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCITYPOS) && this.organizerCityPos != null) {
            this.organizerCityPos.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TARGETPOS) && this.targetPos != null) {
            this.targetPos.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RALLYARMYINFOMAP) && this.rallyArmyInfoMap != null) {
            this.rallyArmyInfoMap.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_RECOMMENDSOLDIERTYPELIST) && this.recommendSoldierTypeList != null) {
            this.recommendSoldierTypeList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_CURSOLDIERTYPELIST) && this.curSoldierTypeList != null) {
            this.curSoldierTypeList.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_ORGANIZERCARDHEAD) && this.organizerCardHead != null) {
            this.organizerCardHead.unMarkAll();
        }
        if (this.hasMark(FIELD_INDEX_TARGETCARDHEAD) && this.targetCardHead != null) {
            this.targetCardHead.unMarkAll();
        }
        if (!this.hasAnyMark()) {
            return;
        }
        this.unMarkAllBits();
        this.unMarkParentNode();
    }

    @Override
    public void markAll() {
        if (this.organizerCityPos != null) {
            this.organizerCityPos.markAll();
        }
        if (this.targetPos != null) {
            this.targetPos.markAll();
        }
        if (this.rallyArmyInfoMap != null) {
            this.rallyArmyInfoMap.markAll();
        }
        if (this.recommendSoldierTypeList != null) {
            this.recommendSoldierTypeList.markAll();
        }
        if (this.curSoldierTypeList != null) {
            this.curSoldierTypeList.markAll();
        }
        if (this.organizerCardHead != null) {
            this.organizerCardHead.markAll();
        }
        if (this.targetCardHead != null) {
            this.targetCardHead.markAll();
        }
        this.markAllBits();
        this.markParentNode();
    }

    @Override
    public void mark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 |= (1L << inWhichBit);
            this.markParentNode();
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public void unMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            this.markBits0 &= ~(1L << inWhichBit);
            if (!this.hasAnyMark()) {
                this.unMarkParentNode();
            }
            return;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasMark(final int index) {
        if (index < 0 || index >= this.getFieldCount()) {
            throw new IndexOutOfBoundsException(Integer.toString(index));
        }
        final int inWhichMarkBits = index >>> Constant.MARK_MOVE_RIGHT_BIT_SIZE ;
        final int inWhichBit = index & Constant.MARK_MOD_MASK;
        if (inWhichMarkBits == 0) {
            return (this.markBits0 & (1L << inWhichBit)) != 0;
        }
        throw new IndexOutOfBoundsException(Integer.toString(index));
    }

    @Override
    public boolean hasAnyMark() {
        long mark = 0L;
        mark |= this.markBits0;
        return mark != 0L;
    }

    @Override
    public Long getPrivateKey() {
        return this.rallyId;
    }

    @Override
    public boolean compareDataTo(final IMarkContainer node) {
        if (node == null) {
            return false;
        }
        if (this == node) {
            return true;
        }
        if (!(node instanceof RallyInfoProp)) {
            return false;
        }
        final RallyInfoProp otherNode = (RallyInfoProp) node;
        if (this.rallyId != otherNode.rallyId) {
            return false;
        }
        if (this.organizerId != otherNode.organizerId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.organizerClanShortName, otherNode.organizerClanShortName)) {
            return false;
        }
        if (!this.getOrganizerCityPos().compareDataTo(otherNode.getOrganizerCityPos())) {
            return false;
        }
        if (this.organizerCityId != otherNode.organizerCityId) {
            return false;
        }
        if (this.targetId != otherNode.targetId) {
            return false;
        }
        if (!com.yorha.gemini.utils.StringUtils.equals(this.targetClanShortName, otherNode.targetClanShortName)) {
            return false;
        }
        if (!this.getTargetPos().compareDataTo(otherNode.getTargetPos())) {
            return false;
        }
        if (this.targetType != otherNode.targetType) {
            return false;
        }
        if (this.maxSoldierNum != otherNode.maxSoldierNum) {
            return false;
        }
        if (this.curSoldierNum != otherNode.curSoldierNum) {
            return false;
        }
        if (this.rallyState != otherNode.rallyState) {
            return false;
        }
        if (this.stateStartTs != otherNode.stateStartTs) {
            return false;
        }
        if (this.stateEndTs != otherNode.stateEndTs) {
            return false;
        }
        if (!this.getRallyArmyInfoMap().compareDataTo(otherNode.getRallyArmyInfoMap())) {
            return false;
        }
        if (!this.getRecommendSoldierTypeList().compareDataTo(otherNode.getRecommendSoldierTypeList())) {
            return false;
        }
        if (this.organizerClanId != otherNode.organizerClanId) {
            return false;
        }
        if (this.targetClanId != otherNode.targetClanId) {
            return false;
        }
        if (this.targetTemplateId != otherNode.targetTemplateId) {
            return false;
        }
        if (!this.getCurSoldierTypeList().compareDataTo(otherNode.getCurSoldierTypeList())) {
            return false;
        }
        if (!this.getOrganizerCardHead().compareDataTo(otherNode.getOrganizerCardHead())) {
            return false;
        }
        if (!this.getTargetCardHead().compareDataTo(otherNode.getTargetCardHead())) {
            return false;
        }
        if (this.beRallyId != otherNode.beRallyId) {
            return false;
        }
        return true;
    }


    /**
     * 标记所有位
     */
    private void markAllBits() {
        this.markBits0 = ~(0L) >>> 41;
    }

    /**
     * 取消所有位的标记
     */
    private void unMarkAllBits() {
        this.markBits0 = 0L;
    }
}
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_territory.proto

package com.yorha.proto;

public final class SsClanTerritory {
  private SsClanTerritory() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GetClanPowerRewardAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetClanPowerRewardAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetClanPowerRewardAsk}
   */
  public static final class GetClanPowerRewardAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetClanPowerRewardAsk)
      GetClanPowerRewardAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetClanPowerRewardAsk.newBuilder() to construct.
    private GetClanPowerRewardAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetClanPowerRewardAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetClanPowerRewardAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetClanPowerRewardAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk.class, com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk other = (com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetClanPowerRewardAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetClanPowerRewardAsk)
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk.class, com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk build() {
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk buildPartial() {
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk result = new com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk other) {
        if (other == com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetClanPowerRewardAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetClanPowerRewardAsk)
    private static final com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk();
    }

    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetClanPowerRewardAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetClanPowerRewardAsk>() {
      @java.lang.Override
      public GetClanPowerRewardAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetClanPowerRewardAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetClanPowerRewardAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetClanPowerRewardAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetClanPowerRewardAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetClanPowerRewardAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 rewardId = 1;</code>
     * @return Whether the rewardId field is set.
     */
    boolean hasRewardId();
    /**
     * <code>optional int32 rewardId = 1;</code>
     * @return The rewardId.
     */
    int getRewardId();

    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */
    int getResourceCount();
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */
    boolean containsResource(
        int key);
    /**
     * Use {@link #getResourceMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getResource();
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getResourceMap();
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */

    int getResourceOrDefault(
        int key,
        int defaultValue);
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */

    int getResourceOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetClanPowerRewardAns}
   */
  public static final class GetClanPowerRewardAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetClanPowerRewardAns)
      GetClanPowerRewardAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetClanPowerRewardAns.newBuilder() to construct.
    private GetClanPowerRewardAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetClanPowerRewardAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetClanPowerRewardAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetClanPowerRewardAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              rewardId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                resource_ = com.google.protobuf.MapField.newMapField(
                    ResourceDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              resource__ = input.readMessage(
                  ResourceDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              resource_.getMutableMap().put(
                  resource__.getKey(), resource__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetResource();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns.class, com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns.Builder.class);
    }

    private int bitField0_;
    public static final int REWARDID_FIELD_NUMBER = 1;
    private int rewardId_;
    /**
     * <code>optional int32 rewardId = 1;</code>
     * @return Whether the rewardId field is set.
     */
    @java.lang.Override
    public boolean hasRewardId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 rewardId = 1;</code>
     * @return The rewardId.
     */
    @java.lang.Override
    public int getRewardId() {
      return rewardId_;
    }

    public static final int RESOURCE_FIELD_NUMBER = 2;
    private static final class ResourceDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAns_ResourceEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> resource_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetResource() {
      if (resource_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ResourceDefaultEntryHolder.defaultEntry);
      }
      return resource_;
    }

    public int getResourceCount() {
      return internalGetResource().getMap().size();
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */

    @java.lang.Override
    public boolean containsResource(
        int key) {
      
      return internalGetResource().getMap().containsKey(key);
    }
    /**
     * Use {@link #getResourceMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getResource() {
      return getResourceMap();
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getResourceMap() {
      return internalGetResource().getMap();
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */
    @java.lang.Override

    public int getResourceOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetResource().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 2;</code>
     */
    @java.lang.Override

    public int getResourceOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetResource().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, rewardId_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetResource(),
          ResourceDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rewardId_);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetResource().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        resource__ = ResourceDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, resource__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns other = (com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns) obj;

      if (hasRewardId() != other.hasRewardId()) return false;
      if (hasRewardId()) {
        if (getRewardId()
            != other.getRewardId()) return false;
      }
      if (!internalGetResource().equals(
          other.internalGetResource())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRewardId()) {
        hash = (37 * hash) + REWARDID_FIELD_NUMBER;
        hash = (53 * hash) + getRewardId();
      }
      if (!internalGetResource().getMap().isEmpty()) {
        hash = (37 * hash) + RESOURCE_FIELD_NUMBER;
        hash = (53 * hash) + internalGetResource().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetClanPowerRewardAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetClanPowerRewardAns)
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetResource();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableResource();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns.class, com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rewardId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        internalGetMutableResource().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_GetClanPowerRewardAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns build() {
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns buildPartial() {
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns result = new com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rewardId_ = rewardId_;
          to_bitField0_ |= 0x00000001;
        }
        result.resource_ = internalGetResource();
        result.resource_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns other) {
        if (other == com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns.getDefaultInstance()) return this;
        if (other.hasRewardId()) {
          setRewardId(other.getRewardId());
        }
        internalGetMutableResource().mergeFrom(
            other.internalGetResource());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int rewardId_ ;
      /**
       * <code>optional int32 rewardId = 1;</code>
       * @return Whether the rewardId field is set.
       */
      @java.lang.Override
      public boolean hasRewardId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 rewardId = 1;</code>
       * @return The rewardId.
       */
      @java.lang.Override
      public int getRewardId() {
        return rewardId_;
      }
      /**
       * <code>optional int32 rewardId = 1;</code>
       * @param value The rewardId to set.
       * @return This builder for chaining.
       */
      public Builder setRewardId(int value) {
        bitField0_ |= 0x00000001;
        rewardId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 rewardId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRewardId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rewardId_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> resource_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetResource() {
        if (resource_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ResourceDefaultEntryHolder.defaultEntry);
        }
        return resource_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableResource() {
        onChanged();;
        if (resource_ == null) {
          resource_ = com.google.protobuf.MapField.newMapField(
              ResourceDefaultEntryHolder.defaultEntry);
        }
        if (!resource_.isMutable()) {
          resource_ = resource_.copy();
        }
        return resource_;
      }

      public int getResourceCount() {
        return internalGetResource().getMap().size();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 2;</code>
       */

      @java.lang.Override
      public boolean containsResource(
          int key) {
        
        return internalGetResource().getMap().containsKey(key);
      }
      /**
       * Use {@link #getResourceMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getResource() {
        return getResourceMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 2;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getResourceMap() {
        return internalGetResource().getMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 2;</code>
       */
      @java.lang.Override

      public int getResourceOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetResource().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 2;</code>
       */
      @java.lang.Override

      public int getResourceOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetResource().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearResource() {
        internalGetMutableResource().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 2;</code>
       */

      public Builder removeResource(
          int key) {
        
        internalGetMutableResource().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableResource() {
        return internalGetMutableResource().getMutableMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 2;</code>
       */
      public Builder putResource(
          int key,
          int value) {
        
        
        internalGetMutableResource().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 2;</code>
       */

      public Builder putAllResource(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableResource().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetClanPowerRewardAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetClanPowerRewardAns)
    private static final com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns();
    }

    public static com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetClanPowerRewardAns>
        PARSER = new com.google.protobuf.AbstractParser<GetClanPowerRewardAns>() {
      @java.lang.Override
      public GetClanPowerRewardAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetClanPowerRewardAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetClanPowerRewardAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetClanPowerRewardAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.GetClanPowerRewardAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckRebuildClanBuildingAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckRebuildClanBuildingAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return Whether the rebuildingType field is set.
     */
    boolean hasRebuildingType();
    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return The rebuildingType.
     */
    com.yorha.proto.CommonEnum.MapBuildingType getRebuildingType();

    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return The cardHead.
     */
    com.yorha.proto.Struct.PlayerCardHead getCardHead();
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     */
    com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder();

    /**
     * <pre>
     * 标记建筑的唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 4;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <pre>
     * 标记建筑的唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 4;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <pre>
     * 赛季id 用来改建时抉择建哪个配置
     * </pre>
     *
     * <code>optional int32 storyId = 6;</code>
     * @return Whether the storyId field is set.
     */
    boolean hasStoryId();
    /**
     * <pre>
     * 赛季id 用来改建时抉择建哪个配置
     * </pre>
     *
     * <code>optional int32 storyId = 6;</code>
     * @return The storyId.
     */
    int getStoryId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckRebuildClanBuildingAsk}
   */
  public static final class CheckRebuildClanBuildingAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckRebuildClanBuildingAsk)
      CheckRebuildClanBuildingAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckRebuildClanBuildingAsk.newBuilder() to construct.
    private CheckRebuildClanBuildingAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckRebuildClanBuildingAsk() {
      rebuildingType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckRebuildClanBuildingAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckRebuildClanBuildingAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapBuildingType value = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                rebuildingType_ = rawValue;
              }
              break;
            }
            case 26: {
              com.yorha.proto.Struct.PlayerCardHead.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.Struct.PlayerCardHead.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              entityId_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000010;
              storyId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk.class, com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int REBUILDINGTYPE_FIELD_NUMBER = 2;
    private int rebuildingType_;
    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return Whether the rebuildingType field is set.
     */
    @java.lang.Override public boolean hasRebuildingType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return The rebuildingType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapBuildingType getRebuildingType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rebuildingType_);
      return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
    }

    public static final int CARDHEAD_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.PlayerCardHead cardHead_;
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }

    public static final int ENTITYID_FIELD_NUMBER = 4;
    private long entityId_;
    /**
     * <pre>
     * 标记建筑的唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 4;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 标记建筑的唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 4;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int STORYID_FIELD_NUMBER = 6;
    private int storyId_;
    /**
     * <pre>
     * 赛季id 用来改建时抉择建哪个配置
     * </pre>
     *
     * <code>optional int32 storyId = 6;</code>
     * @return Whether the storyId field is set.
     */
    @java.lang.Override
    public boolean hasStoryId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 赛季id 用来改建时抉择建哪个配置
     * </pre>
     *
     * <code>optional int32 storyId = 6;</code>
     * @return The storyId.
     */
    @java.lang.Override
    public int getStoryId() {
      return storyId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, rebuildingType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getCardHead());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, entityId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(6, storyId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, rebuildingType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCardHead());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, entityId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, storyId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk other = (com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasRebuildingType() != other.hasRebuildingType()) return false;
      if (hasRebuildingType()) {
        if (rebuildingType_ != other.rebuildingType_) return false;
      }
      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasStoryId() != other.hasStoryId()) return false;
      if (hasStoryId()) {
        if (getStoryId()
            != other.getStoryId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasRebuildingType()) {
        hash = (37 * hash) + REBUILDINGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + rebuildingType_;
      }
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasStoryId()) {
        hash = (37 * hash) + STORYID_FIELD_NUMBER;
        hash = (53 * hash) + getStoryId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckRebuildClanBuildingAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckRebuildClanBuildingAsk)
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk.class, com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        rebuildingType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        storyId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk build() {
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk buildPartial() {
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk result = new com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.rebuildingType_ = rebuildingType_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.storyId_ = storyId_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk other) {
        if (other == com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasRebuildingType()) {
          setRebuildingType(other.getRebuildingType());
        }
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasStoryId()) {
          setStoryId(other.getStoryId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int rebuildingType_ = 0;
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @return Whether the rebuildingType field is set.
       */
      @java.lang.Override public boolean hasRebuildingType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @return The rebuildingType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapBuildingType getRebuildingType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rebuildingType_);
        return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @param value The rebuildingType to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildingType(com.yorha.proto.CommonEnum.MapBuildingType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        rebuildingType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildingType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rebuildingType_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.PlayerCardHead cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> cardHeadBuilder_;
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder setCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.Struct.PlayerCardHead.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.Struct.PlayerCardHead.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHead.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }

      private long entityId_ ;
      /**
       * <pre>
       * 标记建筑的唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 4;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 标记建筑的唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 4;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <pre>
       * 标记建筑的唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 4;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000008;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标记建筑的唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int storyId_ ;
      /**
       * <pre>
       * 赛季id 用来改建时抉择建哪个配置
       * </pre>
       *
       * <code>optional int32 storyId = 6;</code>
       * @return Whether the storyId field is set.
       */
      @java.lang.Override
      public boolean hasStoryId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 赛季id 用来改建时抉择建哪个配置
       * </pre>
       *
       * <code>optional int32 storyId = 6;</code>
       * @return The storyId.
       */
      @java.lang.Override
      public int getStoryId() {
        return storyId_;
      }
      /**
       * <pre>
       * 赛季id 用来改建时抉择建哪个配置
       * </pre>
       *
       * <code>optional int32 storyId = 6;</code>
       * @param value The storyId to set.
       * @return This builder for chaining.
       */
      public Builder setStoryId(int value) {
        bitField0_ |= 0x00000010;
        storyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 赛季id 用来改建时抉择建哪个配置
       * </pre>
       *
       * <code>optional int32 storyId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearStoryId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        storyId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckRebuildClanBuildingAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckRebuildClanBuildingAsk)
    private static final com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk();
    }

    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckRebuildClanBuildingAsk>
        PARSER = new com.google.protobuf.AbstractParser<CheckRebuildClanBuildingAsk>() {
      @java.lang.Override
      public CheckRebuildClanBuildingAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckRebuildClanBuildingAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckRebuildClanBuildingAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckRebuildClanBuildingAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckRebuildClanBuildingAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckRebuildClanBuildingAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 staffId = 1;</code>
     * @return Whether the staffId field is set.
     */
    boolean hasStaffId();
    /**
     * <code>optional int32 staffId = 1;</code>
     * @return The staffId.
     */
    int getStaffId();

    /**
     * <pre>
     * 改建建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 改建建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    int getTemplateId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckRebuildClanBuildingAns}
   */
  public static final class CheckRebuildClanBuildingAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckRebuildClanBuildingAns)
      CheckRebuildClanBuildingAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckRebuildClanBuildingAns.newBuilder() to construct.
    private CheckRebuildClanBuildingAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckRebuildClanBuildingAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckRebuildClanBuildingAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckRebuildClanBuildingAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              staffId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              templateId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns.class, com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns.Builder.class);
    }

    private int bitField0_;
    public static final int STAFFID_FIELD_NUMBER = 1;
    private int staffId_;
    /**
     * <code>optional int32 staffId = 1;</code>
     * @return Whether the staffId field is set.
     */
    @java.lang.Override
    public boolean hasStaffId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 staffId = 1;</code>
     * @return The staffId.
     */
    @java.lang.Override
    public int getStaffId() {
      return staffId_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 2;
    private int templateId_;
    /**
     * <pre>
     * 改建建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 改建建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, staffId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, templateId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, staffId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, templateId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns other = (com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns) obj;

      if (hasStaffId() != other.hasStaffId()) return false;
      if (hasStaffId()) {
        if (getStaffId()
            != other.getStaffId()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasStaffId()) {
        hash = (37 * hash) + STAFFID_FIELD_NUMBER;
        hash = (53 * hash) + getStaffId();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckRebuildClanBuildingAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckRebuildClanBuildingAns)
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns.class, com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        staffId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns build() {
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns buildPartial() {
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns result = new com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.staffId_ = staffId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns other) {
        if (other == com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns.getDefaultInstance()) return this;
        if (other.hasStaffId()) {
          setStaffId(other.getStaffId());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int staffId_ ;
      /**
       * <code>optional int32 staffId = 1;</code>
       * @return Whether the staffId field is set.
       */
      @java.lang.Override
      public boolean hasStaffId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 staffId = 1;</code>
       * @return The staffId.
       */
      @java.lang.Override
      public int getStaffId() {
        return staffId_;
      }
      /**
       * <code>optional int32 staffId = 1;</code>
       * @param value The staffId to set.
       * @return This builder for chaining.
       */
      public Builder setStaffId(int value) {
        bitField0_ |= 0x00000001;
        staffId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 staffId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStaffId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        staffId_ = 0;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 改建建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 改建建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 改建建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000002;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 改建建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckRebuildClanBuildingAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckRebuildClanBuildingAns)
    private static final com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns();
    }

    public static com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckRebuildClanBuildingAns>
        PARSER = new com.google.protobuf.AbstractParser<CheckRebuildClanBuildingAns>() {
      @java.lang.Override
      public CheckRebuildClanBuildingAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckRebuildClanBuildingAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckRebuildClanBuildingAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckRebuildClanBuildingAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.CheckRebuildClanBuildingAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlaceClanResBuildAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlaceClanResBuildAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.MapBuildingType getType();

    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return The cardHead.
     */
    com.yorha.proto.Struct.PlayerCardHead getCardHead();
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     */
    com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlaceClanResBuildAsk}
   */
  public static final class PlaceClanResBuildAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlaceClanResBuildAsk)
      PlaceClanResBuildAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlaceClanResBuildAsk.newBuilder() to construct.
    private PlaceClanResBuildAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlaceClanResBuildAsk() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlaceClanResBuildAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlaceClanResBuildAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapBuildingType value = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                type_ = rawValue;
              }
              break;
            }
            case 26: {
              com.yorha.proto.Struct.PlayerCardHead.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.Struct.PlayerCardHead.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk.class, com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapBuildingType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
    }

    public static final int CARDHEAD_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.PlayerCardHead cardHead_;
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }
    /**
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, type_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getCardHead());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, type_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCardHead());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk other = (com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlaceClanResBuildAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlaceClanResBuildAsk)
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk.class, com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk build() {
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk buildPartial() {
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk result = new com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk other) {
        if (other == com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int type_ = 0;
      /**
       * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapBuildingType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.MapBuildingType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.PlayerCardHead cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> cardHeadBuilder_;
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder setCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.Struct.PlayerCardHead.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.Struct.PlayerCardHead.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHead.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlaceClanResBuildAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlaceClanResBuildAsk)
    private static final com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk();
    }

    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlaceClanResBuildAsk>
        PARSER = new com.google.protobuf.AbstractParser<PlaceClanResBuildAsk>() {
      @java.lang.Override
      public PlaceClanResBuildAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlaceClanResBuildAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlaceClanResBuildAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlaceClanResBuildAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlaceClanResBuildAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlaceClanResBuildAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <pre>
     * 资源田配置
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 资源田配置
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    int getTemplateId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlaceClanResBuildAns}
   */
  public static final class PlaceClanResBuildAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlaceClanResBuildAns)
      PlaceClanResBuildAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlaceClanResBuildAns.newBuilder() to construct.
    private PlaceClanResBuildAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlaceClanResBuildAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlaceClanResBuildAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlaceClanResBuildAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              entityId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              templateId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns.class, com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns.Builder.class);
    }

    private int bitField0_;
    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 2;
    private int templateId_;
    /**
     * <pre>
     * 资源田配置
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 资源田配置
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, templateId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, templateId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns other = (com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns) obj;

      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlaceClanResBuildAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlaceClanResBuildAns)
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns.class, com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_PlaceClanResBuildAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns build() {
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns buildPartial() {
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns result = new com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns other) {
        if (other == com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns.getDefaultInstance()) return this;
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long entityId_ ;
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000001;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 资源田配置
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 资源田配置
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 资源田配置
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000002;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 资源田配置
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlaceClanResBuildAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlaceClanResBuildAns)
    private static final com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns();
    }

    public static com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlaceClanResBuildAns>
        PARSER = new com.google.protobuf.AbstractParser<PlaceClanResBuildAns>() {
      @java.lang.Override
      public PlaceClanResBuildAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlaceClanResBuildAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlaceClanResBuildAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlaceClanResBuildAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.PlaceClanResBuildAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckExtinguishClanBuildingAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckExtinguishClanBuildingAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return Whether the rebuildingType field is set.
     */
    boolean hasRebuildingType();
    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return The rebuildingType.
     */
    com.yorha.proto.CommonEnum.MapBuildingType getRebuildingType();

    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return The cardHead.
     */
    com.yorha.proto.Struct.PlayerCardHead getCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     */
    com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder();

    /**
     * <pre>
     * 被熄灭的建筑id
     * </pre>
     *
     * <code>optional int32 buildingTemplateId = 4;</code>
     * @return Whether the buildingTemplateId field is set.
     */
    boolean hasBuildingTemplateId();
    /**
     * <pre>
     * 被熄灭的建筑id
     * </pre>
     *
     * <code>optional int32 buildingTemplateId = 4;</code>
     * @return The buildingTemplateId.
     */
    int getBuildingTemplateId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckExtinguishClanBuildingAsk}
   */
  public static final class CheckExtinguishClanBuildingAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckExtinguishClanBuildingAsk)
      CheckExtinguishClanBuildingAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckExtinguishClanBuildingAsk.newBuilder() to construct.
    private CheckExtinguishClanBuildingAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckExtinguishClanBuildingAsk() {
      rebuildingType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckExtinguishClanBuildingAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckExtinguishClanBuildingAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapBuildingType value = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                rebuildingType_ = rawValue;
              }
              break;
            }
            case 26: {
              com.yorha.proto.Struct.PlayerCardHead.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.Struct.PlayerCardHead.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              buildingTemplateId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk.class, com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int REBUILDINGTYPE_FIELD_NUMBER = 2;
    private int rebuildingType_;
    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return Whether the rebuildingType field is set.
     */
    @java.lang.Override public boolean hasRebuildingType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
     * @return The rebuildingType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapBuildingType getRebuildingType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rebuildingType_);
      return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
    }

    public static final int CARDHEAD_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.PlayerCardHead cardHead_;
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }

    public static final int BUILDINGTEMPLATEID_FIELD_NUMBER = 4;
    private int buildingTemplateId_;
    /**
     * <pre>
     * 被熄灭的建筑id
     * </pre>
     *
     * <code>optional int32 buildingTemplateId = 4;</code>
     * @return Whether the buildingTemplateId field is set.
     */
    @java.lang.Override
    public boolean hasBuildingTemplateId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 被熄灭的建筑id
     * </pre>
     *
     * <code>optional int32 buildingTemplateId = 4;</code>
     * @return The buildingTemplateId.
     */
    @java.lang.Override
    public int getBuildingTemplateId() {
      return buildingTemplateId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, rebuildingType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getCardHead());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, buildingTemplateId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, rebuildingType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCardHead());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, buildingTemplateId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk other = (com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasRebuildingType() != other.hasRebuildingType()) return false;
      if (hasRebuildingType()) {
        if (rebuildingType_ != other.rebuildingType_) return false;
      }
      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (hasBuildingTemplateId() != other.hasBuildingTemplateId()) return false;
      if (hasBuildingTemplateId()) {
        if (getBuildingTemplateId()
            != other.getBuildingTemplateId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasRebuildingType()) {
        hash = (37 * hash) + REBUILDINGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + rebuildingType_;
      }
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      if (hasBuildingTemplateId()) {
        hash = (37 * hash) + BUILDINGTEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getBuildingTemplateId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckExtinguishClanBuildingAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckExtinguishClanBuildingAsk)
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk.class, com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        rebuildingType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        buildingTemplateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk build() {
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk buildPartial() {
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk result = new com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.rebuildingType_ = rebuildingType_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.buildingTemplateId_ = buildingTemplateId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk other) {
        if (other == com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasRebuildingType()) {
          setRebuildingType(other.getRebuildingType());
        }
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        if (other.hasBuildingTemplateId()) {
          setBuildingTemplateId(other.getBuildingTemplateId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int rebuildingType_ = 0;
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @return Whether the rebuildingType field is set.
       */
      @java.lang.Override public boolean hasRebuildingType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @return The rebuildingType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapBuildingType getRebuildingType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rebuildingType_);
        return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @param value The rebuildingType to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildingType(com.yorha.proto.CommonEnum.MapBuildingType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        rebuildingType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MapBuildingType rebuildingType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildingType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rebuildingType_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.PlayerCardHead cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> cardHeadBuilder_;
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder setCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.Struct.PlayerCardHead.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.Struct.PlayerCardHead.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHead.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }

      private int buildingTemplateId_ ;
      /**
       * <pre>
       * 被熄灭的建筑id
       * </pre>
       *
       * <code>optional int32 buildingTemplateId = 4;</code>
       * @return Whether the buildingTemplateId field is set.
       */
      @java.lang.Override
      public boolean hasBuildingTemplateId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 被熄灭的建筑id
       * </pre>
       *
       * <code>optional int32 buildingTemplateId = 4;</code>
       * @return The buildingTemplateId.
       */
      @java.lang.Override
      public int getBuildingTemplateId() {
        return buildingTemplateId_;
      }
      /**
       * <pre>
       * 被熄灭的建筑id
       * </pre>
       *
       * <code>optional int32 buildingTemplateId = 4;</code>
       * @param value The buildingTemplateId to set.
       * @return This builder for chaining.
       */
      public Builder setBuildingTemplateId(int value) {
        bitField0_ |= 0x00000008;
        buildingTemplateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被熄灭的建筑id
       * </pre>
       *
       * <code>optional int32 buildingTemplateId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuildingTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        buildingTemplateId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckExtinguishClanBuildingAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckExtinguishClanBuildingAsk)
    private static final com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk();
    }

    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckExtinguishClanBuildingAsk>
        PARSER = new com.google.protobuf.AbstractParser<CheckExtinguishClanBuildingAsk>() {
      @java.lang.Override
      public CheckExtinguishClanBuildingAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckExtinguishClanBuildingAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckExtinguishClanBuildingAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckExtinguishClanBuildingAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckExtinguishClanBuildingAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckExtinguishClanBuildingAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckExtinguishClanBuildingAns}
   */
  public static final class CheckExtinguishClanBuildingAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckExtinguishClanBuildingAns)
      CheckExtinguishClanBuildingAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckExtinguishClanBuildingAns.newBuilder() to construct.
    private CheckExtinguishClanBuildingAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckExtinguishClanBuildingAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckExtinguishClanBuildingAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckExtinguishClanBuildingAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns.class, com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns other = (com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckExtinguishClanBuildingAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckExtinguishClanBuildingAns)
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns.class, com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns build() {
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns buildPartial() {
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns result = new com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns other) {
        if (other == com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckExtinguishClanBuildingAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckExtinguishClanBuildingAns)
    private static final com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns();
    }

    public static com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckExtinguishClanBuildingAns>
        PARSER = new com.google.protobuf.AbstractParser<CheckExtinguishClanBuildingAns>() {
      @java.lang.Override
      public CheckExtinguishClanBuildingAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckExtinguishClanBuildingAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckExtinguishClanBuildingAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckExtinguishClanBuildingAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.CheckExtinguishClanBuildingAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanBuildingInfoAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanBuildingInfoAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanBuildingInfoAsk}
   */
  public static final class FetchClanBuildingInfoAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanBuildingInfoAsk)
      FetchClanBuildingInfoAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanBuildingInfoAsk.newBuilder() to construct.
    private FetchClanBuildingInfoAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanBuildingInfoAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanBuildingInfoAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanBuildingInfoAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk.class, com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk other = (com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanBuildingInfoAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanBuildingInfoAsk)
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk.class, com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk build() {
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk buildPartial() {
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk result = new com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk other) {
        if (other == com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanBuildingInfoAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanBuildingInfoAsk)
    private static final com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk();
    }

    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanBuildingInfoAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanBuildingInfoAsk>() {
      @java.lang.Override
      public FetchClanBuildingInfoAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanBuildingInfoAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanBuildingInfoAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanBuildingInfoAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanBuildingInfoAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanBuildingInfoAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */
    int getClanBuildingCount();
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */
    boolean containsClanBuilding(
        int key);
    /**
     * Use {@link #getClanBuildingMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
    getClanBuilding();
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
    getClanBuildingMap();
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */

    com.yorha.proto.StructClan.ClanBuildingInfo getClanBuildingOrDefault(
        int key,
        com.yorha.proto.StructClan.ClanBuildingInfo defaultValue);
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */

    com.yorha.proto.StructClan.ClanBuildingInfo getClanBuildingOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanBuildingInfoAns}
   */
  public static final class FetchClanBuildingInfoAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanBuildingInfoAns)
      FetchClanBuildingInfoAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanBuildingInfoAns.newBuilder() to construct.
    private FetchClanBuildingInfoAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanBuildingInfoAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanBuildingInfoAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanBuildingInfoAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                clanBuilding_ = com.google.protobuf.MapField.newMapField(
                    ClanBuildingDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
              clanBuilding__ = input.readMessage(
                  ClanBuildingDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              clanBuilding_.getMutableMap().put(
                  clanBuilding__.getKey(), clanBuilding__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetClanBuilding();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns.class, com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns.Builder.class);
    }

    public static final int CLANBUILDING_FIELD_NUMBER = 1;
    private static final class ClanBuildingDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>newDefaultInstance(
                  com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAns_ClanBuildingEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructClan.ClanBuildingInfo.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> clanBuilding_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
    internalGetClanBuilding() {
      if (clanBuilding_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ClanBuildingDefaultEntryHolder.defaultEntry);
      }
      return clanBuilding_;
    }

    public int getClanBuildingCount() {
      return internalGetClanBuilding().getMap().size();
    }
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */

    @java.lang.Override
    public boolean containsClanBuilding(
        int key) {
      
      return internalGetClanBuilding().getMap().containsKey(key);
    }
    /**
     * Use {@link #getClanBuildingMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> getClanBuilding() {
      return getClanBuildingMap();
    }
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> getClanBuildingMap() {
      return internalGetClanBuilding().getMap();
    }
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructClan.ClanBuildingInfo getClanBuildingOrDefault(
        int key,
        com.yorha.proto.StructClan.ClanBuildingInfo defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> map =
          internalGetClanBuilding().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 联盟建筑对应类型数量, key为MapBuildingType
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructClan.ClanBuildingInfo getClanBuildingOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> map =
          internalGetClanBuilding().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetClanBuilding(),
          ClanBuildingDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> entry
           : internalGetClanBuilding().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
        clanBuilding__ = ClanBuildingDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, clanBuilding__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns other = (com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns) obj;

      if (!internalGetClanBuilding().equals(
          other.internalGetClanBuilding())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetClanBuilding().getMap().isEmpty()) {
        hash = (37 * hash) + CLANBUILDING_FIELD_NUMBER;
        hash = (53 * hash) + internalGetClanBuilding().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanBuildingInfoAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanBuildingInfoAns)
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetClanBuilding();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableClanBuilding();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns.class, com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableClanBuilding().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchClanBuildingInfoAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns build() {
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns buildPartial() {
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns result = new com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns(this);
        int from_bitField0_ = bitField0_;
        result.clanBuilding_ = internalGetClanBuilding();
        result.clanBuilding_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns other) {
        if (other == com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns.getDefaultInstance()) return this;
        internalGetMutableClanBuilding().mergeFrom(
            other.internalGetClanBuilding());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> clanBuilding_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
      internalGetClanBuilding() {
        if (clanBuilding_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ClanBuildingDefaultEntryHolder.defaultEntry);
        }
        return clanBuilding_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
      internalGetMutableClanBuilding() {
        onChanged();;
        if (clanBuilding_ == null) {
          clanBuilding_ = com.google.protobuf.MapField.newMapField(
              ClanBuildingDefaultEntryHolder.defaultEntry);
        }
        if (!clanBuilding_.isMutable()) {
          clanBuilding_ = clanBuilding_.copy();
        }
        return clanBuilding_;
      }

      public int getClanBuildingCount() {
        return internalGetClanBuilding().getMap().size();
      }
      /**
       * <pre>
       * 联盟建筑对应类型数量, key为MapBuildingType
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
       */

      @java.lang.Override
      public boolean containsClanBuilding(
          int key) {
        
        return internalGetClanBuilding().getMap().containsKey(key);
      }
      /**
       * Use {@link #getClanBuildingMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> getClanBuilding() {
        return getClanBuildingMap();
      }
      /**
       * <pre>
       * 联盟建筑对应类型数量, key为MapBuildingType
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> getClanBuildingMap() {
        return internalGetClanBuilding().getMap();
      }
      /**
       * <pre>
       * 联盟建筑对应类型数量, key为MapBuildingType
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructClan.ClanBuildingInfo getClanBuildingOrDefault(
          int key,
          com.yorha.proto.StructClan.ClanBuildingInfo defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> map =
            internalGetClanBuilding().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 联盟建筑对应类型数量, key为MapBuildingType
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructClan.ClanBuildingInfo getClanBuildingOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> map =
            internalGetClanBuilding().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearClanBuilding() {
        internalGetMutableClanBuilding().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 联盟建筑对应类型数量, key为MapBuildingType
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
       */

      public Builder removeClanBuilding(
          int key) {
        
        internalGetMutableClanBuilding().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo>
      getMutableClanBuilding() {
        return internalGetMutableClanBuilding().getMutableMap();
      }
      /**
       * <pre>
       * 联盟建筑对应类型数量, key为MapBuildingType
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
       */
      public Builder putClanBuilding(
          int key,
          com.yorha.proto.StructClan.ClanBuildingInfo value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableClanBuilding().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 联盟建筑对应类型数量, key为MapBuildingType
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ClanBuildingInfo&gt; clanBuilding = 1;</code>
       */

      public Builder putAllClanBuilding(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructClan.ClanBuildingInfo> values) {
        internalGetMutableClanBuilding().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanBuildingInfoAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanBuildingInfoAns)
    private static final com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns();
    }

    public static com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanBuildingInfoAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanBuildingInfoAns>() {
      @java.lang.Override
      public FetchClanBuildingInfoAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanBuildingInfoAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanBuildingInfoAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanBuildingInfoAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.FetchClanBuildingInfoAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchTerritoryPageAllAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchTerritoryPageAllAsk)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchTerritoryPageAllAsk}
   */
  public static final class FetchTerritoryPageAllAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchTerritoryPageAllAsk)
      FetchTerritoryPageAllAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchTerritoryPageAllAsk.newBuilder() to construct.
    private FetchTerritoryPageAllAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchTerritoryPageAllAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchTerritoryPageAllAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchTerritoryPageAllAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk.class, com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk other = (com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchTerritoryPageAllAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchTerritoryPageAllAsk)
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk.class, com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk build() {
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk buildPartial() {
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk result = new com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk other) {
        if (other == com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchTerritoryPageAllAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchTerritoryPageAllAsk)
    private static final com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk();
    }

    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchTerritoryPageAllAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchTerritoryPageAllAsk>() {
      @java.lang.Override
      public FetchTerritoryPageAllAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchTerritoryPageAllAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchTerritoryPageAllAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchTerritoryPageAllAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchTerritoryPageAllAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchTerritoryPageAllAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
     * @return The page.
     */
    com.yorha.proto.CommonMsg.ClanTerritoryPage getPage();
    /**
     * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
     */
    com.yorha.proto.CommonMsg.ClanTerritoryPageOrBuilder getPageOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchTerritoryPageAllAns}
   */
  public static final class FetchTerritoryPageAllAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchTerritoryPageAllAns)
      FetchTerritoryPageAllAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchTerritoryPageAllAns.newBuilder() to construct.
    private FetchTerritoryPageAllAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchTerritoryPageAllAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchTerritoryPageAllAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchTerritoryPageAllAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ClanTerritoryPage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = page_.toBuilder();
              }
              page_ = input.readMessage(com.yorha.proto.CommonMsg.ClanTerritoryPage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(page_);
                page_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns.class, com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns.Builder.class);
    }

    private int bitField0_;
    public static final int PAGE_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ClanTerritoryPage page_;
    /**
     * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
     * @return The page.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanTerritoryPage getPage() {
      return page_ == null ? com.yorha.proto.CommonMsg.ClanTerritoryPage.getDefaultInstance() : page_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanTerritoryPageOrBuilder getPageOrBuilder() {
      return page_ == null ? com.yorha.proto.CommonMsg.ClanTerritoryPage.getDefaultInstance() : page_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPage());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPage());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns other = (com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns) obj;

      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (!getPage()
            .equals(other.getPage())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchTerritoryPageAllAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchTerritoryPageAllAns)
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns.class, com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pageBuilder_ == null) {
          page_ = null;
        } else {
          pageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_FetchTerritoryPageAllAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns build() {
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns buildPartial() {
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns result = new com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (pageBuilder_ == null) {
            result.page_ = page_;
          } else {
            result.page_ = pageBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns other) {
        if (other == com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns.getDefaultInstance()) return this;
        if (other.hasPage()) {
          mergePage(other.getPage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ClanTerritoryPage page_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanTerritoryPage, com.yorha.proto.CommonMsg.ClanTerritoryPage.Builder, com.yorha.proto.CommonMsg.ClanTerritoryPageOrBuilder> pageBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       * @return Whether the page field is set.
       */
      public boolean hasPage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       * @return The page.
       */
      public com.yorha.proto.CommonMsg.ClanTerritoryPage getPage() {
        if (pageBuilder_ == null) {
          return page_ == null ? com.yorha.proto.CommonMsg.ClanTerritoryPage.getDefaultInstance() : page_;
        } else {
          return pageBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       */
      public Builder setPage(com.yorha.proto.CommonMsg.ClanTerritoryPage value) {
        if (pageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          page_ = value;
          onChanged();
        } else {
          pageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       */
      public Builder setPage(
          com.yorha.proto.CommonMsg.ClanTerritoryPage.Builder builderForValue) {
        if (pageBuilder_ == null) {
          page_ = builderForValue.build();
          onChanged();
        } else {
          pageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       */
      public Builder mergePage(com.yorha.proto.CommonMsg.ClanTerritoryPage value) {
        if (pageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              page_ != null &&
              page_ != com.yorha.proto.CommonMsg.ClanTerritoryPage.getDefaultInstance()) {
            page_ =
              com.yorha.proto.CommonMsg.ClanTerritoryPage.newBuilder(page_).mergeFrom(value).buildPartial();
          } else {
            page_ = value;
          }
          onChanged();
        } else {
          pageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       */
      public Builder clearPage() {
        if (pageBuilder_ == null) {
          page_ = null;
          onChanged();
        } else {
          pageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanTerritoryPage.Builder getPageBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPageFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanTerritoryPageOrBuilder getPageOrBuilder() {
        if (pageBuilder_ != null) {
          return pageBuilder_.getMessageOrBuilder();
        } else {
          return page_ == null ?
              com.yorha.proto.CommonMsg.ClanTerritoryPage.getDefaultInstance() : page_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTerritoryPage page = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanTerritoryPage, com.yorha.proto.CommonMsg.ClanTerritoryPage.Builder, com.yorha.proto.CommonMsg.ClanTerritoryPageOrBuilder> 
          getPageFieldBuilder() {
        if (pageBuilder_ == null) {
          pageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ClanTerritoryPage, com.yorha.proto.CommonMsg.ClanTerritoryPage.Builder, com.yorha.proto.CommonMsg.ClanTerritoryPageOrBuilder>(
                  getPage(),
                  getParentForChildren(),
                  isClean());
          page_ = null;
        }
        return pageBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchTerritoryPageAllAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchTerritoryPageAllAns)
    private static final com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns();
    }

    public static com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchTerritoryPageAllAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchTerritoryPageAllAns>() {
      @java.lang.Override
      public FetchTerritoryPageAllAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchTerritoryPageAllAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchTerritoryPageAllAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchTerritoryPageAllAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.FetchTerritoryPageAllAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SyncTerritoryBuffSnapshotCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SyncTerritoryBuffSnapshotCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */
    int getBuffCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */
    boolean containsBuff(
        int key);
    /**
     * Use {@link #getBuffMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
    getBuff();
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
    getBuffMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */

    com.yorha.proto.CommonMsg.TerritoryBuffItem getBuffOrDefault(
        int key,
        com.yorha.proto.CommonMsg.TerritoryBuffItem defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */

    com.yorha.proto.CommonMsg.TerritoryBuffItem getBuffOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SyncTerritoryBuffSnapshotCmd}
   */
  public static final class SyncTerritoryBuffSnapshotCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SyncTerritoryBuffSnapshotCmd)
      SyncTerritoryBuffSnapshotCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SyncTerritoryBuffSnapshotCmd.newBuilder() to construct.
    private SyncTerritoryBuffSnapshotCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SyncTerritoryBuffSnapshotCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SyncTerritoryBuffSnapshotCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SyncTerritoryBuffSnapshotCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                buff_ = com.google.protobuf.MapField.newMapField(
                    BuffDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
              buff__ = input.readMessage(
                  BuffDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              buff_.getMutableMap().put(
                  buff__.getKey(), buff__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetBuff();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd.class, com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd.Builder.class);
    }

    public static final int BUFF_FIELD_NUMBER = 1;
    private static final class BuffDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>newDefaultInstance(
                  com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_BuffEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.CommonMsg.TerritoryBuffItem.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> buff_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
    internalGetBuff() {
      if (buff_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            BuffDefaultEntryHolder.defaultEntry);
      }
      return buff_;
    }

    public int getBuffCount() {
      return internalGetBuff().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */

    @java.lang.Override
    public boolean containsBuff(
        int key) {
      
      return internalGetBuff().getMap().containsKey(key);
    }
    /**
     * Use {@link #getBuffMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> getBuff() {
      return getBuffMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> getBuffMap() {
      return internalGetBuff().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.TerritoryBuffItem getBuffOrDefault(
        int key,
        com.yorha.proto.CommonMsg.TerritoryBuffItem defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> map =
          internalGetBuff().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.TerritoryBuffItem getBuffOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> map =
          internalGetBuff().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetBuff(),
          BuffDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> entry
           : internalGetBuff().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
        buff__ = BuffDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, buff__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd other = (com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd) obj;

      if (!internalGetBuff().equals(
          other.internalGetBuff())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetBuff().getMap().isEmpty()) {
        hash = (37 * hash) + BUFF_FIELD_NUMBER;
        hash = (53 * hash) + internalGetBuff().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SyncTerritoryBuffSnapshotCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SyncTerritoryBuffSnapshotCmd)
        com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetBuff();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableBuff();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd.class, com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableBuff().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd build() {
        com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd buildPartial() {
        com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd result = new com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd(this);
        int from_bitField0_ = bitField0_;
        result.buff_ = internalGetBuff();
        result.buff_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd other) {
        if (other == com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd.getDefaultInstance()) return this;
        internalGetMutableBuff().mergeFrom(
            other.internalGetBuff());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> buff_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
      internalGetBuff() {
        if (buff_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              BuffDefaultEntryHolder.defaultEntry);
        }
        return buff_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
      internalGetMutableBuff() {
        onChanged();;
        if (buff_ == null) {
          buff_ = com.google.protobuf.MapField.newMapField(
              BuffDefaultEntryHolder.defaultEntry);
        }
        if (!buff_.isMutable()) {
          buff_ = buff_.copy();
        }
        return buff_;
      }

      public int getBuffCount() {
        return internalGetBuff().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
       */

      @java.lang.Override
      public boolean containsBuff(
          int key) {
        
        return internalGetBuff().getMap().containsKey(key);
      }
      /**
       * Use {@link #getBuffMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> getBuff() {
        return getBuffMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> getBuffMap() {
        return internalGetBuff().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.TerritoryBuffItem getBuffOrDefault(
          int key,
          com.yorha.proto.CommonMsg.TerritoryBuffItem defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> map =
            internalGetBuff().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.TerritoryBuffItem getBuffOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> map =
            internalGetBuff().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearBuff() {
        internalGetMutableBuff().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
       */

      public Builder removeBuff(
          int key) {
        
        internalGetMutableBuff().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem>
      getMutableBuff() {
        return internalGetMutableBuff().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
       */
      public Builder putBuff(
          int key,
          com.yorha.proto.CommonMsg.TerritoryBuffItem value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableBuff().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.TerritoryBuffItem&gt; buff = 1;</code>
       */

      public Builder putAllBuff(
          java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.TerritoryBuffItem> values) {
        internalGetMutableBuff().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SyncTerritoryBuffSnapshotCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SyncTerritoryBuffSnapshotCmd)
    private static final com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd();
    }

    public static com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SyncTerritoryBuffSnapshotCmd>
        PARSER = new com.google.protobuf.AbstractParser<SyncTerritoryBuffSnapshotCmd>() {
      @java.lang.Override
      public SyncTerritoryBuffSnapshotCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SyncTerritoryBuffSnapshotCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SyncTerritoryBuffSnapshotCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SyncTerritoryBuffSnapshotCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.SyncTerritoryBuffSnapshotCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SyncTerritoryInfoCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SyncTerritoryInfoCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 territoryPower = 2;</code>
     * @return Whether the territoryPower field is set.
     */
    boolean hasTerritoryPower();
    /**
     * <code>optional int32 territoryPower = 2;</code>
     * @return The territoryPower.
     */
    int getTerritoryPower();

    /**
     * <code>optional int32 partNum = 3;</code>
     * @return Whether the partNum field is set.
     */
    boolean hasPartNum();
    /**
     * <code>optional int32 partNum = 3;</code>
     * @return The partNum.
     */
    int getPartNum();

    /**
     * <code>optional int32 buildingType = 4;</code>
     * @return Whether the buildingType field is set.
     */
    boolean hasBuildingType();
    /**
     * <code>optional int32 buildingType = 4;</code>
     * @return The buildingType.
     */
    int getBuildingType();

    /**
     * <code>optional int32 totalNum = 5;</code>
     * @return Whether the totalNum field is set.
     */
    boolean hasTotalNum();
    /**
     * <code>optional int32 totalNum = 5;</code>
     * @return The totalNum.
     */
    int getTotalNum();

    /**
     * <code>optional int32 alreadyBuiltNum = 6;</code>
     * @return Whether the alreadyBuiltNum field is set.
     */
    boolean hasAlreadyBuiltNum();
    /**
     * <code>optional int32 alreadyBuiltNum = 6;</code>
     * @return The alreadyBuiltNum.
     */
    int getAlreadyBuiltNum();

    /**
     * <pre>
     * 增加地块 true，减少地块 false
     * </pre>
     *
     * <code>optional bool addPartOrReduce = 7;</code>
     * @return Whether the addPartOrReduce field is set.
     */
    boolean hasAddPartOrReduce();
    /**
     * <pre>
     * 增加地块 true，减少地块 false
     * </pre>
     *
     * <code>optional bool addPartOrReduce = 7;</code>
     * @return The addPartOrReduce.
     */
    boolean getAddPartOrReduce();

    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 8;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 8;</code>
     * @return The partId.
     */
    int getPartId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SyncTerritoryInfoCmd}
   */
  public static final class SyncTerritoryInfoCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SyncTerritoryInfoCmd)
      SyncTerritoryInfoCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SyncTerritoryInfoCmd.newBuilder() to construct.
    private SyncTerritoryInfoCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SyncTerritoryInfoCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SyncTerritoryInfoCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SyncTerritoryInfoCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 16: {
              bitField0_ |= 0x00000001;
              territoryPower_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              partNum_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              buildingType_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              totalNum_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000010;
              alreadyBuiltNum_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000020;
              addPartOrReduce_ = input.readBool();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000040;
              partId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryInfoCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryInfoCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd.class, com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd.Builder.class);
    }

    private int bitField0_;
    public static final int TERRITORYPOWER_FIELD_NUMBER = 2;
    private int territoryPower_;
    /**
     * <code>optional int32 territoryPower = 2;</code>
     * @return Whether the territoryPower field is set.
     */
    @java.lang.Override
    public boolean hasTerritoryPower() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 territoryPower = 2;</code>
     * @return The territoryPower.
     */
    @java.lang.Override
    public int getTerritoryPower() {
      return territoryPower_;
    }

    public static final int PARTNUM_FIELD_NUMBER = 3;
    private int partNum_;
    /**
     * <code>optional int32 partNum = 3;</code>
     * @return Whether the partNum field is set.
     */
    @java.lang.Override
    public boolean hasPartNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 partNum = 3;</code>
     * @return The partNum.
     */
    @java.lang.Override
    public int getPartNum() {
      return partNum_;
    }

    public static final int BUILDINGTYPE_FIELD_NUMBER = 4;
    private int buildingType_;
    /**
     * <code>optional int32 buildingType = 4;</code>
     * @return Whether the buildingType field is set.
     */
    @java.lang.Override
    public boolean hasBuildingType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 buildingType = 4;</code>
     * @return The buildingType.
     */
    @java.lang.Override
    public int getBuildingType() {
      return buildingType_;
    }

    public static final int TOTALNUM_FIELD_NUMBER = 5;
    private int totalNum_;
    /**
     * <code>optional int32 totalNum = 5;</code>
     * @return Whether the totalNum field is set.
     */
    @java.lang.Override
    public boolean hasTotalNum() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 totalNum = 5;</code>
     * @return The totalNum.
     */
    @java.lang.Override
    public int getTotalNum() {
      return totalNum_;
    }

    public static final int ALREADYBUILTNUM_FIELD_NUMBER = 6;
    private int alreadyBuiltNum_;
    /**
     * <code>optional int32 alreadyBuiltNum = 6;</code>
     * @return Whether the alreadyBuiltNum field is set.
     */
    @java.lang.Override
    public boolean hasAlreadyBuiltNum() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 alreadyBuiltNum = 6;</code>
     * @return The alreadyBuiltNum.
     */
    @java.lang.Override
    public int getAlreadyBuiltNum() {
      return alreadyBuiltNum_;
    }

    public static final int ADDPARTORREDUCE_FIELD_NUMBER = 7;
    private boolean addPartOrReduce_;
    /**
     * <pre>
     * 增加地块 true，减少地块 false
     * </pre>
     *
     * <code>optional bool addPartOrReduce = 7;</code>
     * @return Whether the addPartOrReduce field is set.
     */
    @java.lang.Override
    public boolean hasAddPartOrReduce() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 增加地块 true，减少地块 false
     * </pre>
     *
     * <code>optional bool addPartOrReduce = 7;</code>
     * @return The addPartOrReduce.
     */
    @java.lang.Override
    public boolean getAddPartOrReduce() {
      return addPartOrReduce_;
    }

    public static final int PARTID_FIELD_NUMBER = 8;
    private int partId_;
    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 8;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 8;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(2, territoryPower_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, partNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(4, buildingType_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(5, totalNum_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(6, alreadyBuiltNum_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBool(7, addPartOrReduce_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(8, partId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, territoryPower_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, partNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, buildingType_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, totalNum_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, alreadyBuiltNum_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, addPartOrReduce_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, partId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd other = (com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd) obj;

      if (hasTerritoryPower() != other.hasTerritoryPower()) return false;
      if (hasTerritoryPower()) {
        if (getTerritoryPower()
            != other.getTerritoryPower()) return false;
      }
      if (hasPartNum() != other.hasPartNum()) return false;
      if (hasPartNum()) {
        if (getPartNum()
            != other.getPartNum()) return false;
      }
      if (hasBuildingType() != other.hasBuildingType()) return false;
      if (hasBuildingType()) {
        if (getBuildingType()
            != other.getBuildingType()) return false;
      }
      if (hasTotalNum() != other.hasTotalNum()) return false;
      if (hasTotalNum()) {
        if (getTotalNum()
            != other.getTotalNum()) return false;
      }
      if (hasAlreadyBuiltNum() != other.hasAlreadyBuiltNum()) return false;
      if (hasAlreadyBuiltNum()) {
        if (getAlreadyBuiltNum()
            != other.getAlreadyBuiltNum()) return false;
      }
      if (hasAddPartOrReduce() != other.hasAddPartOrReduce()) return false;
      if (hasAddPartOrReduce()) {
        if (getAddPartOrReduce()
            != other.getAddPartOrReduce()) return false;
      }
      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTerritoryPower()) {
        hash = (37 * hash) + TERRITORYPOWER_FIELD_NUMBER;
        hash = (53 * hash) + getTerritoryPower();
      }
      if (hasPartNum()) {
        hash = (37 * hash) + PARTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getPartNum();
      }
      if (hasBuildingType()) {
        hash = (37 * hash) + BUILDINGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getBuildingType();
      }
      if (hasTotalNum()) {
        hash = (37 * hash) + TOTALNUM_FIELD_NUMBER;
        hash = (53 * hash) + getTotalNum();
      }
      if (hasAlreadyBuiltNum()) {
        hash = (37 * hash) + ALREADYBUILTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getAlreadyBuiltNum();
      }
      if (hasAddPartOrReduce()) {
        hash = (37 * hash) + ADDPARTORREDUCE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getAddPartOrReduce());
      }
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SyncTerritoryInfoCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SyncTerritoryInfoCmd)
        com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryInfoCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryInfoCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd.class, com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        territoryPower_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        partNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        buildingType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        totalNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        alreadyBuiltNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        addPartOrReduce_ = false;
        bitField0_ = (bitField0_ & ~0x00000020);
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncTerritoryInfoCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd build() {
        com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd buildPartial() {
        com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd result = new com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.territoryPower_ = territoryPower_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.partNum_ = partNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.buildingType_ = buildingType_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.totalNum_ = totalNum_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.alreadyBuiltNum_ = alreadyBuiltNum_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.addPartOrReduce_ = addPartOrReduce_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000040;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd other) {
        if (other == com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd.getDefaultInstance()) return this;
        if (other.hasTerritoryPower()) {
          setTerritoryPower(other.getTerritoryPower());
        }
        if (other.hasPartNum()) {
          setPartNum(other.getPartNum());
        }
        if (other.hasBuildingType()) {
          setBuildingType(other.getBuildingType());
        }
        if (other.hasTotalNum()) {
          setTotalNum(other.getTotalNum());
        }
        if (other.hasAlreadyBuiltNum()) {
          setAlreadyBuiltNum(other.getAlreadyBuiltNum());
        }
        if (other.hasAddPartOrReduce()) {
          setAddPartOrReduce(other.getAddPartOrReduce());
        }
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int territoryPower_ ;
      /**
       * <code>optional int32 territoryPower = 2;</code>
       * @return Whether the territoryPower field is set.
       */
      @java.lang.Override
      public boolean hasTerritoryPower() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 territoryPower = 2;</code>
       * @return The territoryPower.
       */
      @java.lang.Override
      public int getTerritoryPower() {
        return territoryPower_;
      }
      /**
       * <code>optional int32 territoryPower = 2;</code>
       * @param value The territoryPower to set.
       * @return This builder for chaining.
       */
      public Builder setTerritoryPower(int value) {
        bitField0_ |= 0x00000001;
        territoryPower_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 territoryPower = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTerritoryPower() {
        bitField0_ = (bitField0_ & ~0x00000001);
        territoryPower_ = 0;
        onChanged();
        return this;
      }

      private int partNum_ ;
      /**
       * <code>optional int32 partNum = 3;</code>
       * @return Whether the partNum field is set.
       */
      @java.lang.Override
      public boolean hasPartNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 partNum = 3;</code>
       * @return The partNum.
       */
      @java.lang.Override
      public int getPartNum() {
        return partNum_;
      }
      /**
       * <code>optional int32 partNum = 3;</code>
       * @param value The partNum to set.
       * @return This builder for chaining.
       */
      public Builder setPartNum(int value) {
        bitField0_ |= 0x00000002;
        partNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 partNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        partNum_ = 0;
        onChanged();
        return this;
      }

      private int buildingType_ ;
      /**
       * <code>optional int32 buildingType = 4;</code>
       * @return Whether the buildingType field is set.
       */
      @java.lang.Override
      public boolean hasBuildingType() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 buildingType = 4;</code>
       * @return The buildingType.
       */
      @java.lang.Override
      public int getBuildingType() {
        return buildingType_;
      }
      /**
       * <code>optional int32 buildingType = 4;</code>
       * @param value The buildingType to set.
       * @return This builder for chaining.
       */
      public Builder setBuildingType(int value) {
        bitField0_ |= 0x00000004;
        buildingType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 buildingType = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuildingType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        buildingType_ = 0;
        onChanged();
        return this;
      }

      private int totalNum_ ;
      /**
       * <code>optional int32 totalNum = 5;</code>
       * @return Whether the totalNum field is set.
       */
      @java.lang.Override
      public boolean hasTotalNum() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 totalNum = 5;</code>
       * @return The totalNum.
       */
      @java.lang.Override
      public int getTotalNum() {
        return totalNum_;
      }
      /**
       * <code>optional int32 totalNum = 5;</code>
       * @param value The totalNum to set.
       * @return This builder for chaining.
       */
      public Builder setTotalNum(int value) {
        bitField0_ |= 0x00000008;
        totalNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 totalNum = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalNum() {
        bitField0_ = (bitField0_ & ~0x00000008);
        totalNum_ = 0;
        onChanged();
        return this;
      }

      private int alreadyBuiltNum_ ;
      /**
       * <code>optional int32 alreadyBuiltNum = 6;</code>
       * @return Whether the alreadyBuiltNum field is set.
       */
      @java.lang.Override
      public boolean hasAlreadyBuiltNum() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 alreadyBuiltNum = 6;</code>
       * @return The alreadyBuiltNum.
       */
      @java.lang.Override
      public int getAlreadyBuiltNum() {
        return alreadyBuiltNum_;
      }
      /**
       * <code>optional int32 alreadyBuiltNum = 6;</code>
       * @param value The alreadyBuiltNum to set.
       * @return This builder for chaining.
       */
      public Builder setAlreadyBuiltNum(int value) {
        bitField0_ |= 0x00000010;
        alreadyBuiltNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 alreadyBuiltNum = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlreadyBuiltNum() {
        bitField0_ = (bitField0_ & ~0x00000010);
        alreadyBuiltNum_ = 0;
        onChanged();
        return this;
      }

      private boolean addPartOrReduce_ ;
      /**
       * <pre>
       * 增加地块 true，减少地块 false
       * </pre>
       *
       * <code>optional bool addPartOrReduce = 7;</code>
       * @return Whether the addPartOrReduce field is set.
       */
      @java.lang.Override
      public boolean hasAddPartOrReduce() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 增加地块 true，减少地块 false
       * </pre>
       *
       * <code>optional bool addPartOrReduce = 7;</code>
       * @return The addPartOrReduce.
       */
      @java.lang.Override
      public boolean getAddPartOrReduce() {
        return addPartOrReduce_;
      }
      /**
       * <pre>
       * 增加地块 true，减少地块 false
       * </pre>
       *
       * <code>optional bool addPartOrReduce = 7;</code>
       * @param value The addPartOrReduce to set.
       * @return This builder for chaining.
       */
      public Builder setAddPartOrReduce(boolean value) {
        bitField0_ |= 0x00000020;
        addPartOrReduce_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 增加地块 true，减少地块 false
       * </pre>
       *
       * <code>optional bool addPartOrReduce = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddPartOrReduce() {
        bitField0_ = (bitField0_ & ~0x00000020);
        addPartOrReduce_ = false;
        onChanged();
        return this;
      }

      private int partId_ ;
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 8;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 8;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 8;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000040;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        partId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SyncTerritoryInfoCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SyncTerritoryInfoCmd)
    private static final com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd();
    }

    public static com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SyncTerritoryInfoCmd>
        PARSER = new com.google.protobuf.AbstractParser<SyncTerritoryInfoCmd>() {
      @java.lang.Override
      public SyncTerritoryInfoCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SyncTerritoryInfoCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SyncTerritoryInfoCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SyncTerritoryInfoCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.SyncTerritoryInfoCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SyncClanBuildStatusCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SyncClanBuildStatusCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 建筑id，军团资源中心是0L，军团建筑是entityId
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <pre>
     * 建筑id，军团资源中心是0L，军团建筑是entityId
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return The id.
     */
    long getId();

    /**
     * <pre>
     * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
     * @return Whether the status field is set.
     */
    boolean hasStatus();
    /**
     * <pre>
     * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
     * @return The status.
     */
    com.yorha.proto.CommonEnum.ClanBuildStatus getStatus();

    /**
     * <pre>
     * 如果是建设成功需要把位置信息给到军团
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 如果是建设成功需要把位置信息给到军团
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <pre>
     * 如果是建设成功需要把位置信息给到军团
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 如果是建设成功需要把模板id给到军团
     * </pre>
     *
     * <code>optional int32 templateId = 4;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 如果是建设成功需要把模板id给到军团
     * </pre>
     *
     * <code>optional int32 templateId = 4;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 标注建筑是否是军团资源中心
     * </pre>
     *
     * <code>optional bool isClanResBuilding = 5;</code>
     * @return Whether the isClanResBuilding field is set.
     */
    boolean hasIsClanResBuilding();
    /**
     * <pre>
     * 标注建筑是否是军团资源中心
     * </pre>
     *
     * <code>optional bool isClanResBuilding = 5;</code>
     * @return The isClanResBuilding.
     */
    boolean getIsClanResBuilding();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SyncClanBuildStatusCmd}
   */
  public static final class SyncClanBuildStatusCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SyncClanBuildStatusCmd)
      SyncClanBuildStatusCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SyncClanBuildStatusCmd.newBuilder() to construct.
    private SyncClanBuildStatusCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SyncClanBuildStatusCmd() {
      status_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SyncClanBuildStatusCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SyncClanBuildStatusCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanBuildStatus value = com.yorha.proto.CommonEnum.ClanBuildStatus.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                status_ = rawValue;
              }
              break;
            }
            case 26: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              templateId_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              isClanResBuilding_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncClanBuildStatusCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncClanBuildStatusCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd.class, com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <pre>
     * 建筑id，军团资源中心是0L，军团建筑是entityId
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 建筑id，军团资源中心是0L，军团建筑是entityId
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }

    public static final int STATUS_FIELD_NUMBER = 2;
    private int status_;
    /**
     * <pre>
     * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
     * @return Whether the status field is set.
     */
    @java.lang.Override public boolean hasStatus() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
     * @return The status.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanBuildStatus getStatus() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanBuildStatus result = com.yorha.proto.CommonEnum.ClanBuildStatus.valueOf(status_);
      return result == null ? com.yorha.proto.CommonEnum.ClanBuildStatus.CBS_NONE : result;
    }

    public static final int POINT_FIELD_NUMBER = 3;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <pre>
     * 如果是建设成功需要把位置信息给到军团
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 如果是建设成功需要把位置信息给到军团
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 如果是建设成功需要把位置信息给到军团
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 4;
    private int templateId_;
    /**
     * <pre>
     * 如果是建设成功需要把模板id给到军团
     * </pre>
     *
     * <code>optional int32 templateId = 4;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 如果是建设成功需要把模板id给到军团
     * </pre>
     *
     * <code>optional int32 templateId = 4;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int ISCLANRESBUILDING_FIELD_NUMBER = 5;
    private boolean isClanResBuilding_;
    /**
     * <pre>
     * 标注建筑是否是军团资源中心
     * </pre>
     *
     * <code>optional bool isClanResBuilding = 5;</code>
     * @return Whether the isClanResBuilding field is set.
     */
    @java.lang.Override
    public boolean hasIsClanResBuilding() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 标注建筑是否是军团资源中心
     * </pre>
     *
     * <code>optional bool isClanResBuilding = 5;</code>
     * @return The isClanResBuilding.
     */
    @java.lang.Override
    public boolean getIsClanResBuilding() {
      return isClanResBuilding_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, status_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, templateId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(5, isClanResBuilding_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, status_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, templateId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, isClanResBuilding_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd other = (com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd) obj;

      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (hasStatus() != other.hasStatus()) return false;
      if (hasStatus()) {
        if (status_ != other.status_) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasIsClanResBuilding() != other.hasIsClanResBuilding()) return false;
      if (hasIsClanResBuilding()) {
        if (getIsClanResBuilding()
            != other.getIsClanResBuilding()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getId());
      }
      if (hasStatus()) {
        hash = (37 * hash) + STATUS_FIELD_NUMBER;
        hash = (53 * hash) + status_;
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasIsClanResBuilding()) {
        hash = (37 * hash) + ISCLANRESBUILDING_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsClanResBuilding());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SyncClanBuildStatusCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SyncClanBuildStatusCmd)
        com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncClanBuildStatusCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncClanBuildStatusCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd.class, com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        status_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        isClanResBuilding_ = false;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTerritory.internal_static_com_yorha_proto_SyncClanBuildStatusCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd build() {
        com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd buildPartial() {
        com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd result = new com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.status_ = status_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.isClanResBuilding_ = isClanResBuilding_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd) {
          return mergeFrom((com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd other) {
        if (other == com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasStatus()) {
          setStatus(other.getStatus());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasIsClanResBuilding()) {
          setIsClanResBuilding(other.getIsClanResBuilding());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long id_ ;
      /**
       * <pre>
       * 建筑id，军团资源中心是0L，军团建筑是entityId
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 建筑id，军团资源中心是0L，军团建筑是entityId
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public long getId() {
        return id_;
      }
      /**
       * <pre>
       * 建筑id，军团资源中心是0L，军团建筑是entityId
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑id，军团资源中心是0L，军团建筑是entityId
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      private int status_ = 0;
      /**
       * <pre>
       * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
       * @return Whether the status field is set.
       */
      @java.lang.Override public boolean hasStatus() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
       * @return The status.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanBuildStatus getStatus() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanBuildStatus result = com.yorha.proto.CommonEnum.ClanBuildStatus.valueOf(status_);
        return result == null ? com.yorha.proto.CommonEnum.ClanBuildStatus.CBS_NONE : result;
      }
      /**
       * <pre>
       * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(com.yorha.proto.CommonEnum.ClanBuildStatus value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        status_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑变化状态，默认是已开始建设，只会从 开始建设 变为 取消建设 或 完成建设
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanBuildStatus status = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000002);
        status_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 如果是建设成功需要把位置信息给到军团
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 如果是建设成功需要把模板id给到军团
       * </pre>
       *
       * <code>optional int32 templateId = 4;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 如果是建设成功需要把模板id给到军团
       * </pre>
       *
       * <code>optional int32 templateId = 4;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 如果是建设成功需要把模板id给到军团
       * </pre>
       *
       * <code>optional int32 templateId = 4;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000008;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 如果是建设成功需要把模板id给到军团
       * </pre>
       *
       * <code>optional int32 templateId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private boolean isClanResBuilding_ ;
      /**
       * <pre>
       * 标注建筑是否是军团资源中心
       * </pre>
       *
       * <code>optional bool isClanResBuilding = 5;</code>
       * @return Whether the isClanResBuilding field is set.
       */
      @java.lang.Override
      public boolean hasIsClanResBuilding() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 标注建筑是否是军团资源中心
       * </pre>
       *
       * <code>optional bool isClanResBuilding = 5;</code>
       * @return The isClanResBuilding.
       */
      @java.lang.Override
      public boolean getIsClanResBuilding() {
        return isClanResBuilding_;
      }
      /**
       * <pre>
       * 标注建筑是否是军团资源中心
       * </pre>
       *
       * <code>optional bool isClanResBuilding = 5;</code>
       * @param value The isClanResBuilding to set.
       * @return This builder for chaining.
       */
      public Builder setIsClanResBuilding(boolean value) {
        bitField0_ |= 0x00000010;
        isClanResBuilding_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标注建筑是否是军团资源中心
       * </pre>
       *
       * <code>optional bool isClanResBuilding = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsClanResBuilding() {
        bitField0_ = (bitField0_ & ~0x00000010);
        isClanResBuilding_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SyncClanBuildStatusCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SyncClanBuildStatusCmd)
    private static final com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd();
    }

    public static com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SyncClanBuildStatusCmd>
        PARSER = new com.google.protobuf.AbstractParser<SyncClanBuildStatusCmd>() {
      @java.lang.Override
      public SyncClanBuildStatusCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SyncClanBuildStatusCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SyncClanBuildStatusCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SyncClanBuildStatusCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTerritory.SyncClanBuildStatusCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetClanPowerRewardAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetClanPowerRewardAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetClanPowerRewardAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetClanPowerRewardAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetClanPowerRewardAns_ResourceEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetClanPowerRewardAns_ResourceEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlaceClanResBuildAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlaceClanResBuildAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlaceClanResBuildAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlaceClanResBuildAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanBuildingInfoAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanBuildingInfoAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanBuildingInfoAns_ClanBuildingEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanBuildingInfoAns_ClanBuildingEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchTerritoryPageAllAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchTerritoryPageAllAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_BuffEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_BuffEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SyncTerritoryInfoCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SyncTerritoryInfoCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SyncClanBuildStatusCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SyncClanBuildStatusCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/clan/ss_clan_territory.pr" +
      "oto\022\017com.yorha.proto\032%ss_proto/gen/commo" +
      "n/common_enum.proto\032$ss_proto/gen/common" +
      "/common_msg.proto\032 ss_proto/gen/common/s" +
      "truct.proto\032%ss_proto/gen/common/struct_" +
      "clan.proto\")\n\025GetClanPowerRewardAsk\022\020\n\010p" +
      "layerId\030\001 \001(\003\"\242\001\n\025GetClanPowerRewardAns\022" +
      "\020\n\010rewardId\030\001 \001(\005\022F\n\010resource\030\002 \003(\01324.co" +
      "m.yorha.proto.GetClanPowerRewardAns.Reso" +
      "urceEntry\032/\n\rResourceEntry\022\013\n\003key\030\001 \001(\005\022" +
      "\r\n\005value\030\002 \001(\005:\0028\001\"\277\001\n\033CheckRebuildClanB" +
      "uildingAsk\022\020\n\010playerId\030\001 \001(\003\0228\n\016rebuildi" +
      "ngType\030\002 \001(\0162 .com.yorha.proto.MapBuildi" +
      "ngType\0221\n\010cardHead\030\003 \001(\0132\037.com.yorha.pro" +
      "to.PlayerCardHead\022\020\n\010entityId\030\004 \001(\003\022\017\n\007s" +
      "toryId\030\006 \001(\005\"B\n\033CheckRebuildClanBuilding" +
      "Ans\022\017\n\007staffId\030\001 \001(\005\022\022\n\ntemplateId\030\002 \001(\005" +
      "\"\213\001\n\024PlaceClanResBuildAsk\022\020\n\010playerId\030\001 " +
      "\001(\003\022.\n\004type\030\002 \001(\0162 .com.yorha.proto.MapB" +
      "uildingType\0221\n\010cardHead\030\003 \001(\0132\037.com.yorh" +
      "a.proto.PlayerCardHead\"<\n\024PlaceClanResBu" +
      "ildAns\022\020\n\010entityId\030\001 \001(\003\022\022\n\ntemplateId\030\002" +
      " \001(\005\"\273\001\n\036CheckExtinguishClanBuildingAsk\022" +
      "\020\n\010playerId\030\001 \001(\003\0228\n\016rebuildingType\030\002 \001(" +
      "\0162 .com.yorha.proto.MapBuildingType\0221\n\010c" +
      "ardHead\030\003 \001(\0132\037.com.yorha.proto.PlayerCa" +
      "rdHead\022\032\n\022buildingTemplateId\030\004 \001(\005\" \n\036Ch" +
      "eckExtinguishClanBuildingAns\"*\n\030FetchCla" +
      "nBuildingInfoAsk\022\016\n\006clanId\030\001 \001(\003\"\305\001\n\030Fet" +
      "chClanBuildingInfoAns\022Q\n\014clanBuilding\030\001 " +
      "\003(\0132;.com.yorha.proto.FetchClanBuildingI" +
      "nfoAns.ClanBuildingEntry\032V\n\021ClanBuilding" +
      "Entry\022\013\n\003key\030\001 \001(\005\0220\n\005value\030\002 \001(\0132!.com." +
      "yorha.proto.ClanBuildingInfo:\0028\001\"\032\n\030Fetc" +
      "hTerritoryPageAllAsk\"L\n\030FetchTerritoryPa" +
      "geAllAns\0220\n\004page\030\001 \001(\0132\".com.yorha.proto" +
      ".ClanTerritoryPage\"\266\001\n\034SyncTerritoryBuff" +
      "SnapshotCmd\022E\n\004buff\030\001 \003(\01327.com.yorha.pr" +
      "oto.SyncTerritoryBuffSnapshotCmd.BuffEnt" +
      "ry\032O\n\tBuffEntry\022\013\n\003key\030\001 \001(\005\0221\n\005value\030\002 " +
      "\001(\0132\".com.yorha.proto.TerritoryBuffItem:" +
      "\0028\001\"\251\001\n\024SyncTerritoryInfoCmd\022\026\n\016territor" +
      "yPower\030\002 \001(\005\022\017\n\007partNum\030\003 \001(\005\022\024\n\014buildin" +
      "gType\030\004 \001(\005\022\020\n\010totalNum\030\005 \001(\005\022\027\n\017already" +
      "BuiltNum\030\006 \001(\005\022\027\n\017addPartOrReduce\030\007 \001(\010\022" +
      "\016\n\006partId\030\010 \001(\005\"\254\001\n\026SyncClanBuildStatusC" +
      "md\022\n\n\002id\030\001 \001(\003\0220\n\006status\030\002 \001(\0162 .com.yor" +
      "ha.proto.ClanBuildStatus\022%\n\005point\030\003 \001(\0132" +
      "\026.com.yorha.proto.Point\022\022\n\ntemplateId\030\004 " +
      "\001(\005\022\031\n\021isClanResBuilding\030\005 \001(\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructClan.getDescriptor(),
        });
    internal_static_com_yorha_proto_GetClanPowerRewardAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_GetClanPowerRewardAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetClanPowerRewardAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_GetClanPowerRewardAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_GetClanPowerRewardAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetClanPowerRewardAns_descriptor,
        new java.lang.String[] { "RewardId", "Resource", });
    internal_static_com_yorha_proto_GetClanPowerRewardAns_ResourceEntry_descriptor =
      internal_static_com_yorha_proto_GetClanPowerRewardAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_GetClanPowerRewardAns_ResourceEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetClanPowerRewardAns_ResourceEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckRebuildClanBuildingAsk_descriptor,
        new java.lang.String[] { "PlayerId", "RebuildingType", "CardHead", "EntityId", "StoryId", });
    internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckRebuildClanBuildingAns_descriptor,
        new java.lang.String[] { "StaffId", "TemplateId", });
    internal_static_com_yorha_proto_PlaceClanResBuildAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_PlaceClanResBuildAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlaceClanResBuildAsk_descriptor,
        new java.lang.String[] { "PlayerId", "Type", "CardHead", });
    internal_static_com_yorha_proto_PlaceClanResBuildAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_PlaceClanResBuildAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlaceClanResBuildAns_descriptor,
        new java.lang.String[] { "EntityId", "TemplateId", });
    internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckExtinguishClanBuildingAsk_descriptor,
        new java.lang.String[] { "PlayerId", "RebuildingType", "CardHead", "BuildingTemplateId", });
    internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckExtinguishClanBuildingAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanBuildingInfoAsk_descriptor,
        new java.lang.String[] { "ClanId", });
    internal_static_com_yorha_proto_FetchClanBuildingInfoAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_FetchClanBuildingInfoAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanBuildingInfoAns_descriptor,
        new java.lang.String[] { "ClanBuilding", });
    internal_static_com_yorha_proto_FetchClanBuildingInfoAns_ClanBuildingEntry_descriptor =
      internal_static_com_yorha_proto_FetchClanBuildingInfoAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_FetchClanBuildingInfoAns_ClanBuildingEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanBuildingInfoAns_ClanBuildingEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchTerritoryPageAllAsk_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_FetchTerritoryPageAllAns_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_FetchTerritoryPageAllAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchTerritoryPageAllAns_descriptor,
        new java.lang.String[] { "Page", });
    internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_descriptor,
        new java.lang.String[] { "Buff", });
    internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_BuffEntry_descriptor =
      internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_BuffEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SyncTerritoryBuffSnapshotCmd_BuffEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_SyncTerritoryInfoCmd_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_SyncTerritoryInfoCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SyncTerritoryInfoCmd_descriptor,
        new java.lang.String[] { "TerritoryPower", "PartNum", "BuildingType", "TotalNum", "AlreadyBuiltNum", "AddPartOrReduce", "PartId", });
    internal_static_com_yorha_proto_SyncClanBuildStatusCmd_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_SyncClanBuildStatusCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SyncClanBuildStatusCmd_descriptor,
        new java.lang.String[] { "Id", "Status", "Point", "TemplateId", "IsClanResBuilding", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructClan.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

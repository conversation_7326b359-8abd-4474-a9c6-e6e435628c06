// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_hospital.proto

package com.yorha.proto;

public final class PlayerHospital {
  private PlayerHospital() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_HospitalTreat_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HospitalTreat_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return Whether the treatUnion field is set.
     */
    boolean hasTreatUnion();
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return The treatUnion.
     */
    com.yorha.proto.CommonMsg.HospitalTreatUnion getTreatUnion();
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     */
    com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder getTreatUnionOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HospitalTreat_C2S}
   */
  public static final class Player_HospitalTreat_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HospitalTreat_C2S)
      Player_HospitalTreat_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HospitalTreat_C2S.newBuilder() to construct.
    private Player_HospitalTreat_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HospitalTreat_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HospitalTreat_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HospitalTreat_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = treatUnion_.toBuilder();
              }
              treatUnion_ = input.readMessage(com.yorha.proto.CommonMsg.HospitalTreatUnion.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(treatUnion_);
                treatUnion_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S.class, com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int TREATUNION_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.HospitalTreatUnion treatUnion_;
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return Whether the treatUnion field is set.
     */
    @java.lang.Override
    public boolean hasTreatUnion() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return The treatUnion.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.HospitalTreatUnion getTreatUnion() {
      return treatUnion_ == null ? com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
    }
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder getTreatUnionOrBuilder() {
      return treatUnion_ == null ? com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getTreatUnion());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getTreatUnion());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S other = (com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S) obj;

      if (hasTreatUnion() != other.hasTreatUnion()) return false;
      if (hasTreatUnion()) {
        if (!getTreatUnion()
            .equals(other.getTreatUnion())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTreatUnion()) {
        hash = (37 * hash) + TREATUNION_FIELD_NUMBER;
        hash = (53 * hash) + getTreatUnion().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HospitalTreat_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HospitalTreat_C2S)
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S.class, com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTreatUnionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (treatUnionBuilder_ == null) {
          treatUnion_ = null;
        } else {
          treatUnionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S build() {
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S buildPartial() {
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S result = new com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (treatUnionBuilder_ == null) {
            result.treatUnion_ = treatUnion_;
          } else {
            result.treatUnion_ = treatUnionBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S other) {
        if (other == com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S.getDefaultInstance()) return this;
        if (other.hasTreatUnion()) {
          mergeTreatUnion(other.getTreatUnion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.HospitalTreatUnion treatUnion_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.HospitalTreatUnion, com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder, com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder> treatUnionBuilder_;
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       * @return Whether the treatUnion field is set.
       */
      public boolean hasTreatUnion() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       * @return The treatUnion.
       */
      public com.yorha.proto.CommonMsg.HospitalTreatUnion getTreatUnion() {
        if (treatUnionBuilder_ == null) {
          return treatUnion_ == null ? com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
        } else {
          return treatUnionBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder setTreatUnion(com.yorha.proto.CommonMsg.HospitalTreatUnion value) {
        if (treatUnionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          treatUnion_ = value;
          onChanged();
        } else {
          treatUnionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder setTreatUnion(
          com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder builderForValue) {
        if (treatUnionBuilder_ == null) {
          treatUnion_ = builderForValue.build();
          onChanged();
        } else {
          treatUnionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder mergeTreatUnion(com.yorha.proto.CommonMsg.HospitalTreatUnion value) {
        if (treatUnionBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              treatUnion_ != null &&
              treatUnion_ != com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance()) {
            treatUnion_ =
              com.yorha.proto.CommonMsg.HospitalTreatUnion.newBuilder(treatUnion_).mergeFrom(value).buildPartial();
          } else {
            treatUnion_ = value;
          }
          onChanged();
        } else {
          treatUnionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder clearTreatUnion() {
        if (treatUnionBuilder_ == null) {
          treatUnion_ = null;
          onChanged();
        } else {
          treatUnionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder getTreatUnionBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTreatUnionFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder getTreatUnionOrBuilder() {
        if (treatUnionBuilder_ != null) {
          return treatUnionBuilder_.getMessageOrBuilder();
        } else {
          return treatUnion_ == null ?
              com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.HospitalTreatUnion, com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder, com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder> 
          getTreatUnionFieldBuilder() {
        if (treatUnionBuilder_ == null) {
          treatUnionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.HospitalTreatUnion, com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder, com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder>(
                  getTreatUnion(),
                  getParentForChildren(),
                  isClean());
          treatUnion_ = null;
        }
        return treatUnionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HospitalTreat_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HospitalTreat_C2S)
    private static final com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S();
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HospitalTreat_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HospitalTreat_C2S>() {
      @java.lang.Override
      public Player_HospitalTreat_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HospitalTreat_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HospitalTreat_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HospitalTreat_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHospital.Player_HospitalTreat_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HospitalTreat_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HospitalTreat_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HospitalTreat_S2C}
   */
  public static final class Player_HospitalTreat_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HospitalTreat_S2C)
      Player_HospitalTreat_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HospitalTreat_S2C.newBuilder() to construct.
    private Player_HospitalTreat_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HospitalTreat_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HospitalTreat_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HospitalTreat_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C.class, com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C other = (com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HospitalTreat_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HospitalTreat_S2C)
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C.class, com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalTreat_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C build() {
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C buildPartial() {
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C result = new com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C other) {
        if (other == com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HospitalTreat_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HospitalTreat_S2C)
    private static final com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C();
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HospitalTreat_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HospitalTreat_S2C>() {
      @java.lang.Override
      public Player_HospitalTreat_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HospitalTreat_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HospitalTreat_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HospitalTreat_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHospital.Player_HospitalTreat_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HospitalFastTreat_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HospitalFastTreat_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return Whether the treatUnion field is set.
     */
    boolean hasTreatUnion();
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return The treatUnion.
     */
    com.yorha.proto.CommonMsg.HospitalTreatUnion getTreatUnion();
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     */
    com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder getTreatUnionOrBuilder();

    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return Whether the sPassWord field is set.
     */
    boolean hasSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The sPassWord.
     */
    java.lang.String getSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The bytes for sPassWord.
     */
    com.google.protobuf.ByteString
        getSPassWordBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HospitalFastTreat_C2S}
   */
  public static final class Player_HospitalFastTreat_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HospitalFastTreat_C2S)
      Player_HospitalFastTreat_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HospitalFastTreat_C2S.newBuilder() to construct.
    private Player_HospitalFastTreat_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HospitalFastTreat_C2S() {
      sPassWord_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HospitalFastTreat_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HospitalFastTreat_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = treatUnion_.toBuilder();
              }
              treatUnion_ = input.readMessage(com.yorha.proto.CommonMsg.HospitalTreatUnion.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(treatUnion_);
                treatUnion_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              sPassWord_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S.class, com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int TREATUNION_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.HospitalTreatUnion treatUnion_;
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return Whether the treatUnion field is set.
     */
    @java.lang.Override
    public boolean hasTreatUnion() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     * @return The treatUnion.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.HospitalTreatUnion getTreatUnion() {
      return treatUnion_ == null ? com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
    }
    /**
     * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder getTreatUnionOrBuilder() {
      return treatUnion_ == null ? com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
    }

    public static final int SPASSWORD_FIELD_NUMBER = 2;
    private volatile java.lang.Object sPassWord_;
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return Whether the sPassWord field is set.
     */
    @java.lang.Override
    public boolean hasSPassWord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The sPassWord.
     */
    @java.lang.Override
    public java.lang.String getSPassWord() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sPassWord_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The bytes for sPassWord.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSPassWordBytes() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sPassWord_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getTreatUnion());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, sPassWord_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getTreatUnion());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, sPassWord_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S other = (com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S) obj;

      if (hasTreatUnion() != other.hasTreatUnion()) return false;
      if (hasTreatUnion()) {
        if (!getTreatUnion()
            .equals(other.getTreatUnion())) return false;
      }
      if (hasSPassWord() != other.hasSPassWord()) return false;
      if (hasSPassWord()) {
        if (!getSPassWord()
            .equals(other.getSPassWord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTreatUnion()) {
        hash = (37 * hash) + TREATUNION_FIELD_NUMBER;
        hash = (53 * hash) + getTreatUnion().hashCode();
      }
      if (hasSPassWord()) {
        hash = (37 * hash) + SPASSWORD_FIELD_NUMBER;
        hash = (53 * hash) + getSPassWord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HospitalFastTreat_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HospitalFastTreat_C2S)
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S.class, com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTreatUnionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (treatUnionBuilder_ == null) {
          treatUnion_ = null;
        } else {
          treatUnionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        sPassWord_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S build() {
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S buildPartial() {
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S result = new com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (treatUnionBuilder_ == null) {
            result.treatUnion_ = treatUnion_;
          } else {
            result.treatUnion_ = treatUnionBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.sPassWord_ = sPassWord_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S other) {
        if (other == com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S.getDefaultInstance()) return this;
        if (other.hasTreatUnion()) {
          mergeTreatUnion(other.getTreatUnion());
        }
        if (other.hasSPassWord()) {
          bitField0_ |= 0x00000002;
          sPassWord_ = other.sPassWord_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.HospitalTreatUnion treatUnion_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.HospitalTreatUnion, com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder, com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder> treatUnionBuilder_;
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       * @return Whether the treatUnion field is set.
       */
      public boolean hasTreatUnion() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       * @return The treatUnion.
       */
      public com.yorha.proto.CommonMsg.HospitalTreatUnion getTreatUnion() {
        if (treatUnionBuilder_ == null) {
          return treatUnion_ == null ? com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
        } else {
          return treatUnionBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder setTreatUnion(com.yorha.proto.CommonMsg.HospitalTreatUnion value) {
        if (treatUnionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          treatUnion_ = value;
          onChanged();
        } else {
          treatUnionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder setTreatUnion(
          com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder builderForValue) {
        if (treatUnionBuilder_ == null) {
          treatUnion_ = builderForValue.build();
          onChanged();
        } else {
          treatUnionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder mergeTreatUnion(com.yorha.proto.CommonMsg.HospitalTreatUnion value) {
        if (treatUnionBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              treatUnion_ != null &&
              treatUnion_ != com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance()) {
            treatUnion_ =
              com.yorha.proto.CommonMsg.HospitalTreatUnion.newBuilder(treatUnion_).mergeFrom(value).buildPartial();
          } else {
            treatUnion_ = value;
          }
          onChanged();
        } else {
          treatUnionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public Builder clearTreatUnion() {
        if (treatUnionBuilder_ == null) {
          treatUnion_ = null;
          onChanged();
        } else {
          treatUnionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder getTreatUnionBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTreatUnionFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      public com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder getTreatUnionOrBuilder() {
        if (treatUnionBuilder_ != null) {
          return treatUnionBuilder_.getMessageOrBuilder();
        } else {
          return treatUnion_ == null ?
              com.yorha.proto.CommonMsg.HospitalTreatUnion.getDefaultInstance() : treatUnion_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.HospitalTreatUnion treatUnion = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.HospitalTreatUnion, com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder, com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder> 
          getTreatUnionFieldBuilder() {
        if (treatUnionBuilder_ == null) {
          treatUnionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.HospitalTreatUnion, com.yorha.proto.CommonMsg.HospitalTreatUnion.Builder, com.yorha.proto.CommonMsg.HospitalTreatUnionOrBuilder>(
                  getTreatUnion(),
                  getParentForChildren(),
                  isClean());
          treatUnion_ = null;
        }
        return treatUnionBuilder_;
      }

      private java.lang.Object sPassWord_ = "";
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return Whether the sPassWord field is set.
       */
      public boolean hasSPassWord() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return The sPassWord.
       */
      public java.lang.String getSPassWord() {
        java.lang.Object ref = sPassWord_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sPassWord_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return The bytes for sPassWord.
       */
      public com.google.protobuf.ByteString
          getSPassWordBytes() {
        java.lang.Object ref = sPassWord_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sPassWord_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @param value The sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWord(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSPassWord() {
        bitField0_ = (bitField0_ & ~0x00000002);
        sPassWord_ = getDefaultInstance().getSPassWord();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @param value The bytes for sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWordBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HospitalFastTreat_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HospitalFastTreat_C2S)
    private static final com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S();
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HospitalFastTreat_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HospitalFastTreat_C2S>() {
      @java.lang.Override
      public Player_HospitalFastTreat_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HospitalFastTreat_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HospitalFastTreat_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HospitalFastTreat_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HospitalFastTreat_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HospitalFastTreat_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HospitalFastTreat_S2C}
   */
  public static final class Player_HospitalFastTreat_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HospitalFastTreat_S2C)
      Player_HospitalFastTreat_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HospitalFastTreat_S2C.newBuilder() to construct.
    private Player_HospitalFastTreat_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HospitalFastTreat_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HospitalFastTreat_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HospitalFastTreat_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C.class, com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C other = (com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HospitalFastTreat_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HospitalFastTreat_S2C)
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C.class, com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C build() {
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C buildPartial() {
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C result = new com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C other) {
        if (other == com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HospitalFastTreat_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HospitalFastTreat_S2C)
    private static final com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C();
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HospitalFastTreat_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HospitalFastTreat_S2C>() {
      @java.lang.Override
      public Player_HospitalFastTreat_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HospitalFastTreat_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HospitalFastTreat_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HospitalFastTreat_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHospital.Player_HospitalFastTreat_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HospitalReturnSoldiers_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HospitalReturnSoldiers_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HospitalReturnSoldiers_C2S}
   */
  public static final class Player_HospitalReturnSoldiers_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HospitalReturnSoldiers_C2S)
      Player_HospitalReturnSoldiers_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HospitalReturnSoldiers_C2S.newBuilder() to construct.
    private Player_HospitalReturnSoldiers_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HospitalReturnSoldiers_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HospitalReturnSoldiers_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HospitalReturnSoldiers_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S.class, com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S other = (com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HospitalReturnSoldiers_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HospitalReturnSoldiers_C2S)
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S.class, com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S build() {
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S buildPartial() {
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S result = new com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S other) {
        if (other == com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HospitalReturnSoldiers_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HospitalReturnSoldiers_C2S)
    private static final com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S();
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HospitalReturnSoldiers_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HospitalReturnSoldiers_C2S>() {
      @java.lang.Override
      public Player_HospitalReturnSoldiers_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HospitalReturnSoldiers_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HospitalReturnSoldiers_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HospitalReturnSoldiers_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HospitalReturnSoldiers_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HospitalReturnSoldiers_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HospitalReturnSoldiers_S2C}
   */
  public static final class Player_HospitalReturnSoldiers_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HospitalReturnSoldiers_S2C)
      Player_HospitalReturnSoldiers_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HospitalReturnSoldiers_S2C.newBuilder() to construct.
    private Player_HospitalReturnSoldiers_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HospitalReturnSoldiers_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HospitalReturnSoldiers_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HospitalReturnSoldiers_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C.class, com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C other = (com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HospitalReturnSoldiers_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HospitalReturnSoldiers_S2C)
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C.class, com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHospital.internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C build() {
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C buildPartial() {
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C result = new com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C other) {
        if (other == com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HospitalReturnSoldiers_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HospitalReturnSoldiers_S2C)
    private static final com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C();
    }

    public static com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HospitalReturnSoldiers_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HospitalReturnSoldiers_S2C>() {
      @java.lang.Override
      public Player_HospitalReturnSoldiers_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HospitalReturnSoldiers_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HospitalReturnSoldiers_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HospitalReturnSoldiers_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHospital.Player_HospitalReturnSoldiers_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HospitalTreat_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HospitalTreat_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HospitalTreat_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HospitalTreat_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,ss_proto/gen/player/cs/player_hospital" +
      ".proto\022\017com.yorha.proto\032$ss_proto/gen/co" +
      "mmon/common_msg.proto\"S\n\030Player_Hospital" +
      "Treat_C2S\0227\n\ntreatUnion\030\001 \001(\0132#.com.yorh" +
      "a.proto.HospitalTreatUnion\"\032\n\030Player_Hos" +
      "pitalTreat_S2C\"j\n\034Player_HospitalFastTre" +
      "at_C2S\0227\n\ntreatUnion\030\001 \001(\0132#.com.yorha.p" +
      "roto.HospitalTreatUnion\022\021\n\tsPassWord\030\002 \001" +
      "(\t\"\036\n\034Player_HospitalFastTreat_S2C\"#\n!Pl" +
      "ayer_HospitalReturnSoldiers_C2S\"#\n!Playe" +
      "r_HospitalReturnSoldiers_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_HospitalTreat_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_HospitalTreat_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HospitalTreat_C2S_descriptor,
        new java.lang.String[] { "TreatUnion", });
    internal_static_com_yorha_proto_Player_HospitalTreat_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_HospitalTreat_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HospitalTreat_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HospitalFastTreat_C2S_descriptor,
        new java.lang.String[] { "TreatUnion", "SPassWord", });
    internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HospitalFastTreat_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HospitalReturnSoldiers_S2C_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

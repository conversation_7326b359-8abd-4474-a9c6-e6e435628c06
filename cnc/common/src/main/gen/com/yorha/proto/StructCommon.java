// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/common/struct_common.proto

package com.yorha.proto;

public final class StructCommon {
  private StructCommon() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ProgressInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ProgressInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return Whether the uid field is set.
     */
    boolean hasUid();
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return The uid.
     */
    long getUid();

    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return Whether the lastCalNum field is set.
     */
    boolean hasLastCalNum();
    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return The lastCalNum.
     */
    long getLastCalNum();

    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return Whether the maxNum field is set.
     */
    boolean hasMaxNum();
    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return The maxNum.
     */
    long getMaxNum();

    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return Whether the lastCalTsMs field is set.
     */
    boolean hasLastCalTsMs();
    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return The lastCalTsMs.
     */
    long getLastCalTsMs();

    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return Whether the speed field is set.
     */
    boolean hasSpeed();
    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return The speed.
     */
    long getSpeed();

    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    boolean hasStateEndTsMs();
    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return The stateEndTsMs.
     */
    long getStateEndTsMs();

    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    boolean hasStateStartTsMs();
    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return The stateStartTsMs.
     */
    long getStateStartTsMs();

    /**
     * <pre>
     * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
     * </pre>
     *
     * <code>optional int64 enterAdditionValue = 8;</code>
     * @return Whether the enterAdditionValue field is set.
     */
    boolean hasEnterAdditionValue();
    /**
     * <pre>
     * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
     * </pre>
     *
     * <code>optional int64 enterAdditionValue = 8;</code>
     * @return The enterAdditionValue.
     */
    long getEnterAdditionValue();
  }
  /**
   * <pre>
   * 进度条数据结构，有军队进出会主动结算，客户端仅需拿到上次结算值和速度即可显示假的结算值
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.ProgressInfo}
   */
  public static final class ProgressInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ProgressInfo)
      ProgressInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ProgressInfo.newBuilder() to construct.
    private ProgressInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ProgressInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ProgressInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ProgressInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              uid_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              lastCalNum_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              maxNum_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              lastCalTsMs_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              speed_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              stateEndTsMs_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              stateStartTsMs_ = input.readInt64();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              enterAdditionValue_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_ProgressInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_ProgressInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.ProgressInfo.class, com.yorha.proto.StructCommon.ProgressInfo.Builder.class);
    }

    private int bitField0_;
    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return Whether the uid field is set.
     */
    @java.lang.Override
    public boolean hasUid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return The uid.
     */
    @java.lang.Override
    public long getUid() {
      return uid_;
    }

    public static final int LASTCALNUM_FIELD_NUMBER = 2;
    private long lastCalNum_;
    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return Whether the lastCalNum field is set.
     */
    @java.lang.Override
    public boolean hasLastCalNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return The lastCalNum.
     */
    @java.lang.Override
    public long getLastCalNum() {
      return lastCalNum_;
    }

    public static final int MAXNUM_FIELD_NUMBER = 3;
    private long maxNum_;
    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return Whether the maxNum field is set.
     */
    @java.lang.Override
    public boolean hasMaxNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return The maxNum.
     */
    @java.lang.Override
    public long getMaxNum() {
      return maxNum_;
    }

    public static final int LASTCALTSMS_FIELD_NUMBER = 4;
    private long lastCalTsMs_;
    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return Whether the lastCalTsMs field is set.
     */
    @java.lang.Override
    public boolean hasLastCalTsMs() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return The lastCalTsMs.
     */
    @java.lang.Override
    public long getLastCalTsMs() {
      return lastCalTsMs_;
    }

    public static final int SPEED_FIELD_NUMBER = 5;
    private long speed_;
    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return Whether the speed field is set.
     */
    @java.lang.Override
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return The speed.
     */
    @java.lang.Override
    public long getSpeed() {
      return speed_;
    }

    public static final int STATEENDTSMS_FIELD_NUMBER = 6;
    private long stateEndTsMs_;
    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateEndTsMs() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return The stateEndTsMs.
     */
    @java.lang.Override
    public long getStateEndTsMs() {
      return stateEndTsMs_;
    }

    public static final int STATESTARTTSMS_FIELD_NUMBER = 7;
    private long stateStartTsMs_;
    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateStartTsMs() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return The stateStartTsMs.
     */
    @java.lang.Override
    public long getStateStartTsMs() {
      return stateStartTsMs_;
    }

    public static final int ENTERADDITIONVALUE_FIELD_NUMBER = 8;
    private long enterAdditionValue_;
    /**
     * <pre>
     * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
     * </pre>
     *
     * <code>optional int64 enterAdditionValue = 8;</code>
     * @return Whether the enterAdditionValue field is set.
     */
    @java.lang.Override
    public boolean hasEnterAdditionValue() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
     * </pre>
     *
     * <code>optional int64 enterAdditionValue = 8;</code>
     * @return The enterAdditionValue.
     */
    @java.lang.Override
    public long getEnterAdditionValue() {
      return enterAdditionValue_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, uid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, lastCalNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, maxNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, lastCalTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, speed_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt64(6, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt64(7, stateStartTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt64(8, enterAdditionValue_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, uid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, lastCalNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, maxNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, lastCalTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, speed_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, stateStartTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, enterAdditionValue_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.ProgressInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.ProgressInfo other = (com.yorha.proto.StructCommon.ProgressInfo) obj;

      if (hasUid() != other.hasUid()) return false;
      if (hasUid()) {
        if (getUid()
            != other.getUid()) return false;
      }
      if (hasLastCalNum() != other.hasLastCalNum()) return false;
      if (hasLastCalNum()) {
        if (getLastCalNum()
            != other.getLastCalNum()) return false;
      }
      if (hasMaxNum() != other.hasMaxNum()) return false;
      if (hasMaxNum()) {
        if (getMaxNum()
            != other.getMaxNum()) return false;
      }
      if (hasLastCalTsMs() != other.hasLastCalTsMs()) return false;
      if (hasLastCalTsMs()) {
        if (getLastCalTsMs()
            != other.getLastCalTsMs()) return false;
      }
      if (hasSpeed() != other.hasSpeed()) return false;
      if (hasSpeed()) {
        if (getSpeed()
            != other.getSpeed()) return false;
      }
      if (hasStateEndTsMs() != other.hasStateEndTsMs()) return false;
      if (hasStateEndTsMs()) {
        if (getStateEndTsMs()
            != other.getStateEndTsMs()) return false;
      }
      if (hasStateStartTsMs() != other.hasStateStartTsMs()) return false;
      if (hasStateStartTsMs()) {
        if (getStateStartTsMs()
            != other.getStateStartTsMs()) return false;
      }
      if (hasEnterAdditionValue() != other.hasEnterAdditionValue()) return false;
      if (hasEnterAdditionValue()) {
        if (getEnterAdditionValue()
            != other.getEnterAdditionValue()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUid()) {
        hash = (37 * hash) + UID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUid());
      }
      if (hasLastCalNum()) {
        hash = (37 * hash) + LASTCALNUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLastCalNum());
      }
      if (hasMaxNum()) {
        hash = (37 * hash) + MAXNUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMaxNum());
      }
      if (hasLastCalTsMs()) {
        hash = (37 * hash) + LASTCALTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLastCalTsMs());
      }
      if (hasSpeed()) {
        hash = (37 * hash) + SPEED_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSpeed());
      }
      if (hasStateEndTsMs()) {
        hash = (37 * hash) + STATEENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateEndTsMs());
      }
      if (hasStateStartTsMs()) {
        hash = (37 * hash) + STATESTARTTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateStartTsMs());
      }
      if (hasEnterAdditionValue()) {
        hash = (37 * hash) + ENTERADDITIONVALUE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEnterAdditionValue());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.ProgressInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.ProgressInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 进度条数据结构，有军队进出会主动结算，客户端仅需拿到上次结算值和速度即可显示假的结算值
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.ProgressInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ProgressInfo)
        com.yorha.proto.StructCommon.ProgressInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_ProgressInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_ProgressInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.ProgressInfo.class, com.yorha.proto.StructCommon.ProgressInfo.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.ProgressInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        lastCalNum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        maxNum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        lastCalTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        speed_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        stateEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        stateStartTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        enterAdditionValue_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_ProgressInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.ProgressInfo getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.ProgressInfo build() {
        com.yorha.proto.StructCommon.ProgressInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.ProgressInfo buildPartial() {
        com.yorha.proto.StructCommon.ProgressInfo result = new com.yorha.proto.StructCommon.ProgressInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.uid_ = uid_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lastCalNum_ = lastCalNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.maxNum_ = maxNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lastCalTsMs_ = lastCalTsMs_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.speed_ = speed_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.stateEndTsMs_ = stateEndTsMs_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.stateStartTsMs_ = stateStartTsMs_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.enterAdditionValue_ = enterAdditionValue_;
          to_bitField0_ |= 0x00000080;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.ProgressInfo) {
          return mergeFrom((com.yorha.proto.StructCommon.ProgressInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.ProgressInfo other) {
        if (other == com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance()) return this;
        if (other.hasUid()) {
          setUid(other.getUid());
        }
        if (other.hasLastCalNum()) {
          setLastCalNum(other.getLastCalNum());
        }
        if (other.hasMaxNum()) {
          setMaxNum(other.getMaxNum());
        }
        if (other.hasLastCalTsMs()) {
          setLastCalTsMs(other.getLastCalTsMs());
        }
        if (other.hasSpeed()) {
          setSpeed(other.getSpeed());
        }
        if (other.hasStateEndTsMs()) {
          setStateEndTsMs(other.getStateEndTsMs());
        }
        if (other.hasStateStartTsMs()) {
          setStateStartTsMs(other.getStateStartTsMs());
        }
        if (other.hasEnterAdditionValue()) {
          setEnterAdditionValue(other.getEnterAdditionValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.ProgressInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.ProgressInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long uid_ ;
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @return Whether the uid field is set.
       */
      @java.lang.Override
      public boolean hasUid() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @return The uid.
       */
      @java.lang.Override
      public long getUid() {
        return uid_;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(long value) {
        bitField0_ |= 0x00000001;
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        uid_ = 0L;
        onChanged();
        return this;
      }

      private long lastCalNum_ ;
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @return Whether the lastCalNum field is set.
       */
      @java.lang.Override
      public boolean hasLastCalNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @return The lastCalNum.
       */
      @java.lang.Override
      public long getLastCalNum() {
        return lastCalNum_;
      }
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @param value The lastCalNum to set.
       * @return This builder for chaining.
       */
      public Builder setLastCalNum(long value) {
        bitField0_ |= 0x00000002;
        lastCalNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastCalNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lastCalNum_ = 0L;
        onChanged();
        return this;
      }

      private long maxNum_ ;
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @return Whether the maxNum field is set.
       */
      @java.lang.Override
      public boolean hasMaxNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @return The maxNum.
       */
      @java.lang.Override
      public long getMaxNum() {
        return maxNum_;
      }
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @param value The maxNum to set.
       * @return This builder for chaining.
       */
      public Builder setMaxNum(long value) {
        bitField0_ |= 0x00000004;
        maxNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        maxNum_ = 0L;
        onChanged();
        return this;
      }

      private long lastCalTsMs_ ;
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @return Whether the lastCalTsMs field is set.
       */
      @java.lang.Override
      public boolean hasLastCalTsMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @return The lastCalTsMs.
       */
      @java.lang.Override
      public long getLastCalTsMs() {
        return lastCalTsMs_;
      }
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @param value The lastCalTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setLastCalTsMs(long value) {
        bitField0_ |= 0x00000008;
        lastCalTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastCalTsMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lastCalTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long speed_ ;
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @return Whether the speed field is set.
       */
      @java.lang.Override
      public boolean hasSpeed() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @return The speed.
       */
      @java.lang.Override
      public long getSpeed() {
        return speed_;
      }
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @param value The speed to set.
       * @return This builder for chaining.
       */
      public Builder setSpeed(long value) {
        bitField0_ |= 0x00000010;
        speed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSpeed() {
        bitField0_ = (bitField0_ & ~0x00000010);
        speed_ = 0L;
        onChanged();
        return this;
      }

      private long stateEndTsMs_ ;
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @return Whether the stateEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateEndTsMs() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @return The stateEndTsMs.
       */
      @java.lang.Override
      public long getStateEndTsMs() {
        return stateEndTsMs_;
      }
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @param value The stateEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateEndTsMs(long value) {
        bitField0_ |= 0x00000020;
        stateEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000020);
        stateEndTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long stateStartTsMs_ ;
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @return Whether the stateStartTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateStartTsMs() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @return The stateStartTsMs.
       */
      @java.lang.Override
      public long getStateStartTsMs() {
        return stateStartTsMs_;
      }
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @param value The stateStartTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateStartTsMs(long value) {
        bitField0_ |= 0x00000040;
        stateStartTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateStartTsMs() {
        bitField0_ = (bitField0_ & ~0x00000040);
        stateStartTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long enterAdditionValue_ ;
      /**
       * <pre>
       * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
       * </pre>
       *
       * <code>optional int64 enterAdditionValue = 8;</code>
       * @return Whether the enterAdditionValue field is set.
       */
      @java.lang.Override
      public boolean hasEnterAdditionValue() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
       * </pre>
       *
       * <code>optional int64 enterAdditionValue = 8;</code>
       * @return The enterAdditionValue.
       */
      @java.lang.Override
      public long getEnterAdditionValue() {
        return enterAdditionValue_;
      }
      /**
       * <pre>
       * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
       * </pre>
       *
       * <code>optional int64 enterAdditionValue = 8;</code>
       * @param value The enterAdditionValue to set.
       * @return This builder for chaining.
       */
      public Builder setEnterAdditionValue(long value) {
        bitField0_ |= 0x00000080;
        enterAdditionValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 进度条创建时的加成值，用于计算进入时的速度，不需要发给客户端，当前仅用在军团资源中心
       * </pre>
       *
       * <code>optional int64 enterAdditionValue = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnterAdditionValue() {
        bitField0_ = (bitField0_ & ~0x00000080);
        enterAdditionValue_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ProgressInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ProgressInfo)
    private static final com.yorha.proto.StructCommon.ProgressInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.ProgressInfo();
    }

    public static com.yorha.proto.StructCommon.ProgressInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ProgressInfo>
        PARSER = new com.google.protobuf.AbstractParser<ProgressInfo>() {
      @java.lang.Override
      public ProgressInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ProgressInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ProgressInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ProgressInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.ProgressInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface InvitePlayerRecordOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.InvitePlayerRecord)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return Whether the inviteTsMs field is set.
     */
    boolean hasInviteTsMs();
    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return The inviteTsMs.
     */
    long getInviteTsMs();
  }
  /**
   * <pre>
   * 军团邀请单人数据
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.InvitePlayerRecord}
   */
  public static final class InvitePlayerRecord extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.InvitePlayerRecord)
      InvitePlayerRecordOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use InvitePlayerRecord.newBuilder() to construct.
    private InvitePlayerRecord(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private InvitePlayerRecord() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new InvitePlayerRecord();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private InvitePlayerRecord(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              inviteTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_InvitePlayerRecord_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_InvitePlayerRecord_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.InvitePlayerRecord.class, com.yorha.proto.StructCommon.InvitePlayerRecord.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int INVITETSMS_FIELD_NUMBER = 2;
    private long inviteTsMs_;
    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return Whether the inviteTsMs field is set.
     */
    @java.lang.Override
    public boolean hasInviteTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return The inviteTsMs.
     */
    @java.lang.Override
    public long getInviteTsMs() {
      return inviteTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, inviteTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, inviteTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.InvitePlayerRecord)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.InvitePlayerRecord other = (com.yorha.proto.StructCommon.InvitePlayerRecord) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasInviteTsMs() != other.hasInviteTsMs()) return false;
      if (hasInviteTsMs()) {
        if (getInviteTsMs()
            != other.getInviteTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasInviteTsMs()) {
        hash = (37 * hash) + INVITETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getInviteTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.InvitePlayerRecord parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.InvitePlayerRecord prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 军团邀请单人数据
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.InvitePlayerRecord}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.InvitePlayerRecord)
        com.yorha.proto.StructCommon.InvitePlayerRecordOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_InvitePlayerRecord_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_InvitePlayerRecord_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.InvitePlayerRecord.class, com.yorha.proto.StructCommon.InvitePlayerRecord.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.InvitePlayerRecord.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        inviteTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_InvitePlayerRecord_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.InvitePlayerRecord getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.InvitePlayerRecord.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.InvitePlayerRecord build() {
        com.yorha.proto.StructCommon.InvitePlayerRecord result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.InvitePlayerRecord buildPartial() {
        com.yorha.proto.StructCommon.InvitePlayerRecord result = new com.yorha.proto.StructCommon.InvitePlayerRecord(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.inviteTsMs_ = inviteTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.InvitePlayerRecord) {
          return mergeFrom((com.yorha.proto.StructCommon.InvitePlayerRecord)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.InvitePlayerRecord other) {
        if (other == com.yorha.proto.StructCommon.InvitePlayerRecord.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasInviteTsMs()) {
          setInviteTsMs(other.getInviteTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.InvitePlayerRecord parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.InvitePlayerRecord) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long inviteTsMs_ ;
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @return Whether the inviteTsMs field is set.
       */
      @java.lang.Override
      public boolean hasInviteTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @return The inviteTsMs.
       */
      @java.lang.Override
      public long getInviteTsMs() {
        return inviteTsMs_;
      }
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @param value The inviteTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setInviteTsMs(long value) {
        bitField0_ |= 0x00000002;
        inviteTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInviteTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        inviteTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.InvitePlayerRecord)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.InvitePlayerRecord)
    private static final com.yorha.proto.StructCommon.InvitePlayerRecord DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.InvitePlayerRecord();
    }

    public static com.yorha.proto.StructCommon.InvitePlayerRecord getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<InvitePlayerRecord>
        PARSER = new com.google.protobuf.AbstractParser<InvitePlayerRecord>() {
      @java.lang.Override
      public InvitePlayerRecord parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new InvitePlayerRecord(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<InvitePlayerRecord> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<InvitePlayerRecord> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.InvitePlayerRecord getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DungeonSkillSysOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DungeonSkillSys)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
     * @return Whether the skill field is set.
     */
    boolean hasSkill();
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
     * @return The skill.
     */
    com.yorha.proto.StructCommon.Int32DungeonSkillItemMap getSkill();
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
     */
    com.yorha.proto.StructCommon.Int32DungeonSkillItemMapOrBuilder getSkillOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DungeonSkillSys}
   */
  public static final class DungeonSkillSys extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DungeonSkillSys)
      DungeonSkillSysOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DungeonSkillSys.newBuilder() to construct.
    private DungeonSkillSys(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DungeonSkillSys() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DungeonSkillSys();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DungeonSkillSys(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = skill_.toBuilder();
              }
              skill_ = input.readMessage(com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(skill_);
                skill_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillSys_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillSys_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.DungeonSkillSys.class, com.yorha.proto.StructCommon.DungeonSkillSys.Builder.class);
    }

    private int bitField0_;
    public static final int SKILL_FIELD_NUMBER = 1;
    private com.yorha.proto.StructCommon.Int32DungeonSkillItemMap skill_;
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
     * @return Whether the skill field is set.
     */
    @java.lang.Override
    public boolean hasSkill() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
     * @return The skill.
     */
    @java.lang.Override
    public com.yorha.proto.StructCommon.Int32DungeonSkillItemMap getSkill() {
      return skill_ == null ? com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.getDefaultInstance() : skill_;
    }
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructCommon.Int32DungeonSkillItemMapOrBuilder getSkillOrBuilder() {
      return skill_ == null ? com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.getDefaultInstance() : skill_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getSkill());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getSkill());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.DungeonSkillSys)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.DungeonSkillSys other = (com.yorha.proto.StructCommon.DungeonSkillSys) obj;

      if (hasSkill() != other.hasSkill()) return false;
      if (hasSkill()) {
        if (!getSkill()
            .equals(other.getSkill())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkill()) {
        hash = (37 * hash) + SKILL_FIELD_NUMBER;
        hash = (53 * hash) + getSkill().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillSys parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.DungeonSkillSys prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DungeonSkillSys}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DungeonSkillSys)
        com.yorha.proto.StructCommon.DungeonSkillSysOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillSys_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillSys_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.DungeonSkillSys.class, com.yorha.proto.StructCommon.DungeonSkillSys.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.DungeonSkillSys.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSkillFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (skillBuilder_ == null) {
          skill_ = null;
        } else {
          skillBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillSys_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.DungeonSkillSys getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.DungeonSkillSys.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.DungeonSkillSys build() {
        com.yorha.proto.StructCommon.DungeonSkillSys result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.DungeonSkillSys buildPartial() {
        com.yorha.proto.StructCommon.DungeonSkillSys result = new com.yorha.proto.StructCommon.DungeonSkillSys(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (skillBuilder_ == null) {
            result.skill_ = skill_;
          } else {
            result.skill_ = skillBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.DungeonSkillSys) {
          return mergeFrom((com.yorha.proto.StructCommon.DungeonSkillSys)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.DungeonSkillSys other) {
        if (other == com.yorha.proto.StructCommon.DungeonSkillSys.getDefaultInstance()) return this;
        if (other.hasSkill()) {
          mergeSkill(other.getSkill());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.DungeonSkillSys parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.DungeonSkillSys) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructCommon.Int32DungeonSkillItemMap skill_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommon.Int32DungeonSkillItemMap, com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder, com.yorha.proto.StructCommon.Int32DungeonSkillItemMapOrBuilder> skillBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       * @return Whether the skill field is set.
       */
      public boolean hasSkill() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       * @return The skill.
       */
      public com.yorha.proto.StructCommon.Int32DungeonSkillItemMap getSkill() {
        if (skillBuilder_ == null) {
          return skill_ == null ? com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.getDefaultInstance() : skill_;
        } else {
          return skillBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       */
      public Builder setSkill(com.yorha.proto.StructCommon.Int32DungeonSkillItemMap value) {
        if (skillBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          skill_ = value;
          onChanged();
        } else {
          skillBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       */
      public Builder setSkill(
          com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder builderForValue) {
        if (skillBuilder_ == null) {
          skill_ = builderForValue.build();
          onChanged();
        } else {
          skillBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       */
      public Builder mergeSkill(com.yorha.proto.StructCommon.Int32DungeonSkillItemMap value) {
        if (skillBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              skill_ != null &&
              skill_ != com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.getDefaultInstance()) {
            skill_ =
              com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.newBuilder(skill_).mergeFrom(value).buildPartial();
          } else {
            skill_ = value;
          }
          onChanged();
        } else {
          skillBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       */
      public Builder clearSkill() {
        if (skillBuilder_ == null) {
          skill_ = null;
          onChanged();
        } else {
          skillBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       */
      public com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder getSkillBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSkillFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       */
      public com.yorha.proto.StructCommon.Int32DungeonSkillItemMapOrBuilder getSkillOrBuilder() {
        if (skillBuilder_ != null) {
          return skillBuilder_.getMessageOrBuilder();
        } else {
          return skill_ == null ?
              com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.getDefaultInstance() : skill_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMap skill = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommon.Int32DungeonSkillItemMap, com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder, com.yorha.proto.StructCommon.Int32DungeonSkillItemMapOrBuilder> 
          getSkillFieldBuilder() {
        if (skillBuilder_ == null) {
          skillBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructCommon.Int32DungeonSkillItemMap, com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder, com.yorha.proto.StructCommon.Int32DungeonSkillItemMapOrBuilder>(
                  getSkill(),
                  getParentForChildren(),
                  isClean());
          skill_ = null;
        }
        return skillBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DungeonSkillSys)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DungeonSkillSys)
    private static final com.yorha.proto.StructCommon.DungeonSkillSys DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.DungeonSkillSys();
    }

    public static com.yorha.proto.StructCommon.DungeonSkillSys getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DungeonSkillSys>
        PARSER = new com.google.protobuf.AbstractParser<DungeonSkillSys>() {
      @java.lang.Override
      public DungeonSkillSys parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DungeonSkillSys(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DungeonSkillSys> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DungeonSkillSys> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.DungeonSkillSys getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DungeonSkillItemOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DungeonSkillItem)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return Whether the skillId field is set.
     */
    boolean hasSkillId();
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return The skillId.
     */
    int getSkillId();

    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return Whether the canUseNum field is set.
     */
    boolean hasCanUseNum();
    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return The canUseNum.
     */
    int getCanUseNum();

    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return Whether the canUseTsMs field is set.
     */
    boolean hasCanUseTsMs();
    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return The canUseTsMs.
     */
    long getCanUseTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DungeonSkillItem}
   */
  public static final class DungeonSkillItem extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DungeonSkillItem)
      DungeonSkillItemOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DungeonSkillItem.newBuilder() to construct.
    private DungeonSkillItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DungeonSkillItem() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DungeonSkillItem();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DungeonSkillItem(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              skillId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              canUseNum_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              canUseTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.DungeonSkillItem.class, com.yorha.proto.StructCommon.DungeonSkillItem.Builder.class);
    }

    private int bitField0_;
    public static final int SKILLID_FIELD_NUMBER = 1;
    private int skillId_;
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return Whether the skillId field is set.
     */
    @java.lang.Override
    public boolean hasSkillId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return The skillId.
     */
    @java.lang.Override
    public int getSkillId() {
      return skillId_;
    }

    public static final int CANUSENUM_FIELD_NUMBER = 2;
    private int canUseNum_;
    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return Whether the canUseNum field is set.
     */
    @java.lang.Override
    public boolean hasCanUseNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return The canUseNum.
     */
    @java.lang.Override
    public int getCanUseNum() {
      return canUseNum_;
    }

    public static final int CANUSETSMS_FIELD_NUMBER = 3;
    private long canUseTsMs_;
    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return Whether the canUseTsMs field is set.
     */
    @java.lang.Override
    public boolean hasCanUseTsMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return The canUseTsMs.
     */
    @java.lang.Override
    public long getCanUseTsMs() {
      return canUseTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, skillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, canUseNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, canUseTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, skillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, canUseNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, canUseTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.DungeonSkillItem)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.DungeonSkillItem other = (com.yorha.proto.StructCommon.DungeonSkillItem) obj;

      if (hasSkillId() != other.hasSkillId()) return false;
      if (hasSkillId()) {
        if (getSkillId()
            != other.getSkillId()) return false;
      }
      if (hasCanUseNum() != other.hasCanUseNum()) return false;
      if (hasCanUseNum()) {
        if (getCanUseNum()
            != other.getCanUseNum()) return false;
      }
      if (hasCanUseTsMs() != other.hasCanUseTsMs()) return false;
      if (hasCanUseTsMs()) {
        if (getCanUseTsMs()
            != other.getCanUseTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkillId()) {
        hash = (37 * hash) + SKILLID_FIELD_NUMBER;
        hash = (53 * hash) + getSkillId();
      }
      if (hasCanUseNum()) {
        hash = (37 * hash) + CANUSENUM_FIELD_NUMBER;
        hash = (53 * hash) + getCanUseNum();
      }
      if (hasCanUseTsMs()) {
        hash = (37 * hash) + CANUSETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCanUseTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.DungeonSkillItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.DungeonSkillItem prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DungeonSkillItem}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DungeonSkillItem)
        com.yorha.proto.StructCommon.DungeonSkillItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillItem_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.DungeonSkillItem.class, com.yorha.proto.StructCommon.DungeonSkillItem.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.DungeonSkillItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skillId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        canUseNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        canUseTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_DungeonSkillItem_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.DungeonSkillItem getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.DungeonSkillItem.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.DungeonSkillItem build() {
        com.yorha.proto.StructCommon.DungeonSkillItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.DungeonSkillItem buildPartial() {
        com.yorha.proto.StructCommon.DungeonSkillItem result = new com.yorha.proto.StructCommon.DungeonSkillItem(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.skillId_ = skillId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.canUseNum_ = canUseNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.canUseTsMs_ = canUseTsMs_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.DungeonSkillItem) {
          return mergeFrom((com.yorha.proto.StructCommon.DungeonSkillItem)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.DungeonSkillItem other) {
        if (other == com.yorha.proto.StructCommon.DungeonSkillItem.getDefaultInstance()) return this;
        if (other.hasSkillId()) {
          setSkillId(other.getSkillId());
        }
        if (other.hasCanUseNum()) {
          setCanUseNum(other.getCanUseNum());
        }
        if (other.hasCanUseTsMs()) {
          setCanUseTsMs(other.getCanUseTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.DungeonSkillItem parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.DungeonSkillItem) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int skillId_ ;
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return Whether the skillId field is set.
       */
      @java.lang.Override
      public boolean hasSkillId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return The skillId.
       */
      @java.lang.Override
      public int getSkillId() {
        return skillId_;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @param value The skillId to set.
       * @return This builder for chaining.
       */
      public Builder setSkillId(int value) {
        bitField0_ |= 0x00000001;
        skillId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        skillId_ = 0;
        onChanged();
        return this;
      }

      private int canUseNum_ ;
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @return Whether the canUseNum field is set.
       */
      @java.lang.Override
      public boolean hasCanUseNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @return The canUseNum.
       */
      @java.lang.Override
      public int getCanUseNum() {
        return canUseNum_;
      }
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @param value The canUseNum to set.
       * @return This builder for chaining.
       */
      public Builder setCanUseNum(int value) {
        bitField0_ |= 0x00000002;
        canUseNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCanUseNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        canUseNum_ = 0;
        onChanged();
        return this;
      }

      private long canUseTsMs_ ;
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @return Whether the canUseTsMs field is set.
       */
      @java.lang.Override
      public boolean hasCanUseTsMs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @return The canUseTsMs.
       */
      @java.lang.Override
      public long getCanUseTsMs() {
        return canUseTsMs_;
      }
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @param value The canUseTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setCanUseTsMs(long value) {
        bitField0_ |= 0x00000004;
        canUseTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCanUseTsMs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        canUseTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DungeonSkillItem)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DungeonSkillItem)
    private static final com.yorha.proto.StructCommon.DungeonSkillItem DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.DungeonSkillItem();
    }

    public static com.yorha.proto.StructCommon.DungeonSkillItem getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DungeonSkillItem>
        PARSER = new com.google.protobuf.AbstractParser<DungeonSkillItem>() {
      @java.lang.Override
      public DungeonSkillItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DungeonSkillItem(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DungeonSkillItem> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DungeonSkillItem> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.DungeonSkillItem getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CrossDataModelOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CrossDataModel)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
     * @return The data.
     */
    com.yorha.proto.StructCommon.Int32CrossDataMap getData();
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
     */
    com.yorha.proto.StructCommon.Int32CrossDataMapOrBuilder getDataOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CrossDataModel}
   */
  public static final class CrossDataModel extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CrossDataModel)
      CrossDataModelOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CrossDataModel.newBuilder() to construct.
    private CrossDataModel(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CrossDataModel() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CrossDataModel();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CrossDataModel(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructCommon.Int32CrossDataMap.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = data_.toBuilder();
              }
              data_ = input.readMessage(com.yorha.proto.StructCommon.Int32CrossDataMap.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(data_);
                data_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossDataModel_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossDataModel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.CrossDataModel.class, com.yorha.proto.StructCommon.CrossDataModel.Builder.class);
    }

    private int bitField0_;
    public static final int DATA_FIELD_NUMBER = 1;
    private com.yorha.proto.StructCommon.Int32CrossDataMap data_;
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
     * @return Whether the data field is set.
     */
    @java.lang.Override
    public boolean hasData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.yorha.proto.StructCommon.Int32CrossDataMap getData() {
      return data_ == null ? com.yorha.proto.StructCommon.Int32CrossDataMap.getDefaultInstance() : data_;
    }
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructCommon.Int32CrossDataMapOrBuilder getDataOrBuilder() {
      return data_ == null ? com.yorha.proto.StructCommon.Int32CrossDataMap.getDefaultInstance() : data_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.CrossDataModel)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.CrossDataModel other = (com.yorha.proto.StructCommon.CrossDataModel) obj;

      if (hasData() != other.hasData()) return false;
      if (hasData()) {
        if (!getData()
            .equals(other.getData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasData()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.CrossDataModel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.CrossDataModel prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CrossDataModel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CrossDataModel)
        com.yorha.proto.StructCommon.CrossDataModelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossDataModel_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossDataModel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.CrossDataModel.class, com.yorha.proto.StructCommon.CrossDataModel.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.CrossDataModel.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dataBuilder_ == null) {
          data_ = null;
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossDataModel_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.CrossDataModel getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.CrossDataModel.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.CrossDataModel build() {
        com.yorha.proto.StructCommon.CrossDataModel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.CrossDataModel buildPartial() {
        com.yorha.proto.StructCommon.CrossDataModel result = new com.yorha.proto.StructCommon.CrossDataModel(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (dataBuilder_ == null) {
            result.data_ = data_;
          } else {
            result.data_ = dataBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.CrossDataModel) {
          return mergeFrom((com.yorha.proto.StructCommon.CrossDataModel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.CrossDataModel other) {
        if (other == com.yorha.proto.StructCommon.CrossDataModel.getDefaultInstance()) return this;
        if (other.hasData()) {
          mergeData(other.getData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.CrossDataModel parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.CrossDataModel) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructCommon.Int32CrossDataMap data_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommon.Int32CrossDataMap, com.yorha.proto.StructCommon.Int32CrossDataMap.Builder, com.yorha.proto.StructCommon.Int32CrossDataMapOrBuilder> dataBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       * @return Whether the data field is set.
       */
      public boolean hasData() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       * @return The data.
       */
      public com.yorha.proto.StructCommon.Int32CrossDataMap getData() {
        if (dataBuilder_ == null) {
          return data_ == null ? com.yorha.proto.StructCommon.Int32CrossDataMap.getDefaultInstance() : data_;
        } else {
          return dataBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       */
      public Builder setData(com.yorha.proto.StructCommon.Int32CrossDataMap value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          data_ = value;
          onChanged();
        } else {
          dataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       */
      public Builder setData(
          com.yorha.proto.StructCommon.Int32CrossDataMap.Builder builderForValue) {
        if (dataBuilder_ == null) {
          data_ = builderForValue.build();
          onChanged();
        } else {
          dataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       */
      public Builder mergeData(com.yorha.proto.StructCommon.Int32CrossDataMap value) {
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              data_ != null &&
              data_ != com.yorha.proto.StructCommon.Int32CrossDataMap.getDefaultInstance()) {
            data_ =
              com.yorha.proto.StructCommon.Int32CrossDataMap.newBuilder(data_).mergeFrom(value).buildPartial();
          } else {
            data_ = value;
          }
          onChanged();
        } else {
          dataBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = null;
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       */
      public com.yorha.proto.StructCommon.Int32CrossDataMap.Builder getDataBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDataFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       */
      public com.yorha.proto.StructCommon.Int32CrossDataMapOrBuilder getDataOrBuilder() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilder();
        } else {
          return data_ == null ?
              com.yorha.proto.StructCommon.Int32CrossDataMap.getDefaultInstance() : data_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMap data = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommon.Int32CrossDataMap, com.yorha.proto.StructCommon.Int32CrossDataMap.Builder, com.yorha.proto.StructCommon.Int32CrossDataMapOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructCommon.Int32CrossDataMap, com.yorha.proto.StructCommon.Int32CrossDataMap.Builder, com.yorha.proto.StructCommon.Int32CrossDataMapOrBuilder>(
                  getData(),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CrossDataModel)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CrossDataModel)
    private static final com.yorha.proto.StructCommon.CrossDataModel DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.CrossDataModel();
    }

    public static com.yorha.proto.StructCommon.CrossDataModel getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CrossDataModel>
        PARSER = new com.google.protobuf.AbstractParser<CrossDataModel>() {
      @java.lang.Override
      public CrossDataModel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CrossDataModel(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CrossDataModel> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CrossDataModel> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.CrossDataModel getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CrossDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CrossData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return Whether the ownerClanId field is set.
     */
    boolean hasOwnerClanId();
    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return The ownerClanId.
     */
    long getOwnerClanId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CrossData}
   */
  public static final class CrossData extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CrossData)
      CrossDataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CrossData.newBuilder() to construct.
    private CrossData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CrossData() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CrossData();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CrossData(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              partId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ownerClanId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.CrossData.class, com.yorha.proto.StructCommon.CrossData.Builder.class);
    }

    private int bitField0_;
    public static final int PARTID_FIELD_NUMBER = 1;
    private int partId_;
    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int OWNERCLANID_FIELD_NUMBER = 2;
    private long ownerClanId_;
    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return Whether the ownerClanId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerClanId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return The ownerClanId.
     */
    @java.lang.Override
    public long getOwnerClanId() {
      return ownerClanId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, partId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, ownerClanId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, partId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ownerClanId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.CrossData)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.CrossData other = (com.yorha.proto.StructCommon.CrossData) obj;

      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (hasOwnerClanId() != other.hasOwnerClanId()) return false;
      if (hasOwnerClanId()) {
        if (getOwnerClanId()
            != other.getOwnerClanId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      if (hasOwnerClanId()) {
        hash = (37 * hash) + OWNERCLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerClanId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.CrossData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.CrossData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.CrossData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CrossData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CrossData)
        com.yorha.proto.StructCommon.CrossDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.CrossData.class, com.yorha.proto.StructCommon.CrossData.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.CrossData.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_CrossData_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.CrossData getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.CrossData.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.CrossData build() {
        com.yorha.proto.StructCommon.CrossData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.CrossData buildPartial() {
        com.yorha.proto.StructCommon.CrossData result = new com.yorha.proto.StructCommon.CrossData(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ownerClanId_ = ownerClanId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.CrossData) {
          return mergeFrom((com.yorha.proto.StructCommon.CrossData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.CrossData other) {
        if (other == com.yorha.proto.StructCommon.CrossData.getDefaultInstance()) return this;
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        if (other.hasOwnerClanId()) {
          setOwnerClanId(other.getOwnerClanId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.CrossData parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.CrossData) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000001;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private long ownerClanId_ ;
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @return Whether the ownerClanId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerClanId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @return The ownerClanId.
       */
      @java.lang.Override
      public long getOwnerClanId() {
        return ownerClanId_;
      }
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @param value The ownerClanId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerClanId(long value) {
        bitField0_ |= 0x00000002;
        ownerClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerClanId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ownerClanId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CrossData)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CrossData)
    private static final com.yorha.proto.StructCommon.CrossData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.CrossData();
    }

    public static com.yorha.proto.StructCommon.CrossData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CrossData>
        PARSER = new com.google.protobuf.AbstractParser<CrossData>() {
      @java.lang.Override
      public CrossData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CrossData(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CrossData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CrossData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.CrossData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32DungeonSkillItemMapOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32DungeonSkillItemMap)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */
    boolean containsDatas(
        int key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
    getDatas();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
    getDatasMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.DungeonSkillItem getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommon.DungeonSkillItem defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.DungeonSkillItem getDatasOrThrow(
        int key);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32DungeonSkillItemMap}
   */
  public static final class Int32DungeonSkillItemMap extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32DungeonSkillItemMap)
      Int32DungeonSkillItemMapOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32DungeonSkillItemMap.newBuilder() to construct.
    private Int32DungeonSkillItemMap(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32DungeonSkillItemMap() {
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32DungeonSkillItemMap();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32DungeonSkillItemMap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32DungeonSkillItemMap_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32DungeonSkillItemMap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.class, com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>newDefaultInstance(
                  com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32DungeonSkillItemMap_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommon.DungeonSkillItem.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> datas_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        int key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.DungeonSkillItem getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommon.DungeonSkillItem defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.DungeonSkillItem getDatasOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.Int32DungeonSkillItemMap)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.Int32DungeonSkillItemMap other = (com.yorha.proto.StructCommon.Int32DungeonSkillItemMap) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.Int32DungeonSkillItemMap prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32DungeonSkillItemMap}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32DungeonSkillItemMap)
        com.yorha.proto.StructCommon.Int32DungeonSkillItemMapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32DungeonSkillItemMap_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32DungeonSkillItemMap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.class, com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32DungeonSkillItemMap_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int32DungeonSkillItemMap getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int32DungeonSkillItemMap build() {
        com.yorha.proto.StructCommon.Int32DungeonSkillItemMap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int32DungeonSkillItemMap buildPartial() {
        com.yorha.proto.StructCommon.Int32DungeonSkillItemMap result = new com.yorha.proto.StructCommon.Int32DungeonSkillItemMap(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.Int32DungeonSkillItemMap) {
          return mergeFrom((com.yorha.proto.StructCommon.Int32DungeonSkillItemMap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.Int32DungeonSkillItemMap other) {
        if (other == com.yorha.proto.StructCommon.Int32DungeonSkillItemMap.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.Int32DungeonSkillItemMap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.Int32DungeonSkillItemMap) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> datas_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          int key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.DungeonSkillItem getDatasOrDefault(
          int key,
          com.yorha.proto.StructCommon.DungeonSkillItem defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.DungeonSkillItem getDatasOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          int key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
       */
      public Builder putDatas(
          int key,
          com.yorha.proto.StructCommon.DungeonSkillItem value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItem&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.DungeonSkillItem> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32DungeonSkillItemMap)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32DungeonSkillItemMap)
    private static final com.yorha.proto.StructCommon.Int32DungeonSkillItemMap DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.Int32DungeonSkillItemMap();
    }

    public static com.yorha.proto.StructCommon.Int32DungeonSkillItemMap getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32DungeonSkillItemMap>
        PARSER = new com.google.protobuf.AbstractParser<Int32DungeonSkillItemMap>() {
      @java.lang.Override
      public Int32DungeonSkillItemMap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32DungeonSkillItemMap(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32DungeonSkillItemMap> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32DungeonSkillItemMap> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.Int32DungeonSkillItemMap getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32CrossDataMapOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32CrossDataMap)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */
    boolean containsDatas(
        int key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
    getDatas();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
    getDatasMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.CrossData getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommon.CrossData defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.CrossData getDatasOrThrow(
        int key);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32CrossDataMap}
   */
  public static final class Int32CrossDataMap extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32CrossDataMap)
      Int32CrossDataMapOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32CrossDataMap.newBuilder() to construct.
    private Int32CrossDataMap(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32CrossDataMap() {
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32CrossDataMap();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32CrossDataMap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32CrossDataMap_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32CrossDataMap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.Int32CrossDataMap.class, com.yorha.proto.StructCommon.Int32CrossDataMap.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructCommon.CrossData> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>newDefaultInstance(
                  com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32CrossDataMap_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommon.CrossData.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructCommon.CrossData> datas_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        int key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.CrossData getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommon.CrossData defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.CrossData getDatasOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.Int32CrossDataMap)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.Int32CrossDataMap other = (com.yorha.proto.StructCommon.Int32CrossDataMap) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int32CrossDataMap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.Int32CrossDataMap prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32CrossDataMap}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32CrossDataMap)
        com.yorha.proto.StructCommon.Int32CrossDataMapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32CrossDataMap_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32CrossDataMap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.Int32CrossDataMap.class, com.yorha.proto.StructCommon.Int32CrossDataMap.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.Int32CrossDataMap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int32CrossDataMap_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int32CrossDataMap getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.Int32CrossDataMap.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int32CrossDataMap build() {
        com.yorha.proto.StructCommon.Int32CrossDataMap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int32CrossDataMap buildPartial() {
        com.yorha.proto.StructCommon.Int32CrossDataMap result = new com.yorha.proto.StructCommon.Int32CrossDataMap(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.Int32CrossDataMap) {
          return mergeFrom((com.yorha.proto.StructCommon.Int32CrossDataMap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.Int32CrossDataMap other) {
        if (other == com.yorha.proto.StructCommon.Int32CrossDataMap.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.Int32CrossDataMap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.Int32CrossDataMap) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructCommon.CrossData> datas_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          int key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.CrossData getDatasOrDefault(
          int key,
          com.yorha.proto.StructCommon.CrossData defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.CrossData getDatasOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          int key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
       */
      public Builder putDatas(
          int key,
          com.yorha.proto.StructCommon.CrossData value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossData&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructCommon.CrossData> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32CrossDataMap)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32CrossDataMap)
    private static final com.yorha.proto.StructCommon.Int32CrossDataMap DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.Int32CrossDataMap();
    }

    public static com.yorha.proto.StructCommon.Int32CrossDataMap getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32CrossDataMap>
        PARSER = new com.google.protobuf.AbstractParser<Int32CrossDataMap>() {
      @java.lang.Override
      public Int32CrossDataMap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32CrossDataMap(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32CrossDataMap> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32CrossDataMap> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.Int32CrossDataMap getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int64InvitePlayerRecordMapOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int64InvitePlayerRecordMap)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */
    boolean containsDatas(
        long key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
    getDatas();
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
    getDatasMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.InvitePlayerRecord getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommon.InvitePlayerRecord defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.InvitePlayerRecord getDatasOrThrow(
        long key);

    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Long> getDeleteKeysList();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    long getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int64InvitePlayerRecordMap}
   */
  public static final class Int64InvitePlayerRecordMap extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int64InvitePlayerRecordMap)
      Int64InvitePlayerRecordMapOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int64InvitePlayerRecordMap.newBuilder() to construct.
    private Int64InvitePlayerRecordMap(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int64InvitePlayerRecordMap() {
      deleteKeys_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int64InvitePlayerRecordMap();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int64InvitePlayerRecordMap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap.class, com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>newDefaultInstance(
                  com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommon.InvitePlayerRecord.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> datas_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        long key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.InvitePlayerRecord getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommon.InvitePlayerRecord defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.InvitePlayerRecord getDatasOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList deleteKeys_;
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public long getDeleteKeys(int index) {
      return deleteKeys_.getLong(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt64(2, deleteKeys_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(deleteKeys_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap other = (com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int64InvitePlayerRecordMap}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int64InvitePlayerRecordMap)
        com.yorha.proto.StructCommon.Int64InvitePlayerRecordMapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap.class, com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap build() {
        com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap buildPartial() {
        com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap result = new com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap) {
          return mergeFrom((com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap other) {
        if (other == com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> datas_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          long key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.InvitePlayerRecord getDatasOrDefault(
          long key,
          com.yorha.proto.StructCommon.InvitePlayerRecord defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.InvitePlayerRecord getDatasOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          long key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
       */
      public Builder putDatas(
          long key,
          com.yorha.proto.StructCommon.InvitePlayerRecord value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecord&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.InvitePlayerRecord> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.LongList deleteKeys_ = emptyLongList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Long>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public long getDeleteKeys(int index) {
        return deleteKeys_.getLong(index);
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int64InvitePlayerRecordMap)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int64InvitePlayerRecordMap)
    private static final com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap();
    }

    public static com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int64InvitePlayerRecordMap>
        PARSER = new com.google.protobuf.AbstractParser<Int64InvitePlayerRecordMap>() {
      @java.lang.Override
      public Int64InvitePlayerRecordMap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int64InvitePlayerRecordMap(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int64InvitePlayerRecordMap> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int64InvitePlayerRecordMap> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.Int64InvitePlayerRecordMap getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int64ProgressInfoMapOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int64ProgressInfoMap)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */
    boolean containsDatas(
        long key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
    getDatas();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
    getDatasMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.ProgressInfo getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommon.ProgressInfo defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommon.ProgressInfo getDatasOrThrow(
        long key);

    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Long> getDeleteKeysList();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    long getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int64ProgressInfoMap}
   */
  public static final class Int64ProgressInfoMap extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int64ProgressInfoMap)
      Int64ProgressInfoMapOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int64ProgressInfoMap.newBuilder() to construct.
    private Int64ProgressInfoMap(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int64ProgressInfoMap() {
      deleteKeys_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int64ProgressInfoMap();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int64ProgressInfoMap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64ProgressInfoMap_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64ProgressInfoMap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommon.Int64ProgressInfoMap.class, com.yorha.proto.StructCommon.Int64ProgressInfoMap.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>newDefaultInstance(
                  com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64ProgressInfoMap_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommon.ProgressInfo.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> datas_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        long key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.ProgressInfo getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommon.ProgressInfo defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommon.ProgressInfo getDatasOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList deleteKeys_;
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public long getDeleteKeys(int index) {
      return deleteKeys_.getLong(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt64(2, deleteKeys_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(deleteKeys_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommon.Int64ProgressInfoMap)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommon.Int64ProgressInfoMap other = (com.yorha.proto.StructCommon.Int64ProgressInfoMap) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommon.Int64ProgressInfoMap prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int64ProgressInfoMap}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int64ProgressInfoMap)
        com.yorha.proto.StructCommon.Int64ProgressInfoMapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64ProgressInfoMap_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64ProgressInfoMap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommon.Int64ProgressInfoMap.class, com.yorha.proto.StructCommon.Int64ProgressInfoMap.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommon.Int64ProgressInfoMap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommon.internal_static_com_yorha_proto_Int64ProgressInfoMap_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int64ProgressInfoMap getDefaultInstanceForType() {
        return com.yorha.proto.StructCommon.Int64ProgressInfoMap.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int64ProgressInfoMap build() {
        com.yorha.proto.StructCommon.Int64ProgressInfoMap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommon.Int64ProgressInfoMap buildPartial() {
        com.yorha.proto.StructCommon.Int64ProgressInfoMap result = new com.yorha.proto.StructCommon.Int64ProgressInfoMap(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommon.Int64ProgressInfoMap) {
          return mergeFrom((com.yorha.proto.StructCommon.Int64ProgressInfoMap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommon.Int64ProgressInfoMap other) {
        if (other == com.yorha.proto.StructCommon.Int64ProgressInfoMap.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommon.Int64ProgressInfoMap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommon.Int64ProgressInfoMap) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> datas_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          long key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.ProgressInfo getDatasOrDefault(
          long key,
          com.yorha.proto.StructCommon.ProgressInfo defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommon.ProgressInfo getDatasOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          long key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
       */
      public Builder putDatas(
          long key,
          com.yorha.proto.StructCommon.ProgressInfo value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfo&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Long, com.yorha.proto.StructCommon.ProgressInfo> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.LongList deleteKeys_ = emptyLongList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Long>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public long getDeleteKeys(int index) {
        return deleteKeys_.getLong(index);
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int64ProgressInfoMap)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int64ProgressInfoMap)
    private static final com.yorha.proto.StructCommon.Int64ProgressInfoMap DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommon.Int64ProgressInfoMap();
    }

    public static com.yorha.proto.StructCommon.Int64ProgressInfoMap getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int64ProgressInfoMap>
        PARSER = new com.google.protobuf.AbstractParser<Int64ProgressInfoMap>() {
      @java.lang.Override
      public Int64ProgressInfoMap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int64ProgressInfoMap(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int64ProgressInfoMap> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int64ProgressInfoMap> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommon.Int64ProgressInfoMap getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ProgressInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ProgressInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_InvitePlayerRecord_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_InvitePlayerRecord_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonSkillSys_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonSkillSys_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonSkillItem_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonSkillItem_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CrossDataModel_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CrossDataModel_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CrossData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CrossData_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32DungeonSkillItemMap_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32DungeonSkillItemMap_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32DungeonSkillItemMap_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32DungeonSkillItemMap_DatasEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32CrossDataMap_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32CrossDataMap_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32CrossDataMap_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32CrossDataMap_DatasEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_DatasEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64ProgressInfoMap_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64ProgressInfoMap_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64ProgressInfoMap_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64ProgressInfoMap_DatasEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'ss_proto/gen/common/struct_common.prot" +
      "o\022\017com.yorha.proto\"\255\001\n\014ProgressInfo\022\013\n\003u" +
      "id\030\001 \001(\003\022\022\n\nlastCalNum\030\002 \001(\003\022\016\n\006maxNum\030\003" +
      " \001(\003\022\023\n\013lastCalTsMs\030\004 \001(\003\022\r\n\005speed\030\005 \001(\003" +
      "\022\024\n\014stateEndTsMs\030\006 \001(\003\022\026\n\016stateStartTsMs" +
      "\030\007 \001(\003\022\032\n\022enterAdditionValue\030\010 \001(\003\":\n\022In" +
      "vitePlayerRecord\022\020\n\010playerId\030\001 \001(\003\022\022\n\nin" +
      "viteTsMs\030\002 \001(\003\"K\n\017DungeonSkillSys\0228\n\005ski" +
      "ll\030\001 \001(\0132).com.yorha.proto.Int32DungeonS" +
      "killItemMap\"J\n\020DungeonSkillItem\022\017\n\007skill" +
      "Id\030\001 \001(\005\022\021\n\tcanUseNum\030\002 \001(\005\022\022\n\ncanUseTsM" +
      "s\030\003 \001(\003\"B\n\016CrossDataModel\0220\n\004data\030\001 \001(\0132" +
      "\".com.yorha.proto.Int32CrossDataMap\"0\n\tC" +
      "rossData\022\016\n\006partId\030\001 \001(\005\022\023\n\013ownerClanId\030" +
      "\002 \001(\003\"\327\001\n\030Int32DungeonSkillItemMap\022C\n\005da" +
      "tas\030\001 \003(\01324.com.yorha.proto.Int32Dungeon" +
      "SkillItemMap.DatasEntry\022\022\n\ndeleteKeys\030\002 " +
      "\003(\005\022\021\n\tclearFlag\030\003 \001(\010\032O\n\nDatasEntry\022\013\n\003" +
      "key\030\001 \001(\005\0220\n\005value\030\002 \001(\0132!.com.yorha.pro" +
      "to.DungeonSkillItem:\0028\001\"\302\001\n\021Int32CrossDa" +
      "taMap\022<\n\005datas\030\001 \003(\0132-.com.yorha.proto.I" +
      "nt32CrossDataMap.DatasEntry\022\022\n\ndeleteKey" +
      "s\030\002 \003(\005\022\021\n\tclearFlag\030\003 \001(\010\032H\n\nDatasEntry" +
      "\022\013\n\003key\030\001 \001(\005\022)\n\005value\030\002 \001(\0132\032.com.yorha" +
      ".proto.CrossData:\0028\001\"\335\001\n\032Int64InvitePlay" +
      "erRecordMap\022E\n\005datas\030\001 \003(\01326.com.yorha.p" +
      "roto.Int64InvitePlayerRecordMap.DatasEnt" +
      "ry\022\022\n\ndeleteKeys\030\002 \003(\003\022\021\n\tclearFlag\030\003 \001(" +
      "\010\032Q\n\nDatasEntry\022\013\n\003key\030\001 \001(\003\0222\n\005value\030\002 " +
      "\001(\0132#.com.yorha.proto.InvitePlayerRecord" +
      ":\0028\001\"\313\001\n\024Int64ProgressInfoMap\022?\n\005datas\030\001" +
      " \003(\01320.com.yorha.proto.Int64ProgressInfo" +
      "Map.DatasEntry\022\022\n\ndeleteKeys\030\002 \003(\003\022\021\n\tcl" +
      "earFlag\030\003 \001(\010\032K\n\nDatasEntry\022\013\n\003key\030\001 \001(\003" +
      "\022,\n\005value\030\002 \001(\0132\035.com.yorha.proto.Progre" +
      "ssInfo:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_ProgressInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ProgressInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ProgressInfo_descriptor,
        new java.lang.String[] { "Uid", "LastCalNum", "MaxNum", "LastCalTsMs", "Speed", "StateEndTsMs", "StateStartTsMs", "EnterAdditionValue", });
    internal_static_com_yorha_proto_InvitePlayerRecord_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_InvitePlayerRecord_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_InvitePlayerRecord_descriptor,
        new java.lang.String[] { "PlayerId", "InviteTsMs", });
    internal_static_com_yorha_proto_DungeonSkillSys_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_DungeonSkillSys_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonSkillSys_descriptor,
        new java.lang.String[] { "Skill", });
    internal_static_com_yorha_proto_DungeonSkillItem_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_DungeonSkillItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonSkillItem_descriptor,
        new java.lang.String[] { "SkillId", "CanUseNum", "CanUseTsMs", });
    internal_static_com_yorha_proto_CrossDataModel_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_CrossDataModel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CrossDataModel_descriptor,
        new java.lang.String[] { "Data", });
    internal_static_com_yorha_proto_CrossData_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_CrossData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CrossData_descriptor,
        new java.lang.String[] { "PartId", "OwnerClanId", });
    internal_static_com_yorha_proto_Int32DungeonSkillItemMap_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Int32DungeonSkillItemMap_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32DungeonSkillItemMap_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int32DungeonSkillItemMap_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int32DungeonSkillItemMap_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int32DungeonSkillItemMap_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32DungeonSkillItemMap_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Int32CrossDataMap_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Int32CrossDataMap_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32CrossDataMap_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int32CrossDataMap_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int32CrossDataMap_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int32CrossDataMap_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32CrossDataMap_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64InvitePlayerRecordMap_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Int64ProgressInfoMap_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Int64ProgressInfoMap_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64ProgressInfoMap_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int64ProgressInfoMap_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int64ProgressInfoMap_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int64ProgressInfoMap_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64ProgressInfoMap_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

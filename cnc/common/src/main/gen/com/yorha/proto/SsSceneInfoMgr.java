// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_info_mgr.proto

package com.yorha.proto;

public final class SsSceneInfoMgr {
  private SsSceneInfoMgr() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GetOnlinePlayerIdAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetOnlinePlayerIdAsk)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdAsk}
   */
  public static final class GetOnlinePlayerIdAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetOnlinePlayerIdAsk)
      GetOnlinePlayerIdAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetOnlinePlayerIdAsk.newBuilder() to construct.
    private GetOnlinePlayerIdAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetOnlinePlayerIdAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetOnlinePlayerIdAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetOnlinePlayerIdAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk other = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetOnlinePlayerIdAsk)
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk build() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk result = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetOnlinePlayerIdAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetOnlinePlayerIdAsk)
    private static final com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk();
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetOnlinePlayerIdAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetOnlinePlayerIdAsk>() {
      @java.lang.Override
      public GetOnlinePlayerIdAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetOnlinePlayerIdAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetOnlinePlayerIdAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetOnlinePlayerIdAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetOnlinePlayerIdAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetOnlinePlayerIdAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return Whether the player field is set.
     */
    boolean hasPlayer();
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return The player.
     */
    com.yorha.proto.Basic.Int64List getPlayer();
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     */
    com.yorha.proto.Basic.Int64ListOrBuilder getPlayerOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdAns}
   */
  public static final class GetOnlinePlayerIdAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetOnlinePlayerIdAns)
      GetOnlinePlayerIdAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetOnlinePlayerIdAns.newBuilder() to construct.
    private GetOnlinePlayerIdAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetOnlinePlayerIdAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetOnlinePlayerIdAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetOnlinePlayerIdAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Basic.Int64List.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = player_.toBuilder();
              }
              player_ = input.readMessage(com.yorha.proto.Basic.Int64List.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(player_);
                player_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYER_FIELD_NUMBER = 1;
    private com.yorha.proto.Basic.Int64List player_;
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return Whether the player field is set.
     */
    @java.lang.Override
    public boolean hasPlayer() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return The player.
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64List getPlayer() {
      return player_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
    }
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64ListOrBuilder getPlayerOrBuilder() {
      return player_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPlayer());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPlayer());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns other = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns) obj;

      if (hasPlayer() != other.hasPlayer()) return false;
      if (hasPlayer()) {
        if (!getPlayer()
            .equals(other.getPlayer())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayer()) {
        hash = (37 * hash) + PLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getPlayer().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetOnlinePlayerIdAns)
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPlayerFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (playerBuilder_ == null) {
          player_ = null;
        } else {
          playerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns build() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns result = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (playerBuilder_ == null) {
            result.player_ = player_;
          } else {
            result.player_ = playerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns.getDefaultInstance()) return this;
        if (other.hasPlayer()) {
          mergePlayer(other.getPlayer());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Basic.Int64List player_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> playerBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       * @return Whether the player field is set.
       */
      public boolean hasPlayer() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       * @return The player.
       */
      public com.yorha.proto.Basic.Int64List getPlayer() {
        if (playerBuilder_ == null) {
          return player_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
        } else {
          return playerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder setPlayer(com.yorha.proto.Basic.Int64List value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          player_ = value;
          onChanged();
        } else {
          playerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder setPlayer(
          com.yorha.proto.Basic.Int64List.Builder builderForValue) {
        if (playerBuilder_ == null) {
          player_ = builderForValue.build();
          onChanged();
        } else {
          playerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder mergePlayer(com.yorha.proto.Basic.Int64List value) {
        if (playerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              player_ != null &&
              player_ != com.yorha.proto.Basic.Int64List.getDefaultInstance()) {
            player_ =
              com.yorha.proto.Basic.Int64List.newBuilder(player_).mergeFrom(value).buildPartial();
          } else {
            player_ = value;
          }
          onChanged();
        } else {
          playerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = null;
          onChanged();
        } else {
          playerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public com.yorha.proto.Basic.Int64List.Builder getPlayerBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPlayerFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public com.yorha.proto.Basic.Int64ListOrBuilder getPlayerOrBuilder() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilder();
        } else {
          return player_ == null ?
              com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder>(
                  getPlayer(),
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetOnlinePlayerIdAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetOnlinePlayerIdAns)
    private static final com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns();
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetOnlinePlayerIdAns>
        PARSER = new com.google.protobuf.AbstractParser<GetOnlinePlayerIdAns>() {
      @java.lang.Override
      public GetOnlinePlayerIdAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetOnlinePlayerIdAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetOnlinePlayerIdAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetOnlinePlayerIdAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetOnlinePlayerIdFilterByCreateTimeAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 startTime = 1;</code>
     * @return Whether the startTime field is set.
     */
    boolean hasStartTime();
    /**
     * <code>optional int64 startTime = 1;</code>
     * @return The startTime.
     */
    long getStartTime();

    /**
     * <code>optional int64 endTime = 2;</code>
     * @return Whether the endTime field is set.
     */
    boolean hasEndTime();
    /**
     * <code>optional int64 endTime = 2;</code>
     * @return The endTime.
     */
    long getEndTime();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAsk}
   */
  public static final class GetOnlinePlayerIdFilterByCreateTimeAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAsk)
      GetOnlinePlayerIdFilterByCreateTimeAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetOnlinePlayerIdFilterByCreateTimeAsk.newBuilder() to construct.
    private GetOnlinePlayerIdFilterByCreateTimeAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetOnlinePlayerIdFilterByCreateTimeAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetOnlinePlayerIdFilterByCreateTimeAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetOnlinePlayerIdFilterByCreateTimeAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              startTime_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              endTime_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.Builder.class);
    }

    private int bitField0_;
    public static final int STARTTIME_FIELD_NUMBER = 1;
    private long startTime_;
    /**
     * <code>optional int64 startTime = 1;</code>
     * @return Whether the startTime field is set.
     */
    @java.lang.Override
    public boolean hasStartTime() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 startTime = 1;</code>
     * @return The startTime.
     */
    @java.lang.Override
    public long getStartTime() {
      return startTime_;
    }

    public static final int ENDTIME_FIELD_NUMBER = 2;
    private long endTime_;
    /**
     * <code>optional int64 endTime = 2;</code>
     * @return Whether the endTime field is set.
     */
    @java.lang.Override
    public boolean hasEndTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 endTime = 2;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public long getEndTime() {
      return endTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, startTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, endTime_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, startTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, endTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk other = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk) obj;

      if (hasStartTime() != other.hasStartTime()) return false;
      if (hasStartTime()) {
        if (getStartTime()
            != other.getStartTime()) return false;
      }
      if (hasEndTime() != other.hasEndTime()) return false;
      if (hasEndTime()) {
        if (getEndTime()
            != other.getEndTime()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasStartTime()) {
        hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStartTime());
      }
      if (hasEndTime()) {
        hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEndTime());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAsk)
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        startTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        endTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk build() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk result = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.startTime_ = startTime_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.endTime_ = endTime_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.getDefaultInstance()) return this;
        if (other.hasStartTime()) {
          setStartTime(other.getStartTime());
        }
        if (other.hasEndTime()) {
          setEndTime(other.getEndTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long startTime_ ;
      /**
       * <code>optional int64 startTime = 1;</code>
       * @return Whether the startTime field is set.
       */
      @java.lang.Override
      public boolean hasStartTime() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 startTime = 1;</code>
       * @return The startTime.
       */
      @java.lang.Override
      public long getStartTime() {
        return startTime_;
      }
      /**
       * <code>optional int64 startTime = 1;</code>
       * @param value The startTime to set.
       * @return This builder for chaining.
       */
      public Builder setStartTime(long value) {
        bitField0_ |= 0x00000001;
        startTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 startTime = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTime() {
        bitField0_ = (bitField0_ & ~0x00000001);
        startTime_ = 0L;
        onChanged();
        return this;
      }

      private long endTime_ ;
      /**
       * <code>optional int64 endTime = 2;</code>
       * @return Whether the endTime field is set.
       */
      @java.lang.Override
      public boolean hasEndTime() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 endTime = 2;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public long getEndTime() {
        return endTime_;
      }
      /**
       * <code>optional int64 endTime = 2;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(long value) {
        bitField0_ |= 0x00000002;
        endTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 endTime = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        endTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAsk)
    private static final com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk();
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetOnlinePlayerIdFilterByCreateTimeAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetOnlinePlayerIdFilterByCreateTimeAsk>() {
      @java.lang.Override
      public GetOnlinePlayerIdFilterByCreateTimeAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetOnlinePlayerIdFilterByCreateTimeAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetOnlinePlayerIdFilterByCreateTimeAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetOnlinePlayerIdFilterByCreateTimeAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetOnlinePlayerIdFilterByCreateTimeAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return Whether the player field is set.
     */
    boolean hasPlayer();
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return The player.
     */
    com.yorha.proto.Basic.Int64List getPlayer();
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     */
    com.yorha.proto.Basic.Int64ListOrBuilder getPlayerOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAns}
   */
  public static final class GetOnlinePlayerIdFilterByCreateTimeAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAns)
      GetOnlinePlayerIdFilterByCreateTimeAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetOnlinePlayerIdFilterByCreateTimeAns.newBuilder() to construct.
    private GetOnlinePlayerIdFilterByCreateTimeAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetOnlinePlayerIdFilterByCreateTimeAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetOnlinePlayerIdFilterByCreateTimeAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetOnlinePlayerIdFilterByCreateTimeAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Basic.Int64List.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = player_.toBuilder();
              }
              player_ = input.readMessage(com.yorha.proto.Basic.Int64List.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(player_);
                player_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYER_FIELD_NUMBER = 1;
    private com.yorha.proto.Basic.Int64List player_;
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return Whether the player field is set.
     */
    @java.lang.Override
    public boolean hasPlayer() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     * @return The player.
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64List getPlayer() {
      return player_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
    }
    /**
     * <code>optional .com.yorha.proto.Int64List player = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int64ListOrBuilder getPlayerOrBuilder() {
      return player_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPlayer());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPlayer());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns other = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns) obj;

      if (hasPlayer() != other.hasPlayer()) return false;
      if (hasPlayer()) {
        if (!getPlayer()
            .equals(other.getPlayer())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayer()) {
        hash = (37 * hash) + PLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getPlayer().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAns)
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.class, com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPlayerFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (playerBuilder_ == null) {
          player_ = null;
        } else {
          playerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns build() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns result = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (playerBuilder_ == null) {
            result.player_ = player_;
          } else {
            result.player_ = playerBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.getDefaultInstance()) return this;
        if (other.hasPlayer()) {
          mergePlayer(other.getPlayer());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Basic.Int64List player_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> playerBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       * @return Whether the player field is set.
       */
      public boolean hasPlayer() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       * @return The player.
       */
      public com.yorha.proto.Basic.Int64List getPlayer() {
        if (playerBuilder_ == null) {
          return player_ == null ? com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
        } else {
          return playerBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder setPlayer(com.yorha.proto.Basic.Int64List value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          player_ = value;
          onChanged();
        } else {
          playerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder setPlayer(
          com.yorha.proto.Basic.Int64List.Builder builderForValue) {
        if (playerBuilder_ == null) {
          player_ = builderForValue.build();
          onChanged();
        } else {
          playerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder mergePlayer(com.yorha.proto.Basic.Int64List value) {
        if (playerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              player_ != null &&
              player_ != com.yorha.proto.Basic.Int64List.getDefaultInstance()) {
            player_ =
              com.yorha.proto.Basic.Int64List.newBuilder(player_).mergeFrom(value).buildPartial();
          } else {
            player_ = value;
          }
          onChanged();
        } else {
          playerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = null;
          onChanged();
        } else {
          playerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public com.yorha.proto.Basic.Int64List.Builder getPlayerBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPlayerFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      public com.yorha.proto.Basic.Int64ListOrBuilder getPlayerOrBuilder() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilder();
        } else {
          return player_ == null ?
              com.yorha.proto.Basic.Int64List.getDefaultInstance() : player_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int64List player = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Basic.Int64List, com.yorha.proto.Basic.Int64List.Builder, com.yorha.proto.Basic.Int64ListOrBuilder>(
                  getPlayer(),
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetOnlinePlayerIdFilterByCreateTimeAns)
    private static final com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns();
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetOnlinePlayerIdFilterByCreateTimeAns>
        PARSER = new com.google.protobuf.AbstractParser<GetOnlinePlayerIdFilterByCreateTimeAns>() {
      @java.lang.Override
      public GetOnlinePlayerIdFilterByCreateTimeAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetOnlinePlayerIdFilterByCreateTimeAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetOnlinePlayerIdFilterByCreateTimeAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetOnlinePlayerIdFilterByCreateTimeAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckPlayerOnlineAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckPlayerOnlineAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 player = 1;</code>
     * @return A list containing the player.
     */
    java.util.List<java.lang.Long> getPlayerList();
    /**
     * <code>repeated int64 player = 1;</code>
     * @return The count of player.
     */
    int getPlayerCount();
    /**
     * <code>repeated int64 player = 1;</code>
     * @param index The index of the element to return.
     * @return The player at the given index.
     */
    long getPlayer(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckPlayerOnlineAsk}
   */
  public static final class CheckPlayerOnlineAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckPlayerOnlineAsk)
      CheckPlayerOnlineAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckPlayerOnlineAsk.newBuilder() to construct.
    private CheckPlayerOnlineAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckPlayerOnlineAsk() {
      player_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckPlayerOnlineAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckPlayerOnlineAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                player_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              player_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                player_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                player_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          player_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk.class, com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk.Builder.class);
    }

    public static final int PLAYER_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList player_;
    /**
     * <code>repeated int64 player = 1;</code>
     * @return A list containing the player.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getPlayerList() {
      return player_;
    }
    /**
     * <code>repeated int64 player = 1;</code>
     * @return The count of player.
     */
    public int getPlayerCount() {
      return player_.size();
    }
    /**
     * <code>repeated int64 player = 1;</code>
     * @param index The index of the element to return.
     * @return The player at the given index.
     */
    public long getPlayer(int index) {
      return player_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < player_.size(); i++) {
        output.writeInt64(1, player_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < player_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(player_.getLong(i));
        }
        size += dataSize;
        size += 1 * getPlayerList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk other = (com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk) obj;

      if (!getPlayerList()
          .equals(other.getPlayerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPlayerCount() > 0) {
        hash = (37 * hash) + PLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckPlayerOnlineAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckPlayerOnlineAsk)
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk.class, com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        player_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk build() {
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk result = new com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          player_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.player_ = player_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk.getDefaultInstance()) return this;
        if (!other.player_.isEmpty()) {
          if (player_.isEmpty()) {
            player_ = other.player_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePlayerIsMutable();
            player_.addAll(other.player_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList player_ = emptyLongList();
      private void ensurePlayerIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          player_ = mutableCopy(player_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 player = 1;</code>
       * @return A list containing the player.
       */
      public java.util.List<java.lang.Long>
          getPlayerList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(player_) : player_;
      }
      /**
       * <code>repeated int64 player = 1;</code>
       * @return The count of player.
       */
      public int getPlayerCount() {
        return player_.size();
      }
      /**
       * <code>repeated int64 player = 1;</code>
       * @param index The index of the element to return.
       * @return The player at the given index.
       */
      public long getPlayer(int index) {
        return player_.getLong(index);
      }
      /**
       * <code>repeated int64 player = 1;</code>
       * @param index The index to set the value at.
       * @param value The player to set.
       * @return This builder for chaining.
       */
      public Builder setPlayer(
          int index, long value) {
        ensurePlayerIsMutable();
        player_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 player = 1;</code>
       * @param value The player to add.
       * @return This builder for chaining.
       */
      public Builder addPlayer(long value) {
        ensurePlayerIsMutable();
        player_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 player = 1;</code>
       * @param values The player to add.
       * @return This builder for chaining.
       */
      public Builder addAllPlayer(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensurePlayerIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, player_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 player = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayer() {
        player_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckPlayerOnlineAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckPlayerOnlineAsk)
    private static final com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk();
    }

    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckPlayerOnlineAsk>
        PARSER = new com.google.protobuf.AbstractParser<CheckPlayerOnlineAsk>() {
      @java.lang.Override
      public CheckPlayerOnlineAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckPlayerOnlineAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckPlayerOnlineAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckPlayerOnlineAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckPlayerOnlineAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckPlayerOnlineAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 onlinePlayer = 1;</code>
     * @return A list containing the onlinePlayer.
     */
    java.util.List<java.lang.Long> getOnlinePlayerList();
    /**
     * <code>repeated int64 onlinePlayer = 1;</code>
     * @return The count of onlinePlayer.
     */
    int getOnlinePlayerCount();
    /**
     * <code>repeated int64 onlinePlayer = 1;</code>
     * @param index The index of the element to return.
     * @return The onlinePlayer at the given index.
     */
    long getOnlinePlayer(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckPlayerOnlineAns}
   */
  public static final class CheckPlayerOnlineAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckPlayerOnlineAns)
      CheckPlayerOnlineAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckPlayerOnlineAns.newBuilder() to construct.
    private CheckPlayerOnlineAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckPlayerOnlineAns() {
      onlinePlayer_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckPlayerOnlineAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckPlayerOnlineAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                onlinePlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              onlinePlayer_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                onlinePlayer_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                onlinePlayer_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          onlinePlayer_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns.class, com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns.Builder.class);
    }

    public static final int ONLINEPLAYER_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList onlinePlayer_;
    /**
     * <code>repeated int64 onlinePlayer = 1;</code>
     * @return A list containing the onlinePlayer.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getOnlinePlayerList() {
      return onlinePlayer_;
    }
    /**
     * <code>repeated int64 onlinePlayer = 1;</code>
     * @return The count of onlinePlayer.
     */
    public int getOnlinePlayerCount() {
      return onlinePlayer_.size();
    }
    /**
     * <code>repeated int64 onlinePlayer = 1;</code>
     * @param index The index of the element to return.
     * @return The onlinePlayer at the given index.
     */
    public long getOnlinePlayer(int index) {
      return onlinePlayer_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < onlinePlayer_.size(); i++) {
        output.writeInt64(1, onlinePlayer_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < onlinePlayer_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(onlinePlayer_.getLong(i));
        }
        size += dataSize;
        size += 1 * getOnlinePlayerList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns other = (com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns) obj;

      if (!getOnlinePlayerList()
          .equals(other.getOnlinePlayerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getOnlinePlayerCount() > 0) {
        hash = (37 * hash) + ONLINEPLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getOnlinePlayerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckPlayerOnlineAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckPlayerOnlineAns)
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns.class, com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        onlinePlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_CheckPlayerOnlineAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns build() {
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns result = new com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          onlinePlayer_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.onlinePlayer_ = onlinePlayer_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns.getDefaultInstance()) return this;
        if (!other.onlinePlayer_.isEmpty()) {
          if (onlinePlayer_.isEmpty()) {
            onlinePlayer_ = other.onlinePlayer_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureOnlinePlayerIsMutable();
            onlinePlayer_.addAll(other.onlinePlayer_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList onlinePlayer_ = emptyLongList();
      private void ensureOnlinePlayerIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          onlinePlayer_ = mutableCopy(onlinePlayer_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 onlinePlayer = 1;</code>
       * @return A list containing the onlinePlayer.
       */
      public java.util.List<java.lang.Long>
          getOnlinePlayerList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(onlinePlayer_) : onlinePlayer_;
      }
      /**
       * <code>repeated int64 onlinePlayer = 1;</code>
       * @return The count of onlinePlayer.
       */
      public int getOnlinePlayerCount() {
        return onlinePlayer_.size();
      }
      /**
       * <code>repeated int64 onlinePlayer = 1;</code>
       * @param index The index of the element to return.
       * @return The onlinePlayer at the given index.
       */
      public long getOnlinePlayer(int index) {
        return onlinePlayer_.getLong(index);
      }
      /**
       * <code>repeated int64 onlinePlayer = 1;</code>
       * @param index The index to set the value at.
       * @param value The onlinePlayer to set.
       * @return This builder for chaining.
       */
      public Builder setOnlinePlayer(
          int index, long value) {
        ensureOnlinePlayerIsMutable();
        onlinePlayer_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 onlinePlayer = 1;</code>
       * @param value The onlinePlayer to add.
       * @return This builder for chaining.
       */
      public Builder addOnlinePlayer(long value) {
        ensureOnlinePlayerIsMutable();
        onlinePlayer_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 onlinePlayer = 1;</code>
       * @param values The onlinePlayer to add.
       * @return This builder for chaining.
       */
      public Builder addAllOnlinePlayer(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureOnlinePlayerIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, onlinePlayer_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 onlinePlayer = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOnlinePlayer() {
        onlinePlayer_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckPlayerOnlineAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckPlayerOnlineAns)
    private static final com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns();
    }

    public static com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckPlayerOnlineAns>
        PARSER = new com.google.protobuf.AbstractParser<CheckPlayerOnlineAns>() {
      @java.lang.Override
      public CheckPlayerOnlineAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckPlayerOnlineAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckPlayerOnlineAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckPlayerOnlineAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.CheckPlayerOnlineAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetZoneIpPortAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetZoneIpPortAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标加速通道
     * </pre>
     *
     * <code>optional string channelName = 1;</code>
     * @return Whether the channelName field is set.
     */
    boolean hasChannelName();
    /**
     * <pre>
     * 目标加速通道
     * </pre>
     *
     * <code>optional string channelName = 1;</code>
     * @return The channelName.
     */
    java.lang.String getChannelName();
    /**
     * <pre>
     * 目标加速通道
     * </pre>
     *
     * <code>optional string channelName = 1;</code>
     * @return The bytes for channelName.
     */
    com.google.protobuf.ByteString
        getChannelNameBytes();

    /**
     * <pre>
     * 是否是超级白名单
     * </pre>
     *
     * <code>optional bool isSuperWhite = 2;</code>
     * @return Whether the isSuperWhite field is set.
     */
    boolean hasIsSuperWhite();
    /**
     * <pre>
     * 是否是超级白名单
     * </pre>
     *
     * <code>optional bool isSuperWhite = 2;</code>
     * @return The isSuperWhite.
     */
    boolean getIsSuperWhite();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetZoneIpPortAsk}
   */
  public static final class GetZoneIpPortAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetZoneIpPortAsk)
      GetZoneIpPortAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetZoneIpPortAsk.newBuilder() to construct.
    private GetZoneIpPortAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetZoneIpPortAsk() {
      channelName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetZoneIpPortAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetZoneIpPortAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              channelName_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              isSuperWhite_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk.class, com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNELNAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object channelName_;
    /**
     * <pre>
     * 目标加速通道
     * </pre>
     *
     * <code>optional string channelName = 1;</code>
     * @return Whether the channelName field is set.
     */
    @java.lang.Override
    public boolean hasChannelName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 目标加速通道
     * </pre>
     *
     * <code>optional string channelName = 1;</code>
     * @return The channelName.
     */
    @java.lang.Override
    public java.lang.String getChannelName() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 目标加速通道
     * </pre>
     *
     * <code>optional string channelName = 1;</code>
     * @return The bytes for channelName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelNameBytes() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ISSUPERWHITE_FIELD_NUMBER = 2;
    private boolean isSuperWhite_;
    /**
     * <pre>
     * 是否是超级白名单
     * </pre>
     *
     * <code>optional bool isSuperWhite = 2;</code>
     * @return Whether the isSuperWhite field is set.
     */
    @java.lang.Override
    public boolean hasIsSuperWhite() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否是超级白名单
     * </pre>
     *
     * <code>optional bool isSuperWhite = 2;</code>
     * @return The isSuperWhite.
     */
    @java.lang.Override
    public boolean getIsSuperWhite() {
      return isSuperWhite_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, isSuperWhite_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isSuperWhite_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk other = (com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk) obj;

      if (hasChannelName() != other.hasChannelName()) return false;
      if (hasChannelName()) {
        if (!getChannelName()
            .equals(other.getChannelName())) return false;
      }
      if (hasIsSuperWhite() != other.hasIsSuperWhite()) return false;
      if (hasIsSuperWhite()) {
        if (getIsSuperWhite()
            != other.getIsSuperWhite()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChannelName()) {
        hash = (37 * hash) + CHANNELNAME_FIELD_NUMBER;
        hash = (53 * hash) + getChannelName().hashCode();
      }
      if (hasIsSuperWhite()) {
        hash = (37 * hash) + ISSUPERWHITE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsSuperWhite());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetZoneIpPortAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetZoneIpPortAsk)
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk.class, com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelName_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        isSuperWhite_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk build() {
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk result = new com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.channelName_ = channelName_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isSuperWhite_ = isSuperWhite_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk.getDefaultInstance()) return this;
        if (other.hasChannelName()) {
          bitField0_ |= 0x00000001;
          channelName_ = other.channelName_;
          onChanged();
        }
        if (other.hasIsSuperWhite()) {
          setIsSuperWhite(other.getIsSuperWhite());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object channelName_ = "";
      /**
       * <pre>
       * 目标加速通道
       * </pre>
       *
       * <code>optional string channelName = 1;</code>
       * @return Whether the channelName field is set.
       */
      public boolean hasChannelName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 目标加速通道
       * </pre>
       *
       * <code>optional string channelName = 1;</code>
       * @return The channelName.
       */
      public java.lang.String getChannelName() {
        java.lang.Object ref = channelName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 目标加速通道
       * </pre>
       *
       * <code>optional string channelName = 1;</code>
       * @return The bytes for channelName.
       */
      public com.google.protobuf.ByteString
          getChannelNameBytes() {
        java.lang.Object ref = channelName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 目标加速通道
       * </pre>
       *
       * <code>optional string channelName = 1;</code>
       * @param value The channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标加速通道
       * </pre>
       *
       * <code>optional string channelName = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channelName_ = getDefaultInstance().getChannelName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标加速通道
       * </pre>
       *
       * <code>optional string channelName = 1;</code>
       * @param value The bytes for channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        channelName_ = value;
        onChanged();
        return this;
      }

      private boolean isSuperWhite_ ;
      /**
       * <pre>
       * 是否是超级白名单
       * </pre>
       *
       * <code>optional bool isSuperWhite = 2;</code>
       * @return Whether the isSuperWhite field is set.
       */
      @java.lang.Override
      public boolean hasIsSuperWhite() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 是否是超级白名单
       * </pre>
       *
       * <code>optional bool isSuperWhite = 2;</code>
       * @return The isSuperWhite.
       */
      @java.lang.Override
      public boolean getIsSuperWhite() {
        return isSuperWhite_;
      }
      /**
       * <pre>
       * 是否是超级白名单
       * </pre>
       *
       * <code>optional bool isSuperWhite = 2;</code>
       * @param value The isSuperWhite to set.
       * @return This builder for chaining.
       */
      public Builder setIsSuperWhite(boolean value) {
        bitField0_ |= 0x00000002;
        isSuperWhite_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否是超级白名单
       * </pre>
       *
       * <code>optional bool isSuperWhite = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSuperWhite() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isSuperWhite_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetZoneIpPortAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetZoneIpPortAsk)
    private static final com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk();
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetZoneIpPortAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetZoneIpPortAsk>() {
      @java.lang.Override
      public GetZoneIpPortAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetZoneIpPortAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetZoneIpPortAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetZoneIpPortAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetZoneIpPortAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetZoneIpPortAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    boolean hasIp();
    /**
     * <code>optional string ip = 1;</code>
     * @return The ip.
     */
    java.lang.String getIp();
    /**
     * <code>optional string ip = 1;</code>
     * @return The bytes for ip.
     */
    com.google.protobuf.ByteString
        getIpBytes();

    /**
     * <code>optional int32 port = 2;</code>
     * @return Whether the port field is set.
     */
    boolean hasPort();
    /**
     * <code>optional int32 port = 2;</code>
     * @return The port.
     */
    int getPort();

    /**
     * <pre>
     * 实际加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return Whether the channelName field is set.
     */
    boolean hasChannelName();
    /**
     * <pre>
     * 实际加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The channelName.
     */
    java.lang.String getChannelName();
    /**
     * <pre>
     * 实际加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The bytes for channelName.
     */
    com.google.protobuf.ByteString
        getChannelNameBytes();

    /**
     * <pre>
     * 是否可注册(对于超级白名单仅)
     * </pre>
     *
     * <code>optional bool canRegister = 4;</code>
     * @return Whether the canRegister field is set.
     */
    boolean hasCanRegister();
    /**
     * <pre>
     * 是否可注册(对于超级白名单仅)
     * </pre>
     *
     * <code>optional bool canRegister = 4;</code>
     * @return The canRegister.
     */
    boolean getCanRegister();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetZoneIpPortAns}
   */
  public static final class GetZoneIpPortAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetZoneIpPortAns)
      GetZoneIpPortAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetZoneIpPortAns.newBuilder() to construct.
    private GetZoneIpPortAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetZoneIpPortAns() {
      ip_ = "";
      channelName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetZoneIpPortAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetZoneIpPortAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              ip_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              port_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              channelName_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              canRegister_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns.class, com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns.Builder.class);
    }

    private int bitField0_;
    public static final int IP_FIELD_NUMBER = 1;
    private volatile java.lang.Object ip_;
    /**
     * <code>optional string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    @java.lang.Override
    public boolean hasIp() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string ip = 1;</code>
     * @return The ip.
     */
    @java.lang.Override
    public java.lang.String getIp() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ip_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string ip = 1;</code>
     * @return The bytes for ip.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIpBytes() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PORT_FIELD_NUMBER = 2;
    private int port_;
    /**
     * <code>optional int32 port = 2;</code>
     * @return Whether the port field is set.
     */
    @java.lang.Override
    public boolean hasPort() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 port = 2;</code>
     * @return The port.
     */
    @java.lang.Override
    public int getPort() {
      return port_;
    }

    public static final int CHANNELNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object channelName_;
    /**
     * <pre>
     * 实际加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return Whether the channelName field is set.
     */
    @java.lang.Override
    public boolean hasChannelName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 实际加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The channelName.
     */
    @java.lang.Override
    public java.lang.String getChannelName() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 实际加速通道
     * </pre>
     *
     * <code>optional string channelName = 3;</code>
     * @return The bytes for channelName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelNameBytes() {
      java.lang.Object ref = channelName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CANREGISTER_FIELD_NUMBER = 4;
    private boolean canRegister_;
    /**
     * <pre>
     * 是否可注册(对于超级白名单仅)
     * </pre>
     *
     * <code>optional bool canRegister = 4;</code>
     * @return Whether the canRegister field is set.
     */
    @java.lang.Override
    public boolean hasCanRegister() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 是否可注册(对于超级白名单仅)
     * </pre>
     *
     * <code>optional bool canRegister = 4;</code>
     * @return The canRegister.
     */
    @java.lang.Override
    public boolean getCanRegister() {
      return canRegister_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, channelName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, canRegister_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, channelName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, canRegister_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns other = (com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns) obj;

      if (hasIp() != other.hasIp()) return false;
      if (hasIp()) {
        if (!getIp()
            .equals(other.getIp())) return false;
      }
      if (hasPort() != other.hasPort()) return false;
      if (hasPort()) {
        if (getPort()
            != other.getPort()) return false;
      }
      if (hasChannelName() != other.hasChannelName()) return false;
      if (hasChannelName()) {
        if (!getChannelName()
            .equals(other.getChannelName())) return false;
      }
      if (hasCanRegister() != other.hasCanRegister()) return false;
      if (hasCanRegister()) {
        if (getCanRegister()
            != other.getCanRegister()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIp()) {
        hash = (37 * hash) + IP_FIELD_NUMBER;
        hash = (53 * hash) + getIp().hashCode();
      }
      if (hasPort()) {
        hash = (37 * hash) + PORT_FIELD_NUMBER;
        hash = (53 * hash) + getPort();
      }
      if (hasChannelName()) {
        hash = (37 * hash) + CHANNELNAME_FIELD_NUMBER;
        hash = (53 * hash) + getChannelName().hashCode();
      }
      if (hasCanRegister()) {
        hash = (37 * hash) + CANREGISTER_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getCanRegister());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetZoneIpPortAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetZoneIpPortAns)
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns.class, com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ip_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        port_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        channelName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        canRegister_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneInfoMgr.internal_static_com_yorha_proto_GetZoneIpPortAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns build() {
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns buildPartial() {
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns result = new com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.ip_ = ip_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.port_ = port_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.channelName_ = channelName_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.canRegister_ = canRegister_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns) {
          return mergeFrom((com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns other) {
        if (other == com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns.getDefaultInstance()) return this;
        if (other.hasIp()) {
          bitField0_ |= 0x00000001;
          ip_ = other.ip_;
          onChanged();
        }
        if (other.hasPort()) {
          setPort(other.getPort());
        }
        if (other.hasChannelName()) {
          bitField0_ |= 0x00000004;
          channelName_ = other.channelName_;
          onChanged();
        }
        if (other.hasCanRegister()) {
          setCanRegister(other.getCanRegister());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object ip_ = "";
      /**
       * <code>optional string ip = 1;</code>
       * @return Whether the ip field is set.
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string ip = 1;</code>
       * @return The ip.
       */
      public java.lang.String getIp() {
        java.lang.Object ref = ip_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ip_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string ip = 1;</code>
       * @return The bytes for ip.
       */
      public com.google.protobuf.ByteString
          getIpBytes() {
        java.lang.Object ref = ip_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ip_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string ip = 1;</code>
       * @param value The ip to set.
       * @return This builder for chaining.
       */
      public Builder setIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string ip = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ip_ = getDefaultInstance().getIp();
        onChanged();
        return this;
      }
      /**
       * <code>optional string ip = 1;</code>
       * @param value The bytes for ip to set.
       * @return This builder for chaining.
       */
      public Builder setIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }

      private int port_ ;
      /**
       * <code>optional int32 port = 2;</code>
       * @return Whether the port field is set.
       */
      @java.lang.Override
      public boolean hasPort() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 port = 2;</code>
       * @return The port.
       */
      @java.lang.Override
      public int getPort() {
        return port_;
      }
      /**
       * <code>optional int32 port = 2;</code>
       * @param value The port to set.
       * @return This builder for chaining.
       */
      public Builder setPort(int value) {
        bitField0_ |= 0x00000002;
        port_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 port = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPort() {
        bitField0_ = (bitField0_ & ~0x00000002);
        port_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object channelName_ = "";
      /**
       * <pre>
       * 实际加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return Whether the channelName field is set.
       */
      public boolean hasChannelName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 实际加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return The channelName.
       */
      public java.lang.String getChannelName() {
        java.lang.Object ref = channelName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 实际加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return The bytes for channelName.
       */
      public com.google.protobuf.ByteString
          getChannelNameBytes() {
        java.lang.Object ref = channelName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 实际加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @param value The channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 实际加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        channelName_ = getDefaultInstance().getChannelName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 实际加速通道
       * </pre>
       *
       * <code>optional string channelName = 3;</code>
       * @param value The bytes for channelName to set.
       * @return This builder for chaining.
       */
      public Builder setChannelNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        channelName_ = value;
        onChanged();
        return this;
      }

      private boolean canRegister_ ;
      /**
       * <pre>
       * 是否可注册(对于超级白名单仅)
       * </pre>
       *
       * <code>optional bool canRegister = 4;</code>
       * @return Whether the canRegister field is set.
       */
      @java.lang.Override
      public boolean hasCanRegister() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 是否可注册(对于超级白名单仅)
       * </pre>
       *
       * <code>optional bool canRegister = 4;</code>
       * @return The canRegister.
       */
      @java.lang.Override
      public boolean getCanRegister() {
        return canRegister_;
      }
      /**
       * <pre>
       * 是否可注册(对于超级白名单仅)
       * </pre>
       *
       * <code>optional bool canRegister = 4;</code>
       * @param value The canRegister to set.
       * @return This builder for chaining.
       */
      public Builder setCanRegister(boolean value) {
        bitField0_ |= 0x00000008;
        canRegister_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否可注册(对于超级白名单仅)
       * </pre>
       *
       * <code>optional bool canRegister = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCanRegister() {
        bitField0_ = (bitField0_ & ~0x00000008);
        canRegister_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetZoneIpPortAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetZoneIpPortAns)
    private static final com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns();
    }

    public static com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetZoneIpPortAns>
        PARSER = new com.google.protobuf.AbstractParser<GetZoneIpPortAns>() {
      @java.lang.Override
      public GetZoneIpPortAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetZoneIpPortAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetZoneIpPortAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetZoneIpPortAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneInfoMgr.GetZoneIpPortAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetOnlinePlayerIdAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetOnlinePlayerIdAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckPlayerOnlineAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckPlayerOnlineAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckPlayerOnlineAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckPlayerOnlineAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetZoneIpPortAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetZoneIpPortAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetZoneIpPortAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetZoneIpPortAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*ss_proto/gen/scene/ss_scene_info_mgr.p" +
      "roto\022\017com.yorha.proto\032\034ss_proto/gen/cnc/" +
      "basic.proto\"\026\n\024GetOnlinePlayerIdAsk\"B\n\024G" +
      "etOnlinePlayerIdAns\022*\n\006player\030\001 \001(\0132\032.co" +
      "m.yorha.proto.Int64List\"L\n&GetOnlinePlay" +
      "erIdFilterByCreateTimeAsk\022\021\n\tstartTime\030\001" +
      " \001(\003\022\017\n\007endTime\030\002 \001(\003\"T\n&GetOnlinePlayer" +
      "IdFilterByCreateTimeAns\022*\n\006player\030\001 \001(\0132" +
      "\032.com.yorha.proto.Int64List\"&\n\024CheckPlay" +
      "erOnlineAsk\022\016\n\006player\030\001 \003(\003\",\n\024CheckPlay" +
      "erOnlineAns\022\024\n\014onlinePlayer\030\001 \003(\003\"=\n\020Get" +
      "ZoneIpPortAsk\022\023\n\013channelName\030\001 \001(\t\022\024\n\014is" +
      "SuperWhite\030\002 \001(\010\"V\n\020GetZoneIpPortAns\022\n\n\002" +
      "ip\030\001 \001(\t\022\014\n\004port\030\002 \001(\005\022\023\n\013channelName\030\003 " +
      "\001(\t\022\023\n\013canRegister\030\004 \001(\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Basic.getDescriptor(),
        });
    internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetOnlinePlayerIdAsk_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_GetOnlinePlayerIdAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_GetOnlinePlayerIdAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetOnlinePlayerIdAns_descriptor,
        new java.lang.String[] { "Player", });
    internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAsk_descriptor,
        new java.lang.String[] { "StartTime", "EndTime", });
    internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetOnlinePlayerIdFilterByCreateTimeAns_descriptor,
        new java.lang.String[] { "Player", });
    internal_static_com_yorha_proto_CheckPlayerOnlineAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_CheckPlayerOnlineAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckPlayerOnlineAsk_descriptor,
        new java.lang.String[] { "Player", });
    internal_static_com_yorha_proto_CheckPlayerOnlineAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_CheckPlayerOnlineAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckPlayerOnlineAns_descriptor,
        new java.lang.String[] { "OnlinePlayer", });
    internal_static_com_yorha_proto_GetZoneIpPortAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_GetZoneIpPortAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetZoneIpPortAsk_descriptor,
        new java.lang.String[] { "ChannelName", "IsSuperWhite", });
    internal_static_com_yorha_proto_GetZoneIpPortAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_GetZoneIpPortAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetZoneIpPortAns_descriptor,
        new java.lang.String[] { "Ip", "Port", "ChannelName", "CanRegister", });
    com.yorha.proto.Basic.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

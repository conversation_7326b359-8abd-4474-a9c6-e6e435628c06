// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/mapBuilding/map_buildingPB.proto

package com.yorha.proto;

public final class MapBuildingPB {
  private MapBuildingPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MapBuildingEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MapBuildingEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
     * @return Whether the occupyinfo field is set.
     */
    boolean hasOccupyinfo();
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
     * @return The occupyinfo.
     */
    com.yorha.proto.MapBuildingPB.OccupyInfoPB getOccupyinfo();
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
     */
    com.yorha.proto.MapBuildingPB.OccupyInfoPBOrBuilder getOccupyinfoOrBuilder();

    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return Whether the troop field is set.
     */
    boolean hasTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return The troop.
     */
    com.yorha.proto.StructPlayerPB.TroopPB getTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     */
    com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder();

    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return Whether the battle field is set.
     */
    boolean hasBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return The battle.
     */
    com.yorha.proto.StructBattlePB.BattlePB getBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     */
    com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder();

    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
     * @return Whether the buffSys field is set.
     */
    boolean hasBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
     * @return The buffSys.
     */
    com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
     */
    com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder();

    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
     * @return Whether the constructInfo field is set.
     */
    boolean hasConstructInfo();
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
     * @return The constructInfo.
     */
    com.yorha.proto.MapBuildingPB.ConstructInfoPB getConstructInfo();
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
     */
    com.yorha.proto.MapBuildingPB.ConstructInfoPBOrBuilder getConstructInfoOrBuilder();

    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
     * @return Whether the devBuffSys field is set.
     */
    boolean hasDevBuffSys();
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
     * @return The devBuffSys.
     */
    com.yorha.proto.StructBattlePB.SceneDevBuffSysPB getDevBuffSys();
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
     */
    com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder getDevBuffSysOrBuilder();

    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
     * @return Whether the safeGuard field is set.
     */
    boolean hasSafeGuard();
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
     * @return The safeGuard.
     */
    com.yorha.proto.StructPB.SpecialSafeGuardPB getSafeGuard();
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
     */
    com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder getSafeGuardOrBuilder();

    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
     * @return Whether the recommendSoldierTypeList field is set.
     */
    boolean hasRecommendSoldierTypeList();
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
     * @return The recommendSoldierTypeList.
     */
    com.yorha.proto.BasicPB.Int32ListPB getRecommendSoldierTypeList();
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
     */
    com.yorha.proto.BasicPB.Int32ListPBOrBuilder getRecommendSoldierTypeListOrBuilder();

    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return Whether the passingArmyNum field is set.
     */
    boolean hasPassingArmyNum();
    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return The passingArmyNum.
     */
    int getPassingArmyNum();

    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
     * @return Whether the expression field is set.
     */
    boolean hasExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
     * @return The expression.
     */
    com.yorha.proto.StructPB.ExpressionPB getExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
     */
    com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder();

    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
     * @return Whether the kingdomModel field is set.
     */
    boolean hasKingdomModel();
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
     * @return The kingdomModel.
     */
    com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB getKingdomModel();
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
     */
    com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPBOrBuilder getKingdomModelOrBuilder();

    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return Whether the arrow field is set.
     */
    boolean hasArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return The arrow.
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder();
  }
  /**
   * <pre>
   * 地图中立建筑
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.MapBuildingEntityPB}
   */
  public static final class MapBuildingEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MapBuildingEntityPB)
      MapBuildingEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MapBuildingEntityPB.newBuilder() to construct.
    private MapBuildingEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MapBuildingEntityPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MapBuildingEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MapBuildingEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              partId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              templateId_ = input.readInt32();
              break;
            }
            case 42: {
              com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = occupyinfo_.toBuilder();
              }
              occupyinfo_ = input.readMessage(com.yorha.proto.MapBuildingPB.OccupyInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(occupyinfo_);
                occupyinfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 50: {
              com.yorha.proto.StructPlayerPB.TroopPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = troop_.toBuilder();
              }
              troop_ = input.readMessage(com.yorha.proto.StructPlayerPB.TroopPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(troop_);
                troop_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 58: {
              com.yorha.proto.StructBattlePB.BattlePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000020) != 0)) {
                subBuilder = battle_.toBuilder();
              }
              battle_ = input.readMessage(com.yorha.proto.StructBattlePB.BattlePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battle_);
                battle_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000020;
              break;
            }
            case 66: {
              com.yorha.proto.StructBattlePB.BuffSysPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = buffSys_.toBuilder();
              }
              buffSys_ = input.readMessage(com.yorha.proto.StructBattlePB.BuffSysPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buffSys_);
                buffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 74: {
              com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000080) != 0)) {
                subBuilder = constructInfo_.toBuilder();
              }
              constructInfo_ = input.readMessage(com.yorha.proto.MapBuildingPB.ConstructInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(constructInfo_);
                constructInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000080;
              break;
            }
            case 90: {
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) != 0)) {
                subBuilder = devBuffSys_.toBuilder();
              }
              devBuffSys_ = input.readMessage(com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(devBuffSys_);
                devBuffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
            case 98: {
              com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000200) != 0)) {
                subBuilder = safeGuard_.toBuilder();
              }
              safeGuard_ = input.readMessage(com.yorha.proto.StructPB.SpecialSafeGuardPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(safeGuard_);
                safeGuard_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000200;
              break;
            }
            case 106: {
              com.yorha.proto.BasicPB.Int32ListPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000400) != 0)) {
                subBuilder = recommendSoldierTypeList_.toBuilder();
              }
              recommendSoldierTypeList_ = input.readMessage(com.yorha.proto.BasicPB.Int32ListPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(recommendSoldierTypeList_);
                recommendSoldierTypeList_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000400;
              break;
            }
            case 112: {
              bitField0_ |= 0x00000800;
              passingArmyNum_ = input.readInt32();
              break;
            }
            case 122: {
              com.yorha.proto.StructPB.ExpressionPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00001000) != 0)) {
                subBuilder = expression_.toBuilder();
              }
              expression_ = input.readMessage(com.yorha.proto.StructPB.ExpressionPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(expression_);
                expression_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00001000;
              break;
            }
            case 130: {
              com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00002000) != 0)) {
                subBuilder = kingdomModel_.toBuilder();
              }
              kingdomModel_ = input.readMessage(com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(kingdomModel_);
                kingdomModel_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00002000;
              break;
            }
            case 186: {
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00004000) != 0)) {
                subBuilder = arrow_.toBuilder();
              }
              arrow_ = input.readMessage(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(arrow_);
                arrow_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00004000;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.class, com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int POINT_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int PARTID_FIELD_NUMBER = 2;
    private int partId_;
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 3;
    private int templateId_;
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int OCCUPYINFO_FIELD_NUMBER = 5;
    private com.yorha.proto.MapBuildingPB.OccupyInfoPB occupyinfo_;
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
     * @return Whether the occupyinfo field is set.
     */
    @java.lang.Override
    public boolean hasOccupyinfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
     * @return The occupyinfo.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.OccupyInfoPB getOccupyinfo() {
      return occupyinfo_ == null ? com.yorha.proto.MapBuildingPB.OccupyInfoPB.getDefaultInstance() : occupyinfo_;
    }
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.OccupyInfoPBOrBuilder getOccupyinfoOrBuilder() {
      return occupyinfo_ == null ? com.yorha.proto.MapBuildingPB.OccupyInfoPB.getDefaultInstance() : occupyinfo_;
    }

    public static final int TROOP_FIELD_NUMBER = 6;
    private com.yorha.proto.StructPlayerPB.TroopPB troop_;
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return Whether the troop field is set.
     */
    @java.lang.Override
    public boolean hasTroop() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     * @return The troop.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }

    public static final int BATTLE_FIELD_NUMBER = 7;
    private com.yorha.proto.StructBattlePB.BattlePB battle_;
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return Whether the battle field is set.
     */
    @java.lang.Override
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     * @return The battle.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }

    public static final int BUFFSYS_FIELD_NUMBER = 8;
    private com.yorha.proto.StructBattlePB.BuffSysPB buffSys_;
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
     * @return Whether the buffSys field is set.
     */
    @java.lang.Override
    public boolean hasBuffSys() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
     * @return The buffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys() {
      return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder() {
      return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
    }

    public static final int CONSTRUCTINFO_FIELD_NUMBER = 9;
    private com.yorha.proto.MapBuildingPB.ConstructInfoPB constructInfo_;
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
     * @return Whether the constructInfo field is set.
     */
    @java.lang.Override
    public boolean hasConstructInfo() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
     * @return The constructInfo.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.ConstructInfoPB getConstructInfo() {
      return constructInfo_ == null ? com.yorha.proto.MapBuildingPB.ConstructInfoPB.getDefaultInstance() : constructInfo_;
    }
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.ConstructInfoPBOrBuilder getConstructInfoOrBuilder() {
      return constructInfo_ == null ? com.yorha.proto.MapBuildingPB.ConstructInfoPB.getDefaultInstance() : constructInfo_;
    }

    public static final int DEVBUFFSYS_FIELD_NUMBER = 11;
    private com.yorha.proto.StructBattlePB.SceneDevBuffSysPB devBuffSys_;
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
     * @return Whether the devBuffSys field is set.
     */
    @java.lang.Override
    public boolean hasDevBuffSys() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
     * @return The devBuffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.SceneDevBuffSysPB getDevBuffSys() {
      return devBuffSys_ == null ? com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
    }
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder getDevBuffSysOrBuilder() {
      return devBuffSys_ == null ? com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
    }

    public static final int SAFEGUARD_FIELD_NUMBER = 12;
    private com.yorha.proto.StructPB.SpecialSafeGuardPB safeGuard_;
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
     * @return Whether the safeGuard field is set.
     */
    @java.lang.Override
    public boolean hasSafeGuard() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
     * @return The safeGuard.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.SpecialSafeGuardPB getSafeGuard() {
      return safeGuard_ == null ? com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
    }
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder getSafeGuardOrBuilder() {
      return safeGuard_ == null ? com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
    }

    public static final int RECOMMENDSOLDIERTYPELIST_FIELD_NUMBER = 13;
    private com.yorha.proto.BasicPB.Int32ListPB recommendSoldierTypeList_;
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
     * @return Whether the recommendSoldierTypeList field is set.
     */
    @java.lang.Override
    public boolean hasRecommendSoldierTypeList() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
     * @return The recommendSoldierTypeList.
     */
    @java.lang.Override
    public com.yorha.proto.BasicPB.Int32ListPB getRecommendSoldierTypeList() {
      return recommendSoldierTypeList_ == null ? com.yorha.proto.BasicPB.Int32ListPB.getDefaultInstance() : recommendSoldierTypeList_;
    }
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
     */
    @java.lang.Override
    public com.yorha.proto.BasicPB.Int32ListPBOrBuilder getRecommendSoldierTypeListOrBuilder() {
      return recommendSoldierTypeList_ == null ? com.yorha.proto.BasicPB.Int32ListPB.getDefaultInstance() : recommendSoldierTypeList_;
    }

    public static final int PASSINGARMYNUM_FIELD_NUMBER = 14;
    private int passingArmyNum_;
    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return Whether the passingArmyNum field is set.
     */
    @java.lang.Override
    public boolean hasPassingArmyNum() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return The passingArmyNum.
     */
    @java.lang.Override
    public int getPassingArmyNum() {
      return passingArmyNum_;
    }

    public static final int EXPRESSION_FIELD_NUMBER = 15;
    private com.yorha.proto.StructPB.ExpressionPB expression_;
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
     * @return Whether the expression field is set.
     */
    @java.lang.Override
    public boolean hasExpression() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
     * @return The expression.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPB getExpression() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }

    public static final int KINGDOMMODEL_FIELD_NUMBER = 16;
    private com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB kingdomModel_;
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
     * @return Whether the kingdomModel field is set.
     */
    @java.lang.Override
    public boolean hasKingdomModel() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
     * @return The kingdomModel.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB getKingdomModel() {
      return kingdomModel_ == null ? com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.getDefaultInstance() : kingdomModel_;
    }
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPBOrBuilder getKingdomModelOrBuilder() {
      return kingdomModel_ == null ? com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.getDefaultInstance() : kingdomModel_;
    }

    public static final int ARROW_FIELD_NUMBER = 23;
    private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return Whether the arrow field is set.
     */
    @java.lang.Override
    public boolean hasArrow() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return The arrow.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, partId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(5, getOccupyinfo());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(6, getTroop());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(7, getBattle());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(8, getBuffSys());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(9, getConstructInfo());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeMessage(11, getDevBuffSys());
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeMessage(12, getSafeGuard());
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeMessage(13, getRecommendSoldierTypeList());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt32(14, passingArmyNum_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeMessage(15, getExpression());
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeMessage(16, getKingdomModel());
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeMessage(23, getArrow());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, partId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getOccupyinfo());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getTroop());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getBattle());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getBuffSys());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, getConstructInfo());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, getDevBuffSys());
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getSafeGuard());
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, getRecommendSoldierTypeList());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(14, passingArmyNum_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, getExpression());
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, getKingdomModel());
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(23, getArrow());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuildingPB.MapBuildingEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuildingPB.MapBuildingEntityPB other = (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) obj;

      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasOccupyinfo() != other.hasOccupyinfo()) return false;
      if (hasOccupyinfo()) {
        if (!getOccupyinfo()
            .equals(other.getOccupyinfo())) return false;
      }
      if (hasTroop() != other.hasTroop()) return false;
      if (hasTroop()) {
        if (!getTroop()
            .equals(other.getTroop())) return false;
      }
      if (hasBattle() != other.hasBattle()) return false;
      if (hasBattle()) {
        if (!getBattle()
            .equals(other.getBattle())) return false;
      }
      if (hasBuffSys() != other.hasBuffSys()) return false;
      if (hasBuffSys()) {
        if (!getBuffSys()
            .equals(other.getBuffSys())) return false;
      }
      if (hasConstructInfo() != other.hasConstructInfo()) return false;
      if (hasConstructInfo()) {
        if (!getConstructInfo()
            .equals(other.getConstructInfo())) return false;
      }
      if (hasDevBuffSys() != other.hasDevBuffSys()) return false;
      if (hasDevBuffSys()) {
        if (!getDevBuffSys()
            .equals(other.getDevBuffSys())) return false;
      }
      if (hasSafeGuard() != other.hasSafeGuard()) return false;
      if (hasSafeGuard()) {
        if (!getSafeGuard()
            .equals(other.getSafeGuard())) return false;
      }
      if (hasRecommendSoldierTypeList() != other.hasRecommendSoldierTypeList()) return false;
      if (hasRecommendSoldierTypeList()) {
        if (!getRecommendSoldierTypeList()
            .equals(other.getRecommendSoldierTypeList())) return false;
      }
      if (hasPassingArmyNum() != other.hasPassingArmyNum()) return false;
      if (hasPassingArmyNum()) {
        if (getPassingArmyNum()
            != other.getPassingArmyNum()) return false;
      }
      if (hasExpression() != other.hasExpression()) return false;
      if (hasExpression()) {
        if (!getExpression()
            .equals(other.getExpression())) return false;
      }
      if (hasKingdomModel() != other.hasKingdomModel()) return false;
      if (hasKingdomModel()) {
        if (!getKingdomModel()
            .equals(other.getKingdomModel())) return false;
      }
      if (hasArrow() != other.hasArrow()) return false;
      if (hasArrow()) {
        if (!getArrow()
            .equals(other.getArrow())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasOccupyinfo()) {
        hash = (37 * hash) + OCCUPYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getOccupyinfo().hashCode();
      }
      if (hasTroop()) {
        hash = (37 * hash) + TROOP_FIELD_NUMBER;
        hash = (53 * hash) + getTroop().hashCode();
      }
      if (hasBattle()) {
        hash = (37 * hash) + BATTLE_FIELD_NUMBER;
        hash = (53 * hash) + getBattle().hashCode();
      }
      if (hasBuffSys()) {
        hash = (37 * hash) + BUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getBuffSys().hashCode();
      }
      if (hasConstructInfo()) {
        hash = (37 * hash) + CONSTRUCTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getConstructInfo().hashCode();
      }
      if (hasDevBuffSys()) {
        hash = (37 * hash) + DEVBUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getDevBuffSys().hashCode();
      }
      if (hasSafeGuard()) {
        hash = (37 * hash) + SAFEGUARD_FIELD_NUMBER;
        hash = (53 * hash) + getSafeGuard().hashCode();
      }
      if (hasRecommendSoldierTypeList()) {
        hash = (37 * hash) + RECOMMENDSOLDIERTYPELIST_FIELD_NUMBER;
        hash = (53 * hash) + getRecommendSoldierTypeList().hashCode();
      }
      if (hasPassingArmyNum()) {
        hash = (37 * hash) + PASSINGARMYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getPassingArmyNum();
      }
      if (hasExpression()) {
        hash = (37 * hash) + EXPRESSION_FIELD_NUMBER;
        hash = (53 * hash) + getExpression().hashCode();
      }
      if (hasKingdomModel()) {
        hash = (37 * hash) + KINGDOMMODEL_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomModel().hashCode();
      }
      if (hasArrow()) {
        hash = (37 * hash) + ARROW_FIELD_NUMBER;
        hash = (53 * hash) + getArrow().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuildingPB.MapBuildingEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 地图中立建筑
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.MapBuildingEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MapBuildingEntityPB)
        com.yorha.proto.MapBuildingPB.MapBuildingEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.class, com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getOccupyinfoFieldBuilder();
          getTroopFieldBuilder();
          getBattleFieldBuilder();
          getBuffSysFieldBuilder();
          getConstructInfoFieldBuilder();
          getDevBuffSysFieldBuilder();
          getSafeGuardFieldBuilder();
          getRecommendSoldierTypeListFieldBuilder();
          getExpressionFieldBuilder();
          getKingdomModelFieldBuilder();
          getArrowFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (occupyinfoBuilder_ == null) {
          occupyinfo_ = null;
        } else {
          occupyinfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (troopBuilder_ == null) {
          troop_ = null;
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        if (battleBuilder_ == null) {
          battle_ = null;
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        if (constructInfoBuilder_ == null) {
          constructInfo_ = null;
        } else {
          constructInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = null;
        } else {
          devBuffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        if (safeGuardBuilder_ == null) {
          safeGuard_ = null;
        } else {
          safeGuardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeList_ = null;
        } else {
          recommendSoldierTypeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        passingArmyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        if (expressionBuilder_ == null) {
          expression_ = null;
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00001000);
        if (kingdomModelBuilder_ == null) {
          kingdomModel_ = null;
        } else {
          kingdomModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00002000);
        if (arrowBuilder_ == null) {
          arrow_ = null;
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingEntityPB build() {
        com.yorha.proto.MapBuildingPB.MapBuildingEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingEntityPB buildPartial() {
        com.yorha.proto.MapBuildingPB.MapBuildingEntityPB result = new com.yorha.proto.MapBuildingPB.MapBuildingEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (occupyinfoBuilder_ == null) {
            result.occupyinfo_ = occupyinfo_;
          } else {
            result.occupyinfo_ = occupyinfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (troopBuilder_ == null) {
            result.troop_ = troop_;
          } else {
            result.troop_ = troopBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          if (battleBuilder_ == null) {
            result.battle_ = battle_;
          } else {
            result.battle_ = battleBuilder_.build();
          }
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (buffSysBuilder_ == null) {
            result.buffSys_ = buffSys_;
          } else {
            result.buffSys_ = buffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          if (constructInfoBuilder_ == null) {
            result.constructInfo_ = constructInfo_;
          } else {
            result.constructInfo_ = constructInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          if (devBuffSysBuilder_ == null) {
            result.devBuffSys_ = devBuffSys_;
          } else {
            result.devBuffSys_ = devBuffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          if (safeGuardBuilder_ == null) {
            result.safeGuard_ = safeGuard_;
          } else {
            result.safeGuard_ = safeGuardBuilder_.build();
          }
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          if (recommendSoldierTypeListBuilder_ == null) {
            result.recommendSoldierTypeList_ = recommendSoldierTypeList_;
          } else {
            result.recommendSoldierTypeList_ = recommendSoldierTypeListBuilder_.build();
          }
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.passingArmyNum_ = passingArmyNum_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          if (expressionBuilder_ == null) {
            result.expression_ = expression_;
          } else {
            result.expression_ = expressionBuilder_.build();
          }
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          if (kingdomModelBuilder_ == null) {
            result.kingdomModel_ = kingdomModel_;
          } else {
            result.kingdomModel_ = kingdomModelBuilder_.build();
          }
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          if (arrowBuilder_ == null) {
            result.arrow_ = arrow_;
          } else {
            result.arrow_ = arrowBuilder_.build();
          }
          to_bitField0_ |= 0x00004000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) {
          return mergeFrom((com.yorha.proto.MapBuildingPB.MapBuildingEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuildingPB.MapBuildingEntityPB other) {
        if (other == com.yorha.proto.MapBuildingPB.MapBuildingEntityPB.getDefaultInstance()) return this;
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasOccupyinfo()) {
          mergeOccupyinfo(other.getOccupyinfo());
        }
        if (other.hasTroop()) {
          mergeTroop(other.getTroop());
        }
        if (other.hasBattle()) {
          mergeBattle(other.getBattle());
        }
        if (other.hasBuffSys()) {
          mergeBuffSys(other.getBuffSys());
        }
        if (other.hasConstructInfo()) {
          mergeConstructInfo(other.getConstructInfo());
        }
        if (other.hasDevBuffSys()) {
          mergeDevBuffSys(other.getDevBuffSys());
        }
        if (other.hasSafeGuard()) {
          mergeSafeGuard(other.getSafeGuard());
        }
        if (other.hasRecommendSoldierTypeList()) {
          mergeRecommendSoldierTypeList(other.getRecommendSoldierTypeList());
        }
        if (other.hasPassingArmyNum()) {
          setPassingArmyNum(other.getPassingArmyNum());
        }
        if (other.hasExpression()) {
          mergeExpression(other.getExpression());
        }
        if (other.hasKingdomModel()) {
          mergeKingdomModel(other.getKingdomModel());
        }
        if (other.hasArrow()) {
          mergeArrow(other.getArrow());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuildingPB.MapBuildingEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuildingPB.MapBuildingEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private int partId_ ;
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000002;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000004;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.MapBuildingPB.OccupyInfoPB occupyinfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.OccupyInfoPB, com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder, com.yorha.proto.MapBuildingPB.OccupyInfoPBOrBuilder> occupyinfoBuilder_;
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       * @return Whether the occupyinfo field is set.
       */
      public boolean hasOccupyinfo() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       * @return The occupyinfo.
       */
      public com.yorha.proto.MapBuildingPB.OccupyInfoPB getOccupyinfo() {
        if (occupyinfoBuilder_ == null) {
          return occupyinfo_ == null ? com.yorha.proto.MapBuildingPB.OccupyInfoPB.getDefaultInstance() : occupyinfo_;
        } else {
          return occupyinfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       */
      public Builder setOccupyinfo(com.yorha.proto.MapBuildingPB.OccupyInfoPB value) {
        if (occupyinfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          occupyinfo_ = value;
          onChanged();
        } else {
          occupyinfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       */
      public Builder setOccupyinfo(
          com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder builderForValue) {
        if (occupyinfoBuilder_ == null) {
          occupyinfo_ = builderForValue.build();
          onChanged();
        } else {
          occupyinfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       */
      public Builder mergeOccupyinfo(com.yorha.proto.MapBuildingPB.OccupyInfoPB value) {
        if (occupyinfoBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              occupyinfo_ != null &&
              occupyinfo_ != com.yorha.proto.MapBuildingPB.OccupyInfoPB.getDefaultInstance()) {
            occupyinfo_ =
              com.yorha.proto.MapBuildingPB.OccupyInfoPB.newBuilder(occupyinfo_).mergeFrom(value).buildPartial();
          } else {
            occupyinfo_ = value;
          }
          onChanged();
        } else {
          occupyinfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       */
      public Builder clearOccupyinfo() {
        if (occupyinfoBuilder_ == null) {
          occupyinfo_ = null;
          onChanged();
        } else {
          occupyinfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       */
      public com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder getOccupyinfoBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getOccupyinfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       */
      public com.yorha.proto.MapBuildingPB.OccupyInfoPBOrBuilder getOccupyinfoOrBuilder() {
        if (occupyinfoBuilder_ != null) {
          return occupyinfoBuilder_.getMessageOrBuilder();
        } else {
          return occupyinfo_ == null ?
              com.yorha.proto.MapBuildingPB.OccupyInfoPB.getDefaultInstance() : occupyinfo_;
        }
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfoPB occupyinfo = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.OccupyInfoPB, com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder, com.yorha.proto.MapBuildingPB.OccupyInfoPBOrBuilder> 
          getOccupyinfoFieldBuilder() {
        if (occupyinfoBuilder_ == null) {
          occupyinfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuildingPB.OccupyInfoPB, com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder, com.yorha.proto.MapBuildingPB.OccupyInfoPBOrBuilder>(
                  getOccupyinfo(),
                  getParentForChildren(),
                  isClean());
          occupyinfo_ = null;
        }
        return occupyinfoBuilder_;
      }

      private com.yorha.proto.StructPlayerPB.TroopPB troop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> troopBuilder_;
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       * @return Whether the troop field is set.
       */
      public boolean hasTroop() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       * @return The troop.
       */
      public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
        if (troopBuilder_ == null) {
          return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        } else {
          return troopBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder setTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          troop_ = value;
          onChanged();
        } else {
          troopBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder setTroop(
          com.yorha.proto.StructPlayerPB.TroopPB.Builder builderForValue) {
        if (troopBuilder_ == null) {
          troop_ = builderForValue.build();
          onChanged();
        } else {
          troopBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder mergeTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              troop_ != null &&
              troop_ != com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance()) {
            troop_ =
              com.yorha.proto.StructPlayerPB.TroopPB.newBuilder(troop_).mergeFrom(value).buildPartial();
          } else {
            troop_ = value;
          }
          onChanged();
        } else {
          troopBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public Builder clearTroop() {
        if (troopBuilder_ == null) {
          troop_ = null;
          onChanged();
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPB.Builder getTroopBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getTroopFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
        if (troopBuilder_ != null) {
          return troopBuilder_.getMessageOrBuilder();
        } else {
          return troop_ == null ?
              com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.TroopPB troop = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> 
          getTroopFieldBuilder() {
        if (troopBuilder_ == null) {
          troopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder>(
                  getTroop(),
                  getParentForChildren(),
                  isClean());
          troop_ = null;
        }
        return troopBuilder_;
      }

      private com.yorha.proto.StructBattlePB.BattlePB battle_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> battleBuilder_;
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       * @return Whether the battle field is set.
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       * @return The battle.
       */
      public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
        if (battleBuilder_ == null) {
          return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        } else {
          return battleBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder setBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battle_ = value;
          onChanged();
        } else {
          battleBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder setBattle(
          com.yorha.proto.StructBattlePB.BattlePB.Builder builderForValue) {
        if (battleBuilder_ == null) {
          battle_ = builderForValue.build();
          onChanged();
        } else {
          battleBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder mergeBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
              battle_ != null &&
              battle_ != com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance()) {
            battle_ =
              com.yorha.proto.StructBattlePB.BattlePB.newBuilder(battle_).mergeFrom(value).buildPartial();
          } else {
            battle_ = value;
          }
          onChanged();
        } else {
          battleBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public Builder clearBattle() {
        if (battleBuilder_ == null) {
          battle_ = null;
          onChanged();
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePB.Builder getBattleBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getBattleFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
        if (battleBuilder_ != null) {
          return battleBuilder_.getMessageOrBuilder();
        } else {
          return battle_ == null ?
              com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.BattlePB battle = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> 
          getBattleFieldBuilder() {
        if (battleBuilder_ == null) {
          battleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder>(
                  getBattle(),
                  getParentForChildren(),
                  isClean());
          battle_ = null;
        }
        return battleBuilder_;
      }

      private com.yorha.proto.StructBattlePB.BuffSysPB buffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder> buffSysBuilder_;
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       * @return Whether the buffSys field is set.
       */
      public boolean hasBuffSys() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       * @return The buffSys.
       */
      public com.yorha.proto.StructBattlePB.BuffSysPB getBuffSys() {
        if (buffSysBuilder_ == null) {
          return buffSys_ == null ? com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
        } else {
          return buffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       */
      public Builder setBuffSys(com.yorha.proto.StructBattlePB.BuffSysPB value) {
        if (buffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buffSys_ = value;
          onChanged();
        } else {
          buffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       */
      public Builder setBuffSys(
          com.yorha.proto.StructBattlePB.BuffSysPB.Builder builderForValue) {
        if (buffSysBuilder_ == null) {
          buffSys_ = builderForValue.build();
          onChanged();
        } else {
          buffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       */
      public Builder mergeBuffSys(com.yorha.proto.StructBattlePB.BuffSysPB value) {
        if (buffSysBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              buffSys_ != null &&
              buffSys_ != com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance()) {
            buffSys_ =
              com.yorha.proto.StructBattlePB.BuffSysPB.newBuilder(buffSys_).mergeFrom(value).buildPartial();
          } else {
            buffSys_ = value;
          }
          onChanged();
        } else {
          buffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       */
      public Builder clearBuffSys() {
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
          onChanged();
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       */
      public com.yorha.proto.StructBattlePB.BuffSysPB.Builder getBuffSysBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       */
      public com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder getBuffSysOrBuilder() {
        if (buffSysBuilder_ != null) {
          return buffSysBuilder_.getMessageOrBuilder();
        } else {
          return buffSys_ == null ?
              com.yorha.proto.StructBattlePB.BuffSysPB.getDefaultInstance() : buffSys_;
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSysPB buffSys = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder> 
          getBuffSysFieldBuilder() {
        if (buffSysBuilder_ == null) {
          buffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BuffSysPB, com.yorha.proto.StructBattlePB.BuffSysPB.Builder, com.yorha.proto.StructBattlePB.BuffSysPBOrBuilder>(
                  getBuffSys(),
                  getParentForChildren(),
                  isClean());
          buffSys_ = null;
        }
        return buffSysBuilder_;
      }

      private com.yorha.proto.MapBuildingPB.ConstructInfoPB constructInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.ConstructInfoPB, com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder, com.yorha.proto.MapBuildingPB.ConstructInfoPBOrBuilder> constructInfoBuilder_;
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       * @return Whether the constructInfo field is set.
       */
      public boolean hasConstructInfo() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       * @return The constructInfo.
       */
      public com.yorha.proto.MapBuildingPB.ConstructInfoPB getConstructInfo() {
        if (constructInfoBuilder_ == null) {
          return constructInfo_ == null ? com.yorha.proto.MapBuildingPB.ConstructInfoPB.getDefaultInstance() : constructInfo_;
        } else {
          return constructInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       */
      public Builder setConstructInfo(com.yorha.proto.MapBuildingPB.ConstructInfoPB value) {
        if (constructInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          constructInfo_ = value;
          onChanged();
        } else {
          constructInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       */
      public Builder setConstructInfo(
          com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder builderForValue) {
        if (constructInfoBuilder_ == null) {
          constructInfo_ = builderForValue.build();
          onChanged();
        } else {
          constructInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       */
      public Builder mergeConstructInfo(com.yorha.proto.MapBuildingPB.ConstructInfoPB value) {
        if (constructInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
              constructInfo_ != null &&
              constructInfo_ != com.yorha.proto.MapBuildingPB.ConstructInfoPB.getDefaultInstance()) {
            constructInfo_ =
              com.yorha.proto.MapBuildingPB.ConstructInfoPB.newBuilder(constructInfo_).mergeFrom(value).buildPartial();
          } else {
            constructInfo_ = value;
          }
          onChanged();
        } else {
          constructInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       */
      public Builder clearConstructInfo() {
        if (constructInfoBuilder_ == null) {
          constructInfo_ = null;
          onChanged();
        } else {
          constructInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       */
      public com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder getConstructInfoBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getConstructInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       */
      public com.yorha.proto.MapBuildingPB.ConstructInfoPBOrBuilder getConstructInfoOrBuilder() {
        if (constructInfoBuilder_ != null) {
          return constructInfoBuilder_.getMessageOrBuilder();
        } else {
          return constructInfo_ == null ?
              com.yorha.proto.MapBuildingPB.ConstructInfoPB.getDefaultInstance() : constructInfo_;
        }
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfoPB constructInfo = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.ConstructInfoPB, com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder, com.yorha.proto.MapBuildingPB.ConstructInfoPBOrBuilder> 
          getConstructInfoFieldBuilder() {
        if (constructInfoBuilder_ == null) {
          constructInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuildingPB.ConstructInfoPB, com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder, com.yorha.proto.MapBuildingPB.ConstructInfoPBOrBuilder>(
                  getConstructInfo(),
                  getParentForChildren(),
                  isClean());
          constructInfo_ = null;
        }
        return constructInfoBuilder_;
      }

      private com.yorha.proto.StructBattlePB.SceneDevBuffSysPB devBuffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.SceneDevBuffSysPB, com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder, com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder> devBuffSysBuilder_;
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       * @return Whether the devBuffSys field is set.
       */
      public boolean hasDevBuffSys() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       * @return The devBuffSys.
       */
      public com.yorha.proto.StructBattlePB.SceneDevBuffSysPB getDevBuffSys() {
        if (devBuffSysBuilder_ == null) {
          return devBuffSys_ == null ? com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
        } else {
          return devBuffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       */
      public Builder setDevBuffSys(com.yorha.proto.StructBattlePB.SceneDevBuffSysPB value) {
        if (devBuffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          devBuffSys_ = value;
          onChanged();
        } else {
          devBuffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       */
      public Builder setDevBuffSys(
          com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder builderForValue) {
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = builderForValue.build();
          onChanged();
        } else {
          devBuffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       */
      public Builder mergeDevBuffSys(com.yorha.proto.StructBattlePB.SceneDevBuffSysPB value) {
        if (devBuffSysBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0) &&
              devBuffSys_ != null &&
              devBuffSys_ != com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance()) {
            devBuffSys_ =
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.newBuilder(devBuffSys_).mergeFrom(value).buildPartial();
          } else {
            devBuffSys_ = value;
          }
          onChanged();
        } else {
          devBuffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       */
      public Builder clearDevBuffSys() {
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = null;
          onChanged();
        } else {
          devBuffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       */
      public com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder getDevBuffSysBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getDevBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       */
      public com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder getDevBuffSysOrBuilder() {
        if (devBuffSysBuilder_ != null) {
          return devBuffSysBuilder_.getMessageOrBuilder();
        } else {
          return devBuffSys_ == null ?
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.getDefaultInstance() : devBuffSys_;
        }
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSysPB devBuffSys = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.SceneDevBuffSysPB, com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder, com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder> 
          getDevBuffSysFieldBuilder() {
        if (devBuffSysBuilder_ == null) {
          devBuffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.SceneDevBuffSysPB, com.yorha.proto.StructBattlePB.SceneDevBuffSysPB.Builder, com.yorha.proto.StructBattlePB.SceneDevBuffSysPBOrBuilder>(
                  getDevBuffSys(),
                  getParentForChildren(),
                  isClean());
          devBuffSys_ = null;
        }
        return devBuffSysBuilder_;
      }

      private com.yorha.proto.StructPB.SpecialSafeGuardPB safeGuard_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.SpecialSafeGuardPB, com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder, com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder> safeGuardBuilder_;
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       * @return Whether the safeGuard field is set.
       */
      public boolean hasSafeGuard() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       * @return The safeGuard.
       */
      public com.yorha.proto.StructPB.SpecialSafeGuardPB getSafeGuard() {
        if (safeGuardBuilder_ == null) {
          return safeGuard_ == null ? com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
        } else {
          return safeGuardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       */
      public Builder setSafeGuard(com.yorha.proto.StructPB.SpecialSafeGuardPB value) {
        if (safeGuardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          safeGuard_ = value;
          onChanged();
        } else {
          safeGuardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       */
      public Builder setSafeGuard(
          com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder builderForValue) {
        if (safeGuardBuilder_ == null) {
          safeGuard_ = builderForValue.build();
          onChanged();
        } else {
          safeGuardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       */
      public Builder mergeSafeGuard(com.yorha.proto.StructPB.SpecialSafeGuardPB value) {
        if (safeGuardBuilder_ == null) {
          if (((bitField0_ & 0x00000200) != 0) &&
              safeGuard_ != null &&
              safeGuard_ != com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance()) {
            safeGuard_ =
              com.yorha.proto.StructPB.SpecialSafeGuardPB.newBuilder(safeGuard_).mergeFrom(value).buildPartial();
          } else {
            safeGuard_ = value;
          }
          onChanged();
        } else {
          safeGuardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       */
      public Builder clearSafeGuard() {
        if (safeGuardBuilder_ == null) {
          safeGuard_ = null;
          onChanged();
        } else {
          safeGuardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       */
      public com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder getSafeGuardBuilder() {
        bitField0_ |= 0x00000200;
        onChanged();
        return getSafeGuardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       */
      public com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder getSafeGuardOrBuilder() {
        if (safeGuardBuilder_ != null) {
          return safeGuardBuilder_.getMessageOrBuilder();
        } else {
          return safeGuard_ == null ?
              com.yorha.proto.StructPB.SpecialSafeGuardPB.getDefaultInstance() : safeGuard_;
        }
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuardPB safeGuard = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.SpecialSafeGuardPB, com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder, com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder> 
          getSafeGuardFieldBuilder() {
        if (safeGuardBuilder_ == null) {
          safeGuardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.SpecialSafeGuardPB, com.yorha.proto.StructPB.SpecialSafeGuardPB.Builder, com.yorha.proto.StructPB.SpecialSafeGuardPBOrBuilder>(
                  getSafeGuard(),
                  getParentForChildren(),
                  isClean());
          safeGuard_ = null;
        }
        return safeGuardBuilder_;
      }

      private com.yorha.proto.BasicPB.Int32ListPB recommendSoldierTypeList_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.BasicPB.Int32ListPB, com.yorha.proto.BasicPB.Int32ListPB.Builder, com.yorha.proto.BasicPB.Int32ListPBOrBuilder> recommendSoldierTypeListBuilder_;
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       * @return Whether the recommendSoldierTypeList field is set.
       */
      public boolean hasRecommendSoldierTypeList() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       * @return The recommendSoldierTypeList.
       */
      public com.yorha.proto.BasicPB.Int32ListPB getRecommendSoldierTypeList() {
        if (recommendSoldierTypeListBuilder_ == null) {
          return recommendSoldierTypeList_ == null ? com.yorha.proto.BasicPB.Int32ListPB.getDefaultInstance() : recommendSoldierTypeList_;
        } else {
          return recommendSoldierTypeListBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       */
      public Builder setRecommendSoldierTypeList(com.yorha.proto.BasicPB.Int32ListPB value) {
        if (recommendSoldierTypeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          recommendSoldierTypeList_ = value;
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       */
      public Builder setRecommendSoldierTypeList(
          com.yorha.proto.BasicPB.Int32ListPB.Builder builderForValue) {
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeList_ = builderForValue.build();
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       */
      public Builder mergeRecommendSoldierTypeList(com.yorha.proto.BasicPB.Int32ListPB value) {
        if (recommendSoldierTypeListBuilder_ == null) {
          if (((bitField0_ & 0x00000400) != 0) &&
              recommendSoldierTypeList_ != null &&
              recommendSoldierTypeList_ != com.yorha.proto.BasicPB.Int32ListPB.getDefaultInstance()) {
            recommendSoldierTypeList_ =
              com.yorha.proto.BasicPB.Int32ListPB.newBuilder(recommendSoldierTypeList_).mergeFrom(value).buildPartial();
          } else {
            recommendSoldierTypeList_ = value;
          }
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       */
      public Builder clearRecommendSoldierTypeList() {
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeList_ = null;
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       */
      public com.yorha.proto.BasicPB.Int32ListPB.Builder getRecommendSoldierTypeListBuilder() {
        bitField0_ |= 0x00000400;
        onChanged();
        return getRecommendSoldierTypeListFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       */
      public com.yorha.proto.BasicPB.Int32ListPBOrBuilder getRecommendSoldierTypeListOrBuilder() {
        if (recommendSoldierTypeListBuilder_ != null) {
          return recommendSoldierTypeListBuilder_.getMessageOrBuilder();
        } else {
          return recommendSoldierTypeList_ == null ?
              com.yorha.proto.BasicPB.Int32ListPB.getDefaultInstance() : recommendSoldierTypeList_;
        }
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32ListPB recommendSoldierTypeList = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.BasicPB.Int32ListPB, com.yorha.proto.BasicPB.Int32ListPB.Builder, com.yorha.proto.BasicPB.Int32ListPBOrBuilder> 
          getRecommendSoldierTypeListFieldBuilder() {
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeListBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.BasicPB.Int32ListPB, com.yorha.proto.BasicPB.Int32ListPB.Builder, com.yorha.proto.BasicPB.Int32ListPBOrBuilder>(
                  getRecommendSoldierTypeList(),
                  getParentForChildren(),
                  isClean());
          recommendSoldierTypeList_ = null;
        }
        return recommendSoldierTypeListBuilder_;
      }

      private int passingArmyNum_ ;
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @return Whether the passingArmyNum field is set.
       */
      @java.lang.Override
      public boolean hasPassingArmyNum() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @return The passingArmyNum.
       */
      @java.lang.Override
      public int getPassingArmyNum() {
        return passingArmyNum_;
      }
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @param value The passingArmyNum to set.
       * @return This builder for chaining.
       */
      public Builder setPassingArmyNum(int value) {
        bitField0_ |= 0x00000800;
        passingArmyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearPassingArmyNum() {
        bitField0_ = (bitField0_ & ~0x00000800);
        passingArmyNum_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.ExpressionPB expression_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> expressionBuilder_;
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       * @return Whether the expression field is set.
       */
      public boolean hasExpression() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       * @return The expression.
       */
      public com.yorha.proto.StructPB.ExpressionPB getExpression() {
        if (expressionBuilder_ == null) {
          return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        } else {
          return expressionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       */
      public Builder setExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          expression_ = value;
          onChanged();
        } else {
          expressionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       */
      public Builder setExpression(
          com.yorha.proto.StructPB.ExpressionPB.Builder builderForValue) {
        if (expressionBuilder_ == null) {
          expression_ = builderForValue.build();
          onChanged();
        } else {
          expressionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       */
      public Builder mergeExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (((bitField0_ & 0x00001000) != 0) &&
              expression_ != null &&
              expression_ != com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance()) {
            expression_ =
              com.yorha.proto.StructPB.ExpressionPB.newBuilder(expression_).mergeFrom(value).buildPartial();
          } else {
            expression_ = value;
          }
          onChanged();
        } else {
          expressionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       */
      public Builder clearExpression() {
        if (expressionBuilder_ == null) {
          expression_ = null;
          onChanged();
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00001000);
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPB.Builder getExpressionBuilder() {
        bitField0_ |= 0x00001000;
        onChanged();
        return getExpressionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
        if (expressionBuilder_ != null) {
          return expressionBuilder_.getMessageOrBuilder();
        } else {
          return expression_ == null ?
              com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 15;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> 
          getExpressionFieldBuilder() {
        if (expressionBuilder_ == null) {
          expressionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder>(
                  getExpression(),
                  getParentForChildren(),
                  isClean());
          expression_ = null;
        }
        return expressionBuilder_;
      }

      private com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB kingdomModel_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPBOrBuilder> kingdomModelBuilder_;
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       * @return Whether the kingdomModel field is set.
       */
      public boolean hasKingdomModel() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       * @return The kingdomModel.
       */
      public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB getKingdomModel() {
        if (kingdomModelBuilder_ == null) {
          return kingdomModel_ == null ? com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.getDefaultInstance() : kingdomModel_;
        } else {
          return kingdomModelBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       */
      public Builder setKingdomModel(com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB value) {
        if (kingdomModelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          kingdomModel_ = value;
          onChanged();
        } else {
          kingdomModelBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00002000;
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       */
      public Builder setKingdomModel(
          com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder builderForValue) {
        if (kingdomModelBuilder_ == null) {
          kingdomModel_ = builderForValue.build();
          onChanged();
        } else {
          kingdomModelBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00002000;
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       */
      public Builder mergeKingdomModel(com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB value) {
        if (kingdomModelBuilder_ == null) {
          if (((bitField0_ & 0x00002000) != 0) &&
              kingdomModel_ != null &&
              kingdomModel_ != com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.getDefaultInstance()) {
            kingdomModel_ =
              com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.newBuilder(kingdomModel_).mergeFrom(value).buildPartial();
          } else {
            kingdomModel_ = value;
          }
          onChanged();
        } else {
          kingdomModelBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00002000;
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       */
      public Builder clearKingdomModel() {
        if (kingdomModelBuilder_ == null) {
          kingdomModel_ = null;
          onChanged();
        } else {
          kingdomModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00002000);
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       */
      public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder getKingdomModelBuilder() {
        bitField0_ |= 0x00002000;
        onChanged();
        return getKingdomModelFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       */
      public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPBOrBuilder getKingdomModelOrBuilder() {
        if (kingdomModelBuilder_ != null) {
          return kingdomModelBuilder_.getMessageOrBuilder();
        } else {
          return kingdomModel_ == null ?
              com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.getDefaultInstance() : kingdomModel_;
        }
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModelPB kingdomModel = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPBOrBuilder> 
          getKingdomModelFieldBuilder() {
        if (kingdomModelBuilder_ == null) {
          kingdomModelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPBOrBuilder>(
                  getKingdomModel(),
                  getParentForChildren(),
                  isClean());
          kingdomModel_ = null;
        }
        return kingdomModelBuilder_;
      }

      private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> arrowBuilder_;
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       * @return Whether the arrow field is set.
       */
      public boolean hasArrow() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       * @return The arrow.
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
        if (arrowBuilder_ == null) {
          return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        } else {
          return arrowBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder setArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          arrow_ = value;
          onChanged();
        } else {
          arrowBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder setArrow(
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder builderForValue) {
        if (arrowBuilder_ == null) {
          arrow_ = builderForValue.build();
          onChanged();
        } else {
          arrowBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder mergeArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (((bitField0_ & 0x00004000) != 0) &&
              arrow_ != null &&
              arrow_ != com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance()) {
            arrow_ =
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.newBuilder(arrow_).mergeFrom(value).buildPartial();
          } else {
            arrow_ = value;
          }
          onChanged();
        } else {
          arrowBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder clearArrow() {
        if (arrowBuilder_ == null) {
          arrow_ = null;
          onChanged();
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder getArrowBuilder() {
        bitField0_ |= 0x00004000;
        onChanged();
        return getArrowFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
        if (arrowBuilder_ != null) {
          return arrowBuilder_.getMessageOrBuilder();
        } else {
          return arrow_ == null ?
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> 
          getArrowFieldBuilder() {
        if (arrowBuilder_ == null) {
          arrowBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder>(
                  getArrow(),
                  getParentForChildren(),
                  isClean());
          arrow_ = null;
        }
        return arrowBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MapBuildingEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MapBuildingEntityPB)
    private static final com.yorha.proto.MapBuildingPB.MapBuildingEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuildingPB.MapBuildingEntityPB();
    }

    public static com.yorha.proto.MapBuildingPB.MapBuildingEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MapBuildingEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<MapBuildingEntityPB>() {
      @java.lang.Override
      public MapBuildingEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MapBuildingEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MapBuildingEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MapBuildingEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.MapBuildingEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OccupyInfoPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OccupyInfoPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return The state.
     */
    com.yorha.proto.CommonEnum.OccupyState getState();

    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    boolean hasStateStartTsMs();
    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return The stateStartTsMs.
     */
    long getStateStartTsMs();

    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    boolean hasStateEndTsMs();
    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return The stateEndTsMs.
     */
    long getStateEndTsMs();

    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return Whether the ownerClanId field is set.
     */
    boolean hasOwnerClanId();
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return The ownerClanId.
     */
    long getOwnerClanId();

    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return Whether the showClanSimpleName field is set.
     */
    boolean hasShowClanSimpleName();
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The showClanSimpleName.
     */
    java.lang.String getShowClanSimpleName();
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The bytes for showClanSimpleName.
     */
    com.google.protobuf.ByteString
        getShowClanSimpleNameBytes();

    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return Whether the showClanName field is set.
     */
    boolean hasShowClanName();
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The showClanName.
     */
    java.lang.String getShowClanName();
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The bytes for showClanName.
     */
    com.google.protobuf.ByteString
        getShowClanNameBytes();

    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return Whether the occupyNum field is set.
     */
    boolean hasOccupyNum();
    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return The occupyNum.
     */
    int getOccupyNum();

    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return Whether the occupyNumCalcTsMs field is set.
     */
    boolean hasOccupyNumCalcTsMs();
    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return The occupyNumCalcTsMs.
     */
    long getOccupyNumCalcTsMs();

    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return Whether the occupySpeed field is set.
     */
    boolean hasOccupySpeed();
    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return The occupySpeed.
     */
    int getOccupySpeed();

    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return Whether the occupyClanId field is set.
     */
    boolean hasOccupyClanId();
    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return The occupyClanId.
     */
    long getOccupyClanId();

    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return Whether the showColor field is set.
     */
    boolean hasShowColor();
    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return The showColor.
     */
    int getShowColor();

    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return Whether the fisrtOwnTsMs field is set.
     */
    boolean hasFisrtOwnTsMs();
    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return The fisrtOwnTsMs.
     */
    long getFisrtOwnTsMs();

    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return Whether the rebuildNumCalcTsMs field is set.
     */
    boolean hasRebuildNumCalcTsMs();
    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return The rebuildNumCalcTsMs.
     */
    long getRebuildNumCalcTsMs();

    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return Whether the rebuildSpeed field is set.
     */
    boolean hasRebuildSpeed();
    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return The rebuildSpeed.
     */
    int getRebuildSpeed();

    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return Whether the rebuildNum field is set.
     */
    boolean hasRebuildNum();
    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return The rebuildNum.
     */
    int getRebuildNum();

    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return Whether the fileNumCalcTsMs field is set.
     */
    boolean hasFileNumCalcTsMs();
    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return The fileNumCalcTsMs.
     */
    long getFileNumCalcTsMs();

    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return Whether the alreadyFireNum field is set.
     */
    boolean hasAlreadyFireNum();
    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return The alreadyFireNum.
     */
    int getAlreadyFireNum();

    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return Whether the wouldOverBurn field is set.
     */
    boolean hasWouldOverBurn();
    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return The wouldOverBurn.
     */
    boolean getWouldOverBurn();

    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return Whether the rebuildTotalWork field is set.
     */
    boolean hasRebuildTotalWork();
    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return The rebuildTotalWork.
     */
    int getRebuildTotalWork();

    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return Whether the mustBurnEndTsMs field is set.
     */
    boolean hasMustBurnEndTsMs();
    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return The mustBurnEndTsMs.
     */
    long getMustBurnEndTsMs();

    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return Whether the flagColor field is set.
     */
    boolean hasFlagColor();
    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return The flagColor.
     */
    int getFlagColor();

    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return Whether the flagShading field is set.
     */
    boolean hasFlagShading();
    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return The flagShading.
     */
    int getFlagShading();

    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return Whether the flagSign field is set.
     */
    boolean hasFlagSign();
    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return The flagSign.
     */
    int getFlagSign();

    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return Whether the fireSpeed field is set.
     */
    boolean hasFireSpeed();
    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return The fireSpeed.
     */
    int getFireSpeed();

    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return Whether the triggerFireClanId field is set.
     */
    boolean hasTriggerFireClanId();
    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return The triggerFireClanId.
     */
    long getTriggerFireClanId();

    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return Whether the nationFlagId field is set.
     */
    boolean hasNationFlagId();
    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return The nationFlagId.
     */
    int getNationFlagId();

    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return The zoneId.
     */
    int getZoneId();

    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return Whether the zoneColor field is set.
     */
    boolean hasZoneColor();
    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return The zoneColor.
     */
    int getZoneColor();
  }
  /**
   * <pre>
   * 占领信息
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.OccupyInfoPB}
   */
  public static final class OccupyInfoPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OccupyInfoPB)
      OccupyInfoPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OccupyInfoPB.newBuilder() to construct.
    private OccupyInfoPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OccupyInfoPB() {
      state_ = 0;
      showClanSimpleName_ = "";
      showClanName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OccupyInfoPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OccupyInfoPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.OccupyState value = com.yorha.proto.CommonEnum.OccupyState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                state_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              stateStartTsMs_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              stateEndTsMs_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              ownerClanId_ = input.readInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              showClanSimpleName_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              showClanName_ = bs;
              break;
            }
            case 64: {
              bitField0_ |= 0x00000040;
              occupyNum_ = input.readInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000080;
              occupyNumCalcTsMs_ = input.readInt64();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000100;
              occupySpeed_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000200;
              occupyClanId_ = input.readInt64();
              break;
            }
            case 104: {
              bitField0_ |= 0x00000400;
              showColor_ = input.readInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00000800;
              fisrtOwnTsMs_ = input.readInt64();
              break;
            }
            case 120: {
              bitField0_ |= 0x00001000;
              rebuildNumCalcTsMs_ = input.readInt64();
              break;
            }
            case 128: {
              bitField0_ |= 0x00002000;
              rebuildSpeed_ = input.readInt32();
              break;
            }
            case 136: {
              bitField0_ |= 0x00004000;
              rebuildNum_ = input.readInt32();
              break;
            }
            case 144: {
              bitField0_ |= 0x00008000;
              fileNumCalcTsMs_ = input.readInt64();
              break;
            }
            case 152: {
              bitField0_ |= 0x00010000;
              alreadyFireNum_ = input.readInt32();
              break;
            }
            case 160: {
              bitField0_ |= 0x00020000;
              wouldOverBurn_ = input.readBool();
              break;
            }
            case 168: {
              bitField0_ |= 0x00040000;
              rebuildTotalWork_ = input.readInt32();
              break;
            }
            case 176: {
              bitField0_ |= 0x00080000;
              mustBurnEndTsMs_ = input.readInt64();
              break;
            }
            case 184: {
              bitField0_ |= 0x00100000;
              flagColor_ = input.readInt32();
              break;
            }
            case 192: {
              bitField0_ |= 0x00200000;
              flagShading_ = input.readInt32();
              break;
            }
            case 200: {
              bitField0_ |= 0x00400000;
              flagSign_ = input.readInt32();
              break;
            }
            case 216: {
              bitField0_ |= 0x00800000;
              fireSpeed_ = input.readInt32();
              break;
            }
            case 224: {
              bitField0_ |= 0x01000000;
              triggerFireClanId_ = input.readInt64();
              break;
            }
            case 232: {
              bitField0_ |= 0x02000000;
              nationFlagId_ = input.readInt32();
              break;
            }
            case 240: {
              bitField0_ |= 0x04000000;
              zoneId_ = input.readInt32();
              break;
            }
            case 248: {
              bitField0_ |= 0x08000000;
              zoneColor_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_OccupyInfoPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_OccupyInfoPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuildingPB.OccupyInfoPB.class, com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder.class);
    }

    private int bitField0_;
    public static final int STATE_FIELD_NUMBER = 1;
    private int state_;
    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override public boolean hasState() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return The state.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.OccupyState getState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.OccupyState result = com.yorha.proto.CommonEnum.OccupyState.valueOf(state_);
      return result == null ? com.yorha.proto.CommonEnum.OccupyState.TOS_NEUTRAL : result;
    }

    public static final int STATESTARTTSMS_FIELD_NUMBER = 2;
    private long stateStartTsMs_;
    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateStartTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return The stateStartTsMs.
     */
    @java.lang.Override
    public long getStateStartTsMs() {
      return stateStartTsMs_;
    }

    public static final int STATEENDTSMS_FIELD_NUMBER = 3;
    private long stateEndTsMs_;
    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateEndTsMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return The stateEndTsMs.
     */
    @java.lang.Override
    public long getStateEndTsMs() {
      return stateEndTsMs_;
    }

    public static final int OWNERCLANID_FIELD_NUMBER = 4;
    private long ownerClanId_;
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return Whether the ownerClanId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerClanId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return The ownerClanId.
     */
    @java.lang.Override
    public long getOwnerClanId() {
      return ownerClanId_;
    }

    public static final int SHOWCLANSIMPLENAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object showClanSimpleName_;
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return Whether the showClanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasShowClanSimpleName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The showClanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getShowClanSimpleName() {
      java.lang.Object ref = showClanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          showClanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The bytes for showClanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getShowClanSimpleNameBytes() {
      java.lang.Object ref = showClanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        showClanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHOWCLANNAME_FIELD_NUMBER = 6;
    private volatile java.lang.Object showClanName_;
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return Whether the showClanName field is set.
     */
    @java.lang.Override
    public boolean hasShowClanName() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The showClanName.
     */
    @java.lang.Override
    public java.lang.String getShowClanName() {
      java.lang.Object ref = showClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          showClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The bytes for showClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getShowClanNameBytes() {
      java.lang.Object ref = showClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        showClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OCCUPYNUM_FIELD_NUMBER = 8;
    private int occupyNum_;
    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return Whether the occupyNum field is set.
     */
    @java.lang.Override
    public boolean hasOccupyNum() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return The occupyNum.
     */
    @java.lang.Override
    public int getOccupyNum() {
      return occupyNum_;
    }

    public static final int OCCUPYNUMCALCTSMS_FIELD_NUMBER = 10;
    private long occupyNumCalcTsMs_;
    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return Whether the occupyNumCalcTsMs field is set.
     */
    @java.lang.Override
    public boolean hasOccupyNumCalcTsMs() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return The occupyNumCalcTsMs.
     */
    @java.lang.Override
    public long getOccupyNumCalcTsMs() {
      return occupyNumCalcTsMs_;
    }

    public static final int OCCUPYSPEED_FIELD_NUMBER = 11;
    private int occupySpeed_;
    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return Whether the occupySpeed field is set.
     */
    @java.lang.Override
    public boolean hasOccupySpeed() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return The occupySpeed.
     */
    @java.lang.Override
    public int getOccupySpeed() {
      return occupySpeed_;
    }

    public static final int OCCUPYCLANID_FIELD_NUMBER = 12;
    private long occupyClanId_;
    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return Whether the occupyClanId field is set.
     */
    @java.lang.Override
    public boolean hasOccupyClanId() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return The occupyClanId.
     */
    @java.lang.Override
    public long getOccupyClanId() {
      return occupyClanId_;
    }

    public static final int SHOWCOLOR_FIELD_NUMBER = 13;
    private int showColor_;
    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return Whether the showColor field is set.
     */
    @java.lang.Override
    public boolean hasShowColor() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return The showColor.
     */
    @java.lang.Override
    public int getShowColor() {
      return showColor_;
    }

    public static final int FISRTOWNTSMS_FIELD_NUMBER = 14;
    private long fisrtOwnTsMs_;
    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return Whether the fisrtOwnTsMs field is set.
     */
    @java.lang.Override
    public boolean hasFisrtOwnTsMs() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return The fisrtOwnTsMs.
     */
    @java.lang.Override
    public long getFisrtOwnTsMs() {
      return fisrtOwnTsMs_;
    }

    public static final int REBUILDNUMCALCTSMS_FIELD_NUMBER = 15;
    private long rebuildNumCalcTsMs_;
    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return Whether the rebuildNumCalcTsMs field is set.
     */
    @java.lang.Override
    public boolean hasRebuildNumCalcTsMs() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return The rebuildNumCalcTsMs.
     */
    @java.lang.Override
    public long getRebuildNumCalcTsMs() {
      return rebuildNumCalcTsMs_;
    }

    public static final int REBUILDSPEED_FIELD_NUMBER = 16;
    private int rebuildSpeed_;
    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return Whether the rebuildSpeed field is set.
     */
    @java.lang.Override
    public boolean hasRebuildSpeed() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return The rebuildSpeed.
     */
    @java.lang.Override
    public int getRebuildSpeed() {
      return rebuildSpeed_;
    }

    public static final int REBUILDNUM_FIELD_NUMBER = 17;
    private int rebuildNum_;
    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return Whether the rebuildNum field is set.
     */
    @java.lang.Override
    public boolean hasRebuildNum() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return The rebuildNum.
     */
    @java.lang.Override
    public int getRebuildNum() {
      return rebuildNum_;
    }

    public static final int FILENUMCALCTSMS_FIELD_NUMBER = 18;
    private long fileNumCalcTsMs_;
    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return Whether the fileNumCalcTsMs field is set.
     */
    @java.lang.Override
    public boolean hasFileNumCalcTsMs() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return The fileNumCalcTsMs.
     */
    @java.lang.Override
    public long getFileNumCalcTsMs() {
      return fileNumCalcTsMs_;
    }

    public static final int ALREADYFIRENUM_FIELD_NUMBER = 19;
    private int alreadyFireNum_;
    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return Whether the alreadyFireNum field is set.
     */
    @java.lang.Override
    public boolean hasAlreadyFireNum() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return The alreadyFireNum.
     */
    @java.lang.Override
    public int getAlreadyFireNum() {
      return alreadyFireNum_;
    }

    public static final int WOULDOVERBURN_FIELD_NUMBER = 20;
    private boolean wouldOverBurn_;
    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return Whether the wouldOverBurn field is set.
     */
    @java.lang.Override
    public boolean hasWouldOverBurn() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return The wouldOverBurn.
     */
    @java.lang.Override
    public boolean getWouldOverBurn() {
      return wouldOverBurn_;
    }

    public static final int REBUILDTOTALWORK_FIELD_NUMBER = 21;
    private int rebuildTotalWork_;
    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return Whether the rebuildTotalWork field is set.
     */
    @java.lang.Override
    public boolean hasRebuildTotalWork() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return The rebuildTotalWork.
     */
    @java.lang.Override
    public int getRebuildTotalWork() {
      return rebuildTotalWork_;
    }

    public static final int MUSTBURNENDTSMS_FIELD_NUMBER = 22;
    private long mustBurnEndTsMs_;
    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return Whether the mustBurnEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasMustBurnEndTsMs() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return The mustBurnEndTsMs.
     */
    @java.lang.Override
    public long getMustBurnEndTsMs() {
      return mustBurnEndTsMs_;
    }

    public static final int FLAGCOLOR_FIELD_NUMBER = 23;
    private int flagColor_;
    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return Whether the flagColor field is set.
     */
    @java.lang.Override
    public boolean hasFlagColor() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return The flagColor.
     */
    @java.lang.Override
    public int getFlagColor() {
      return flagColor_;
    }

    public static final int FLAGSHADING_FIELD_NUMBER = 24;
    private int flagShading_;
    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return Whether the flagShading field is set.
     */
    @java.lang.Override
    public boolean hasFlagShading() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return The flagShading.
     */
    @java.lang.Override
    public int getFlagShading() {
      return flagShading_;
    }

    public static final int FLAGSIGN_FIELD_NUMBER = 25;
    private int flagSign_;
    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return Whether the flagSign field is set.
     */
    @java.lang.Override
    public boolean hasFlagSign() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return The flagSign.
     */
    @java.lang.Override
    public int getFlagSign() {
      return flagSign_;
    }

    public static final int FIRESPEED_FIELD_NUMBER = 27;
    private int fireSpeed_;
    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return Whether the fireSpeed field is set.
     */
    @java.lang.Override
    public boolean hasFireSpeed() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return The fireSpeed.
     */
    @java.lang.Override
    public int getFireSpeed() {
      return fireSpeed_;
    }

    public static final int TRIGGERFIRECLANID_FIELD_NUMBER = 28;
    private long triggerFireClanId_;
    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return Whether the triggerFireClanId field is set.
     */
    @java.lang.Override
    public boolean hasTriggerFireClanId() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return The triggerFireClanId.
     */
    @java.lang.Override
    public long getTriggerFireClanId() {
      return triggerFireClanId_;
    }

    public static final int NATIONFLAGID_FIELD_NUMBER = 29;
    private int nationFlagId_;
    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return Whether the nationFlagId field is set.
     */
    @java.lang.Override
    public boolean hasNationFlagId() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return The nationFlagId.
     */
    @java.lang.Override
    public int getNationFlagId() {
      return nationFlagId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 30;
    private int zoneId_;
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    public static final int ZONECOLOR_FIELD_NUMBER = 31;
    private int zoneColor_;
    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return Whether the zoneColor field is set.
     */
    @java.lang.Override
    public boolean hasZoneColor() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return The zoneColor.
     */
    @java.lang.Override
    public int getZoneColor() {
      return zoneColor_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, state_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, stateStartTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, ownerClanId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, showClanSimpleName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, showClanName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(8, occupyNum_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt64(10, occupyNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt32(11, occupySpeed_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt64(12, occupyClanId_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeInt32(13, showColor_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt64(14, fisrtOwnTsMs_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeInt64(15, rebuildNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeInt32(16, rebuildSpeed_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeInt32(17, rebuildNum_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeInt64(18, fileNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeInt32(19, alreadyFireNum_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeBool(20, wouldOverBurn_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeInt32(21, rebuildTotalWork_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeInt64(22, mustBurnEndTsMs_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeInt32(23, flagColor_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeInt32(24, flagShading_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeInt32(25, flagSign_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeInt32(27, fireSpeed_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeInt64(28, triggerFireClanId_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeInt32(29, nationFlagId_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeInt32(30, zoneId_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeInt32(31, zoneColor_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, state_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, stateStartTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, ownerClanId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, showClanSimpleName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, showClanName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, occupyNum_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, occupyNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, occupySpeed_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(12, occupyClanId_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, showColor_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(14, fisrtOwnTsMs_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(15, rebuildNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(16, rebuildSpeed_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(17, rebuildNum_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(18, fileNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(19, alreadyFireNum_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(20, wouldOverBurn_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(21, rebuildTotalWork_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(22, mustBurnEndTsMs_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(23, flagColor_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(24, flagShading_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(25, flagSign_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(27, fireSpeed_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(28, triggerFireClanId_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(29, nationFlagId_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(30, zoneId_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(31, zoneColor_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuildingPB.OccupyInfoPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuildingPB.OccupyInfoPB other = (com.yorha.proto.MapBuildingPB.OccupyInfoPB) obj;

      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (state_ != other.state_) return false;
      }
      if (hasStateStartTsMs() != other.hasStateStartTsMs()) return false;
      if (hasStateStartTsMs()) {
        if (getStateStartTsMs()
            != other.getStateStartTsMs()) return false;
      }
      if (hasStateEndTsMs() != other.hasStateEndTsMs()) return false;
      if (hasStateEndTsMs()) {
        if (getStateEndTsMs()
            != other.getStateEndTsMs()) return false;
      }
      if (hasOwnerClanId() != other.hasOwnerClanId()) return false;
      if (hasOwnerClanId()) {
        if (getOwnerClanId()
            != other.getOwnerClanId()) return false;
      }
      if (hasShowClanSimpleName() != other.hasShowClanSimpleName()) return false;
      if (hasShowClanSimpleName()) {
        if (!getShowClanSimpleName()
            .equals(other.getShowClanSimpleName())) return false;
      }
      if (hasShowClanName() != other.hasShowClanName()) return false;
      if (hasShowClanName()) {
        if (!getShowClanName()
            .equals(other.getShowClanName())) return false;
      }
      if (hasOccupyNum() != other.hasOccupyNum()) return false;
      if (hasOccupyNum()) {
        if (getOccupyNum()
            != other.getOccupyNum()) return false;
      }
      if (hasOccupyNumCalcTsMs() != other.hasOccupyNumCalcTsMs()) return false;
      if (hasOccupyNumCalcTsMs()) {
        if (getOccupyNumCalcTsMs()
            != other.getOccupyNumCalcTsMs()) return false;
      }
      if (hasOccupySpeed() != other.hasOccupySpeed()) return false;
      if (hasOccupySpeed()) {
        if (getOccupySpeed()
            != other.getOccupySpeed()) return false;
      }
      if (hasOccupyClanId() != other.hasOccupyClanId()) return false;
      if (hasOccupyClanId()) {
        if (getOccupyClanId()
            != other.getOccupyClanId()) return false;
      }
      if (hasShowColor() != other.hasShowColor()) return false;
      if (hasShowColor()) {
        if (getShowColor()
            != other.getShowColor()) return false;
      }
      if (hasFisrtOwnTsMs() != other.hasFisrtOwnTsMs()) return false;
      if (hasFisrtOwnTsMs()) {
        if (getFisrtOwnTsMs()
            != other.getFisrtOwnTsMs()) return false;
      }
      if (hasRebuildNumCalcTsMs() != other.hasRebuildNumCalcTsMs()) return false;
      if (hasRebuildNumCalcTsMs()) {
        if (getRebuildNumCalcTsMs()
            != other.getRebuildNumCalcTsMs()) return false;
      }
      if (hasRebuildSpeed() != other.hasRebuildSpeed()) return false;
      if (hasRebuildSpeed()) {
        if (getRebuildSpeed()
            != other.getRebuildSpeed()) return false;
      }
      if (hasRebuildNum() != other.hasRebuildNum()) return false;
      if (hasRebuildNum()) {
        if (getRebuildNum()
            != other.getRebuildNum()) return false;
      }
      if (hasFileNumCalcTsMs() != other.hasFileNumCalcTsMs()) return false;
      if (hasFileNumCalcTsMs()) {
        if (getFileNumCalcTsMs()
            != other.getFileNumCalcTsMs()) return false;
      }
      if (hasAlreadyFireNum() != other.hasAlreadyFireNum()) return false;
      if (hasAlreadyFireNum()) {
        if (getAlreadyFireNum()
            != other.getAlreadyFireNum()) return false;
      }
      if (hasWouldOverBurn() != other.hasWouldOverBurn()) return false;
      if (hasWouldOverBurn()) {
        if (getWouldOverBurn()
            != other.getWouldOverBurn()) return false;
      }
      if (hasRebuildTotalWork() != other.hasRebuildTotalWork()) return false;
      if (hasRebuildTotalWork()) {
        if (getRebuildTotalWork()
            != other.getRebuildTotalWork()) return false;
      }
      if (hasMustBurnEndTsMs() != other.hasMustBurnEndTsMs()) return false;
      if (hasMustBurnEndTsMs()) {
        if (getMustBurnEndTsMs()
            != other.getMustBurnEndTsMs()) return false;
      }
      if (hasFlagColor() != other.hasFlagColor()) return false;
      if (hasFlagColor()) {
        if (getFlagColor()
            != other.getFlagColor()) return false;
      }
      if (hasFlagShading() != other.hasFlagShading()) return false;
      if (hasFlagShading()) {
        if (getFlagShading()
            != other.getFlagShading()) return false;
      }
      if (hasFlagSign() != other.hasFlagSign()) return false;
      if (hasFlagSign()) {
        if (getFlagSign()
            != other.getFlagSign()) return false;
      }
      if (hasFireSpeed() != other.hasFireSpeed()) return false;
      if (hasFireSpeed()) {
        if (getFireSpeed()
            != other.getFireSpeed()) return false;
      }
      if (hasTriggerFireClanId() != other.hasTriggerFireClanId()) return false;
      if (hasTriggerFireClanId()) {
        if (getTriggerFireClanId()
            != other.getTriggerFireClanId()) return false;
      }
      if (hasNationFlagId() != other.hasNationFlagId()) return false;
      if (hasNationFlagId()) {
        if (getNationFlagId()
            != other.getNationFlagId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (hasZoneColor() != other.hasZoneColor()) return false;
      if (hasZoneColor()) {
        if (getZoneColor()
            != other.getZoneColor()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + state_;
      }
      if (hasStateStartTsMs()) {
        hash = (37 * hash) + STATESTARTTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateStartTsMs());
      }
      if (hasStateEndTsMs()) {
        hash = (37 * hash) + STATEENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateEndTsMs());
      }
      if (hasOwnerClanId()) {
        hash = (37 * hash) + OWNERCLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerClanId());
      }
      if (hasShowClanSimpleName()) {
        hash = (37 * hash) + SHOWCLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getShowClanSimpleName().hashCode();
      }
      if (hasShowClanName()) {
        hash = (37 * hash) + SHOWCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getShowClanName().hashCode();
      }
      if (hasOccupyNum()) {
        hash = (37 * hash) + OCCUPYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getOccupyNum();
      }
      if (hasOccupyNumCalcTsMs()) {
        hash = (37 * hash) + OCCUPYNUMCALCTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOccupyNumCalcTsMs());
      }
      if (hasOccupySpeed()) {
        hash = (37 * hash) + OCCUPYSPEED_FIELD_NUMBER;
        hash = (53 * hash) + getOccupySpeed();
      }
      if (hasOccupyClanId()) {
        hash = (37 * hash) + OCCUPYCLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOccupyClanId());
      }
      if (hasShowColor()) {
        hash = (37 * hash) + SHOWCOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getShowColor();
      }
      if (hasFisrtOwnTsMs()) {
        hash = (37 * hash) + FISRTOWNTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFisrtOwnTsMs());
      }
      if (hasRebuildNumCalcTsMs()) {
        hash = (37 * hash) + REBUILDNUMCALCTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRebuildNumCalcTsMs());
      }
      if (hasRebuildSpeed()) {
        hash = (37 * hash) + REBUILDSPEED_FIELD_NUMBER;
        hash = (53 * hash) + getRebuildSpeed();
      }
      if (hasRebuildNum()) {
        hash = (37 * hash) + REBUILDNUM_FIELD_NUMBER;
        hash = (53 * hash) + getRebuildNum();
      }
      if (hasFileNumCalcTsMs()) {
        hash = (37 * hash) + FILENUMCALCTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFileNumCalcTsMs());
      }
      if (hasAlreadyFireNum()) {
        hash = (37 * hash) + ALREADYFIRENUM_FIELD_NUMBER;
        hash = (53 * hash) + getAlreadyFireNum();
      }
      if (hasWouldOverBurn()) {
        hash = (37 * hash) + WOULDOVERBURN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getWouldOverBurn());
      }
      if (hasRebuildTotalWork()) {
        hash = (37 * hash) + REBUILDTOTALWORK_FIELD_NUMBER;
        hash = (53 * hash) + getRebuildTotalWork();
      }
      if (hasMustBurnEndTsMs()) {
        hash = (37 * hash) + MUSTBURNENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMustBurnEndTsMs());
      }
      if (hasFlagColor()) {
        hash = (37 * hash) + FLAGCOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getFlagColor();
      }
      if (hasFlagShading()) {
        hash = (37 * hash) + FLAGSHADING_FIELD_NUMBER;
        hash = (53 * hash) + getFlagShading();
      }
      if (hasFlagSign()) {
        hash = (37 * hash) + FLAGSIGN_FIELD_NUMBER;
        hash = (53 * hash) + getFlagSign();
      }
      if (hasFireSpeed()) {
        hash = (37 * hash) + FIRESPEED_FIELD_NUMBER;
        hash = (53 * hash) + getFireSpeed();
      }
      if (hasTriggerFireClanId()) {
        hash = (37 * hash) + TRIGGERFIRECLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTriggerFireClanId());
      }
      if (hasNationFlagId()) {
        hash = (37 * hash) + NATIONFLAGID_FIELD_NUMBER;
        hash = (53 * hash) + getNationFlagId();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      if (hasZoneColor()) {
        hash = (37 * hash) + ZONECOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getZoneColor();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuildingPB.OccupyInfoPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 占领信息
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.OccupyInfoPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OccupyInfoPB)
        com.yorha.proto.MapBuildingPB.OccupyInfoPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_OccupyInfoPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_OccupyInfoPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuildingPB.OccupyInfoPB.class, com.yorha.proto.MapBuildingPB.OccupyInfoPB.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuildingPB.OccupyInfoPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        state_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        stateStartTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        stateEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        ownerClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        showClanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        showClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        occupyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        occupyNumCalcTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        occupySpeed_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        occupyClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        showColor_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        fisrtOwnTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        rebuildNumCalcTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00001000);
        rebuildSpeed_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        rebuildNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        fileNumCalcTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00008000);
        alreadyFireNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00010000);
        wouldOverBurn_ = false;
        bitField0_ = (bitField0_ & ~0x00020000);
        rebuildTotalWork_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        mustBurnEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00080000);
        flagColor_ = 0;
        bitField0_ = (bitField0_ & ~0x00100000);
        flagShading_ = 0;
        bitField0_ = (bitField0_ & ~0x00200000);
        flagSign_ = 0;
        bitField0_ = (bitField0_ & ~0x00400000);
        fireSpeed_ = 0;
        bitField0_ = (bitField0_ & ~0x00800000);
        triggerFireClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x01000000);
        nationFlagId_ = 0;
        bitField0_ = (bitField0_ & ~0x02000000);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x04000000);
        zoneColor_ = 0;
        bitField0_ = (bitField0_ & ~0x08000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_OccupyInfoPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.OccupyInfoPB getDefaultInstanceForType() {
        return com.yorha.proto.MapBuildingPB.OccupyInfoPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.OccupyInfoPB build() {
        com.yorha.proto.MapBuildingPB.OccupyInfoPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.OccupyInfoPB buildPartial() {
        com.yorha.proto.MapBuildingPB.OccupyInfoPB result = new com.yorha.proto.MapBuildingPB.OccupyInfoPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.state_ = state_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.stateStartTsMs_ = stateStartTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.stateEndTsMs_ = stateEndTsMs_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.ownerClanId_ = ownerClanId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.showClanSimpleName_ = showClanSimpleName_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.showClanName_ = showClanName_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.occupyNum_ = occupyNum_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.occupyNumCalcTsMs_ = occupyNumCalcTsMs_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.occupySpeed_ = occupySpeed_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.occupyClanId_ = occupyClanId_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.showColor_ = showColor_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.fisrtOwnTsMs_ = fisrtOwnTsMs_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.rebuildNumCalcTsMs_ = rebuildNumCalcTsMs_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.rebuildSpeed_ = rebuildSpeed_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.rebuildNum_ = rebuildNum_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.fileNumCalcTsMs_ = fileNumCalcTsMs_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.alreadyFireNum_ = alreadyFireNum_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.wouldOverBurn_ = wouldOverBurn_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.rebuildTotalWork_ = rebuildTotalWork_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.mustBurnEndTsMs_ = mustBurnEndTsMs_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.flagColor_ = flagColor_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.flagShading_ = flagShading_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.flagSign_ = flagSign_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.fireSpeed_ = fireSpeed_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.triggerFireClanId_ = triggerFireClanId_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.nationFlagId_ = nationFlagId_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.zoneColor_ = zoneColor_;
          to_bitField0_ |= 0x08000000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuildingPB.OccupyInfoPB) {
          return mergeFrom((com.yorha.proto.MapBuildingPB.OccupyInfoPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuildingPB.OccupyInfoPB other) {
        if (other == com.yorha.proto.MapBuildingPB.OccupyInfoPB.getDefaultInstance()) return this;
        if (other.hasState()) {
          setState(other.getState());
        }
        if (other.hasStateStartTsMs()) {
          setStateStartTsMs(other.getStateStartTsMs());
        }
        if (other.hasStateEndTsMs()) {
          setStateEndTsMs(other.getStateEndTsMs());
        }
        if (other.hasOwnerClanId()) {
          setOwnerClanId(other.getOwnerClanId());
        }
        if (other.hasShowClanSimpleName()) {
          bitField0_ |= 0x00000010;
          showClanSimpleName_ = other.showClanSimpleName_;
          onChanged();
        }
        if (other.hasShowClanName()) {
          bitField0_ |= 0x00000020;
          showClanName_ = other.showClanName_;
          onChanged();
        }
        if (other.hasOccupyNum()) {
          setOccupyNum(other.getOccupyNum());
        }
        if (other.hasOccupyNumCalcTsMs()) {
          setOccupyNumCalcTsMs(other.getOccupyNumCalcTsMs());
        }
        if (other.hasOccupySpeed()) {
          setOccupySpeed(other.getOccupySpeed());
        }
        if (other.hasOccupyClanId()) {
          setOccupyClanId(other.getOccupyClanId());
        }
        if (other.hasShowColor()) {
          setShowColor(other.getShowColor());
        }
        if (other.hasFisrtOwnTsMs()) {
          setFisrtOwnTsMs(other.getFisrtOwnTsMs());
        }
        if (other.hasRebuildNumCalcTsMs()) {
          setRebuildNumCalcTsMs(other.getRebuildNumCalcTsMs());
        }
        if (other.hasRebuildSpeed()) {
          setRebuildSpeed(other.getRebuildSpeed());
        }
        if (other.hasRebuildNum()) {
          setRebuildNum(other.getRebuildNum());
        }
        if (other.hasFileNumCalcTsMs()) {
          setFileNumCalcTsMs(other.getFileNumCalcTsMs());
        }
        if (other.hasAlreadyFireNum()) {
          setAlreadyFireNum(other.getAlreadyFireNum());
        }
        if (other.hasWouldOverBurn()) {
          setWouldOverBurn(other.getWouldOverBurn());
        }
        if (other.hasRebuildTotalWork()) {
          setRebuildTotalWork(other.getRebuildTotalWork());
        }
        if (other.hasMustBurnEndTsMs()) {
          setMustBurnEndTsMs(other.getMustBurnEndTsMs());
        }
        if (other.hasFlagColor()) {
          setFlagColor(other.getFlagColor());
        }
        if (other.hasFlagShading()) {
          setFlagShading(other.getFlagShading());
        }
        if (other.hasFlagSign()) {
          setFlagSign(other.getFlagSign());
        }
        if (other.hasFireSpeed()) {
          setFireSpeed(other.getFireSpeed());
        }
        if (other.hasTriggerFireClanId()) {
          setTriggerFireClanId(other.getTriggerFireClanId());
        }
        if (other.hasNationFlagId()) {
          setNationFlagId(other.getNationFlagId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        if (other.hasZoneColor()) {
          setZoneColor(other.getZoneColor());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuildingPB.OccupyInfoPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuildingPB.OccupyInfoPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int state_ = 0;
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override public boolean hasState() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.OccupyState getState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.OccupyState result = com.yorha.proto.CommonEnum.OccupyState.valueOf(state_);
        return result == null ? com.yorha.proto.CommonEnum.OccupyState.TOS_NEUTRAL : result;
      }
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.yorha.proto.CommonEnum.OccupyState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        bitField0_ = (bitField0_ & ~0x00000001);
        state_ = 0;
        onChanged();
        return this;
      }

      private long stateStartTsMs_ ;
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @return Whether the stateStartTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateStartTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @return The stateStartTsMs.
       */
      @java.lang.Override
      public long getStateStartTsMs() {
        return stateStartTsMs_;
      }
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @param value The stateStartTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateStartTsMs(long value) {
        bitField0_ |= 0x00000002;
        stateStartTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateStartTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        stateStartTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long stateEndTsMs_ ;
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @return Whether the stateEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateEndTsMs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @return The stateEndTsMs.
       */
      @java.lang.Override
      public long getStateEndTsMs() {
        return stateEndTsMs_;
      }
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @param value The stateEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateEndTsMs(long value) {
        bitField0_ |= 0x00000004;
        stateEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        stateEndTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long ownerClanId_ ;
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @return Whether the ownerClanId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerClanId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @return The ownerClanId.
       */
      @java.lang.Override
      public long getOwnerClanId() {
        return ownerClanId_;
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @param value The ownerClanId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerClanId(long value) {
        bitField0_ |= 0x00000008;
        ownerClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerClanId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        ownerClanId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object showClanSimpleName_ = "";
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return Whether the showClanSimpleName field is set.
       */
      public boolean hasShowClanSimpleName() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return The showClanSimpleName.
       */
      public java.lang.String getShowClanSimpleName() {
        java.lang.Object ref = showClanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            showClanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return The bytes for showClanSimpleName.
       */
      public com.google.protobuf.ByteString
          getShowClanSimpleNameBytes() {
        java.lang.Object ref = showClanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          showClanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @param value The showClanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        showClanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        showClanSimpleName_ = getDefaultInstance().getShowClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @param value The bytes for showClanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        showClanSimpleName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object showClanName_ = "";
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return Whether the showClanName field is set.
       */
      public boolean hasShowClanName() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return The showClanName.
       */
      public java.lang.String getShowClanName() {
        java.lang.Object ref = showClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            showClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return The bytes for showClanName.
       */
      public com.google.protobuf.ByteString
          getShowClanNameBytes() {
        java.lang.Object ref = showClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          showClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @param value The showClanName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        showClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowClanName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        showClanName_ = getDefaultInstance().getShowClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @param value The bytes for showClanName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        showClanName_ = value;
        onChanged();
        return this;
      }

      private int occupyNum_ ;
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @return Whether the occupyNum field is set.
       */
      @java.lang.Override
      public boolean hasOccupyNum() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @return The occupyNum.
       */
      @java.lang.Override
      public int getOccupyNum() {
        return occupyNum_;
      }
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @param value The occupyNum to set.
       * @return This builder for chaining.
       */
      public Builder setOccupyNum(int value) {
        bitField0_ |= 0x00000040;
        occupyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupyNum() {
        bitField0_ = (bitField0_ & ~0x00000040);
        occupyNum_ = 0;
        onChanged();
        return this;
      }

      private long occupyNumCalcTsMs_ ;
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @return Whether the occupyNumCalcTsMs field is set.
       */
      @java.lang.Override
      public boolean hasOccupyNumCalcTsMs() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @return The occupyNumCalcTsMs.
       */
      @java.lang.Override
      public long getOccupyNumCalcTsMs() {
        return occupyNumCalcTsMs_;
      }
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @param value The occupyNumCalcTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setOccupyNumCalcTsMs(long value) {
        bitField0_ |= 0x00000080;
        occupyNumCalcTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupyNumCalcTsMs() {
        bitField0_ = (bitField0_ & ~0x00000080);
        occupyNumCalcTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int occupySpeed_ ;
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @return Whether the occupySpeed field is set.
       */
      @java.lang.Override
      public boolean hasOccupySpeed() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @return The occupySpeed.
       */
      @java.lang.Override
      public int getOccupySpeed() {
        return occupySpeed_;
      }
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @param value The occupySpeed to set.
       * @return This builder for chaining.
       */
      public Builder setOccupySpeed(int value) {
        bitField0_ |= 0x00000100;
        occupySpeed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupySpeed() {
        bitField0_ = (bitField0_ & ~0x00000100);
        occupySpeed_ = 0;
        onChanged();
        return this;
      }

      private long occupyClanId_ ;
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @return Whether the occupyClanId field is set.
       */
      @java.lang.Override
      public boolean hasOccupyClanId() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @return The occupyClanId.
       */
      @java.lang.Override
      public long getOccupyClanId() {
        return occupyClanId_;
      }
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @param value The occupyClanId to set.
       * @return This builder for chaining.
       */
      public Builder setOccupyClanId(long value) {
        bitField0_ |= 0x00000200;
        occupyClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupyClanId() {
        bitField0_ = (bitField0_ & ~0x00000200);
        occupyClanId_ = 0L;
        onChanged();
        return this;
      }

      private int showColor_ ;
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @return Whether the showColor field is set.
       */
      @java.lang.Override
      public boolean hasShowColor() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @return The showColor.
       */
      @java.lang.Override
      public int getShowColor() {
        return showColor_;
      }
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @param value The showColor to set.
       * @return This builder for chaining.
       */
      public Builder setShowColor(int value) {
        bitField0_ |= 0x00000400;
        showColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowColor() {
        bitField0_ = (bitField0_ & ~0x00000400);
        showColor_ = 0;
        onChanged();
        return this;
      }

      private long fisrtOwnTsMs_ ;
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @return Whether the fisrtOwnTsMs field is set.
       */
      @java.lang.Override
      public boolean hasFisrtOwnTsMs() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @return The fisrtOwnTsMs.
       */
      @java.lang.Override
      public long getFisrtOwnTsMs() {
        return fisrtOwnTsMs_;
      }
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @param value The fisrtOwnTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setFisrtOwnTsMs(long value) {
        bitField0_ |= 0x00000800;
        fisrtOwnTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearFisrtOwnTsMs() {
        bitField0_ = (bitField0_ & ~0x00000800);
        fisrtOwnTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long rebuildNumCalcTsMs_ ;
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @return Whether the rebuildNumCalcTsMs field is set.
       */
      @java.lang.Override
      public boolean hasRebuildNumCalcTsMs() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @return The rebuildNumCalcTsMs.
       */
      @java.lang.Override
      public long getRebuildNumCalcTsMs() {
        return rebuildNumCalcTsMs_;
      }
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @param value The rebuildNumCalcTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildNumCalcTsMs(long value) {
        bitField0_ |= 0x00001000;
        rebuildNumCalcTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildNumCalcTsMs() {
        bitField0_ = (bitField0_ & ~0x00001000);
        rebuildNumCalcTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int rebuildSpeed_ ;
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @return Whether the rebuildSpeed field is set.
       */
      @java.lang.Override
      public boolean hasRebuildSpeed() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @return The rebuildSpeed.
       */
      @java.lang.Override
      public int getRebuildSpeed() {
        return rebuildSpeed_;
      }
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @param value The rebuildSpeed to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildSpeed(int value) {
        bitField0_ |= 0x00002000;
        rebuildSpeed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildSpeed() {
        bitField0_ = (bitField0_ & ~0x00002000);
        rebuildSpeed_ = 0;
        onChanged();
        return this;
      }

      private int rebuildNum_ ;
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @return Whether the rebuildNum field is set.
       */
      @java.lang.Override
      public boolean hasRebuildNum() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @return The rebuildNum.
       */
      @java.lang.Override
      public int getRebuildNum() {
        return rebuildNum_;
      }
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @param value The rebuildNum to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildNum(int value) {
        bitField0_ |= 0x00004000;
        rebuildNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildNum() {
        bitField0_ = (bitField0_ & ~0x00004000);
        rebuildNum_ = 0;
        onChanged();
        return this;
      }

      private long fileNumCalcTsMs_ ;
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @return Whether the fileNumCalcTsMs field is set.
       */
      @java.lang.Override
      public boolean hasFileNumCalcTsMs() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @return The fileNumCalcTsMs.
       */
      @java.lang.Override
      public long getFileNumCalcTsMs() {
        return fileNumCalcTsMs_;
      }
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @param value The fileNumCalcTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setFileNumCalcTsMs(long value) {
        bitField0_ |= 0x00008000;
        fileNumCalcTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileNumCalcTsMs() {
        bitField0_ = (bitField0_ & ~0x00008000);
        fileNumCalcTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int alreadyFireNum_ ;
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @return Whether the alreadyFireNum field is set.
       */
      @java.lang.Override
      public boolean hasAlreadyFireNum() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @return The alreadyFireNum.
       */
      @java.lang.Override
      public int getAlreadyFireNum() {
        return alreadyFireNum_;
      }
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @param value The alreadyFireNum to set.
       * @return This builder for chaining.
       */
      public Builder setAlreadyFireNum(int value) {
        bitField0_ |= 0x00010000;
        alreadyFireNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlreadyFireNum() {
        bitField0_ = (bitField0_ & ~0x00010000);
        alreadyFireNum_ = 0;
        onChanged();
        return this;
      }

      private boolean wouldOverBurn_ ;
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @return Whether the wouldOverBurn field is set.
       */
      @java.lang.Override
      public boolean hasWouldOverBurn() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @return The wouldOverBurn.
       */
      @java.lang.Override
      public boolean getWouldOverBurn() {
        return wouldOverBurn_;
      }
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @param value The wouldOverBurn to set.
       * @return This builder for chaining.
       */
      public Builder setWouldOverBurn(boolean value) {
        bitField0_ |= 0x00020000;
        wouldOverBurn_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearWouldOverBurn() {
        bitField0_ = (bitField0_ & ~0x00020000);
        wouldOverBurn_ = false;
        onChanged();
        return this;
      }

      private int rebuildTotalWork_ ;
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @return Whether the rebuildTotalWork field is set.
       */
      @java.lang.Override
      public boolean hasRebuildTotalWork() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @return The rebuildTotalWork.
       */
      @java.lang.Override
      public int getRebuildTotalWork() {
        return rebuildTotalWork_;
      }
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @param value The rebuildTotalWork to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildTotalWork(int value) {
        bitField0_ |= 0x00040000;
        rebuildTotalWork_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildTotalWork() {
        bitField0_ = (bitField0_ & ~0x00040000);
        rebuildTotalWork_ = 0;
        onChanged();
        return this;
      }

      private long mustBurnEndTsMs_ ;
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @return Whether the mustBurnEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasMustBurnEndTsMs() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @return The mustBurnEndTsMs.
       */
      @java.lang.Override
      public long getMustBurnEndTsMs() {
        return mustBurnEndTsMs_;
      }
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @param value The mustBurnEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setMustBurnEndTsMs(long value) {
        bitField0_ |= 0x00080000;
        mustBurnEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearMustBurnEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00080000);
        mustBurnEndTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int flagColor_ ;
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @return Whether the flagColor field is set.
       */
      @java.lang.Override
      public boolean hasFlagColor() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @return The flagColor.
       */
      @java.lang.Override
      public int getFlagColor() {
        return flagColor_;
      }
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @param value The flagColor to set.
       * @return This builder for chaining.
       */
      public Builder setFlagColor(int value) {
        bitField0_ |= 0x00100000;
        flagColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlagColor() {
        bitField0_ = (bitField0_ & ~0x00100000);
        flagColor_ = 0;
        onChanged();
        return this;
      }

      private int flagShading_ ;
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @return Whether the flagShading field is set.
       */
      @java.lang.Override
      public boolean hasFlagShading() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @return The flagShading.
       */
      @java.lang.Override
      public int getFlagShading() {
        return flagShading_;
      }
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @param value The flagShading to set.
       * @return This builder for chaining.
       */
      public Builder setFlagShading(int value) {
        bitField0_ |= 0x00200000;
        flagShading_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlagShading() {
        bitField0_ = (bitField0_ & ~0x00200000);
        flagShading_ = 0;
        onChanged();
        return this;
      }

      private int flagSign_ ;
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @return Whether the flagSign field is set.
       */
      @java.lang.Override
      public boolean hasFlagSign() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @return The flagSign.
       */
      @java.lang.Override
      public int getFlagSign() {
        return flagSign_;
      }
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @param value The flagSign to set.
       * @return This builder for chaining.
       */
      public Builder setFlagSign(int value) {
        bitField0_ |= 0x00400000;
        flagSign_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlagSign() {
        bitField0_ = (bitField0_ & ~0x00400000);
        flagSign_ = 0;
        onChanged();
        return this;
      }

      private int fireSpeed_ ;
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @return Whether the fireSpeed field is set.
       */
      @java.lang.Override
      public boolean hasFireSpeed() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @return The fireSpeed.
       */
      @java.lang.Override
      public int getFireSpeed() {
        return fireSpeed_;
      }
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @param value The fireSpeed to set.
       * @return This builder for chaining.
       */
      public Builder setFireSpeed(int value) {
        bitField0_ |= 0x00800000;
        fireSpeed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearFireSpeed() {
        bitField0_ = (bitField0_ & ~0x00800000);
        fireSpeed_ = 0;
        onChanged();
        return this;
      }

      private long triggerFireClanId_ ;
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @return Whether the triggerFireClanId field is set.
       */
      @java.lang.Override
      public boolean hasTriggerFireClanId() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @return The triggerFireClanId.
       */
      @java.lang.Override
      public long getTriggerFireClanId() {
        return triggerFireClanId_;
      }
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @param value The triggerFireClanId to set.
       * @return This builder for chaining.
       */
      public Builder setTriggerFireClanId(long value) {
        bitField0_ |= 0x01000000;
        triggerFireClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearTriggerFireClanId() {
        bitField0_ = (bitField0_ & ~0x01000000);
        triggerFireClanId_ = 0L;
        onChanged();
        return this;
      }

      private int nationFlagId_ ;
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @return Whether the nationFlagId field is set.
       */
      @java.lang.Override
      public boolean hasNationFlagId() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @return The nationFlagId.
       */
      @java.lang.Override
      public int getNationFlagId() {
        return nationFlagId_;
      }
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @param value The nationFlagId to set.
       * @return This builder for chaining.
       */
      public Builder setNationFlagId(int value) {
        bitField0_ |= 0x02000000;
        nationFlagId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearNationFlagId() {
        bitField0_ = (bitField0_ & ~0x02000000);
        nationFlagId_ = 0;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x04000000;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x04000000);
        zoneId_ = 0;
        onChanged();
        return this;
      }

      private int zoneColor_ ;
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @return Whether the zoneColor field is set.
       */
      @java.lang.Override
      public boolean hasZoneColor() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @return The zoneColor.
       */
      @java.lang.Override
      public int getZoneColor() {
        return zoneColor_;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @param value The zoneColor to set.
       * @return This builder for chaining.
       */
      public Builder setZoneColor(int value) {
        bitField0_ |= 0x08000000;
        zoneColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneColor() {
        bitField0_ = (bitField0_ & ~0x08000000);
        zoneColor_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OccupyInfoPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OccupyInfoPB)
    private static final com.yorha.proto.MapBuildingPB.OccupyInfoPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuildingPB.OccupyInfoPB();
    }

    public static com.yorha.proto.MapBuildingPB.OccupyInfoPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OccupyInfoPB>
        PARSER = new com.google.protobuf.AbstractParser<OccupyInfoPB>() {
      @java.lang.Override
      public OccupyInfoPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OccupyInfoPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OccupyInfoPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OccupyInfoPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.OccupyInfoPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConstructInfoPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ConstructInfoPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.MapBuildingType getType();

    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return Whether the currentDurability field is set.
     */
    boolean hasCurrentDurability();
    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return The currentDurability.
     */
    int getCurrentDurability();

    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return Whether the maxDurability field is set.
     */
    boolean hasMaxDurability();
    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return The maxDurability.
     */
    int getMaxDurability();

    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return Whether the isOnFire field is set.
     */
    boolean hasIsOnFire();
    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return The isOnFire.
     */
    boolean getIsOnFire();

    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return Whether the isConnectedToCommandNet field is set.
     */
    boolean hasIsConnectedToCommandNet();
    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return The isConnectedToCommandNet.
     */
    boolean getIsConnectedToCommandNet();

    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return Whether the beforeRebuildTemplateId field is set.
     */
    boolean hasBeforeRebuildTemplateId();
    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return The beforeRebuildTemplateId.
     */
    int getBeforeRebuildTemplateId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ConstructInfoPB}
   */
  public static final class ConstructInfoPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ConstructInfoPB)
      ConstructInfoPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConstructInfoPB.newBuilder() to construct.
    private ConstructInfoPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConstructInfoPB() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConstructInfoPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ConstructInfoPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapBuildingType value = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              currentDurability_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              maxDurability_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              isOnFire_ = input.readBool();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              isConnectedToCommandNet_ = input.readBool();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              beforeRebuildTemplateId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_ConstructInfoPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_ConstructInfoPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuildingPB.ConstructInfoPB.class, com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapBuildingType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
    }

    public static final int CURRENTDURABILITY_FIELD_NUMBER = 2;
    private int currentDurability_;
    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return Whether the currentDurability field is set.
     */
    @java.lang.Override
    public boolean hasCurrentDurability() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return The currentDurability.
     */
    @java.lang.Override
    public int getCurrentDurability() {
      return currentDurability_;
    }

    public static final int MAXDURABILITY_FIELD_NUMBER = 3;
    private int maxDurability_;
    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return Whether the maxDurability field is set.
     */
    @java.lang.Override
    public boolean hasMaxDurability() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return The maxDurability.
     */
    @java.lang.Override
    public int getMaxDurability() {
      return maxDurability_;
    }

    public static final int ISONFIRE_FIELD_NUMBER = 4;
    private boolean isOnFire_;
    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return Whether the isOnFire field is set.
     */
    @java.lang.Override
    public boolean hasIsOnFire() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return The isOnFire.
     */
    @java.lang.Override
    public boolean getIsOnFire() {
      return isOnFire_;
    }

    public static final int ISCONNECTEDTOCOMMANDNET_FIELD_NUMBER = 5;
    private boolean isConnectedToCommandNet_;
    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return Whether the isConnectedToCommandNet field is set.
     */
    @java.lang.Override
    public boolean hasIsConnectedToCommandNet() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return The isConnectedToCommandNet.
     */
    @java.lang.Override
    public boolean getIsConnectedToCommandNet() {
      return isConnectedToCommandNet_;
    }

    public static final int BEFOREREBUILDTEMPLATEID_FIELD_NUMBER = 6;
    private int beforeRebuildTemplateId_;
    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return Whether the beforeRebuildTemplateId field is set.
     */
    @java.lang.Override
    public boolean hasBeforeRebuildTemplateId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return The beforeRebuildTemplateId.
     */
    @java.lang.Override
    public int getBeforeRebuildTemplateId() {
      return beforeRebuildTemplateId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, currentDurability_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, maxDurability_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, isOnFire_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(5, isConnectedToCommandNet_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, beforeRebuildTemplateId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, currentDurability_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, maxDurability_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, isOnFire_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, isConnectedToCommandNet_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, beforeRebuildTemplateId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuildingPB.ConstructInfoPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuildingPB.ConstructInfoPB other = (com.yorha.proto.MapBuildingPB.ConstructInfoPB) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasCurrentDurability() != other.hasCurrentDurability()) return false;
      if (hasCurrentDurability()) {
        if (getCurrentDurability()
            != other.getCurrentDurability()) return false;
      }
      if (hasMaxDurability() != other.hasMaxDurability()) return false;
      if (hasMaxDurability()) {
        if (getMaxDurability()
            != other.getMaxDurability()) return false;
      }
      if (hasIsOnFire() != other.hasIsOnFire()) return false;
      if (hasIsOnFire()) {
        if (getIsOnFire()
            != other.getIsOnFire()) return false;
      }
      if (hasIsConnectedToCommandNet() != other.hasIsConnectedToCommandNet()) return false;
      if (hasIsConnectedToCommandNet()) {
        if (getIsConnectedToCommandNet()
            != other.getIsConnectedToCommandNet()) return false;
      }
      if (hasBeforeRebuildTemplateId() != other.hasBeforeRebuildTemplateId()) return false;
      if (hasBeforeRebuildTemplateId()) {
        if (getBeforeRebuildTemplateId()
            != other.getBeforeRebuildTemplateId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasCurrentDurability()) {
        hash = (37 * hash) + CURRENTDURABILITY_FIELD_NUMBER;
        hash = (53 * hash) + getCurrentDurability();
      }
      if (hasMaxDurability()) {
        hash = (37 * hash) + MAXDURABILITY_FIELD_NUMBER;
        hash = (53 * hash) + getMaxDurability();
      }
      if (hasIsOnFire()) {
        hash = (37 * hash) + ISONFIRE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsOnFire());
      }
      if (hasIsConnectedToCommandNet()) {
        hash = (37 * hash) + ISCONNECTEDTOCOMMANDNET_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsConnectedToCommandNet());
      }
      if (hasBeforeRebuildTemplateId()) {
        hash = (37 * hash) + BEFOREREBUILDTEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeRebuildTemplateId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuildingPB.ConstructInfoPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ConstructInfoPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ConstructInfoPB)
        com.yorha.proto.MapBuildingPB.ConstructInfoPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_ConstructInfoPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_ConstructInfoPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuildingPB.ConstructInfoPB.class, com.yorha.proto.MapBuildingPB.ConstructInfoPB.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuildingPB.ConstructInfoPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        currentDurability_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        maxDurability_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        isOnFire_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        isConnectedToCommandNet_ = false;
        bitField0_ = (bitField0_ & ~0x00000010);
        beforeRebuildTemplateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_ConstructInfoPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.ConstructInfoPB getDefaultInstanceForType() {
        return com.yorha.proto.MapBuildingPB.ConstructInfoPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.ConstructInfoPB build() {
        com.yorha.proto.MapBuildingPB.ConstructInfoPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.ConstructInfoPB buildPartial() {
        com.yorha.proto.MapBuildingPB.ConstructInfoPB result = new com.yorha.proto.MapBuildingPB.ConstructInfoPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.currentDurability_ = currentDurability_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.maxDurability_ = maxDurability_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.isOnFire_ = isOnFire_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.isConnectedToCommandNet_ = isConnectedToCommandNet_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.beforeRebuildTemplateId_ = beforeRebuildTemplateId_;
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuildingPB.ConstructInfoPB) {
          return mergeFrom((com.yorha.proto.MapBuildingPB.ConstructInfoPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuildingPB.ConstructInfoPB other) {
        if (other == com.yorha.proto.MapBuildingPB.ConstructInfoPB.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasCurrentDurability()) {
          setCurrentDurability(other.getCurrentDurability());
        }
        if (other.hasMaxDurability()) {
          setMaxDurability(other.getMaxDurability());
        }
        if (other.hasIsOnFire()) {
          setIsOnFire(other.getIsOnFire());
        }
        if (other.hasIsConnectedToCommandNet()) {
          setIsConnectedToCommandNet(other.getIsConnectedToCommandNet());
        }
        if (other.hasBeforeRebuildTemplateId()) {
          setBeforeRebuildTemplateId(other.getBeforeRebuildTemplateId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuildingPB.ConstructInfoPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuildingPB.ConstructInfoPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapBuildingType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
      }
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.MapBuildingType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int currentDurability_ ;
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @return Whether the currentDurability field is set.
       */
      @java.lang.Override
      public boolean hasCurrentDurability() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @return The currentDurability.
       */
      @java.lang.Override
      public int getCurrentDurability() {
        return currentDurability_;
      }
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @param value The currentDurability to set.
       * @return This builder for chaining.
       */
      public Builder setCurrentDurability(int value) {
        bitField0_ |= 0x00000002;
        currentDurability_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrentDurability() {
        bitField0_ = (bitField0_ & ~0x00000002);
        currentDurability_ = 0;
        onChanged();
        return this;
      }

      private int maxDurability_ ;
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @return Whether the maxDurability field is set.
       */
      @java.lang.Override
      public boolean hasMaxDurability() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @return The maxDurability.
       */
      @java.lang.Override
      public int getMaxDurability() {
        return maxDurability_;
      }
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @param value The maxDurability to set.
       * @return This builder for chaining.
       */
      public Builder setMaxDurability(int value) {
        bitField0_ |= 0x00000004;
        maxDurability_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxDurability() {
        bitField0_ = (bitField0_ & ~0x00000004);
        maxDurability_ = 0;
        onChanged();
        return this;
      }

      private boolean isOnFire_ ;
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @return Whether the isOnFire field is set.
       */
      @java.lang.Override
      public boolean hasIsOnFire() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @return The isOnFire.
       */
      @java.lang.Override
      public boolean getIsOnFire() {
        return isOnFire_;
      }
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @param value The isOnFire to set.
       * @return This builder for chaining.
       */
      public Builder setIsOnFire(boolean value) {
        bitField0_ |= 0x00000008;
        isOnFire_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsOnFire() {
        bitField0_ = (bitField0_ & ~0x00000008);
        isOnFire_ = false;
        onChanged();
        return this;
      }

      private boolean isConnectedToCommandNet_ ;
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @return Whether the isConnectedToCommandNet field is set.
       */
      @java.lang.Override
      public boolean hasIsConnectedToCommandNet() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @return The isConnectedToCommandNet.
       */
      @java.lang.Override
      public boolean getIsConnectedToCommandNet() {
        return isConnectedToCommandNet_;
      }
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @param value The isConnectedToCommandNet to set.
       * @return This builder for chaining.
       */
      public Builder setIsConnectedToCommandNet(boolean value) {
        bitField0_ |= 0x00000010;
        isConnectedToCommandNet_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsConnectedToCommandNet() {
        bitField0_ = (bitField0_ & ~0x00000010);
        isConnectedToCommandNet_ = false;
        onChanged();
        return this;
      }

      private int beforeRebuildTemplateId_ ;
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @return Whether the beforeRebuildTemplateId field is set.
       */
      @java.lang.Override
      public boolean hasBeforeRebuildTemplateId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @return The beforeRebuildTemplateId.
       */
      @java.lang.Override
      public int getBeforeRebuildTemplateId() {
        return beforeRebuildTemplateId_;
      }
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @param value The beforeRebuildTemplateId to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeRebuildTemplateId(int value) {
        bitField0_ |= 0x00000020;
        beforeRebuildTemplateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeRebuildTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        beforeRebuildTemplateId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ConstructInfoPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ConstructInfoPB)
    private static final com.yorha.proto.MapBuildingPB.ConstructInfoPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuildingPB.ConstructInfoPB();
    }

    public static com.yorha.proto.MapBuildingPB.ConstructInfoPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ConstructInfoPB>
        PARSER = new com.google.protobuf.AbstractParser<ConstructInfoPB>() {
      @java.lang.Override
      public ConstructInfoPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ConstructInfoPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ConstructInfoPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConstructInfoPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.ConstructInfoPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MapBuildingKingdomModelPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MapBuildingKingdomModelPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
     * @return Whether the kingCardHead field is set.
     */
    boolean hasKingCardHead();
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
     * @return The kingCardHead.
     */
    com.yorha.proto.StructPB.PlayerCardHeadPB getKingCardHead();
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
     */
    com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getKingCardHeadOrBuilder();

    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return Whether the selectedColor field is set.
     */
    boolean hasSelectedColor();
    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return The selectedColor.
     */
    int getSelectedColor();

    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return Whether the destroyZoneId field is set.
     */
    boolean hasDestroyZoneId();
    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return The destroyZoneId.
     */
    int getDestroyZoneId();

    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return Whether the destroyClanName field is set.
     */
    boolean hasDestroyClanName();
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The destroyClanName.
     */
    java.lang.String getDestroyClanName();
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The bytes for destroyClanName.
     */
    com.google.protobuf.ByteString
        getDestroyClanNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MapBuildingKingdomModelPB}
   */
  public static final class MapBuildingKingdomModelPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MapBuildingKingdomModelPB)
      MapBuildingKingdomModelPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MapBuildingKingdomModelPB.newBuilder() to construct.
    private MapBuildingKingdomModelPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MapBuildingKingdomModelPB() {
      destroyClanName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MapBuildingKingdomModelPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MapBuildingKingdomModelPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.PlayerCardHeadPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = kingCardHead_.toBuilder();
              }
              kingCardHead_ = input.readMessage(com.yorha.proto.StructPB.PlayerCardHeadPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(kingCardHead_);
                kingCardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              selectedColor_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              destroyZoneId_ = input.readInt32();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              destroyClanName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingKingdomModelPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingKingdomModelPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.class, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder.class);
    }

    private int bitField0_;
    public static final int KINGCARDHEAD_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.PlayerCardHeadPB kingCardHead_;
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
     * @return Whether the kingCardHead field is set.
     */
    @java.lang.Override
    public boolean hasKingCardHead() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
     * @return The kingCardHead.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPB getKingCardHead() {
      return kingCardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : kingCardHead_;
    }
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getKingCardHeadOrBuilder() {
      return kingCardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : kingCardHead_;
    }

    public static final int SELECTEDCOLOR_FIELD_NUMBER = 3;
    private int selectedColor_;
    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return Whether the selectedColor field is set.
     */
    @java.lang.Override
    public boolean hasSelectedColor() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return The selectedColor.
     */
    @java.lang.Override
    public int getSelectedColor() {
      return selectedColor_;
    }

    public static final int DESTROYZONEID_FIELD_NUMBER = 4;
    private int destroyZoneId_;
    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return Whether the destroyZoneId field is set.
     */
    @java.lang.Override
    public boolean hasDestroyZoneId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return The destroyZoneId.
     */
    @java.lang.Override
    public int getDestroyZoneId() {
      return destroyZoneId_;
    }

    public static final int DESTROYCLANNAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object destroyClanName_;
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return Whether the destroyClanName field is set.
     */
    @java.lang.Override
    public boolean hasDestroyClanName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The destroyClanName.
     */
    @java.lang.Override
    public java.lang.String getDestroyClanName() {
      java.lang.Object ref = destroyClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          destroyClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The bytes for destroyClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDestroyClanNameBytes() {
      java.lang.Object ref = destroyClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        destroyClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getKingCardHead());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, selectedColor_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(4, destroyZoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, destroyClanName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getKingCardHead());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, selectedColor_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, destroyZoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, destroyClanName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB other = (com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB) obj;

      if (hasKingCardHead() != other.hasKingCardHead()) return false;
      if (hasKingCardHead()) {
        if (!getKingCardHead()
            .equals(other.getKingCardHead())) return false;
      }
      if (hasSelectedColor() != other.hasSelectedColor()) return false;
      if (hasSelectedColor()) {
        if (getSelectedColor()
            != other.getSelectedColor()) return false;
      }
      if (hasDestroyZoneId() != other.hasDestroyZoneId()) return false;
      if (hasDestroyZoneId()) {
        if (getDestroyZoneId()
            != other.getDestroyZoneId()) return false;
      }
      if (hasDestroyClanName() != other.hasDestroyClanName()) return false;
      if (hasDestroyClanName()) {
        if (!getDestroyClanName()
            .equals(other.getDestroyClanName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingCardHead()) {
        hash = (37 * hash) + KINGCARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getKingCardHead().hashCode();
      }
      if (hasSelectedColor()) {
        hash = (37 * hash) + SELECTEDCOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getSelectedColor();
      }
      if (hasDestroyZoneId()) {
        hash = (37 * hash) + DESTROYZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getDestroyZoneId();
      }
      if (hasDestroyClanName()) {
        hash = (37 * hash) + DESTROYCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getDestroyClanName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MapBuildingKingdomModelPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MapBuildingKingdomModelPB)
        com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingKingdomModelPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingKingdomModelPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.class, com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getKingCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (kingCardHeadBuilder_ == null) {
          kingCardHead_ = null;
        } else {
          kingCardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        selectedColor_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        destroyZoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        destroyClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuildingPB.internal_static_com_yorha_proto_MapBuildingKingdomModelPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB getDefaultInstanceForType() {
        return com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB build() {
        com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB buildPartial() {
        com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB result = new com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (kingCardHeadBuilder_ == null) {
            result.kingCardHead_ = kingCardHead_;
          } else {
            result.kingCardHead_ = kingCardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.selectedColor_ = selectedColor_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.destroyZoneId_ = destroyZoneId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.destroyClanName_ = destroyClanName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB) {
          return mergeFrom((com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB other) {
        if (other == com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB.getDefaultInstance()) return this;
        if (other.hasKingCardHead()) {
          mergeKingCardHead(other.getKingCardHead());
        }
        if (other.hasSelectedColor()) {
          setSelectedColor(other.getSelectedColor());
        }
        if (other.hasDestroyZoneId()) {
          setDestroyZoneId(other.getDestroyZoneId());
        }
        if (other.hasDestroyClanName()) {
          bitField0_ |= 0x00000008;
          destroyClanName_ = other.destroyClanName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.PlayerCardHeadPB kingCardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> kingCardHeadBuilder_;
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       * @return Whether the kingCardHead field is set.
       */
      public boolean hasKingCardHead() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       * @return The kingCardHead.
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB getKingCardHead() {
        if (kingCardHeadBuilder_ == null) {
          return kingCardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : kingCardHead_;
        } else {
          return kingCardHeadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       */
      public Builder setKingCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (kingCardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          kingCardHead_ = value;
          onChanged();
        } else {
          kingCardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       */
      public Builder setKingCardHead(
          com.yorha.proto.StructPB.PlayerCardHeadPB.Builder builderForValue) {
        if (kingCardHeadBuilder_ == null) {
          kingCardHead_ = builderForValue.build();
          onChanged();
        } else {
          kingCardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       */
      public Builder mergeKingCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (kingCardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              kingCardHead_ != null &&
              kingCardHead_ != com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance()) {
            kingCardHead_ =
              com.yorha.proto.StructPB.PlayerCardHeadPB.newBuilder(kingCardHead_).mergeFrom(value).buildPartial();
          } else {
            kingCardHead_ = value;
          }
          onChanged();
        } else {
          kingCardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       */
      public Builder clearKingCardHead() {
        if (kingCardHeadBuilder_ == null) {
          kingCardHead_ = null;
          onChanged();
        } else {
          kingCardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB.Builder getKingCardHeadBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getKingCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getKingCardHeadOrBuilder() {
        if (kingCardHeadBuilder_ != null) {
          return kingCardHeadBuilder_.getMessageOrBuilder();
        } else {
          return kingCardHead_ == null ?
              com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : kingCardHead_;
        }
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB kingCardHead = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> 
          getKingCardHeadFieldBuilder() {
        if (kingCardHeadBuilder_ == null) {
          kingCardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder>(
                  getKingCardHead(),
                  getParentForChildren(),
                  isClean());
          kingCardHead_ = null;
        }
        return kingCardHeadBuilder_;
      }

      private int selectedColor_ ;
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @return Whether the selectedColor field is set.
       */
      @java.lang.Override
      public boolean hasSelectedColor() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @return The selectedColor.
       */
      @java.lang.Override
      public int getSelectedColor() {
        return selectedColor_;
      }
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @param value The selectedColor to set.
       * @return This builder for chaining.
       */
      public Builder setSelectedColor(int value) {
        bitField0_ |= 0x00000002;
        selectedColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSelectedColor() {
        bitField0_ = (bitField0_ & ~0x00000002);
        selectedColor_ = 0;
        onChanged();
        return this;
      }

      private int destroyZoneId_ ;
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @return Whether the destroyZoneId field is set.
       */
      @java.lang.Override
      public boolean hasDestroyZoneId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @return The destroyZoneId.
       */
      @java.lang.Override
      public int getDestroyZoneId() {
        return destroyZoneId_;
      }
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @param value The destroyZoneId to set.
       * @return This builder for chaining.
       */
      public Builder setDestroyZoneId(int value) {
        bitField0_ |= 0x00000004;
        destroyZoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDestroyZoneId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        destroyZoneId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object destroyClanName_ = "";
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return Whether the destroyClanName field is set.
       */
      public boolean hasDestroyClanName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return The destroyClanName.
       */
      public java.lang.String getDestroyClanName() {
        java.lang.Object ref = destroyClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            destroyClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return The bytes for destroyClanName.
       */
      public com.google.protobuf.ByteString
          getDestroyClanNameBytes() {
        java.lang.Object ref = destroyClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          destroyClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @param value The destroyClanName to set.
       * @return This builder for chaining.
       */
      public Builder setDestroyClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        destroyClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDestroyClanName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        destroyClanName_ = getDefaultInstance().getDestroyClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @param value The bytes for destroyClanName to set.
       * @return This builder for chaining.
       */
      public Builder setDestroyClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        destroyClanName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MapBuildingKingdomModelPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MapBuildingKingdomModelPB)
    private static final com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB();
    }

    public static com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MapBuildingKingdomModelPB>
        PARSER = new com.google.protobuf.AbstractParser<MapBuildingKingdomModelPB>() {
      @java.lang.Override
      public MapBuildingKingdomModelPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MapBuildingKingdomModelPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MapBuildingKingdomModelPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MapBuildingKingdomModelPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuildingPB.MapBuildingKingdomModelPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MapBuildingEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MapBuildingEntityPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OccupyInfoPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OccupyInfoPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ConstructInfoPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ConstructInfoPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MapBuildingKingdomModelPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MapBuildingKingdomModelPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-cs_proto/gen/mapBuilding/map_buildingP" +
      "B.proto\022\017com.yorha.proto\032\036cs_proto/gen/c" +
      "nc/basicPB.proto\032\"cs_proto/gen/common/st" +
      "ructPB.proto\032)cs_proto/gen/common/struct" +
      "_battlePB.proto\032)cs_proto/gen/common/str" +
      "uct_playerPB.proto\032%ss_proto/gen/common/" +
      "common_enum.proto\"\305\005\n\023MapBuildingEntityP" +
      "B\022\'\n\005point\030\001 \001(\0132\030.com.yorha.proto.Point" +
      "PB\022\016\n\006partId\030\002 \001(\005\022\022\n\ntemplateId\030\003 \001(\005\0221" +
      "\n\noccupyinfo\030\005 \001(\0132\035.com.yorha.proto.Occ" +
      "upyInfoPB\022\'\n\005troop\030\006 \001(\0132\030.com.yorha.pro" +
      "to.TroopPB\022)\n\006battle\030\007 \001(\0132\031.com.yorha.p" +
      "roto.BattlePB\022+\n\007buffSys\030\010 \001(\0132\032.com.yor" +
      "ha.proto.BuffSysPB\0227\n\rconstructInfo\030\t \001(" +
      "\0132 .com.yorha.proto.ConstructInfoPB\0226\n\nd" +
      "evBuffSys\030\013 \001(\0132\".com.yorha.proto.SceneD" +
      "evBuffSysPB\0226\n\tsafeGuard\030\014 \001(\0132#.com.yor" +
      "ha.proto.SpecialSafeGuardPB\022>\n\030recommend" +
      "SoldierTypeList\030\r \001(\0132\034.com.yorha.proto." +
      "Int32ListPB\022\026\n\016passingArmyNum\030\016 \001(\005\0221\n\ne" +
      "xpression\030\017 \001(\0132\035.com.yorha.proto.Expres" +
      "sionPB\022@\n\014kingdomModel\030\020 \001(\0132*.com.yorha" +
      ".proto.MapBuildingKingdomModelPB\0227\n\005arro" +
      "w\030\027 \001(\0132(.com.yorha.proto.Int64ArmyArrow" +
      "ItemMapPB\"\224\005\n\014OccupyInfoPB\022+\n\005state\030\001 \001(" +
      "\0162\034.com.yorha.proto.OccupyState\022\026\n\016state" +
      "StartTsMs\030\002 \001(\003\022\024\n\014stateEndTsMs\030\003 \001(\003\022\023\n" +
      "\013ownerClanId\030\004 \001(\003\022\032\n\022showClanSimpleName" +
      "\030\005 \001(\t\022\024\n\014showClanName\030\006 \001(\t\022\021\n\toccupyNu" +
      "m\030\010 \001(\005\022\031\n\021occupyNumCalcTsMs\030\n \001(\003\022\023\n\013oc" +
      "cupySpeed\030\013 \001(\005\022\024\n\014occupyClanId\030\014 \001(\003\022\021\n" +
      "\tshowColor\030\r \001(\005\022\024\n\014fisrtOwnTsMs\030\016 \001(\003\022\032" +
      "\n\022rebuildNumCalcTsMs\030\017 \001(\003\022\024\n\014rebuildSpe" +
      "ed\030\020 \001(\005\022\022\n\nrebuildNum\030\021 \001(\005\022\027\n\017fileNumC" +
      "alcTsMs\030\022 \001(\003\022\026\n\016alreadyFireNum\030\023 \001(\005\022\025\n" +
      "\rwouldOverBurn\030\024 \001(\010\022\030\n\020rebuildTotalWork" +
      "\030\025 \001(\005\022\027\n\017mustBurnEndTsMs\030\026 \001(\003\022\021\n\tflagC" +
      "olor\030\027 \001(\005\022\023\n\013flagShading\030\030 \001(\005\022\020\n\010flagS" +
      "ign\030\031 \001(\005\022\021\n\tfireSpeed\030\033 \001(\005\022\031\n\021triggerF" +
      "ireClanId\030\034 \001(\003\022\024\n\014nationFlagId\030\035 \001(\005\022\016\n" +
      "\006zoneId\030\036 \001(\005\022\021\n\tzoneColor\030\037 \001(\005\"\307\001\n\017Con" +
      "structInfoPB\022.\n\004type\030\001 \001(\0162 .com.yorha.p" +
      "roto.MapBuildingType\022\031\n\021currentDurabilit" +
      "y\030\002 \001(\005\022\025\n\rmaxDurability\030\003 \001(\005\022\020\n\010isOnFi" +
      "re\030\004 \001(\010\022\037\n\027isConnectedToCommandNet\030\005 \001(" +
      "\010\022\037\n\027beforeRebuildTemplateId\030\006 \001(\005\"\233\001\n\031M" +
      "apBuildingKingdomModelPB\0227\n\014kingCardHead" +
      "\030\001 \001(\0132!.com.yorha.proto.PlayerCardHeadP" +
      "B\022\025\n\rselectedColor\030\003 \001(\005\022\025\n\rdestroyZoneI" +
      "d\030\004 \001(\005\022\027\n\017destroyClanName\030\005 \001(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.BasicPB.getDescriptor(),
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.StructBattlePB.getDescriptor(),
          com.yorha.proto.StructPlayerPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_MapBuildingEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_MapBuildingEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MapBuildingEntityPB_descriptor,
        new java.lang.String[] { "Point", "PartId", "TemplateId", "Occupyinfo", "Troop", "Battle", "BuffSys", "ConstructInfo", "DevBuffSys", "SafeGuard", "RecommendSoldierTypeList", "PassingArmyNum", "Expression", "KingdomModel", "Arrow", });
    internal_static_com_yorha_proto_OccupyInfoPB_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_OccupyInfoPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OccupyInfoPB_descriptor,
        new java.lang.String[] { "State", "StateStartTsMs", "StateEndTsMs", "OwnerClanId", "ShowClanSimpleName", "ShowClanName", "OccupyNum", "OccupyNumCalcTsMs", "OccupySpeed", "OccupyClanId", "ShowColor", "FisrtOwnTsMs", "RebuildNumCalcTsMs", "RebuildSpeed", "RebuildNum", "FileNumCalcTsMs", "AlreadyFireNum", "WouldOverBurn", "RebuildTotalWork", "MustBurnEndTsMs", "FlagColor", "FlagShading", "FlagSign", "FireSpeed", "TriggerFireClanId", "NationFlagId", "ZoneId", "ZoneColor", });
    internal_static_com_yorha_proto_ConstructInfoPB_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ConstructInfoPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ConstructInfoPB_descriptor,
        new java.lang.String[] { "Type", "CurrentDurability", "MaxDurability", "IsOnFire", "IsConnectedToCommandNet", "BeforeRebuildTemplateId", });
    internal_static_com_yorha_proto_MapBuildingKingdomModelPB_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_MapBuildingKingdomModelPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MapBuildingKingdomModelPB_descriptor,
        new java.lang.String[] { "KingCardHead", "SelectedColor", "DestroyZoneId", "DestroyClanName", });
    com.yorha.proto.BasicPB.getDescriptor();
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.StructBattlePB.getDescriptor();
    com.yorha.proto.StructPlayerPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

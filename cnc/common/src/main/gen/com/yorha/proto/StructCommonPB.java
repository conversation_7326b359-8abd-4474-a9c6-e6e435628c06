// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/common/struct_commonPB.proto

package com.yorha.proto;

public final class StructCommonPB {
  private StructCommonPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ProgressInfoPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ProgressInfoPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return Whether the uid field is set.
     */
    boolean hasUid();
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return The uid.
     */
    long getUid();

    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return Whether the lastCalNum field is set.
     */
    boolean hasLastCalNum();
    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return The lastCalNum.
     */
    long getLastCalNum();

    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return Whether the maxNum field is set.
     */
    boolean hasMaxNum();
    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return The maxNum.
     */
    long getMaxNum();

    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return Whether the lastCalTsMs field is set.
     */
    boolean hasLastCalTsMs();
    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return The lastCalTsMs.
     */
    long getLastCalTsMs();

    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return Whether the speed field is set.
     */
    boolean hasSpeed();
    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return The speed.
     */
    long getSpeed();

    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    boolean hasStateEndTsMs();
    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return The stateEndTsMs.
     */
    long getStateEndTsMs();

    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    boolean hasStateStartTsMs();
    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return The stateStartTsMs.
     */
    long getStateStartTsMs();
  }
  /**
   * <pre>
   * 进度条数据结构，有军队进出会主动结算，客户端仅需拿到上次结算值和速度即可显示假的结算值
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.ProgressInfoPB}
   */
  public static final class ProgressInfoPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ProgressInfoPB)
      ProgressInfoPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ProgressInfoPB.newBuilder() to construct.
    private ProgressInfoPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ProgressInfoPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ProgressInfoPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ProgressInfoPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              uid_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              lastCalNum_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              maxNum_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              lastCalTsMs_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              speed_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              stateEndTsMs_ = input.readInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              stateStartTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_ProgressInfoPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_ProgressInfoPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.ProgressInfoPB.class, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder.class);
    }

    private int bitField0_;
    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return Whether the uid field is set.
     */
    @java.lang.Override
    public boolean hasUid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 uid = 1;</code>
     * @return The uid.
     */
    @java.lang.Override
    public long getUid() {
      return uid_;
    }

    public static final int LASTCALNUM_FIELD_NUMBER = 2;
    private long lastCalNum_;
    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return Whether the lastCalNum field is set.
     */
    @java.lang.Override
    public boolean hasLastCalNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 lastCalNum = 2;</code>
     * @return The lastCalNum.
     */
    @java.lang.Override
    public long getLastCalNum() {
      return lastCalNum_;
    }

    public static final int MAXNUM_FIELD_NUMBER = 3;
    private long maxNum_;
    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return Whether the maxNum field is set.
     */
    @java.lang.Override
    public boolean hasMaxNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 总量，可以解释为总的资源量，建设量，占领量，耐久度
     * </pre>
     *
     * <code>optional int64 maxNum = 3;</code>
     * @return The maxNum.
     */
    @java.lang.Override
    public long getMaxNum() {
      return maxNum_;
    }

    public static final int LASTCALTSMS_FIELD_NUMBER = 4;
    private long lastCalTsMs_;
    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return Whether the lastCalTsMs field is set.
     */
    @java.lang.Override
    public boolean hasLastCalTsMs() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
     * </pre>
     *
     * <code>optional int64 lastCalTsMs = 4;</code>
     * @return The lastCalTsMs.
     */
    @java.lang.Override
    public long getLastCalTsMs() {
      return lastCalTsMs_;
    }

    public static final int SPEED_FIELD_NUMBER = 5;
    private long speed_;
    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return Whether the speed field is set.
     */
    @java.lang.Override
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
     * </pre>
     *
     * <code>optional int64 speed = 5;</code>
     * @return The speed.
     */
    @java.lang.Override
    public long getSpeed() {
      return speed_;
    }

    public static final int STATEENDTSMS_FIELD_NUMBER = 6;
    private long stateEndTsMs_;
    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateEndTsMs() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 6;</code>
     * @return The stateEndTsMs.
     */
    @java.lang.Override
    public long getStateEndTsMs() {
      return stateEndTsMs_;
    }

    public static final int STATESTARTTSMS_FIELD_NUMBER = 7;
    private long stateStartTsMs_;
    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateStartTsMs() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 7;</code>
     * @return The stateStartTsMs.
     */
    @java.lang.Override
    public long getStateStartTsMs() {
      return stateStartTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, uid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, lastCalNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, maxNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, lastCalTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, speed_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt64(6, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt64(7, stateStartTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, uid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, lastCalNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, maxNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, lastCalTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, speed_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, stateStartTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.ProgressInfoPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.ProgressInfoPB other = (com.yorha.proto.StructCommonPB.ProgressInfoPB) obj;

      if (hasUid() != other.hasUid()) return false;
      if (hasUid()) {
        if (getUid()
            != other.getUid()) return false;
      }
      if (hasLastCalNum() != other.hasLastCalNum()) return false;
      if (hasLastCalNum()) {
        if (getLastCalNum()
            != other.getLastCalNum()) return false;
      }
      if (hasMaxNum() != other.hasMaxNum()) return false;
      if (hasMaxNum()) {
        if (getMaxNum()
            != other.getMaxNum()) return false;
      }
      if (hasLastCalTsMs() != other.hasLastCalTsMs()) return false;
      if (hasLastCalTsMs()) {
        if (getLastCalTsMs()
            != other.getLastCalTsMs()) return false;
      }
      if (hasSpeed() != other.hasSpeed()) return false;
      if (hasSpeed()) {
        if (getSpeed()
            != other.getSpeed()) return false;
      }
      if (hasStateEndTsMs() != other.hasStateEndTsMs()) return false;
      if (hasStateEndTsMs()) {
        if (getStateEndTsMs()
            != other.getStateEndTsMs()) return false;
      }
      if (hasStateStartTsMs() != other.hasStateStartTsMs()) return false;
      if (hasStateStartTsMs()) {
        if (getStateStartTsMs()
            != other.getStateStartTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasUid()) {
        hash = (37 * hash) + UID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUid());
      }
      if (hasLastCalNum()) {
        hash = (37 * hash) + LASTCALNUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLastCalNum());
      }
      if (hasMaxNum()) {
        hash = (37 * hash) + MAXNUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMaxNum());
      }
      if (hasLastCalTsMs()) {
        hash = (37 * hash) + LASTCALTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLastCalTsMs());
      }
      if (hasSpeed()) {
        hash = (37 * hash) + SPEED_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSpeed());
      }
      if (hasStateEndTsMs()) {
        hash = (37 * hash) + STATEENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateEndTsMs());
      }
      if (hasStateStartTsMs()) {
        hash = (37 * hash) + STATESTARTTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateStartTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.ProgressInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.ProgressInfoPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 进度条数据结构，有军队进出会主动结算，客户端仅需拿到上次结算值和速度即可显示假的结算值
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.ProgressInfoPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ProgressInfoPB)
        com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_ProgressInfoPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_ProgressInfoPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.ProgressInfoPB.class, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.ProgressInfoPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        lastCalNum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        maxNum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        lastCalTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        speed_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        stateEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        stateStartTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_ProgressInfoPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.ProgressInfoPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.ProgressInfoPB build() {
        com.yorha.proto.StructCommonPB.ProgressInfoPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.ProgressInfoPB buildPartial() {
        com.yorha.proto.StructCommonPB.ProgressInfoPB result = new com.yorha.proto.StructCommonPB.ProgressInfoPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.uid_ = uid_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lastCalNum_ = lastCalNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.maxNum_ = maxNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lastCalTsMs_ = lastCalTsMs_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.speed_ = speed_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.stateEndTsMs_ = stateEndTsMs_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.stateStartTsMs_ = stateStartTsMs_;
          to_bitField0_ |= 0x00000040;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.ProgressInfoPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.ProgressInfoPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.ProgressInfoPB other) {
        if (other == com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance()) return this;
        if (other.hasUid()) {
          setUid(other.getUid());
        }
        if (other.hasLastCalNum()) {
          setLastCalNum(other.getLastCalNum());
        }
        if (other.hasMaxNum()) {
          setMaxNum(other.getMaxNum());
        }
        if (other.hasLastCalTsMs()) {
          setLastCalTsMs(other.getLastCalTsMs());
        }
        if (other.hasSpeed()) {
          setSpeed(other.getSpeed());
        }
        if (other.hasStateEndTsMs()) {
          setStateEndTsMs(other.getStateEndTsMs());
        }
        if (other.hasStateStartTsMs()) {
          setStateStartTsMs(other.getStateStartTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.ProgressInfoPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.ProgressInfoPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long uid_ ;
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @return Whether the uid field is set.
       */
      @java.lang.Override
      public boolean hasUid() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @return The uid.
       */
      @java.lang.Override
      public long getUid() {
        return uid_;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(long value) {
        bitField0_ |= 0x00000001;
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 uid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        uid_ = 0L;
        onChanged();
        return this;
      }

      private long lastCalNum_ ;
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @return Whether the lastCalNum field is set.
       */
      @java.lang.Override
      public boolean hasLastCalNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @return The lastCalNum.
       */
      @java.lang.Override
      public long getLastCalNum() {
        return lastCalNum_;
      }
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @param value The lastCalNum to set.
       * @return This builder for chaining.
       */
      public Builder setLastCalNum(long value) {
        bitField0_ |= 0x00000002;
        lastCalNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 服务端上次结算的数量，可以解释为上次有军队进出结算的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 lastCalNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastCalNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lastCalNum_ = 0L;
        onChanged();
        return this;
      }

      private long maxNum_ ;
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @return Whether the maxNum field is set.
       */
      @java.lang.Override
      public boolean hasMaxNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @return The maxNum.
       */
      @java.lang.Override
      public long getMaxNum() {
        return maxNum_;
      }
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @param value The maxNum to set.
       * @return This builder for chaining.
       */
      public Builder setMaxNum(long value) {
        bitField0_ |= 0x00000004;
        maxNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 总量，可以解释为总的资源量，建设量，占领量，耐久度
       * </pre>
       *
       * <code>optional int64 maxNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        maxNum_ = 0L;
        onChanged();
        return this;
      }

      private long lastCalTsMs_ ;
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @return Whether the lastCalTsMs field is set.
       */
      @java.lang.Override
      public boolean hasLastCalTsMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @return The lastCalTsMs.
       */
      @java.lang.Override
      public long getLastCalTsMs() {
        return lastCalTsMs_;
      }
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @param value The lastCalTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setLastCalTsMs(long value) {
        bitField0_ |= 0x00000008;
        lastCalTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 上次结算的时间戳，结算事件可能是军队进出，加成变化，建筑状态变化等
       * </pre>
       *
       * <code>optional int64 lastCalTsMs = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastCalTsMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lastCalTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long speed_ ;
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @return Whether the speed field is set.
       */
      @java.lang.Override
      public boolean hasSpeed() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @return The speed.
       */
      @java.lang.Override
      public long getSpeed() {
        return speed_;
      }
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @param value The speed to set.
       * @return This builder for chaining.
       */
      public Builder setSpeed(long value) {
        bitField0_ |= 0x00000010;
        speed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前的速度，可以解释为采集速度，建设速度，占领速度，耐久度降低、恢复速度，用于计算假的显示值
       * </pre>
       *
       * <code>optional int64 speed = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSpeed() {
        bitField0_ = (bitField0_ & ~0x00000010);
        speed_ = 0L;
        onChanged();
        return this;
      }

      private long stateEndTsMs_ ;
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @return Whether the stateEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateEndTsMs() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @return The stateEndTsMs.
       */
      @java.lang.Override
      public long getStateEndTsMs() {
        return stateEndTsMs_;
      }
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @param value The stateEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateEndTsMs(long value) {
        bitField0_ |= 0x00000020;
        stateEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态结束时间戳，可以解释为占领、建设等状态的结束时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000020);
        stateEndTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long stateStartTsMs_ ;
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @return Whether the stateStartTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateStartTsMs() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @return The stateStartTsMs.
       */
      @java.lang.Override
      public long getStateStartTsMs() {
        return stateStartTsMs_;
      }
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @param value The stateStartTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateStartTsMs(long value) {
        bitField0_ |= 0x00000040;
        stateStartTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态开始时间戳，可以解释为占领、建设等状态的开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateStartTsMs() {
        bitField0_ = (bitField0_ & ~0x00000040);
        stateStartTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ProgressInfoPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ProgressInfoPB)
    private static final com.yorha.proto.StructCommonPB.ProgressInfoPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.ProgressInfoPB();
    }

    public static com.yorha.proto.StructCommonPB.ProgressInfoPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ProgressInfoPB>
        PARSER = new com.google.protobuf.AbstractParser<ProgressInfoPB>() {
      @java.lang.Override
      public ProgressInfoPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ProgressInfoPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ProgressInfoPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ProgressInfoPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.ProgressInfoPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface InvitePlayerRecordPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.InvitePlayerRecordPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return Whether the inviteTsMs field is set.
     */
    boolean hasInviteTsMs();
    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return The inviteTsMs.
     */
    long getInviteTsMs();
  }
  /**
   * <pre>
   * 军团邀请单人数据
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.InvitePlayerRecordPB}
   */
  public static final class InvitePlayerRecordPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.InvitePlayerRecordPB)
      InvitePlayerRecordPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use InvitePlayerRecordPB.newBuilder() to construct.
    private InvitePlayerRecordPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private InvitePlayerRecordPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new InvitePlayerRecordPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private InvitePlayerRecordPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              inviteTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_InvitePlayerRecordPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_InvitePlayerRecordPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.class, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int INVITETSMS_FIELD_NUMBER = 2;
    private long inviteTsMs_;
    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return Whether the inviteTsMs field is set.
     */
    @java.lang.Override
    public boolean hasInviteTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 邀请发起的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 inviteTsMs = 2;</code>
     * @return The inviteTsMs.
     */
    @java.lang.Override
    public long getInviteTsMs() {
      return inviteTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, inviteTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, inviteTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.InvitePlayerRecordPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.InvitePlayerRecordPB other = (com.yorha.proto.StructCommonPB.InvitePlayerRecordPB) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasInviteTsMs() != other.hasInviteTsMs()) return false;
      if (hasInviteTsMs()) {
        if (getInviteTsMs()
            != other.getInviteTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasInviteTsMs()) {
        hash = (37 * hash) + INVITETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getInviteTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.InvitePlayerRecordPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 军团邀请单人数据
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.InvitePlayerRecordPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.InvitePlayerRecordPB)
        com.yorha.proto.StructCommonPB.InvitePlayerRecordPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_InvitePlayerRecordPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_InvitePlayerRecordPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.class, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        inviteTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_InvitePlayerRecordPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB build() {
        com.yorha.proto.StructCommonPB.InvitePlayerRecordPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB buildPartial() {
        com.yorha.proto.StructCommonPB.InvitePlayerRecordPB result = new com.yorha.proto.StructCommonPB.InvitePlayerRecordPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.inviteTsMs_ = inviteTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.InvitePlayerRecordPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.InvitePlayerRecordPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.InvitePlayerRecordPB other) {
        if (other == com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasInviteTsMs()) {
          setInviteTsMs(other.getInviteTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.InvitePlayerRecordPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.InvitePlayerRecordPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long inviteTsMs_ ;
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @return Whether the inviteTsMs field is set.
       */
      @java.lang.Override
      public boolean hasInviteTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @return The inviteTsMs.
       */
      @java.lang.Override
      public long getInviteTsMs() {
        return inviteTsMs_;
      }
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @param value The inviteTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setInviteTsMs(long value) {
        bitField0_ |= 0x00000002;
        inviteTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邀请发起的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 inviteTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInviteTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        inviteTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.InvitePlayerRecordPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.InvitePlayerRecordPB)
    private static final com.yorha.proto.StructCommonPB.InvitePlayerRecordPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.InvitePlayerRecordPB();
    }

    public static com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<InvitePlayerRecordPB>
        PARSER = new com.google.protobuf.AbstractParser<InvitePlayerRecordPB>() {
      @java.lang.Override
      public InvitePlayerRecordPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new InvitePlayerRecordPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<InvitePlayerRecordPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<InvitePlayerRecordPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DungeonSkillSysPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DungeonSkillSysPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
     * @return Whether the skill field is set.
     */
    boolean hasSkill();
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
     * @return The skill.
     */
    com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB getSkill();
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
     */
    com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPBOrBuilder getSkillOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DungeonSkillSysPB}
   */
  public static final class DungeonSkillSysPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DungeonSkillSysPB)
      DungeonSkillSysPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DungeonSkillSysPB.newBuilder() to construct.
    private DungeonSkillSysPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DungeonSkillSysPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DungeonSkillSysPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DungeonSkillSysPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = skill_.toBuilder();
              }
              skill_ = input.readMessage(com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(skill_);
                skill_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillSysPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillSysPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.DungeonSkillSysPB.class, com.yorha.proto.StructCommonPB.DungeonSkillSysPB.Builder.class);
    }

    private int bitField0_;
    public static final int SKILL_FIELD_NUMBER = 1;
    private com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB skill_;
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
     * @return Whether the skill field is set.
     */
    @java.lang.Override
    public boolean hasSkill() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
     * @return The skill.
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB getSkill() {
      return skill_ == null ? com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.getDefaultInstance() : skill_;
    }
    /**
     * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPBOrBuilder getSkillOrBuilder() {
      return skill_ == null ? com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.getDefaultInstance() : skill_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getSkill());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getSkill());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.DungeonSkillSysPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.DungeonSkillSysPB other = (com.yorha.proto.StructCommonPB.DungeonSkillSysPB) obj;

      if (hasSkill() != other.hasSkill()) return false;
      if (hasSkill()) {
        if (!getSkill()
            .equals(other.getSkill())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkill()) {
        hash = (37 * hash) + SKILL_FIELD_NUMBER;
        hash = (53 * hash) + getSkill().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.DungeonSkillSysPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DungeonSkillSysPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DungeonSkillSysPB)
        com.yorha.proto.StructCommonPB.DungeonSkillSysPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillSysPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillSysPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.DungeonSkillSysPB.class, com.yorha.proto.StructCommonPB.DungeonSkillSysPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.DungeonSkillSysPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSkillFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (skillBuilder_ == null) {
          skill_ = null;
        } else {
          skillBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillSysPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.DungeonSkillSysPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.DungeonSkillSysPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.DungeonSkillSysPB build() {
        com.yorha.proto.StructCommonPB.DungeonSkillSysPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.DungeonSkillSysPB buildPartial() {
        com.yorha.proto.StructCommonPB.DungeonSkillSysPB result = new com.yorha.proto.StructCommonPB.DungeonSkillSysPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (skillBuilder_ == null) {
            result.skill_ = skill_;
          } else {
            result.skill_ = skillBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.DungeonSkillSysPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.DungeonSkillSysPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.DungeonSkillSysPB other) {
        if (other == com.yorha.proto.StructCommonPB.DungeonSkillSysPB.getDefaultInstance()) return this;
        if (other.hasSkill()) {
          mergeSkill(other.getSkill());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.DungeonSkillSysPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.DungeonSkillSysPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB skill_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPBOrBuilder> skillBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       * @return Whether the skill field is set.
       */
      public boolean hasSkill() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       * @return The skill.
       */
      public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB getSkill() {
        if (skillBuilder_ == null) {
          return skill_ == null ? com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.getDefaultInstance() : skill_;
        } else {
          return skillBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       */
      public Builder setSkill(com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB value) {
        if (skillBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          skill_ = value;
          onChanged();
        } else {
          skillBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       */
      public Builder setSkill(
          com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder builderForValue) {
        if (skillBuilder_ == null) {
          skill_ = builderForValue.build();
          onChanged();
        } else {
          skillBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       */
      public Builder mergeSkill(com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB value) {
        if (skillBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              skill_ != null &&
              skill_ != com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.getDefaultInstance()) {
            skill_ =
              com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.newBuilder(skill_).mergeFrom(value).buildPartial();
          } else {
            skill_ = value;
          }
          onChanged();
        } else {
          skillBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       */
      public Builder clearSkill() {
        if (skillBuilder_ == null) {
          skill_ = null;
          onChanged();
        } else {
          skillBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       */
      public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder getSkillBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSkillFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       */
      public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPBOrBuilder getSkillOrBuilder() {
        if (skillBuilder_ != null) {
          return skillBuilder_.getMessageOrBuilder();
        } else {
          return skill_ == null ?
              com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.getDefaultInstance() : skill_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32DungeonSkillItemMapPB skill = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPBOrBuilder> 
          getSkillFieldBuilder() {
        if (skillBuilder_ == null) {
          skillBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPBOrBuilder>(
                  getSkill(),
                  getParentForChildren(),
                  isClean());
          skill_ = null;
        }
        return skillBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DungeonSkillSysPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DungeonSkillSysPB)
    private static final com.yorha.proto.StructCommonPB.DungeonSkillSysPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.DungeonSkillSysPB();
    }

    public static com.yorha.proto.StructCommonPB.DungeonSkillSysPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DungeonSkillSysPB>
        PARSER = new com.google.protobuf.AbstractParser<DungeonSkillSysPB>() {
      @java.lang.Override
      public DungeonSkillSysPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DungeonSkillSysPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DungeonSkillSysPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DungeonSkillSysPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.DungeonSkillSysPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DungeonSkillItemPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DungeonSkillItemPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return Whether the skillId field is set.
     */
    boolean hasSkillId();
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return The skillId.
     */
    int getSkillId();

    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return Whether the canUseNum field is set.
     */
    boolean hasCanUseNum();
    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return The canUseNum.
     */
    int getCanUseNum();

    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return Whether the canUseTsMs field is set.
     */
    boolean hasCanUseTsMs();
    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return The canUseTsMs.
     */
    long getCanUseTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DungeonSkillItemPB}
   */
  public static final class DungeonSkillItemPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DungeonSkillItemPB)
      DungeonSkillItemPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DungeonSkillItemPB.newBuilder() to construct.
    private DungeonSkillItemPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DungeonSkillItemPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DungeonSkillItemPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DungeonSkillItemPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              skillId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              canUseNum_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              canUseTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillItemPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillItemPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.DungeonSkillItemPB.class, com.yorha.proto.StructCommonPB.DungeonSkillItemPB.Builder.class);
    }

    private int bitField0_;
    public static final int SKILLID_FIELD_NUMBER = 1;
    private int skillId_;
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return Whether the skillId field is set.
     */
    @java.lang.Override
    public boolean hasSkillId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 skillId = 1;</code>
     * @return The skillId.
     */
    @java.lang.Override
    public int getSkillId() {
      return skillId_;
    }

    public static final int CANUSENUM_FIELD_NUMBER = 2;
    private int canUseNum_;
    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return Whether the canUseNum field is set.
     */
    @java.lang.Override
    public boolean hasCanUseNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 可使用次数  可能为-1表无限
     * </pre>
     *
     * <code>optional int32 canUseNum = 2;</code>
     * @return The canUseNum.
     */
    @java.lang.Override
    public int getCanUseNum() {
      return canUseNum_;
    }

    public static final int CANUSETSMS_FIELD_NUMBER = 3;
    private long canUseTsMs_;
    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return Whether the canUseTsMs field is set.
     */
    @java.lang.Override
    public boolean hasCanUseTsMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 可使用时间戳  小于当前时间可用
     * </pre>
     *
     * <code>optional int64 canUseTsMs = 3;</code>
     * @return The canUseTsMs.
     */
    @java.lang.Override
    public long getCanUseTsMs() {
      return canUseTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, skillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, canUseNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, canUseTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, skillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, canUseNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, canUseTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.DungeonSkillItemPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.DungeonSkillItemPB other = (com.yorha.proto.StructCommonPB.DungeonSkillItemPB) obj;

      if (hasSkillId() != other.hasSkillId()) return false;
      if (hasSkillId()) {
        if (getSkillId()
            != other.getSkillId()) return false;
      }
      if (hasCanUseNum() != other.hasCanUseNum()) return false;
      if (hasCanUseNum()) {
        if (getCanUseNum()
            != other.getCanUseNum()) return false;
      }
      if (hasCanUseTsMs() != other.hasCanUseTsMs()) return false;
      if (hasCanUseTsMs()) {
        if (getCanUseTsMs()
            != other.getCanUseTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkillId()) {
        hash = (37 * hash) + SKILLID_FIELD_NUMBER;
        hash = (53 * hash) + getSkillId();
      }
      if (hasCanUseNum()) {
        hash = (37 * hash) + CANUSENUM_FIELD_NUMBER;
        hash = (53 * hash) + getCanUseNum();
      }
      if (hasCanUseTsMs()) {
        hash = (37 * hash) + CANUSETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCanUseTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.DungeonSkillItemPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DungeonSkillItemPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DungeonSkillItemPB)
        com.yorha.proto.StructCommonPB.DungeonSkillItemPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillItemPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillItemPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.DungeonSkillItemPB.class, com.yorha.proto.StructCommonPB.DungeonSkillItemPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.DungeonSkillItemPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skillId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        canUseNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        canUseTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_DungeonSkillItemPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.DungeonSkillItemPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.DungeonSkillItemPB build() {
        com.yorha.proto.StructCommonPB.DungeonSkillItemPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.DungeonSkillItemPB buildPartial() {
        com.yorha.proto.StructCommonPB.DungeonSkillItemPB result = new com.yorha.proto.StructCommonPB.DungeonSkillItemPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.skillId_ = skillId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.canUseNum_ = canUseNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.canUseTsMs_ = canUseTsMs_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.DungeonSkillItemPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.DungeonSkillItemPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.DungeonSkillItemPB other) {
        if (other == com.yorha.proto.StructCommonPB.DungeonSkillItemPB.getDefaultInstance()) return this;
        if (other.hasSkillId()) {
          setSkillId(other.getSkillId());
        }
        if (other.hasCanUseNum()) {
          setCanUseNum(other.getCanUseNum());
        }
        if (other.hasCanUseTsMs()) {
          setCanUseTsMs(other.getCanUseTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.DungeonSkillItemPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.DungeonSkillItemPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int skillId_ ;
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return Whether the skillId field is set.
       */
      @java.lang.Override
      public boolean hasSkillId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return The skillId.
       */
      @java.lang.Override
      public int getSkillId() {
        return skillId_;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @param value The skillId to set.
       * @return This builder for chaining.
       */
      public Builder setSkillId(int value) {
        bitField0_ |= 0x00000001;
        skillId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 skillId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        skillId_ = 0;
        onChanged();
        return this;
      }

      private int canUseNum_ ;
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @return Whether the canUseNum field is set.
       */
      @java.lang.Override
      public boolean hasCanUseNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @return The canUseNum.
       */
      @java.lang.Override
      public int getCanUseNum() {
        return canUseNum_;
      }
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @param value The canUseNum to set.
       * @return This builder for chaining.
       */
      public Builder setCanUseNum(int value) {
        bitField0_ |= 0x00000002;
        canUseNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可使用次数  可能为-1表无限
       * </pre>
       *
       * <code>optional int32 canUseNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCanUseNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        canUseNum_ = 0;
        onChanged();
        return this;
      }

      private long canUseTsMs_ ;
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @return Whether the canUseTsMs field is set.
       */
      @java.lang.Override
      public boolean hasCanUseTsMs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @return The canUseTsMs.
       */
      @java.lang.Override
      public long getCanUseTsMs() {
        return canUseTsMs_;
      }
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @param value The canUseTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setCanUseTsMs(long value) {
        bitField0_ |= 0x00000004;
        canUseTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 可使用时间戳  小于当前时间可用
       * </pre>
       *
       * <code>optional int64 canUseTsMs = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCanUseTsMs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        canUseTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DungeonSkillItemPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DungeonSkillItemPB)
    private static final com.yorha.proto.StructCommonPB.DungeonSkillItemPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.DungeonSkillItemPB();
    }

    public static com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DungeonSkillItemPB>
        PARSER = new com.google.protobuf.AbstractParser<DungeonSkillItemPB>() {
      @java.lang.Override
      public DungeonSkillItemPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DungeonSkillItemPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DungeonSkillItemPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DungeonSkillItemPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CrossDataModelPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CrossDataModelPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
     * @return The data.
     */
    com.yorha.proto.StructCommonPB.Int32CrossDataMapPB getData();
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
     */
    com.yorha.proto.StructCommonPB.Int32CrossDataMapPBOrBuilder getDataOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CrossDataModelPB}
   */
  public static final class CrossDataModelPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CrossDataModelPB)
      CrossDataModelPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CrossDataModelPB.newBuilder() to construct.
    private CrossDataModelPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CrossDataModelPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CrossDataModelPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CrossDataModelPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = data_.toBuilder();
              }
              data_ = input.readMessage(com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(data_);
                data_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataModelPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataModelPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.CrossDataModelPB.class, com.yorha.proto.StructCommonPB.CrossDataModelPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATA_FIELD_NUMBER = 1;
    private com.yorha.proto.StructCommonPB.Int32CrossDataMapPB data_;
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
     * @return Whether the data field is set.
     */
    @java.lang.Override
    public boolean hasData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int32CrossDataMapPB getData() {
      return data_ == null ? com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.getDefaultInstance() : data_;
    }
    /**
     * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int32CrossDataMapPBOrBuilder getDataOrBuilder() {
      return data_ == null ? com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.getDefaultInstance() : data_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.CrossDataModelPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.CrossDataModelPB other = (com.yorha.proto.StructCommonPB.CrossDataModelPB) obj;

      if (hasData() != other.hasData()) return false;
      if (hasData()) {
        if (!getData()
            .equals(other.getData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasData()) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataModelPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.CrossDataModelPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CrossDataModelPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CrossDataModelPB)
        com.yorha.proto.StructCommonPB.CrossDataModelPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataModelPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataModelPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.CrossDataModelPB.class, com.yorha.proto.StructCommonPB.CrossDataModelPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.CrossDataModelPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dataBuilder_ == null) {
          data_ = null;
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataModelPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.CrossDataModelPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.CrossDataModelPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.CrossDataModelPB build() {
        com.yorha.proto.StructCommonPB.CrossDataModelPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.CrossDataModelPB buildPartial() {
        com.yorha.proto.StructCommonPB.CrossDataModelPB result = new com.yorha.proto.StructCommonPB.CrossDataModelPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (dataBuilder_ == null) {
            result.data_ = data_;
          } else {
            result.data_ = dataBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.CrossDataModelPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.CrossDataModelPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.CrossDataModelPB other) {
        if (other == com.yorha.proto.StructCommonPB.CrossDataModelPB.getDefaultInstance()) return this;
        if (other.hasData()) {
          mergeData(other.getData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.CrossDataModelPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.CrossDataModelPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructCommonPB.Int32CrossDataMapPB data_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.Int32CrossDataMapPB, com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder, com.yorha.proto.StructCommonPB.Int32CrossDataMapPBOrBuilder> dataBuilder_;
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       * @return Whether the data field is set.
       */
      public boolean hasData() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       * @return The data.
       */
      public com.yorha.proto.StructCommonPB.Int32CrossDataMapPB getData() {
        if (dataBuilder_ == null) {
          return data_ == null ? com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.getDefaultInstance() : data_;
        } else {
          return dataBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       */
      public Builder setData(com.yorha.proto.StructCommonPB.Int32CrossDataMapPB value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          data_ = value;
          onChanged();
        } else {
          dataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       */
      public Builder setData(
          com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder builderForValue) {
        if (dataBuilder_ == null) {
          data_ = builderForValue.build();
          onChanged();
        } else {
          dataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       */
      public Builder mergeData(com.yorha.proto.StructCommonPB.Int32CrossDataMapPB value) {
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              data_ != null &&
              data_ != com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.getDefaultInstance()) {
            data_ =
              com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.newBuilder(data_).mergeFrom(value).buildPartial();
          } else {
            data_ = value;
          }
          onChanged();
        } else {
          dataBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = null;
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       */
      public com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder getDataBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDataFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       */
      public com.yorha.proto.StructCommonPB.Int32CrossDataMapPBOrBuilder getDataOrBuilder() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilder();
        } else {
          return data_ == null ?
              com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.getDefaultInstance() : data_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Int32CrossDataMapPB data = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.Int32CrossDataMapPB, com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder, com.yorha.proto.StructCommonPB.Int32CrossDataMapPBOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructCommonPB.Int32CrossDataMapPB, com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder, com.yorha.proto.StructCommonPB.Int32CrossDataMapPBOrBuilder>(
                  getData(),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CrossDataModelPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CrossDataModelPB)
    private static final com.yorha.proto.StructCommonPB.CrossDataModelPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.CrossDataModelPB();
    }

    public static com.yorha.proto.StructCommonPB.CrossDataModelPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CrossDataModelPB>
        PARSER = new com.google.protobuf.AbstractParser<CrossDataModelPB>() {
      @java.lang.Override
      public CrossDataModelPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CrossDataModelPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CrossDataModelPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CrossDataModelPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.CrossDataModelPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CrossDataPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CrossDataPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return Whether the ownerClanId field is set.
     */
    boolean hasOwnerClanId();
    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return The ownerClanId.
     */
    long getOwnerClanId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CrossDataPB}
   */
  public static final class CrossDataPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CrossDataPB)
      CrossDataPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CrossDataPB.newBuilder() to construct.
    private CrossDataPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CrossDataPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CrossDataPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CrossDataPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              partId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ownerClanId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.CrossDataPB.class, com.yorha.proto.StructCommonPB.CrossDataPB.Builder.class);
    }

    private int bitField0_;
    public static final int PARTID_FIELD_NUMBER = 1;
    private int partId_;
    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 片id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int OWNERCLANID_FIELD_NUMBER = 2;
    private long ownerClanId_;
    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return Whether the ownerClanId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerClanId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 归属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 2;</code>
     * @return The ownerClanId.
     */
    @java.lang.Override
    public long getOwnerClanId() {
      return ownerClanId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, partId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, ownerClanId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, partId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ownerClanId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.CrossDataPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.CrossDataPB other = (com.yorha.proto.StructCommonPB.CrossDataPB) obj;

      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (hasOwnerClanId() != other.hasOwnerClanId()) return false;
      if (hasOwnerClanId()) {
        if (getOwnerClanId()
            != other.getOwnerClanId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      if (hasOwnerClanId()) {
        hash = (37 * hash) + OWNERCLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerClanId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.CrossDataPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.CrossDataPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CrossDataPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CrossDataPB)
        com.yorha.proto.StructCommonPB.CrossDataPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.CrossDataPB.class, com.yorha.proto.StructCommonPB.CrossDataPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.CrossDataPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_CrossDataPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.CrossDataPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.CrossDataPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.CrossDataPB build() {
        com.yorha.proto.StructCommonPB.CrossDataPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.CrossDataPB buildPartial() {
        com.yorha.proto.StructCommonPB.CrossDataPB result = new com.yorha.proto.StructCommonPB.CrossDataPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ownerClanId_ = ownerClanId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.CrossDataPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.CrossDataPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.CrossDataPB other) {
        if (other == com.yorha.proto.StructCommonPB.CrossDataPB.getDefaultInstance()) return this;
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        if (other.hasOwnerClanId()) {
          setOwnerClanId(other.getOwnerClanId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.CrossDataPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.CrossDataPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000001;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 片id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private long ownerClanId_ ;
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @return Whether the ownerClanId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerClanId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @return The ownerClanId.
       */
      @java.lang.Override
      public long getOwnerClanId() {
        return ownerClanId_;
      }
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @param value The ownerClanId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerClanId(long value) {
        bitField0_ |= 0x00000002;
        ownerClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 归属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerClanId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ownerClanId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CrossDataPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CrossDataPB)
    private static final com.yorha.proto.StructCommonPB.CrossDataPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.CrossDataPB();
    }

    public static com.yorha.proto.StructCommonPB.CrossDataPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CrossDataPB>
        PARSER = new com.google.protobuf.AbstractParser<CrossDataPB>() {
      @java.lang.Override
      public CrossDataPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CrossDataPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CrossDataPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CrossDataPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.CrossDataPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32DungeonSkillItemMapPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32DungeonSkillItemMapPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */
    boolean containsDatas(
        int key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
    getDatas();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
    getDatasMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommonPB.DungeonSkillItemPB defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDatasOrThrow(
        int key);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32DungeonSkillItemMapPB}
   */
  public static final class Int32DungeonSkillItemMapPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32DungeonSkillItemMapPB)
      Int32DungeonSkillItemMapPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32DungeonSkillItemMapPB.newBuilder() to construct.
    private Int32DungeonSkillItemMapPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32DungeonSkillItemMapPB() {
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32DungeonSkillItemMapPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32DungeonSkillItemMapPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.class, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>newDefaultInstance(
                  com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommonPB.DungeonSkillItemPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> datas_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        int key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommonPB.DungeonSkillItemPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDatasOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB other = (com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32DungeonSkillItemMapPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32DungeonSkillItemMapPB)
        com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.class, com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB build() {
        com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB buildPartial() {
        com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB result = new com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB other) {
        if (other == com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> datas_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          int key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDatasOrDefault(
          int key,
          com.yorha.proto.StructCommonPB.DungeonSkillItemPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.DungeonSkillItemPB getDatasOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          int key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
       */
      public Builder putDatas(
          int key,
          com.yorha.proto.StructCommonPB.DungeonSkillItemPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DungeonSkillItemPB&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.DungeonSkillItemPB> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32DungeonSkillItemMapPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32DungeonSkillItemMapPB)
    private static final com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB();
    }

    public static com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32DungeonSkillItemMapPB>
        PARSER = new com.google.protobuf.AbstractParser<Int32DungeonSkillItemMapPB>() {
      @java.lang.Override
      public Int32DungeonSkillItemMapPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32DungeonSkillItemMapPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32DungeonSkillItemMapPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32DungeonSkillItemMapPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int32DungeonSkillItemMapPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32CrossDataMapPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32CrossDataMapPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */
    boolean containsDatas(
        int key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
    getDatas();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
    getDatasMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.CrossDataPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommonPB.CrossDataPB defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.CrossDataPB getDatasOrThrow(
        int key);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32CrossDataMapPB}
   */
  public static final class Int32CrossDataMapPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32CrossDataMapPB)
      Int32CrossDataMapPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32CrossDataMapPB.newBuilder() to construct.
    private Int32CrossDataMapPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32CrossDataMapPB() {
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32CrossDataMapPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32CrossDataMapPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32CrossDataMapPB_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32CrossDataMapPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.class, com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>newDefaultInstance(
                  com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32CrossDataMapPB_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommonPB.CrossDataPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> datas_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        int key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.CrossDataPB getDatasOrDefault(
        int key,
        com.yorha.proto.StructCommonPB.CrossDataPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.CrossDataPB getDatasOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.Int32CrossDataMapPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.Int32CrossDataMapPB other = (com.yorha.proto.StructCommonPB.Int32CrossDataMapPB) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.Int32CrossDataMapPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32CrossDataMapPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32CrossDataMapPB)
        com.yorha.proto.StructCommonPB.Int32CrossDataMapPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32CrossDataMapPB_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32CrossDataMapPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.class, com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int32CrossDataMapPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int32CrossDataMapPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int32CrossDataMapPB build() {
        com.yorha.proto.StructCommonPB.Int32CrossDataMapPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int32CrossDataMapPB buildPartial() {
        com.yorha.proto.StructCommonPB.Int32CrossDataMapPB result = new com.yorha.proto.StructCommonPB.Int32CrossDataMapPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.Int32CrossDataMapPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.Int32CrossDataMapPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.Int32CrossDataMapPB other) {
        if (other == com.yorha.proto.StructCommonPB.Int32CrossDataMapPB.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.Int32CrossDataMapPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.Int32CrossDataMapPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> datas_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          int key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.CrossDataPB getDatasOrDefault(
          int key,
          com.yorha.proto.StructCommonPB.CrossDataPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.CrossDataPB getDatasOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          int key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
       */
      public Builder putDatas(
          int key,
          com.yorha.proto.StructCommonPB.CrossDataPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.CrossDataPB&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructCommonPB.CrossDataPB> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32CrossDataMapPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32CrossDataMapPB)
    private static final com.yorha.proto.StructCommonPB.Int32CrossDataMapPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.Int32CrossDataMapPB();
    }

    public static com.yorha.proto.StructCommonPB.Int32CrossDataMapPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32CrossDataMapPB>
        PARSER = new com.google.protobuf.AbstractParser<Int32CrossDataMapPB>() {
      @java.lang.Override
      public Int32CrossDataMapPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32CrossDataMapPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32CrossDataMapPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32CrossDataMapPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int32CrossDataMapPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int64InvitePlayerRecordMapPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int64InvitePlayerRecordMapPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */
    boolean containsDatas(
        long key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
    getDatas();
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
    getDatasMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommonPB.InvitePlayerRecordPB defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDatasOrThrow(
        long key);

    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Long> getDeleteKeysList();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    long getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int64InvitePlayerRecordMapPB}
   */
  public static final class Int64InvitePlayerRecordMapPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int64InvitePlayerRecordMapPB)
      Int64InvitePlayerRecordMapPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int64InvitePlayerRecordMapPB.newBuilder() to construct.
    private Int64InvitePlayerRecordMapPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int64InvitePlayerRecordMapPB() {
      deleteKeys_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int64InvitePlayerRecordMapPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int64InvitePlayerRecordMapPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB.class, com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>newDefaultInstance(
                  com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommonPB.InvitePlayerRecordPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> datas_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        long key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommonPB.InvitePlayerRecordPB defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDatasOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList deleteKeys_;
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public long getDeleteKeys(int index) {
      return deleteKeys_.getLong(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt64(2, deleteKeys_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(deleteKeys_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB other = (com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int64InvitePlayerRecordMapPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int64InvitePlayerRecordMapPB)
        com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB.class, com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB build() {
        com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB buildPartial() {
        com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB result = new com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB other) {
        if (other == com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> datas_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          long key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDatasOrDefault(
          long key,
          com.yorha.proto.StructCommonPB.InvitePlayerRecordPB defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.InvitePlayerRecordPB getDatasOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          long key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
       */
      public Builder putDatas(
          long key,
          com.yorha.proto.StructCommonPB.InvitePlayerRecordPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.InvitePlayerRecordPB&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.InvitePlayerRecordPB> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.LongList deleteKeys_ = emptyLongList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Long>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public long getDeleteKeys(int index) {
        return deleteKeys_.getLong(index);
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int64InvitePlayerRecordMapPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int64InvitePlayerRecordMapPB)
    private static final com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB();
    }

    public static com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int64InvitePlayerRecordMapPB>
        PARSER = new com.google.protobuf.AbstractParser<Int64InvitePlayerRecordMapPB>() {
      @java.lang.Override
      public Int64InvitePlayerRecordMapPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int64InvitePlayerRecordMapPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int64InvitePlayerRecordMapPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int64InvitePlayerRecordMapPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int64InvitePlayerRecordMapPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int64ProgressInfoMapPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int64ProgressInfoMapPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */
    boolean containsDatas(
        long key);
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
    getDatas();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
    getDatasMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.ProgressInfoPB getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommonPB.ProgressInfoPB defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */

    com.yorha.proto.StructCommonPB.ProgressInfoPB getDatasOrThrow(
        long key);

    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Long> getDeleteKeysList();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    long getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int64ProgressInfoMapPB}
   */
  public static final class Int64ProgressInfoMapPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int64ProgressInfoMapPB)
      Int64ProgressInfoMapPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int64ProgressInfoMapPB.newBuilder() to construct.
    private Int64ProgressInfoMapPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int64ProgressInfoMapPB() {
      deleteKeys_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int64ProgressInfoMapPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int64ProgressInfoMapPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = com.google.protobuf.MapField.newMapField(
                    DatasDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
              datas__ = input.readMessage(
                  DatasDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              datas_.getMutableMap().put(
                  datas__.getKey(), datas__.getValue());
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64ProgressInfoMapPB_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetDatas();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64ProgressInfoMapPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB.class, com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private static final class DatasDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>newDefaultInstance(
                  com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64ProgressInfoMapPB_DatasEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> datas_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
    internalGetDatas() {
      if (datas_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DatasDefaultEntryHolder.defaultEntry);
      }
      return datas_;
    }

    public int getDatasCount() {
      return internalGetDatas().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */

    @java.lang.Override
    public boolean containsDatas(
        long key) {
      
      return internalGetDatas().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDatasMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> getDatas() {
      return getDatasMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> getDatasMap() {
      return internalGetDatas().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.ProgressInfoPB getDatasOrDefault(
        long key,
        com.yorha.proto.StructCommonPB.ProgressInfoPB defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> map =
          internalGetDatas().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructCommonPB.ProgressInfoPB getDatasOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> map =
          internalGetDatas().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList deleteKeys_;
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public long getDeleteKeys(int index) {
      return deleteKeys_.getLong(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetDatas(),
          DatasDefaultEntryHolder.defaultEntry,
          1);
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt64(2, deleteKeys_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> entry
           : internalGetDatas().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
        datas__ = DatasDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, datas__);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(deleteKeys_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB other = (com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB) obj;

      if (!internalGetDatas().equals(
          other.internalGetDatas())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetDatas().getMap().isEmpty()) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDatas().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int64ProgressInfoMapPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int64ProgressInfoMapPB)
        com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64ProgressInfoMapPB_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableDatas();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64ProgressInfoMapPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB.class, com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB.Builder.class);
      }

      // Construct using com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableDatas().clear();
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.StructCommonPB.internal_static_com_yorha_proto_Int64ProgressInfoMapPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB getDefaultInstanceForType() {
        return com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB build() {
        com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB buildPartial() {
        com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB result = new com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.datas_ = internalGetDatas();
        result.datas_.makeImmutable();
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB) {
          return mergeFrom((com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB other) {
        if (other == com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB.getDefaultInstance()) return this;
        internalGetMutableDatas().mergeFrom(
            other.internalGetDatas());
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> datas_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
      internalGetDatas() {
        if (datas_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        return datas_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
      internalGetMutableDatas() {
        onChanged();;
        if (datas_ == null) {
          datas_ = com.google.protobuf.MapField.newMapField(
              DatasDefaultEntryHolder.defaultEntry);
        }
        if (!datas_.isMutable()) {
          datas_ = datas_.copy();
        }
        return datas_;
      }

      public int getDatasCount() {
        return internalGetDatas().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
       */

      @java.lang.Override
      public boolean containsDatas(
          long key) {
        
        return internalGetDatas().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDatasMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> getDatas() {
        return getDatasMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> getDatasMap() {
        return internalGetDatas().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.ProgressInfoPB getDatasOrDefault(
          long key,
          com.yorha.proto.StructCommonPB.ProgressInfoPB defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> map =
            internalGetDatas().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructCommonPB.ProgressInfoPB getDatasOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> map =
            internalGetDatas().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDatas() {
        internalGetMutableDatas().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
       */

      public Builder removeDatas(
          long key) {
        
        internalGetMutableDatas().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB>
      getMutableDatas() {
        return internalGetMutableDatas().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
       */
      public Builder putDatas(
          long key,
          com.yorha.proto.StructCommonPB.ProgressInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDatas().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ProgressInfoPB&gt; datas = 1;</code>
       */

      public Builder putAllDatas(
          java.util.Map<java.lang.Long, com.yorha.proto.StructCommonPB.ProgressInfoPB> values) {
        internalGetMutableDatas().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.Internal.LongList deleteKeys_ = emptyLongList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Long>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public long getDeleteKeys(int index) {
        return deleteKeys_.getLong(index);
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int64ProgressInfoMapPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int64ProgressInfoMapPB)
    private static final com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB();
    }

    public static com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int64ProgressInfoMapPB>
        PARSER = new com.google.protobuf.AbstractParser<Int64ProgressInfoMapPB>() {
      @java.lang.Override
      public Int64ProgressInfoMapPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int64ProgressInfoMapPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int64ProgressInfoMapPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int64ProgressInfoMapPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.StructCommonPB.Int64ProgressInfoMapPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ProgressInfoPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ProgressInfoPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_InvitePlayerRecordPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_InvitePlayerRecordPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonSkillSysPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonSkillSysPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonSkillItemPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonSkillItemPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CrossDataModelPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CrossDataModelPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CrossDataPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CrossDataPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_DatasEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32CrossDataMapPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32CrossDataMapPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32CrossDataMapPB_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32CrossDataMapPB_DatasEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_DatasEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64ProgressInfoMapPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64ProgressInfoMapPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64ProgressInfoMapPB_DatasEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64ProgressInfoMapPB_DatasEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)cs_proto/gen/common/struct_commonPB.pr" +
      "oto\022\017com.yorha.proto\"\223\001\n\016ProgressInfoPB\022" +
      "\013\n\003uid\030\001 \001(\003\022\022\n\nlastCalNum\030\002 \001(\003\022\016\n\006maxN" +
      "um\030\003 \001(\003\022\023\n\013lastCalTsMs\030\004 \001(\003\022\r\n\005speed\030\005" +
      " \001(\003\022\024\n\014stateEndTsMs\030\006 \001(\003\022\026\n\016stateStart" +
      "TsMs\030\007 \001(\003\"<\n\024InvitePlayerRecordPB\022\020\n\010pl" +
      "ayerId\030\001 \001(\003\022\022\n\ninviteTsMs\030\002 \001(\003\"O\n\021Dung" +
      "eonSkillSysPB\022:\n\005skill\030\001 \001(\0132+.com.yorha" +
      ".proto.Int32DungeonSkillItemMapPB\"L\n\022Dun" +
      "geonSkillItemPB\022\017\n\007skillId\030\001 \001(\005\022\021\n\tcanU" +
      "seNum\030\002 \001(\005\022\022\n\ncanUseTsMs\030\003 \001(\003\"F\n\020Cross" +
      "DataModelPB\0222\n\004data\030\001 \001(\0132$.com.yorha.pr" +
      "oto.Int32CrossDataMapPB\"2\n\013CrossDataPB\022\016" +
      "\n\006partId\030\001 \001(\005\022\023\n\013ownerClanId\030\002 \001(\003\"\335\001\n\032" +
      "Int32DungeonSkillItemMapPB\022E\n\005datas\030\001 \003(" +
      "\01326.com.yorha.proto.Int32DungeonSkillIte" +
      "mMapPB.DatasEntry\022\022\n\ndeleteKeys\030\002 \003(\005\022\021\n" +
      "\tclearFlag\030\003 \001(\010\032Q\n\nDatasEntry\022\013\n\003key\030\001 " +
      "\001(\005\0222\n\005value\030\002 \001(\0132#.com.yorha.proto.Dun" +
      "geonSkillItemPB:\0028\001\"\310\001\n\023Int32CrossDataMa" +
      "pPB\022>\n\005datas\030\001 \003(\0132/.com.yorha.proto.Int" +
      "32CrossDataMapPB.DatasEntry\022\022\n\ndeleteKey" +
      "s\030\002 \003(\005\022\021\n\tclearFlag\030\003 \001(\010\032J\n\nDatasEntry" +
      "\022\013\n\003key\030\001 \001(\005\022+\n\005value\030\002 \001(\0132\034.com.yorha" +
      ".proto.CrossDataPB:\0028\001\"\343\001\n\034Int64InvitePl" +
      "ayerRecordMapPB\022G\n\005datas\030\001 \003(\01328.com.yor" +
      "ha.proto.Int64InvitePlayerRecordMapPB.Da" +
      "tasEntry\022\022\n\ndeleteKeys\030\002 \003(\003\022\021\n\tclearFla" +
      "g\030\003 \001(\010\032S\n\nDatasEntry\022\013\n\003key\030\001 \001(\003\0224\n\005va" +
      "lue\030\002 \001(\0132%.com.yorha.proto.InvitePlayer" +
      "RecordPB:\0028\001\"\321\001\n\026Int64ProgressInfoMapPB\022" +
      "A\n\005datas\030\001 \003(\01322.com.yorha.proto.Int64Pr" +
      "ogressInfoMapPB.DatasEntry\022\022\n\ndeleteKeys" +
      "\030\002 \003(\003\022\021\n\tclearFlag\030\003 \001(\010\032M\n\nDatasEntry\022" +
      "\013\n\003key\030\001 \001(\003\022.\n\005value\030\002 \001(\0132\037.com.yorha." +
      "proto.ProgressInfoPB:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_ProgressInfoPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ProgressInfoPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ProgressInfoPB_descriptor,
        new java.lang.String[] { "Uid", "LastCalNum", "MaxNum", "LastCalTsMs", "Speed", "StateEndTsMs", "StateStartTsMs", });
    internal_static_com_yorha_proto_InvitePlayerRecordPB_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_InvitePlayerRecordPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_InvitePlayerRecordPB_descriptor,
        new java.lang.String[] { "PlayerId", "InviteTsMs", });
    internal_static_com_yorha_proto_DungeonSkillSysPB_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_DungeonSkillSysPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonSkillSysPB_descriptor,
        new java.lang.String[] { "Skill", });
    internal_static_com_yorha_proto_DungeonSkillItemPB_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_DungeonSkillItemPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonSkillItemPB_descriptor,
        new java.lang.String[] { "SkillId", "CanUseNum", "CanUseTsMs", });
    internal_static_com_yorha_proto_CrossDataModelPB_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_CrossDataModelPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CrossDataModelPB_descriptor,
        new java.lang.String[] { "Data", });
    internal_static_com_yorha_proto_CrossDataPB_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_CrossDataPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CrossDataPB_descriptor,
        new java.lang.String[] { "PartId", "OwnerClanId", });
    internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32DungeonSkillItemMapPB_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Int32CrossDataMapPB_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Int32CrossDataMapPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32CrossDataMapPB_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int32CrossDataMapPB_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int32CrossDataMapPB_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int32CrossDataMapPB_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32CrossDataMapPB_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64InvitePlayerRecordMapPB_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Int64ProgressInfoMapPB_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Int64ProgressInfoMapPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64ProgressInfoMapPB_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int64ProgressInfoMapPB_DatasEntry_descriptor =
      internal_static_com_yorha_proto_Int64ProgressInfoMapPB_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Int64ProgressInfoMapPB_DatasEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64ProgressInfoMapPB_DatasEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_air_force.proto

package com.yorha.proto;

public final class PlayerAirForce {
  private PlayerAirForce() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_CreateSpyPlane_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CreateSpyPlane_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return Whether the spyInfo field is set.
     */
    boolean hasSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return The spyInfo.
     */
    com.yorha.proto.StructMsg.SpyInfo getSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     */
    com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CreateSpyPlane_C2S}
   */
  public static final class Player_CreateSpyPlane_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CreateSpyPlane_C2S)
      Player_CreateSpyPlane_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CreateSpyPlane_C2S.newBuilder() to construct.
    private Player_CreateSpyPlane_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CreateSpyPlane_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CreateSpyPlane_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CreateSpyPlane_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 50: {
              com.yorha.proto.StructMsg.SpyInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = spyInfo_.toBuilder();
              }
              spyInfo_ = input.readMessage(com.yorha.proto.StructMsg.SpyInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(spyInfo_);
                spyInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SPYINFO_FIELD_NUMBER = 6;
    private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return Whether the spyInfo field is set.
     */
    @java.lang.Override
    public boolean hasSpyInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return The spyInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(6, getSpyInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getSpyInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S other = (com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S) obj;

      if (hasSpyInfo() != other.hasSpyInfo()) return false;
      if (hasSpyInfo()) {
        if (!getSpyInfo()
            .equals(other.getSpyInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSpyInfo()) {
        hash = (37 * hash) + SPYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getSpyInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CreateSpyPlane_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CreateSpyPlane_C2S)
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSpyInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S build() {
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S result = new com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (spyInfoBuilder_ == null) {
            result.spyInfo_ = spyInfo_;
          } else {
            result.spyInfo_ = spyInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S.getDefaultInstance()) return this;
        if (other.hasSpyInfo()) {
          mergeSpyInfo(other.getSpyInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> spyInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       * @return Whether the spyInfo field is set.
       */
      public boolean hasSpyInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       * @return The spyInfo.
       */
      public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
        if (spyInfoBuilder_ == null) {
          return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        } else {
          return spyInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder setSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          spyInfo_ = value;
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder setSpyInfo(
          com.yorha.proto.StructMsg.SpyInfo.Builder builderForValue) {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = builderForValue.build();
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder mergeSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              spyInfo_ != null &&
              spyInfo_ != com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance()) {
            spyInfo_ =
              com.yorha.proto.StructMsg.SpyInfo.newBuilder(spyInfo_).mergeFrom(value).buildPartial();
          } else {
            spyInfo_ = value;
          }
          onChanged();
        } else {
          spyInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder clearSpyInfo() {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
          onChanged();
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfo.Builder getSpyInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSpyInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
        if (spyInfoBuilder_ != null) {
          return spyInfoBuilder_.getMessageOrBuilder();
        } else {
          return spyInfo_ == null ?
              com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> 
          getSpyInfoFieldBuilder() {
        if (spyInfoBuilder_ == null) {
          spyInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder>(
                  getSpyInfo(),
                  getParentForChildren(),
                  isClean());
          spyInfo_ = null;
        }
        return spyInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CreateSpyPlane_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CreateSpyPlane_C2S)
    private static final com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S();
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CreateSpyPlane_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_CreateSpyPlane_C2S>() {
      @java.lang.Override
      public Player_CreateSpyPlane_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CreateSpyPlane_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CreateSpyPlane_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CreateSpyPlane_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CreateSpyPlane_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CreateSpyPlane_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CreateSpyPlane_S2C}
   */
  public static final class Player_CreateSpyPlane_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CreateSpyPlane_S2C)
      Player_CreateSpyPlane_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CreateSpyPlane_S2C.newBuilder() to construct.
    private Player_CreateSpyPlane_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CreateSpyPlane_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CreateSpyPlane_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CreateSpyPlane_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C other = (com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CreateSpyPlane_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CreateSpyPlane_S2C)
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C build() {
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C result = new com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CreateSpyPlane_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CreateSpyPlane_S2C)
    private static final com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C();
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CreateSpyPlane_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_CreateSpyPlane_S2C>() {
      @java.lang.Override
      public Player_CreateSpyPlane_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CreateSpyPlane_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CreateSpyPlane_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CreateSpyPlane_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_CreateSpyPlane_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CheckSpy_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CheckSpy_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return Whether the targetId field is set.
     */
    boolean hasTargetId();
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CheckSpy_C2S}
   */
  public static final class Player_CheckSpy_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CheckSpy_C2S)
      Player_CheckSpy_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CheckSpy_C2S.newBuilder() to construct.
    private Player_CheckSpy_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CheckSpy_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CheckSpy_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CheckSpy_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              targetId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S.class, com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int TARGETID_FIELD_NUMBER = 1;
    private long targetId_;
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return Whether the targetId field is set.
     */
    @java.lang.Override
    public boolean hasTargetId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 targetId = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, targetId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, targetId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S other = (com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S) obj;

      if (hasTargetId() != other.hasTargetId()) return false;
      if (hasTargetId()) {
        if (getTargetId()
            != other.getTargetId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTargetId()) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTargetId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CheckSpy_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CheckSpy_C2S)
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S.class, com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        targetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S build() {
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S result = new com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S.getDefaultInstance()) return this;
        if (other.hasTargetId()) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @return Whether the targetId field is set.
       */
      @java.lang.Override
      public boolean hasTargetId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        bitField0_ |= 0x00000001;
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 targetId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CheckSpy_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CheckSpy_C2S)
    private static final com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S();
    }

    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CheckSpy_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_CheckSpy_C2S>() {
      @java.lang.Override
      public Player_CheckSpy_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CheckSpy_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CheckSpy_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CheckSpy_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_CheckSpy_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CheckSpy_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CheckSpy_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    boolean hasErrorCode();
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    int getErrorCode();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CheckSpy_S2C}
   */
  public static final class Player_CheckSpy_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CheckSpy_S2C)
      Player_CheckSpy_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CheckSpy_S2C.newBuilder() to construct.
    private Player_CheckSpy_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CheckSpy_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CheckSpy_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CheckSpy_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              errorCode_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C.class, com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int ERRORCODE_FIELD_NUMBER = 1;
    private int errorCode_;
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return Whether the errorCode field is set.
     */
    @java.lang.Override
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 errorCode = 1;</code>
     * @return The errorCode.
     */
    @java.lang.Override
    public int getErrorCode() {
      return errorCode_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, errorCode_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorCode_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C other = (com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C) obj;

      if (hasErrorCode() != other.hasErrorCode()) return false;
      if (hasErrorCode()) {
        if (getErrorCode()
            != other.getErrorCode()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasErrorCode()) {
        hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
        hash = (53 * hash) + getErrorCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CheckSpy_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CheckSpy_S2C)
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C.class, com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        errorCode_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CheckSpy_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C build() {
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C result = new com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.errorCode_ = errorCode_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C.getDefaultInstance()) return this;
        if (other.hasErrorCode()) {
          setErrorCode(other.getErrorCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int errorCode_ ;
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return Whether the errorCode field is set.
       */
      @java.lang.Override
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return The errorCode.
       */
      @java.lang.Override
      public int getErrorCode() {
        return errorCode_;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(int value) {
        bitField0_ |= 0x00000001;
        errorCode_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorCode = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorCode_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CheckSpy_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CheckSpy_S2C)
    private static final com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C();
    }

    public static com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CheckSpy_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_CheckSpy_S2C>() {
      @java.lang.Override
      public Player_CheckSpy_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CheckSpy_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CheckSpy_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CheckSpy_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_CheckSpy_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ChangeActionSpyPlane_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ChangeActionSpyPlane_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return Whether the spyInfo field is set.
     */
    boolean hasSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return The spyInfo.
     */
    com.yorha.proto.StructMsg.SpyInfo getSpyInfo();
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     */
    com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ChangeActionSpyPlane_C2S}
   */
  public static final class Player_ChangeActionSpyPlane_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ChangeActionSpyPlane_C2S)
      Player_ChangeActionSpyPlane_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ChangeActionSpyPlane_C2S.newBuilder() to construct.
    private Player_ChangeActionSpyPlane_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ChangeActionSpyPlane_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ChangeActionSpyPlane_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ChangeActionSpyPlane_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 50: {
              com.yorha.proto.StructMsg.SpyInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = spyInfo_.toBuilder();
              }
              spyInfo_ = input.readMessage(com.yorha.proto.StructMsg.SpyInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(spyInfo_);
                spyInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SPYINFO_FIELD_NUMBER = 6;
    private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return Whether the spyInfo field is set.
     */
    @java.lang.Override
    public boolean hasSpyInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     * @return The spyInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
      return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(6, getSpyInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getSpyInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S other = (com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S) obj;

      if (hasSpyInfo() != other.hasSpyInfo()) return false;
      if (hasSpyInfo()) {
        if (!getSpyInfo()
            .equals(other.getSpyInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSpyInfo()) {
        hash = (37 * hash) + SPYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getSpyInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ChangeActionSpyPlane_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ChangeActionSpyPlane_C2S)
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSpyInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S build() {
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S result = new com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (spyInfoBuilder_ == null) {
            result.spyInfo_ = spyInfo_;
          } else {
            result.spyInfo_ = spyInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S.getDefaultInstance()) return this;
        if (other.hasSpyInfo()) {
          mergeSpyInfo(other.getSpyInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMsg.SpyInfo spyInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> spyInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       * @return Whether the spyInfo field is set.
       */
      public boolean hasSpyInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       * @return The spyInfo.
       */
      public com.yorha.proto.StructMsg.SpyInfo getSpyInfo() {
        if (spyInfoBuilder_ == null) {
          return spyInfo_ == null ? com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        } else {
          return spyInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder setSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          spyInfo_ = value;
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder setSpyInfo(
          com.yorha.proto.StructMsg.SpyInfo.Builder builderForValue) {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = builderForValue.build();
          onChanged();
        } else {
          spyInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder mergeSpyInfo(com.yorha.proto.StructMsg.SpyInfo value) {
        if (spyInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              spyInfo_ != null &&
              spyInfo_ != com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance()) {
            spyInfo_ =
              com.yorha.proto.StructMsg.SpyInfo.newBuilder(spyInfo_).mergeFrom(value).buildPartial();
          } else {
            spyInfo_ = value;
          }
          onChanged();
        } else {
          spyInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public Builder clearSpyInfo() {
        if (spyInfoBuilder_ == null) {
          spyInfo_ = null;
          onChanged();
        } else {
          spyInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfo.Builder getSpyInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSpyInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      public com.yorha.proto.StructMsg.SpyInfoOrBuilder getSpyInfoOrBuilder() {
        if (spyInfoBuilder_ != null) {
          return spyInfoBuilder_.getMessageOrBuilder();
        } else {
          return spyInfo_ == null ?
              com.yorha.proto.StructMsg.SpyInfo.getDefaultInstance() : spyInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.SpyInfo spyInfo = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder> 
          getSpyInfoFieldBuilder() {
        if (spyInfoBuilder_ == null) {
          spyInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.SpyInfo, com.yorha.proto.StructMsg.SpyInfo.Builder, com.yorha.proto.StructMsg.SpyInfoOrBuilder>(
                  getSpyInfo(),
                  getParentForChildren(),
                  isClean());
          spyInfo_ = null;
        }
        return spyInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ChangeActionSpyPlane_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ChangeActionSpyPlane_C2S)
    private static final com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S();
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ChangeActionSpyPlane_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ChangeActionSpyPlane_C2S>() {
      @java.lang.Override
      public Player_ChangeActionSpyPlane_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ChangeActionSpyPlane_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ChangeActionSpyPlane_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ChangeActionSpyPlane_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ChangeActionSpyPlane_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ChangeActionSpyPlane_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ChangeActionSpyPlane_S2C}
   */
  public static final class Player_ChangeActionSpyPlane_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ChangeActionSpyPlane_S2C)
      Player_ChangeActionSpyPlane_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ChangeActionSpyPlane_S2C.newBuilder() to construct.
    private Player_ChangeActionSpyPlane_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ChangeActionSpyPlane_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ChangeActionSpyPlane_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ChangeActionSpyPlane_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C other = (com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ChangeActionSpyPlane_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ChangeActionSpyPlane_S2C)
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C build() {
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C result = new com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ChangeActionSpyPlane_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ChangeActionSpyPlane_S2C)
    private static final com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C();
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ChangeActionSpyPlane_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ChangeActionSpyPlane_S2C>() {
      @java.lang.Override
      public Player_ChangeActionSpyPlane_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ChangeActionSpyPlane_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ChangeActionSpyPlane_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ChangeActionSpyPlane_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_ChangeActionSpyPlane_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CreateLogisticsPlane_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CreateLogisticsPlane_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return The info.
     */
    com.yorha.proto.StructMsg.LogisticsInfo getInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     */
    com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CreateLogisticsPlane_C2S}
   */
  public static final class Player_CreateLogisticsPlane_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CreateLogisticsPlane_C2S)
      Player_CreateLogisticsPlane_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CreateLogisticsPlane_C2S.newBuilder() to construct.
    private Player_CreateLogisticsPlane_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CreateLogisticsPlane_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CreateLogisticsPlane_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CreateLogisticsPlane_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 58: {
              com.yorha.proto.StructMsg.LogisticsInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.yorha.proto.StructMsg.LogisticsInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 7;
    private com.yorha.proto.StructMsg.LogisticsInfo info_;
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfo getInfo() {
      return info_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getInfoOrBuilder() {
      return info_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(7, getInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S other = (com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S) obj;

      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CreateLogisticsPlane_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CreateLogisticsPlane_C2S)
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S build() {
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S result = new com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (infoBuilder_ == null) {
            result.info_ = info_;
          } else {
            result.info_ = infoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S.getDefaultInstance()) return this;
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMsg.LogisticsInfo info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> infoBuilder_;
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       * @return The info.
       */
      public com.yorha.proto.StructMsg.LogisticsInfo getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder setInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder setInfo(
          com.yorha.proto.StructMsg.LogisticsInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder mergeInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              info_ != null &&
              info_ != com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance()) {
            info_ =
              com.yorha.proto.StructMsg.LogisticsInfo.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfo.Builder getInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CreateLogisticsPlane_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CreateLogisticsPlane_C2S)
    private static final com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S();
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CreateLogisticsPlane_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_CreateLogisticsPlane_C2S>() {
      @java.lang.Override
      public Player_CreateLogisticsPlane_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CreateLogisticsPlane_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CreateLogisticsPlane_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CreateLogisticsPlane_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_CreateLogisticsPlane_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_CreateLogisticsPlane_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_CreateLogisticsPlane_S2C}
   */
  public static final class Player_CreateLogisticsPlane_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_CreateLogisticsPlane_S2C)
      Player_CreateLogisticsPlane_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_CreateLogisticsPlane_S2C.newBuilder() to construct.
    private Player_CreateLogisticsPlane_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_CreateLogisticsPlane_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_CreateLogisticsPlane_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_CreateLogisticsPlane_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C other = (com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_CreateLogisticsPlane_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_CreateLogisticsPlane_S2C)
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C build() {
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C result = new com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_CreateLogisticsPlane_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_CreateLogisticsPlane_S2C)
    private static final com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C();
    }

    public static com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_CreateLogisticsPlane_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_CreateLogisticsPlane_S2C>() {
      @java.lang.Override
      public Player_CreateLogisticsPlane_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_CreateLogisticsPlane_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_CreateLogisticsPlane_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_CreateLogisticsPlane_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_CreateLogisticsPlane_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ChangeLogisticsPlane_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ChangeLogisticsPlane_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return The info.
     */
    com.yorha.proto.StructMsg.LogisticsInfo getInfo();
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     */
    com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ChangeLogisticsPlane_C2S}
   */
  public static final class Player_ChangeLogisticsPlane_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ChangeLogisticsPlane_C2S)
      Player_ChangeLogisticsPlane_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ChangeLogisticsPlane_C2S.newBuilder() to construct.
    private Player_ChangeLogisticsPlane_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ChangeLogisticsPlane_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ChangeLogisticsPlane_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ChangeLogisticsPlane_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 58: {
              com.yorha.proto.StructMsg.LogisticsInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.yorha.proto.StructMsg.LogisticsInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 7;
    private com.yorha.proto.StructMsg.LogisticsInfo info_;
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfo getInfo() {
      return info_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
    }
    /**
     * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getInfoOrBuilder() {
      return info_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(7, getInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S other = (com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S) obj;

      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ChangeLogisticsPlane_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ChangeLogisticsPlane_C2S)
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S.class, com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S build() {
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S result = new com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (infoBuilder_ == null) {
            result.info_ = info_;
          } else {
            result.info_ = infoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S.getDefaultInstance()) return this;
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMsg.LogisticsInfo info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> infoBuilder_;
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       * @return The info.
       */
      public com.yorha.proto.StructMsg.LogisticsInfo getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder setInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder setInfo(
          com.yorha.proto.StructMsg.LogisticsInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder mergeInfo(com.yorha.proto.StructMsg.LogisticsInfo value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              info_ != null &&
              info_ != com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance()) {
            info_ =
              com.yorha.proto.StructMsg.LogisticsInfo.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfo.Builder getInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      public com.yorha.proto.StructMsg.LogisticsInfoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.yorha.proto.StructMsg.LogisticsInfo.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.LogisticsInfo info = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.LogisticsInfo, com.yorha.proto.StructMsg.LogisticsInfo.Builder, com.yorha.proto.StructMsg.LogisticsInfoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ChangeLogisticsPlane_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ChangeLogisticsPlane_C2S)
    private static final com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S();
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ChangeLogisticsPlane_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ChangeLogisticsPlane_C2S>() {
      @java.lang.Override
      public Player_ChangeLogisticsPlane_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ChangeLogisticsPlane_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ChangeLogisticsPlane_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ChangeLogisticsPlane_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ChangeLogisticsPlane_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ChangeLogisticsPlane_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ChangeLogisticsPlane_S2C}
   */
  public static final class Player_ChangeLogisticsPlane_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ChangeLogisticsPlane_S2C)
      Player_ChangeLogisticsPlane_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ChangeLogisticsPlane_S2C.newBuilder() to construct.
    private Player_ChangeLogisticsPlane_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ChangeLogisticsPlane_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ChangeLogisticsPlane_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ChangeLogisticsPlane_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C other = (com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ChangeLogisticsPlane_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ChangeLogisticsPlane_S2C)
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C.class, com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerAirForce.internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C build() {
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C buildPartial() {
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C result = new com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C) {
          return mergeFrom((com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C other) {
        if (other == com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ChangeLogisticsPlane_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ChangeLogisticsPlane_S2C)
    private static final com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C();
    }

    public static com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ChangeLogisticsPlane_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ChangeLogisticsPlane_S2C>() {
      @java.lang.Override
      public Player_ChangeLogisticsPlane_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ChangeLogisticsPlane_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ChangeLogisticsPlane_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ChangeLogisticsPlane_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerAirForce.Player_ChangeLogisticsPlane_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CheckSpy_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CheckSpy_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CheckSpy_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CheckSpy_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-ss_proto/gen/player/cs/player_air_forc" +
      "e.proto\022\017com.yorha.proto\032$ss_proto/gen/c" +
      "ommon/struct_msg.proto\"F\n\031Player_CreateS" +
      "pyPlane_C2S\022)\n\007spyInfo\030\006 \001(\0132\030.com.yorha" +
      ".proto.SpyInfo\"\033\n\031Player_CreateSpyPlane_" +
      "S2C\"\'\n\023Player_CheckSpy_C2S\022\020\n\010targetId\030\001" +
      " \001(\003\"(\n\023Player_CheckSpy_S2C\022\021\n\terrorCode" +
      "\030\001 \001(\005\"L\n\037Player_ChangeActionSpyPlane_C2" +
      "S\022)\n\007spyInfo\030\006 \001(\0132\030.com.yorha.proto.Spy" +
      "Info\"!\n\037Player_ChangeActionSpyPlane_S2C\"" +
      "O\n\037Player_CreateLogisticsPlane_C2S\022,\n\004in" +
      "fo\030\007 \001(\0132\036.com.yorha.proto.LogisticsInfo" +
      "\"!\n\037Player_CreateLogisticsPlane_S2C\"O\n\037P" +
      "layer_ChangeLogisticsPlane_C2S\022,\n\004info\030\007" +
      " \001(\0132\036.com.yorha.proto.LogisticsInfo\"!\n\037" +
      "Player_ChangeLogisticsPlane_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CreateSpyPlane_C2S_descriptor,
        new java.lang.String[] { "SpyInfo", });
    internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CreateSpyPlane_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_CheckSpy_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_CheckSpy_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CheckSpy_C2S_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_com_yorha_proto_Player_CheckSpy_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_CheckSpy_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CheckSpy_S2C_descriptor,
        new java.lang.String[] { "ErrorCode", });
    internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_C2S_descriptor,
        new java.lang.String[] { "SpyInfo", });
    internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ChangeActionSpyPlane_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CreateLogisticsPlane_C2S_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_CreateLogisticsPlane_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_C2S_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ChangeLogisticsPlane_S2C_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

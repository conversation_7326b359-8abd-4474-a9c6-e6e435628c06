// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player_clan.proto

package com.yorha.proto;

public final class SsPlayerClan {
  private SsPlayerClan() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ClanApplyResultAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanApplyResultAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <code>optional bool isAllow = 2;</code>
     * @return Whether the isAllow field is set.
     */
    boolean hasIsAllow();
    /**
     * <code>optional bool isAllow = 2;</code>
     * @return The isAllow.
     */
    boolean getIsAllow();

    /**
     * <code>optional string clanSname = 3;</code>
     * @return Whether the clanSname field is set.
     */
    boolean hasClanSname();
    /**
     * <code>optional string clanSname = 3;</code>
     * @return The clanSname.
     */
    java.lang.String getClanSname();
    /**
     * <code>optional string clanSname = 3;</code>
     * @return The bytes for clanSname.
     */
    com.google.protobuf.ByteString
        getClanSnameBytes();

    /**
     * <code>optional int64 operatorId = 4;</code>
     * @return Whether the operatorId field is set.
     */
    boolean hasOperatorId();
    /**
     * <code>optional int64 operatorId = 4;</code>
     * @return The operatorId.
     */
    long getOperatorId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanApplyResultAsk}
   */
  public static final class ClanApplyResultAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanApplyResultAsk)
      ClanApplyResultAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanApplyResultAsk.newBuilder() to construct.
    private ClanApplyResultAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanApplyResultAsk() {
      clanSname_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanApplyResultAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanApplyResultAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              isAllow_ = input.readBool();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              clanSname_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              operatorId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.ClanApplyResultAsk.class, com.yorha.proto.SsPlayerClan.ClanApplyResultAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int ISALLOW_FIELD_NUMBER = 2;
    private boolean isAllow_;
    /**
     * <code>optional bool isAllow = 2;</code>
     * @return Whether the isAllow field is set.
     */
    @java.lang.Override
    public boolean hasIsAllow() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isAllow = 2;</code>
     * @return The isAllow.
     */
    @java.lang.Override
    public boolean getIsAllow() {
      return isAllow_;
    }

    public static final int CLANSNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object clanSname_;
    /**
     * <code>optional string clanSname = 3;</code>
     * @return Whether the clanSname field is set.
     */
    @java.lang.Override
    public boolean hasClanSname() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string clanSname = 3;</code>
     * @return The clanSname.
     */
    @java.lang.Override
    public java.lang.String getClanSname() {
      java.lang.Object ref = clanSname_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSname_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string clanSname = 3;</code>
     * @return The bytes for clanSname.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSnameBytes() {
      java.lang.Object ref = clanSname_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OPERATORID_FIELD_NUMBER = 4;
    private long operatorId_;
    /**
     * <code>optional int64 operatorId = 4;</code>
     * @return Whether the operatorId field is set.
     */
    @java.lang.Override
    public boolean hasOperatorId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 operatorId = 4;</code>
     * @return The operatorId.
     */
    @java.lang.Override
    public long getOperatorId() {
      return operatorId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, isAllow_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, clanSname_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, operatorId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isAllow_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, clanSname_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, operatorId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.ClanApplyResultAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.ClanApplyResultAsk other = (com.yorha.proto.SsPlayerClan.ClanApplyResultAsk) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasIsAllow() != other.hasIsAllow()) return false;
      if (hasIsAllow()) {
        if (getIsAllow()
            != other.getIsAllow()) return false;
      }
      if (hasClanSname() != other.hasClanSname()) return false;
      if (hasClanSname()) {
        if (!getClanSname()
            .equals(other.getClanSname())) return false;
      }
      if (hasOperatorId() != other.hasOperatorId()) return false;
      if (hasOperatorId()) {
        if (getOperatorId()
            != other.getOperatorId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasIsAllow()) {
        hash = (37 * hash) + ISALLOW_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsAllow());
      }
      if (hasClanSname()) {
        hash = (37 * hash) + CLANSNAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSname().hashCode();
      }
      if (hasOperatorId()) {
        hash = (37 * hash) + OPERATORID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOperatorId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.ClanApplyResultAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanApplyResultAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanApplyResultAsk)
        com.yorha.proto.SsPlayerClan.ClanApplyResultAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.ClanApplyResultAsk.class, com.yorha.proto.SsPlayerClan.ClanApplyResultAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.ClanApplyResultAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        isAllow_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        clanSname_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        operatorId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.ClanApplyResultAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.ClanApplyResultAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.ClanApplyResultAsk build() {
        com.yorha.proto.SsPlayerClan.ClanApplyResultAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.ClanApplyResultAsk buildPartial() {
        com.yorha.proto.SsPlayerClan.ClanApplyResultAsk result = new com.yorha.proto.SsPlayerClan.ClanApplyResultAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isAllow_ = isAllow_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.clanSname_ = clanSname_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.operatorId_ = operatorId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.ClanApplyResultAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.ClanApplyResultAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.ClanApplyResultAsk other) {
        if (other == com.yorha.proto.SsPlayerClan.ClanApplyResultAsk.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasIsAllow()) {
          setIsAllow(other.getIsAllow());
        }
        if (other.hasClanSname()) {
          bitField0_ |= 0x00000004;
          clanSname_ = other.clanSname_;
          onChanged();
        }
        if (other.hasOperatorId()) {
          setOperatorId(other.getOperatorId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.ClanApplyResultAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.ClanApplyResultAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isAllow_ ;
      /**
       * <code>optional bool isAllow = 2;</code>
       * @return Whether the isAllow field is set.
       */
      @java.lang.Override
      public boolean hasIsAllow() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool isAllow = 2;</code>
       * @return The isAllow.
       */
      @java.lang.Override
      public boolean getIsAllow() {
        return isAllow_;
      }
      /**
       * <code>optional bool isAllow = 2;</code>
       * @param value The isAllow to set.
       * @return This builder for chaining.
       */
      public Builder setIsAllow(boolean value) {
        bitField0_ |= 0x00000002;
        isAllow_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isAllow = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsAllow() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isAllow_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object clanSname_ = "";
      /**
       * <code>optional string clanSname = 3;</code>
       * @return Whether the clanSname field is set.
       */
      public boolean hasClanSname() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string clanSname = 3;</code>
       * @return The clanSname.
       */
      public java.lang.String getClanSname() {
        java.lang.Object ref = clanSname_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSname_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string clanSname = 3;</code>
       * @return The bytes for clanSname.
       */
      public com.google.protobuf.ByteString
          getClanSnameBytes() {
        java.lang.Object ref = clanSname_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSname_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string clanSname = 3;</code>
       * @param value The clanSname to set.
       * @return This builder for chaining.
       */
      public Builder setClanSname(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSname_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanSname = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSname() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clanSname_ = getDefaultInstance().getClanSname();
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanSname = 3;</code>
       * @param value The bytes for clanSname to set.
       * @return This builder for chaining.
       */
      public Builder setClanSnameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSname_ = value;
        onChanged();
        return this;
      }

      private long operatorId_ ;
      /**
       * <code>optional int64 operatorId = 4;</code>
       * @return Whether the operatorId field is set.
       */
      @java.lang.Override
      public boolean hasOperatorId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int64 operatorId = 4;</code>
       * @return The operatorId.
       */
      @java.lang.Override
      public long getOperatorId() {
        return operatorId_;
      }
      /**
       * <code>optional int64 operatorId = 4;</code>
       * @param value The operatorId to set.
       * @return This builder for chaining.
       */
      public Builder setOperatorId(long value) {
        bitField0_ |= 0x00000008;
        operatorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 operatorId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperatorId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        operatorId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanApplyResultAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanApplyResultAsk)
    private static final com.yorha.proto.SsPlayerClan.ClanApplyResultAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.ClanApplyResultAsk();
    }

    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanApplyResultAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClanApplyResultAsk>() {
      @java.lang.Override
      public ClanApplyResultAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanApplyResultAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanApplyResultAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanApplyResultAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.ClanApplyResultAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanApplyResultAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanApplyResultAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional bool isSuccess = 2;</code>
     * @return Whether the isSuccess field is set.
     */
    boolean hasIsSuccess();
    /**
     * <code>optional bool isSuccess = 2;</code>
     * @return The isSuccess.
     */
    boolean getIsSuccess();

    /**
     * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
     * @return Whether the member field is set.
     */
    boolean hasMember();
    /**
     * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
     * @return The member.
     */
    com.yorha.proto.StructClan.ClanMember getMember();
    /**
     * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
     */
    com.yorha.proto.StructClan.ClanMemberOrBuilder getMemberOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanApplyResultAns}
   */
  public static final class ClanApplyResultAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanApplyResultAns)
      ClanApplyResultAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanApplyResultAns.newBuilder() to construct.
    private ClanApplyResultAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanApplyResultAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanApplyResultAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanApplyResultAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              isSuccess_ = input.readBool();
              break;
            }
            case 26: {
              com.yorha.proto.StructClan.ClanMember.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = member_.toBuilder();
              }
              member_ = input.readMessage(com.yorha.proto.StructClan.ClanMember.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(member_);
                member_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.ClanApplyResultAns.class, com.yorha.proto.SsPlayerClan.ClanApplyResultAns.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ISSUCCESS_FIELD_NUMBER = 2;
    private boolean isSuccess_;
    /**
     * <code>optional bool isSuccess = 2;</code>
     * @return Whether the isSuccess field is set.
     */
    @java.lang.Override
    public boolean hasIsSuccess() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isSuccess = 2;</code>
     * @return The isSuccess.
     */
    @java.lang.Override
    public boolean getIsSuccess() {
      return isSuccess_;
    }

    public static final int MEMBER_FIELD_NUMBER = 3;
    private com.yorha.proto.StructClan.ClanMember member_;
    /**
     * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
     * @return Whether the member field is set.
     */
    @java.lang.Override
    public boolean hasMember() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
     * @return The member.
     */
    @java.lang.Override
    public com.yorha.proto.StructClan.ClanMember getMember() {
      return member_ == null ? com.yorha.proto.StructClan.ClanMember.getDefaultInstance() : member_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructClan.ClanMemberOrBuilder getMemberOrBuilder() {
      return member_ == null ? com.yorha.proto.StructClan.ClanMember.getDefaultInstance() : member_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, isSuccess_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getMember());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isSuccess_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getMember());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.ClanApplyResultAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.ClanApplyResultAns other = (com.yorha.proto.SsPlayerClan.ClanApplyResultAns) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasIsSuccess() != other.hasIsSuccess()) return false;
      if (hasIsSuccess()) {
        if (getIsSuccess()
            != other.getIsSuccess()) return false;
      }
      if (hasMember() != other.hasMember()) return false;
      if (hasMember()) {
        if (!getMember()
            .equals(other.getMember())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasIsSuccess()) {
        hash = (37 * hash) + ISSUCCESS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsSuccess());
      }
      if (hasMember()) {
        hash = (37 * hash) + MEMBER_FIELD_NUMBER;
        hash = (53 * hash) + getMember().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.ClanApplyResultAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanApplyResultAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanApplyResultAns)
        com.yorha.proto.SsPlayerClan.ClanApplyResultAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.ClanApplyResultAns.class, com.yorha.proto.SsPlayerClan.ClanApplyResultAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.ClanApplyResultAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMemberFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        isSuccess_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (memberBuilder_ == null) {
          member_ = null;
        } else {
          memberBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_ClanApplyResultAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.ClanApplyResultAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.ClanApplyResultAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.ClanApplyResultAns build() {
        com.yorha.proto.SsPlayerClan.ClanApplyResultAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.ClanApplyResultAns buildPartial() {
        com.yorha.proto.SsPlayerClan.ClanApplyResultAns result = new com.yorha.proto.SsPlayerClan.ClanApplyResultAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isSuccess_ = isSuccess_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (memberBuilder_ == null) {
            result.member_ = member_;
          } else {
            result.member_ = memberBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.ClanApplyResultAns) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.ClanApplyResultAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.ClanApplyResultAns other) {
        if (other == com.yorha.proto.SsPlayerClan.ClanApplyResultAns.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasIsSuccess()) {
          setIsSuccess(other.getIsSuccess());
        }
        if (other.hasMember()) {
          mergeMember(other.getMember());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.ClanApplyResultAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.ClanApplyResultAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isSuccess_ ;
      /**
       * <code>optional bool isSuccess = 2;</code>
       * @return Whether the isSuccess field is set.
       */
      @java.lang.Override
      public boolean hasIsSuccess() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool isSuccess = 2;</code>
       * @return The isSuccess.
       */
      @java.lang.Override
      public boolean getIsSuccess() {
        return isSuccess_;
      }
      /**
       * <code>optional bool isSuccess = 2;</code>
       * @param value The isSuccess to set.
       * @return This builder for chaining.
       */
      public Builder setIsSuccess(boolean value) {
        bitField0_ |= 0x00000002;
        isSuccess_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isSuccess = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSuccess() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isSuccess_ = false;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructClan.ClanMember member_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClan.ClanMember, com.yorha.proto.StructClan.ClanMember.Builder, com.yorha.proto.StructClan.ClanMemberOrBuilder> memberBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       * @return Whether the member field is set.
       */
      public boolean hasMember() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       * @return The member.
       */
      public com.yorha.proto.StructClan.ClanMember getMember() {
        if (memberBuilder_ == null) {
          return member_ == null ? com.yorha.proto.StructClan.ClanMember.getDefaultInstance() : member_;
        } else {
          return memberBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       */
      public Builder setMember(com.yorha.proto.StructClan.ClanMember value) {
        if (memberBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          member_ = value;
          onChanged();
        } else {
          memberBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       */
      public Builder setMember(
          com.yorha.proto.StructClan.ClanMember.Builder builderForValue) {
        if (memberBuilder_ == null) {
          member_ = builderForValue.build();
          onChanged();
        } else {
          memberBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       */
      public Builder mergeMember(com.yorha.proto.StructClan.ClanMember value) {
        if (memberBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              member_ != null &&
              member_ != com.yorha.proto.StructClan.ClanMember.getDefaultInstance()) {
            member_ =
              com.yorha.proto.StructClan.ClanMember.newBuilder(member_).mergeFrom(value).buildPartial();
          } else {
            member_ = value;
          }
          onChanged();
        } else {
          memberBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       */
      public Builder clearMember() {
        if (memberBuilder_ == null) {
          member_ = null;
          onChanged();
        } else {
          memberBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       */
      public com.yorha.proto.StructClan.ClanMember.Builder getMemberBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getMemberFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       */
      public com.yorha.proto.StructClan.ClanMemberOrBuilder getMemberOrBuilder() {
        if (memberBuilder_ != null) {
          return memberBuilder_.getMessageOrBuilder();
        } else {
          return member_ == null ?
              com.yorha.proto.StructClan.ClanMember.getDefaultInstance() : member_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanMember member = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClan.ClanMember, com.yorha.proto.StructClan.ClanMember.Builder, com.yorha.proto.StructClan.ClanMemberOrBuilder> 
          getMemberFieldBuilder() {
        if (memberBuilder_ == null) {
          memberBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructClan.ClanMember, com.yorha.proto.StructClan.ClanMember.Builder, com.yorha.proto.StructClan.ClanMemberOrBuilder>(
                  getMember(),
                  getParentForChildren(),
                  isClean());
          member_ = null;
        }
        return memberBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanApplyResultAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanApplyResultAns)
    private static final com.yorha.proto.SsPlayerClan.ClanApplyResultAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.ClanApplyResultAns();
    }

    public static com.yorha.proto.SsPlayerClan.ClanApplyResultAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanApplyResultAns>
        PARSER = new com.google.protobuf.AbstractParser<ClanApplyResultAns>() {
      @java.lang.Override
      public ClanApplyResultAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanApplyResultAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanApplyResultAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanApplyResultAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.ClanApplyResultAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnNtfClanKickOffResultCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnNtfClanKickOffResultCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <code>optional int64 callerId = 2;</code>
     * @return Whether the callerId field is set.
     */
    boolean hasCallerId();
    /**
     * <code>optional int64 callerId = 2;</code>
     * @return The callerId.
     */
    long getCallerId();

    /**
     * <code>optional int64 kickOffTsMs = 3;</code>
     * @return Whether the kickOffTsMs field is set.
     */
    boolean hasKickOffTsMs();
    /**
     * <code>optional int64 kickOffTsMs = 3;</code>
     * @return The kickOffTsMs.
     */
    long getKickOffTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnNtfClanKickOffResultCmd}
   */
  public static final class OnNtfClanKickOffResultCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnNtfClanKickOffResultCmd)
      OnNtfClanKickOffResultCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnNtfClanKickOffResultCmd.newBuilder() to construct.
    private OnNtfClanKickOffResultCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnNtfClanKickOffResultCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnNtfClanKickOffResultCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnNtfClanKickOffResultCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              callerId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              kickOffTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd.class, com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int CALLERID_FIELD_NUMBER = 2;
    private long callerId_;
    /**
     * <code>optional int64 callerId = 2;</code>
     * @return Whether the callerId field is set.
     */
    @java.lang.Override
    public boolean hasCallerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 callerId = 2;</code>
     * @return The callerId.
     */
    @java.lang.Override
    public long getCallerId() {
      return callerId_;
    }

    public static final int KICKOFFTSMS_FIELD_NUMBER = 3;
    private long kickOffTsMs_;
    /**
     * <code>optional int64 kickOffTsMs = 3;</code>
     * @return Whether the kickOffTsMs field is set.
     */
    @java.lang.Override
    public boolean hasKickOffTsMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 kickOffTsMs = 3;</code>
     * @return The kickOffTsMs.
     */
    @java.lang.Override
    public long getKickOffTsMs() {
      return kickOffTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, callerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, kickOffTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, callerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, kickOffTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd other = (com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasCallerId() != other.hasCallerId()) return false;
      if (hasCallerId()) {
        if (getCallerId()
            != other.getCallerId()) return false;
      }
      if (hasKickOffTsMs() != other.hasKickOffTsMs()) return false;
      if (hasKickOffTsMs()) {
        if (getKickOffTsMs()
            != other.getKickOffTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasCallerId()) {
        hash = (37 * hash) + CALLERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCallerId());
      }
      if (hasKickOffTsMs()) {
        hash = (37 * hash) + KICKOFFTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getKickOffTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnNtfClanKickOffResultCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnNtfClanKickOffResultCmd)
        com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd.class, com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        callerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        kickOffTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd build() {
        com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd buildPartial() {
        com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd result = new com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.callerId_ = callerId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.kickOffTsMs_ = kickOffTsMs_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd other) {
        if (other == com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasCallerId()) {
          setCallerId(other.getCallerId());
        }
        if (other.hasKickOffTsMs()) {
          setKickOffTsMs(other.getKickOffTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private long callerId_ ;
      /**
       * <code>optional int64 callerId = 2;</code>
       * @return Whether the callerId field is set.
       */
      @java.lang.Override
      public boolean hasCallerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 callerId = 2;</code>
       * @return The callerId.
       */
      @java.lang.Override
      public long getCallerId() {
        return callerId_;
      }
      /**
       * <code>optional int64 callerId = 2;</code>
       * @param value The callerId to set.
       * @return This builder for chaining.
       */
      public Builder setCallerId(long value) {
        bitField0_ |= 0x00000002;
        callerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 callerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCallerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        callerId_ = 0L;
        onChanged();
        return this;
      }

      private long kickOffTsMs_ ;
      /**
       * <code>optional int64 kickOffTsMs = 3;</code>
       * @return Whether the kickOffTsMs field is set.
       */
      @java.lang.Override
      public boolean hasKickOffTsMs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 kickOffTsMs = 3;</code>
       * @return The kickOffTsMs.
       */
      @java.lang.Override
      public long getKickOffTsMs() {
        return kickOffTsMs_;
      }
      /**
       * <code>optional int64 kickOffTsMs = 3;</code>
       * @param value The kickOffTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setKickOffTsMs(long value) {
        bitField0_ |= 0x00000004;
        kickOffTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 kickOffTsMs = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearKickOffTsMs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        kickOffTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnNtfClanKickOffResultCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnNtfClanKickOffResultCmd)
    private static final com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd();
    }

    public static com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnNtfClanKickOffResultCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnNtfClanKickOffResultCmd>() {
      @java.lang.Override
      public OnNtfClanKickOffResultCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnNtfClanKickOffResultCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnNtfClanKickOffResultCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnNtfClanKickOffResultCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnNtfClanKickOffResultCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnClanAdditionUpdateCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnClanAdditionUpdateCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
     * @return Whether the source field is set.
     */
    boolean hasSource();
    /**
     * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
     * @return The source.
     */
    com.yorha.proto.CommonEnum.AdditionSourceType getSource();

    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */
    int getAdditionsCount();
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */
    boolean containsAdditions(
        int key);
    /**
     * Use {@link #getAdditionsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Long>
    getAdditions();
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Long>
    getAdditionsMap();
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */

    long getAdditionsOrDefault(
        int key,
        long defaultValue);
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */

    long getAdditionsOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnClanAdditionUpdateCmd}
   */
  public static final class OnClanAdditionUpdateCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnClanAdditionUpdateCmd)
      OnClanAdditionUpdateCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnClanAdditionUpdateCmd.newBuilder() to construct.
    private OnClanAdditionUpdateCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnClanAdditionUpdateCmd() {
      source_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnClanAdditionUpdateCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnClanAdditionUpdateCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.AdditionSourceType value = com.yorha.proto.CommonEnum.AdditionSourceType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                source_ = rawValue;
              }
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                additions_ = com.google.protobuf.MapField.newMapField(
                    AdditionsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
              additions__ = input.readMessage(
                  AdditionsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              additions_.getMutableMap().put(
                  additions__.getKey(), additions__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetAdditions();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd.class, com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int SOURCE_FIELD_NUMBER = 2;
    private int source_;
    /**
     * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
     * @return Whether the source field is set.
     */
    @java.lang.Override public boolean hasSource() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
     * @return The source.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.AdditionSourceType getSource() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.AdditionSourceType result = com.yorha.proto.CommonEnum.AdditionSourceType.valueOf(source_);
      return result == null ? com.yorha.proto.CommonEnum.AdditionSourceType.AST_INNER_NONE : result;
    }

    public static final int ADDITIONS_FIELD_NUMBER = 3;
    private static final class AdditionsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Long> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Long>newDefaultInstance(
                  com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_AdditionsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Long> additions_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
    internalGetAdditions() {
      if (additions_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            AdditionsDefaultEntryHolder.defaultEntry);
      }
      return additions_;
    }

    public int getAdditionsCount() {
      return internalGetAdditions().getMap().size();
    }
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */

    @java.lang.Override
    public boolean containsAdditions(
        int key) {
      
      return internalGetAdditions().getMap().containsKey(key);
    }
    /**
     * Use {@link #getAdditionsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Long> getAdditions() {
      return getAdditionsMap();
    }
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Long> getAdditionsMap() {
      return internalGetAdditions().getMap();
    }
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */
    @java.lang.Override

    public long getAdditionsOrDefault(
        int key,
        long defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetAdditions().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, int64&gt; additions = 3;</code>
     */
    @java.lang.Override

    public long getAdditionsOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Long> map =
          internalGetAdditions().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, source_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetAdditions(),
          AdditionsDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, source_);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> entry
           : internalGetAdditions().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Long>
        additions__ = AdditionsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, additions__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd other = (com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasSource() != other.hasSource()) return false;
      if (hasSource()) {
        if (source_ != other.source_) return false;
      }
      if (!internalGetAdditions().equals(
          other.internalGetAdditions())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasSource()) {
        hash = (37 * hash) + SOURCE_FIELD_NUMBER;
        hash = (53 * hash) + source_;
      }
      if (!internalGetAdditions().getMap().isEmpty()) {
        hash = (37 * hash) + ADDITIONS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetAdditions().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnClanAdditionUpdateCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnClanAdditionUpdateCmd)
        com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetAdditions();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableAdditions();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd.class, com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        source_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        internalGetMutableAdditions().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd build() {
        com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd buildPartial() {
        com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd result = new com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.source_ = source_;
        result.additions_ = internalGetAdditions();
        result.additions_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd other) {
        if (other == com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasSource()) {
          setSource(other.getSource());
        }
        internalGetMutableAdditions().mergeFrom(
            other.internalGetAdditions());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private int source_ = 0;
      /**
       * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
       * @return Whether the source field is set.
       */
      @java.lang.Override public boolean hasSource() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
       * @return The source.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.AdditionSourceType getSource() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.AdditionSourceType result = com.yorha.proto.CommonEnum.AdditionSourceType.valueOf(source_);
        return result == null ? com.yorha.proto.CommonEnum.AdditionSourceType.AST_INNER_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(com.yorha.proto.CommonEnum.AdditionSourceType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        source_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AdditionSourceType source = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField0_ = (bitField0_ & ~0x00000002);
        source_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Long> additions_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetAdditions() {
        if (additions_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              AdditionsDefaultEntryHolder.defaultEntry);
        }
        return additions_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Long>
      internalGetMutableAdditions() {
        onChanged();;
        if (additions_ == null) {
          additions_ = com.google.protobuf.MapField.newMapField(
              AdditionsDefaultEntryHolder.defaultEntry);
        }
        if (!additions_.isMutable()) {
          additions_ = additions_.copy();
        }
        return additions_;
      }

      public int getAdditionsCount() {
        return internalGetAdditions().getMap().size();
      }
      /**
       * <code>map&lt;int32, int64&gt; additions = 3;</code>
       */

      @java.lang.Override
      public boolean containsAdditions(
          int key) {
        
        return internalGetAdditions().getMap().containsKey(key);
      }
      /**
       * Use {@link #getAdditionsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long> getAdditions() {
        return getAdditionsMap();
      }
      /**
       * <code>map&lt;int32, int64&gt; additions = 3;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Long> getAdditionsMap() {
        return internalGetAdditions().getMap();
      }
      /**
       * <code>map&lt;int32, int64&gt; additions = 3;</code>
       */
      @java.lang.Override

      public long getAdditionsOrDefault(
          int key,
          long defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetAdditions().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, int64&gt; additions = 3;</code>
       */
      @java.lang.Override

      public long getAdditionsOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Long> map =
            internalGetAdditions().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearAdditions() {
        internalGetMutableAdditions().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, int64&gt; additions = 3;</code>
       */

      public Builder removeAdditions(
          int key) {
        
        internalGetMutableAdditions().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Long>
      getMutableAdditions() {
        return internalGetMutableAdditions().getMutableMap();
      }
      /**
       * <code>map&lt;int32, int64&gt; additions = 3;</code>
       */
      public Builder putAdditions(
          int key,
          long value) {
        
        
        internalGetMutableAdditions().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, int64&gt; additions = 3;</code>
       */

      public Builder putAllAdditions(
          java.util.Map<java.lang.Integer, java.lang.Long> values) {
        internalGetMutableAdditions().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnClanAdditionUpdateCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnClanAdditionUpdateCmd)
    private static final com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd();
    }

    public static com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnClanAdditionUpdateCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnClanAdditionUpdateCmd>() {
      @java.lang.Override
      public OnClanAdditionUpdateCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnClanAdditionUpdateCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnClanAdditionUpdateCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnClanAdditionUpdateCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnClanAdditionUpdateCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnClanDevBuffUpdateCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnClanDevBuffUpdateCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <code>optional bool isAdd = 2;</code>
     * @return Whether the isAdd field is set.
     */
    boolean hasIsAdd();
    /**
     * <code>optional bool isAdd = 2;</code>
     * @return The isAdd.
     */
    boolean getIsAdd();

    /**
     * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
     * @return Whether the devBuffProp field is set.
     */
    boolean hasDevBuffProp();
    /**
     * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
     * @return The devBuffProp.
     */
    com.yorha.proto.CommonMsg.DevBuffAddParam getDevBuffProp();
    /**
     * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
     */
    com.yorha.proto.CommonMsg.DevBuffAddParamOrBuilder getDevBuffPropOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnClanDevBuffUpdateCmd}
   */
  public static final class OnClanDevBuffUpdateCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnClanDevBuffUpdateCmd)
      OnClanDevBuffUpdateCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnClanDevBuffUpdateCmd.newBuilder() to construct.
    private OnClanDevBuffUpdateCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnClanDevBuffUpdateCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnClanDevBuffUpdateCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnClanDevBuffUpdateCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              isAdd_ = input.readBool();
              break;
            }
            case 26: {
              com.yorha.proto.CommonMsg.DevBuffAddParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = devBuffProp_.toBuilder();
              }
              devBuffProp_ = input.readMessage(com.yorha.proto.CommonMsg.DevBuffAddParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(devBuffProp_);
                devBuffProp_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd.class, com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int ISADD_FIELD_NUMBER = 2;
    private boolean isAdd_;
    /**
     * <code>optional bool isAdd = 2;</code>
     * @return Whether the isAdd field is set.
     */
    @java.lang.Override
    public boolean hasIsAdd() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bool isAdd = 2;</code>
     * @return The isAdd.
     */
    @java.lang.Override
    public boolean getIsAdd() {
      return isAdd_;
    }

    public static final int DEVBUFFPROP_FIELD_NUMBER = 3;
    private com.yorha.proto.CommonMsg.DevBuffAddParam devBuffProp_;
    /**
     * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
     * @return Whether the devBuffProp field is set.
     */
    @java.lang.Override
    public boolean hasDevBuffProp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
     * @return The devBuffProp.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.DevBuffAddParam getDevBuffProp() {
      return devBuffProp_ == null ? com.yorha.proto.CommonMsg.DevBuffAddParam.getDefaultInstance() : devBuffProp_;
    }
    /**
     * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.DevBuffAddParamOrBuilder getDevBuffPropOrBuilder() {
      return devBuffProp_ == null ? com.yorha.proto.CommonMsg.DevBuffAddParam.getDefaultInstance() : devBuffProp_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, isAdd_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getDevBuffProp());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isAdd_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getDevBuffProp());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd other = (com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasIsAdd() != other.hasIsAdd()) return false;
      if (hasIsAdd()) {
        if (getIsAdd()
            != other.getIsAdd()) return false;
      }
      if (hasDevBuffProp() != other.hasDevBuffProp()) return false;
      if (hasDevBuffProp()) {
        if (!getDevBuffProp()
            .equals(other.getDevBuffProp())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasIsAdd()) {
        hash = (37 * hash) + ISADD_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsAdd());
      }
      if (hasDevBuffProp()) {
        hash = (37 * hash) + DEVBUFFPROP_FIELD_NUMBER;
        hash = (53 * hash) + getDevBuffProp().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnClanDevBuffUpdateCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnClanDevBuffUpdateCmd)
        com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd.class, com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDevBuffPropFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        isAdd_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (devBuffPropBuilder_ == null) {
          devBuffProp_ = null;
        } else {
          devBuffPropBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd build() {
        com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd buildPartial() {
        com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd result = new com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isAdd_ = isAdd_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (devBuffPropBuilder_ == null) {
            result.devBuffProp_ = devBuffProp_;
          } else {
            result.devBuffProp_ = devBuffPropBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd other) {
        if (other == com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasIsAdd()) {
          setIsAdd(other.getIsAdd());
        }
        if (other.hasDevBuffProp()) {
          mergeDevBuffProp(other.getDevBuffProp());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isAdd_ ;
      /**
       * <code>optional bool isAdd = 2;</code>
       * @return Whether the isAdd field is set.
       */
      @java.lang.Override
      public boolean hasIsAdd() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bool isAdd = 2;</code>
       * @return The isAdd.
       */
      @java.lang.Override
      public boolean getIsAdd() {
        return isAdd_;
      }
      /**
       * <code>optional bool isAdd = 2;</code>
       * @param value The isAdd to set.
       * @return This builder for chaining.
       */
      public Builder setIsAdd(boolean value) {
        bitField0_ |= 0x00000002;
        isAdd_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isAdd = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsAdd() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isAdd_ = false;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.DevBuffAddParam devBuffProp_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.DevBuffAddParam, com.yorha.proto.CommonMsg.DevBuffAddParam.Builder, com.yorha.proto.CommonMsg.DevBuffAddParamOrBuilder> devBuffPropBuilder_;
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       * @return Whether the devBuffProp field is set.
       */
      public boolean hasDevBuffProp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       * @return The devBuffProp.
       */
      public com.yorha.proto.CommonMsg.DevBuffAddParam getDevBuffProp() {
        if (devBuffPropBuilder_ == null) {
          return devBuffProp_ == null ? com.yorha.proto.CommonMsg.DevBuffAddParam.getDefaultInstance() : devBuffProp_;
        } else {
          return devBuffPropBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       */
      public Builder setDevBuffProp(com.yorha.proto.CommonMsg.DevBuffAddParam value) {
        if (devBuffPropBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          devBuffProp_ = value;
          onChanged();
        } else {
          devBuffPropBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       */
      public Builder setDevBuffProp(
          com.yorha.proto.CommonMsg.DevBuffAddParam.Builder builderForValue) {
        if (devBuffPropBuilder_ == null) {
          devBuffProp_ = builderForValue.build();
          onChanged();
        } else {
          devBuffPropBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       */
      public Builder mergeDevBuffProp(com.yorha.proto.CommonMsg.DevBuffAddParam value) {
        if (devBuffPropBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              devBuffProp_ != null &&
              devBuffProp_ != com.yorha.proto.CommonMsg.DevBuffAddParam.getDefaultInstance()) {
            devBuffProp_ =
              com.yorha.proto.CommonMsg.DevBuffAddParam.newBuilder(devBuffProp_).mergeFrom(value).buildPartial();
          } else {
            devBuffProp_ = value;
          }
          onChanged();
        } else {
          devBuffPropBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       */
      public Builder clearDevBuffProp() {
        if (devBuffPropBuilder_ == null) {
          devBuffProp_ = null;
          onChanged();
        } else {
          devBuffPropBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       */
      public com.yorha.proto.CommonMsg.DevBuffAddParam.Builder getDevBuffPropBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getDevBuffPropFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       */
      public com.yorha.proto.CommonMsg.DevBuffAddParamOrBuilder getDevBuffPropOrBuilder() {
        if (devBuffPropBuilder_ != null) {
          return devBuffPropBuilder_.getMessageOrBuilder();
        } else {
          return devBuffProp_ == null ?
              com.yorha.proto.CommonMsg.DevBuffAddParam.getDefaultInstance() : devBuffProp_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.DevBuffAddParam devBuffProp = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.DevBuffAddParam, com.yorha.proto.CommonMsg.DevBuffAddParam.Builder, com.yorha.proto.CommonMsg.DevBuffAddParamOrBuilder> 
          getDevBuffPropFieldBuilder() {
        if (devBuffPropBuilder_ == null) {
          devBuffPropBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.DevBuffAddParam, com.yorha.proto.CommonMsg.DevBuffAddParam.Builder, com.yorha.proto.CommonMsg.DevBuffAddParamOrBuilder>(
                  getDevBuffProp(),
                  getParentForChildren(),
                  isClean());
          devBuffProp_ = null;
        }
        return devBuffPropBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnClanDevBuffUpdateCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnClanDevBuffUpdateCmd)
    private static final com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd();
    }

    public static com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnClanDevBuffUpdateCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnClanDevBuffUpdateCmd>() {
      @java.lang.Override
      public OnClanDevBuffUpdateCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnClanDevBuffUpdateCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnClanDevBuffUpdateCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnClanDevBuffUpdateCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnClanDevBuffUpdateCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnAddClanScoreCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnAddClanScoreCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
     * @return Whether the scoreType field is set.
     */
    boolean hasScoreType();
    /**
     * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
     * @return The scoreType.
     */
    com.yorha.proto.CommonEnum.ClanScoreCategory getScoreType();

    /**
     * <code>optional int64 addValue = 2;</code>
     * @return Whether the addValue field is set.
     */
    boolean hasAddValue();
    /**
     * <code>optional int64 addValue = 2;</code>
     * @return The addValue.
     */
    long getAddValue();

    /**
     * <pre>
     * 获取分数经历的间隔，单位秒
     * </pre>
     *
     * <code>optional int32 scoreUseInterval = 3;</code>
     * @return Whether the scoreUseInterval field is set.
     */
    boolean hasScoreUseInterval();
    /**
     * <pre>
     * 获取分数经历的间隔，单位秒
     * </pre>
     *
     * <code>optional int32 scoreUseInterval = 3;</code>
     * @return The scoreUseInterval.
     */
    int getScoreUseInterval();

    /**
     * <pre>
     * 占领时是否正处在据点中，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool inBuildingWhenOccupy = 4;</code>
     * @return Whether the inBuildingWhenOccupy field is set.
     */
    boolean hasInBuildingWhenOccupy();
    /**
     * <pre>
     * 占领时是否正处在据点中，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool inBuildingWhenOccupy = 4;</code>
     * @return The inBuildingWhenOccupy.
     */
    boolean getInBuildingWhenOccupy();

    /**
     * <pre>
     * 是占领获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isOccupyAddScore = 5;</code>
     * @return Whether the isOccupyAddScore field is set.
     */
    boolean hasIsOccupyAddScore();
    /**
     * <pre>
     * 是占领获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isOccupyAddScore = 5;</code>
     * @return The isOccupyAddScore.
     */
    boolean getIsOccupyAddScore();

    /**
     * <pre>
     * 是建设获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isBuildAddScore = 6;</code>
     * @return Whether the isBuildAddScore field is set.
     */
    boolean hasIsBuildAddScore();
    /**
     * <pre>
     * 是建设获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isBuildAddScore = 6;</code>
     * @return The isBuildAddScore.
     */
    boolean getIsBuildAddScore();

    /**
     * <pre>
     * 联盟建筑建设完成
     * </pre>
     *
     * <code>optional bool isFinishBuild = 7;</code>
     * @return Whether the isFinishBuild field is set.
     */
    boolean hasIsFinishBuild();
    /**
     * <pre>
     * 联盟建筑建设完成
     * </pre>
     *
     * <code>optional bool isFinishBuild = 7;</code>
     * @return The isFinishBuild.
     */
    boolean getIsFinishBuild();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnAddClanScoreCmd}
   */
  public static final class OnAddClanScoreCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnAddClanScoreCmd)
      OnAddClanScoreCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnAddClanScoreCmd.newBuilder() to construct.
    private OnAddClanScoreCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnAddClanScoreCmd() {
      scoreType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnAddClanScoreCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnAddClanScoreCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanScoreCategory value = com.yorha.proto.CommonEnum.ClanScoreCategory.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                scoreType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              addValue_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              scoreUseInterval_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              inBuildingWhenOccupy_ = input.readBool();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              isOccupyAddScore_ = input.readBool();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              isBuildAddScore_ = input.readBool();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              isFinishBuild_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanScoreCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanScoreCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd.class, com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd.Builder.class);
    }

    private int bitField0_;
    public static final int SCORETYPE_FIELD_NUMBER = 1;
    private int scoreType_;
    /**
     * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
     * @return Whether the scoreType field is set.
     */
    @java.lang.Override public boolean hasScoreType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
     * @return The scoreType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanScoreCategory getScoreType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanScoreCategory result = com.yorha.proto.CommonEnum.ClanScoreCategory.valueOf(scoreType_);
      return result == null ? com.yorha.proto.CommonEnum.ClanScoreCategory.CSC_NONE : result;
    }

    public static final int ADDVALUE_FIELD_NUMBER = 2;
    private long addValue_;
    /**
     * <code>optional int64 addValue = 2;</code>
     * @return Whether the addValue field is set.
     */
    @java.lang.Override
    public boolean hasAddValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 addValue = 2;</code>
     * @return The addValue.
     */
    @java.lang.Override
    public long getAddValue() {
      return addValue_;
    }

    public static final int SCOREUSEINTERVAL_FIELD_NUMBER = 3;
    private int scoreUseInterval_;
    /**
     * <pre>
     * 获取分数经历的间隔，单位秒
     * </pre>
     *
     * <code>optional int32 scoreUseInterval = 3;</code>
     * @return Whether the scoreUseInterval field is set.
     */
    @java.lang.Override
    public boolean hasScoreUseInterval() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 获取分数经历的间隔，单位秒
     * </pre>
     *
     * <code>optional int32 scoreUseInterval = 3;</code>
     * @return The scoreUseInterval.
     */
    @java.lang.Override
    public int getScoreUseInterval() {
      return scoreUseInterval_;
    }

    public static final int INBUILDINGWHENOCCUPY_FIELD_NUMBER = 4;
    private boolean inBuildingWhenOccupy_;
    /**
     * <pre>
     * 占领时是否正处在据点中，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool inBuildingWhenOccupy = 4;</code>
     * @return Whether the inBuildingWhenOccupy field is set.
     */
    @java.lang.Override
    public boolean hasInBuildingWhenOccupy() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 占领时是否正处在据点中，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool inBuildingWhenOccupy = 4;</code>
     * @return The inBuildingWhenOccupy.
     */
    @java.lang.Override
    public boolean getInBuildingWhenOccupy() {
      return inBuildingWhenOccupy_;
    }

    public static final int ISOCCUPYADDSCORE_FIELD_NUMBER = 5;
    private boolean isOccupyAddScore_;
    /**
     * <pre>
     * 是占领获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isOccupyAddScore = 5;</code>
     * @return Whether the isOccupyAddScore field is set.
     */
    @java.lang.Override
    public boolean hasIsOccupyAddScore() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 是占领获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isOccupyAddScore = 5;</code>
     * @return The isOccupyAddScore.
     */
    @java.lang.Override
    public boolean getIsOccupyAddScore() {
      return isOccupyAddScore_;
    }

    public static final int ISBUILDADDSCORE_FIELD_NUMBER = 6;
    private boolean isBuildAddScore_;
    /**
     * <pre>
     * 是建设获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isBuildAddScore = 6;</code>
     * @return Whether the isBuildAddScore field is set.
     */
    @java.lang.Override
    public boolean hasIsBuildAddScore() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 是建设获取的积分，其他情况不会设置该值
     * </pre>
     *
     * <code>optional bool isBuildAddScore = 6;</code>
     * @return The isBuildAddScore.
     */
    @java.lang.Override
    public boolean getIsBuildAddScore() {
      return isBuildAddScore_;
    }

    public static final int ISFINISHBUILD_FIELD_NUMBER = 7;
    private boolean isFinishBuild_;
    /**
     * <pre>
     * 联盟建筑建设完成
     * </pre>
     *
     * <code>optional bool isFinishBuild = 7;</code>
     * @return Whether the isFinishBuild field is set.
     */
    @java.lang.Override
    public boolean hasIsFinishBuild() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 联盟建筑建设完成
     * </pre>
     *
     * <code>optional bool isFinishBuild = 7;</code>
     * @return The isFinishBuild.
     */
    @java.lang.Override
    public boolean getIsFinishBuild() {
      return isFinishBuild_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, scoreType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, addValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, scoreUseInterval_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, inBuildingWhenOccupy_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(5, isOccupyAddScore_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBool(6, isBuildAddScore_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeBool(7, isFinishBuild_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, scoreType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, addValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, scoreUseInterval_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, inBuildingWhenOccupy_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, isOccupyAddScore_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(6, isBuildAddScore_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, isFinishBuild_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd other = (com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd) obj;

      if (hasScoreType() != other.hasScoreType()) return false;
      if (hasScoreType()) {
        if (scoreType_ != other.scoreType_) return false;
      }
      if (hasAddValue() != other.hasAddValue()) return false;
      if (hasAddValue()) {
        if (getAddValue()
            != other.getAddValue()) return false;
      }
      if (hasScoreUseInterval() != other.hasScoreUseInterval()) return false;
      if (hasScoreUseInterval()) {
        if (getScoreUseInterval()
            != other.getScoreUseInterval()) return false;
      }
      if (hasInBuildingWhenOccupy() != other.hasInBuildingWhenOccupy()) return false;
      if (hasInBuildingWhenOccupy()) {
        if (getInBuildingWhenOccupy()
            != other.getInBuildingWhenOccupy()) return false;
      }
      if (hasIsOccupyAddScore() != other.hasIsOccupyAddScore()) return false;
      if (hasIsOccupyAddScore()) {
        if (getIsOccupyAddScore()
            != other.getIsOccupyAddScore()) return false;
      }
      if (hasIsBuildAddScore() != other.hasIsBuildAddScore()) return false;
      if (hasIsBuildAddScore()) {
        if (getIsBuildAddScore()
            != other.getIsBuildAddScore()) return false;
      }
      if (hasIsFinishBuild() != other.hasIsFinishBuild()) return false;
      if (hasIsFinishBuild()) {
        if (getIsFinishBuild()
            != other.getIsFinishBuild()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasScoreType()) {
        hash = (37 * hash) + SCORETYPE_FIELD_NUMBER;
        hash = (53 * hash) + scoreType_;
      }
      if (hasAddValue()) {
        hash = (37 * hash) + ADDVALUE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getAddValue());
      }
      if (hasScoreUseInterval()) {
        hash = (37 * hash) + SCOREUSEINTERVAL_FIELD_NUMBER;
        hash = (53 * hash) + getScoreUseInterval();
      }
      if (hasInBuildingWhenOccupy()) {
        hash = (37 * hash) + INBUILDINGWHENOCCUPY_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getInBuildingWhenOccupy());
      }
      if (hasIsOccupyAddScore()) {
        hash = (37 * hash) + ISOCCUPYADDSCORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsOccupyAddScore());
      }
      if (hasIsBuildAddScore()) {
        hash = (37 * hash) + ISBUILDADDSCORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsBuildAddScore());
      }
      if (hasIsFinishBuild()) {
        hash = (37 * hash) + ISFINISHBUILD_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsFinishBuild());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnAddClanScoreCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnAddClanScoreCmd)
        com.yorha.proto.SsPlayerClan.OnAddClanScoreCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanScoreCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanScoreCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd.class, com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        scoreType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        addValue_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        scoreUseInterval_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        inBuildingWhenOccupy_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        isOccupyAddScore_ = false;
        bitField0_ = (bitField0_ & ~0x00000010);
        isBuildAddScore_ = false;
        bitField0_ = (bitField0_ & ~0x00000020);
        isFinishBuild_ = false;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanScoreCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd build() {
        com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd buildPartial() {
        com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd result = new com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.scoreType_ = scoreType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.addValue_ = addValue_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.scoreUseInterval_ = scoreUseInterval_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.inBuildingWhenOccupy_ = inBuildingWhenOccupy_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.isOccupyAddScore_ = isOccupyAddScore_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.isBuildAddScore_ = isBuildAddScore_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.isFinishBuild_ = isFinishBuild_;
          to_bitField0_ |= 0x00000040;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd other) {
        if (other == com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd.getDefaultInstance()) return this;
        if (other.hasScoreType()) {
          setScoreType(other.getScoreType());
        }
        if (other.hasAddValue()) {
          setAddValue(other.getAddValue());
        }
        if (other.hasScoreUseInterval()) {
          setScoreUseInterval(other.getScoreUseInterval());
        }
        if (other.hasInBuildingWhenOccupy()) {
          setInBuildingWhenOccupy(other.getInBuildingWhenOccupy());
        }
        if (other.hasIsOccupyAddScore()) {
          setIsOccupyAddScore(other.getIsOccupyAddScore());
        }
        if (other.hasIsBuildAddScore()) {
          setIsBuildAddScore(other.getIsBuildAddScore());
        }
        if (other.hasIsFinishBuild()) {
          setIsFinishBuild(other.getIsFinishBuild());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int scoreType_ = 0;
      /**
       * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
       * @return Whether the scoreType field is set.
       */
      @java.lang.Override public boolean hasScoreType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
       * @return The scoreType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanScoreCategory getScoreType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanScoreCategory result = com.yorha.proto.CommonEnum.ClanScoreCategory.valueOf(scoreType_);
        return result == null ? com.yorha.proto.CommonEnum.ClanScoreCategory.CSC_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
       * @param value The scoreType to set.
       * @return This builder for chaining.
       */
      public Builder setScoreType(com.yorha.proto.CommonEnum.ClanScoreCategory value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        scoreType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanScoreCategory scoreType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearScoreType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        scoreType_ = 0;
        onChanged();
        return this;
      }

      private long addValue_ ;
      /**
       * <code>optional int64 addValue = 2;</code>
       * @return Whether the addValue field is set.
       */
      @java.lang.Override
      public boolean hasAddValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 addValue = 2;</code>
       * @return The addValue.
       */
      @java.lang.Override
      public long getAddValue() {
        return addValue_;
      }
      /**
       * <code>optional int64 addValue = 2;</code>
       * @param value The addValue to set.
       * @return This builder for chaining.
       */
      public Builder setAddValue(long value) {
        bitField0_ |= 0x00000002;
        addValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 addValue = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        addValue_ = 0L;
        onChanged();
        return this;
      }

      private int scoreUseInterval_ ;
      /**
       * <pre>
       * 获取分数经历的间隔，单位秒
       * </pre>
       *
       * <code>optional int32 scoreUseInterval = 3;</code>
       * @return Whether the scoreUseInterval field is set.
       */
      @java.lang.Override
      public boolean hasScoreUseInterval() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 获取分数经历的间隔，单位秒
       * </pre>
       *
       * <code>optional int32 scoreUseInterval = 3;</code>
       * @return The scoreUseInterval.
       */
      @java.lang.Override
      public int getScoreUseInterval() {
        return scoreUseInterval_;
      }
      /**
       * <pre>
       * 获取分数经历的间隔，单位秒
       * </pre>
       *
       * <code>optional int32 scoreUseInterval = 3;</code>
       * @param value The scoreUseInterval to set.
       * @return This builder for chaining.
       */
      public Builder setScoreUseInterval(int value) {
        bitField0_ |= 0x00000004;
        scoreUseInterval_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 获取分数经历的间隔，单位秒
       * </pre>
       *
       * <code>optional int32 scoreUseInterval = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearScoreUseInterval() {
        bitField0_ = (bitField0_ & ~0x00000004);
        scoreUseInterval_ = 0;
        onChanged();
        return this;
      }

      private boolean inBuildingWhenOccupy_ ;
      /**
       * <pre>
       * 占领时是否正处在据点中，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool inBuildingWhenOccupy = 4;</code>
       * @return Whether the inBuildingWhenOccupy field is set.
       */
      @java.lang.Override
      public boolean hasInBuildingWhenOccupy() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 占领时是否正处在据点中，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool inBuildingWhenOccupy = 4;</code>
       * @return The inBuildingWhenOccupy.
       */
      @java.lang.Override
      public boolean getInBuildingWhenOccupy() {
        return inBuildingWhenOccupy_;
      }
      /**
       * <pre>
       * 占领时是否正处在据点中，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool inBuildingWhenOccupy = 4;</code>
       * @param value The inBuildingWhenOccupy to set.
       * @return This builder for chaining.
       */
      public Builder setInBuildingWhenOccupy(boolean value) {
        bitField0_ |= 0x00000008;
        inBuildingWhenOccupy_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 占领时是否正处在据点中，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool inBuildingWhenOccupy = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearInBuildingWhenOccupy() {
        bitField0_ = (bitField0_ & ~0x00000008);
        inBuildingWhenOccupy_ = false;
        onChanged();
        return this;
      }

      private boolean isOccupyAddScore_ ;
      /**
       * <pre>
       * 是占领获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isOccupyAddScore = 5;</code>
       * @return Whether the isOccupyAddScore field is set.
       */
      @java.lang.Override
      public boolean hasIsOccupyAddScore() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 是占领获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isOccupyAddScore = 5;</code>
       * @return The isOccupyAddScore.
       */
      @java.lang.Override
      public boolean getIsOccupyAddScore() {
        return isOccupyAddScore_;
      }
      /**
       * <pre>
       * 是占领获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isOccupyAddScore = 5;</code>
       * @param value The isOccupyAddScore to set.
       * @return This builder for chaining.
       */
      public Builder setIsOccupyAddScore(boolean value) {
        bitField0_ |= 0x00000010;
        isOccupyAddScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是占领获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isOccupyAddScore = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsOccupyAddScore() {
        bitField0_ = (bitField0_ & ~0x00000010);
        isOccupyAddScore_ = false;
        onChanged();
        return this;
      }

      private boolean isBuildAddScore_ ;
      /**
       * <pre>
       * 是建设获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isBuildAddScore = 6;</code>
       * @return Whether the isBuildAddScore field is set.
       */
      @java.lang.Override
      public boolean hasIsBuildAddScore() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 是建设获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isBuildAddScore = 6;</code>
       * @return The isBuildAddScore.
       */
      @java.lang.Override
      public boolean getIsBuildAddScore() {
        return isBuildAddScore_;
      }
      /**
       * <pre>
       * 是建设获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isBuildAddScore = 6;</code>
       * @param value The isBuildAddScore to set.
       * @return This builder for chaining.
       */
      public Builder setIsBuildAddScore(boolean value) {
        bitField0_ |= 0x00000020;
        isBuildAddScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是建设获取的积分，其他情况不会设置该值
       * </pre>
       *
       * <code>optional bool isBuildAddScore = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsBuildAddScore() {
        bitField0_ = (bitField0_ & ~0x00000020);
        isBuildAddScore_ = false;
        onChanged();
        return this;
      }

      private boolean isFinishBuild_ ;
      /**
       * <pre>
       * 联盟建筑建设完成
       * </pre>
       *
       * <code>optional bool isFinishBuild = 7;</code>
       * @return Whether the isFinishBuild field is set.
       */
      @java.lang.Override
      public boolean hasIsFinishBuild() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 联盟建筑建设完成
       * </pre>
       *
       * <code>optional bool isFinishBuild = 7;</code>
       * @return The isFinishBuild.
       */
      @java.lang.Override
      public boolean getIsFinishBuild() {
        return isFinishBuild_;
      }
      /**
       * <pre>
       * 联盟建筑建设完成
       * </pre>
       *
       * <code>optional bool isFinishBuild = 7;</code>
       * @param value The isFinishBuild to set.
       * @return This builder for chaining.
       */
      public Builder setIsFinishBuild(boolean value) {
        bitField0_ |= 0x00000040;
        isFinishBuild_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建设完成
       * </pre>
       *
       * <code>optional bool isFinishBuild = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsFinishBuild() {
        bitField0_ = (bitField0_ & ~0x00000040);
        isFinishBuild_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnAddClanScoreCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnAddClanScoreCmd)
    private static final com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd();
    }

    public static com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnAddClanScoreCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnAddClanScoreCmd>() {
      @java.lang.Override
      public OnAddClanScoreCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnAddClanScoreCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnAddClanScoreCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnAddClanScoreCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnAddClanScoreCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnAddClanPowerResourceCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnAddClanPowerResourceCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */
    int getResourceCount();
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */
    boolean containsResource(
        int key);
    /**
     * Use {@link #getResourceMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getResource();
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getResourceMap();
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */

    int getResourceOrDefault(
        int key,
        int defaultValue);
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */

    int getResourceOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnAddClanPowerResourceCmd}
   */
  public static final class OnAddClanPowerResourceCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnAddClanPowerResourceCmd)
      OnAddClanPowerResourceCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnAddClanPowerResourceCmd.newBuilder() to construct.
    private OnAddClanPowerResourceCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnAddClanPowerResourceCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnAddClanPowerResourceCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnAddClanPowerResourceCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                resource_ = com.google.protobuf.MapField.newMapField(
                    ResourceDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              resource__ = input.readMessage(
                  ResourceDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              resource_.getMutableMap().put(
                  resource__.getKey(), resource__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetResource();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd.class, com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd.Builder.class);
    }

    public static final int RESOURCE_FIELD_NUMBER = 1;
    private static final class ResourceDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_ResourceEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> resource_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetResource() {
      if (resource_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ResourceDefaultEntryHolder.defaultEntry);
      }
      return resource_;
    }

    public int getResourceCount() {
      return internalGetResource().getMap().size();
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */

    @java.lang.Override
    public boolean containsResource(
        int key) {
      
      return internalGetResource().getMap().containsKey(key);
    }
    /**
     * Use {@link #getResourceMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getResource() {
      return getResourceMap();
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getResourceMap() {
      return internalGetResource().getMap();
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */
    @java.lang.Override

    public int getResourceOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetResource().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, int32&gt; resource = 1;</code>
     */
    @java.lang.Override

    public int getResourceOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetResource().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetResource(),
          ResourceDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetResource().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        resource__ = ResourceDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, resource__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd other = (com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd) obj;

      if (!internalGetResource().equals(
          other.internalGetResource())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetResource().getMap().isEmpty()) {
        hash = (37 * hash) + RESOURCE_FIELD_NUMBER;
        hash = (53 * hash) + internalGetResource().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnAddClanPowerResourceCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnAddClanPowerResourceCmd)
        com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetResource();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableResource();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd.class, com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableResource().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd build() {
        com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd buildPartial() {
        com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd result = new com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd(this);
        int from_bitField0_ = bitField0_;
        result.resource_ = internalGetResource();
        result.resource_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd other) {
        if (other == com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd.getDefaultInstance()) return this;
        internalGetMutableResource().mergeFrom(
            other.internalGetResource());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> resource_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetResource() {
        if (resource_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ResourceDefaultEntryHolder.defaultEntry);
        }
        return resource_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableResource() {
        onChanged();;
        if (resource_ == null) {
          resource_ = com.google.protobuf.MapField.newMapField(
              ResourceDefaultEntryHolder.defaultEntry);
        }
        if (!resource_.isMutable()) {
          resource_ = resource_.copy();
        }
        return resource_;
      }

      public int getResourceCount() {
        return internalGetResource().getMap().size();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 1;</code>
       */

      @java.lang.Override
      public boolean containsResource(
          int key) {
        
        return internalGetResource().getMap().containsKey(key);
      }
      /**
       * Use {@link #getResourceMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getResource() {
        return getResourceMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getResourceMap() {
        return internalGetResource().getMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 1;</code>
       */
      @java.lang.Override

      public int getResourceOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetResource().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 1;</code>
       */
      @java.lang.Override

      public int getResourceOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetResource().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearResource() {
        internalGetMutableResource().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 1;</code>
       */

      public Builder removeResource(
          int key) {
        
        internalGetMutableResource().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableResource() {
        return internalGetMutableResource().getMutableMap();
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 1;</code>
       */
      public Builder putResource(
          int key,
          int value) {
        
        
        internalGetMutableResource().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, int32&gt; resource = 1;</code>
       */

      public Builder putAllResource(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableResource().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnAddClanPowerResourceCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnAddClanPowerResourceCmd)
    private static final com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd();
    }

    public static com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnAddClanPowerResourceCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnAddClanPowerResourceCmd>() {
      @java.lang.Override
      public OnAddClanPowerResourceCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnAddClanPowerResourceCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnAddClanPowerResourceCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnAddClanPowerResourceCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnAddClanPowerResourceCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnClanHelpHappenAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnClanHelpHappenAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 需要被加速的队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return Whether the queueId field is set.
     */
    boolean hasQueueId();
    /**
     * <pre>
     * 需要被加速的队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return The queueId.
     */
    long getQueueId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnClanHelpHappenAsk}
   */
  public static final class OnClanHelpHappenAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnClanHelpHappenAsk)
      OnClanHelpHappenAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnClanHelpHappenAsk.newBuilder() to construct.
    private OnClanHelpHappenAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnClanHelpHappenAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnClanHelpHappenAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnClanHelpHappenAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              queueId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk.class, com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk.Builder.class);
    }

    private int bitField0_;
    public static final int QUEUEID_FIELD_NUMBER = 1;
    private long queueId_;
    /**
     * <pre>
     * 需要被加速的队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return Whether the queueId field is set.
     */
    @java.lang.Override
    public boolean hasQueueId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 需要被加速的队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return The queueId.
     */
    @java.lang.Override
    public long getQueueId() {
      return queueId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, queueId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, queueId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk other = (com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk) obj;

      if (hasQueueId() != other.hasQueueId()) return false;
      if (hasQueueId()) {
        if (getQueueId()
            != other.getQueueId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQueueId()) {
        hash = (37 * hash) + QUEUEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getQueueId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnClanHelpHappenAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnClanHelpHappenAsk)
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk.class, com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        queueId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk build() {
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk buildPartial() {
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk result = new com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.queueId_ = queueId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk other) {
        if (other == com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk.getDefaultInstance()) return this;
        if (other.hasQueueId()) {
          setQueueId(other.getQueueId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long queueId_ ;
      /**
       * <pre>
       * 需要被加速的队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @return Whether the queueId field is set.
       */
      @java.lang.Override
      public boolean hasQueueId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 需要被加速的队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @return The queueId.
       */
      @java.lang.Override
      public long getQueueId() {
        return queueId_;
      }
      /**
       * <pre>
       * 需要被加速的队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @param value The queueId to set.
       * @return This builder for chaining.
       */
      public Builder setQueueId(long value) {
        bitField0_ |= 0x00000001;
        queueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 需要被加速的队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueueId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        queueId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnClanHelpHappenAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnClanHelpHappenAsk)
    private static final com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk();
    }

    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnClanHelpHappenAsk>
        PARSER = new com.google.protobuf.AbstractParser<OnClanHelpHappenAsk>() {
      @java.lang.Override
      public OnClanHelpHappenAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnClanHelpHappenAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnClanHelpHappenAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnClanHelpHappenAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnClanHelpHappenAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnClanHelpHappenAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 本次减少的时间
     * </pre>
     *
     * <code>optional int64 reduceTime = 1;</code>
     * @return Whether the reduceTime field is set.
     */
    boolean hasReduceTime();
    /**
     * <pre>
     * 本次减少的时间
     * </pre>
     *
     * <code>optional int64 reduceTime = 1;</code>
     * @return The reduceTime.
     */
    long getReduceTime();

    /**
     * <pre>
     * 是否成功减少时间
     * </pre>
     *
     * <code>optional bool isSuccessReduce = 3;</code>
     * @return Whether the isSuccessReduce field is set.
     */
    boolean hasIsSuccessReduce();
    /**
     * <pre>
     * 是否成功减少时间
     * </pre>
     *
     * <code>optional bool isSuccessReduce = 3;</code>
     * @return The isSuccessReduce.
     */
    boolean getIsSuccessReduce();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnClanHelpHappenAns}
   */
  public static final class OnClanHelpHappenAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnClanHelpHappenAns)
      OnClanHelpHappenAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnClanHelpHappenAns.newBuilder() to construct.
    private OnClanHelpHappenAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnClanHelpHappenAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnClanHelpHappenAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnClanHelpHappenAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              reduceTime_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              isSuccessReduce_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns.class, com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns.Builder.class);
    }

    private int bitField0_;
    public static final int REDUCETIME_FIELD_NUMBER = 1;
    private long reduceTime_;
    /**
     * <pre>
     * 本次减少的时间
     * </pre>
     *
     * <code>optional int64 reduceTime = 1;</code>
     * @return Whether the reduceTime field is set.
     */
    @java.lang.Override
    public boolean hasReduceTime() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 本次减少的时间
     * </pre>
     *
     * <code>optional int64 reduceTime = 1;</code>
     * @return The reduceTime.
     */
    @java.lang.Override
    public long getReduceTime() {
      return reduceTime_;
    }

    public static final int ISSUCCESSREDUCE_FIELD_NUMBER = 3;
    private boolean isSuccessReduce_;
    /**
     * <pre>
     * 是否成功减少时间
     * </pre>
     *
     * <code>optional bool isSuccessReduce = 3;</code>
     * @return Whether the isSuccessReduce field is set.
     */
    @java.lang.Override
    public boolean hasIsSuccessReduce() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否成功减少时间
     * </pre>
     *
     * <code>optional bool isSuccessReduce = 3;</code>
     * @return The isSuccessReduce.
     */
    @java.lang.Override
    public boolean getIsSuccessReduce() {
      return isSuccessReduce_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, reduceTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, isSuccessReduce_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, reduceTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, isSuccessReduce_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns other = (com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns) obj;

      if (hasReduceTime() != other.hasReduceTime()) return false;
      if (hasReduceTime()) {
        if (getReduceTime()
            != other.getReduceTime()) return false;
      }
      if (hasIsSuccessReduce() != other.hasIsSuccessReduce()) return false;
      if (hasIsSuccessReduce()) {
        if (getIsSuccessReduce()
            != other.getIsSuccessReduce()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReduceTime()) {
        hash = (37 * hash) + REDUCETIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getReduceTime());
      }
      if (hasIsSuccessReduce()) {
        hash = (37 * hash) + ISSUCCESSREDUCE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsSuccessReduce());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnClanHelpHappenAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnClanHelpHappenAns)
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns.class, com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        reduceTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        isSuccessReduce_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanHelpHappenAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns build() {
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns buildPartial() {
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns result = new com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.reduceTime_ = reduceTime_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isSuccessReduce_ = isSuccessReduce_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns other) {
        if (other == com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns.getDefaultInstance()) return this;
        if (other.hasReduceTime()) {
          setReduceTime(other.getReduceTime());
        }
        if (other.hasIsSuccessReduce()) {
          setIsSuccessReduce(other.getIsSuccessReduce());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long reduceTime_ ;
      /**
       * <pre>
       * 本次减少的时间
       * </pre>
       *
       * <code>optional int64 reduceTime = 1;</code>
       * @return Whether the reduceTime field is set.
       */
      @java.lang.Override
      public boolean hasReduceTime() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 本次减少的时间
       * </pre>
       *
       * <code>optional int64 reduceTime = 1;</code>
       * @return The reduceTime.
       */
      @java.lang.Override
      public long getReduceTime() {
        return reduceTime_;
      }
      /**
       * <pre>
       * 本次减少的时间
       * </pre>
       *
       * <code>optional int64 reduceTime = 1;</code>
       * @param value The reduceTime to set.
       * @return This builder for chaining.
       */
      public Builder setReduceTime(long value) {
        bitField0_ |= 0x00000001;
        reduceTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 本次减少的时间
       * </pre>
       *
       * <code>optional int64 reduceTime = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearReduceTime() {
        bitField0_ = (bitField0_ & ~0x00000001);
        reduceTime_ = 0L;
        onChanged();
        return this;
      }

      private boolean isSuccessReduce_ ;
      /**
       * <pre>
       * 是否成功减少时间
       * </pre>
       *
       * <code>optional bool isSuccessReduce = 3;</code>
       * @return Whether the isSuccessReduce field is set.
       */
      @java.lang.Override
      public boolean hasIsSuccessReduce() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 是否成功减少时间
       * </pre>
       *
       * <code>optional bool isSuccessReduce = 3;</code>
       * @return The isSuccessReduce.
       */
      @java.lang.Override
      public boolean getIsSuccessReduce() {
        return isSuccessReduce_;
      }
      /**
       * <pre>
       * 是否成功减少时间
       * </pre>
       *
       * <code>optional bool isSuccessReduce = 3;</code>
       * @param value The isSuccessReduce to set.
       * @return This builder for chaining.
       */
      public Builder setIsSuccessReduce(boolean value) {
        bitField0_ |= 0x00000002;
        isSuccessReduce_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否成功减少时间
       * </pre>
       *
       * <code>optional bool isSuccessReduce = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSuccessReduce() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isSuccessReduce_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnClanHelpHappenAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnClanHelpHappenAns)
    private static final com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns();
    }

    public static com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnClanHelpHappenAns>
        PARSER = new com.google.protobuf.AbstractParser<OnClanHelpHappenAns>() {
      @java.lang.Override
      public OnClanHelpHappenAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnClanHelpHappenAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnClanHelpHappenAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnClanHelpHappenAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnClanHelpHappenAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnPlayerNeedClanInfoChangeCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnPlayerNeedClanInfoChangeCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 staffId = 1;</code>
     * @return Whether the staffId field is set.
     */
    boolean hasStaffId();
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 staffId = 1;</code>
     * @return The staffId.
     */
    int getStaffId();

    /**
     * <pre>
     * 红点数据，新增或删除，目前仅用于申请红点的变动同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
     * @return Whether the redDotData field is set.
     */
    boolean hasRedDotData();
    /**
     * <pre>
     * 红点数据，新增或删除，目前仅用于申请红点的变动同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
     * @return The redDotData.
     */
    com.yorha.proto.CommonMsg.UpdateRedDotData getRedDotData();
    /**
     * <pre>
     * 红点数据，新增或删除，目前仅用于申请红点的变动同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
     */
    com.yorha.proto.CommonMsg.UpdateRedDotDataOrBuilder getRedDotDataOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnPlayerNeedClanInfoChangeCmd}
   */
  public static final class OnPlayerNeedClanInfoChangeCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnPlayerNeedClanInfoChangeCmd)
      OnPlayerNeedClanInfoChangeCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnPlayerNeedClanInfoChangeCmd.newBuilder() to construct.
    private OnPlayerNeedClanInfoChangeCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnPlayerNeedClanInfoChangeCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnPlayerNeedClanInfoChangeCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnPlayerNeedClanInfoChangeCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              staffId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.UpdateRedDotData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = redDotData_.toBuilder();
              }
              redDotData_ = input.readMessage(com.yorha.proto.CommonMsg.UpdateRedDotData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(redDotData_);
                redDotData_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.class, com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.Builder.class);
    }

    private int bitField0_;
    public static final int STAFFID_FIELD_NUMBER = 1;
    private int staffId_;
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 staffId = 1;</code>
     * @return Whether the staffId field is set.
     */
    @java.lang.Override
    public boolean hasStaffId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 staffId = 1;</code>
     * @return The staffId.
     */
    @java.lang.Override
    public int getStaffId() {
      return staffId_;
    }

    public static final int REDDOTDATA_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.UpdateRedDotData redDotData_;
    /**
     * <pre>
     * 红点数据，新增或删除，目前仅用于申请红点的变动同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
     * @return Whether the redDotData field is set.
     */
    @java.lang.Override
    public boolean hasRedDotData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 红点数据，新增或删除，目前仅用于申请红点的变动同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
     * @return The redDotData.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.UpdateRedDotData getRedDotData() {
      return redDotData_ == null ? com.yorha.proto.CommonMsg.UpdateRedDotData.getDefaultInstance() : redDotData_;
    }
    /**
     * <pre>
     * 红点数据，新增或删除，目前仅用于申请红点的变动同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.UpdateRedDotDataOrBuilder getRedDotDataOrBuilder() {
      return redDotData_ == null ? com.yorha.proto.CommonMsg.UpdateRedDotData.getDefaultInstance() : redDotData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, staffId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getRedDotData());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, staffId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getRedDotData());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd other = (com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd) obj;

      if (hasStaffId() != other.hasStaffId()) return false;
      if (hasStaffId()) {
        if (getStaffId()
            != other.getStaffId()) return false;
      }
      if (hasRedDotData() != other.hasRedDotData()) return false;
      if (hasRedDotData()) {
        if (!getRedDotData()
            .equals(other.getRedDotData())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasStaffId()) {
        hash = (37 * hash) + STAFFID_FIELD_NUMBER;
        hash = (53 * hash) + getStaffId();
      }
      if (hasRedDotData()) {
        hash = (37 * hash) + REDDOTDATA_FIELD_NUMBER;
        hash = (53 * hash) + getRedDotData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnPlayerNeedClanInfoChangeCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnPlayerNeedClanInfoChangeCmd)
        com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.class, com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRedDotDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        staffId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (redDotDataBuilder_ == null) {
          redDotData_ = null;
        } else {
          redDotDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd build() {
        com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd buildPartial() {
        com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd result = new com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.staffId_ = staffId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (redDotDataBuilder_ == null) {
            result.redDotData_ = redDotData_;
          } else {
            result.redDotData_ = redDotDataBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd other) {
        if (other == com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.getDefaultInstance()) return this;
        if (other.hasStaffId()) {
          setStaffId(other.getStaffId());
        }
        if (other.hasRedDotData()) {
          mergeRedDotData(other.getRedDotData());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int staffId_ ;
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 staffId = 1;</code>
       * @return Whether the staffId field is set.
       */
      @java.lang.Override
      public boolean hasStaffId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 staffId = 1;</code>
       * @return The staffId.
       */
      @java.lang.Override
      public int getStaffId() {
        return staffId_;
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 staffId = 1;</code>
       * @param value The staffId to set.
       * @return This builder for chaining.
       */
      public Builder setStaffId(int value) {
        bitField0_ |= 0x00000001;
        staffId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 staffId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStaffId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        staffId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.UpdateRedDotData redDotData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.UpdateRedDotData, com.yorha.proto.CommonMsg.UpdateRedDotData.Builder, com.yorha.proto.CommonMsg.UpdateRedDotDataOrBuilder> redDotDataBuilder_;
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       * @return Whether the redDotData field is set.
       */
      public boolean hasRedDotData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       * @return The redDotData.
       */
      public com.yorha.proto.CommonMsg.UpdateRedDotData getRedDotData() {
        if (redDotDataBuilder_ == null) {
          return redDotData_ == null ? com.yorha.proto.CommonMsg.UpdateRedDotData.getDefaultInstance() : redDotData_;
        } else {
          return redDotDataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       */
      public Builder setRedDotData(com.yorha.proto.CommonMsg.UpdateRedDotData value) {
        if (redDotDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          redDotData_ = value;
          onChanged();
        } else {
          redDotDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       */
      public Builder setRedDotData(
          com.yorha.proto.CommonMsg.UpdateRedDotData.Builder builderForValue) {
        if (redDotDataBuilder_ == null) {
          redDotData_ = builderForValue.build();
          onChanged();
        } else {
          redDotDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       */
      public Builder mergeRedDotData(com.yorha.proto.CommonMsg.UpdateRedDotData value) {
        if (redDotDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              redDotData_ != null &&
              redDotData_ != com.yorha.proto.CommonMsg.UpdateRedDotData.getDefaultInstance()) {
            redDotData_ =
              com.yorha.proto.CommonMsg.UpdateRedDotData.newBuilder(redDotData_).mergeFrom(value).buildPartial();
          } else {
            redDotData_ = value;
          }
          onChanged();
        } else {
          redDotDataBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       */
      public Builder clearRedDotData() {
        if (redDotDataBuilder_ == null) {
          redDotData_ = null;
          onChanged();
        } else {
          redDotDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       */
      public com.yorha.proto.CommonMsg.UpdateRedDotData.Builder getRedDotDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getRedDotDataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       */
      public com.yorha.proto.CommonMsg.UpdateRedDotDataOrBuilder getRedDotDataOrBuilder() {
        if (redDotDataBuilder_ != null) {
          return redDotDataBuilder_.getMessageOrBuilder();
        } else {
          return redDotData_ == null ?
              com.yorha.proto.CommonMsg.UpdateRedDotData.getDefaultInstance() : redDotData_;
        }
      }
      /**
       * <pre>
       * 红点数据，新增或删除，目前仅用于申请红点的变动同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.UpdateRedDotData redDotData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.UpdateRedDotData, com.yorha.proto.CommonMsg.UpdateRedDotData.Builder, com.yorha.proto.CommonMsg.UpdateRedDotDataOrBuilder> 
          getRedDotDataFieldBuilder() {
        if (redDotDataBuilder_ == null) {
          redDotDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.UpdateRedDotData, com.yorha.proto.CommonMsg.UpdateRedDotData.Builder, com.yorha.proto.CommonMsg.UpdateRedDotDataOrBuilder>(
                  getRedDotData(),
                  getParentForChildren(),
                  isClean());
          redDotData_ = null;
        }
        return redDotDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnPlayerNeedClanInfoChangeCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnPlayerNeedClanInfoChangeCmd)
    private static final com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd();
    }

    public static com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnPlayerNeedClanInfoChangeCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnPlayerNeedClanInfoChangeCmd>() {
      @java.lang.Override
      public OnPlayerNeedClanInfoChangeCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnPlayerNeedClanInfoChangeCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnPlayerNeedClanInfoChangeCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnPlayerNeedClanInfoChangeCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnPlayerNeedClanInfoChangeCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnClanTerritoryLvChangeCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnClanTerritoryLvChangeCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 新的势力值等级
     * </pre>
     *
     * <code>optional int32 newTerritoryLv = 1;</code>
     * @return Whether the newTerritoryLv field is set.
     */
    boolean hasNewTerritoryLv();
    /**
     * <pre>
     * 新的势力值等级
     * </pre>
     *
     * <code>optional int32 newTerritoryLv = 1;</code>
     * @return The newTerritoryLv.
     */
    int getNewTerritoryLv();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnClanTerritoryLvChangeCmd}
   */
  public static final class OnClanTerritoryLvChangeCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnClanTerritoryLvChangeCmd)
      OnClanTerritoryLvChangeCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnClanTerritoryLvChangeCmd.newBuilder() to construct.
    private OnClanTerritoryLvChangeCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnClanTerritoryLvChangeCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnClanTerritoryLvChangeCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnClanTerritoryLvChangeCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              newTerritoryLv_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd.class, com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd.Builder.class);
    }

    private int bitField0_;
    public static final int NEWTERRITORYLV_FIELD_NUMBER = 1;
    private int newTerritoryLv_;
    /**
     * <pre>
     * 新的势力值等级
     * </pre>
     *
     * <code>optional int32 newTerritoryLv = 1;</code>
     * @return Whether the newTerritoryLv field is set.
     */
    @java.lang.Override
    public boolean hasNewTerritoryLv() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 新的势力值等级
     * </pre>
     *
     * <code>optional int32 newTerritoryLv = 1;</code>
     * @return The newTerritoryLv.
     */
    @java.lang.Override
    public int getNewTerritoryLv() {
      return newTerritoryLv_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, newTerritoryLv_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, newTerritoryLv_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd other = (com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd) obj;

      if (hasNewTerritoryLv() != other.hasNewTerritoryLv()) return false;
      if (hasNewTerritoryLv()) {
        if (getNewTerritoryLv()
            != other.getNewTerritoryLv()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNewTerritoryLv()) {
        hash = (37 * hash) + NEWTERRITORYLV_FIELD_NUMBER;
        hash = (53 * hash) + getNewTerritoryLv();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnClanTerritoryLvChangeCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnClanTerritoryLvChangeCmd)
        com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd.class, com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        newTerritoryLv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerClan.internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd build() {
        com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd buildPartial() {
        com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd result = new com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.newTerritoryLv_ = newTerritoryLv_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd other) {
        if (other == com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd.getDefaultInstance()) return this;
        if (other.hasNewTerritoryLv()) {
          setNewTerritoryLv(other.getNewTerritoryLv());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int newTerritoryLv_ ;
      /**
       * <pre>
       * 新的势力值等级
       * </pre>
       *
       * <code>optional int32 newTerritoryLv = 1;</code>
       * @return Whether the newTerritoryLv field is set.
       */
      @java.lang.Override
      public boolean hasNewTerritoryLv() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 新的势力值等级
       * </pre>
       *
       * <code>optional int32 newTerritoryLv = 1;</code>
       * @return The newTerritoryLv.
       */
      @java.lang.Override
      public int getNewTerritoryLv() {
        return newTerritoryLv_;
      }
      /**
       * <pre>
       * 新的势力值等级
       * </pre>
       *
       * <code>optional int32 newTerritoryLv = 1;</code>
       * @param value The newTerritoryLv to set.
       * @return This builder for chaining.
       */
      public Builder setNewTerritoryLv(int value) {
        bitField0_ |= 0x00000001;
        newTerritoryLv_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 新的势力值等级
       * </pre>
       *
       * <code>optional int32 newTerritoryLv = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewTerritoryLv() {
        bitField0_ = (bitField0_ & ~0x00000001);
        newTerritoryLv_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnClanTerritoryLvChangeCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnClanTerritoryLvChangeCmd)
    private static final com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd();
    }

    public static com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnClanTerritoryLvChangeCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnClanTerritoryLvChangeCmd>() {
      @java.lang.Override
      public OnClanTerritoryLvChangeCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnClanTerritoryLvChangeCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnClanTerritoryLvChangeCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnClanTerritoryLvChangeCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerClan.OnClanTerritoryLvChangeCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanApplyResultAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanApplyResultAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanApplyResultAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanApplyResultAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_AdditionsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_AdditionsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnAddClanScoreCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnAddClanScoreCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_ResourceEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_ResourceEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanHelpHappenAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanHelpHappenAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanHelpHappenAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanHelpHappenAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/player/ss/ss_player_clan." +
      "proto\022\017com.yorha.proto\032%ss_proto/gen/com" +
      "mon/common_enum.proto\032$ss_proto/gen/comm" +
      "on/common_msg.proto\032%ss_proto/gen/common" +
      "/struct_clan.proto\"\\\n\022ClanApplyResultAsk" +
      "\022\016\n\006clanId\030\001 \001(\003\022\017\n\007isAllow\030\002 \001(\010\022\021\n\tcla" +
      "nSname\030\003 \001(\t\022\022\n\noperatorId\030\004 \001(\003\"f\n\022Clan" +
      "ApplyResultAns\022\020\n\010playerId\030\001 \001(\003\022\021\n\tisSu" +
      "ccess\030\002 \001(\010\022+\n\006member\030\003 \001(\0132\033.com.yorha." +
      "proto.ClanMember\"R\n\031OnNtfClanKickOffResu" +
      "ltCmd\022\016\n\006clanId\030\001 \001(\003\022\020\n\010callerId\030\002 \001(\003\022" +
      "\023\n\013kickOffTsMs\030\003 \001(\003\"\334\001\n\027OnClanAdditionU" +
      "pdateCmd\022\016\n\006clanId\030\001 \001(\003\0223\n\006source\030\002 \001(\016" +
      "2#.com.yorha.proto.AdditionSourceType\022J\n" +
      "\tadditions\030\003 \003(\01327.com.yorha.proto.OnCla" +
      "nAdditionUpdateCmd.AdditionsEntry\0320\n\016Add" +
      "itionsEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\003:" +
      "\0028\001\"n\n\026OnClanDevBuffUpdateCmd\022\016\n\006clanId\030" +
      "\001 \001(\003\022\r\n\005isAdd\030\002 \001(\010\0225\n\013devBuffProp\030\003 \001(" +
      "\0132 .com.yorha.proto.DevBuffAddParam\"\336\001\n\021" +
      "OnAddClanScoreCmd\0225\n\tscoreType\030\001 \001(\0162\".c" +
      "om.yorha.proto.ClanScoreCategory\022\020\n\010addV" +
      "alue\030\002 \001(\003\022\030\n\020scoreUseInterval\030\003 \001(\005\022\034\n\024" +
      "inBuildingWhenOccupy\030\004 \001(\010\022\030\n\020isOccupyAd" +
      "dScore\030\005 \001(\010\022\027\n\017isBuildAddScore\030\006 \001(\010\022\025\n" +
      "\risFinishBuild\030\007 \001(\010\"\230\001\n\031OnAddClanPowerR" +
      "esourceCmd\022J\n\010resource\030\001 \003(\01328.com.yorha" +
      ".proto.OnAddClanPowerResourceCmd.Resourc" +
      "eEntry\032/\n\rResourceEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005" +
      "value\030\002 \001(\005:\0028\001\"&\n\023OnClanHelpHappenAsk\022\017" +
      "\n\007queueId\030\001 \001(\003\"B\n\023OnClanHelpHappenAns\022\022" +
      "\n\nreduceTime\030\001 \001(\003\022\027\n\017isSuccessReduce\030\003 " +
      "\001(\010\"g\n\035OnPlayerNeedClanInfoChangeCmd\022\017\n\007" +
      "staffId\030\001 \001(\005\0225\n\nredDotData\030\002 \001(\0132!.com." +
      "yorha.proto.UpdateRedDotData\"4\n\032OnClanTe" +
      "rritoryLvChangeCmd\022\026\n\016newTerritoryLv\030\001 \001" +
      "(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
          com.yorha.proto.StructClan.getDescriptor(),
        });
    internal_static_com_yorha_proto_ClanApplyResultAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ClanApplyResultAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanApplyResultAsk_descriptor,
        new java.lang.String[] { "ClanId", "IsAllow", "ClanSname", "OperatorId", });
    internal_static_com_yorha_proto_ClanApplyResultAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_ClanApplyResultAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanApplyResultAns_descriptor,
        new java.lang.String[] { "PlayerId", "IsSuccess", "Member", });
    internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnNtfClanKickOffResultCmd_descriptor,
        new java.lang.String[] { "ClanId", "CallerId", "KickOffTsMs", });
    internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_descriptor,
        new java.lang.String[] { "ClanId", "Source", "Additions", });
    internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_AdditionsEntry_descriptor =
      internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_AdditionsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanAdditionUpdateCmd_AdditionsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanDevBuffUpdateCmd_descriptor,
        new java.lang.String[] { "ClanId", "IsAdd", "DevBuffProp", });
    internal_static_com_yorha_proto_OnAddClanScoreCmd_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_OnAddClanScoreCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnAddClanScoreCmd_descriptor,
        new java.lang.String[] { "ScoreType", "AddValue", "ScoreUseInterval", "InBuildingWhenOccupy", "IsOccupyAddScore", "IsBuildAddScore", "IsFinishBuild", });
    internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_descriptor,
        new java.lang.String[] { "Resource", });
    internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_ResourceEntry_descriptor =
      internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_ResourceEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnAddClanPowerResourceCmd_ResourceEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_OnClanHelpHappenAsk_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_OnClanHelpHappenAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanHelpHappenAsk_descriptor,
        new java.lang.String[] { "QueueId", });
    internal_static_com_yorha_proto_OnClanHelpHappenAns_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_OnClanHelpHappenAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanHelpHappenAns_descriptor,
        new java.lang.String[] { "ReduceTime", "IsSuccessReduce", });
    internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnPlayerNeedClanInfoChangeCmd_descriptor,
        new java.lang.String[] { "StaffId", "RedDotData", });
    internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnClanTerritoryLvChangeCmd_descriptor,
        new java.lang.String[] { "NewTerritoryLv", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
    com.yorha.proto.StructClan.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_skynet.proto

package com.yorha.proto;

public final class PlayerSkynet {
  private PlayerSkynet() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_SkynetDoingTask_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetDoingTask_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return Whether the skynetModelType field is set.
     */
    boolean hasSkynetModelType();
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return The skynetModelType.
     */
    com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType();

    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    boolean hasSkynetTaskId();
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    int getSkynetTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetDoingTask_C2S}
   */
  public static final class Player_SkynetDoingTask_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetDoingTask_C2S)
      Player_SkynetDoingTask_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetDoingTask_C2S.newBuilder() to construct.
    private Player_SkynetDoingTask_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetDoingTask_C2S() {
      skynetModelType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetDoingTask_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetDoingTask_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SkynetModelType value = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                skynetModelType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              skynetTaskId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SKYNETMODELTYPE_FIELD_NUMBER = 1;
    private int skynetModelType_;
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return Whether the skynetModelType field is set.
     */
    @java.lang.Override public boolean hasSkynetModelType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return The skynetModelType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SkynetModelType result = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(skynetModelType_);
      return result == null ? com.yorha.proto.CommonEnum.SkynetModelType.SMT_NONE : result;
    }

    public static final int SKYNETTASKID_FIELD_NUMBER = 2;
    private int skynetTaskId_;
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    @java.lang.Override
    public boolean hasSkynetTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    @java.lang.Override
    public int getSkynetTaskId() {
      return skynetTaskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, skynetModelType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, skynetTaskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, skynetModelType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, skynetTaskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S other = (com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S) obj;

      if (hasSkynetModelType() != other.hasSkynetModelType()) return false;
      if (hasSkynetModelType()) {
        if (skynetModelType_ != other.skynetModelType_) return false;
      }
      if (hasSkynetTaskId() != other.hasSkynetTaskId()) return false;
      if (hasSkynetTaskId()) {
        if (getSkynetTaskId()
            != other.getSkynetTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkynetModelType()) {
        hash = (37 * hash) + SKYNETMODELTYPE_FIELD_NUMBER;
        hash = (53 * hash) + skynetModelType_;
      }
      if (hasSkynetTaskId()) {
        hash = (37 * hash) + SKYNETTASKID_FIELD_NUMBER;
        hash = (53 * hash) + getSkynetTaskId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetDoingTask_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetDoingTask_C2S)
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skynetModelType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetTaskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S result = new com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.skynetModelType_ = skynetModelType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.skynetTaskId_ = skynetTaskId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S.getDefaultInstance()) return this;
        if (other.hasSkynetModelType()) {
          setSkynetModelType(other.getSkynetModelType());
        }
        if (other.hasSkynetTaskId()) {
          setSkynetTaskId(other.getSkynetTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int skynetModelType_ = 0;
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return Whether the skynetModelType field is set.
       */
      @java.lang.Override public boolean hasSkynetModelType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return The skynetModelType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SkynetModelType result = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(skynetModelType_);
        return result == null ? com.yorha.proto.CommonEnum.SkynetModelType.SMT_NONE : result;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @param value The skynetModelType to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetModelType(com.yorha.proto.CommonEnum.SkynetModelType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        skynetModelType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetModelType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetModelType_ = 0;
        onChanged();
        return this;
      }

      private int skynetTaskId_ ;
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return Whether the skynetTaskId field is set.
       */
      @java.lang.Override
      public boolean hasSkynetTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return The skynetTaskId.
       */
      @java.lang.Override
      public int getSkynetTaskId() {
        return skynetTaskId_;
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @param value The skynetTaskId to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetTaskId(int value) {
        bitField0_ |= 0x00000002;
        skynetTaskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        skynetTaskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetDoingTask_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetDoingTask_C2S)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetDoingTask_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetDoingTask_C2S>() {
      @java.lang.Override
      public Player_SkynetDoingTask_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetDoingTask_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetDoingTask_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetDoingTask_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetDoingTask_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetDoingTask_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 野怪实体id
     * </pre>
     *
     * <code>optional int64 monsterId = 1;</code>
     * @return Whether the monsterId field is set.
     */
    boolean hasMonsterId();
    /**
     * <pre>
     * 野怪实体id
     * </pre>
     *
     * <code>optional int64 monsterId = 1;</code>
     * @return The monsterId.
     */
    long getMonsterId();

    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
     * @return Whether the bornPoint field is set.
     */
    boolean hasBornPoint();
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
     * @return The bornPoint.
     */
    com.yorha.proto.StructPB.PointPB getBornPoint();
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getBornPointOrBuilder();

    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 3;</code>
     * @return Whether the lifeEndTsMs field is set.
     */
    boolean hasLifeEndTsMs();
    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 3;</code>
     * @return The lifeEndTsMs.
     */
    long getLifeEndTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetDoingTask_S2C}
   */
  public static final class Player_SkynetDoingTask_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetDoingTask_S2C)
      Player_SkynetDoingTask_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetDoingTask_S2C.newBuilder() to construct.
    private Player_SkynetDoingTask_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetDoingTask_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetDoingTask_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetDoingTask_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              monsterId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = bornPoint_.toBuilder();
              }
              bornPoint_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(bornPoint_);
                bornPoint_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              lifeEndTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int MONSTERID_FIELD_NUMBER = 1;
    private long monsterId_;
    /**
     * <pre>
     * 野怪实体id
     * </pre>
     *
     * <code>optional int64 monsterId = 1;</code>
     * @return Whether the monsterId field is set.
     */
    @java.lang.Override
    public boolean hasMonsterId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 野怪实体id
     * </pre>
     *
     * <code>optional int64 monsterId = 1;</code>
     * @return The monsterId.
     */
    @java.lang.Override
    public long getMonsterId() {
      return monsterId_;
    }

    public static final int BORNPOINT_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPB.PointPB bornPoint_;
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
     * @return Whether the bornPoint field is set.
     */
    @java.lang.Override
    public boolean hasBornPoint() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
     * @return The bornPoint.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getBornPoint() {
      return bornPoint_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
    }
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getBornPointOrBuilder() {
      return bornPoint_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
    }

    public static final int LIFEENDTSMS_FIELD_NUMBER = 3;
    private long lifeEndTsMs_;
    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 3;</code>
     * @return Whether the lifeEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasLifeEndTsMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 3;</code>
     * @return The lifeEndTsMs.
     */
    @java.lang.Override
    public long getLifeEndTsMs() {
      return lifeEndTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, monsterId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getBornPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, lifeEndTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, monsterId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getBornPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, lifeEndTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C other = (com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C) obj;

      if (hasMonsterId() != other.hasMonsterId()) return false;
      if (hasMonsterId()) {
        if (getMonsterId()
            != other.getMonsterId()) return false;
      }
      if (hasBornPoint() != other.hasBornPoint()) return false;
      if (hasBornPoint()) {
        if (!getBornPoint()
            .equals(other.getBornPoint())) return false;
      }
      if (hasLifeEndTsMs() != other.hasLifeEndTsMs()) return false;
      if (hasLifeEndTsMs()) {
        if (getLifeEndTsMs()
            != other.getLifeEndTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMonsterId()) {
        hash = (37 * hash) + MONSTERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMonsterId());
      }
      if (hasBornPoint()) {
        hash = (37 * hash) + BORNPOINT_FIELD_NUMBER;
        hash = (53 * hash) + getBornPoint().hashCode();
      }
      if (hasLifeEndTsMs()) {
        hash = (37 * hash) + LIFEENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLifeEndTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetDoingTask_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetDoingTask_S2C)
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getBornPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        monsterId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (bornPointBuilder_ == null) {
          bornPoint_ = null;
        } else {
          bornPointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        lifeEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C result = new com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.monsterId_ = monsterId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (bornPointBuilder_ == null) {
            result.bornPoint_ = bornPoint_;
          } else {
            result.bornPoint_ = bornPointBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.lifeEndTsMs_ = lifeEndTsMs_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C.getDefaultInstance()) return this;
        if (other.hasMonsterId()) {
          setMonsterId(other.getMonsterId());
        }
        if (other.hasBornPoint()) {
          mergeBornPoint(other.getBornPoint());
        }
        if (other.hasLifeEndTsMs()) {
          setLifeEndTsMs(other.getLifeEndTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long monsterId_ ;
      /**
       * <pre>
       * 野怪实体id
       * </pre>
       *
       * <code>optional int64 monsterId = 1;</code>
       * @return Whether the monsterId field is set.
       */
      @java.lang.Override
      public boolean hasMonsterId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 野怪实体id
       * </pre>
       *
       * <code>optional int64 monsterId = 1;</code>
       * @return The monsterId.
       */
      @java.lang.Override
      public long getMonsterId() {
        return monsterId_;
      }
      /**
       * <pre>
       * 野怪实体id
       * </pre>
       *
       * <code>optional int64 monsterId = 1;</code>
       * @param value The monsterId to set.
       * @return This builder for chaining.
       */
      public Builder setMonsterId(long value) {
        bitField0_ |= 0x00000001;
        monsterId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 野怪实体id
       * </pre>
       *
       * <code>optional int64 monsterId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMonsterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        monsterId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB bornPoint_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> bornPointBuilder_;
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       * @return Whether the bornPoint field is set.
       */
      public boolean hasBornPoint() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       * @return The bornPoint.
       */
      public com.yorha.proto.StructPB.PointPB getBornPoint() {
        if (bornPointBuilder_ == null) {
          return bornPoint_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
        } else {
          return bornPointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       */
      public Builder setBornPoint(com.yorha.proto.StructPB.PointPB value) {
        if (bornPointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          bornPoint_ = value;
          onChanged();
        } else {
          bornPointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       */
      public Builder setBornPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (bornPointBuilder_ == null) {
          bornPoint_ = builderForValue.build();
          onChanged();
        } else {
          bornPointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       */
      public Builder mergeBornPoint(com.yorha.proto.StructPB.PointPB value) {
        if (bornPointBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              bornPoint_ != null &&
              bornPoint_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            bornPoint_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(bornPoint_).mergeFrom(value).buildPartial();
          } else {
            bornPoint_ = value;
          }
          onChanged();
        } else {
          bornPointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       */
      public Builder clearBornPoint() {
        if (bornPointBuilder_ == null) {
          bornPoint_ = null;
          onChanged();
        } else {
          bornPointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getBornPointBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getBornPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getBornPointOrBuilder() {
        if (bornPointBuilder_ != null) {
          return bornPointBuilder_.getMessageOrBuilder();
        } else {
          return bornPoint_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
        }
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getBornPointFieldBuilder() {
        if (bornPointBuilder_ == null) {
          bornPointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getBornPoint(),
                  getParentForChildren(),
                  isClean());
          bornPoint_ = null;
        }
        return bornPointBuilder_;
      }

      private long lifeEndTsMs_ ;
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 3;</code>
       * @return Whether the lifeEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasLifeEndTsMs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 3;</code>
       * @return The lifeEndTsMs.
       */
      @java.lang.Override
      public long getLifeEndTsMs() {
        return lifeEndTsMs_;
      }
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 3;</code>
       * @param value The lifeEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setLifeEndTsMs(long value) {
        bitField0_ |= 0x00000004;
        lifeEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLifeEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        lifeEndTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetDoingTask_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetDoingTask_S2C)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetDoingTask_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetDoingTask_S2C>() {
      @java.lang.Override
      public Player_SkynetDoingTask_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetDoingTask_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetDoingTask_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetDoingTask_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetDoingTask_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetStoreInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetStoreInfo_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetStoreInfo_C2S}
   */
  public static final class Player_SkynetStoreInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetStoreInfo_C2S)
      Player_SkynetStoreInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetStoreInfo_C2S.newBuilder() to construct.
    private Player_SkynetStoreInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetStoreInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetStoreInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetStoreInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S other = (com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetStoreInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetStoreInfo_C2S)
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S result = new com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetStoreInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetStoreInfo_C2S)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetStoreInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetStoreInfo_C2S>() {
      @java.lang.Override
      public Player_SkynetStoreInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetStoreInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetStoreInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetStoreInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetStoreInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetStoreInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.StoreInfo> 
        getInfoList();
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    com.yorha.proto.StructMsg.StoreInfo getInfo(int index);
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    int getInfoCount();
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
        getInfoOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    com.yorha.proto.StructMsg.StoreInfoOrBuilder getInfoOrBuilder(
        int index);

    /**
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return Whether the nextRefreshTsMs field is set.
     */
    boolean hasNextRefreshTsMs();
    /**
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return The nextRefreshTsMs.
     */
    long getNextRefreshTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetStoreInfo_S2C}
   */
  public static final class Player_SkynetStoreInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetStoreInfo_S2C)
      Player_SkynetStoreInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetStoreInfo_S2C.newBuilder() to construct.
    private Player_SkynetStoreInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetStoreInfo_S2C() {
      info_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetStoreInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetStoreInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = new java.util.ArrayList<com.yorha.proto.StructMsg.StoreInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              info_.add(
                  input.readMessage(com.yorha.proto.StructMsg.StoreInfo.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              nextRefreshTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          info_ = java.util.Collections.unmodifiableList(info_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructMsg.StoreInfo> info_;
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.StoreInfo> getInfoList() {
      return info_;
    }
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
        getInfoOrBuilderList() {
      return info_;
    }
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public int getInfoCount() {
      return info_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreInfo getInfo(int index) {
      return info_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreInfoOrBuilder getInfoOrBuilder(
        int index) {
      return info_.get(index);
    }

    public static final int NEXTREFRESHTSMS_FIELD_NUMBER = 2;
    private long nextRefreshTsMs_;
    /**
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return Whether the nextRefreshTsMs field is set.
     */
    @java.lang.Override
    public boolean hasNextRefreshTsMs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return The nextRefreshTsMs.
     */
    @java.lang.Override
    public long getNextRefreshTsMs() {
      return nextRefreshTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < info_.size(); i++) {
        output.writeMessage(1, info_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(2, nextRefreshTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < info_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, info_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, nextRefreshTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C other = (com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C) obj;

      if (!getInfoList()
          .equals(other.getInfoList())) return false;
      if (hasNextRefreshTsMs() != other.hasNextRefreshTsMs()) return false;
      if (hasNextRefreshTsMs()) {
        if (getNextRefreshTsMs()
            != other.getNextRefreshTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfoCount() > 0) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfoList().hashCode();
      }
      if (hasNextRefreshTsMs()) {
        hash = (37 * hash) + NEXTREFRESHTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNextRefreshTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetStoreInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetStoreInfo_S2C)
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infoBuilder_.clear();
        }
        nextRefreshTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C result = new com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            info_ = java.util.Collections.unmodifiableList(info_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.info_ = info_;
        } else {
          result.info_ = infoBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.nextRefreshTsMs_ = nextRefreshTsMs_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C.getDefaultInstance()) return this;
        if (infoBuilder_ == null) {
          if (!other.info_.isEmpty()) {
            if (info_.isEmpty()) {
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfoIsMutable();
              info_.addAll(other.info_);
            }
            onChanged();
          }
        } else {
          if (!other.info_.isEmpty()) {
            if (infoBuilder_.isEmpty()) {
              infoBuilder_.dispose();
              infoBuilder_ = null;
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfoFieldBuilder() : null;
            } else {
              infoBuilder_.addAllMessages(other.info_);
            }
          }
        }
        if (other.hasNextRefreshTsMs()) {
          setNextRefreshTsMs(other.getNextRefreshTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructMsg.StoreInfo> info_ =
        java.util.Collections.emptyList();
      private void ensureInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          info_ = new java.util.ArrayList<com.yorha.proto.StructMsg.StoreInfo>(info_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreInfo, com.yorha.proto.StructMsg.StoreInfo.Builder, com.yorha.proto.StructMsg.StoreInfoOrBuilder> infoBuilder_;

      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.StoreInfo> getInfoList() {
        if (infoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(info_);
        } else {
          return infoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public int getInfoCount() {
        if (infoBuilder_ == null) {
          return info_.size();
        } else {
          return infoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo getInfo(int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);
        } else {
          return infoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.set(index, value);
          onChanged();
        } else {
          infoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.set(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(com.yorha.proto.StructMsg.StoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(value);
          onChanged();
        } else {
          infoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(index, value);
          onChanged();
        } else {
          infoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(
          com.yorha.proto.StructMsg.StoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.StructMsg.StoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder addAllInfo(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.StoreInfo> values) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, info_);
          onChanged();
        } else {
          infoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public Builder removeInfo(int index) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.remove(index);
          onChanged();
        } else {
          infoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo.Builder getInfoBuilder(
          int index) {
        return getInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfoOrBuilder getInfoOrBuilder(
          int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);  } else {
          return infoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
           getInfoOrBuilderList() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(info_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo.Builder addInfoBuilder() {
        return getInfoFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.StoreInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreInfo.Builder addInfoBuilder(
          int index) {
        return getInfoFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.StoreInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.StoreInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.StoreInfo.Builder> 
           getInfoBuilderList() {
        return getInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreInfo, com.yorha.proto.StructMsg.StoreInfo.Builder, com.yorha.proto.StructMsg.StoreInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.StoreInfo, com.yorha.proto.StructMsg.StoreInfo.Builder, com.yorha.proto.StructMsg.StoreInfoOrBuilder>(
                  info_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }

      private long nextRefreshTsMs_ ;
      /**
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @return Whether the nextRefreshTsMs field is set.
       */
      @java.lang.Override
      public boolean hasNextRefreshTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @return The nextRefreshTsMs.
       */
      @java.lang.Override
      public long getNextRefreshTsMs() {
        return nextRefreshTsMs_;
      }
      /**
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @param value The nextRefreshTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setNextRefreshTsMs(long value) {
        bitField0_ |= 0x00000002;
        nextRefreshTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextRefreshTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        nextRefreshTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetStoreInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetStoreInfo_S2C)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetStoreInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetStoreInfo_S2C>() {
      @java.lang.Override
      public Player_SkynetStoreInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetStoreInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetStoreInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetStoreInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetStoreInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetCharge_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetCharge_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetCharge_C2S}
   */
  public static final class Player_SkynetCharge_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetCharge_C2S)
      Player_SkynetCharge_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetCharge_C2S.newBuilder() to construct.
    private Player_SkynetCharge_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetCharge_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetCharge_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetCharge_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S other = (com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetCharge_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetCharge_C2S)
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S result = new com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetCharge_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetCharge_C2S)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetCharge_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetCharge_C2S>() {
      @java.lang.Override
      public Player_SkynetCharge_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetCharge_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetCharge_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetCharge_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetCharge_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetCharge_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetCharge_S2C}
   */
  public static final class Player_SkynetCharge_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetCharge_S2C)
      Player_SkynetCharge_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetCharge_S2C.newBuilder() to construct.
    private Player_SkynetCharge_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetCharge_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetCharge_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetCharge_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C other = (com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetCharge_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetCharge_S2C)
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetCharge_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C result = new com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetCharge_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetCharge_S2C)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetCharge_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetCharge_S2C>() {
      @java.lang.Override
      public Player_SkynetCharge_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetCharge_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetCharge_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetCharge_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetCharge_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetTakeTaskReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetTakeTaskReward_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return Whether the skynetModelType field is set.
     */
    boolean hasSkynetModelType();
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return The skynetModelType.
     */
    com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType();

    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    boolean hasSkynetTaskId();
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    int getSkynetTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetTakeTaskReward_C2S}
   */
  public static final class Player_SkynetTakeTaskReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetTakeTaskReward_C2S)
      Player_SkynetTakeTaskReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetTakeTaskReward_C2S.newBuilder() to construct.
    private Player_SkynetTakeTaskReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetTakeTaskReward_C2S() {
      skynetModelType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetTakeTaskReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetTakeTaskReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SkynetModelType value = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                skynetModelType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              skynetTaskId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SKYNETMODELTYPE_FIELD_NUMBER = 1;
    private int skynetModelType_;
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return Whether the skynetModelType field is set.
     */
    @java.lang.Override public boolean hasSkynetModelType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return The skynetModelType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SkynetModelType result = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(skynetModelType_);
      return result == null ? com.yorha.proto.CommonEnum.SkynetModelType.SMT_NONE : result;
    }

    public static final int SKYNETTASKID_FIELD_NUMBER = 2;
    private int skynetTaskId_;
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    @java.lang.Override
    public boolean hasSkynetTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    @java.lang.Override
    public int getSkynetTaskId() {
      return skynetTaskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, skynetModelType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, skynetTaskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, skynetModelType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, skynetTaskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S other = (com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S) obj;

      if (hasSkynetModelType() != other.hasSkynetModelType()) return false;
      if (hasSkynetModelType()) {
        if (skynetModelType_ != other.skynetModelType_) return false;
      }
      if (hasSkynetTaskId() != other.hasSkynetTaskId()) return false;
      if (hasSkynetTaskId()) {
        if (getSkynetTaskId()
            != other.getSkynetTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkynetModelType()) {
        hash = (37 * hash) + SKYNETMODELTYPE_FIELD_NUMBER;
        hash = (53 * hash) + skynetModelType_;
      }
      if (hasSkynetTaskId()) {
        hash = (37 * hash) + SKYNETTASKID_FIELD_NUMBER;
        hash = (53 * hash) + getSkynetTaskId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetTakeTaskReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetTakeTaskReward_C2S)
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skynetModelType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetTaskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S result = new com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.skynetModelType_ = skynetModelType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.skynetTaskId_ = skynetTaskId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S.getDefaultInstance()) return this;
        if (other.hasSkynetModelType()) {
          setSkynetModelType(other.getSkynetModelType());
        }
        if (other.hasSkynetTaskId()) {
          setSkynetTaskId(other.getSkynetTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int skynetModelType_ = 0;
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return Whether the skynetModelType field is set.
       */
      @java.lang.Override public boolean hasSkynetModelType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return The skynetModelType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SkynetModelType result = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(skynetModelType_);
        return result == null ? com.yorha.proto.CommonEnum.SkynetModelType.SMT_NONE : result;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @param value The skynetModelType to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetModelType(com.yorha.proto.CommonEnum.SkynetModelType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        skynetModelType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetModelType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetModelType_ = 0;
        onChanged();
        return this;
      }

      private int skynetTaskId_ ;
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return Whether the skynetTaskId field is set.
       */
      @java.lang.Override
      public boolean hasSkynetTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return The skynetTaskId.
       */
      @java.lang.Override
      public int getSkynetTaskId() {
        return skynetTaskId_;
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @param value The skynetTaskId to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetTaskId(int value) {
        bitField0_ |= 0x00000002;
        skynetTaskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        skynetTaskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetTakeTaskReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetTakeTaskReward_C2S)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetTakeTaskReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetTakeTaskReward_C2S>() {
      @java.lang.Override
      public Player_SkynetTakeTaskReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetTakeTaskReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetTakeTaskReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetTakeTaskReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetTakeTaskReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetTakeTaskReward_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetTakeTaskReward_S2C}
   */
  public static final class Player_SkynetTakeTaskReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetTakeTaskReward_S2C)
      Player_SkynetTakeTaskReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetTakeTaskReward_S2C.newBuilder() to construct.
    private Player_SkynetTakeTaskReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetTakeTaskReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetTakeTaskReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetTakeTaskReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C other = (com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetTakeTaskReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetTakeTaskReward_S2C)
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C result = new com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetTakeTaskReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetTakeTaskReward_S2C)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetTakeTaskReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetTakeTaskReward_S2C>() {
      @java.lang.Override
      public Player_SkynetTakeTaskReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetTakeTaskReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetTakeTaskReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetTakeTaskReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetTakeTaskReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetBuyStore_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetBuyStore_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 shopId = 1;</code>
     * @return Whether the shopId field is set.
     */
    boolean hasShopId();
    /**
     * <code>optional int32 shopId = 1;</code>
     * @return The shopId.
     */
    int getShopId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetBuyStore_C2S}
   */
  public static final class Player_SkynetBuyStore_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetBuyStore_C2S)
      Player_SkynetBuyStore_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetBuyStore_C2S.newBuilder() to construct.
    private Player_SkynetBuyStore_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetBuyStore_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetBuyStore_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetBuyStore_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              shopId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SHOPID_FIELD_NUMBER = 1;
    private int shopId_;
    /**
     * <code>optional int32 shopId = 1;</code>
     * @return Whether the shopId field is set.
     */
    @java.lang.Override
    public boolean hasShopId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 shopId = 1;</code>
     * @return The shopId.
     */
    @java.lang.Override
    public int getShopId() {
      return shopId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, shopId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, shopId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S other = (com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S) obj;

      if (hasShopId() != other.hasShopId()) return false;
      if (hasShopId()) {
        if (getShopId()
            != other.getShopId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasShopId()) {
        hash = (37 * hash) + SHOPID_FIELD_NUMBER;
        hash = (53 * hash) + getShopId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetBuyStore_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetBuyStore_C2S)
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        shopId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S result = new com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.shopId_ = shopId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S.getDefaultInstance()) return this;
        if (other.hasShopId()) {
          setShopId(other.getShopId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int shopId_ ;
      /**
       * <code>optional int32 shopId = 1;</code>
       * @return Whether the shopId field is set.
       */
      @java.lang.Override
      public boolean hasShopId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 shopId = 1;</code>
       * @return The shopId.
       */
      @java.lang.Override
      public int getShopId() {
        return shopId_;
      }
      /**
       * <code>optional int32 shopId = 1;</code>
       * @param value The shopId to set.
       * @return This builder for chaining.
       */
      public Builder setShopId(int value) {
        bitField0_ |= 0x00000001;
        shopId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 shopId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearShopId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        shopId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetBuyStore_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetBuyStore_C2S)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetBuyStore_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetBuyStore_C2S>() {
      @java.lang.Override
      public Player_SkynetBuyStore_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetBuyStore_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetBuyStore_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetBuyStore_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetBuyStore_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetBuyStore_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetBuyStore_S2C}
   */
  public static final class Player_SkynetBuyStore_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetBuyStore_S2C)
      Player_SkynetBuyStore_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetBuyStore_S2C.newBuilder() to construct.
    private Player_SkynetBuyStore_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetBuyStore_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetBuyStore_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetBuyStore_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C other = (com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetBuyStore_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetBuyStore_S2C)
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C result = new com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetBuyStore_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetBuyStore_S2C)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetBuyStore_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetBuyStore_S2C>() {
      @java.lang.Override
      public Player_SkynetBuyStore_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetBuyStore_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetBuyStore_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetBuyStore_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetBuyStore_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetFindMonster_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetFindMonster_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return Whether the skynetModelType field is set.
     */
    boolean hasSkynetModelType();
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return The skynetModelType.
     */
    com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType();

    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    boolean hasSkynetTaskId();
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    int getSkynetTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetFindMonster_C2S}
   */
  public static final class Player_SkynetFindMonster_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetFindMonster_C2S)
      Player_SkynetFindMonster_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetFindMonster_C2S.newBuilder() to construct.
    private Player_SkynetFindMonster_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetFindMonster_C2S() {
      skynetModelType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetFindMonster_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetFindMonster_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SkynetModelType value = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                skynetModelType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              skynetTaskId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SKYNETMODELTYPE_FIELD_NUMBER = 1;
    private int skynetModelType_;
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return Whether the skynetModelType field is set.
     */
    @java.lang.Override public boolean hasSkynetModelType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
     * @return The skynetModelType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SkynetModelType result = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(skynetModelType_);
      return result == null ? com.yorha.proto.CommonEnum.SkynetModelType.SMT_NONE : result;
    }

    public static final int SKYNETTASKID_FIELD_NUMBER = 2;
    private int skynetTaskId_;
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return Whether the skynetTaskId field is set.
     */
    @java.lang.Override
    public boolean hasSkynetTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 模块唯一id
     * </pre>
     *
     * <code>optional int32 skynetTaskId = 2;</code>
     * @return The skynetTaskId.
     */
    @java.lang.Override
    public int getSkynetTaskId() {
      return skynetTaskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, skynetModelType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, skynetTaskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, skynetModelType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, skynetTaskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S other = (com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S) obj;

      if (hasSkynetModelType() != other.hasSkynetModelType()) return false;
      if (hasSkynetModelType()) {
        if (skynetModelType_ != other.skynetModelType_) return false;
      }
      if (hasSkynetTaskId() != other.hasSkynetTaskId()) return false;
      if (hasSkynetTaskId()) {
        if (getSkynetTaskId()
            != other.getSkynetTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSkynetModelType()) {
        hash = (37 * hash) + SKYNETMODELTYPE_FIELD_NUMBER;
        hash = (53 * hash) + skynetModelType_;
      }
      if (hasSkynetTaskId()) {
        hash = (37 * hash) + SKYNETTASKID_FIELD_NUMBER;
        hash = (53 * hash) + getSkynetTaskId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetFindMonster_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetFindMonster_C2S)
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S.class, com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skynetModelType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetTaskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S result = new com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.skynetModelType_ = skynetModelType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.skynetTaskId_ = skynetTaskId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S.getDefaultInstance()) return this;
        if (other.hasSkynetModelType()) {
          setSkynetModelType(other.getSkynetModelType());
        }
        if (other.hasSkynetTaskId()) {
          setSkynetTaskId(other.getSkynetTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int skynetModelType_ = 0;
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return Whether the skynetModelType field is set.
       */
      @java.lang.Override public boolean hasSkynetModelType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return The skynetModelType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SkynetModelType getSkynetModelType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SkynetModelType result = com.yorha.proto.CommonEnum.SkynetModelType.valueOf(skynetModelType_);
        return result == null ? com.yorha.proto.CommonEnum.SkynetModelType.SMT_NONE : result;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @param value The skynetModelType to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetModelType(com.yorha.proto.CommonEnum.SkynetModelType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        skynetModelType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.SkynetModelType skynetModelType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetModelType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        skynetModelType_ = 0;
        onChanged();
        return this;
      }

      private int skynetTaskId_ ;
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return Whether the skynetTaskId field is set.
       */
      @java.lang.Override
      public boolean hasSkynetTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return The skynetTaskId.
       */
      @java.lang.Override
      public int getSkynetTaskId() {
        return skynetTaskId_;
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @param value The skynetTaskId to set.
       * @return This builder for chaining.
       */
      public Builder setSkynetTaskId(int value) {
        bitField0_ |= 0x00000002;
        skynetTaskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模块唯一id
       * </pre>
       *
       * <code>optional int32 skynetTaskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkynetTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        skynetTaskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetFindMonster_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetFindMonster_C2S)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetFindMonster_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetFindMonster_C2S>() {
      @java.lang.Override
      public Player_SkynetFindMonster_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetFindMonster_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetFindMonster_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetFindMonster_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SkynetFindMonster_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SkynetFindMonster_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
     * @return Whether the bornPoint field is set.
     */
    boolean hasBornPoint();
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
     * @return The bornPoint.
     */
    com.yorha.proto.StructPB.PointPB getBornPoint();
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getBornPointOrBuilder();

    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 2;</code>
     * @return Whether the lifeEndTsMs field is set.
     */
    boolean hasLifeEndTsMs();
    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 2;</code>
     * @return The lifeEndTsMs.
     */
    long getLifeEndTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SkynetFindMonster_S2C}
   */
  public static final class Player_SkynetFindMonster_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SkynetFindMonster_S2C)
      Player_SkynetFindMonster_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SkynetFindMonster_S2C.newBuilder() to construct.
    private Player_SkynetFindMonster_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SkynetFindMonster_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SkynetFindMonster_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SkynetFindMonster_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = bornPoint_.toBuilder();
              }
              bornPoint_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(bornPoint_);
                bornPoint_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              lifeEndTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int BORNPOINT_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.PointPB bornPoint_;
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
     * @return Whether the bornPoint field is set.
     */
    @java.lang.Override
    public boolean hasBornPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
     * @return The bornPoint.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getBornPoint() {
      return bornPoint_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
    }
    /**
     * <pre>
     * 出生点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getBornPointOrBuilder() {
      return bornPoint_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
    }

    public static final int LIFEENDTSMS_FIELD_NUMBER = 2;
    private long lifeEndTsMs_;
    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 2;</code>
     * @return Whether the lifeEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasLifeEndTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * GG时间
     * </pre>
     *
     * <code>optional int64 lifeEndTsMs = 2;</code>
     * @return The lifeEndTsMs.
     */
    @java.lang.Override
    public long getLifeEndTsMs() {
      return lifeEndTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getBornPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, lifeEndTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getBornPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, lifeEndTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C other = (com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C) obj;

      if (hasBornPoint() != other.hasBornPoint()) return false;
      if (hasBornPoint()) {
        if (!getBornPoint()
            .equals(other.getBornPoint())) return false;
      }
      if (hasLifeEndTsMs() != other.hasLifeEndTsMs()) return false;
      if (hasLifeEndTsMs()) {
        if (getLifeEndTsMs()
            != other.getLifeEndTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBornPoint()) {
        hash = (37 * hash) + BORNPOINT_FIELD_NUMBER;
        hash = (53 * hash) + getBornPoint().hashCode();
      }
      if (hasLifeEndTsMs()) {
        hash = (37 * hash) + LIFEENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLifeEndTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SkynetFindMonster_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SkynetFindMonster_S2C)
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C.class, com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getBornPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (bornPointBuilder_ == null) {
          bornPoint_ = null;
        } else {
          bornPointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        lifeEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerSkynet.internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C build() {
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C buildPartial() {
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C result = new com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (bornPointBuilder_ == null) {
            result.bornPoint_ = bornPoint_;
          } else {
            result.bornPoint_ = bornPointBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lifeEndTsMs_ = lifeEndTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C) {
          return mergeFrom((com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C other) {
        if (other == com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C.getDefaultInstance()) return this;
        if (other.hasBornPoint()) {
          mergeBornPoint(other.getBornPoint());
        }
        if (other.hasLifeEndTsMs()) {
          setLifeEndTsMs(other.getLifeEndTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.PointPB bornPoint_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> bornPointBuilder_;
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       * @return Whether the bornPoint field is set.
       */
      public boolean hasBornPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       * @return The bornPoint.
       */
      public com.yorha.proto.StructPB.PointPB getBornPoint() {
        if (bornPointBuilder_ == null) {
          return bornPoint_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
        } else {
          return bornPointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       */
      public Builder setBornPoint(com.yorha.proto.StructPB.PointPB value) {
        if (bornPointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          bornPoint_ = value;
          onChanged();
        } else {
          bornPointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       */
      public Builder setBornPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (bornPointBuilder_ == null) {
          bornPoint_ = builderForValue.build();
          onChanged();
        } else {
          bornPointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       */
      public Builder mergeBornPoint(com.yorha.proto.StructPB.PointPB value) {
        if (bornPointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              bornPoint_ != null &&
              bornPoint_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            bornPoint_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(bornPoint_).mergeFrom(value).buildPartial();
          } else {
            bornPoint_ = value;
          }
          onChanged();
        } else {
          bornPointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       */
      public Builder clearBornPoint() {
        if (bornPointBuilder_ == null) {
          bornPoint_ = null;
          onChanged();
        } else {
          bornPointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getBornPointBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getBornPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getBornPointOrBuilder() {
        if (bornPointBuilder_ != null) {
          return bornPointBuilder_.getMessageOrBuilder();
        } else {
          return bornPoint_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : bornPoint_;
        }
      }
      /**
       * <pre>
       * 出生点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB bornPoint = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getBornPointFieldBuilder() {
        if (bornPointBuilder_ == null) {
          bornPointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getBornPoint(),
                  getParentForChildren(),
                  isClean());
          bornPoint_ = null;
        }
        return bornPointBuilder_;
      }

      private long lifeEndTsMs_ ;
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 2;</code>
       * @return Whether the lifeEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasLifeEndTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 2;</code>
       * @return The lifeEndTsMs.
       */
      @java.lang.Override
      public long getLifeEndTsMs() {
        return lifeEndTsMs_;
      }
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 2;</code>
       * @param value The lifeEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setLifeEndTsMs(long value) {
        bitField0_ |= 0x00000002;
        lifeEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * GG时间
       * </pre>
       *
       * <code>optional int64 lifeEndTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLifeEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lifeEndTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SkynetFindMonster_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SkynetFindMonster_S2C)
    private static final com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C();
    }

    public static com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SkynetFindMonster_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SkynetFindMonster_S2C>() {
      @java.lang.Override
      public Player_SkynetFindMonster_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SkynetFindMonster_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SkynetFindMonster_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SkynetFindMonster_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerSkynet.Player_SkynetFindMonster_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetCharge_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetCharge_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetCharge_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetCharge_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*ss_proto/gen/player/cs/player_skynet.p" +
      "roto\022\017com.yorha.proto\032\"cs_proto/gen/comm" +
      "on/structPB.proto\032%ss_proto/gen/common/c" +
      "ommon_enum.proto\032$ss_proto/gen/common/st" +
      "ruct_msg.proto\"m\n\032Player_SkynetDoingTask" +
      "_C2S\0229\n\017skynetModelType\030\001 \001(\0162 .com.yorh" +
      "a.proto.SkynetModelType\022\024\n\014skynetTaskId\030" +
      "\002 \001(\005\"q\n\032Player_SkynetDoingTask_S2C\022\021\n\tm" +
      "onsterId\030\001 \001(\003\022+\n\tbornPoint\030\002 \001(\0132\030.com." +
      "yorha.proto.PointPB\022\023\n\013lifeEndTsMs\030\003 \001(\003" +
      "\"\034\n\032Player_SkynetStoreInfo_C2S\"_\n\032Player" +
      "_SkynetStoreInfo_S2C\022(\n\004info\030\001 \003(\0132\032.com" +
      ".yorha.proto.StoreInfo\022\027\n\017nextRefreshTsM" +
      "s\030\002 \001(\003\"\031\n\027Player_SkynetCharge_C2S\"\031\n\027Pl" +
      "ayer_SkynetCharge_S2C\"r\n\037Player_SkynetTa" +
      "keTaskReward_C2S\0229\n\017skynetModelType\030\001 \001(" +
      "\0162 .com.yorha.proto.SkynetModelType\022\024\n\014s" +
      "kynetTaskId\030\002 \001(\005\"!\n\037Player_SkynetTakeTa" +
      "skReward_S2C\"8\n\031Player_SkynetBuyStore_C2" +
      "S\022\016\n\006shopId\030\001 \001(\005\022\013\n\003num\030\002 \001(\005\"\033\n\031Player" +
      "_SkynetBuyStore_S2C\"o\n\034Player_SkynetFind" +
      "Monster_C2S\0229\n\017skynetModelType\030\001 \001(\0162 .c" +
      "om.yorha.proto.SkynetModelType\022\024\n\014skynet" +
      "TaskId\030\002 \001(\005\"`\n\034Player_SkynetFindMonster" +
      "_S2C\022+\n\tbornPoint\030\001 \001(\0132\030.com.yorha.prot" +
      "o.PointPB\022\023\n\013lifeEndTsMs\030\002 \001(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetDoingTask_C2S_descriptor,
        new java.lang.String[] { "SkynetModelType", "SkynetTaskId", });
    internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetDoingTask_S2C_descriptor,
        new java.lang.String[] { "MonsterId", "BornPoint", "LifeEndTsMs", });
    internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetStoreInfo_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetStoreInfo_S2C_descriptor,
        new java.lang.String[] { "Info", "NextRefreshTsMs", });
    internal_static_com_yorha_proto_Player_SkynetCharge_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_SkynetCharge_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetCharge_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SkynetCharge_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_SkynetCharge_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetCharge_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_C2S_descriptor,
        new java.lang.String[] { "SkynetModelType", "SkynetTaskId", });
    internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetTakeTaskReward_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetBuyStore_C2S_descriptor,
        new java.lang.String[] { "ShopId", "Num", });
    internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetBuyStore_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetFindMonster_C2S_descriptor,
        new java.lang.String[] { "SkynetModelType", "SkynetTaskId", });
    internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SkynetFindMonster_S2C_descriptor,
        new java.lang.String[] { "BornPoint", "LifeEndTsMs", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_activity_schedule.proto

package com.yorha.proto;

public final class SsSceneActivitySchedule {
  private SsSceneActivitySchedule() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface BestCommanderFetchAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderFetchAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 总活动id
     * </pre>
     *
     * <code>optional int32 actId = 2;</code>
     * @return Whether the actId field is set.
     */
    boolean hasActId();
    /**
     * <pre>
     * 总活动id
     * </pre>
     *
     * <code>optional int32 actId = 2;</code>
     * @return The actId.
     */
    int getActId();

    /**
     * <code>repeated int32 childActIds = 3;</code>
     * @return A list containing the childActIds.
     */
    java.util.List<java.lang.Integer> getChildActIdsList();
    /**
     * <code>repeated int32 childActIds = 3;</code>
     * @return The count of childActIds.
     */
    int getChildActIdsCount();
    /**
     * <code>repeated int32 childActIds = 3;</code>
     * @param index The index of the element to return.
     * @return The childActIds at the given index.
     */
    int getChildActIds(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderFetchAsk}
   */
  public static final class BestCommanderFetchAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderFetchAsk)
      BestCommanderFetchAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderFetchAsk.newBuilder() to construct.
    private BestCommanderFetchAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderFetchAsk() {
      childActIds_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderFetchAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderFetchAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              actId_ = input.readInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                childActIds_ = newIntList();
                mutable_bitField0_ |= 0x00000004;
              }
              childActIds_.addInt(input.readInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) != 0) && input.getBytesUntilLimit() > 0) {
                childActIds_ = newIntList();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                childActIds_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          childActIds_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ACTID_FIELD_NUMBER = 2;
    private int actId_;
    /**
     * <pre>
     * 总活动id
     * </pre>
     *
     * <code>optional int32 actId = 2;</code>
     * @return Whether the actId field is set.
     */
    @java.lang.Override
    public boolean hasActId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 总活动id
     * </pre>
     *
     * <code>optional int32 actId = 2;</code>
     * @return The actId.
     */
    @java.lang.Override
    public int getActId() {
      return actId_;
    }

    public static final int CHILDACTIDS_FIELD_NUMBER = 3;
    private com.google.protobuf.Internal.IntList childActIds_;
    /**
     * <code>repeated int32 childActIds = 3;</code>
     * @return A list containing the childActIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getChildActIdsList() {
      return childActIds_;
    }
    /**
     * <code>repeated int32 childActIds = 3;</code>
     * @return The count of childActIds.
     */
    public int getChildActIdsCount() {
      return childActIds_.size();
    }
    /**
     * <code>repeated int32 childActIds = 3;</code>
     * @param index The index of the element to return.
     * @return The childActIds at the given index.
     */
    public int getChildActIds(int index) {
      return childActIds_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, actId_);
      }
      for (int i = 0; i < childActIds_.size(); i++) {
        output.writeInt32(3, childActIds_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, actId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < childActIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(childActIds_.getInt(i));
        }
        size += dataSize;
        size += 1 * getChildActIdsList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasActId() != other.hasActId()) return false;
      if (hasActId()) {
        if (getActId()
            != other.getActId()) return false;
      }
      if (!getChildActIdsList()
          .equals(other.getChildActIdsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasActId()) {
        hash = (37 * hash) + ACTID_FIELD_NUMBER;
        hash = (53 * hash) + getActId();
      }
      if (getChildActIdsCount() > 0) {
        hash = (37 * hash) + CHILDACTIDS_FIELD_NUMBER;
        hash = (53 * hash) + getChildActIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderFetchAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderFetchAsk)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        actId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        childActIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.actId_ = actId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          childActIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.childActIds_ = childActIds_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasActId()) {
          setActId(other.getActId());
        }
        if (!other.childActIds_.isEmpty()) {
          if (childActIds_.isEmpty()) {
            childActIds_ = other.childActIds_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureChildActIdsIsMutable();
            childActIds_.addAll(other.childActIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int actId_ ;
      /**
       * <pre>
       * 总活动id
       * </pre>
       *
       * <code>optional int32 actId = 2;</code>
       * @return Whether the actId field is set.
       */
      @java.lang.Override
      public boolean hasActId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 总活动id
       * </pre>
       *
       * <code>optional int32 actId = 2;</code>
       * @return The actId.
       */
      @java.lang.Override
      public int getActId() {
        return actId_;
      }
      /**
       * <pre>
       * 总活动id
       * </pre>
       *
       * <code>optional int32 actId = 2;</code>
       * @param value The actId to set.
       * @return This builder for chaining.
       */
      public Builder setActId(int value) {
        bitField0_ |= 0x00000002;
        actId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 总活动id
       * </pre>
       *
       * <code>optional int32 actId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearActId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        actId_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList childActIds_ = emptyIntList();
      private void ensureChildActIdsIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          childActIds_ = mutableCopy(childActIds_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int32 childActIds = 3;</code>
       * @return A list containing the childActIds.
       */
      public java.util.List<java.lang.Integer>
          getChildActIdsList() {
        return ((bitField0_ & 0x00000004) != 0) ?
                 java.util.Collections.unmodifiableList(childActIds_) : childActIds_;
      }
      /**
       * <code>repeated int32 childActIds = 3;</code>
       * @return The count of childActIds.
       */
      public int getChildActIdsCount() {
        return childActIds_.size();
      }
      /**
       * <code>repeated int32 childActIds = 3;</code>
       * @param index The index of the element to return.
       * @return The childActIds at the given index.
       */
      public int getChildActIds(int index) {
        return childActIds_.getInt(index);
      }
      /**
       * <code>repeated int32 childActIds = 3;</code>
       * @param index The index to set the value at.
       * @param value The childActIds to set.
       * @return This builder for chaining.
       */
      public Builder setChildActIds(
          int index, int value) {
        ensureChildActIdsIsMutable();
        childActIds_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 childActIds = 3;</code>
       * @param value The childActIds to add.
       * @return This builder for chaining.
       */
      public Builder addChildActIds(int value) {
        ensureChildActIdsIsMutable();
        childActIds_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 childActIds = 3;</code>
       * @param values The childActIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllChildActIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureChildActIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, childActIds_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 childActIds = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearChildActIds() {
        childActIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderFetchAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderFetchAsk)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderFetchAsk>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderFetchAsk>() {
      @java.lang.Override
      public BestCommanderFetchAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderFetchAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderFetchAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderFetchAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderFetchAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderFetchAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    java.util.List<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem> 
        getFetchItemsList();
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem getFetchItems(int index);
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    int getFetchItemsCount();
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder> 
        getFetchItemsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder getFetchItemsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderFetchAns}
   */
  public static final class BestCommanderFetchAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderFetchAns)
      BestCommanderFetchAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderFetchAns.newBuilder() to construct.
    private BestCommanderFetchAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderFetchAns() {
      fetchItems_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderFetchAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderFetchAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                fetchItems_ = new java.util.ArrayList<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem>();
                mutable_bitField0_ |= 0x00000001;
              }
              fetchItems_.add(
                  input.readMessage(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          fetchItems_ = java.util.Collections.unmodifiableList(fetchItems_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns.Builder.class);
    }

    public static final int FETCHITEMS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem> fetchItems_;
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem> getFetchItemsList() {
      return fetchItems_;
    }
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder> 
        getFetchItemsOrBuilderList() {
      return fetchItems_;
    }
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    @java.lang.Override
    public int getFetchItemsCount() {
      return fetchItems_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem getFetchItems(int index) {
      return fetchItems_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder getFetchItemsOrBuilder(
        int index) {
      return fetchItems_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < fetchItems_.size(); i++) {
        output.writeMessage(1, fetchItems_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < fetchItems_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, fetchItems_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns) obj;

      if (!getFetchItemsList()
          .equals(other.getFetchItemsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getFetchItemsCount() > 0) {
        hash = (37 * hash) + FETCHITEMS_FIELD_NUMBER;
        hash = (53 * hash) + getFetchItemsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderFetchAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderFetchAns)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFetchItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (fetchItemsBuilder_ == null) {
          fetchItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          fetchItemsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns(this);
        int from_bitField0_ = bitField0_;
        if (fetchItemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            fetchItems_ = java.util.Collections.unmodifiableList(fetchItems_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.fetchItems_ = fetchItems_;
        } else {
          result.fetchItems_ = fetchItemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns.getDefaultInstance()) return this;
        if (fetchItemsBuilder_ == null) {
          if (!other.fetchItems_.isEmpty()) {
            if (fetchItems_.isEmpty()) {
              fetchItems_ = other.fetchItems_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFetchItemsIsMutable();
              fetchItems_.addAll(other.fetchItems_);
            }
            onChanged();
          }
        } else {
          if (!other.fetchItems_.isEmpty()) {
            if (fetchItemsBuilder_.isEmpty()) {
              fetchItemsBuilder_.dispose();
              fetchItemsBuilder_ = null;
              fetchItems_ = other.fetchItems_;
              bitField0_ = (bitField0_ & ~0x00000001);
              fetchItemsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFetchItemsFieldBuilder() : null;
            } else {
              fetchItemsBuilder_.addAllMessages(other.fetchItems_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem> fetchItems_ =
        java.util.Collections.emptyList();
      private void ensureFetchItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fetchItems_ = new java.util.ArrayList<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem>(fetchItems_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder> fetchItemsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public java.util.List<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem> getFetchItemsList() {
        if (fetchItemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(fetchItems_);
        } else {
          return fetchItemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public int getFetchItemsCount() {
        if (fetchItemsBuilder_ == null) {
          return fetchItems_.size();
        } else {
          return fetchItemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem getFetchItems(int index) {
        if (fetchItemsBuilder_ == null) {
          return fetchItems_.get(index);
        } else {
          return fetchItemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder setFetchItems(
          int index, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem value) {
        if (fetchItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFetchItemsIsMutable();
          fetchItems_.set(index, value);
          onChanged();
        } else {
          fetchItemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder setFetchItems(
          int index, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder builderForValue) {
        if (fetchItemsBuilder_ == null) {
          ensureFetchItemsIsMutable();
          fetchItems_.set(index, builderForValue.build());
          onChanged();
        } else {
          fetchItemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder addFetchItems(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem value) {
        if (fetchItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFetchItemsIsMutable();
          fetchItems_.add(value);
          onChanged();
        } else {
          fetchItemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder addFetchItems(
          int index, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem value) {
        if (fetchItemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFetchItemsIsMutable();
          fetchItems_.add(index, value);
          onChanged();
        } else {
          fetchItemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder addFetchItems(
          com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder builderForValue) {
        if (fetchItemsBuilder_ == null) {
          ensureFetchItemsIsMutable();
          fetchItems_.add(builderForValue.build());
          onChanged();
        } else {
          fetchItemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder addFetchItems(
          int index, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder builderForValue) {
        if (fetchItemsBuilder_ == null) {
          ensureFetchItemsIsMutable();
          fetchItems_.add(index, builderForValue.build());
          onChanged();
        } else {
          fetchItemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder addAllFetchItems(
          java.lang.Iterable<? extends com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem> values) {
        if (fetchItemsBuilder_ == null) {
          ensureFetchItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, fetchItems_);
          onChanged();
        } else {
          fetchItemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder clearFetchItems() {
        if (fetchItemsBuilder_ == null) {
          fetchItems_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          fetchItemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public Builder removeFetchItems(int index) {
        if (fetchItemsBuilder_ == null) {
          ensureFetchItemsIsMutable();
          fetchItems_.remove(index);
          onChanged();
        } else {
          fetchItemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder getFetchItemsBuilder(
          int index) {
        return getFetchItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder getFetchItemsOrBuilder(
          int index) {
        if (fetchItemsBuilder_ == null) {
          return fetchItems_.get(index);  } else {
          return fetchItemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder> 
           getFetchItemsOrBuilderList() {
        if (fetchItemsBuilder_ != null) {
          return fetchItemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(fetchItems_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder addFetchItemsBuilder() {
        return getFetchItemsFieldBuilder().addBuilder(
            com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder addFetchItemsBuilder(
          int index) {
        return getFetchItemsFieldBuilder().addBuilder(
            index, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.BestCommanderFetchItem fetchItems = 1;</code>
       */
      public java.util.List<com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder> 
           getFetchItemsBuilderList() {
        return getFetchItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder> 
          getFetchItemsFieldBuilder() {
        if (fetchItemsBuilder_ == null) {
          fetchItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder>(
                  fetchItems_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          fetchItems_ = null;
        }
        return fetchItemsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderFetchAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderFetchAns)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderFetchAns>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderFetchAns>() {
      @java.lang.Override
      public BestCommanderFetchAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderFetchAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderFetchAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderFetchAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderFetchItemOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderFetchItem)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    boolean hasActId();
    /**
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    int getActId();

    /**
     * <code>optional int32 rank = 2;</code>
     * @return Whether the rank field is set.
     */
    boolean hasRank();
    /**
     * <code>optional int32 rank = 2;</code>
     * @return The rank.
     */
    int getRank();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderFetchItem}
   */
  public static final class BestCommanderFetchItem extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderFetchItem)
      BestCommanderFetchItemOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderFetchItem.newBuilder() to construct.
    private BestCommanderFetchItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderFetchItem() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderFetchItem();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderFetchItem(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              actId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              rank_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder.class);
    }

    private int bitField0_;
    public static final int ACTID_FIELD_NUMBER = 1;
    private int actId_;
    /**
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    @java.lang.Override
    public boolean hasActId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    @java.lang.Override
    public int getActId() {
      return actId_;
    }

    public static final int RANK_FIELD_NUMBER = 2;
    private int rank_;
    /**
     * <code>optional int32 rank = 2;</code>
     * @return Whether the rank field is set.
     */
    @java.lang.Override
    public boolean hasRank() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 rank = 2;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, rank_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, rank_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem) obj;

      if (hasActId() != other.hasActId()) return false;
      if (hasActId()) {
        if (getActId()
            != other.getActId()) return false;
      }
      if (hasRank() != other.hasRank()) return false;
      if (hasRank()) {
        if (getRank()
            != other.getRank()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActId()) {
        hash = (37 * hash) + ACTID_FIELD_NUMBER;
        hash = (53 * hash) + getActId();
      }
      if (hasRank()) {
        hash = (37 * hash) + RANK_FIELD_NUMBER;
        hash = (53 * hash) + getRank();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderFetchItem}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderFetchItem)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchItem_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        rank_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderFetchItem_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.actId_ = actId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rank_ = rank_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem.getDefaultInstance()) return this;
        if (other.hasActId()) {
          setActId(other.getActId());
        }
        if (other.hasRank()) {
          setRank(other.getRank());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int actId_ ;
      /**
       * <code>optional int32 actId = 1;</code>
       * @return Whether the actId field is set.
       */
      @java.lang.Override
      public boolean hasActId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @return The actId.
       */
      @java.lang.Override
      public int getActId() {
        return actId_;
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @param value The actId to set.
       * @return This builder for chaining.
       */
      public Builder setActId(int value) {
        bitField0_ |= 0x00000001;
        actId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        actId_ = 0;
        onChanged();
        return this;
      }

      private int rank_ ;
      /**
       * <code>optional int32 rank = 2;</code>
       * @return Whether the rank field is set.
       */
      @java.lang.Override
      public boolean hasRank() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 rank = 2;</code>
       * @return The rank.
       */
      @java.lang.Override
      public int getRank() {
        return rank_;
      }
      /**
       * <code>optional int32 rank = 2;</code>
       * @param value The rank to set.
       * @return This builder for chaining.
       */
      public Builder setRank(int value) {
        bitField0_ |= 0x00000002;
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 rank = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRank() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rank_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderFetchItem)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderFetchItem)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderFetchItem>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderFetchItem>() {
      @java.lang.Override
      public BestCommanderFetchItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderFetchItem(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderFetchItem> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderFetchItem> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderFetchItem getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderHistoryRankAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderHistoryRankAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 想要拉取几期的数据,0的话服务器直接20期全发
     * </pre>
     *
     * <code>optional int32 want = 1;</code>
     * @return Whether the want field is set.
     */
    boolean hasWant();
    /**
     * <pre>
     * 想要拉取几期的数据,0的话服务器直接20期全发
     * </pre>
     *
     * <code>optional int32 want = 1;</code>
     * @return The want.
     */
    int getWant();

    /**
     * <pre>
     * 从大到小，从哪一期开始拉，0就是从最新那一期
     * </pre>
     *
     * <code>optional int32 startAge = 2;</code>
     * @return Whether the startAge field is set.
     */
    boolean hasStartAge();
    /**
     * <pre>
     * 从大到小，从哪一期开始拉，0就是从最新那一期
     * </pre>
     *
     * <code>optional int32 startAge = 2;</code>
     * @return The startAge.
     */
    int getStartAge();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderHistoryRankAsk}
   */
  public static final class BestCommanderHistoryRankAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderHistoryRankAsk)
      BestCommanderHistoryRankAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderHistoryRankAsk.newBuilder() to construct.
    private BestCommanderHistoryRankAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderHistoryRankAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderHistoryRankAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderHistoryRankAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              want_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              startAge_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk.Builder.class);
    }

    private int bitField0_;
    public static final int WANT_FIELD_NUMBER = 1;
    private int want_;
    /**
     * <pre>
     * 想要拉取几期的数据,0的话服务器直接20期全发
     * </pre>
     *
     * <code>optional int32 want = 1;</code>
     * @return Whether the want field is set.
     */
    @java.lang.Override
    public boolean hasWant() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 想要拉取几期的数据,0的话服务器直接20期全发
     * </pre>
     *
     * <code>optional int32 want = 1;</code>
     * @return The want.
     */
    @java.lang.Override
    public int getWant() {
      return want_;
    }

    public static final int STARTAGE_FIELD_NUMBER = 2;
    private int startAge_;
    /**
     * <pre>
     * 从大到小，从哪一期开始拉，0就是从最新那一期
     * </pre>
     *
     * <code>optional int32 startAge = 2;</code>
     * @return Whether the startAge field is set.
     */
    @java.lang.Override
    public boolean hasStartAge() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 从大到小，从哪一期开始拉，0就是从最新那一期
     * </pre>
     *
     * <code>optional int32 startAge = 2;</code>
     * @return The startAge.
     */
    @java.lang.Override
    public int getStartAge() {
      return startAge_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, want_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, startAge_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, want_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, startAge_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk) obj;

      if (hasWant() != other.hasWant()) return false;
      if (hasWant()) {
        if (getWant()
            != other.getWant()) return false;
      }
      if (hasStartAge() != other.hasStartAge()) return false;
      if (hasStartAge()) {
        if (getStartAge()
            != other.getStartAge()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasWant()) {
        hash = (37 * hash) + WANT_FIELD_NUMBER;
        hash = (53 * hash) + getWant();
      }
      if (hasStartAge()) {
        hash = (37 * hash) + STARTAGE_FIELD_NUMBER;
        hash = (53 * hash) + getStartAge();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderHistoryRankAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderHistoryRankAsk)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        want_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        startAge_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.want_ = want_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.startAge_ = startAge_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk.getDefaultInstance()) return this;
        if (other.hasWant()) {
          setWant(other.getWant());
        }
        if (other.hasStartAge()) {
          setStartAge(other.getStartAge());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int want_ ;
      /**
       * <pre>
       * 想要拉取几期的数据,0的话服务器直接20期全发
       * </pre>
       *
       * <code>optional int32 want = 1;</code>
       * @return Whether the want field is set.
       */
      @java.lang.Override
      public boolean hasWant() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 想要拉取几期的数据,0的话服务器直接20期全发
       * </pre>
       *
       * <code>optional int32 want = 1;</code>
       * @return The want.
       */
      @java.lang.Override
      public int getWant() {
        return want_;
      }
      /**
       * <pre>
       * 想要拉取几期的数据,0的话服务器直接20期全发
       * </pre>
       *
       * <code>optional int32 want = 1;</code>
       * @param value The want to set.
       * @return This builder for chaining.
       */
      public Builder setWant(int value) {
        bitField0_ |= 0x00000001;
        want_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 想要拉取几期的数据,0的话服务器直接20期全发
       * </pre>
       *
       * <code>optional int32 want = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearWant() {
        bitField0_ = (bitField0_ & ~0x00000001);
        want_ = 0;
        onChanged();
        return this;
      }

      private int startAge_ ;
      /**
       * <pre>
       * 从大到小，从哪一期开始拉，0就是从最新那一期
       * </pre>
       *
       * <code>optional int32 startAge = 2;</code>
       * @return Whether the startAge field is set.
       */
      @java.lang.Override
      public boolean hasStartAge() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 从大到小，从哪一期开始拉，0就是从最新那一期
       * </pre>
       *
       * <code>optional int32 startAge = 2;</code>
       * @return The startAge.
       */
      @java.lang.Override
      public int getStartAge() {
        return startAge_;
      }
      /**
       * <pre>
       * 从大到小，从哪一期开始拉，0就是从最新那一期
       * </pre>
       *
       * <code>optional int32 startAge = 2;</code>
       * @param value The startAge to set.
       * @return This builder for chaining.
       */
      public Builder setStartAge(int value) {
        bitField0_ |= 0x00000002;
        startAge_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 从大到小，从哪一期开始拉，0就是从最新那一期
       * </pre>
       *
       * <code>optional int32 startAge = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartAge() {
        bitField0_ = (bitField0_ & ~0x00000002);
        startAge_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderHistoryRankAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderHistoryRankAsk)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderHistoryRankAsk>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderHistoryRankAsk>() {
      @java.lang.Override
      public BestCommanderHistoryRankAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderHistoryRankAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderHistoryRankAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderHistoryRankAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderHistoryRankAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderHistoryRankAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.BestCommanderHistoryRank> 
        getHistoryRankList();
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    com.yorha.proto.StructMsg.BestCommanderHistoryRank getHistoryRank(int index);
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    int getHistoryRankCount();
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder> 
        getHistoryRankOrBuilderList();
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder getHistoryRankOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderHistoryRankAns}
   */
  public static final class BestCommanderHistoryRankAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderHistoryRankAns)
      BestCommanderHistoryRankAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderHistoryRankAns.newBuilder() to construct.
    private BestCommanderHistoryRankAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderHistoryRankAns() {
      historyRank_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderHistoryRankAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderHistoryRankAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                historyRank_ = new java.util.ArrayList<com.yorha.proto.StructMsg.BestCommanderHistoryRank>();
                mutable_bitField0_ |= 0x00000001;
              }
              historyRank_.add(
                  input.readMessage(com.yorha.proto.StructMsg.BestCommanderHistoryRank.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          historyRank_ = java.util.Collections.unmodifiableList(historyRank_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns.Builder.class);
    }

    public static final int HISTORYRANK_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructMsg.BestCommanderHistoryRank> historyRank_;
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.BestCommanderHistoryRank> getHistoryRankList() {
      return historyRank_;
    }
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder> 
        getHistoryRankOrBuilderList() {
      return historyRank_;
    }
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    @java.lang.Override
    public int getHistoryRankCount() {
      return historyRank_.size();
    }
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BestCommanderHistoryRank getHistoryRank(int index) {
      return historyRank_.get(index);
    }
    /**
     * <pre>
     * 从新到老排好序
     * </pre>
     *
     * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder getHistoryRankOrBuilder(
        int index) {
      return historyRank_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < historyRank_.size(); i++) {
        output.writeMessage(1, historyRank_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < historyRank_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, historyRank_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns) obj;

      if (!getHistoryRankList()
          .equals(other.getHistoryRankList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getHistoryRankCount() > 0) {
        hash = (37 * hash) + HISTORYRANK_FIELD_NUMBER;
        hash = (53 * hash) + getHistoryRankList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderHistoryRankAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderHistoryRankAns)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getHistoryRankFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (historyRankBuilder_ == null) {
          historyRank_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          historyRankBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderHistoryRankAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns(this);
        int from_bitField0_ = bitField0_;
        if (historyRankBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            historyRank_ = java.util.Collections.unmodifiableList(historyRank_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.historyRank_ = historyRank_;
        } else {
          result.historyRank_ = historyRankBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns.getDefaultInstance()) return this;
        if (historyRankBuilder_ == null) {
          if (!other.historyRank_.isEmpty()) {
            if (historyRank_.isEmpty()) {
              historyRank_ = other.historyRank_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureHistoryRankIsMutable();
              historyRank_.addAll(other.historyRank_);
            }
            onChanged();
          }
        } else {
          if (!other.historyRank_.isEmpty()) {
            if (historyRankBuilder_.isEmpty()) {
              historyRankBuilder_.dispose();
              historyRankBuilder_ = null;
              historyRank_ = other.historyRank_;
              bitField0_ = (bitField0_ & ~0x00000001);
              historyRankBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getHistoryRankFieldBuilder() : null;
            } else {
              historyRankBuilder_.addAllMessages(other.historyRank_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructMsg.BestCommanderHistoryRank> historyRank_ =
        java.util.Collections.emptyList();
      private void ensureHistoryRankIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          historyRank_ = new java.util.ArrayList<com.yorha.proto.StructMsg.BestCommanderHistoryRank>(historyRank_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.BestCommanderHistoryRank, com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder, com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder> historyRankBuilder_;

      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.BestCommanderHistoryRank> getHistoryRankList() {
        if (historyRankBuilder_ == null) {
          return java.util.Collections.unmodifiableList(historyRank_);
        } else {
          return historyRankBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public int getHistoryRankCount() {
        if (historyRankBuilder_ == null) {
          return historyRank_.size();
        } else {
          return historyRankBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public com.yorha.proto.StructMsg.BestCommanderHistoryRank getHistoryRank(int index) {
        if (historyRankBuilder_ == null) {
          return historyRank_.get(index);
        } else {
          return historyRankBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder setHistoryRank(
          int index, com.yorha.proto.StructMsg.BestCommanderHistoryRank value) {
        if (historyRankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistoryRankIsMutable();
          historyRank_.set(index, value);
          onChanged();
        } else {
          historyRankBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder setHistoryRank(
          int index, com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder builderForValue) {
        if (historyRankBuilder_ == null) {
          ensureHistoryRankIsMutable();
          historyRank_.set(index, builderForValue.build());
          onChanged();
        } else {
          historyRankBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder addHistoryRank(com.yorha.proto.StructMsg.BestCommanderHistoryRank value) {
        if (historyRankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistoryRankIsMutable();
          historyRank_.add(value);
          onChanged();
        } else {
          historyRankBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder addHistoryRank(
          int index, com.yorha.proto.StructMsg.BestCommanderHistoryRank value) {
        if (historyRankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistoryRankIsMutable();
          historyRank_.add(index, value);
          onChanged();
        } else {
          historyRankBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder addHistoryRank(
          com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder builderForValue) {
        if (historyRankBuilder_ == null) {
          ensureHistoryRankIsMutable();
          historyRank_.add(builderForValue.build());
          onChanged();
        } else {
          historyRankBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder addHistoryRank(
          int index, com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder builderForValue) {
        if (historyRankBuilder_ == null) {
          ensureHistoryRankIsMutable();
          historyRank_.add(index, builderForValue.build());
          onChanged();
        } else {
          historyRankBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder addAllHistoryRank(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.BestCommanderHistoryRank> values) {
        if (historyRankBuilder_ == null) {
          ensureHistoryRankIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, historyRank_);
          onChanged();
        } else {
          historyRankBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder clearHistoryRank() {
        if (historyRankBuilder_ == null) {
          historyRank_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          historyRankBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public Builder removeHistoryRank(int index) {
        if (historyRankBuilder_ == null) {
          ensureHistoryRankIsMutable();
          historyRank_.remove(index);
          onChanged();
        } else {
          historyRankBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder getHistoryRankBuilder(
          int index) {
        return getHistoryRankFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder getHistoryRankOrBuilder(
          int index) {
        if (historyRankBuilder_ == null) {
          return historyRank_.get(index);  } else {
          return historyRankBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder> 
           getHistoryRankOrBuilderList() {
        if (historyRankBuilder_ != null) {
          return historyRankBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(historyRank_);
        }
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder addHistoryRankBuilder() {
        return getHistoryRankFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.BestCommanderHistoryRank.getDefaultInstance());
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder addHistoryRankBuilder(
          int index) {
        return getHistoryRankFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.BestCommanderHistoryRank.getDefaultInstance());
      }
      /**
       * <pre>
       * 从新到老排好序
       * </pre>
       *
       * <code>repeated .com.yorha.proto.BestCommanderHistoryRank historyRank = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder> 
           getHistoryRankBuilderList() {
        return getHistoryRankFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.BestCommanderHistoryRank, com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder, com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder> 
          getHistoryRankFieldBuilder() {
        if (historyRankBuilder_ == null) {
          historyRankBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.BestCommanderHistoryRank, com.yorha.proto.StructMsg.BestCommanderHistoryRank.Builder, com.yorha.proto.StructMsg.BestCommanderHistoryRankOrBuilder>(
                  historyRank_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          historyRank_ = null;
        }
        return historyRankBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderHistoryRankAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderHistoryRankAns)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderHistoryRankAns>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderHistoryRankAns>() {
      @java.lang.Override
      public BestCommanderHistoryRankAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderHistoryRankAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderHistoryRankAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderHistoryRankAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderHistoryRankAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderChooseItemAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderChooseItemAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    boolean hasActId();
    /**
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    int getActId();

    /**
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    boolean hasUnitId();
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    int getUnitId();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int32 itemId = 4;</code>
     * @return Whether the itemId field is set.
     */
    boolean hasItemId();
    /**
     * <code>optional int32 itemId = 4;</code>
     * @return The itemId.
     */
    int getItemId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderChooseItemAsk}
   */
  public static final class BestCommanderChooseItemAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderChooseItemAsk)
      BestCommanderChooseItemAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderChooseItemAsk.newBuilder() to construct.
    private BestCommanderChooseItemAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderChooseItemAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderChooseItemAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderChooseItemAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              actId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              unitId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              itemId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ACTID_FIELD_NUMBER = 1;
    private int actId_;
    /**
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    @java.lang.Override
    public boolean hasActId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    @java.lang.Override
    public int getActId() {
      return actId_;
    }

    public static final int UNITID_FIELD_NUMBER = 2;
    private int unitId_;
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    @java.lang.Override
    public boolean hasUnitId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    @java.lang.Override
    public int getUnitId() {
      return unitId_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ITEMID_FIELD_NUMBER = 4;
    private int itemId_;
    /**
     * <code>optional int32 itemId = 4;</code>
     * @return Whether the itemId field is set.
     */
    @java.lang.Override
    public boolean hasItemId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 itemId = 4;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, unitId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, itemId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, unitId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, itemId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk) obj;

      if (hasActId() != other.hasActId()) return false;
      if (hasActId()) {
        if (getActId()
            != other.getActId()) return false;
      }
      if (hasUnitId() != other.hasUnitId()) return false;
      if (hasUnitId()) {
        if (getUnitId()
            != other.getUnitId()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasItemId() != other.hasItemId()) return false;
      if (hasItemId()) {
        if (getItemId()
            != other.getItemId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActId()) {
        hash = (37 * hash) + ACTID_FIELD_NUMBER;
        hash = (53 * hash) + getActId();
      }
      if (hasUnitId()) {
        hash = (37 * hash) + UNITID_FIELD_NUMBER;
        hash = (53 * hash) + getUnitId();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasItemId()) {
        hash = (37 * hash) + ITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getItemId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderChooseItemAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderChooseItemAsk)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        unitId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        itemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.actId_ = actId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.unitId_ = unitId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.itemId_ = itemId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk.getDefaultInstance()) return this;
        if (other.hasActId()) {
          setActId(other.getActId());
        }
        if (other.hasUnitId()) {
          setUnitId(other.getUnitId());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasItemId()) {
          setItemId(other.getItemId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int actId_ ;
      /**
       * <code>optional int32 actId = 1;</code>
       * @return Whether the actId field is set.
       */
      @java.lang.Override
      public boolean hasActId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @return The actId.
       */
      @java.lang.Override
      public int getActId() {
        return actId_;
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @param value The actId to set.
       * @return This builder for chaining.
       */
      public Builder setActId(int value) {
        bitField0_ |= 0x00000001;
        actId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        actId_ = 0;
        onChanged();
        return this;
      }

      private int unitId_ ;
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return Whether the unitId field is set.
       */
      @java.lang.Override
      public boolean hasUnitId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return The unitId.
       */
      @java.lang.Override
      public int getUnitId() {
        return unitId_;
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @param value The unitId to set.
       * @return This builder for chaining.
       */
      public Builder setUnitId(int value) {
        bitField0_ |= 0x00000002;
        unitId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 unitId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        unitId_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int itemId_ ;
      /**
       * <code>optional int32 itemId = 4;</code>
       * @return Whether the itemId field is set.
       */
      @java.lang.Override
      public boolean hasItemId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 itemId = 4;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <code>optional int32 itemId = 4;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        bitField0_ |= 0x00000008;
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 itemId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        itemId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderChooseItemAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderChooseItemAsk)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderChooseItemAsk>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderChooseItemAsk>() {
      @java.lang.Override
      public BestCommanderChooseItemAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderChooseItemAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderChooseItemAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderChooseItemAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderChooseItemAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderChooseItemAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderChooseItemAns}
   */
  public static final class BestCommanderChooseItemAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderChooseItemAns)
      BestCommanderChooseItemAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderChooseItemAns.newBuilder() to construct.
    private BestCommanderChooseItemAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderChooseItemAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderChooseItemAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderChooseItemAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderChooseItemAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderChooseItemAns)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderChooseItemAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderChooseItemAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderChooseItemAns)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderChooseItemAns>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderChooseItemAns>() {
      @java.lang.Override
      public BestCommanderChooseItemAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderChooseItemAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderChooseItemAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderChooseItemAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderChooseItemAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderGetVolumeAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderGetVolumeAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    boolean hasActId();
    /**
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    int getActId();

    /**
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderGetVolumeAsk}
   */
  public static final class BestCommanderGetVolumeAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderGetVolumeAsk)
      BestCommanderGetVolumeAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderGetVolumeAsk.newBuilder() to construct.
    private BestCommanderGetVolumeAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderGetVolumeAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderGetVolumeAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderGetVolumeAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              actId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ACTID_FIELD_NUMBER = 1;
    private int actId_;
    /**
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    @java.lang.Override
    public boolean hasActId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    @java.lang.Override
    public int getActId() {
      return actId_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk) obj;

      if (hasActId() != other.hasActId()) return false;
      if (hasActId()) {
        if (getActId()
            != other.getActId()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActId()) {
        hash = (37 * hash) + ACTID_FIELD_NUMBER;
        hash = (53 * hash) + getActId();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderGetVolumeAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderGetVolumeAsk)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.actId_ = actId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk.getDefaultInstance()) return this;
        if (other.hasActId()) {
          setActId(other.getActId());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int actId_ ;
      /**
       * <code>optional int32 actId = 1;</code>
       * @return Whether the actId field is set.
       */
      @java.lang.Override
      public boolean hasActId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @return The actId.
       */
      @java.lang.Override
      public int getActId() {
        return actId_;
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @param value The actId to set.
       * @return This builder for chaining.
       */
      public Builder setActId(int value) {
        bitField0_ |= 0x00000001;
        actId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 actId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        actId_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000002;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderGetVolumeAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderGetVolumeAsk)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderGetVolumeAsk>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderGetVolumeAsk>() {
      @java.lang.Override
      public BestCommanderGetVolumeAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderGetVolumeAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderGetVolumeAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderGetVolumeAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BestCommanderGetVolumeAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BestCommanderGetVolumeAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 volume = 2;</code>
     * @return Whether the volume field is set.
     */
    boolean hasVolume();
    /**
     * <code>optional int32 volume = 2;</code>
     * @return The volume.
     */
    int getVolume();

    /**
     * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
     * @return Whether the actStage field is set.
     */
    boolean hasActStage();
    /**
     * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
     * @return The actStage.
     */
    com.yorha.proto.CommonEnum.CommanderActivtyStage getActStage();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BestCommanderGetVolumeAns}
   */
  public static final class BestCommanderGetVolumeAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BestCommanderGetVolumeAns)
      BestCommanderGetVolumeAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BestCommanderGetVolumeAns.newBuilder() to construct.
    private BestCommanderGetVolumeAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BestCommanderGetVolumeAns() {
      actStage_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BestCommanderGetVolumeAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BestCommanderGetVolumeAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 16: {
              bitField0_ |= 0x00000001;
              volume_ = input.readInt32();
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.CommanderActivtyStage value = com.yorha.proto.CommonEnum.CommanderActivtyStage.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                actStage_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns.Builder.class);
    }

    private int bitField0_;
    public static final int VOLUME_FIELD_NUMBER = 2;
    private int volume_;
    /**
     * <code>optional int32 volume = 2;</code>
     * @return Whether the volume field is set.
     */
    @java.lang.Override
    public boolean hasVolume() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 volume = 2;</code>
     * @return The volume.
     */
    @java.lang.Override
    public int getVolume() {
      return volume_;
    }

    public static final int ACTSTAGE_FIELD_NUMBER = 3;
    private int actStage_;
    /**
     * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
     * @return Whether the actStage field is set.
     */
    @java.lang.Override public boolean hasActStage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
     * @return The actStage.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.CommanderActivtyStage getActStage() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.CommanderActivtyStage result = com.yorha.proto.CommonEnum.CommanderActivtyStage.valueOf(actStage_);
      return result == null ? com.yorha.proto.CommonEnum.CommanderActivtyStage.CAS_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(2, volume_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(3, actStage_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, volume_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, actStage_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns other = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns) obj;

      if (hasVolume() != other.hasVolume()) return false;
      if (hasVolume()) {
        if (getVolume()
            != other.getVolume()) return false;
      }
      if (hasActStage() != other.hasActStage()) return false;
      if (hasActStage()) {
        if (actStage_ != other.actStage_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasVolume()) {
        hash = (37 * hash) + VOLUME_FIELD_NUMBER;
        hash = (53 * hash) + getVolume();
      }
      if (hasActStage()) {
        hash = (37 * hash) + ACTSTAGE_FIELD_NUMBER;
        hash = (53 * hash) + actStage_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BestCommanderGetVolumeAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BestCommanderGetVolumeAns)
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns.class, com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        volume_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        actStage_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_BestCommanderGetVolumeAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns build() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns result = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.volume_ = volume_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.actStage_ = actStage_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns.getDefaultInstance()) return this;
        if (other.hasVolume()) {
          setVolume(other.getVolume());
        }
        if (other.hasActStage()) {
          setActStage(other.getActStage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int volume_ ;
      /**
       * <code>optional int32 volume = 2;</code>
       * @return Whether the volume field is set.
       */
      @java.lang.Override
      public boolean hasVolume() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 volume = 2;</code>
       * @return The volume.
       */
      @java.lang.Override
      public int getVolume() {
        return volume_;
      }
      /**
       * <code>optional int32 volume = 2;</code>
       * @param value The volume to set.
       * @return This builder for chaining.
       */
      public Builder setVolume(int value) {
        bitField0_ |= 0x00000001;
        volume_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 volume = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVolume() {
        bitField0_ = (bitField0_ & ~0x00000001);
        volume_ = 0;
        onChanged();
        return this;
      }

      private int actStage_ = 0;
      /**
       * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
       * @return Whether the actStage field is set.
       */
      @java.lang.Override public boolean hasActStage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
       * @return The actStage.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.CommanderActivtyStage getActStage() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.CommanderActivtyStage result = com.yorha.proto.CommonEnum.CommanderActivtyStage.valueOf(actStage_);
        return result == null ? com.yorha.proto.CommonEnum.CommanderActivtyStage.CAS_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
       * @param value The actStage to set.
       * @return This builder for chaining.
       */
      public Builder setActStage(com.yorha.proto.CommonEnum.CommanderActivtyStage value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        actStage_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CommanderActivtyStage actStage = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearActStage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        actStage_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BestCommanderGetVolumeAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BestCommanderGetVolumeAns)
    private static final com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BestCommanderGetVolumeAns>
        PARSER = new com.google.protobuf.AbstractParser<BestCommanderGetVolumeAns>() {
      @java.lang.Override
      public BestCommanderGetVolumeAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BestCommanderGetVolumeAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BestCommanderGetVolumeAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BestCommanderGetVolumeAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.BestCommanderGetVolumeAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetLotteryInfoAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetLotteryInfoAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    boolean hasActivityId();
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    int getActivityId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetLotteryInfoAsk}
   */
  public static final class GetLotteryInfoAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetLotteryInfoAsk)
      GetLotteryInfoAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetLotteryInfoAsk.newBuilder() to construct.
    private GetLotteryInfoAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetLotteryInfoAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetLotteryInfoAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetLotteryInfoAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              activityId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk.class, com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ACTIVITYID_FIELD_NUMBER = 1;
    private int activityId_;
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return Whether the activityId field is set.
     */
    @java.lang.Override
    public boolean hasActivityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 activityId = 1;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, activityId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, activityId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk other = (com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk) obj;

      if (hasActivityId() != other.hasActivityId()) return false;
      if (hasActivityId()) {
        if (getActivityId()
            != other.getActivityId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActivityId()) {
        hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
        hash = (53 * hash) + getActivityId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetLotteryInfoAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetLotteryInfoAsk)
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk.class, com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        activityId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk build() {
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk result = new com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.activityId_ = activityId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk.getDefaultInstance()) return this;
        if (other.hasActivityId()) {
          setActivityId(other.getActivityId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int activityId_ ;
      /**
       * <code>optional int32 activityId = 1;</code>
       * @return Whether the activityId field is set.
       */
      @java.lang.Override
      public boolean hasActivityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 activityId = 1;</code>
       * @return The activityId.
       */
      @java.lang.Override
      public int getActivityId() {
        return activityId_;
      }
      /**
       * <code>optional int32 activityId = 1;</code>
       * @param value The activityId to set.
       * @return This builder for chaining.
       */
      public Builder setActivityId(int value) {
        bitField0_ |= 0x00000001;
        activityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 activityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActivityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        activityId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetLotteryInfoAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetLotteryInfoAsk)
    private static final com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetLotteryInfoAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetLotteryInfoAsk>() {
      @java.lang.Override
      public GetLotteryInfoAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetLotteryInfoAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetLotteryInfoAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetLotteryInfoAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetLotteryInfoAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetLotteryInfoAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 volume = 1;</code>
     * @return Whether the volume field is set.
     */
    boolean hasVolume();
    /**
     * <code>optional int32 volume = 1;</code>
     * @return The volume.
     */
    int getVolume();

    /**
     * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
     * @return Whether the stage field is set.
     */
    boolean hasStage();
    /**
     * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
     * @return The stage.
     */
    com.yorha.proto.CommonEnum.ZoneSeasonStage getStage();

    /**
     * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
     * @return Whether the season field is set.
     */
    boolean hasSeason();
    /**
     * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
     * @return The season.
     */
    com.yorha.proto.CommonEnum.ZoneSeason getSeason();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetLotteryInfoAns}
   */
  public static final class GetLotteryInfoAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetLotteryInfoAns)
      GetLotteryInfoAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetLotteryInfoAns.newBuilder() to construct.
    private GetLotteryInfoAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetLotteryInfoAns() {
      stage_ = 0;
      season_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetLotteryInfoAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetLotteryInfoAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              volume_ = input.readInt32();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ZoneSeasonStage value = com.yorha.proto.CommonEnum.ZoneSeasonStage.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                stage_ = rawValue;
              }
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ZoneSeason value = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                season_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns.class, com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns.Builder.class);
    }

    private int bitField0_;
    public static final int VOLUME_FIELD_NUMBER = 1;
    private int volume_;
    /**
     * <code>optional int32 volume = 1;</code>
     * @return Whether the volume field is set.
     */
    @java.lang.Override
    public boolean hasVolume() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 volume = 1;</code>
     * @return The volume.
     */
    @java.lang.Override
    public int getVolume() {
      return volume_;
    }

    public static final int STAGE_FIELD_NUMBER = 2;
    private int stage_;
    /**
     * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
     * @return Whether the stage field is set.
     */
    @java.lang.Override public boolean hasStage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
     * @return The stage.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ZoneSeasonStage getStage() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ZoneSeasonStage result = com.yorha.proto.CommonEnum.ZoneSeasonStage.valueOf(stage_);
      return result == null ? com.yorha.proto.CommonEnum.ZoneSeasonStage.ZSS_NONE : result;
    }

    public static final int SEASON_FIELD_NUMBER = 3;
    private int season_;
    /**
     * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
     * @return Whether the season field is set.
     */
    @java.lang.Override public boolean hasSeason() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
     * @return The season.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ZoneSeason getSeason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ZoneSeason result = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(season_);
      return result == null ? com.yorha.proto.CommonEnum.ZoneSeason.ZS_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, volume_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, stage_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(3, season_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, volume_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, stage_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, season_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns other = (com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns) obj;

      if (hasVolume() != other.hasVolume()) return false;
      if (hasVolume()) {
        if (getVolume()
            != other.getVolume()) return false;
      }
      if (hasStage() != other.hasStage()) return false;
      if (hasStage()) {
        if (stage_ != other.stage_) return false;
      }
      if (hasSeason() != other.hasSeason()) return false;
      if (hasSeason()) {
        if (season_ != other.season_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasVolume()) {
        hash = (37 * hash) + VOLUME_FIELD_NUMBER;
        hash = (53 * hash) + getVolume();
      }
      if (hasStage()) {
        hash = (37 * hash) + STAGE_FIELD_NUMBER;
        hash = (53 * hash) + stage_;
      }
      if (hasSeason()) {
        hash = (37 * hash) + SEASON_FIELD_NUMBER;
        hash = (53 * hash) + season_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetLotteryInfoAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetLotteryInfoAns)
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns.class, com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        volume_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        stage_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        season_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivitySchedule.internal_static_com_yorha_proto_GetLotteryInfoAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns build() {
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns buildPartial() {
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns result = new com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.volume_ = volume_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.stage_ = stage_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.season_ = season_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns) {
          return mergeFrom((com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns other) {
        if (other == com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns.getDefaultInstance()) return this;
        if (other.hasVolume()) {
          setVolume(other.getVolume());
        }
        if (other.hasStage()) {
          setStage(other.getStage());
        }
        if (other.hasSeason()) {
          setSeason(other.getSeason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int volume_ ;
      /**
       * <code>optional int32 volume = 1;</code>
       * @return Whether the volume field is set.
       */
      @java.lang.Override
      public boolean hasVolume() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 volume = 1;</code>
       * @return The volume.
       */
      @java.lang.Override
      public int getVolume() {
        return volume_;
      }
      /**
       * <code>optional int32 volume = 1;</code>
       * @param value The volume to set.
       * @return This builder for chaining.
       */
      public Builder setVolume(int value) {
        bitField0_ |= 0x00000001;
        volume_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 volume = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVolume() {
        bitField0_ = (bitField0_ & ~0x00000001);
        volume_ = 0;
        onChanged();
        return this;
      }

      private int stage_ = 0;
      /**
       * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
       * @return Whether the stage field is set.
       */
      @java.lang.Override public boolean hasStage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
       * @return The stage.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ZoneSeasonStage getStage() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ZoneSeasonStage result = com.yorha.proto.CommonEnum.ZoneSeasonStage.valueOf(stage_);
        return result == null ? com.yorha.proto.CommonEnum.ZoneSeasonStage.ZSS_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
       * @param value The stage to set.
       * @return This builder for chaining.
       */
      public Builder setStage(com.yorha.proto.CommonEnum.ZoneSeasonStage value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        stage_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeasonStage stage = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        stage_ = 0;
        onChanged();
        return this;
      }

      private int season_ = 0;
      /**
       * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
       * @return Whether the season field is set.
       */
      @java.lang.Override public boolean hasSeason() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
       * @return The season.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ZoneSeason getSeason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ZoneSeason result = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(season_);
        return result == null ? com.yorha.proto.CommonEnum.ZoneSeason.ZS_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
       * @param value The season to set.
       * @return This builder for chaining.
       */
      public Builder setSeason(com.yorha.proto.CommonEnum.ZoneSeason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        season_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason season = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeason() {
        bitField0_ = (bitField0_ & ~0x00000004);
        season_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetLotteryInfoAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetLotteryInfoAns)
    private static final com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns();
    }

    public static com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetLotteryInfoAns>
        PARSER = new com.google.protobuf.AbstractParser<GetLotteryInfoAns>() {
      @java.lang.Override
      public GetLotteryInfoAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetLotteryInfoAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetLotteryInfoAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetLotteryInfoAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivitySchedule.GetLotteryInfoAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderFetchAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderFetchAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderFetchAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderFetchAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderFetchItem_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderFetchItem_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderHistoryRankAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderHistoryRankAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderChooseItemAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderChooseItemAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderChooseItemAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderChooseItemAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BestCommanderGetVolumeAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BestCommanderGetVolumeAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetLotteryInfoAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetLotteryInfoAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetLotteryInfoAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetLotteryInfoAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n3ss_proto/gen/scene/ss_scene_activity_s" +
      "chedule.proto\022\017com.yorha.proto\032%ss_proto" +
      "/gen/common/common_enum.proto\032$ss_proto/" +
      "gen/common/struct_msg.proto\"M\n\025BestComma" +
      "nderFetchAsk\022\020\n\010playerId\030\001 \001(\003\022\r\n\005actId\030" +
      "\002 \001(\005\022\023\n\013childActIds\030\003 \003(\005\"T\n\025BestComman" +
      "derFetchAns\022;\n\nfetchItems\030\001 \003(\0132\'.com.yo" +
      "rha.proto.BestCommanderFetchItem\"5\n\026Best" +
      "CommanderFetchItem\022\r\n\005actId\030\001 \001(\005\022\014\n\004ran" +
      "k\030\002 \001(\005\"=\n\033BestCommanderHistoryRankAsk\022\014" +
      "\n\004want\030\001 \001(\005\022\020\n\010startAge\030\002 \001(\005\"]\n\033BestCo" +
      "mmanderHistoryRankAns\022>\n\013historyRank\030\001 \003" +
      "(\0132).com.yorha.proto.BestCommanderHistor" +
      "yRank\"]\n\032BestCommanderChooseItemAsk\022\r\n\005a" +
      "ctId\030\001 \001(\005\022\016\n\006unitId\030\002 \001(\005\022\020\n\010playerId\030\003" +
      " \001(\003\022\016\n\006itemId\030\004 \001(\005\"\034\n\032BestCommanderCho" +
      "oseItemAns\"<\n\031BestCommanderGetVolumeAsk\022" +
      "\r\n\005actId\030\001 \001(\005\022\020\n\010playerId\030\002 \001(\003\"e\n\031Best" +
      "CommanderGetVolumeAns\022\016\n\006volume\030\002 \001(\005\0228\n" +
      "\010actStage\030\003 \001(\0162&.com.yorha.proto.Comman" +
      "derActivtyStage\"\'\n\021GetLotteryInfoAsk\022\022\n\n" +
      "activityId\030\001 \001(\005\"\201\001\n\021GetLotteryInfoAns\022\016" +
      "\n\006volume\030\001 \001(\005\022/\n\005stage\030\002 \001(\0162 .com.yorh" +
      "a.proto.ZoneSeasonStage\022+\n\006season\030\003 \001(\0162" +
      "\033.com.yorha.proto.ZoneSeasonB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_BestCommanderFetchAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_BestCommanderFetchAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderFetchAsk_descriptor,
        new java.lang.String[] { "PlayerId", "ActId", "ChildActIds", });
    internal_static_com_yorha_proto_BestCommanderFetchAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_BestCommanderFetchAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderFetchAns_descriptor,
        new java.lang.String[] { "FetchItems", });
    internal_static_com_yorha_proto_BestCommanderFetchItem_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_BestCommanderFetchItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderFetchItem_descriptor,
        new java.lang.String[] { "ActId", "Rank", });
    internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderHistoryRankAsk_descriptor,
        new java.lang.String[] { "Want", "StartAge", });
    internal_static_com_yorha_proto_BestCommanderHistoryRankAns_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_BestCommanderHistoryRankAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderHistoryRankAns_descriptor,
        new java.lang.String[] { "HistoryRank", });
    internal_static_com_yorha_proto_BestCommanderChooseItemAsk_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_BestCommanderChooseItemAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderChooseItemAsk_descriptor,
        new java.lang.String[] { "ActId", "UnitId", "PlayerId", "ItemId", });
    internal_static_com_yorha_proto_BestCommanderChooseItemAns_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_BestCommanderChooseItemAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderChooseItemAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderGetVolumeAsk_descriptor,
        new java.lang.String[] { "ActId", "PlayerId", });
    internal_static_com_yorha_proto_BestCommanderGetVolumeAns_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_BestCommanderGetVolumeAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BestCommanderGetVolumeAns_descriptor,
        new java.lang.String[] { "Volume", "ActStage", });
    internal_static_com_yorha_proto_GetLotteryInfoAsk_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_GetLotteryInfoAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetLotteryInfoAsk_descriptor,
        new java.lang.String[] { "ActivityId", });
    internal_static_com_yorha_proto_GetLotteryInfoAns_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_GetLotteryInfoAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetLotteryInfoAns_descriptor,
        new java.lang.String[] { "Volume", "Stage", "Season", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

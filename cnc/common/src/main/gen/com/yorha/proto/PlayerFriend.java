// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_friend.proto

package com.yorha.proto;

public final class PlayerFriend {
  private PlayerFriend() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_AddFriend_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_AddFriend_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 对方玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 对方玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 对方玩家所在zone
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 对方玩家所在zone
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_AddFriend_C2S}
   */
  public static final class Player_AddFriend_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_AddFriend_C2S)
      Player_AddFriend_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_AddFriend_C2S.newBuilder() to construct.
    private Player_AddFriend_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_AddFriend_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_AddFriend_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_AddFriend_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_AddFriend_C2S.class, com.yorha.proto.PlayerFriend.Player_AddFriend_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 对方玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 对方玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 2;
    private int zoneId_;
    /**
     * <pre>
     * 对方玩家所在zone
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 对方玩家所在zone
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_AddFriend_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_AddFriend_C2S other = (com.yorha.proto.PlayerFriend.Player_AddFriend_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_AddFriend_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_AddFriend_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_AddFriend_C2S)
        com.yorha.proto.PlayerFriend.Player_AddFriend_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_AddFriend_C2S.class, com.yorha.proto.PlayerFriend.Player_AddFriend_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_AddFriend_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AddFriend_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_AddFriend_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AddFriend_C2S build() {
        com.yorha.proto.PlayerFriend.Player_AddFriend_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AddFriend_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_AddFriend_C2S result = new com.yorha.proto.PlayerFriend.Player_AddFriend_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_AddFriend_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_AddFriend_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_AddFriend_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_AddFriend_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_AddFriend_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_AddFriend_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 对方玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 对方玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 对方玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 对方玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 对方玩家所在zone
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 对方玩家所在zone
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 对方玩家所在zone
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000002;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 对方玩家所在zone
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_AddFriend_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_AddFriend_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_AddFriend_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_AddFriend_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_AddFriend_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_AddFriend_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_AddFriend_C2S>() {
      @java.lang.Override
      public Player_AddFriend_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_AddFriend_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_AddFriend_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_AddFriend_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_AddFriend_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_AddFriend_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_AddFriend_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_AddFriend_S2C}
   */
  public static final class Player_AddFriend_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_AddFriend_S2C)
      Player_AddFriend_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_AddFriend_S2C.newBuilder() to construct.
    private Player_AddFriend_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_AddFriend_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_AddFriend_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_AddFriend_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_AddFriend_S2C.class, com.yorha.proto.PlayerFriend.Player_AddFriend_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_AddFriend_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_AddFriend_S2C other = (com.yorha.proto.PlayerFriend.Player_AddFriend_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_AddFriend_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_AddFriend_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_AddFriend_S2C)
        com.yorha.proto.PlayerFriend.Player_AddFriend_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_AddFriend_S2C.class, com.yorha.proto.PlayerFriend.Player_AddFriend_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_AddFriend_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AddFriend_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AddFriend_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_AddFriend_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AddFriend_S2C build() {
        com.yorha.proto.PlayerFriend.Player_AddFriend_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AddFriend_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_AddFriend_S2C result = new com.yorha.proto.PlayerFriend.Player_AddFriend_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_AddFriend_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_AddFriend_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_AddFriend_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_AddFriend_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_AddFriend_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_AddFriend_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_AddFriend_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_AddFriend_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_AddFriend_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_AddFriend_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_AddFriend_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_AddFriend_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_AddFriend_S2C>() {
      @java.lang.Override
      public Player_AddFriend_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_AddFriend_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_AddFriend_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_AddFriend_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_AddFriend_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_AgreeFriendApply_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_AgreeFriendApply_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_AgreeFriendApply_C2S}
   */
  public static final class Player_AgreeFriendApply_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_AgreeFriendApply_C2S)
      Player_AgreeFriendApply_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_AgreeFriendApply_C2S.newBuilder() to construct.
    private Player_AgreeFriendApply_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_AgreeFriendApply_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_AgreeFriendApply_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_AgreeFriendApply_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S.class, com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S other = (com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_AgreeFriendApply_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_AgreeFriendApply_C2S)
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S.class, com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S build() {
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S result = new com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_AgreeFriendApply_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_AgreeFriendApply_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_AgreeFriendApply_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_AgreeFriendApply_C2S>() {
      @java.lang.Override
      public Player_AgreeFriendApply_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_AgreeFriendApply_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_AgreeFriendApply_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_AgreeFriendApply_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_AgreeFriendApply_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_AgreeFriendApply_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_AgreeFriendApply_S2C}
   */
  public static final class Player_AgreeFriendApply_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_AgreeFriendApply_S2C)
      Player_AgreeFriendApply_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_AgreeFriendApply_S2C.newBuilder() to construct.
    private Player_AgreeFriendApply_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_AgreeFriendApply_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_AgreeFriendApply_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_AgreeFriendApply_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C.class, com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C other = (com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_AgreeFriendApply_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_AgreeFriendApply_S2C)
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C.class, com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C build() {
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C result = new com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_AgreeFriendApply_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_AgreeFriendApply_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_AgreeFriendApply_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_AgreeFriendApply_S2C>() {
      @java.lang.Override
      public Player_AgreeFriendApply_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_AgreeFriendApply_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_AgreeFriendApply_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_AgreeFriendApply_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_AgreeFriendApply_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_RefuseFriendApply_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_RefuseFriendApply_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_RefuseFriendApply_C2S}
   */
  public static final class Player_RefuseFriendApply_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_RefuseFriendApply_C2S)
      Player_RefuseFriendApply_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_RefuseFriendApply_C2S.newBuilder() to construct.
    private Player_RefuseFriendApply_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_RefuseFriendApply_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_RefuseFriendApply_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_RefuseFriendApply_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S.class, com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 申请者id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S other = (com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_RefuseFriendApply_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_RefuseFriendApply_C2S)
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S.class, com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S build() {
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S result = new com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 申请者id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_RefuseFriendApply_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_RefuseFriendApply_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_RefuseFriendApply_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_RefuseFriendApply_C2S>() {
      @java.lang.Override
      public Player_RefuseFriendApply_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_RefuseFriendApply_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_RefuseFriendApply_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_RefuseFriendApply_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_RefuseFriendApply_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_RefuseFriendApply_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_RefuseFriendApply_S2C}
   */
  public static final class Player_RefuseFriendApply_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_RefuseFriendApply_S2C)
      Player_RefuseFriendApply_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_RefuseFriendApply_S2C.newBuilder() to construct.
    private Player_RefuseFriendApply_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_RefuseFriendApply_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_RefuseFriendApply_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_RefuseFriendApply_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C.class, com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C other = (com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_RefuseFriendApply_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_RefuseFriendApply_S2C)
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C.class, com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C build() {
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C result = new com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_RefuseFriendApply_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_RefuseFriendApply_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_RefuseFriendApply_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_RefuseFriendApply_S2C>() {
      @java.lang.Override
      public Player_RefuseFriendApply_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_RefuseFriendApply_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_RefuseFriendApply_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_RefuseFriendApply_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_RefuseFriendApply_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_DelFriend_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_DelFriend_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 删除玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 删除玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_DelFriend_C2S}
   */
  public static final class Player_DelFriend_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_DelFriend_C2S)
      Player_DelFriend_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_DelFriend_C2S.newBuilder() to construct.
    private Player_DelFriend_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_DelFriend_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_DelFriend_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_DelFriend_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_DelFriend_C2S.class, com.yorha.proto.PlayerFriend.Player_DelFriend_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 删除玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 删除玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_DelFriend_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_DelFriend_C2S other = (com.yorha.proto.PlayerFriend.Player_DelFriend_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_DelFriend_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_DelFriend_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_DelFriend_C2S)
        com.yorha.proto.PlayerFriend.Player_DelFriend_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_DelFriend_C2S.class, com.yorha.proto.PlayerFriend.Player_DelFriend_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_DelFriend_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_DelFriend_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_DelFriend_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_DelFriend_C2S build() {
        com.yorha.proto.PlayerFriend.Player_DelFriend_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_DelFriend_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_DelFriend_C2S result = new com.yorha.proto.PlayerFriend.Player_DelFriend_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_DelFriend_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_DelFriend_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_DelFriend_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_DelFriend_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_DelFriend_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_DelFriend_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 删除玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 删除玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 删除玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 删除玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_DelFriend_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_DelFriend_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_DelFriend_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_DelFriend_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_DelFriend_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_DelFriend_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_DelFriend_C2S>() {
      @java.lang.Override
      public Player_DelFriend_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_DelFriend_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_DelFriend_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_DelFriend_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_DelFriend_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_DelFriend_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_DelFriend_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_DelFriend_S2C}
   */
  public static final class Player_DelFriend_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_DelFriend_S2C)
      Player_DelFriend_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_DelFriend_S2C.newBuilder() to construct.
    private Player_DelFriend_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_DelFriend_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_DelFriend_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_DelFriend_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_DelFriend_S2C.class, com.yorha.proto.PlayerFriend.Player_DelFriend_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_DelFriend_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_DelFriend_S2C other = (com.yorha.proto.PlayerFriend.Player_DelFriend_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_DelFriend_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_DelFriend_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_DelFriend_S2C)
        com.yorha.proto.PlayerFriend.Player_DelFriend_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_DelFriend_S2C.class, com.yorha.proto.PlayerFriend.Player_DelFriend_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_DelFriend_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_DelFriend_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_DelFriend_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_DelFriend_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_DelFriend_S2C build() {
        com.yorha.proto.PlayerFriend.Player_DelFriend_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_DelFriend_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_DelFriend_S2C result = new com.yorha.proto.PlayerFriend.Player_DelFriend_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_DelFriend_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_DelFriend_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_DelFriend_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_DelFriend_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_DelFriend_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_DelFriend_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_DelFriend_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_DelFriend_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_DelFriend_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_DelFriend_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_DelFriend_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_DelFriend_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_DelFriend_S2C>() {
      @java.lang.Override
      public Player_DelFriend_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_DelFriend_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_DelFriend_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_DelFriend_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_DelFriend_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ShieldPlayer_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ShieldPlayer_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 屏蔽玩家的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 屏蔽玩家的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ShieldPlayer_C2S}
   */
  public static final class Player_ShieldPlayer_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ShieldPlayer_C2S)
      Player_ShieldPlayer_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ShieldPlayer_C2S.newBuilder() to construct.
    private Player_ShieldPlayer_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ShieldPlayer_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ShieldPlayer_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ShieldPlayer_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S.class, com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 2;
    private int zoneId_;
    /**
     * <pre>
     * 屏蔽玩家的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 屏蔽玩家的zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S other = (com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ShieldPlayer_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ShieldPlayer_C2S)
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S.class, com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S build() {
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S result = new com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 屏蔽玩家的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 屏蔽玩家的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 屏蔽玩家的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000002;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 屏蔽玩家的zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ShieldPlayer_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ShieldPlayer_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ShieldPlayer_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ShieldPlayer_C2S>() {
      @java.lang.Override
      public Player_ShieldPlayer_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ShieldPlayer_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ShieldPlayer_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ShieldPlayer_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ShieldPlayer_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ShieldPlayer_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ShieldPlayer_S2C}
   */
  public static final class Player_ShieldPlayer_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ShieldPlayer_S2C)
      Player_ShieldPlayer_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ShieldPlayer_S2C.newBuilder() to construct.
    private Player_ShieldPlayer_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ShieldPlayer_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ShieldPlayer_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ShieldPlayer_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C.class, com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C other = (com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ShieldPlayer_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ShieldPlayer_S2C)
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C.class, com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C build() {
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C result = new com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ShieldPlayer_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ShieldPlayer_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ShieldPlayer_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ShieldPlayer_S2C>() {
      @java.lang.Override
      public Player_ShieldPlayer_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ShieldPlayer_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ShieldPlayer_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ShieldPlayer_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_ShieldPlayer_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_RemoveShield_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_RemoveShield_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 解除屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 解除屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_RemoveShield_C2S}
   */
  public static final class Player_RemoveShield_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_RemoveShield_C2S)
      Player_RemoveShield_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_RemoveShield_C2S.newBuilder() to construct.
    private Player_RemoveShield_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_RemoveShield_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_RemoveShield_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_RemoveShield_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S.class, com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 解除屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 解除屏蔽玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S other = (com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_RemoveShield_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_RemoveShield_C2S)
        com.yorha.proto.PlayerFriend.Player_RemoveShield_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S.class, com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S build() {
        com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S result = new com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 解除屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 解除屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 解除屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 解除屏蔽玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_RemoveShield_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_RemoveShield_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_RemoveShield_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_RemoveShield_C2S>() {
      @java.lang.Override
      public Player_RemoveShield_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_RemoveShield_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_RemoveShield_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_RemoveShield_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_RemoveShield_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_RemoveShield_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_RemoveShield_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_RemoveShield_S2C}
   */
  public static final class Player_RemoveShield_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_RemoveShield_S2C)
      Player_RemoveShield_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_RemoveShield_S2C.newBuilder() to construct.
    private Player_RemoveShield_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_RemoveShield_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_RemoveShield_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_RemoveShield_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C.class, com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C other = (com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_RemoveShield_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_RemoveShield_S2C)
        com.yorha.proto.PlayerFriend.Player_RemoveShield_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C.class, com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_RemoveShield_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C build() {
        com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C result = new com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_RemoveShield_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_RemoveShield_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_RemoveShield_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_RemoveShield_S2C>() {
      @java.lang.Override
      public Player_RemoveShield_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_RemoveShield_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_RemoveShield_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_RemoveShield_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_RemoveShield_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SearchPlayer_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SearchPlayer_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SearchPlayer_C2S}
   */
  public static final class Player_SearchPlayer_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SearchPlayer_C2S)
      Player_SearchPlayer_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SearchPlayer_C2S.newBuilder() to construct.
    private Player_SearchPlayer_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SearchPlayer_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SearchPlayer_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SearchPlayer_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S.class, com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S other = (com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SearchPlayer_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SearchPlayer_C2S)
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S.class, com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S build() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S result = new com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SearchPlayer_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SearchPlayer_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SearchPlayer_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SearchPlayer_C2S>() {
      @java.lang.Override
      public Player_SearchPlayer_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SearchPlayer_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SearchPlayer_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SearchPlayer_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_SearchPlayer_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SearchPlayer_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SearchPlayer_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家是否存在
     * </pre>
     *
     * <code>optional bool exist = 1;</code>
     * @return Whether the exist field is set.
     */
    boolean hasExist();
    /**
     * <pre>
     * 玩家是否存在
     * </pre>
     *
     * <code>optional bool exist = 1;</code>
     * @return The exist.
     */
    boolean getExist();

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 玩家当前所在zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 玩家当前所在zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    int getZoneId();

    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
     * @return The cardHead.
     */
    com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
     */
    com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SearchPlayer_S2C}
   */
  public static final class Player_SearchPlayer_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SearchPlayer_S2C)
      Player_SearchPlayer_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SearchPlayer_S2C.newBuilder() to construct.
    private Player_SearchPlayer_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SearchPlayer_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SearchPlayer_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SearchPlayer_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              exist_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              playerId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              zoneId_ = input.readInt32();
              break;
            }
            case 34: {
              com.yorha.proto.StructPB.PlayerCardHeadPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.StructPB.PlayerCardHeadPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C.class, com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int EXIST_FIELD_NUMBER = 1;
    private boolean exist_;
    /**
     * <pre>
     * 玩家是否存在
     * </pre>
     *
     * <code>optional bool exist = 1;</code>
     * @return Whether the exist field is set.
     */
    @java.lang.Override
    public boolean hasExist() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家是否存在
     * </pre>
     *
     * <code>optional bool exist = 1;</code>
     * @return The exist.
     */
    @java.lang.Override
    public boolean getExist() {
      return exist_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 3;
    private int zoneId_;
    /**
     * <pre>
     * 玩家当前所在zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 玩家当前所在zoneId
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    public static final int CARDHEAD_FIELD_NUMBER = 4;
    private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, exist_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, playerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, zoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getCardHead());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, exist_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, zoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCardHead());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C other = (com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C) obj;

      if (hasExist() != other.hasExist()) return false;
      if (hasExist()) {
        if (getExist()
            != other.getExist()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasExist()) {
        hash = (37 * hash) + EXIST_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getExist());
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SearchPlayer_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SearchPlayer_S2C)
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C.class, com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        exist_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayer_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C build() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C result = new com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.exist_ = exist_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C.getDefaultInstance()) return this;
        if (other.hasExist()) {
          setExist(other.getExist());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean exist_ ;
      /**
       * <pre>
       * 玩家是否存在
       * </pre>
       *
       * <code>optional bool exist = 1;</code>
       * @return Whether the exist field is set.
       */
      @java.lang.Override
      public boolean hasExist() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家是否存在
       * </pre>
       *
       * <code>optional bool exist = 1;</code>
       * @return The exist.
       */
      @java.lang.Override
      public boolean getExist() {
        return exist_;
      }
      /**
       * <pre>
       * 玩家是否存在
       * </pre>
       *
       * <code>optional bool exist = 1;</code>
       * @param value The exist to set.
       * @return This builder for chaining.
       */
      public Builder setExist(boolean value) {
        bitField0_ |= 0x00000001;
        exist_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家是否存在
       * </pre>
       *
       * <code>optional bool exist = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearExist() {
        bitField0_ = (bitField0_ & ~0x00000001);
        exist_ = false;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000002;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 玩家当前所在zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 玩家当前所在zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 玩家当前所在zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000004;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家当前所在zoneId
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        zoneId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> cardHeadBuilder_;
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       */
      public Builder setCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.StructPB.PlayerCardHeadPB.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.StructPB.PlayerCardHeadPB.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SearchPlayer_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SearchPlayer_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SearchPlayer_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SearchPlayer_S2C>() {
      @java.lang.Override
      public Player_SearchPlayer_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SearchPlayer_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SearchPlayer_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SearchPlayer_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_SearchPlayer_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SearchPlayerByName_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SearchPlayerByName_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SearchPlayerByName_C2S}
   */
  public static final class Player_SearchPlayerByName_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SearchPlayerByName_C2S)
      Player_SearchPlayerByName_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SearchPlayerByName_C2S.newBuilder() to construct.
    private Player_SearchPlayerByName_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SearchPlayerByName_C2S() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SearchPlayerByName_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SearchPlayerByName_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              name_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S.class, com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S other = (com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S) obj;

      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SearchPlayerByName_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SearchPlayerByName_C2S)
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S.class, com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S build() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S buildPartial() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S result = new com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.name_ = name_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S other) {
        if (other == com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S.getDefaultInstance()) return this;
        if (other.hasName()) {
          bitField0_ |= 0x00000001;
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 1;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string name = 1;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 1;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 1;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 1;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SearchPlayerByName_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SearchPlayerByName_C2S)
    private static final com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S();
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SearchPlayerByName_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SearchPlayerByName_C2S>() {
      @java.lang.Override
      public Player_SearchPlayerByName_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SearchPlayerByName_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SearchPlayerByName_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SearchPlayerByName_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SearchPlayerByName_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SearchPlayerByName_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    java.util.List<com.yorha.proto.StructPB.PlayerCardInfoPB> 
        getCardListList();
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    com.yorha.proto.StructPB.PlayerCardInfoPB getCardList(int index);
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    int getCardListCount();
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> 
        getCardListOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SearchPlayerByName_S2C}
   */
  public static final class Player_SearchPlayerByName_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SearchPlayerByName_S2C)
      Player_SearchPlayerByName_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SearchPlayerByName_S2C.newBuilder() to construct.
    private Player_SearchPlayerByName_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SearchPlayerByName_S2C() {
      cardList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SearchPlayerByName_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SearchPlayerByName_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                cardList_ = new java.util.ArrayList<com.yorha.proto.StructPB.PlayerCardInfoPB>();
                mutable_bitField0_ |= 0x00000001;
              }
              cardList_.add(
                  input.readMessage(com.yorha.proto.StructPB.PlayerCardInfoPB.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          cardList_ = java.util.Collections.unmodifiableList(cardList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C.class, com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C.Builder.class);
    }

    public static final int CARDLIST_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructPB.PlayerCardInfoPB> cardList_;
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructPB.PlayerCardInfoPB> getCardListList() {
      return cardList_;
    }
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> 
        getCardListOrBuilderList() {
      return cardList_;
    }
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    @java.lang.Override
    public int getCardListCount() {
      return cardList_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardInfoPB getCardList(int index) {
      return cardList_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardListOrBuilder(
        int index) {
      return cardList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < cardList_.size(); i++) {
        output.writeMessage(1, cardList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < cardList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, cardList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C other = (com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C) obj;

      if (!getCardListList()
          .equals(other.getCardListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getCardListCount() > 0) {
        hash = (37 * hash) + CARDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getCardListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SearchPlayerByName_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SearchPlayerByName_S2C)
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C.class, com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (cardListBuilder_ == null) {
          cardList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          cardListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerFriend.internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C build() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C buildPartial() {
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C result = new com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C(this);
        int from_bitField0_ = bitField0_;
        if (cardListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            cardList_ = java.util.Collections.unmodifiableList(cardList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.cardList_ = cardList_;
        } else {
          result.cardList_ = cardListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C) {
          return mergeFrom((com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C other) {
        if (other == com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C.getDefaultInstance()) return this;
        if (cardListBuilder_ == null) {
          if (!other.cardList_.isEmpty()) {
            if (cardList_.isEmpty()) {
              cardList_ = other.cardList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureCardListIsMutable();
              cardList_.addAll(other.cardList_);
            }
            onChanged();
          }
        } else {
          if (!other.cardList_.isEmpty()) {
            if (cardListBuilder_.isEmpty()) {
              cardListBuilder_.dispose();
              cardListBuilder_ = null;
              cardList_ = other.cardList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              cardListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCardListFieldBuilder() : null;
            } else {
              cardListBuilder_.addAllMessages(other.cardList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructPB.PlayerCardInfoPB> cardList_ =
        java.util.Collections.emptyList();
      private void ensureCardListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          cardList_ = new java.util.ArrayList<com.yorha.proto.StructPB.PlayerCardInfoPB>(cardList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> cardListBuilder_;

      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.PlayerCardInfoPB> getCardListList() {
        if (cardListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(cardList_);
        } else {
          return cardListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public int getCardListCount() {
        if (cardListBuilder_ == null) {
          return cardList_.size();
        } else {
          return cardListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB getCardList(int index) {
        if (cardListBuilder_ == null) {
          return cardList_.get(index);
        } else {
          return cardListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder setCardList(
          int index, com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        if (cardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCardListIsMutable();
          cardList_.set(index, value);
          onChanged();
        } else {
          cardListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder setCardList(
          int index, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder builderForValue) {
        if (cardListBuilder_ == null) {
          ensureCardListIsMutable();
          cardList_.set(index, builderForValue.build());
          onChanged();
        } else {
          cardListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder addCardList(com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        if (cardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCardListIsMutable();
          cardList_.add(value);
          onChanged();
        } else {
          cardListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder addCardList(
          int index, com.yorha.proto.StructPB.PlayerCardInfoPB value) {
        if (cardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCardListIsMutable();
          cardList_.add(index, value);
          onChanged();
        } else {
          cardListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder addCardList(
          com.yorha.proto.StructPB.PlayerCardInfoPB.Builder builderForValue) {
        if (cardListBuilder_ == null) {
          ensureCardListIsMutable();
          cardList_.add(builderForValue.build());
          onChanged();
        } else {
          cardListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder addCardList(
          int index, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder builderForValue) {
        if (cardListBuilder_ == null) {
          ensureCardListIsMutable();
          cardList_.add(index, builderForValue.build());
          onChanged();
        } else {
          cardListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder addAllCardList(
          java.lang.Iterable<? extends com.yorha.proto.StructPB.PlayerCardInfoPB> values) {
        if (cardListBuilder_ == null) {
          ensureCardListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, cardList_);
          onChanged();
        } else {
          cardListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder clearCardList() {
        if (cardListBuilder_ == null) {
          cardList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          cardListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public Builder removeCardList(int index) {
        if (cardListBuilder_ == null) {
          ensureCardListIsMutable();
          cardList_.remove(index);
          onChanged();
        } else {
          cardListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB.Builder getCardListBuilder(
          int index) {
        return getCardListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder getCardListOrBuilder(
          int index) {
        if (cardListBuilder_ == null) {
          return cardList_.get(index);  } else {
          return cardListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> 
           getCardListOrBuilderList() {
        if (cardListBuilder_ != null) {
          return cardListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(cardList_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB.Builder addCardListBuilder() {
        return getCardListFieldBuilder().addBuilder(
            com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardInfoPB.Builder addCardListBuilder(
          int index) {
        return getCardListFieldBuilder().addBuilder(
            index, com.yorha.proto.StructPB.PlayerCardInfoPB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.PlayerCardInfoPB cardList = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructPB.PlayerCardInfoPB.Builder> 
           getCardListBuilderList() {
        return getCardListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder> 
          getCardListFieldBuilder() {
        if (cardListBuilder_ == null) {
          cardListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardInfoPB, com.yorha.proto.StructPB.PlayerCardInfoPB.Builder, com.yorha.proto.StructPB.PlayerCardInfoPBOrBuilder>(
                  cardList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          cardList_ = null;
        }
        return cardListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SearchPlayerByName_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SearchPlayerByName_S2C)
    private static final com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C();
    }

    public static com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SearchPlayerByName_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SearchPlayerByName_S2C>() {
      @java.lang.Override
      public Player_SearchPlayerByName_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SearchPlayerByName_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SearchPlayerByName_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SearchPlayerByName_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerFriend.Player_SearchPlayerByName_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_AddFriend_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_AddFriend_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_AddFriend_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_AddFriend_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_DelFriend_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_DelFriend_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_DelFriend_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_DelFriend_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_RemoveShield_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_RemoveShield_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_RemoveShield_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_RemoveShield_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SearchPlayer_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SearchPlayer_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SearchPlayer_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SearchPlayer_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*ss_proto/gen/player/cs/player_friend.p" +
      "roto\022\017com.yorha.proto\032\"cs_proto/gen/comm" +
      "on/structPB.proto\"8\n\024Player_AddFriend_C2" +
      "S\022\020\n\010playerId\030\001 \001(\003\022\016\n\006zoneId\030\002 \001(\005\"\026\n\024P" +
      "layer_AddFriend_S2C\"/\n\033Player_AgreeFrien" +
      "dApply_C2S\022\020\n\010playerId\030\001 \001(\003\"\035\n\033Player_A" +
      "greeFriendApply_S2C\"0\n\034Player_RefuseFrie" +
      "ndApply_C2S\022\020\n\010playerId\030\001 \001(\003\"\036\n\034Player_" +
      "RefuseFriendApply_S2C\"(\n\024Player_DelFrien" +
      "d_C2S\022\020\n\010playerId\030\001 \001(\003\"\026\n\024Player_DelFri" +
      "end_S2C\";\n\027Player_ShieldPlayer_C2S\022\020\n\010pl" +
      "ayerId\030\001 \001(\003\022\016\n\006zoneId\030\002 \001(\005\"\031\n\027Player_S" +
      "hieldPlayer_S2C\"+\n\027Player_RemoveShield_C" +
      "2S\022\020\n\010playerId\030\001 \001(\003\"\031\n\027Player_RemoveShi" +
      "eld_S2C\"+\n\027Player_SearchPlayer_C2S\022\020\n\010pl" +
      "ayerId\030\001 \001(\003\"\177\n\027Player_SearchPlayer_S2C\022" +
      "\r\n\005exist\030\001 \001(\010\022\020\n\010playerId\030\002 \001(\003\022\016\n\006zone" +
      "Id\030\003 \001(\005\0223\n\010cardHead\030\004 \001(\0132!.com.yorha.p" +
      "roto.PlayerCardHeadPB\"-\n\035Player_SearchPl" +
      "ayerByName_C2S\022\014\n\004name\030\001 \001(\t\"T\n\035Player_S" +
      "earchPlayerByName_S2C\0223\n\010cardList\030\001 \003(\0132" +
      "!.com.yorha.proto.PlayerCardInfoPBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_AddFriend_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_AddFriend_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_AddFriend_C2S_descriptor,
        new java.lang.String[] { "PlayerId", "ZoneId", });
    internal_static_com_yorha_proto_Player_AddFriend_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_AddFriend_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_AddFriend_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_AgreeFriendApply_C2S_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_AgreeFriendApply_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_RefuseFriendApply_C2S_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_RefuseFriendApply_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_DelFriend_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_DelFriend_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_DelFriend_C2S_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_Player_DelFriend_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_DelFriend_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_DelFriend_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ShieldPlayer_C2S_descriptor,
        new java.lang.String[] { "PlayerId", "ZoneId", });
    internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ShieldPlayer_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_RemoveShield_C2S_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_Player_RemoveShield_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_RemoveShield_C2S_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_Player_RemoveShield_S2C_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_Player_RemoveShield_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_RemoveShield_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SearchPlayer_C2S_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_Player_SearchPlayer_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SearchPlayer_C2S_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_Player_SearchPlayer_S2C_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_Player_SearchPlayer_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SearchPlayer_S2C_descriptor,
        new java.lang.String[] { "Exist", "PlayerId", "ZoneId", "CardHead", });
    internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SearchPlayerByName_C2S_descriptor,
        new java.lang.String[] { "Name", });
    internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SearchPlayerByName_S2C_descriptor,
        new java.lang.String[] { "CardList", });
    com.yorha.proto.StructPB.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

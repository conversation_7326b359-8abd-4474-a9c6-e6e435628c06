// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_task.proto

package com.yorha.proto;

public final class PlayerTask {
  private PlayerTask() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_TaskAward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TaskAward_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
     * @return Whether the class field is set.
     */
    boolean hasClass_();
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
     * @return The class.
     */
    com.yorha.proto.CommonEnum.TaskClass getClass_();

    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    boolean hasTaskId();
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 taskId = 2;</code>
     * @return The taskId.
     */
    int getTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TaskAward_C2S}
   */
  public static final class Player_TaskAward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TaskAward_C2S)
      Player_TaskAward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TaskAward_C2S.newBuilder() to construct.
    private Player_TaskAward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TaskAward_C2S() {
      class__ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TaskAward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TaskAward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.TaskClass value = com.yorha.proto.CommonEnum.TaskClass.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                class__ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              taskId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerTask.Player_TaskAward_C2S.class, com.yorha.proto.PlayerTask.Player_TaskAward_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CLASS_FIELD_NUMBER = 1;
    private int class__;
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
     * @return Whether the class field is set.
     */
    @java.lang.Override public boolean hasClass_() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 任务类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
     * @return The class.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.TaskClass getClass_() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.TaskClass result = com.yorha.proto.CommonEnum.TaskClass.valueOf(class__);
      return result == null ? com.yorha.proto.CommonEnum.TaskClass.TC_NONE : result;
    }

    public static final int TASKID_FIELD_NUMBER = 2;
    private int taskId_;
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 taskId = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public int getTaskId() {
      return taskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, class__);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, taskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, class__);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, taskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerTask.Player_TaskAward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerTask.Player_TaskAward_C2S other = (com.yorha.proto.PlayerTask.Player_TaskAward_C2S) obj;

      if (hasClass_() != other.hasClass_()) return false;
      if (hasClass_()) {
        if (class__ != other.class__) return false;
      }
      if (hasTaskId() != other.hasTaskId()) return false;
      if (hasTaskId()) {
        if (getTaskId()
            != other.getTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClass_()) {
        hash = (37 * hash) + CLASS_FIELD_NUMBER;
        hash = (53 * hash) + class__;
      }
      if (hasTaskId()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + getTaskId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerTask.Player_TaskAward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TaskAward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TaskAward_C2S)
        com.yorha.proto.PlayerTask.Player_TaskAward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerTask.Player_TaskAward_C2S.class, com.yorha.proto.PlayerTask.Player_TaskAward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerTask.Player_TaskAward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        class__ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        taskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.Player_TaskAward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerTask.Player_TaskAward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.Player_TaskAward_C2S build() {
        com.yorha.proto.PlayerTask.Player_TaskAward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.Player_TaskAward_C2S buildPartial() {
        com.yorha.proto.PlayerTask.Player_TaskAward_C2S result = new com.yorha.proto.PlayerTask.Player_TaskAward_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.class__ = class__;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.taskId_ = taskId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerTask.Player_TaskAward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerTask.Player_TaskAward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerTask.Player_TaskAward_C2S other) {
        if (other == com.yorha.proto.PlayerTask.Player_TaskAward_C2S.getDefaultInstance()) return this;
        if (other.hasClass_()) {
          setClass_(other.getClass_());
        }
        if (other.hasTaskId()) {
          setTaskId(other.getTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerTask.Player_TaskAward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerTask.Player_TaskAward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int class__ = 0;
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
       * @return Whether the class field is set.
       */
      @java.lang.Override public boolean hasClass_() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
       * @return The class.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.TaskClass getClass_() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.TaskClass result = com.yorha.proto.CommonEnum.TaskClass.valueOf(class__);
        return result == null ? com.yorha.proto.CommonEnum.TaskClass.TC_NONE : result;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
       * @param value The class to set.
       * @return This builder for chaining.
       */
      public Builder setClass_(com.yorha.proto.CommonEnum.TaskClass value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        class__ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.TaskClass class = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClass_() {
        bitField0_ = (bitField0_ & ~0x00000001);
        class__ = 0;
        onChanged();
        return this;
      }

      private int taskId_ ;
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 taskId = 2;</code>
       * @return Whether the taskId field is set.
       */
      @java.lang.Override
      public boolean hasTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 taskId = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public int getTaskId() {
        return taskId_;
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 taskId = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(int value) {
        bitField0_ |= 0x00000002;
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 taskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        taskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TaskAward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TaskAward_C2S)
    private static final com.yorha.proto.PlayerTask.Player_TaskAward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerTask.Player_TaskAward_C2S();
    }

    public static com.yorha.proto.PlayerTask.Player_TaskAward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TaskAward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_TaskAward_C2S>() {
      @java.lang.Override
      public Player_TaskAward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TaskAward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TaskAward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TaskAward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerTask.Player_TaskAward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TaskAward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TaskAward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    java.util.List<com.yorha.proto.PlayerTask.RewardPair> 
        getRewardList();
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    com.yorha.proto.PlayerTask.RewardPair getReward(int index);
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    int getRewardCount();
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.PlayerTask.RewardPairOrBuilder> 
        getRewardOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    com.yorha.proto.PlayerTask.RewardPairOrBuilder getRewardOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TaskAward_S2C}
   */
  public static final class Player_TaskAward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TaskAward_S2C)
      Player_TaskAward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TaskAward_S2C.newBuilder() to construct.
    private Player_TaskAward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TaskAward_S2C() {
      reward_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TaskAward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TaskAward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                reward_ = new java.util.ArrayList<com.yorha.proto.PlayerTask.RewardPair>();
                mutable_bitField0_ |= 0x00000001;
              }
              reward_.add(
                  input.readMessage(com.yorha.proto.PlayerTask.RewardPair.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          reward_ = java.util.Collections.unmodifiableList(reward_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerTask.Player_TaskAward_S2C.class, com.yorha.proto.PlayerTask.Player_TaskAward_S2C.Builder.class);
    }

    public static final int REWARD_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.PlayerTask.RewardPair> reward_;
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.PlayerTask.RewardPair> getRewardList() {
      return reward_;
    }
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.PlayerTask.RewardPairOrBuilder> 
        getRewardOrBuilderList() {
      return reward_;
    }
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    @java.lang.Override
    public int getRewardCount() {
      return reward_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerTask.RewardPair getReward(int index) {
      return reward_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerTask.RewardPairOrBuilder getRewardOrBuilder(
        int index) {
      return reward_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < reward_.size(); i++) {
        output.writeMessage(1, reward_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < reward_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, reward_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerTask.Player_TaskAward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerTask.Player_TaskAward_S2C other = (com.yorha.proto.PlayerTask.Player_TaskAward_S2C) obj;

      if (!getRewardList()
          .equals(other.getRewardList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRewardCount() > 0) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getRewardList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerTask.Player_TaskAward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TaskAward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TaskAward_S2C)
        com.yorha.proto.PlayerTask.Player_TaskAward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerTask.Player_TaskAward_S2C.class, com.yorha.proto.PlayerTask.Player_TaskAward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerTask.Player_TaskAward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardBuilder_ == null) {
          reward_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rewardBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_Player_TaskAward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.Player_TaskAward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerTask.Player_TaskAward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.Player_TaskAward_S2C build() {
        com.yorha.proto.PlayerTask.Player_TaskAward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.Player_TaskAward_S2C buildPartial() {
        com.yorha.proto.PlayerTask.Player_TaskAward_S2C result = new com.yorha.proto.PlayerTask.Player_TaskAward_S2C(this);
        int from_bitField0_ = bitField0_;
        if (rewardBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            reward_ = java.util.Collections.unmodifiableList(reward_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.reward_ = reward_;
        } else {
          result.reward_ = rewardBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerTask.Player_TaskAward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerTask.Player_TaskAward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerTask.Player_TaskAward_S2C other) {
        if (other == com.yorha.proto.PlayerTask.Player_TaskAward_S2C.getDefaultInstance()) return this;
        if (rewardBuilder_ == null) {
          if (!other.reward_.isEmpty()) {
            if (reward_.isEmpty()) {
              reward_ = other.reward_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardIsMutable();
              reward_.addAll(other.reward_);
            }
            onChanged();
          }
        } else {
          if (!other.reward_.isEmpty()) {
            if (rewardBuilder_.isEmpty()) {
              rewardBuilder_.dispose();
              rewardBuilder_ = null;
              reward_ = other.reward_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardFieldBuilder() : null;
            } else {
              rewardBuilder_.addAllMessages(other.reward_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerTask.Player_TaskAward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerTask.Player_TaskAward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.PlayerTask.RewardPair> reward_ =
        java.util.Collections.emptyList();
      private void ensureRewardIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          reward_ = new java.util.ArrayList<com.yorha.proto.PlayerTask.RewardPair>(reward_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerTask.RewardPair, com.yorha.proto.PlayerTask.RewardPair.Builder, com.yorha.proto.PlayerTask.RewardPairOrBuilder> rewardBuilder_;

      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerTask.RewardPair> getRewardList() {
        if (rewardBuilder_ == null) {
          return java.util.Collections.unmodifiableList(reward_);
        } else {
          return rewardBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public int getRewardCount() {
        if (rewardBuilder_ == null) {
          return reward_.size();
        } else {
          return rewardBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public com.yorha.proto.PlayerTask.RewardPair getReward(int index) {
        if (rewardBuilder_ == null) {
          return reward_.get(index);
        } else {
          return rewardBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder setReward(
          int index, com.yorha.proto.PlayerTask.RewardPair value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardIsMutable();
          reward_.set(index, value);
          onChanged();
        } else {
          rewardBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder setReward(
          int index, com.yorha.proto.PlayerTask.RewardPair.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder addReward(com.yorha.proto.PlayerTask.RewardPair value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardIsMutable();
          reward_.add(value);
          onChanged();
        } else {
          rewardBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder addReward(
          int index, com.yorha.proto.PlayerTask.RewardPair value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardIsMutable();
          reward_.add(index, value);
          onChanged();
        } else {
          rewardBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder addReward(
          com.yorha.proto.PlayerTask.RewardPair.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.add(builderForValue.build());
          onChanged();
        } else {
          rewardBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder addReward(
          int index, com.yorha.proto.PlayerTask.RewardPair.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder addAllReward(
          java.lang.Iterable<? extends com.yorha.proto.PlayerTask.RewardPair> values) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, reward_);
          onChanged();
        } else {
          rewardBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public Builder removeReward(int index) {
        if (rewardBuilder_ == null) {
          ensureRewardIsMutable();
          reward_.remove(index);
          onChanged();
        } else {
          rewardBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public com.yorha.proto.PlayerTask.RewardPair.Builder getRewardBuilder(
          int index) {
        return getRewardFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public com.yorha.proto.PlayerTask.RewardPairOrBuilder getRewardOrBuilder(
          int index) {
        if (rewardBuilder_ == null) {
          return reward_.get(index);  } else {
          return rewardBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.PlayerTask.RewardPairOrBuilder> 
           getRewardOrBuilderList() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(reward_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public com.yorha.proto.PlayerTask.RewardPair.Builder addRewardBuilder() {
        return getRewardFieldBuilder().addBuilder(
            com.yorha.proto.PlayerTask.RewardPair.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public com.yorha.proto.PlayerTask.RewardPair.Builder addRewardBuilder(
          int index) {
        return getRewardFieldBuilder().addBuilder(
            index, com.yorha.proto.PlayerTask.RewardPair.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RewardPair reward = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerTask.RewardPair.Builder> 
           getRewardBuilderList() {
        return getRewardFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerTask.RewardPair, com.yorha.proto.PlayerTask.RewardPair.Builder, com.yorha.proto.PlayerTask.RewardPairOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.PlayerTask.RewardPair, com.yorha.proto.PlayerTask.RewardPair.Builder, com.yorha.proto.PlayerTask.RewardPairOrBuilder>(
                  reward_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TaskAward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TaskAward_S2C)
    private static final com.yorha.proto.PlayerTask.Player_TaskAward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerTask.Player_TaskAward_S2C();
    }

    public static com.yorha.proto.PlayerTask.Player_TaskAward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TaskAward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_TaskAward_S2C>() {
      @java.lang.Override
      public Player_TaskAward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TaskAward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TaskAward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TaskAward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerTask.Player_TaskAward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RewardPairOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RewardPair)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    boolean hasItemId();
    /**
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RewardPair}
   */
  public static final class RewardPair extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RewardPair)
      RewardPairOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RewardPair.newBuilder() to construct.
    private RewardPair(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RewardPair() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RewardPair();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RewardPair(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              itemId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_RewardPair_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_RewardPair_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerTask.RewardPair.class, com.yorha.proto.PlayerTask.RewardPair.Builder.class);
    }

    private int bitField0_;
    public static final int ITEMID_FIELD_NUMBER = 1;
    private int itemId_;
    /**
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    @java.lang.Override
    public boolean hasItemId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerTask.RewardPair)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerTask.RewardPair other = (com.yorha.proto.PlayerTask.RewardPair) obj;

      if (hasItemId() != other.hasItemId()) return false;
      if (hasItemId()) {
        if (getItemId()
            != other.getItemId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasItemId()) {
        hash = (37 * hash) + ITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getItemId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerTask.RewardPair parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerTask.RewardPair prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RewardPair}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RewardPair)
        com.yorha.proto.PlayerTask.RewardPairOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_RewardPair_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_RewardPair_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerTask.RewardPair.class, com.yorha.proto.PlayerTask.RewardPair.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerTask.RewardPair.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        itemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerTask.internal_static_com_yorha_proto_RewardPair_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.RewardPair getDefaultInstanceForType() {
        return com.yorha.proto.PlayerTask.RewardPair.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.RewardPair build() {
        com.yorha.proto.PlayerTask.RewardPair result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerTask.RewardPair buildPartial() {
        com.yorha.proto.PlayerTask.RewardPair result = new com.yorha.proto.PlayerTask.RewardPair(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.itemId_ = itemId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerTask.RewardPair) {
          return mergeFrom((com.yorha.proto.PlayerTask.RewardPair)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerTask.RewardPair other) {
        if (other == com.yorha.proto.PlayerTask.RewardPair.getDefaultInstance()) return this;
        if (other.hasItemId()) {
          setItemId(other.getItemId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerTask.RewardPair parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerTask.RewardPair) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int itemId_ ;
      /**
       * <code>optional int32 itemId = 1;</code>
       * @return Whether the itemId field is set.
       */
      @java.lang.Override
      public boolean hasItemId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 itemId = 1;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <code>optional int32 itemId = 1;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        bitField0_ |= 0x00000001;
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 itemId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RewardPair)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RewardPair)
    private static final com.yorha.proto.PlayerTask.RewardPair DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerTask.RewardPair();
    }

    public static com.yorha.proto.PlayerTask.RewardPair getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RewardPair>
        PARSER = new com.google.protobuf.AbstractParser<RewardPair>() {
      @java.lang.Override
      public RewardPair parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RewardPair(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RewardPair> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RewardPair> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerTask.RewardPair getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TaskAward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TaskAward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TaskAward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TaskAward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RewardPair_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RewardPair_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n(ss_proto/gen/player/cs/player_task.pro" +
      "to\022\017com.yorha.proto\032%ss_proto/gen/common" +
      "/common_enum.proto\"Q\n\024Player_TaskAward_C" +
      "2S\022)\n\005class\030\001 \001(\0162\032.com.yorha.proto.Task" +
      "Class\022\016\n\006taskId\030\002 \001(\005\"C\n\024Player_TaskAwar" +
      "d_S2C\022+\n\006reward\030\001 \003(\0132\033.com.yorha.proto." +
      "RewardPair\")\n\nRewardPair\022\016\n\006itemId\030\001 \001(\005" +
      "\022\013\n\003num\030\002 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_TaskAward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_TaskAward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TaskAward_C2S_descriptor,
        new java.lang.String[] { "Class_", "TaskId", });
    internal_static_com_yorha_proto_Player_TaskAward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_TaskAward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TaskAward_S2C_descriptor,
        new java.lang.String[] { "Reward", });
    internal_static_com_yorha_proto_RewardPair_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_RewardPair_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RewardPair_descriptor,
        new java.lang.String[] { "ItemId", "Num", });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

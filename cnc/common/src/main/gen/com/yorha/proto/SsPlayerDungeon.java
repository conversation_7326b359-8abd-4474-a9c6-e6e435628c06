// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player_dungeon.proto

package com.yorha.proto;

public final class SsPlayerDungeon {
  private SsPlayerDungeon() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface OnDungeonDestroyCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnDungeonDestroyCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 sceneId = 1;</code>
     * @return Whether the sceneId field is set.
     */
    boolean hasSceneId();
    /**
     * <code>optional int64 sceneId = 1;</code>
     * @return The sceneId.
     */
    long getSceneId();

    /**
     * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.DungeonType getType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnDungeonDestroyCmd}
   */
  public static final class OnDungeonDestroyCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnDungeonDestroyCmd)
      OnDungeonDestroyCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnDungeonDestroyCmd.newBuilder() to construct.
    private OnDungeonDestroyCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnDungeonDestroyCmd() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnDungeonDestroyCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnDungeonDestroyCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              sceneId_ = input.readInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.DungeonType value = com.yorha.proto.CommonEnum.DungeonType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                type_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerDungeon.internal_static_com_yorha_proto_OnDungeonDestroyCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerDungeon.internal_static_com_yorha_proto_OnDungeonDestroyCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd.class, com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd.Builder.class);
    }

    private int bitField0_;
    public static final int SCENEID_FIELD_NUMBER = 1;
    private long sceneId_;
    /**
     * <code>optional int64 sceneId = 1;</code>
     * @return Whether the sceneId field is set.
     */
    @java.lang.Override
    public boolean hasSceneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 sceneId = 1;</code>
     * @return The sceneId.
     */
    @java.lang.Override
    public long getSceneId() {
      return sceneId_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.DungeonType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, sceneId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, sceneId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd other = (com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd) obj;

      if (hasSceneId() != other.hasSceneId()) return false;
      if (hasSceneId()) {
        if (getSceneId()
            != other.getSceneId()) return false;
      }
      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSceneId()) {
        hash = (37 * hash) + SCENEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSceneId());
      }
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnDungeonDestroyCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnDungeonDestroyCmd)
        com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerDungeon.internal_static_com_yorha_proto_OnDungeonDestroyCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerDungeon.internal_static_com_yorha_proto_OnDungeonDestroyCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd.class, com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        sceneId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerDungeon.internal_static_com_yorha_proto_OnDungeonDestroyCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd build() {
        com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd buildPartial() {
        com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd result = new com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.sceneId_ = sceneId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd other) {
        if (other == com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd.getDefaultInstance()) return this;
        if (other.hasSceneId()) {
          setSceneId(other.getSceneId());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long sceneId_ ;
      /**
       * <code>optional int64 sceneId = 1;</code>
       * @return Whether the sceneId field is set.
       */
      @java.lang.Override
      public boolean hasSceneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 sceneId = 1;</code>
       * @return The sceneId.
       */
      @java.lang.Override
      public long getSceneId() {
        return sceneId_;
      }
      /**
       * <code>optional int64 sceneId = 1;</code>
       * @param value The sceneId to set.
       * @return This builder for chaining.
       */
      public Builder setSceneId(long value) {
        bitField0_ |= 0x00000001;
        sceneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 sceneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSceneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sceneId_ = 0L;
        onChanged();
        return this;
      }

      private int type_ = 0;
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.DungeonType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.DungeonType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnDungeonDestroyCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnDungeonDestroyCmd)
    private static final com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd();
    }

    public static com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnDungeonDestroyCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnDungeonDestroyCmd>() {
      @java.lang.Override
      public OnDungeonDestroyCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnDungeonDestroyCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnDungeonDestroyCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnDungeonDestroyCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerDungeon.OnDungeonDestroyCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnDungeonDestroyCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnDungeonDestroyCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/player/ss/ss_player_dunge" +
      "on.proto\022\017com.yorha.proto\032%ss_proto/gen/" +
      "common/common_enum.proto\"R\n\023OnDungeonDes" +
      "troyCmd\022\017\n\007sceneId\030\001 \001(\003\022*\n\004type\030\002 \001(\0162\034" +
      ".com.yorha.proto.DungeonTypeB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_OnDungeonDestroyCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_OnDungeonDestroyCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnDungeonDestroyCmd_descriptor,
        new java.lang.String[] { "SceneId", "Type", });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

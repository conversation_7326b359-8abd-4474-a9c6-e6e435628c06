// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/aoiView/ss_aoi_view.proto

package com.yorha.proto;

public final class SsAoiView {
  private SsAoiView() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AABBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AABB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 left = 1;</code>
     * @return Whether the left field is set.
     */
    boolean hasLeft();
    /**
     * <code>optional int32 left = 1;</code>
     * @return The left.
     */
    int getLeft();

    /**
     * <code>optional int32 top = 2;</code>
     * @return Whether the top field is set.
     */
    boolean hasTop();
    /**
     * <code>optional int32 top = 2;</code>
     * @return The top.
     */
    int getTop();

    /**
     * <code>optional int32 right = 3;</code>
     * @return Whether the right field is set.
     */
    boolean hasRight();
    /**
     * <code>optional int32 right = 3;</code>
     * @return The right.
     */
    int getRight();

    /**
     * <code>optional int32 bottom = 4;</code>
     * @return Whether the bottom field is set.
     */
    boolean hasBottom();
    /**
     * <code>optional int32 bottom = 4;</code>
     * @return The bottom.
     */
    int getBottom();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AABB}
   */
  public static final class AABB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AABB)
      AABBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AABB.newBuilder() to construct.
    private AABB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AABB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AABB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AABB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              left_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              top_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              right_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              bottom_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AABB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AABB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAoiView.AABB.class, com.yorha.proto.SsAoiView.AABB.Builder.class);
    }

    private int bitField0_;
    public static final int LEFT_FIELD_NUMBER = 1;
    private int left_;
    /**
     * <code>optional int32 left = 1;</code>
     * @return Whether the left field is set.
     */
    @java.lang.Override
    public boolean hasLeft() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 left = 1;</code>
     * @return The left.
     */
    @java.lang.Override
    public int getLeft() {
      return left_;
    }

    public static final int TOP_FIELD_NUMBER = 2;
    private int top_;
    /**
     * <code>optional int32 top = 2;</code>
     * @return Whether the top field is set.
     */
    @java.lang.Override
    public boolean hasTop() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 top = 2;</code>
     * @return The top.
     */
    @java.lang.Override
    public int getTop() {
      return top_;
    }

    public static final int RIGHT_FIELD_NUMBER = 3;
    private int right_;
    /**
     * <code>optional int32 right = 3;</code>
     * @return Whether the right field is set.
     */
    @java.lang.Override
    public boolean hasRight() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 right = 3;</code>
     * @return The right.
     */
    @java.lang.Override
    public int getRight() {
      return right_;
    }

    public static final int BOTTOM_FIELD_NUMBER = 4;
    private int bottom_;
    /**
     * <code>optional int32 bottom = 4;</code>
     * @return Whether the bottom field is set.
     */
    @java.lang.Override
    public boolean hasBottom() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 bottom = 4;</code>
     * @return The bottom.
     */
    @java.lang.Override
    public int getBottom() {
      return bottom_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, left_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, top_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, right_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, bottom_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, left_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, top_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, right_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, bottom_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAoiView.AABB)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAoiView.AABB other = (com.yorha.proto.SsAoiView.AABB) obj;

      if (hasLeft() != other.hasLeft()) return false;
      if (hasLeft()) {
        if (getLeft()
            != other.getLeft()) return false;
      }
      if (hasTop() != other.hasTop()) return false;
      if (hasTop()) {
        if (getTop()
            != other.getTop()) return false;
      }
      if (hasRight() != other.hasRight()) return false;
      if (hasRight()) {
        if (getRight()
            != other.getRight()) return false;
      }
      if (hasBottom() != other.hasBottom()) return false;
      if (hasBottom()) {
        if (getBottom()
            != other.getBottom()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLeft()) {
        hash = (37 * hash) + LEFT_FIELD_NUMBER;
        hash = (53 * hash) + getLeft();
      }
      if (hasTop()) {
        hash = (37 * hash) + TOP_FIELD_NUMBER;
        hash = (53 * hash) + getTop();
      }
      if (hasRight()) {
        hash = (37 * hash) + RIGHT_FIELD_NUMBER;
        hash = (53 * hash) + getRight();
      }
      if (hasBottom()) {
        hash = (37 * hash) + BOTTOM_FIELD_NUMBER;
        hash = (53 * hash) + getBottom();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AABB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.AABB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.AABB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAoiView.AABB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AABB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AABB)
        com.yorha.proto.SsAoiView.AABBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AABB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AABB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAoiView.AABB.class, com.yorha.proto.SsAoiView.AABB.Builder.class);
      }

      // Construct using com.yorha.proto.SsAoiView.AABB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        left_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        top_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        right_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        bottom_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AABB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.AABB getDefaultInstanceForType() {
        return com.yorha.proto.SsAoiView.AABB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.AABB build() {
        com.yorha.proto.SsAoiView.AABB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.AABB buildPartial() {
        com.yorha.proto.SsAoiView.AABB result = new com.yorha.proto.SsAoiView.AABB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.left_ = left_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.top_ = top_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.right_ = right_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.bottom_ = bottom_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAoiView.AABB) {
          return mergeFrom((com.yorha.proto.SsAoiView.AABB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAoiView.AABB other) {
        if (other == com.yorha.proto.SsAoiView.AABB.getDefaultInstance()) return this;
        if (other.hasLeft()) {
          setLeft(other.getLeft());
        }
        if (other.hasTop()) {
          setTop(other.getTop());
        }
        if (other.hasRight()) {
          setRight(other.getRight());
        }
        if (other.hasBottom()) {
          setBottom(other.getBottom());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAoiView.AABB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAoiView.AABB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int left_ ;
      /**
       * <code>optional int32 left = 1;</code>
       * @return Whether the left field is set.
       */
      @java.lang.Override
      public boolean hasLeft() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 left = 1;</code>
       * @return The left.
       */
      @java.lang.Override
      public int getLeft() {
        return left_;
      }
      /**
       * <code>optional int32 left = 1;</code>
       * @param value The left to set.
       * @return This builder for chaining.
       */
      public Builder setLeft(int value) {
        bitField0_ |= 0x00000001;
        left_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 left = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLeft() {
        bitField0_ = (bitField0_ & ~0x00000001);
        left_ = 0;
        onChanged();
        return this;
      }

      private int top_ ;
      /**
       * <code>optional int32 top = 2;</code>
       * @return Whether the top field is set.
       */
      @java.lang.Override
      public boolean hasTop() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 top = 2;</code>
       * @return The top.
       */
      @java.lang.Override
      public int getTop() {
        return top_;
      }
      /**
       * <code>optional int32 top = 2;</code>
       * @param value The top to set.
       * @return This builder for chaining.
       */
      public Builder setTop(int value) {
        bitField0_ |= 0x00000002;
        top_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 top = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTop() {
        bitField0_ = (bitField0_ & ~0x00000002);
        top_ = 0;
        onChanged();
        return this;
      }

      private int right_ ;
      /**
       * <code>optional int32 right = 3;</code>
       * @return Whether the right field is set.
       */
      @java.lang.Override
      public boolean hasRight() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 right = 3;</code>
       * @return The right.
       */
      @java.lang.Override
      public int getRight() {
        return right_;
      }
      /**
       * <code>optional int32 right = 3;</code>
       * @param value The right to set.
       * @return This builder for chaining.
       */
      public Builder setRight(int value) {
        bitField0_ |= 0x00000004;
        right_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 right = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRight() {
        bitField0_ = (bitField0_ & ~0x00000004);
        right_ = 0;
        onChanged();
        return this;
      }

      private int bottom_ ;
      /**
       * <code>optional int32 bottom = 4;</code>
       * @return Whether the bottom field is set.
       */
      @java.lang.Override
      public boolean hasBottom() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 bottom = 4;</code>
       * @return The bottom.
       */
      @java.lang.Override
      public int getBottom() {
        return bottom_;
      }
      /**
       * <code>optional int32 bottom = 4;</code>
       * @param value The bottom to set.
       * @return This builder for chaining.
       */
      public Builder setBottom(int value) {
        bitField0_ |= 0x00000008;
        bottom_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 bottom = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBottom() {
        bitField0_ = (bitField0_ & ~0x00000008);
        bottom_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AABB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AABB)
    private static final com.yorha.proto.SsAoiView.AABB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAoiView.AABB();
    }

    public static com.yorha.proto.SsAoiView.AABB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AABB>
        PARSER = new com.google.protobuf.AbstractParser<AABB>() {
      @java.lang.Override
      public AABB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AABB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AABB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AABB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAoiView.AABB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpdateViewAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.UpdateViewAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
     * @return Whether the p1 field is set.
     */
    boolean hasP1();
    /**
     * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
     * @return The p1.
     */
    com.yorha.proto.StructPB.PointPB getP1();
    /**
     * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getP1OrBuilder();

    /**
     * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
     * @return Whether the p2 field is set.
     */
    boolean hasP2();
    /**
     * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
     * @return The p2.
     */
    com.yorha.proto.StructPB.PointPB getP2();
    /**
     * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getP2OrBuilder();

    /**
     * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
     * @return Whether the p3 field is set.
     */
    boolean hasP3();
    /**
     * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
     * @return The p3.
     */
    com.yorha.proto.StructPB.PointPB getP3();
    /**
     * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getP3OrBuilder();

    /**
     * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
     * @return Whether the p4 field is set.
     */
    boolean hasP4();
    /**
     * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
     * @return The p4.
     */
    com.yorha.proto.StructPB.PointPB getP4();
    /**
     * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getP4OrBuilder();

    /**
     * <code>optional int32 layerId = 6;</code>
     * @return Whether the layerId field is set.
     */
    boolean hasLayerId();
    /**
     * <code>optional int32 layerId = 6;</code>
     * @return The layerId.
     */
    int getLayerId();

    /**
     * <pre>
     * 场景zoneId, 在kvk里就是kvkId
     * </pre>
     *
     * <code>optional int32 sceneZoneId = 7;</code>
     * @return Whether the sceneZoneId field is set.
     */
    boolean hasSceneZoneId();
    /**
     * <pre>
     * 场景zoneId, 在kvk里就是kvkId
     * </pre>
     *
     * <code>optional int32 sceneZoneId = 7;</code>
     * @return The sceneZoneId.
     */
    int getSceneZoneId();

    /**
     * <pre>
     * 旁观者的gate
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
     * @return Whether the sessionRef field is set.
     */
    boolean hasSessionRef();
    /**
     * <pre>
     * 旁观者的gate
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
     * @return The sessionRef.
     */
    com.yorha.proto.CommonMsg.ActorRefData getSessionRef();
    /**
     * <pre>
     * 旁观者的gate
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
     */
    com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.UpdateViewAsk}
   */
  public static final class UpdateViewAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.UpdateViewAsk)
      UpdateViewAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpdateViewAsk.newBuilder() to construct.
    private UpdateViewAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpdateViewAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpdateViewAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UpdateViewAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = p1_.toBuilder();
              }
              p1_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(p1_);
                p1_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = p2_.toBuilder();
              }
              p2_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(p2_);
                p2_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 34: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = p3_.toBuilder();
              }
              p3_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(p3_);
                p3_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = p4_.toBuilder();
              }
              p4_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(p4_);
                p4_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              layerId_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              sceneZoneId_ = input.readInt32();
              break;
            }
            case 66: {
              com.yorha.proto.CommonMsg.ActorRefData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000080) != 0)) {
                subBuilder = sessionRef_.toBuilder();
              }
              sessionRef_ = input.readMessage(com.yorha.proto.CommonMsg.ActorRefData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sessionRef_);
                sessionRef_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000080;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAoiView.UpdateViewAsk.class, com.yorha.proto.SsAoiView.UpdateViewAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int P1_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPB.PointPB p1_;
    /**
     * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
     * @return Whether the p1 field is set.
     */
    @java.lang.Override
    public boolean hasP1() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
     * @return The p1.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getP1() {
      return p1_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p1_;
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getP1OrBuilder() {
      return p1_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p1_;
    }

    public static final int P2_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.PointPB p2_;
    /**
     * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
     * @return Whether the p2 field is set.
     */
    @java.lang.Override
    public boolean hasP2() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
     * @return The p2.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getP2() {
      return p2_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p2_;
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getP2OrBuilder() {
      return p2_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p2_;
    }

    public static final int P3_FIELD_NUMBER = 4;
    private com.yorha.proto.StructPB.PointPB p3_;
    /**
     * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
     * @return Whether the p3 field is set.
     */
    @java.lang.Override
    public boolean hasP3() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
     * @return The p3.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getP3() {
      return p3_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p3_;
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getP3OrBuilder() {
      return p3_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p3_;
    }

    public static final int P4_FIELD_NUMBER = 5;
    private com.yorha.proto.StructPB.PointPB p4_;
    /**
     * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
     * @return Whether the p4 field is set.
     */
    @java.lang.Override
    public boolean hasP4() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
     * @return The p4.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getP4() {
      return p4_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p4_;
    }
    /**
     * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getP4OrBuilder() {
      return p4_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p4_;
    }

    public static final int LAYERID_FIELD_NUMBER = 6;
    private int layerId_;
    /**
     * <code>optional int32 layerId = 6;</code>
     * @return Whether the layerId field is set.
     */
    @java.lang.Override
    public boolean hasLayerId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional int32 layerId = 6;</code>
     * @return The layerId.
     */
    @java.lang.Override
    public int getLayerId() {
      return layerId_;
    }

    public static final int SCENEZONEID_FIELD_NUMBER = 7;
    private int sceneZoneId_;
    /**
     * <pre>
     * 场景zoneId, 在kvk里就是kvkId
     * </pre>
     *
     * <code>optional int32 sceneZoneId = 7;</code>
     * @return Whether the sceneZoneId field is set.
     */
    @java.lang.Override
    public boolean hasSceneZoneId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 场景zoneId, 在kvk里就是kvkId
     * </pre>
     *
     * <code>optional int32 sceneZoneId = 7;</code>
     * @return The sceneZoneId.
     */
    @java.lang.Override
    public int getSceneZoneId() {
      return sceneZoneId_;
    }

    public static final int SESSIONREF_FIELD_NUMBER = 8;
    private com.yorha.proto.CommonMsg.ActorRefData sessionRef_;
    /**
     * <pre>
     * 旁观者的gate
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
     * @return Whether the sessionRef field is set.
     */
    @java.lang.Override
    public boolean hasSessionRef() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 旁观者的gate
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
     * @return The sessionRef.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefData getSessionRef() {
      return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
    }
    /**
     * <pre>
     * 旁观者的gate
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder() {
      return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getP1());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getP2());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getP3());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getP4());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, layerId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(7, sceneZoneId_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(8, getSessionRef());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getP1());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getP2());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getP3());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getP4());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, layerId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, sceneZoneId_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getSessionRef());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAoiView.UpdateViewAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAoiView.UpdateViewAsk other = (com.yorha.proto.SsAoiView.UpdateViewAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasP1() != other.hasP1()) return false;
      if (hasP1()) {
        if (!getP1()
            .equals(other.getP1())) return false;
      }
      if (hasP2() != other.hasP2()) return false;
      if (hasP2()) {
        if (!getP2()
            .equals(other.getP2())) return false;
      }
      if (hasP3() != other.hasP3()) return false;
      if (hasP3()) {
        if (!getP3()
            .equals(other.getP3())) return false;
      }
      if (hasP4() != other.hasP4()) return false;
      if (hasP4()) {
        if (!getP4()
            .equals(other.getP4())) return false;
      }
      if (hasLayerId() != other.hasLayerId()) return false;
      if (hasLayerId()) {
        if (getLayerId()
            != other.getLayerId()) return false;
      }
      if (hasSceneZoneId() != other.hasSceneZoneId()) return false;
      if (hasSceneZoneId()) {
        if (getSceneZoneId()
            != other.getSceneZoneId()) return false;
      }
      if (hasSessionRef() != other.hasSessionRef()) return false;
      if (hasSessionRef()) {
        if (!getSessionRef()
            .equals(other.getSessionRef())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasP1()) {
        hash = (37 * hash) + P1_FIELD_NUMBER;
        hash = (53 * hash) + getP1().hashCode();
      }
      if (hasP2()) {
        hash = (37 * hash) + P2_FIELD_NUMBER;
        hash = (53 * hash) + getP2().hashCode();
      }
      if (hasP3()) {
        hash = (37 * hash) + P3_FIELD_NUMBER;
        hash = (53 * hash) + getP3().hashCode();
      }
      if (hasP4()) {
        hash = (37 * hash) + P4_FIELD_NUMBER;
        hash = (53 * hash) + getP4().hashCode();
      }
      if (hasLayerId()) {
        hash = (37 * hash) + LAYERID_FIELD_NUMBER;
        hash = (53 * hash) + getLayerId();
      }
      if (hasSceneZoneId()) {
        hash = (37 * hash) + SCENEZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getSceneZoneId();
      }
      if (hasSessionRef()) {
        hash = (37 * hash) + SESSIONREF_FIELD_NUMBER;
        hash = (53 * hash) + getSessionRef().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAoiView.UpdateViewAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.UpdateViewAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.UpdateViewAsk)
        com.yorha.proto.SsAoiView.UpdateViewAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAoiView.UpdateViewAsk.class, com.yorha.proto.SsAoiView.UpdateViewAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsAoiView.UpdateViewAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getP1FieldBuilder();
          getP2FieldBuilder();
          getP3FieldBuilder();
          getP4FieldBuilder();
          getSessionRefFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (p1Builder_ == null) {
          p1_ = null;
        } else {
          p1Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (p2Builder_ == null) {
          p2_ = null;
        } else {
          p2Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (p3Builder_ == null) {
          p3_ = null;
        } else {
          p3Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (p4Builder_ == null) {
          p4_ = null;
        } else {
          p4Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        layerId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        sceneZoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        if (sessionRefBuilder_ == null) {
          sessionRef_ = null;
        } else {
          sessionRefBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.UpdateViewAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsAoiView.UpdateViewAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.UpdateViewAsk build() {
        com.yorha.proto.SsAoiView.UpdateViewAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.UpdateViewAsk buildPartial() {
        com.yorha.proto.SsAoiView.UpdateViewAsk result = new com.yorha.proto.SsAoiView.UpdateViewAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (p1Builder_ == null) {
            result.p1_ = p1_;
          } else {
            result.p1_ = p1Builder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (p2Builder_ == null) {
            result.p2_ = p2_;
          } else {
            result.p2_ = p2Builder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (p3Builder_ == null) {
            result.p3_ = p3_;
          } else {
            result.p3_ = p3Builder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (p4Builder_ == null) {
            result.p4_ = p4_;
          } else {
            result.p4_ = p4Builder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.layerId_ = layerId_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.sceneZoneId_ = sceneZoneId_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          if (sessionRefBuilder_ == null) {
            result.sessionRef_ = sessionRef_;
          } else {
            result.sessionRef_ = sessionRefBuilder_.build();
          }
          to_bitField0_ |= 0x00000080;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAoiView.UpdateViewAsk) {
          return mergeFrom((com.yorha.proto.SsAoiView.UpdateViewAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAoiView.UpdateViewAsk other) {
        if (other == com.yorha.proto.SsAoiView.UpdateViewAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasP1()) {
          mergeP1(other.getP1());
        }
        if (other.hasP2()) {
          mergeP2(other.getP2());
        }
        if (other.hasP3()) {
          mergeP3(other.getP3());
        }
        if (other.hasP4()) {
          mergeP4(other.getP4());
        }
        if (other.hasLayerId()) {
          setLayerId(other.getLayerId());
        }
        if (other.hasSceneZoneId()) {
          setSceneZoneId(other.getSceneZoneId());
        }
        if (other.hasSessionRef()) {
          mergeSessionRef(other.getSessionRef());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAoiView.UpdateViewAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAoiView.UpdateViewAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB p1_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> p1Builder_;
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       * @return Whether the p1 field is set.
       */
      public boolean hasP1() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       * @return The p1.
       */
      public com.yorha.proto.StructPB.PointPB getP1() {
        if (p1Builder_ == null) {
          return p1_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p1_;
        } else {
          return p1Builder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       */
      public Builder setP1(com.yorha.proto.StructPB.PointPB value) {
        if (p1Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          p1_ = value;
          onChanged();
        } else {
          p1Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       */
      public Builder setP1(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (p1Builder_ == null) {
          p1_ = builderForValue.build();
          onChanged();
        } else {
          p1Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       */
      public Builder mergeP1(com.yorha.proto.StructPB.PointPB value) {
        if (p1Builder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              p1_ != null &&
              p1_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            p1_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(p1_).mergeFrom(value).buildPartial();
          } else {
            p1_ = value;
          }
          onChanged();
        } else {
          p1Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       */
      public Builder clearP1() {
        if (p1Builder_ == null) {
          p1_ = null;
          onChanged();
        } else {
          p1Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getP1Builder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getP1FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getP1OrBuilder() {
        if (p1Builder_ != null) {
          return p1Builder_.getMessageOrBuilder();
        } else {
          return p1_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p1_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p1 = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getP1FieldBuilder() {
        if (p1Builder_ == null) {
          p1Builder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getP1(),
                  getParentForChildren(),
                  isClean());
          p1_ = null;
        }
        return p1Builder_;
      }

      private com.yorha.proto.StructPB.PointPB p2_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> p2Builder_;
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       * @return Whether the p2 field is set.
       */
      public boolean hasP2() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       * @return The p2.
       */
      public com.yorha.proto.StructPB.PointPB getP2() {
        if (p2Builder_ == null) {
          return p2_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p2_;
        } else {
          return p2Builder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       */
      public Builder setP2(com.yorha.proto.StructPB.PointPB value) {
        if (p2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          p2_ = value;
          onChanged();
        } else {
          p2Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       */
      public Builder setP2(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (p2Builder_ == null) {
          p2_ = builderForValue.build();
          onChanged();
        } else {
          p2Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       */
      public Builder mergeP2(com.yorha.proto.StructPB.PointPB value) {
        if (p2Builder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              p2_ != null &&
              p2_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            p2_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(p2_).mergeFrom(value).buildPartial();
          } else {
            p2_ = value;
          }
          onChanged();
        } else {
          p2Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       */
      public Builder clearP2() {
        if (p2Builder_ == null) {
          p2_ = null;
          onChanged();
        } else {
          p2Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getP2Builder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getP2FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getP2OrBuilder() {
        if (p2Builder_ != null) {
          return p2Builder_.getMessageOrBuilder();
        } else {
          return p2_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p2_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p2 = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getP2FieldBuilder() {
        if (p2Builder_ == null) {
          p2Builder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getP2(),
                  getParentForChildren(),
                  isClean());
          p2_ = null;
        }
        return p2Builder_;
      }

      private com.yorha.proto.StructPB.PointPB p3_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> p3Builder_;
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       * @return Whether the p3 field is set.
       */
      public boolean hasP3() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       * @return The p3.
       */
      public com.yorha.proto.StructPB.PointPB getP3() {
        if (p3Builder_ == null) {
          return p3_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p3_;
        } else {
          return p3Builder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       */
      public Builder setP3(com.yorha.proto.StructPB.PointPB value) {
        if (p3Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          p3_ = value;
          onChanged();
        } else {
          p3Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       */
      public Builder setP3(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (p3Builder_ == null) {
          p3_ = builderForValue.build();
          onChanged();
        } else {
          p3Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       */
      public Builder mergeP3(com.yorha.proto.StructPB.PointPB value) {
        if (p3Builder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              p3_ != null &&
              p3_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            p3_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(p3_).mergeFrom(value).buildPartial();
          } else {
            p3_ = value;
          }
          onChanged();
        } else {
          p3Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       */
      public Builder clearP3() {
        if (p3Builder_ == null) {
          p3_ = null;
          onChanged();
        } else {
          p3Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getP3Builder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getP3FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getP3OrBuilder() {
        if (p3Builder_ != null) {
          return p3Builder_.getMessageOrBuilder();
        } else {
          return p3_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p3_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p3 = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getP3FieldBuilder() {
        if (p3Builder_ == null) {
          p3Builder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getP3(),
                  getParentForChildren(),
                  isClean());
          p3_ = null;
        }
        return p3Builder_;
      }

      private com.yorha.proto.StructPB.PointPB p4_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> p4Builder_;
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       * @return Whether the p4 field is set.
       */
      public boolean hasP4() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       * @return The p4.
       */
      public com.yorha.proto.StructPB.PointPB getP4() {
        if (p4Builder_ == null) {
          return p4_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p4_;
        } else {
          return p4Builder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       */
      public Builder setP4(com.yorha.proto.StructPB.PointPB value) {
        if (p4Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          p4_ = value;
          onChanged();
        } else {
          p4Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       */
      public Builder setP4(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (p4Builder_ == null) {
          p4_ = builderForValue.build();
          onChanged();
        } else {
          p4Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       */
      public Builder mergeP4(com.yorha.proto.StructPB.PointPB value) {
        if (p4Builder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              p4_ != null &&
              p4_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            p4_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(p4_).mergeFrom(value).buildPartial();
          } else {
            p4_ = value;
          }
          onChanged();
        } else {
          p4Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       */
      public Builder clearP4() {
        if (p4Builder_ == null) {
          p4_ = null;
          onChanged();
        } else {
          p4Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getP4Builder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getP4FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getP4OrBuilder() {
        if (p4Builder_ != null) {
          return p4Builder_.getMessageOrBuilder();
        } else {
          return p4_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : p4_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB p4 = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getP4FieldBuilder() {
        if (p4Builder_ == null) {
          p4Builder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getP4(),
                  getParentForChildren(),
                  isClean());
          p4_ = null;
        }
        return p4Builder_;
      }

      private int layerId_ ;
      /**
       * <code>optional int32 layerId = 6;</code>
       * @return Whether the layerId field is set.
       */
      @java.lang.Override
      public boolean hasLayerId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional int32 layerId = 6;</code>
       * @return The layerId.
       */
      @java.lang.Override
      public int getLayerId() {
        return layerId_;
      }
      /**
       * <code>optional int32 layerId = 6;</code>
       * @param value The layerId to set.
       * @return This builder for chaining.
       */
      public Builder setLayerId(int value) {
        bitField0_ |= 0x00000020;
        layerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 layerId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearLayerId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        layerId_ = 0;
        onChanged();
        return this;
      }

      private int sceneZoneId_ ;
      /**
       * <pre>
       * 场景zoneId, 在kvk里就是kvkId
       * </pre>
       *
       * <code>optional int32 sceneZoneId = 7;</code>
       * @return Whether the sceneZoneId field is set.
       */
      @java.lang.Override
      public boolean hasSceneZoneId() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 场景zoneId, 在kvk里就是kvkId
       * </pre>
       *
       * <code>optional int32 sceneZoneId = 7;</code>
       * @return The sceneZoneId.
       */
      @java.lang.Override
      public int getSceneZoneId() {
        return sceneZoneId_;
      }
      /**
       * <pre>
       * 场景zoneId, 在kvk里就是kvkId
       * </pre>
       *
       * <code>optional int32 sceneZoneId = 7;</code>
       * @param value The sceneZoneId to set.
       * @return This builder for chaining.
       */
      public Builder setSceneZoneId(int value) {
        bitField0_ |= 0x00000040;
        sceneZoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 场景zoneId, 在kvk里就是kvkId
       * </pre>
       *
       * <code>optional int32 sceneZoneId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearSceneZoneId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        sceneZoneId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.ActorRefData sessionRef_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> sessionRefBuilder_;
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       * @return Whether the sessionRef field is set.
       */
      public boolean hasSessionRef() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       * @return The sessionRef.
       */
      public com.yorha.proto.CommonMsg.ActorRefData getSessionRef() {
        if (sessionRefBuilder_ == null) {
          return sessionRef_ == null ? com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
        } else {
          return sessionRefBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       */
      public Builder setSessionRef(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (sessionRefBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sessionRef_ = value;
          onChanged();
        } else {
          sessionRefBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       */
      public Builder setSessionRef(
          com.yorha.proto.CommonMsg.ActorRefData.Builder builderForValue) {
        if (sessionRefBuilder_ == null) {
          sessionRef_ = builderForValue.build();
          onChanged();
        } else {
          sessionRefBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       */
      public Builder mergeSessionRef(com.yorha.proto.CommonMsg.ActorRefData value) {
        if (sessionRefBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
              sessionRef_ != null &&
              sessionRef_ != com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance()) {
            sessionRef_ =
              com.yorha.proto.CommonMsg.ActorRefData.newBuilder(sessionRef_).mergeFrom(value).buildPartial();
          } else {
            sessionRef_ = value;
          }
          onChanged();
        } else {
          sessionRefBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       */
      public Builder clearSessionRef() {
        if (sessionRefBuilder_ == null) {
          sessionRef_ = null;
          onChanged();
        } else {
          sessionRefBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefData.Builder getSessionRefBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getSessionRefFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       */
      public com.yorha.proto.CommonMsg.ActorRefDataOrBuilder getSessionRefOrBuilder() {
        if (sessionRefBuilder_ != null) {
          return sessionRefBuilder_.getMessageOrBuilder();
        } else {
          return sessionRef_ == null ?
              com.yorha.proto.CommonMsg.ActorRefData.getDefaultInstance() : sessionRef_;
        }
      }
      /**
       * <pre>
       * 旁观者的gate
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActorRefData sessionRef = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder> 
          getSessionRefFieldBuilder() {
        if (sessionRefBuilder_ == null) {
          sessionRefBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ActorRefData, com.yorha.proto.CommonMsg.ActorRefData.Builder, com.yorha.proto.CommonMsg.ActorRefDataOrBuilder>(
                  getSessionRef(),
                  getParentForChildren(),
                  isClean());
          sessionRef_ = null;
        }
        return sessionRefBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.UpdateViewAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.UpdateViewAsk)
    private static final com.yorha.proto.SsAoiView.UpdateViewAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAoiView.UpdateViewAsk();
    }

    public static com.yorha.proto.SsAoiView.UpdateViewAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<UpdateViewAsk>
        PARSER = new com.google.protobuf.AbstractParser<UpdateViewAsk>() {
      @java.lang.Override
      public UpdateViewAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UpdateViewAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UpdateViewAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpdateViewAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAoiView.UpdateViewAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpdateViewAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.UpdateViewAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool isOk = 1;</code>
     * @return Whether the isOk field is set.
     */
    boolean hasIsOk();
    /**
     * <code>optional bool isOk = 1;</code>
     * @return The isOk.
     */
    boolean getIsOk();
  }
  /**
   * Protobuf type {@code com.yorha.proto.UpdateViewAns}
   */
  public static final class UpdateViewAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.UpdateViewAns)
      UpdateViewAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpdateViewAns.newBuilder() to construct.
    private UpdateViewAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpdateViewAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpdateViewAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UpdateViewAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isOk_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAoiView.UpdateViewAns.class, com.yorha.proto.SsAoiView.UpdateViewAns.Builder.class);
    }

    private int bitField0_;
    public static final int ISOK_FIELD_NUMBER = 1;
    private boolean isOk_;
    /**
     * <code>optional bool isOk = 1;</code>
     * @return Whether the isOk field is set.
     */
    @java.lang.Override
    public boolean hasIsOk() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool isOk = 1;</code>
     * @return The isOk.
     */
    @java.lang.Override
    public boolean getIsOk() {
      return isOk_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isOk_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isOk_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAoiView.UpdateViewAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAoiView.UpdateViewAns other = (com.yorha.proto.SsAoiView.UpdateViewAns) obj;

      if (hasIsOk() != other.hasIsOk()) return false;
      if (hasIsOk()) {
        if (getIsOk()
            != other.getIsOk()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsOk()) {
        hash = (37 * hash) + ISOK_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsOk());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.UpdateViewAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAoiView.UpdateViewAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.UpdateViewAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.UpdateViewAns)
        com.yorha.proto.SsAoiView.UpdateViewAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAoiView.UpdateViewAns.class, com.yorha.proto.SsAoiView.UpdateViewAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsAoiView.UpdateViewAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isOk_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_UpdateViewAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.UpdateViewAns getDefaultInstanceForType() {
        return com.yorha.proto.SsAoiView.UpdateViewAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.UpdateViewAns build() {
        com.yorha.proto.SsAoiView.UpdateViewAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.UpdateViewAns buildPartial() {
        com.yorha.proto.SsAoiView.UpdateViewAns result = new com.yorha.proto.SsAoiView.UpdateViewAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isOk_ = isOk_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAoiView.UpdateViewAns) {
          return mergeFrom((com.yorha.proto.SsAoiView.UpdateViewAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAoiView.UpdateViewAns other) {
        if (other == com.yorha.proto.SsAoiView.UpdateViewAns.getDefaultInstance()) return this;
        if (other.hasIsOk()) {
          setIsOk(other.getIsOk());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAoiView.UpdateViewAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAoiView.UpdateViewAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isOk_ ;
      /**
       * <code>optional bool isOk = 1;</code>
       * @return Whether the isOk field is set.
       */
      @java.lang.Override
      public boolean hasIsOk() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool isOk = 1;</code>
       * @return The isOk.
       */
      @java.lang.Override
      public boolean getIsOk() {
        return isOk_;
      }
      /**
       * <code>optional bool isOk = 1;</code>
       * @param value The isOk to set.
       * @return This builder for chaining.
       */
      public Builder setIsOk(boolean value) {
        bitField0_ |= 0x00000001;
        isOk_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isOk = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsOk() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isOk_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.UpdateViewAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.UpdateViewAns)
    private static final com.yorha.proto.SsAoiView.UpdateViewAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAoiView.UpdateViewAns();
    }

    public static com.yorha.proto.SsAoiView.UpdateViewAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<UpdateViewAns>
        PARSER = new com.google.protobuf.AbstractParser<UpdateViewAns>() {
      @java.lang.Override
      public UpdateViewAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UpdateViewAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UpdateViewAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpdateViewAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAoiView.UpdateViewAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClearViewAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClearViewAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClearViewAsk}
   */
  public static final class ClearViewAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClearViewAsk)
      ClearViewAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClearViewAsk.newBuilder() to construct.
    private ClearViewAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClearViewAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClearViewAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClearViewAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAoiView.ClearViewAsk.class, com.yorha.proto.SsAoiView.ClearViewAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAoiView.ClearViewAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAoiView.ClearViewAsk other = (com.yorha.proto.SsAoiView.ClearViewAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAoiView.ClearViewAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClearViewAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClearViewAsk)
        com.yorha.proto.SsAoiView.ClearViewAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAoiView.ClearViewAsk.class, com.yorha.proto.SsAoiView.ClearViewAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsAoiView.ClearViewAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ClearViewAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsAoiView.ClearViewAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ClearViewAsk build() {
        com.yorha.proto.SsAoiView.ClearViewAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ClearViewAsk buildPartial() {
        com.yorha.proto.SsAoiView.ClearViewAsk result = new com.yorha.proto.SsAoiView.ClearViewAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAoiView.ClearViewAsk) {
          return mergeFrom((com.yorha.proto.SsAoiView.ClearViewAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAoiView.ClearViewAsk other) {
        if (other == com.yorha.proto.SsAoiView.ClearViewAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAoiView.ClearViewAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAoiView.ClearViewAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClearViewAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClearViewAsk)
    private static final com.yorha.proto.SsAoiView.ClearViewAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAoiView.ClearViewAsk();
    }

    public static com.yorha.proto.SsAoiView.ClearViewAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClearViewAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClearViewAsk>() {
      @java.lang.Override
      public ClearViewAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClearViewAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClearViewAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClearViewAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAoiView.ClearViewAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClearViewAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClearViewAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClearViewAns}
   */
  public static final class ClearViewAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClearViewAns)
      ClearViewAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClearViewAns.newBuilder() to construct.
    private ClearViewAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClearViewAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClearViewAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClearViewAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAoiView.ClearViewAns.class, com.yorha.proto.SsAoiView.ClearViewAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAoiView.ClearViewAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAoiView.ClearViewAns other = (com.yorha.proto.SsAoiView.ClearViewAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ClearViewAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAoiView.ClearViewAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClearViewAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClearViewAns)
        com.yorha.proto.SsAoiView.ClearViewAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAoiView.ClearViewAns.class, com.yorha.proto.SsAoiView.ClearViewAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsAoiView.ClearViewAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ClearViewAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ClearViewAns getDefaultInstanceForType() {
        return com.yorha.proto.SsAoiView.ClearViewAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ClearViewAns build() {
        com.yorha.proto.SsAoiView.ClearViewAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ClearViewAns buildPartial() {
        com.yorha.proto.SsAoiView.ClearViewAns result = new com.yorha.proto.SsAoiView.ClearViewAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAoiView.ClearViewAns) {
          return mergeFrom((com.yorha.proto.SsAoiView.ClearViewAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAoiView.ClearViewAns other) {
        if (other == com.yorha.proto.SsAoiView.ClearViewAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAoiView.ClearViewAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAoiView.ClearViewAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClearViewAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClearViewAns)
    private static final com.yorha.proto.SsAoiView.ClearViewAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAoiView.ClearViewAns();
    }

    public static com.yorha.proto.SsAoiView.ClearViewAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClearViewAns>
        PARSER = new com.google.protobuf.AbstractParser<ClearViewAns>() {
      @java.lang.Override
      public ClearViewAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClearViewAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClearViewAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClearViewAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAoiView.ClearViewAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AddSceneObjCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AddSceneObjCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 objId = 1;</code>
     * @return Whether the objId field is set.
     */
    boolean hasObjId();
    /**
     * <code>optional int64 objId = 1;</code>
     * @return The objId.
     */
    long getObjId();

    /**
     * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
     * @return Whether the aabb field is set.
     */
    boolean hasAabb();
    /**
     * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
     * @return The aabb.
     */
    com.yorha.proto.SsAoiView.AABB getAabb();
    /**
     * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
     */
    com.yorha.proto.SsAoiView.AABBOrBuilder getAabbOrBuilder();

    /**
     * <code>optional int32 layer = 3;</code>
     * @return Whether the layer field is set.
     */
    boolean hasLayer();
    /**
     * <code>optional int32 layer = 3;</code>
     * @return The layer.
     */
    int getLayer();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AddSceneObjCmd}
   */
  public static final class AddSceneObjCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AddSceneObjCmd)
      AddSceneObjCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AddSceneObjCmd.newBuilder() to construct.
    private AddSceneObjCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AddSceneObjCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AddSceneObjCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AddSceneObjCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              objId_ = input.readInt64();
              break;
            }
            case 18: {
              com.yorha.proto.SsAoiView.AABB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = aabb_.toBuilder();
              }
              aabb_ = input.readMessage(com.yorha.proto.SsAoiView.AABB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(aabb_);
                aabb_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              layer_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AddSceneObjCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AddSceneObjCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAoiView.AddSceneObjCmd.class, com.yorha.proto.SsAoiView.AddSceneObjCmd.Builder.class);
    }

    private int bitField0_;
    public static final int OBJID_FIELD_NUMBER = 1;
    private long objId_;
    /**
     * <code>optional int64 objId = 1;</code>
     * @return Whether the objId field is set.
     */
    @java.lang.Override
    public boolean hasObjId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 objId = 1;</code>
     * @return The objId.
     */
    @java.lang.Override
    public long getObjId() {
      return objId_;
    }

    public static final int AABB_FIELD_NUMBER = 2;
    private com.yorha.proto.SsAoiView.AABB aabb_;
    /**
     * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
     * @return Whether the aabb field is set.
     */
    @java.lang.Override
    public boolean hasAabb() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
     * @return The aabb.
     */
    @java.lang.Override
    public com.yorha.proto.SsAoiView.AABB getAabb() {
      return aabb_ == null ? com.yorha.proto.SsAoiView.AABB.getDefaultInstance() : aabb_;
    }
    /**
     * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsAoiView.AABBOrBuilder getAabbOrBuilder() {
      return aabb_ == null ? com.yorha.proto.SsAoiView.AABB.getDefaultInstance() : aabb_;
    }

    public static final int LAYER_FIELD_NUMBER = 3;
    private int layer_;
    /**
     * <code>optional int32 layer = 3;</code>
     * @return Whether the layer field is set.
     */
    @java.lang.Override
    public boolean hasLayer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 layer = 3;</code>
     * @return The layer.
     */
    @java.lang.Override
    public int getLayer() {
      return layer_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, objId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getAabb());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, layer_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, objId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getAabb());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, layer_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAoiView.AddSceneObjCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAoiView.AddSceneObjCmd other = (com.yorha.proto.SsAoiView.AddSceneObjCmd) obj;

      if (hasObjId() != other.hasObjId()) return false;
      if (hasObjId()) {
        if (getObjId()
            != other.getObjId()) return false;
      }
      if (hasAabb() != other.hasAabb()) return false;
      if (hasAabb()) {
        if (!getAabb()
            .equals(other.getAabb())) return false;
      }
      if (hasLayer() != other.hasLayer()) return false;
      if (hasLayer()) {
        if (getLayer()
            != other.getLayer()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasObjId()) {
        hash = (37 * hash) + OBJID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getObjId());
      }
      if (hasAabb()) {
        hash = (37 * hash) + AABB_FIELD_NUMBER;
        hash = (53 * hash) + getAabb().hashCode();
      }
      if (hasLayer()) {
        hash = (37 * hash) + LAYER_FIELD_NUMBER;
        hash = (53 * hash) + getLayer();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.AddSceneObjCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAoiView.AddSceneObjCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AddSceneObjCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AddSceneObjCmd)
        com.yorha.proto.SsAoiView.AddSceneObjCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AddSceneObjCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AddSceneObjCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAoiView.AddSceneObjCmd.class, com.yorha.proto.SsAoiView.AddSceneObjCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsAoiView.AddSceneObjCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAabbFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        objId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (aabbBuilder_ == null) {
          aabb_ = null;
        } else {
          aabbBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        layer_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_AddSceneObjCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.AddSceneObjCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsAoiView.AddSceneObjCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.AddSceneObjCmd build() {
        com.yorha.proto.SsAoiView.AddSceneObjCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.AddSceneObjCmd buildPartial() {
        com.yorha.proto.SsAoiView.AddSceneObjCmd result = new com.yorha.proto.SsAoiView.AddSceneObjCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.objId_ = objId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (aabbBuilder_ == null) {
            result.aabb_ = aabb_;
          } else {
            result.aabb_ = aabbBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.layer_ = layer_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAoiView.AddSceneObjCmd) {
          return mergeFrom((com.yorha.proto.SsAoiView.AddSceneObjCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAoiView.AddSceneObjCmd other) {
        if (other == com.yorha.proto.SsAoiView.AddSceneObjCmd.getDefaultInstance()) return this;
        if (other.hasObjId()) {
          setObjId(other.getObjId());
        }
        if (other.hasAabb()) {
          mergeAabb(other.getAabb());
        }
        if (other.hasLayer()) {
          setLayer(other.getLayer());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAoiView.AddSceneObjCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAoiView.AddSceneObjCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long objId_ ;
      /**
       * <code>optional int64 objId = 1;</code>
       * @return Whether the objId field is set.
       */
      @java.lang.Override
      public boolean hasObjId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 objId = 1;</code>
       * @return The objId.
       */
      @java.lang.Override
      public long getObjId() {
        return objId_;
      }
      /**
       * <code>optional int64 objId = 1;</code>
       * @param value The objId to set.
       * @return This builder for chaining.
       */
      public Builder setObjId(long value) {
        bitField0_ |= 0x00000001;
        objId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 objId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearObjId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        objId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.SsAoiView.AABB aabb_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAoiView.AABB, com.yorha.proto.SsAoiView.AABB.Builder, com.yorha.proto.SsAoiView.AABBOrBuilder> aabbBuilder_;
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       * @return Whether the aabb field is set.
       */
      public boolean hasAabb() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       * @return The aabb.
       */
      public com.yorha.proto.SsAoiView.AABB getAabb() {
        if (aabbBuilder_ == null) {
          return aabb_ == null ? com.yorha.proto.SsAoiView.AABB.getDefaultInstance() : aabb_;
        } else {
          return aabbBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       */
      public Builder setAabb(com.yorha.proto.SsAoiView.AABB value) {
        if (aabbBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          aabb_ = value;
          onChanged();
        } else {
          aabbBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       */
      public Builder setAabb(
          com.yorha.proto.SsAoiView.AABB.Builder builderForValue) {
        if (aabbBuilder_ == null) {
          aabb_ = builderForValue.build();
          onChanged();
        } else {
          aabbBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       */
      public Builder mergeAabb(com.yorha.proto.SsAoiView.AABB value) {
        if (aabbBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              aabb_ != null &&
              aabb_ != com.yorha.proto.SsAoiView.AABB.getDefaultInstance()) {
            aabb_ =
              com.yorha.proto.SsAoiView.AABB.newBuilder(aabb_).mergeFrom(value).buildPartial();
          } else {
            aabb_ = value;
          }
          onChanged();
        } else {
          aabbBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       */
      public Builder clearAabb() {
        if (aabbBuilder_ == null) {
          aabb_ = null;
          onChanged();
        } else {
          aabbBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       */
      public com.yorha.proto.SsAoiView.AABB.Builder getAabbBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getAabbFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       */
      public com.yorha.proto.SsAoiView.AABBOrBuilder getAabbOrBuilder() {
        if (aabbBuilder_ != null) {
          return aabbBuilder_.getMessageOrBuilder();
        } else {
          return aabb_ == null ?
              com.yorha.proto.SsAoiView.AABB.getDefaultInstance() : aabb_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.AABB aabb = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.SsAoiView.AABB, com.yorha.proto.SsAoiView.AABB.Builder, com.yorha.proto.SsAoiView.AABBOrBuilder> 
          getAabbFieldBuilder() {
        if (aabbBuilder_ == null) {
          aabbBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.SsAoiView.AABB, com.yorha.proto.SsAoiView.AABB.Builder, com.yorha.proto.SsAoiView.AABBOrBuilder>(
                  getAabb(),
                  getParentForChildren(),
                  isClean());
          aabb_ = null;
        }
        return aabbBuilder_;
      }

      private int layer_ ;
      /**
       * <code>optional int32 layer = 3;</code>
       * @return Whether the layer field is set.
       */
      @java.lang.Override
      public boolean hasLayer() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 layer = 3;</code>
       * @return The layer.
       */
      @java.lang.Override
      public int getLayer() {
        return layer_;
      }
      /**
       * <code>optional int32 layer = 3;</code>
       * @param value The layer to set.
       * @return This builder for chaining.
       */
      public Builder setLayer(int value) {
        bitField0_ |= 0x00000004;
        layer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 layer = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLayer() {
        bitField0_ = (bitField0_ & ~0x00000004);
        layer_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AddSceneObjCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AddSceneObjCmd)
    private static final com.yorha.proto.SsAoiView.AddSceneObjCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAoiView.AddSceneObjCmd();
    }

    public static com.yorha.proto.SsAoiView.AddSceneObjCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AddSceneObjCmd>
        PARSER = new com.google.protobuf.AbstractParser<AddSceneObjCmd>() {
      @java.lang.Override
      public AddSceneObjCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AddSceneObjCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AddSceneObjCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AddSceneObjCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAoiView.AddSceneObjCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChangeSceneObjCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ChangeSceneObjCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 objId = 1;</code>
     * @return Whether the objId field is set.
     */
    boolean hasObjId();
    /**
     * <code>optional int64 objId = 1;</code>
     * @return The objId.
     */
    long getObjId();

    /**
     * <code>optional int32 layer = 2;</code>
     * @return Whether the layer field is set.
     */
    boolean hasLayer();
    /**
     * <code>optional int32 layer = 2;</code>
     * @return The layer.
     */
    int getLayer();

    /**
     * <code>optional int32 newLayer = 3;</code>
     * @return Whether the newLayer field is set.
     */
    boolean hasNewLayer();
    /**
     * <code>optional int32 newLayer = 3;</code>
     * @return The newLayer.
     */
    int getNewLayer();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ChangeSceneObjCmd}
   */
  public static final class ChangeSceneObjCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ChangeSceneObjCmd)
      ChangeSceneObjCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChangeSceneObjCmd.newBuilder() to construct.
    private ChangeSceneObjCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChangeSceneObjCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChangeSceneObjCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChangeSceneObjCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              objId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              layer_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              newLayer_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ChangeSceneObjCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ChangeSceneObjCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsAoiView.ChangeSceneObjCmd.class, com.yorha.proto.SsAoiView.ChangeSceneObjCmd.Builder.class);
    }

    private int bitField0_;
    public static final int OBJID_FIELD_NUMBER = 1;
    private long objId_;
    /**
     * <code>optional int64 objId = 1;</code>
     * @return Whether the objId field is set.
     */
    @java.lang.Override
    public boolean hasObjId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 objId = 1;</code>
     * @return The objId.
     */
    @java.lang.Override
    public long getObjId() {
      return objId_;
    }

    public static final int LAYER_FIELD_NUMBER = 2;
    private int layer_;
    /**
     * <code>optional int32 layer = 2;</code>
     * @return Whether the layer field is set.
     */
    @java.lang.Override
    public boolean hasLayer() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 layer = 2;</code>
     * @return The layer.
     */
    @java.lang.Override
    public int getLayer() {
      return layer_;
    }

    public static final int NEWLAYER_FIELD_NUMBER = 3;
    private int newLayer_;
    /**
     * <code>optional int32 newLayer = 3;</code>
     * @return Whether the newLayer field is set.
     */
    @java.lang.Override
    public boolean hasNewLayer() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 newLayer = 3;</code>
     * @return The newLayer.
     */
    @java.lang.Override
    public int getNewLayer() {
      return newLayer_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, objId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, layer_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, newLayer_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, objId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, layer_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, newLayer_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsAoiView.ChangeSceneObjCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsAoiView.ChangeSceneObjCmd other = (com.yorha.proto.SsAoiView.ChangeSceneObjCmd) obj;

      if (hasObjId() != other.hasObjId()) return false;
      if (hasObjId()) {
        if (getObjId()
            != other.getObjId()) return false;
      }
      if (hasLayer() != other.hasLayer()) return false;
      if (hasLayer()) {
        if (getLayer()
            != other.getLayer()) return false;
      }
      if (hasNewLayer() != other.hasNewLayer()) return false;
      if (hasNewLayer()) {
        if (getNewLayer()
            != other.getNewLayer()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasObjId()) {
        hash = (37 * hash) + OBJID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getObjId());
      }
      if (hasLayer()) {
        hash = (37 * hash) + LAYER_FIELD_NUMBER;
        hash = (53 * hash) + getLayer();
      }
      if (hasNewLayer()) {
        hash = (37 * hash) + NEWLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getNewLayer();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsAoiView.ChangeSceneObjCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ChangeSceneObjCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ChangeSceneObjCmd)
        com.yorha.proto.SsAoiView.ChangeSceneObjCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ChangeSceneObjCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ChangeSceneObjCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsAoiView.ChangeSceneObjCmd.class, com.yorha.proto.SsAoiView.ChangeSceneObjCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsAoiView.ChangeSceneObjCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        objId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        layer_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        newLayer_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsAoiView.internal_static_com_yorha_proto_ChangeSceneObjCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ChangeSceneObjCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsAoiView.ChangeSceneObjCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ChangeSceneObjCmd build() {
        com.yorha.proto.SsAoiView.ChangeSceneObjCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsAoiView.ChangeSceneObjCmd buildPartial() {
        com.yorha.proto.SsAoiView.ChangeSceneObjCmd result = new com.yorha.proto.SsAoiView.ChangeSceneObjCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.objId_ = objId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.layer_ = layer_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.newLayer_ = newLayer_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsAoiView.ChangeSceneObjCmd) {
          return mergeFrom((com.yorha.proto.SsAoiView.ChangeSceneObjCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsAoiView.ChangeSceneObjCmd other) {
        if (other == com.yorha.proto.SsAoiView.ChangeSceneObjCmd.getDefaultInstance()) return this;
        if (other.hasObjId()) {
          setObjId(other.getObjId());
        }
        if (other.hasLayer()) {
          setLayer(other.getLayer());
        }
        if (other.hasNewLayer()) {
          setNewLayer(other.getNewLayer());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsAoiView.ChangeSceneObjCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsAoiView.ChangeSceneObjCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long objId_ ;
      /**
       * <code>optional int64 objId = 1;</code>
       * @return Whether the objId field is set.
       */
      @java.lang.Override
      public boolean hasObjId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 objId = 1;</code>
       * @return The objId.
       */
      @java.lang.Override
      public long getObjId() {
        return objId_;
      }
      /**
       * <code>optional int64 objId = 1;</code>
       * @param value The objId to set.
       * @return This builder for chaining.
       */
      public Builder setObjId(long value) {
        bitField0_ |= 0x00000001;
        objId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 objId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearObjId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        objId_ = 0L;
        onChanged();
        return this;
      }

      private int layer_ ;
      /**
       * <code>optional int32 layer = 2;</code>
       * @return Whether the layer field is set.
       */
      @java.lang.Override
      public boolean hasLayer() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 layer = 2;</code>
       * @return The layer.
       */
      @java.lang.Override
      public int getLayer() {
        return layer_;
      }
      /**
       * <code>optional int32 layer = 2;</code>
       * @param value The layer to set.
       * @return This builder for chaining.
       */
      public Builder setLayer(int value) {
        bitField0_ |= 0x00000002;
        layer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 layer = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLayer() {
        bitField0_ = (bitField0_ & ~0x00000002);
        layer_ = 0;
        onChanged();
        return this;
      }

      private int newLayer_ ;
      /**
       * <code>optional int32 newLayer = 3;</code>
       * @return Whether the newLayer field is set.
       */
      @java.lang.Override
      public boolean hasNewLayer() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 newLayer = 3;</code>
       * @return The newLayer.
       */
      @java.lang.Override
      public int getNewLayer() {
        return newLayer_;
      }
      /**
       * <code>optional int32 newLayer = 3;</code>
       * @param value The newLayer to set.
       * @return This builder for chaining.
       */
      public Builder setNewLayer(int value) {
        bitField0_ |= 0x00000004;
        newLayer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 newLayer = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewLayer() {
        bitField0_ = (bitField0_ & ~0x00000004);
        newLayer_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ChangeSceneObjCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ChangeSceneObjCmd)
    private static final com.yorha.proto.SsAoiView.ChangeSceneObjCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsAoiView.ChangeSceneObjCmd();
    }

    public static com.yorha.proto.SsAoiView.ChangeSceneObjCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ChangeSceneObjCmd>
        PARSER = new com.google.protobuf.AbstractParser<ChangeSceneObjCmd>() {
      @java.lang.Override
      public ChangeSceneObjCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChangeSceneObjCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChangeSceneObjCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChangeSceneObjCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsAoiView.ChangeSceneObjCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AABB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AABB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_UpdateViewAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_UpdateViewAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_UpdateViewAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_UpdateViewAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClearViewAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClearViewAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClearViewAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClearViewAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AddSceneObjCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AddSceneObjCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ChangeSceneObjCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ChangeSceneObjCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&ss_proto/gen/aoiView/ss_aoi_view.proto" +
      "\022\017com.yorha.proto\032\"cs_proto/gen/common/s" +
      "tructPB.proto\032$ss_proto/gen/common/commo" +
      "n_msg.proto\"@\n\004AABB\022\014\n\004left\030\001 \001(\005\022\013\n\003top" +
      "\030\002 \001(\005\022\r\n\005right\030\003 \001(\005\022\016\n\006bottom\030\004 \001(\005\"\222\002" +
      "\n\rUpdateViewAsk\022\020\n\010playerId\030\001 \001(\003\022$\n\002p1\030" +
      "\002 \001(\0132\030.com.yorha.proto.PointPB\022$\n\002p2\030\003 " +
      "\001(\0132\030.com.yorha.proto.PointPB\022$\n\002p3\030\004 \001(" +
      "\0132\030.com.yorha.proto.PointPB\022$\n\002p4\030\005 \001(\0132" +
      "\030.com.yorha.proto.PointPB\022\017\n\007layerId\030\006 \001" +
      "(\005\022\023\n\013sceneZoneId\030\007 \001(\005\0221\n\nsessionRef\030\010 " +
      "\001(\0132\035.com.yorha.proto.ActorRefData\"\035\n\rUp" +
      "dateViewAns\022\014\n\004isOk\030\001 \001(\010\" \n\014ClearViewAs" +
      "k\022\020\n\010playerId\030\001 \001(\003\"\016\n\014ClearViewAns\"S\n\016A" +
      "ddSceneObjCmd\022\r\n\005objId\030\001 \001(\003\022#\n\004aabb\030\002 \001" +
      "(\0132\025.com.yorha.proto.AABB\022\r\n\005layer\030\003 \001(\005" +
      "\"C\n\021ChangeSceneObjCmd\022\r\n\005objId\030\001 \001(\003\022\r\n\005" +
      "layer\030\002 \001(\005\022\020\n\010newLayer\030\003 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_AABB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_AABB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AABB_descriptor,
        new java.lang.String[] { "Left", "Top", "Right", "Bottom", });
    internal_static_com_yorha_proto_UpdateViewAsk_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_UpdateViewAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_UpdateViewAsk_descriptor,
        new java.lang.String[] { "PlayerId", "P1", "P2", "P3", "P4", "LayerId", "SceneZoneId", "SessionRef", });
    internal_static_com_yorha_proto_UpdateViewAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_UpdateViewAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_UpdateViewAns_descriptor,
        new java.lang.String[] { "IsOk", });
    internal_static_com_yorha_proto_ClearViewAsk_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_ClearViewAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClearViewAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_ClearViewAns_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_ClearViewAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClearViewAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_AddSceneObjCmd_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_AddSceneObjCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AddSceneObjCmd_descriptor,
        new java.lang.String[] { "ObjId", "Aabb", "Layer", });
    internal_static_com_yorha_proto_ChangeSceneObjCmd_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_ChangeSceneObjCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ChangeSceneObjCmd_descriptor,
        new java.lang.String[] { "ObjId", "Layer", "NewLayer", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

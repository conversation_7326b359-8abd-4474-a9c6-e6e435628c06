// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/translator/ss_translator.proto

package com.yorha.proto;

public final class SsTranslator {
  private SsTranslator() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TranslateTextAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TranslateTextAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 翻译的目标语言
     * </pre>
     *
     * <code>optional string targetLanguage = 1;</code>
     * @return Whether the targetLanguage field is set.
     */
    boolean hasTargetLanguage();
    /**
     * <pre>
     * 翻译的目标语言
     * </pre>
     *
     * <code>optional string targetLanguage = 1;</code>
     * @return The targetLanguage.
     */
    java.lang.String getTargetLanguage();
    /**
     * <pre>
     * 翻译的目标语言
     * </pre>
     *
     * <code>optional string targetLanguage = 1;</code>
     * @return The bytes for targetLanguage.
     */
    com.google.protobuf.ByteString
        getTargetLanguageBytes();

    /**
     * <pre>
     * 待翻译字符串
     * </pre>
     *
     * <code>optional string text = 2;</code>
     * @return Whether the text field is set.
     */
    boolean hasText();
    /**
     * <pre>
     * 待翻译字符串
     * </pre>
     *
     * <code>optional string text = 2;</code>
     * @return The text.
     */
    java.lang.String getText();
    /**
     * <pre>
     * 待翻译字符串
     * </pre>
     *
     * <code>optional string text = 2;</code>
     * @return The bytes for text.
     */
    com.google.protobuf.ByteString
        getTextBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.TranslateTextAsk}
   */
  public static final class TranslateTextAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TranslateTextAsk)
      TranslateTextAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TranslateTextAsk.newBuilder() to construct.
    private TranslateTextAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TranslateTextAsk() {
      targetLanguage_ = "";
      text_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TranslateTextAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TranslateTextAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              targetLanguage_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              text_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsTranslator.TranslateTextAsk.class, com.yorha.proto.SsTranslator.TranslateTextAsk.Builder.class);
    }

    private int bitField0_;
    public static final int TARGETLANGUAGE_FIELD_NUMBER = 1;
    private volatile java.lang.Object targetLanguage_;
    /**
     * <pre>
     * 翻译的目标语言
     * </pre>
     *
     * <code>optional string targetLanguage = 1;</code>
     * @return Whether the targetLanguage field is set.
     */
    @java.lang.Override
    public boolean hasTargetLanguage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 翻译的目标语言
     * </pre>
     *
     * <code>optional string targetLanguage = 1;</code>
     * @return The targetLanguage.
     */
    @java.lang.Override
    public java.lang.String getTargetLanguage() {
      java.lang.Object ref = targetLanguage_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          targetLanguage_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 翻译的目标语言
     * </pre>
     *
     * <code>optional string targetLanguage = 1;</code>
     * @return The bytes for targetLanguage.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTargetLanguageBytes() {
      java.lang.Object ref = targetLanguage_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        targetLanguage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TEXT_FIELD_NUMBER = 2;
    private volatile java.lang.Object text_;
    /**
     * <pre>
     * 待翻译字符串
     * </pre>
     *
     * <code>optional string text = 2;</code>
     * @return Whether the text field is set.
     */
    @java.lang.Override
    public boolean hasText() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 待翻译字符串
     * </pre>
     *
     * <code>optional string text = 2;</code>
     * @return The text.
     */
    @java.lang.Override
    public java.lang.String getText() {
      java.lang.Object ref = text_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          text_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 待翻译字符串
     * </pre>
     *
     * <code>optional string text = 2;</code>
     * @return The bytes for text.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTextBytes() {
      java.lang.Object ref = text_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        text_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, targetLanguage_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, text_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, targetLanguage_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, text_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsTranslator.TranslateTextAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsTranslator.TranslateTextAsk other = (com.yorha.proto.SsTranslator.TranslateTextAsk) obj;

      if (hasTargetLanguage() != other.hasTargetLanguage()) return false;
      if (hasTargetLanguage()) {
        if (!getTargetLanguage()
            .equals(other.getTargetLanguage())) return false;
      }
      if (hasText() != other.hasText()) return false;
      if (hasText()) {
        if (!getText()
            .equals(other.getText())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTargetLanguage()) {
        hash = (37 * hash) + TARGETLANGUAGE_FIELD_NUMBER;
        hash = (53 * hash) + getTargetLanguage().hashCode();
      }
      if (hasText()) {
        hash = (37 * hash) + TEXT_FIELD_NUMBER;
        hash = (53 * hash) + getText().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsTranslator.TranslateTextAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TranslateTextAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TranslateTextAsk)
        com.yorha.proto.SsTranslator.TranslateTextAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsTranslator.TranslateTextAsk.class, com.yorha.proto.SsTranslator.TranslateTextAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsTranslator.TranslateTextAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        targetLanguage_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        text_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsTranslator.TranslateTextAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsTranslator.TranslateTextAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsTranslator.TranslateTextAsk build() {
        com.yorha.proto.SsTranslator.TranslateTextAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsTranslator.TranslateTextAsk buildPartial() {
        com.yorha.proto.SsTranslator.TranslateTextAsk result = new com.yorha.proto.SsTranslator.TranslateTextAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.targetLanguage_ = targetLanguage_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.text_ = text_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsTranslator.TranslateTextAsk) {
          return mergeFrom((com.yorha.proto.SsTranslator.TranslateTextAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsTranslator.TranslateTextAsk other) {
        if (other == com.yorha.proto.SsTranslator.TranslateTextAsk.getDefaultInstance()) return this;
        if (other.hasTargetLanguage()) {
          bitField0_ |= 0x00000001;
          targetLanguage_ = other.targetLanguage_;
          onChanged();
        }
        if (other.hasText()) {
          bitField0_ |= 0x00000002;
          text_ = other.text_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsTranslator.TranslateTextAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsTranslator.TranslateTextAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object targetLanguage_ = "";
      /**
       * <pre>
       * 翻译的目标语言
       * </pre>
       *
       * <code>optional string targetLanguage = 1;</code>
       * @return Whether the targetLanguage field is set.
       */
      public boolean hasTargetLanguage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 翻译的目标语言
       * </pre>
       *
       * <code>optional string targetLanguage = 1;</code>
       * @return The targetLanguage.
       */
      public java.lang.String getTargetLanguage() {
        java.lang.Object ref = targetLanguage_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            targetLanguage_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 翻译的目标语言
       * </pre>
       *
       * <code>optional string targetLanguage = 1;</code>
       * @return The bytes for targetLanguage.
       */
      public com.google.protobuf.ByteString
          getTargetLanguageBytes() {
        java.lang.Object ref = targetLanguage_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          targetLanguage_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 翻译的目标语言
       * </pre>
       *
       * <code>optional string targetLanguage = 1;</code>
       * @param value The targetLanguage to set.
       * @return This builder for chaining.
       */
      public Builder setTargetLanguage(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        targetLanguage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 翻译的目标语言
       * </pre>
       *
       * <code>optional string targetLanguage = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetLanguage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetLanguage_ = getDefaultInstance().getTargetLanguage();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 翻译的目标语言
       * </pre>
       *
       * <code>optional string targetLanguage = 1;</code>
       * @param value The bytes for targetLanguage to set.
       * @return This builder for chaining.
       */
      public Builder setTargetLanguageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        targetLanguage_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object text_ = "";
      /**
       * <pre>
       * 待翻译字符串
       * </pre>
       *
       * <code>optional string text = 2;</code>
       * @return Whether the text field is set.
       */
      public boolean hasText() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 待翻译字符串
       * </pre>
       *
       * <code>optional string text = 2;</code>
       * @return The text.
       */
      public java.lang.String getText() {
        java.lang.Object ref = text_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            text_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 待翻译字符串
       * </pre>
       *
       * <code>optional string text = 2;</code>
       * @return The bytes for text.
       */
      public com.google.protobuf.ByteString
          getTextBytes() {
        java.lang.Object ref = text_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          text_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 待翻译字符串
       * </pre>
       *
       * <code>optional string text = 2;</code>
       * @param value The text to set.
       * @return This builder for chaining.
       */
      public Builder setText(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        text_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 待翻译字符串
       * </pre>
       *
       * <code>optional string text = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearText() {
        bitField0_ = (bitField0_ & ~0x00000002);
        text_ = getDefaultInstance().getText();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 待翻译字符串
       * </pre>
       *
       * <code>optional string text = 2;</code>
       * @param value The bytes for text to set.
       * @return This builder for chaining.
       */
      public Builder setTextBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        text_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TranslateTextAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TranslateTextAsk)
    private static final com.yorha.proto.SsTranslator.TranslateTextAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsTranslator.TranslateTextAsk();
    }

    public static com.yorha.proto.SsTranslator.TranslateTextAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TranslateTextAsk>
        PARSER = new com.google.protobuf.AbstractParser<TranslateTextAsk>() {
      @java.lang.Override
      public TranslateTextAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TranslateTextAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TranslateTextAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TranslateTextAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsTranslator.TranslateTextAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TranslateTextAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.TranslateTextAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 翻译后的内容
     * </pre>
     *
     * <code>optional string text = 1;</code>
     * @return Whether the text field is set.
     */
    boolean hasText();
    /**
     * <pre>
     * 翻译后的内容
     * </pre>
     *
     * <code>optional string text = 1;</code>
     * @return The text.
     */
    java.lang.String getText();
    /**
     * <pre>
     * 翻译后的内容
     * </pre>
     *
     * <code>optional string text = 1;</code>
     * @return The bytes for text.
     */
    com.google.protobuf.ByteString
        getTextBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.TranslateTextAns}
   */
  public static final class TranslateTextAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.TranslateTextAns)
      TranslateTextAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TranslateTextAns.newBuilder() to construct.
    private TranslateTextAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TranslateTextAns() {
      text_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TranslateTextAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TranslateTextAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              text_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsTranslator.TranslateTextAns.class, com.yorha.proto.SsTranslator.TranslateTextAns.Builder.class);
    }

    private int bitField0_;
    public static final int TEXT_FIELD_NUMBER = 1;
    private volatile java.lang.Object text_;
    /**
     * <pre>
     * 翻译后的内容
     * </pre>
     *
     * <code>optional string text = 1;</code>
     * @return Whether the text field is set.
     */
    @java.lang.Override
    public boolean hasText() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 翻译后的内容
     * </pre>
     *
     * <code>optional string text = 1;</code>
     * @return The text.
     */
    @java.lang.Override
    public java.lang.String getText() {
      java.lang.Object ref = text_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          text_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 翻译后的内容
     * </pre>
     *
     * <code>optional string text = 1;</code>
     * @return The bytes for text.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTextBytes() {
      java.lang.Object ref = text_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        text_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, text_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, text_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsTranslator.TranslateTextAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsTranslator.TranslateTextAns other = (com.yorha.proto.SsTranslator.TranslateTextAns) obj;

      if (hasText() != other.hasText()) return false;
      if (hasText()) {
        if (!getText()
            .equals(other.getText())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasText()) {
        hash = (37 * hash) + TEXT_FIELD_NUMBER;
        hash = (53 * hash) + getText().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTranslator.TranslateTextAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsTranslator.TranslateTextAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.TranslateTextAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.TranslateTextAns)
        com.yorha.proto.SsTranslator.TranslateTextAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsTranslator.TranslateTextAns.class, com.yorha.proto.SsTranslator.TranslateTextAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsTranslator.TranslateTextAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        text_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsTranslator.internal_static_com_yorha_proto_TranslateTextAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsTranslator.TranslateTextAns getDefaultInstanceForType() {
        return com.yorha.proto.SsTranslator.TranslateTextAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsTranslator.TranslateTextAns build() {
        com.yorha.proto.SsTranslator.TranslateTextAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsTranslator.TranslateTextAns buildPartial() {
        com.yorha.proto.SsTranslator.TranslateTextAns result = new com.yorha.proto.SsTranslator.TranslateTextAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.text_ = text_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsTranslator.TranslateTextAns) {
          return mergeFrom((com.yorha.proto.SsTranslator.TranslateTextAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsTranslator.TranslateTextAns other) {
        if (other == com.yorha.proto.SsTranslator.TranslateTextAns.getDefaultInstance()) return this;
        if (other.hasText()) {
          bitField0_ |= 0x00000001;
          text_ = other.text_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsTranslator.TranslateTextAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsTranslator.TranslateTextAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object text_ = "";
      /**
       * <pre>
       * 翻译后的内容
       * </pre>
       *
       * <code>optional string text = 1;</code>
       * @return Whether the text field is set.
       */
      public boolean hasText() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 翻译后的内容
       * </pre>
       *
       * <code>optional string text = 1;</code>
       * @return The text.
       */
      public java.lang.String getText() {
        java.lang.Object ref = text_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            text_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 翻译后的内容
       * </pre>
       *
       * <code>optional string text = 1;</code>
       * @return The bytes for text.
       */
      public com.google.protobuf.ByteString
          getTextBytes() {
        java.lang.Object ref = text_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          text_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 翻译后的内容
       * </pre>
       *
       * <code>optional string text = 1;</code>
       * @param value The text to set.
       * @return This builder for chaining.
       */
      public Builder setText(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        text_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 翻译后的内容
       * </pre>
       *
       * <code>optional string text = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearText() {
        bitField0_ = (bitField0_ & ~0x00000001);
        text_ = getDefaultInstance().getText();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 翻译后的内容
       * </pre>
       *
       * <code>optional string text = 1;</code>
       * @param value The bytes for text to set.
       * @return This builder for chaining.
       */
      public Builder setTextBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        text_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.TranslateTextAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.TranslateTextAns)
    private static final com.yorha.proto.SsTranslator.TranslateTextAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsTranslator.TranslateTextAns();
    }

    public static com.yorha.proto.SsTranslator.TranslateTextAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TranslateTextAns>
        PARSER = new com.google.protobuf.AbstractParser<TranslateTextAns>() {
      @java.lang.Override
      public TranslateTextAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TranslateTextAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TranslateTextAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TranslateTextAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsTranslator.TranslateTextAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TranslateTextAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TranslateTextAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_TranslateTextAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_TranslateTextAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/translator/ss_translator." +
      "proto\022\017com.yorha.proto\"8\n\020TranslateTextA" +
      "sk\022\026\n\016targetLanguage\030\001 \001(\t\022\014\n\004text\030\002 \001(\t" +
      "\" \n\020TranslateTextAns\022\014\n\004text\030\001 \001(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_TranslateTextAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_TranslateTextAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TranslateTextAsk_descriptor,
        new java.lang.String[] { "TargetLanguage", "Text", });
    internal_static_com_yorha_proto_TranslateTextAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_TranslateTextAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_TranslateTextAns_descriptor,
        new java.lang.String[] { "Text", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

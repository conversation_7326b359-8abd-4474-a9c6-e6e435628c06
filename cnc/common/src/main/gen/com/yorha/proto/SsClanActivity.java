// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_activity.proto

package com.yorha.proto;

public final class SsClanActivity {
  private SsClanActivity() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface AddClanActScoreCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.AddClanActScoreCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 活动类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <pre>
     * 活动类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.ActivityUnitType getType();

    /**
     * <pre>
     * 32位活动id拼上32位开始时间戳（秒
     * </pre>
     *
     * <code>optional int64 curActId = 2;</code>
     * @return Whether the curActId field is set.
     */
    boolean hasCurActId();
    /**
     * <pre>
     * 32位活动id拼上32位开始时间戳（秒
     * </pre>
     *
     * <code>optional int64 curActId = 2;</code>
     * @return The curActId.
     */
    long getCurActId();

    /**
     * <pre>
     * 增加的积分
     * </pre>
     *
     * <code>optional int32 addScore = 3;</code>
     * @return Whether the addScore field is set.
     */
    boolean hasAddScore();
    /**
     * <pre>
     * 增加的积分
     * </pre>
     *
     * <code>optional int32 addScore = 3;</code>
     * @return The addScore.
     */
    int getAddScore();
  }
  /**
   * Protobuf type {@code com.yorha.proto.AddClanActScoreCmd}
   */
  public static final class AddClanActScoreCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.AddClanActScoreCmd)
      AddClanActScoreCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AddClanActScoreCmd.newBuilder() to construct.
    private AddClanActScoreCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AddClanActScoreCmd() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new AddClanActScoreCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AddClanActScoreCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ActivityUnitType value = com.yorha.proto.CommonEnum.ActivityUnitType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              curActId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              addScore_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_AddClanActScoreCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_AddClanActScoreCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanActivity.AddClanActScoreCmd.class, com.yorha.proto.SsClanActivity.AddClanActScoreCmd.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     * 活动类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 活动类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ActivityUnitType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ActivityUnitType result = com.yorha.proto.CommonEnum.ActivityUnitType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.ActivityUnitType.AUT_NONE : result;
    }

    public static final int CURACTID_FIELD_NUMBER = 2;
    private long curActId_;
    /**
     * <pre>
     * 32位活动id拼上32位开始时间戳（秒
     * </pre>
     *
     * <code>optional int64 curActId = 2;</code>
     * @return Whether the curActId field is set.
     */
    @java.lang.Override
    public boolean hasCurActId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 32位活动id拼上32位开始时间戳（秒
     * </pre>
     *
     * <code>optional int64 curActId = 2;</code>
     * @return The curActId.
     */
    @java.lang.Override
    public long getCurActId() {
      return curActId_;
    }

    public static final int ADDSCORE_FIELD_NUMBER = 3;
    private int addScore_;
    /**
     * <pre>
     * 增加的积分
     * </pre>
     *
     * <code>optional int32 addScore = 3;</code>
     * @return Whether the addScore field is set.
     */
    @java.lang.Override
    public boolean hasAddScore() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 增加的积分
     * </pre>
     *
     * <code>optional int32 addScore = 3;</code>
     * @return The addScore.
     */
    @java.lang.Override
    public int getAddScore() {
      return addScore_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, curActId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, addScore_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, curActId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, addScore_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanActivity.AddClanActScoreCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanActivity.AddClanActScoreCmd other = (com.yorha.proto.SsClanActivity.AddClanActScoreCmd) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasCurActId() != other.hasCurActId()) return false;
      if (hasCurActId()) {
        if (getCurActId()
            != other.getCurActId()) return false;
      }
      if (hasAddScore() != other.hasAddScore()) return false;
      if (hasAddScore()) {
        if (getAddScore()
            != other.getAddScore()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasCurActId()) {
        hash = (37 * hash) + CURACTID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCurActId());
      }
      if (hasAddScore()) {
        hash = (37 * hash) + ADDSCORE_FIELD_NUMBER;
        hash = (53 * hash) + getAddScore();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanActivity.AddClanActScoreCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.AddClanActScoreCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.AddClanActScoreCmd)
        com.yorha.proto.SsClanActivity.AddClanActScoreCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_AddClanActScoreCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_AddClanActScoreCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanActivity.AddClanActScoreCmd.class, com.yorha.proto.SsClanActivity.AddClanActScoreCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanActivity.AddClanActScoreCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        curActId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        addScore_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_AddClanActScoreCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.AddClanActScoreCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanActivity.AddClanActScoreCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.AddClanActScoreCmd build() {
        com.yorha.proto.SsClanActivity.AddClanActScoreCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.AddClanActScoreCmd buildPartial() {
        com.yorha.proto.SsClanActivity.AddClanActScoreCmd result = new com.yorha.proto.SsClanActivity.AddClanActScoreCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.curActId_ = curActId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.addScore_ = addScore_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanActivity.AddClanActScoreCmd) {
          return mergeFrom((com.yorha.proto.SsClanActivity.AddClanActScoreCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanActivity.AddClanActScoreCmd other) {
        if (other == com.yorha.proto.SsClanActivity.AddClanActScoreCmd.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasCurActId()) {
          setCurActId(other.getCurActId());
        }
        if (other.hasAddScore()) {
          setAddScore(other.getAddScore());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanActivity.AddClanActScoreCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanActivity.AddClanActScoreCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       * 活动类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 活动类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ActivityUnitType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ActivityUnitType result = com.yorha.proto.CommonEnum.ActivityUnitType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.ActivityUnitType.AUT_NONE : result;
      }
      /**
       * <pre>
       * 活动类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.ActivityUnitType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活动类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private long curActId_ ;
      /**
       * <pre>
       * 32位活动id拼上32位开始时间戳（秒
       * </pre>
       *
       * <code>optional int64 curActId = 2;</code>
       * @return Whether the curActId field is set.
       */
      @java.lang.Override
      public boolean hasCurActId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 32位活动id拼上32位开始时间戳（秒
       * </pre>
       *
       * <code>optional int64 curActId = 2;</code>
       * @return The curActId.
       */
      @java.lang.Override
      public long getCurActId() {
        return curActId_;
      }
      /**
       * <pre>
       * 32位活动id拼上32位开始时间戳（秒
       * </pre>
       *
       * <code>optional int64 curActId = 2;</code>
       * @param value The curActId to set.
       * @return This builder for chaining.
       */
      public Builder setCurActId(long value) {
        bitField0_ |= 0x00000002;
        curActId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 32位活动id拼上32位开始时间戳（秒
       * </pre>
       *
       * <code>optional int64 curActId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurActId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        curActId_ = 0L;
        onChanged();
        return this;
      }

      private int addScore_ ;
      /**
       * <pre>
       * 增加的积分
       * </pre>
       *
       * <code>optional int32 addScore = 3;</code>
       * @return Whether the addScore field is set.
       */
      @java.lang.Override
      public boolean hasAddScore() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 增加的积分
       * </pre>
       *
       * <code>optional int32 addScore = 3;</code>
       * @return The addScore.
       */
      @java.lang.Override
      public int getAddScore() {
        return addScore_;
      }
      /**
       * <pre>
       * 增加的积分
       * </pre>
       *
       * <code>optional int32 addScore = 3;</code>
       * @param value The addScore to set.
       * @return This builder for chaining.
       */
      public Builder setAddScore(int value) {
        bitField0_ |= 0x00000004;
        addScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 增加的积分
       * </pre>
       *
       * <code>optional int32 addScore = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddScore() {
        bitField0_ = (bitField0_ & ~0x00000004);
        addScore_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.AddClanActScoreCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.AddClanActScoreCmd)
    private static final com.yorha.proto.SsClanActivity.AddClanActScoreCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanActivity.AddClanActScoreCmd();
    }

    public static com.yorha.proto.SsClanActivity.AddClanActScoreCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<AddClanActScoreCmd>
        PARSER = new com.google.protobuf.AbstractParser<AddClanActScoreCmd>() {
      @java.lang.Override
      public AddClanActScoreCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AddClanActScoreCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AddClanActScoreCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<AddClanActScoreCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanActivity.AddClanActScoreCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchActivityDataAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchActivityDataAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.ActivityUnitType getType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchActivityDataAsk}
   */
  public static final class FetchActivityDataAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchActivityDataAsk)
      FetchActivityDataAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchActivityDataAsk.newBuilder() to construct.
    private FetchActivityDataAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchActivityDataAsk() {
      type_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchActivityDataAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchActivityDataAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ActivityUnitType value = com.yorha.proto.CommonEnum.ActivityUnitType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanActivity.FetchActivityDataAsk.class, com.yorha.proto.SsClanActivity.FetchActivityDataAsk.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ActivityUnitType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ActivityUnitType result = com.yorha.proto.CommonEnum.ActivityUnitType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.ActivityUnitType.AUT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanActivity.FetchActivityDataAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanActivity.FetchActivityDataAsk other = (com.yorha.proto.SsClanActivity.FetchActivityDataAsk) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanActivity.FetchActivityDataAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchActivityDataAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchActivityDataAsk)
        com.yorha.proto.SsClanActivity.FetchActivityDataAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanActivity.FetchActivityDataAsk.class, com.yorha.proto.SsClanActivity.FetchActivityDataAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanActivity.FetchActivityDataAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.FetchActivityDataAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanActivity.FetchActivityDataAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.FetchActivityDataAsk build() {
        com.yorha.proto.SsClanActivity.FetchActivityDataAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.FetchActivityDataAsk buildPartial() {
        com.yorha.proto.SsClanActivity.FetchActivityDataAsk result = new com.yorha.proto.SsClanActivity.FetchActivityDataAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanActivity.FetchActivityDataAsk) {
          return mergeFrom((com.yorha.proto.SsClanActivity.FetchActivityDataAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanActivity.FetchActivityDataAsk other) {
        if (other == com.yorha.proto.SsClanActivity.FetchActivityDataAsk.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanActivity.FetchActivityDataAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanActivity.FetchActivityDataAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ActivityUnitType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ActivityUnitType result = com.yorha.proto.CommonEnum.ActivityUnitType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.ActivityUnitType.AUT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.ActivityUnitType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ActivityUnitType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchActivityDataAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchActivityDataAsk)
    private static final com.yorha.proto.SsClanActivity.FetchActivityDataAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanActivity.FetchActivityDataAsk();
    }

    public static com.yorha.proto.SsClanActivity.FetchActivityDataAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchActivityDataAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchActivityDataAsk>() {
      @java.lang.Override
      public FetchActivityDataAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchActivityDataAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchActivityDataAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchActivityDataAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanActivity.FetchActivityDataAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchActivityDataAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchActivityDataAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchActivityDataAns}
   */
  public static final class FetchActivityDataAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchActivityDataAns)
      FetchActivityDataAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchActivityDataAns.newBuilder() to construct.
    private FetchActivityDataAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchActivityDataAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchActivityDataAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchActivityDataAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanActivity.FetchActivityDataAns.class, com.yorha.proto.SsClanActivity.FetchActivityDataAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanActivity.FetchActivityDataAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanActivity.FetchActivityDataAns other = (com.yorha.proto.SsClanActivity.FetchActivityDataAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanActivity.FetchActivityDataAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchActivityDataAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchActivityDataAns)
        com.yorha.proto.SsClanActivity.FetchActivityDataAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanActivity.FetchActivityDataAns.class, com.yorha.proto.SsClanActivity.FetchActivityDataAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanActivity.FetchActivityDataAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanActivity.internal_static_com_yorha_proto_FetchActivityDataAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.FetchActivityDataAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanActivity.FetchActivityDataAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.FetchActivityDataAns build() {
        com.yorha.proto.SsClanActivity.FetchActivityDataAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanActivity.FetchActivityDataAns buildPartial() {
        com.yorha.proto.SsClanActivity.FetchActivityDataAns result = new com.yorha.proto.SsClanActivity.FetchActivityDataAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanActivity.FetchActivityDataAns) {
          return mergeFrom((com.yorha.proto.SsClanActivity.FetchActivityDataAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanActivity.FetchActivityDataAns other) {
        if (other == com.yorha.proto.SsClanActivity.FetchActivityDataAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanActivity.FetchActivityDataAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanActivity.FetchActivityDataAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchActivityDataAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchActivityDataAns)
    private static final com.yorha.proto.SsClanActivity.FetchActivityDataAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanActivity.FetchActivityDataAns();
    }

    public static com.yorha.proto.SsClanActivity.FetchActivityDataAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchActivityDataAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchActivityDataAns>() {
      @java.lang.Override
      public FetchActivityDataAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchActivityDataAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchActivityDataAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchActivityDataAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanActivity.FetchActivityDataAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_AddClanActScoreCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_AddClanActScoreCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchActivityDataAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchActivityDataAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchActivityDataAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchActivityDataAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n(ss_proto/gen/clan/ss_clan_activity.pro" +
      "to\022\017com.yorha.proto\032%ss_proto/gen/common" +
      "/common_enum.proto\"i\n\022AddClanActScoreCmd" +
      "\022/\n\004type\030\001 \001(\0162!.com.yorha.proto.Activit" +
      "yUnitType\022\020\n\010curActId\030\002 \001(\003\022\020\n\010addScore\030" +
      "\003 \001(\005\"G\n\024FetchActivityDataAsk\022/\n\004type\030\001 " +
      "\001(\0162!.com.yorha.proto.ActivityUnitType\"\026" +
      "\n\024FetchActivityDataAnsB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_AddClanActScoreCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_AddClanActScoreCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_AddClanActScoreCmd_descriptor,
        new java.lang.String[] { "Type", "CurActId", "AddScore", });
    internal_static_com_yorha_proto_FetchActivityDataAsk_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_FetchActivityDataAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchActivityDataAsk_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_com_yorha_proto_FetchActivityDataAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_FetchActivityDataAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchActivityDataAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/core/core.proto

package com.yorha.proto;

public final class Core {
  private Core() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CodeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Code)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>repeated string param = 2;</code>
     * @return A list containing the param.
     */
    java.util.List<java.lang.String>
        getParamList();
    /**
     * <code>repeated string param = 2;</code>
     * @return The count of param.
     */
    int getParamCount();
    /**
     * <code>repeated string param = 2;</code>
     * @param index The index of the element to return.
     * @return The param at the given index.
     */
    java.lang.String getParam(int index);
    /**
     * <code>repeated string param = 2;</code>
     * @param index The index of the value to return.
     * @return The bytes of the param at the given index.
     */
    com.google.protobuf.ByteString
        getParamBytes(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Code}
   */
  public static final class Code extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Code)
      CodeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Code.newBuilder() to construct.
    private Code(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Code() {
      param_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Code();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Code(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                param_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000002;
              }
              param_.add(bs);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          param_ = param_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_Code_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_Code_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.Code.class, com.yorha.proto.Core.Code.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>optional int32 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int PARAM_FIELD_NUMBER = 2;
    private com.google.protobuf.LazyStringList param_;
    /**
     * <code>repeated string param = 2;</code>
     * @return A list containing the param.
     */
    public com.google.protobuf.ProtocolStringList
        getParamList() {
      return param_;
    }
    /**
     * <code>repeated string param = 2;</code>
     * @return The count of param.
     */
    public int getParamCount() {
      return param_.size();
    }
    /**
     * <code>repeated string param = 2;</code>
     * @param index The index of the element to return.
     * @return The param at the given index.
     */
    public java.lang.String getParam(int index) {
      return param_.get(index);
    }
    /**
     * <code>repeated string param = 2;</code>
     * @param index The index of the value to return.
     * @return The bytes of the param at the given index.
     */
    public com.google.protobuf.ByteString
        getParamBytes(int index) {
      return param_.getByteString(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, id_);
      }
      for (int i = 0; i < param_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, param_.getRaw(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < param_.size(); i++) {
          dataSize += computeStringSizeNoTag(param_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getParamList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.Code)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.Code other = (com.yorha.proto.Core.Code) obj;

      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (!getParamList()
          .equals(other.getParamList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId();
      }
      if (getParamCount() > 0) {
        hash = (37 * hash) + PARAM_FIELD_NUMBER;
        hash = (53 * hash) + getParamList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.Code parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Code parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Code parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Code parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Code parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Code parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Code parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Code parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.Code parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Code parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.Code parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Code parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.Code prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Code}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Code)
        com.yorha.proto.Core.CodeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Code_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Code_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.Code.class, com.yorha.proto.Core.Code.Builder.class);
      }

      // Construct using com.yorha.proto.Core.Code.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        param_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Code_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.Code getDefaultInstanceForType() {
        return com.yorha.proto.Core.Code.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.Code build() {
        com.yorha.proto.Core.Code result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.Code buildPartial() {
        com.yorha.proto.Core.Code result = new com.yorha.proto.Core.Code(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000001;
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          param_ = param_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.param_ = param_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.Code) {
          return mergeFrom((com.yorha.proto.Core.Code)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.Code other) {
        if (other == com.yorha.proto.Core.Code.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (!other.param_.isEmpty()) {
          if (param_.isEmpty()) {
            param_ = other.param_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureParamIsMutable();
            param_.addAll(other.param_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.Code parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.Code) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>optional int32 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList param_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureParamIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          param_ = new com.google.protobuf.LazyStringArrayList(param_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated string param = 2;</code>
       * @return A list containing the param.
       */
      public com.google.protobuf.ProtocolStringList
          getParamList() {
        return param_.getUnmodifiableView();
      }
      /**
       * <code>repeated string param = 2;</code>
       * @return The count of param.
       */
      public int getParamCount() {
        return param_.size();
      }
      /**
       * <code>repeated string param = 2;</code>
       * @param index The index of the element to return.
       * @return The param at the given index.
       */
      public java.lang.String getParam(int index) {
        return param_.get(index);
      }
      /**
       * <code>repeated string param = 2;</code>
       * @param index The index of the value to return.
       * @return The bytes of the param at the given index.
       */
      public com.google.protobuf.ByteString
          getParamBytes(int index) {
        return param_.getByteString(index);
      }
      /**
       * <code>repeated string param = 2;</code>
       * @param index The index to set the value at.
       * @param value The param to set.
       * @return This builder for chaining.
       */
      public Builder setParam(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureParamIsMutable();
        param_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string param = 2;</code>
       * @param value The param to add.
       * @return This builder for chaining.
       */
      public Builder addParam(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureParamIsMutable();
        param_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string param = 2;</code>
       * @param values The param to add.
       * @return This builder for chaining.
       */
      public Builder addAllParam(
          java.lang.Iterable<java.lang.String> values) {
        ensureParamIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, param_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string param = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearParam() {
        param_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string param = 2;</code>
       * @param value The bytes of the param to add.
       * @return This builder for chaining.
       */
      public Builder addParamBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureParamIsMutable();
        param_.add(value);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Code)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Code)
    private static final com.yorha.proto.Core.Code DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.Code();
    }

    public static com.yorha.proto.Core.Code getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Code>
        PARSER = new com.google.protobuf.AbstractParser<Code>() {
      @java.lang.Override
      public Code parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Code(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Code> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Code> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.Code getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RpcMsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RpcMsg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否属于同步的消息
     * </pre>
     *
     * <code>optional bool sync = 1;</code>
     * @return Whether the sync field is set.
     */
    boolean hasSync();
    /**
     * <pre>
     * 是否属于同步的消息
     * </pre>
     *
     * <code>optional bool sync = 1;</code>
     * @return The sync.
     */
    boolean getSync();

    /**
     * <code>optional int64 seqId = 2;</code>
     * @return Whether the seqId field is set.
     */
    boolean hasSeqId();
    /**
     * <code>optional int64 seqId = 2;</code>
     * @return The seqId.
     */
    long getSeqId();

    /**
     * <pre>
     * 协议编号
     * </pre>
     *
     * <code>optional int32 msgType = 3;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <pre>
     * 协议编号
     * </pre>
     *
     * <code>optional int32 msgType = 3;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>optional int64 timestamp = 4;</code>
     * @return Whether the timestamp field is set.
     */
    boolean hasTimestamp();
    /**
     * <code>optional int64 timestamp = 4;</code>
     * @return The timestamp.
     */
    long getTimestamp();

    /**
     * <code>optional string content = 5;</code>
     * @return Whether the content field is set.
     */
    boolean hasContent();
    /**
     * <code>optional string content = 5;</code>
     * @return The content.
     */
    java.lang.String getContent();
    /**
     * <code>optional string content = 5;</code>
     * @return The bytes for content.
     */
    com.google.protobuf.ByteString
        getContentBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RpcMsg}
   */
  public static final class RpcMsg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RpcMsg)
      RpcMsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RpcMsg.newBuilder() to construct.
    private RpcMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RpcMsg() {
      content_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RpcMsg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RpcMsg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              sync_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              seqId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              msgType_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              timestamp_ = input.readInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              content_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.RpcMsg.class, com.yorha.proto.Core.RpcMsg.Builder.class);
    }

    private int bitField0_;
    public static final int SYNC_FIELD_NUMBER = 1;
    private boolean sync_;
    /**
     * <pre>
     * 是否属于同步的消息
     * </pre>
     *
     * <code>optional bool sync = 1;</code>
     * @return Whether the sync field is set.
     */
    @java.lang.Override
    public boolean hasSync() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 是否属于同步的消息
     * </pre>
     *
     * <code>optional bool sync = 1;</code>
     * @return The sync.
     */
    @java.lang.Override
    public boolean getSync() {
      return sync_;
    }

    public static final int SEQID_FIELD_NUMBER = 2;
    private long seqId_;
    /**
     * <code>optional int64 seqId = 2;</code>
     * @return Whether the seqId field is set.
     */
    @java.lang.Override
    public boolean hasSeqId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 seqId = 2;</code>
     * @return The seqId.
     */
    @java.lang.Override
    public long getSeqId() {
      return seqId_;
    }

    public static final int MSGTYPE_FIELD_NUMBER = 3;
    private int msgType_;
    /**
     * <pre>
     * 协议编号
     * </pre>
     *
     * <code>optional int32 msgType = 3;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 协议编号
     * </pre>
     *
     * <code>optional int32 msgType = 3;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 4;
    private long timestamp_;
    /**
     * <code>optional int64 timestamp = 4;</code>
     * @return Whether the timestamp field is set.
     */
    @java.lang.Override
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 timestamp = 4;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int CONTENT_FIELD_NUMBER = 5;
    private volatile java.lang.Object content_;
    /**
     * <code>optional string content = 5;</code>
     * @return Whether the content field is set.
     */
    @java.lang.Override
    public boolean hasContent() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string content = 5;</code>
     * @return The content.
     */
    @java.lang.Override
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          content_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string content = 5;</code>
     * @return The bytes for content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, sync_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, msgType_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, timestamp_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, content_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, sync_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, msgType_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, timestamp_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, content_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.RpcMsg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.RpcMsg other = (com.yorha.proto.Core.RpcMsg) obj;

      if (hasSync() != other.hasSync()) return false;
      if (hasSync()) {
        if (getSync()
            != other.getSync()) return false;
      }
      if (hasSeqId() != other.hasSeqId()) return false;
      if (hasSeqId()) {
        if (getSeqId()
            != other.getSeqId()) return false;
      }
      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (hasTimestamp() != other.hasTimestamp()) return false;
      if (hasTimestamp()) {
        if (getTimestamp()
            != other.getTimestamp()) return false;
      }
      if (hasContent() != other.hasContent()) return false;
      if (hasContent()) {
        if (!getContent()
            .equals(other.getContent())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSync()) {
        hash = (37 * hash) + SYNC_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getSync());
      }
      if (hasSeqId()) {
        hash = (37 * hash) + SEQID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSeqId());
      }
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTimestamp());
      }
      if (hasContent()) {
        hash = (37 * hash) + CONTENT_FIELD_NUMBER;
        hash = (53 * hash) + getContent().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.RpcMsg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcMsg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.RpcMsg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.RpcMsg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.RpcMsg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RpcMsg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RpcMsg)
        com.yorha.proto.Core.RpcMsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcMsg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcMsg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.RpcMsg.class, com.yorha.proto.Core.RpcMsg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.RpcMsg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        sync_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        seqId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        timestamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        content_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcMsg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.RpcMsg getDefaultInstanceForType() {
        return com.yorha.proto.Core.RpcMsg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.RpcMsg build() {
        com.yorha.proto.Core.RpcMsg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.RpcMsg buildPartial() {
        com.yorha.proto.Core.RpcMsg result = new com.yorha.proto.Core.RpcMsg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.sync_ = sync_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.seqId_ = seqId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.timestamp_ = timestamp_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.content_ = content_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.RpcMsg) {
          return mergeFrom((com.yorha.proto.Core.RpcMsg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.RpcMsg other) {
        if (other == com.yorha.proto.Core.RpcMsg.getDefaultInstance()) return this;
        if (other.hasSync()) {
          setSync(other.getSync());
        }
        if (other.hasSeqId()) {
          setSeqId(other.getSeqId());
        }
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasTimestamp()) {
          setTimestamp(other.getTimestamp());
        }
        if (other.hasContent()) {
          bitField0_ |= 0x00000010;
          content_ = other.content_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.RpcMsg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.RpcMsg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean sync_ ;
      /**
       * <pre>
       * 是否属于同步的消息
       * </pre>
       *
       * <code>optional bool sync = 1;</code>
       * @return Whether the sync field is set.
       */
      @java.lang.Override
      public boolean hasSync() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 是否属于同步的消息
       * </pre>
       *
       * <code>optional bool sync = 1;</code>
       * @return The sync.
       */
      @java.lang.Override
      public boolean getSync() {
        return sync_;
      }
      /**
       * <pre>
       * 是否属于同步的消息
       * </pre>
       *
       * <code>optional bool sync = 1;</code>
       * @param value The sync to set.
       * @return This builder for chaining.
       */
      public Builder setSync(boolean value) {
        bitField0_ |= 0x00000001;
        sync_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否属于同步的消息
       * </pre>
       *
       * <code>optional bool sync = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSync() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sync_ = false;
        onChanged();
        return this;
      }

      private long seqId_ ;
      /**
       * <code>optional int64 seqId = 2;</code>
       * @return Whether the seqId field is set.
       */
      @java.lang.Override
      public boolean hasSeqId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 seqId = 2;</code>
       * @return The seqId.
       */
      @java.lang.Override
      public long getSeqId() {
        return seqId_;
      }
      /**
       * <code>optional int64 seqId = 2;</code>
       * @param value The seqId to set.
       * @return This builder for chaining.
       */
      public Builder setSeqId(long value) {
        bitField0_ |= 0x00000002;
        seqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 seqId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeqId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        seqId_ = 0L;
        onChanged();
        return this;
      }

      private int msgType_ ;
      /**
       * <pre>
       * 协议编号
       * </pre>
       *
       * <code>optional int32 msgType = 3;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 协议编号
       * </pre>
       *
       * <code>optional int32 msgType = 3;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <pre>
       * 协议编号
       * </pre>
       *
       * <code>optional int32 msgType = 3;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000004;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 协议编号
       * </pre>
       *
       * <code>optional int32 msgType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private long timestamp_ ;
      /**
       * <code>optional int64 timestamp = 4;</code>
       * @return Whether the timestamp field is set.
       */
      @java.lang.Override
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int64 timestamp = 4;</code>
       * @return The timestamp.
       */
      @java.lang.Override
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <code>optional int64 timestamp = 4;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(long value) {
        bitField0_ |= 0x00000008;
        timestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 timestamp = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object content_ = "";
      /**
       * <code>optional string content = 5;</code>
       * @return Whether the content field is set.
       */
      public boolean hasContent() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional string content = 5;</code>
       * @return The content.
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            content_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string content = 5;</code>
       * @return The bytes for content.
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string content = 5;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        content_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string content = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        bitField0_ = (bitField0_ & ~0x00000010);
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }
      /**
       * <code>optional string content = 5;</code>
       * @param value The bytes for content to set.
       * @return This builder for chaining.
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        content_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RpcMsg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RpcMsg)
    private static final com.yorha.proto.Core.RpcMsg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.RpcMsg();
    }

    public static com.yorha.proto.Core.RpcMsg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RpcMsg>
        PARSER = new com.google.protobuf.AbstractParser<RpcMsg>() {
      @java.lang.Override
      public RpcMsg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RpcMsg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RpcMsg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RpcMsg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.RpcMsg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GameMsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GameMsg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 2;</code>
     * @return Whether the flag field is set.
     */
    boolean hasFlag();
    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 2;</code>
     * @return The flag.
     */
    int getFlag();

    /**
     * <code>optional string content = 3;</code>
     * @return Whether the content field is set.
     */
    boolean hasContent();
    /**
     * <code>optional string content = 3;</code>
     * @return The content.
     */
    java.lang.String getContent();
    /**
     * <code>optional string content = 3;</code>
     * @return The bytes for content.
     */
    com.google.protobuf.ByteString
        getContentBytes();

    /**
     * <pre>
     * 消息ID，客户端生成
     * </pre>
     *
     * <code>optional int32 id = 4;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <pre>
     * 消息ID，客户端生成
     * </pre>
     *
     * <code>optional int32 id = 4;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GameMsg}
   */
  public static final class GameMsg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GameMsg)
      GameMsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GameMsg.newBuilder() to construct.
    private GameMsg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GameMsg() {
      content_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GameMsg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GameMsg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              flag_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              content_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              id_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_GameMsg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_GameMsg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.GameMsg.class, com.yorha.proto.Core.GameMsg.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int FLAG_FIELD_NUMBER = 2;
    private int flag_;
    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 2;</code>
     * @return Whether the flag field is set.
     */
    @java.lang.Override
    public boolean hasFlag() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 2;</code>
     * @return The flag.
     */
    @java.lang.Override
    public int getFlag() {
      return flag_;
    }

    public static final int CONTENT_FIELD_NUMBER = 3;
    private volatile java.lang.Object content_;
    /**
     * <code>optional string content = 3;</code>
     * @return Whether the content field is set.
     */
    @java.lang.Override
    public boolean hasContent() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string content = 3;</code>
     * @return The content.
     */
    @java.lang.Override
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          content_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string content = 3;</code>
     * @return The bytes for content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ID_FIELD_NUMBER = 4;
    private int id_;
    /**
     * <pre>
     * 消息ID，客户端生成
     * </pre>
     *
     * <code>optional int32 id = 4;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 消息ID，客户端生成
     * </pre>
     *
     * <code>optional int32 id = 4;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, flag_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, content_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, id_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, flag_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, content_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, id_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.GameMsg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.GameMsg other = (com.yorha.proto.Core.GameMsg) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (getType()
            != other.getType()) return false;
      }
      if (hasFlag() != other.hasFlag()) return false;
      if (hasFlag()) {
        if (getFlag()
            != other.getFlag()) return false;
      }
      if (hasContent() != other.hasContent()) return false;
      if (hasContent()) {
        if (!getContent()
            .equals(other.getContent())) return false;
      }
      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType();
      }
      if (hasFlag()) {
        hash = (37 * hash) + FLAG_FIELD_NUMBER;
        hash = (53 * hash) + getFlag();
      }
      if (hasContent()) {
        hash = (37 * hash) + CONTENT_FIELD_NUMBER;
        hash = (53 * hash) + getContent().hashCode();
      }
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.GameMsg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.GameMsg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.GameMsg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.GameMsg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.GameMsg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GameMsg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GameMsg)
        com.yorha.proto.Core.GameMsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_GameMsg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_GameMsg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.GameMsg.class, com.yorha.proto.Core.GameMsg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.GameMsg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        flag_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        content_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_GameMsg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.GameMsg getDefaultInstanceForType() {
        return com.yorha.proto.Core.GameMsg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.GameMsg build() {
        com.yorha.proto.Core.GameMsg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.GameMsg buildPartial() {
        com.yorha.proto.Core.GameMsg result = new com.yorha.proto.Core.GameMsg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.flag_ = flag_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.content_ = content_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.GameMsg) {
          return mergeFrom((com.yorha.proto.Core.GameMsg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.GameMsg other) {
        if (other == com.yorha.proto.Core.GameMsg.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasFlag()) {
          setFlag(other.getFlag());
        }
        if (other.hasContent()) {
          bitField0_ |= 0x00000004;
          content_ = other.content_;
          onChanged();
        }
        if (other.hasId()) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.GameMsg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.GameMsg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>optional int32 type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int flag_ ;
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 2;</code>
       * @return Whether the flag field is set.
       */
      @java.lang.Override
      public boolean hasFlag() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 2;</code>
       * @return The flag.
       */
      @java.lang.Override
      public int getFlag() {
        return flag_;
      }
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 2;</code>
       * @param value The flag to set.
       * @return This builder for chaining.
       */
      public Builder setFlag(int value) {
        bitField0_ |= 0x00000002;
        flag_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlag() {
        bitField0_ = (bitField0_ & ~0x00000002);
        flag_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object content_ = "";
      /**
       * <code>optional string content = 3;</code>
       * @return Whether the content field is set.
       */
      public boolean hasContent() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string content = 3;</code>
       * @return The content.
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            content_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string content = 3;</code>
       * @return The bytes for content.
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string content = 3;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        content_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string content = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        bitField0_ = (bitField0_ & ~0x00000004);
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }
      /**
       * <code>optional string content = 3;</code>
       * @param value The bytes for content to set.
       * @return This builder for chaining.
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        content_ = value;
        onChanged();
        return this;
      }

      private int id_ ;
      /**
       * <pre>
       * 消息ID，客户端生成
       * </pre>
       *
       * <code>optional int32 id = 4;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 消息ID，客户端生成
       * </pre>
       *
       * <code>optional int32 id = 4;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       * 消息ID，客户端生成
       * </pre>
       *
       * <code>optional int32 id = 4;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000008;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 消息ID，客户端生成
       * </pre>
       *
       * <code>optional int32 id = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GameMsg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GameMsg)
    private static final com.yorha.proto.Core.GameMsg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.GameMsg();
    }

    public static com.yorha.proto.Core.GameMsg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GameMsg>
        PARSER = new com.google.protobuf.AbstractParser<GameMsg>() {
      @java.lang.Override
      public GameMsg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GameMsg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GameMsg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GameMsg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.GameMsg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RpcHeaderOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RpcHeader)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.RpcHeader}
   */
  public static final class RpcHeader extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RpcHeader)
      RpcHeaderOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RpcHeader.newBuilder() to construct.
    private RpcHeader(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RpcHeader() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RpcHeader();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RpcHeader(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcHeader_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcHeader_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.RpcHeader.class, com.yorha.proto.Core.RpcHeader.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.RpcHeader)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.RpcHeader other = (com.yorha.proto.Core.RpcHeader) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.RpcHeader parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcHeader parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.RpcHeader parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.RpcHeader parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.RpcHeader prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RpcHeader}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RpcHeader)
        com.yorha.proto.Core.RpcHeaderOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcHeader_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcHeader_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.RpcHeader.class, com.yorha.proto.Core.RpcHeader.Builder.class);
      }

      // Construct using com.yorha.proto.Core.RpcHeader.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_RpcHeader_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.RpcHeader getDefaultInstanceForType() {
        return com.yorha.proto.Core.RpcHeader.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.RpcHeader build() {
        com.yorha.proto.Core.RpcHeader result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.RpcHeader buildPartial() {
        com.yorha.proto.Core.RpcHeader result = new com.yorha.proto.Core.RpcHeader(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.RpcHeader) {
          return mergeFrom((com.yorha.proto.Core.RpcHeader)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.RpcHeader other) {
        if (other == com.yorha.proto.Core.RpcHeader.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.RpcHeader parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.RpcHeader) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RpcHeader)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RpcHeader)
    private static final com.yorha.proto.Core.RpcHeader DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.RpcHeader();
    }

    public static com.yorha.proto.Core.RpcHeader getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RpcHeader>
        PARSER = new com.google.protobuf.AbstractParser<RpcHeader>() {
      @java.lang.Override
      public RpcHeader parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RpcHeader(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RpcHeader> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RpcHeader> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.RpcHeader getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Disconnet_S2C_MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Disconnet_S2C_Msg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return Whether the code field is set.
     */
    boolean hasCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return The code.
     */
    com.yorha.proto.Core.Code getCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     */
    com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder();
  }
  /**
   * <pre>
   * cmd=2002|各种断线原因
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Disconnet_S2C_Msg}
   */
  public static final class Disconnet_S2C_Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Disconnet_S2C_Msg)
      Disconnet_S2C_MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Disconnet_S2C_Msg.newBuilder() to construct.
    private Disconnet_S2C_Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Disconnet_S2C_Msg() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Disconnet_S2C_Msg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Disconnet_S2C_Msg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Core.Code.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = code_.toBuilder();
              }
              code_ = input.readMessage(com.yorha.proto.Core.Code.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(code_);
                code_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_Disconnet_S2C_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_Disconnet_S2C_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.Disconnet_S2C_Msg.class, com.yorha.proto.Core.Disconnet_S2C_Msg.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private com.yorha.proto.Core.Code code_;
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return Whether the code field is set.
     */
    @java.lang.Override
    public boolean hasCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public com.yorha.proto.Core.Code getCode() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCode());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCode());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.Disconnet_S2C_Msg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.Disconnet_S2C_Msg other = (com.yorha.proto.Core.Disconnet_S2C_Msg) obj;

      if (hasCode() != other.hasCode()) return false;
      if (hasCode()) {
        if (!getCode()
            .equals(other.getCode())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCode()) {
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Disconnet_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.Disconnet_S2C_Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * cmd=2002|各种断线原因
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Disconnet_S2C_Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Disconnet_S2C_Msg)
        com.yorha.proto.Core.Disconnet_S2C_MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Disconnet_S2C_Msg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Disconnet_S2C_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.Disconnet_S2C_Msg.class, com.yorha.proto.Core.Disconnet_S2C_Msg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.Disconnet_S2C_Msg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCodeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (codeBuilder_ == null) {
          code_ = null;
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Disconnet_S2C_Msg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.Disconnet_S2C_Msg getDefaultInstanceForType() {
        return com.yorha.proto.Core.Disconnet_S2C_Msg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.Disconnet_S2C_Msg build() {
        com.yorha.proto.Core.Disconnet_S2C_Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.Disconnet_S2C_Msg buildPartial() {
        com.yorha.proto.Core.Disconnet_S2C_Msg result = new com.yorha.proto.Core.Disconnet_S2C_Msg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (codeBuilder_ == null) {
            result.code_ = code_;
          } else {
            result.code_ = codeBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.Disconnet_S2C_Msg) {
          return mergeFrom((com.yorha.proto.Core.Disconnet_S2C_Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.Disconnet_S2C_Msg other) {
        if (other == com.yorha.proto.Core.Disconnet_S2C_Msg.getDefaultInstance()) return this;
        if (other.hasCode()) {
          mergeCode(other.getCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.Disconnet_S2C_Msg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.Disconnet_S2C_Msg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Core.Code code_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> codeBuilder_;
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       * @return Whether the code field is set.
       */
      public boolean hasCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       * @return The code.
       */
      public com.yorha.proto.Core.Code getCode() {
        if (codeBuilder_ == null) {
          return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        } else {
          return codeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder setCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          code_ = value;
          onChanged();
        } else {
          codeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder setCode(
          com.yorha.proto.Core.Code.Builder builderForValue) {
        if (codeBuilder_ == null) {
          code_ = builderForValue.build();
          onChanged();
        } else {
          codeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder mergeCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              code_ != null &&
              code_ != com.yorha.proto.Core.Code.getDefaultInstance()) {
            code_ =
              com.yorha.proto.Core.Code.newBuilder(code_).mergeFrom(value).buildPartial();
          } else {
            code_ = value;
          }
          onChanged();
        } else {
          codeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder clearCode() {
        if (codeBuilder_ == null) {
          code_ = null;
          onChanged();
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public com.yorha.proto.Core.Code.Builder getCodeBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCodeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
        if (codeBuilder_ != null) {
          return codeBuilder_.getMessageOrBuilder();
        } else {
          return code_ == null ?
              com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> 
          getCodeFieldBuilder() {
        if (codeBuilder_ == null) {
          codeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder>(
                  getCode(),
                  getParentForChildren(),
                  isClean());
          code_ = null;
        }
        return codeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Disconnet_S2C_Msg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Disconnet_S2C_Msg)
    private static final com.yorha.proto.Core.Disconnet_S2C_Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.Disconnet_S2C_Msg();
    }

    public static com.yorha.proto.Core.Disconnet_S2C_Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Disconnet_S2C_Msg>
        PARSER = new com.google.protobuf.AbstractParser<Disconnet_S2C_Msg>() {
      @java.lang.Override
      public Disconnet_S2C_Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Disconnet_S2C_Msg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Disconnet_S2C_Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Disconnet_S2C_Msg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.Disconnet_S2C_Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Warn_S2C_MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Warn_S2C_Msg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Code Code = 1;</code>
     * @return Whether the code field is set.
     */
    boolean hasCode();
    /**
     * <code>optional .com.yorha.proto.Code Code = 1;</code>
     * @return The code.
     */
    com.yorha.proto.Core.Code getCode();
    /**
     * <code>optional .com.yorha.proto.Code Code = 1;</code>
     */
    com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder();
  }
  /**
   * <pre>
   * cmd=2003|警告框
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Warn_S2C_Msg}
   */
  public static final class Warn_S2C_Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Warn_S2C_Msg)
      Warn_S2C_MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Warn_S2C_Msg.newBuilder() to construct.
    private Warn_S2C_Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Warn_S2C_Msg() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Warn_S2C_Msg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Warn_S2C_Msg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Core.Code.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = code_.toBuilder();
              }
              code_ = input.readMessage(com.yorha.proto.Core.Code.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(code_);
                code_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_Warn_S2C_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_Warn_S2C_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.Warn_S2C_Msg.class, com.yorha.proto.Core.Warn_S2C_Msg.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private com.yorha.proto.Core.Code code_;
    /**
     * <code>optional .com.yorha.proto.Code Code = 1;</code>
     * @return Whether the code field is set.
     */
    @java.lang.Override
    public boolean hasCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Code Code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public com.yorha.proto.Core.Code getCode() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }
    /**
     * <code>optional .com.yorha.proto.Code Code = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCode());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCode());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.Warn_S2C_Msg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.Warn_S2C_Msg other = (com.yorha.proto.Core.Warn_S2C_Msg) obj;

      if (hasCode() != other.hasCode()) return false;
      if (hasCode()) {
        if (!getCode()
            .equals(other.getCode())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCode()) {
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.Warn_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.Warn_S2C_Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * cmd=2003|警告框
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Warn_S2C_Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Warn_S2C_Msg)
        com.yorha.proto.Core.Warn_S2C_MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Warn_S2C_Msg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Warn_S2C_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.Warn_S2C_Msg.class, com.yorha.proto.Core.Warn_S2C_Msg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.Warn_S2C_Msg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCodeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (codeBuilder_ == null) {
          code_ = null;
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_Warn_S2C_Msg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.Warn_S2C_Msg getDefaultInstanceForType() {
        return com.yorha.proto.Core.Warn_S2C_Msg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.Warn_S2C_Msg build() {
        com.yorha.proto.Core.Warn_S2C_Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.Warn_S2C_Msg buildPartial() {
        com.yorha.proto.Core.Warn_S2C_Msg result = new com.yorha.proto.Core.Warn_S2C_Msg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (codeBuilder_ == null) {
            result.code_ = code_;
          } else {
            result.code_ = codeBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.Warn_S2C_Msg) {
          return mergeFrom((com.yorha.proto.Core.Warn_S2C_Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.Warn_S2C_Msg other) {
        if (other == com.yorha.proto.Core.Warn_S2C_Msg.getDefaultInstance()) return this;
        if (other.hasCode()) {
          mergeCode(other.getCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.Warn_S2C_Msg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.Warn_S2C_Msg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Core.Code code_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> codeBuilder_;
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       * @return Whether the code field is set.
       */
      public boolean hasCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       * @return The code.
       */
      public com.yorha.proto.Core.Code getCode() {
        if (codeBuilder_ == null) {
          return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        } else {
          return codeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       */
      public Builder setCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          code_ = value;
          onChanged();
        } else {
          codeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       */
      public Builder setCode(
          com.yorha.proto.Core.Code.Builder builderForValue) {
        if (codeBuilder_ == null) {
          code_ = builderForValue.build();
          onChanged();
        } else {
          codeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       */
      public Builder mergeCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              code_ != null &&
              code_ != com.yorha.proto.Core.Code.getDefaultInstance()) {
            code_ =
              com.yorha.proto.Core.Code.newBuilder(code_).mergeFrom(value).buildPartial();
          } else {
            code_ = value;
          }
          onChanged();
        } else {
          codeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       */
      public Builder clearCode() {
        if (codeBuilder_ == null) {
          code_ = null;
          onChanged();
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       */
      public com.yorha.proto.Core.Code.Builder getCodeBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCodeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       */
      public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
        if (codeBuilder_ != null) {
          return codeBuilder_.getMessageOrBuilder();
        } else {
          return code_ == null ?
              com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code Code = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> 
          getCodeFieldBuilder() {
        if (codeBuilder_ == null) {
          codeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder>(
                  getCode(),
                  getParentForChildren(),
                  isClean());
          code_ = null;
        }
        return codeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Warn_S2C_Msg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Warn_S2C_Msg)
    private static final com.yorha.proto.Core.Warn_S2C_Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.Warn_S2C_Msg();
    }

    public static com.yorha.proto.Core.Warn_S2C_Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Warn_S2C_Msg>
        PARSER = new com.google.protobuf.AbstractParser<Warn_S2C_Msg>() {
      @java.lang.Override
      public Warn_S2C_Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Warn_S2C_Msg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Warn_S2C_Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Warn_S2C_Msg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.Warn_S2C_Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LogLoading_C2S_MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LogLoading_C2S_Msg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string pid = 1;</code>
     * @return Whether the pid field is set.
     */
    boolean hasPid();
    /**
     * <code>optional string pid = 1;</code>
     * @return The pid.
     */
    java.lang.String getPid();
    /**
     * <code>optional string pid = 1;</code>
     * @return The bytes for pid.
     */
    com.google.protobuf.ByteString
        getPidBytes();

    /**
     * <code>optional string userId = 2;</code>
     * @return Whether the userId field is set.
     */
    boolean hasUserId();
    /**
     * <code>optional string userId = 2;</code>
     * @return The userId.
     */
    java.lang.String getUserId();
    /**
     * <code>optional string userId = 2;</code>
     * @return The bytes for userId.
     */
    com.google.protobuf.ByteString
        getUserIdBytes();

    /**
     * <code>optional string userName = 3;</code>
     * @return Whether the userName field is set.
     */
    boolean hasUserName();
    /**
     * <code>optional string userName = 3;</code>
     * @return The userName.
     */
    java.lang.String getUserName();
    /**
     * <code>optional string userName = 3;</code>
     * @return The bytes for userName.
     */
    com.google.protobuf.ByteString
        getUserNameBytes();

    /**
     * <code>optional int32 interval = 4;</code>
     * @return Whether the interval field is set.
     */
    boolean hasInterval();
    /**
     * <code>optional int32 interval = 4;</code>
     * @return The interval.
     */
    int getInterval();

    /**
     * <code>optional string browserType = 5;</code>
     * @return Whether the browserType field is set.
     */
    boolean hasBrowserType();
    /**
     * <code>optional string browserType = 5;</code>
     * @return The browserType.
     */
    java.lang.String getBrowserType();
    /**
     * <code>optional string browserType = 5;</code>
     * @return The bytes for browserType.
     */
    com.google.protobuf.ByteString
        getBrowserTypeBytes();

    /**
     * <code>optional string h5Version = 6;</code>
     * @return Whether the h5Version field is set.
     */
    boolean hasH5Version();
    /**
     * <code>optional string h5Version = 6;</code>
     * @return The h5Version.
     */
    java.lang.String getH5Version();
    /**
     * <code>optional string h5Version = 6;</code>
     * @return The bytes for h5Version.
     */
    com.google.protobuf.ByteString
        getH5VersionBytes();

    /**
     * <code>optional string os = 7;</code>
     * @return Whether the os field is set.
     */
    boolean hasOs();
    /**
     * <code>optional string os = 7;</code>
     * @return The os.
     */
    java.lang.String getOs();
    /**
     * <code>optional string os = 7;</code>
     * @return The bytes for os.
     */
    com.google.protobuf.ByteString
        getOsBytes();

    /**
     * <code>optional string resolution = 8;</code>
     * @return Whether the resolution field is set.
     */
    boolean hasResolution();
    /**
     * <code>optional string resolution = 8;</code>
     * @return The resolution.
     */
    java.lang.String getResolution();
    /**
     * <code>optional string resolution = 8;</code>
     * @return The bytes for resolution.
     */
    com.google.protobuf.ByteString
        getResolutionBytes();

    /**
     * <code>optional string browserResolution = 9;</code>
     * @return Whether the browserResolution field is set.
     */
    boolean hasBrowserResolution();
    /**
     * <code>optional string browserResolution = 9;</code>
     * @return The browserResolution.
     */
    java.lang.String getBrowserResolution();
    /**
     * <code>optional string browserResolution = 9;</code>
     * @return The bytes for browserResolution.
     */
    com.google.protobuf.ByteString
        getBrowserResolutionBytes();
  }
  /**
   * <pre>
   * cmd=1005|记录登录耗时等消息
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.LogLoading_C2S_Msg}
   */
  public static final class LogLoading_C2S_Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LogLoading_C2S_Msg)
      LogLoading_C2S_MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LogLoading_C2S_Msg.newBuilder() to construct.
    private LogLoading_C2S_Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LogLoading_C2S_Msg() {
      pid_ = "";
      userId_ = "";
      userName_ = "";
      browserType_ = "";
      h5Version_ = "";
      os_ = "";
      resolution_ = "";
      browserResolution_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LogLoading_C2S_Msg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LogLoading_C2S_Msg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              pid_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              userId_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              userName_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              interval_ = input.readInt32();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              browserType_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              h5Version_ = bs;
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              os_ = bs;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              resolution_ = bs;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              browserResolution_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_C2S_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_C2S_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.LogLoading_C2S_Msg.class, com.yorha.proto.Core.LogLoading_C2S_Msg.Builder.class);
    }

    private int bitField0_;
    public static final int PID_FIELD_NUMBER = 1;
    private volatile java.lang.Object pid_;
    /**
     * <code>optional string pid = 1;</code>
     * @return Whether the pid field is set.
     */
    @java.lang.Override
    public boolean hasPid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string pid = 1;</code>
     * @return The pid.
     */
    @java.lang.Override
    public java.lang.String getPid() {
      java.lang.Object ref = pid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pid_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string pid = 1;</code>
     * @return The bytes for pid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPidBytes() {
      java.lang.Object ref = pid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private volatile java.lang.Object userId_;
    /**
     * <code>optional string userId = 2;</code>
     * @return Whether the userId field is set.
     */
    @java.lang.Override
    public boolean hasUserId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userId = 2;</code>
     * @return The bytes for userId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USERNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object userName_;
    /**
     * <code>optional string userName = 3;</code>
     * @return Whether the userName field is set.
     */
    @java.lang.Override
    public boolean hasUserName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string userName = 3;</code>
     * @return The userName.
     */
    @java.lang.Override
    public java.lang.String getUserName() {
      java.lang.Object ref = userName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          userName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string userName = 3;</code>
     * @return The bytes for userName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUserNameBytes() {
      java.lang.Object ref = userName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INTERVAL_FIELD_NUMBER = 4;
    private int interval_;
    /**
     * <code>optional int32 interval = 4;</code>
     * @return Whether the interval field is set.
     */
    @java.lang.Override
    public boolean hasInterval() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int32 interval = 4;</code>
     * @return The interval.
     */
    @java.lang.Override
    public int getInterval() {
      return interval_;
    }

    public static final int BROWSERTYPE_FIELD_NUMBER = 5;
    private volatile java.lang.Object browserType_;
    /**
     * <code>optional string browserType = 5;</code>
     * @return Whether the browserType field is set.
     */
    @java.lang.Override
    public boolean hasBrowserType() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string browserType = 5;</code>
     * @return The browserType.
     */
    @java.lang.Override
    public java.lang.String getBrowserType() {
      java.lang.Object ref = browserType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          browserType_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string browserType = 5;</code>
     * @return The bytes for browserType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBrowserTypeBytes() {
      java.lang.Object ref = browserType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        browserType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int H5VERSION_FIELD_NUMBER = 6;
    private volatile java.lang.Object h5Version_;
    /**
     * <code>optional string h5Version = 6;</code>
     * @return Whether the h5Version field is set.
     */
    @java.lang.Override
    public boolean hasH5Version() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string h5Version = 6;</code>
     * @return The h5Version.
     */
    @java.lang.Override
    public java.lang.String getH5Version() {
      java.lang.Object ref = h5Version_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          h5Version_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string h5Version = 6;</code>
     * @return The bytes for h5Version.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getH5VersionBytes() {
      java.lang.Object ref = h5Version_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        h5Version_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OS_FIELD_NUMBER = 7;
    private volatile java.lang.Object os_;
    /**
     * <code>optional string os = 7;</code>
     * @return Whether the os field is set.
     */
    @java.lang.Override
    public boolean hasOs() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string os = 7;</code>
     * @return The os.
     */
    @java.lang.Override
    public java.lang.String getOs() {
      java.lang.Object ref = os_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          os_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string os = 7;</code>
     * @return The bytes for os.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOsBytes() {
      java.lang.Object ref = os_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        os_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RESOLUTION_FIELD_NUMBER = 8;
    private volatile java.lang.Object resolution_;
    /**
     * <code>optional string resolution = 8;</code>
     * @return Whether the resolution field is set.
     */
    @java.lang.Override
    public boolean hasResolution() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional string resolution = 8;</code>
     * @return The resolution.
     */
    @java.lang.Override
    public java.lang.String getResolution() {
      java.lang.Object ref = resolution_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          resolution_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string resolution = 8;</code>
     * @return The bytes for resolution.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getResolutionBytes() {
      java.lang.Object ref = resolution_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        resolution_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BROWSERRESOLUTION_FIELD_NUMBER = 9;
    private volatile java.lang.Object browserResolution_;
    /**
     * <code>optional string browserResolution = 9;</code>
     * @return Whether the browserResolution field is set.
     */
    @java.lang.Override
    public boolean hasBrowserResolution() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional string browserResolution = 9;</code>
     * @return The browserResolution.
     */
    @java.lang.Override
    public java.lang.String getBrowserResolution() {
      java.lang.Object ref = browserResolution_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          browserResolution_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string browserResolution = 9;</code>
     * @return The bytes for browserResolution.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBrowserResolutionBytes() {
      java.lang.Object ref = browserResolution_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        browserResolution_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, pid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, userId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, userName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, interval_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, browserType_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, h5Version_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, os_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, resolution_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, browserResolution_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, pid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, userId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, userName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, interval_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, browserType_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, h5Version_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, os_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, resolution_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, browserResolution_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.LogLoading_C2S_Msg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.LogLoading_C2S_Msg other = (com.yorha.proto.Core.LogLoading_C2S_Msg) obj;

      if (hasPid() != other.hasPid()) return false;
      if (hasPid()) {
        if (!getPid()
            .equals(other.getPid())) return false;
      }
      if (hasUserId() != other.hasUserId()) return false;
      if (hasUserId()) {
        if (!getUserId()
            .equals(other.getUserId())) return false;
      }
      if (hasUserName() != other.hasUserName()) return false;
      if (hasUserName()) {
        if (!getUserName()
            .equals(other.getUserName())) return false;
      }
      if (hasInterval() != other.hasInterval()) return false;
      if (hasInterval()) {
        if (getInterval()
            != other.getInterval()) return false;
      }
      if (hasBrowserType() != other.hasBrowserType()) return false;
      if (hasBrowserType()) {
        if (!getBrowserType()
            .equals(other.getBrowserType())) return false;
      }
      if (hasH5Version() != other.hasH5Version()) return false;
      if (hasH5Version()) {
        if (!getH5Version()
            .equals(other.getH5Version())) return false;
      }
      if (hasOs() != other.hasOs()) return false;
      if (hasOs()) {
        if (!getOs()
            .equals(other.getOs())) return false;
      }
      if (hasResolution() != other.hasResolution()) return false;
      if (hasResolution()) {
        if (!getResolution()
            .equals(other.getResolution())) return false;
      }
      if (hasBrowserResolution() != other.hasBrowserResolution()) return false;
      if (hasBrowserResolution()) {
        if (!getBrowserResolution()
            .equals(other.getBrowserResolution())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPid()) {
        hash = (37 * hash) + PID_FIELD_NUMBER;
        hash = (53 * hash) + getPid().hashCode();
      }
      if (hasUserId()) {
        hash = (37 * hash) + USERID_FIELD_NUMBER;
        hash = (53 * hash) + getUserId().hashCode();
      }
      if (hasUserName()) {
        hash = (37 * hash) + USERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getUserName().hashCode();
      }
      if (hasInterval()) {
        hash = (37 * hash) + INTERVAL_FIELD_NUMBER;
        hash = (53 * hash) + getInterval();
      }
      if (hasBrowserType()) {
        hash = (37 * hash) + BROWSERTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getBrowserType().hashCode();
      }
      if (hasH5Version()) {
        hash = (37 * hash) + H5VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getH5Version().hashCode();
      }
      if (hasOs()) {
        hash = (37 * hash) + OS_FIELD_NUMBER;
        hash = (53 * hash) + getOs().hashCode();
      }
      if (hasResolution()) {
        hash = (37 * hash) + RESOLUTION_FIELD_NUMBER;
        hash = (53 * hash) + getResolution().hashCode();
      }
      if (hasBrowserResolution()) {
        hash = (37 * hash) + BROWSERRESOLUTION_FIELD_NUMBER;
        hash = (53 * hash) + getBrowserResolution().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.LogLoading_C2S_Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.LogLoading_C2S_Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * cmd=1005|记录登录耗时等消息
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.LogLoading_C2S_Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LogLoading_C2S_Msg)
        com.yorha.proto.Core.LogLoading_C2S_MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_C2S_Msg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_C2S_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.LogLoading_C2S_Msg.class, com.yorha.proto.Core.LogLoading_C2S_Msg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.LogLoading_C2S_Msg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pid_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        userName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        interval_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        browserType_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        h5Version_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        os_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        resolution_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        browserResolution_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_C2S_Msg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.LogLoading_C2S_Msg getDefaultInstanceForType() {
        return com.yorha.proto.Core.LogLoading_C2S_Msg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.LogLoading_C2S_Msg build() {
        com.yorha.proto.Core.LogLoading_C2S_Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.LogLoading_C2S_Msg buildPartial() {
        com.yorha.proto.Core.LogLoading_C2S_Msg result = new com.yorha.proto.Core.LogLoading_C2S_Msg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.pid_ = pid_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.userId_ = userId_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.userName_ = userName_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.interval_ = interval_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.browserType_ = browserType_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.h5Version_ = h5Version_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.os_ = os_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.resolution_ = resolution_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          to_bitField0_ |= 0x00000100;
        }
        result.browserResolution_ = browserResolution_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.LogLoading_C2S_Msg) {
          return mergeFrom((com.yorha.proto.Core.LogLoading_C2S_Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.LogLoading_C2S_Msg other) {
        if (other == com.yorha.proto.Core.LogLoading_C2S_Msg.getDefaultInstance()) return this;
        if (other.hasPid()) {
          bitField0_ |= 0x00000001;
          pid_ = other.pid_;
          onChanged();
        }
        if (other.hasUserId()) {
          bitField0_ |= 0x00000002;
          userId_ = other.userId_;
          onChanged();
        }
        if (other.hasUserName()) {
          bitField0_ |= 0x00000004;
          userName_ = other.userName_;
          onChanged();
        }
        if (other.hasInterval()) {
          setInterval(other.getInterval());
        }
        if (other.hasBrowserType()) {
          bitField0_ |= 0x00000010;
          browserType_ = other.browserType_;
          onChanged();
        }
        if (other.hasH5Version()) {
          bitField0_ |= 0x00000020;
          h5Version_ = other.h5Version_;
          onChanged();
        }
        if (other.hasOs()) {
          bitField0_ |= 0x00000040;
          os_ = other.os_;
          onChanged();
        }
        if (other.hasResolution()) {
          bitField0_ |= 0x00000080;
          resolution_ = other.resolution_;
          onChanged();
        }
        if (other.hasBrowserResolution()) {
          bitField0_ |= 0x00000100;
          browserResolution_ = other.browserResolution_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.LogLoading_C2S_Msg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.LogLoading_C2S_Msg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object pid_ = "";
      /**
       * <code>optional string pid = 1;</code>
       * @return Whether the pid field is set.
       */
      public boolean hasPid() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string pid = 1;</code>
       * @return The pid.
       */
      public java.lang.String getPid() {
        java.lang.Object ref = pid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            pid_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string pid = 1;</code>
       * @return The bytes for pid.
       */
      public com.google.protobuf.ByteString
          getPidBytes() {
        java.lang.Object ref = pid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string pid = 1;</code>
       * @param value The pid to set.
       * @return This builder for chaining.
       */
      public Builder setPid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        pid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string pid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        pid_ = getDefaultInstance().getPid();
        onChanged();
        return this;
      }
      /**
       * <code>optional string pid = 1;</code>
       * @param value The bytes for pid to set.
       * @return This builder for chaining.
       */
      public Builder setPidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        pid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object userId_ = "";
      /**
       * <code>optional string userId = 2;</code>
       * @return Whether the userId field is set.
       */
      public boolean hasUserId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string userId = 2;</code>
       * @return The userId.
       */
      public java.lang.String getUserId() {
        java.lang.Object ref = userId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            userId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userId = 2;</code>
       * @return The bytes for userId.
       */
      public com.google.protobuf.ByteString
          getUserIdBytes() {
        java.lang.Object ref = userId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = getDefaultInstance().getUserId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userId = 2;</code>
       * @param value The bytes for userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        userId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object userName_ = "";
      /**
       * <code>optional string userName = 3;</code>
       * @return Whether the userName field is set.
       */
      public boolean hasUserName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string userName = 3;</code>
       * @return The userName.
       */
      public java.lang.String getUserName() {
        java.lang.Object ref = userName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            userName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string userName = 3;</code>
       * @return The bytes for userName.
       */
      public com.google.protobuf.ByteString
          getUserNameBytes() {
        java.lang.Object ref = userName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          userName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string userName = 3;</code>
       * @param value The userName to set.
       * @return This builder for chaining.
       */
      public Builder setUserName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        userName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string userName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userName_ = getDefaultInstance().getUserName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string userName = 3;</code>
       * @param value The bytes for userName to set.
       * @return This builder for chaining.
       */
      public Builder setUserNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        userName_ = value;
        onChanged();
        return this;
      }

      private int interval_ ;
      /**
       * <code>optional int32 interval = 4;</code>
       * @return Whether the interval field is set.
       */
      @java.lang.Override
      public boolean hasInterval() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 interval = 4;</code>
       * @return The interval.
       */
      @java.lang.Override
      public int getInterval() {
        return interval_;
      }
      /**
       * <code>optional int32 interval = 4;</code>
       * @param value The interval to set.
       * @return This builder for chaining.
       */
      public Builder setInterval(int value) {
        bitField0_ |= 0x00000008;
        interval_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 interval = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearInterval() {
        bitField0_ = (bitField0_ & ~0x00000008);
        interval_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object browserType_ = "";
      /**
       * <code>optional string browserType = 5;</code>
       * @return Whether the browserType field is set.
       */
      public boolean hasBrowserType() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional string browserType = 5;</code>
       * @return The browserType.
       */
      public java.lang.String getBrowserType() {
        java.lang.Object ref = browserType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            browserType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string browserType = 5;</code>
       * @return The bytes for browserType.
       */
      public com.google.protobuf.ByteString
          getBrowserTypeBytes() {
        java.lang.Object ref = browserType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          browserType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string browserType = 5;</code>
       * @param value The browserType to set.
       * @return This builder for chaining.
       */
      public Builder setBrowserType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        browserType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string browserType = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearBrowserType() {
        bitField0_ = (bitField0_ & ~0x00000010);
        browserType_ = getDefaultInstance().getBrowserType();
        onChanged();
        return this;
      }
      /**
       * <code>optional string browserType = 5;</code>
       * @param value The bytes for browserType to set.
       * @return This builder for chaining.
       */
      public Builder setBrowserTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        browserType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object h5Version_ = "";
      /**
       * <code>optional string h5Version = 6;</code>
       * @return Whether the h5Version field is set.
       */
      public boolean hasH5Version() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional string h5Version = 6;</code>
       * @return The h5Version.
       */
      public java.lang.String getH5Version() {
        java.lang.Object ref = h5Version_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            h5Version_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string h5Version = 6;</code>
       * @return The bytes for h5Version.
       */
      public com.google.protobuf.ByteString
          getH5VersionBytes() {
        java.lang.Object ref = h5Version_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          h5Version_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string h5Version = 6;</code>
       * @param value The h5Version to set.
       * @return This builder for chaining.
       */
      public Builder setH5Version(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        h5Version_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string h5Version = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearH5Version() {
        bitField0_ = (bitField0_ & ~0x00000020);
        h5Version_ = getDefaultInstance().getH5Version();
        onChanged();
        return this;
      }
      /**
       * <code>optional string h5Version = 6;</code>
       * @param value The bytes for h5Version to set.
       * @return This builder for chaining.
       */
      public Builder setH5VersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        h5Version_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object os_ = "";
      /**
       * <code>optional string os = 7;</code>
       * @return Whether the os field is set.
       */
      public boolean hasOs() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional string os = 7;</code>
       * @return The os.
       */
      public java.lang.String getOs() {
        java.lang.Object ref = os_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            os_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string os = 7;</code>
       * @return The bytes for os.
       */
      public com.google.protobuf.ByteString
          getOsBytes() {
        java.lang.Object ref = os_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          os_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string os = 7;</code>
       * @param value The os to set.
       * @return This builder for chaining.
       */
      public Builder setOs(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        os_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string os = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearOs() {
        bitField0_ = (bitField0_ & ~0x00000040);
        os_ = getDefaultInstance().getOs();
        onChanged();
        return this;
      }
      /**
       * <code>optional string os = 7;</code>
       * @param value The bytes for os to set.
       * @return This builder for chaining.
       */
      public Builder setOsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        os_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object resolution_ = "";
      /**
       * <code>optional string resolution = 8;</code>
       * @return Whether the resolution field is set.
       */
      public boolean hasResolution() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional string resolution = 8;</code>
       * @return The resolution.
       */
      public java.lang.String getResolution() {
        java.lang.Object ref = resolution_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            resolution_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string resolution = 8;</code>
       * @return The bytes for resolution.
       */
      public com.google.protobuf.ByteString
          getResolutionBytes() {
        java.lang.Object ref = resolution_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          resolution_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string resolution = 8;</code>
       * @param value The resolution to set.
       * @return This builder for chaining.
       */
      public Builder setResolution(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        resolution_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string resolution = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearResolution() {
        bitField0_ = (bitField0_ & ~0x00000080);
        resolution_ = getDefaultInstance().getResolution();
        onChanged();
        return this;
      }
      /**
       * <code>optional string resolution = 8;</code>
       * @param value The bytes for resolution to set.
       * @return This builder for chaining.
       */
      public Builder setResolutionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        resolution_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object browserResolution_ = "";
      /**
       * <code>optional string browserResolution = 9;</code>
       * @return Whether the browserResolution field is set.
       */
      public boolean hasBrowserResolution() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional string browserResolution = 9;</code>
       * @return The browserResolution.
       */
      public java.lang.String getBrowserResolution() {
        java.lang.Object ref = browserResolution_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            browserResolution_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string browserResolution = 9;</code>
       * @return The bytes for browserResolution.
       */
      public com.google.protobuf.ByteString
          getBrowserResolutionBytes() {
        java.lang.Object ref = browserResolution_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          browserResolution_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string browserResolution = 9;</code>
       * @param value The browserResolution to set.
       * @return This builder for chaining.
       */
      public Builder setBrowserResolution(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        browserResolution_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string browserResolution = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearBrowserResolution() {
        bitField0_ = (bitField0_ & ~0x00000100);
        browserResolution_ = getDefaultInstance().getBrowserResolution();
        onChanged();
        return this;
      }
      /**
       * <code>optional string browserResolution = 9;</code>
       * @param value The bytes for browserResolution to set.
       * @return This builder for chaining.
       */
      public Builder setBrowserResolutionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        browserResolution_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LogLoading_C2S_Msg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LogLoading_C2S_Msg)
    private static final com.yorha.proto.Core.LogLoading_C2S_Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.LogLoading_C2S_Msg();
    }

    public static com.yorha.proto.Core.LogLoading_C2S_Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LogLoading_C2S_Msg>
        PARSER = new com.google.protobuf.AbstractParser<LogLoading_C2S_Msg>() {
      @java.lang.Override
      public LogLoading_C2S_Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LogLoading_C2S_Msg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LogLoading_C2S_Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LogLoading_C2S_Msg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.LogLoading_C2S_Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LogLoading_S2C_MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LogLoading_S2C_Msg)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.LogLoading_S2C_Msg}
   */
  public static final class LogLoading_S2C_Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LogLoading_S2C_Msg)
      LogLoading_S2C_MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LogLoading_S2C_Msg.newBuilder() to construct.
    private LogLoading_S2C_Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LogLoading_S2C_Msg() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LogLoading_S2C_Msg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LogLoading_S2C_Msg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_S2C_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_S2C_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.LogLoading_S2C_Msg.class, com.yorha.proto.Core.LogLoading_S2C_Msg.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.LogLoading_S2C_Msg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.LogLoading_S2C_Msg other = (com.yorha.proto.Core.LogLoading_S2C_Msg) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.LogLoading_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.LogLoading_S2C_Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.LogLoading_S2C_Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LogLoading_S2C_Msg)
        com.yorha.proto.Core.LogLoading_S2C_MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_S2C_Msg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_S2C_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.LogLoading_S2C_Msg.class, com.yorha.proto.Core.LogLoading_S2C_Msg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.LogLoading_S2C_Msg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_LogLoading_S2C_Msg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.LogLoading_S2C_Msg getDefaultInstanceForType() {
        return com.yorha.proto.Core.LogLoading_S2C_Msg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.LogLoading_S2C_Msg build() {
        com.yorha.proto.Core.LogLoading_S2C_Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.LogLoading_S2C_Msg buildPartial() {
        com.yorha.proto.Core.LogLoading_S2C_Msg result = new com.yorha.proto.Core.LogLoading_S2C_Msg(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.LogLoading_S2C_Msg) {
          return mergeFrom((com.yorha.proto.Core.LogLoading_S2C_Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.LogLoading_S2C_Msg other) {
        if (other == com.yorha.proto.Core.LogLoading_S2C_Msg.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.LogLoading_S2C_Msg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.LogLoading_S2C_Msg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LogLoading_S2C_Msg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LogLoading_S2C_Msg)
    private static final com.yorha.proto.Core.LogLoading_S2C_Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.LogLoading_S2C_Msg();
    }

    public static com.yorha.proto.Core.LogLoading_S2C_Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LogLoading_S2C_Msg>
        PARSER = new com.google.protobuf.AbstractParser<LogLoading_S2C_Msg>() {
      @java.lang.Override
      public LogLoading_S2C_Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LogLoading_S2C_Msg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LogLoading_S2C_Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LogLoading_S2C_Msg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.LogLoading_S2C_Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KeepAlive_C2S_MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KeepAlive_C2S_Msg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
     * </pre>
     *
     * <code>optional int64 ping = 1;</code>
     * @return Whether the ping field is set.
     */
    boolean hasPing();
    /**
     * <pre>
     * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
     * </pre>
     *
     * <code>optional int64 ping = 1;</code>
     * @return The ping.
     */
    long getPing();
  }
  /**
   * <pre>
   * cmd=1001
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.KeepAlive_C2S_Msg}
   */
  public static final class KeepAlive_C2S_Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KeepAlive_C2S_Msg)
      KeepAlive_C2S_MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KeepAlive_C2S_Msg.newBuilder() to construct.
    private KeepAlive_C2S_Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KeepAlive_C2S_Msg() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KeepAlive_C2S_Msg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KeepAlive_C2S_Msg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ping_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_C2S_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_C2S_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.KeepAlive_C2S_Msg.class, com.yorha.proto.Core.KeepAlive_C2S_Msg.Builder.class);
    }

    private int bitField0_;
    public static final int PING_FIELD_NUMBER = 1;
    private long ping_;
    /**
     * <pre>
     * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
     * </pre>
     *
     * <code>optional int64 ping = 1;</code>
     * @return Whether the ping field is set.
     */
    @java.lang.Override
    public boolean hasPing() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
     * </pre>
     *
     * <code>optional int64 ping = 1;</code>
     * @return The ping.
     */
    @java.lang.Override
    public long getPing() {
      return ping_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, ping_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, ping_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.KeepAlive_C2S_Msg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.KeepAlive_C2S_Msg other = (com.yorha.proto.Core.KeepAlive_C2S_Msg) obj;

      if (hasPing() != other.hasPing()) return false;
      if (hasPing()) {
        if (getPing()
            != other.getPing()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPing()) {
        hash = (37 * hash) + PING_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPing());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.KeepAlive_C2S_Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.KeepAlive_C2S_Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * cmd=1001
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.KeepAlive_C2S_Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KeepAlive_C2S_Msg)
        com.yorha.proto.Core.KeepAlive_C2S_MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_C2S_Msg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_C2S_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.KeepAlive_C2S_Msg.class, com.yorha.proto.Core.KeepAlive_C2S_Msg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.KeepAlive_C2S_Msg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ping_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_C2S_Msg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.KeepAlive_C2S_Msg getDefaultInstanceForType() {
        return com.yorha.proto.Core.KeepAlive_C2S_Msg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.KeepAlive_C2S_Msg build() {
        com.yorha.proto.Core.KeepAlive_C2S_Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.KeepAlive_C2S_Msg buildPartial() {
        com.yorha.proto.Core.KeepAlive_C2S_Msg result = new com.yorha.proto.Core.KeepAlive_C2S_Msg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ping_ = ping_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.KeepAlive_C2S_Msg) {
          return mergeFrom((com.yorha.proto.Core.KeepAlive_C2S_Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.KeepAlive_C2S_Msg other) {
        if (other == com.yorha.proto.Core.KeepAlive_C2S_Msg.getDefaultInstance()) return this;
        if (other.hasPing()) {
          setPing(other.getPing());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.KeepAlive_C2S_Msg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.KeepAlive_C2S_Msg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long ping_ ;
      /**
       * <pre>
       * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
       * </pre>
       *
       * <code>optional int64 ping = 1;</code>
       * @return Whether the ping field is set.
       */
      @java.lang.Override
      public boolean hasPing() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
       * </pre>
       *
       * <code>optional int64 ping = 1;</code>
       * @return The ping.
       */
      @java.lang.Override
      public long getPing() {
        return ping_;
      }
      /**
       * <pre>
       * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
       * </pre>
       *
       * <code>optional int64 ping = 1;</code>
       * @param value The ping to set.
       * @return This builder for chaining.
       */
      public Builder setPing(long value) {
        bitField0_ |= 0x00000001;
        ping_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 从客户端对服务器发送数据到接收到服务器反馈数据的时间，ms为单位
       * </pre>
       *
       * <code>optional int64 ping = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPing() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ping_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KeepAlive_C2S_Msg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KeepAlive_C2S_Msg)
    private static final com.yorha.proto.Core.KeepAlive_C2S_Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.KeepAlive_C2S_Msg();
    }

    public static com.yorha.proto.Core.KeepAlive_C2S_Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KeepAlive_C2S_Msg>
        PARSER = new com.google.protobuf.AbstractParser<KeepAlive_C2S_Msg>() {
      @java.lang.Override
      public KeepAlive_C2S_Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KeepAlive_C2S_Msg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KeepAlive_C2S_Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KeepAlive_C2S_Msg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.KeepAlive_C2S_Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KeepAlive_S2C_MsgOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KeepAlive_S2C_Msg)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 curTime = 1;</code>
     * @return Whether the curTime field is set.
     */
    boolean hasCurTime();
    /**
     * <code>optional int64 curTime = 1;</code>
     * @return The curTime.
     */
    long getCurTime();
  }
  /**
   * <pre>
   * cmd=1002
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.KeepAlive_S2C_Msg}
   */
  public static final class KeepAlive_S2C_Msg extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KeepAlive_S2C_Msg)
      KeepAlive_S2C_MsgOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KeepAlive_S2C_Msg.newBuilder() to construct.
    private KeepAlive_S2C_Msg(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KeepAlive_S2C_Msg() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KeepAlive_S2C_Msg();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KeepAlive_S2C_Msg(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              curTime_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_S2C_Msg_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_S2C_Msg_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.KeepAlive_S2C_Msg.class, com.yorha.proto.Core.KeepAlive_S2C_Msg.Builder.class);
    }

    private int bitField0_;
    public static final int CURTIME_FIELD_NUMBER = 1;
    private long curTime_;
    /**
     * <code>optional int64 curTime = 1;</code>
     * @return Whether the curTime field is set.
     */
    @java.lang.Override
    public boolean hasCurTime() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 curTime = 1;</code>
     * @return The curTime.
     */
    @java.lang.Override
    public long getCurTime() {
      return curTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, curTime_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, curTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.KeepAlive_S2C_Msg)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.KeepAlive_S2C_Msg other = (com.yorha.proto.Core.KeepAlive_S2C_Msg) obj;

      if (hasCurTime() != other.hasCurTime()) return false;
      if (hasCurTime()) {
        if (getCurTime()
            != other.getCurTime()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCurTime()) {
        hash = (37 * hash) + CURTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCurTime());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.KeepAlive_S2C_Msg parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.KeepAlive_S2C_Msg prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * cmd=1002
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.KeepAlive_S2C_Msg}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KeepAlive_S2C_Msg)
        com.yorha.proto.Core.KeepAlive_S2C_MsgOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_S2C_Msg_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_S2C_Msg_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.KeepAlive_S2C_Msg.class, com.yorha.proto.Core.KeepAlive_S2C_Msg.Builder.class);
      }

      // Construct using com.yorha.proto.Core.KeepAlive_S2C_Msg.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        curTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_KeepAlive_S2C_Msg_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.KeepAlive_S2C_Msg getDefaultInstanceForType() {
        return com.yorha.proto.Core.KeepAlive_S2C_Msg.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.KeepAlive_S2C_Msg build() {
        com.yorha.proto.Core.KeepAlive_S2C_Msg result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.KeepAlive_S2C_Msg buildPartial() {
        com.yorha.proto.Core.KeepAlive_S2C_Msg result = new com.yorha.proto.Core.KeepAlive_S2C_Msg(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.curTime_ = curTime_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.KeepAlive_S2C_Msg) {
          return mergeFrom((com.yorha.proto.Core.KeepAlive_S2C_Msg)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.KeepAlive_S2C_Msg other) {
        if (other == com.yorha.proto.Core.KeepAlive_S2C_Msg.getDefaultInstance()) return this;
        if (other.hasCurTime()) {
          setCurTime(other.getCurTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.KeepAlive_S2C_Msg parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.KeepAlive_S2C_Msg) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long curTime_ ;
      /**
       * <code>optional int64 curTime = 1;</code>
       * @return Whether the curTime field is set.
       */
      @java.lang.Override
      public boolean hasCurTime() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 curTime = 1;</code>
       * @return The curTime.
       */
      @java.lang.Override
      public long getCurTime() {
        return curTime_;
      }
      /**
       * <code>optional int64 curTime = 1;</code>
       * @param value The curTime to set.
       * @return This builder for chaining.
       */
      public Builder setCurTime(long value) {
        bitField0_ |= 0x00000001;
        curTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 curTime = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurTime() {
        bitField0_ = (bitField0_ & ~0x00000001);
        curTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KeepAlive_S2C_Msg)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KeepAlive_S2C_Msg)
    private static final com.yorha.proto.Core.KeepAlive_S2C_Msg DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.KeepAlive_S2C_Msg();
    }

    public static com.yorha.proto.Core.KeepAlive_S2C_Msg getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KeepAlive_S2C_Msg>
        PARSER = new com.google.protobuf.AbstractParser<KeepAlive_S2C_Msg>() {
      @java.lang.Override
      public KeepAlive_S2C_Msg parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KeepAlive_S2C_Msg(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KeepAlive_S2C_Msg> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KeepAlive_S2C_Msg> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.KeepAlive_S2C_Msg getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CSHeaderOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CSHeader)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>optional int32 seqId = 2;</code>
     * @return Whether the seqId field is set.
     */
    boolean hasSeqId();
    /**
     * <code>optional int32 seqId = 2;</code>
     * @return The seqId.
     */
    int getSeqId();

    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 4;</code>
     * @return Whether the flag field is set.
     */
    boolean hasFlag();
    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 4;</code>
     * @return The flag.
     */
    int getFlag();

    /**
     * <code>optional .com.yorha.proto.Code code = 5;</code>
     * @return Whether the code field is set.
     */
    boolean hasCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 5;</code>
     * @return The code.
     */
    com.yorha.proto.Core.Code getCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 5;</code>
     */
    com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CSHeader}
   */
  public static final class CSHeader extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CSHeader)
      CSHeaderOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CSHeader.newBuilder() to construct.
    private CSHeader(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CSHeader() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CSHeader();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CSHeader(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              seqId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              flag_ = input.readInt32();
              break;
            }
            case 42: {
              com.yorha.proto.Core.Code.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = code_.toBuilder();
              }
              code_ = input.readMessage(com.yorha.proto.Core.Code.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(code_);
                code_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_CSHeader_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Core.internal_static_com_yorha_proto_CSHeader_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Core.CSHeader.class, com.yorha.proto.Core.CSHeader.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>optional int32 type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int SEQID_FIELD_NUMBER = 2;
    private int seqId_;
    /**
     * <code>optional int32 seqId = 2;</code>
     * @return Whether the seqId field is set.
     */
    @java.lang.Override
    public boolean hasSeqId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 seqId = 2;</code>
     * @return The seqId.
     */
    @java.lang.Override
    public int getSeqId() {
      return seqId_;
    }

    public static final int FLAG_FIELD_NUMBER = 4;
    private int flag_;
    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 4;</code>
     * @return Whether the flag field is set.
     */
    @java.lang.Override
    public boolean hasFlag() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 第一位: 是否压缩; 第二位: 是否加密
     * </pre>
     *
     * <code>optional int32 flag = 4;</code>
     * @return The flag.
     */
    @java.lang.Override
    public int getFlag() {
      return flag_;
    }

    public static final int CODE_FIELD_NUMBER = 5;
    private com.yorha.proto.Core.Code code_;
    /**
     * <code>optional .com.yorha.proto.Code code = 5;</code>
     * @return Whether the code field is set.
     */
    @java.lang.Override
    public boolean hasCode() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 5;</code>
     * @return The code.
     */
    @java.lang.Override
    public com.yorha.proto.Core.Code getCode() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(4, flag_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(5, getCode());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, flag_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getCode());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Core.CSHeader)) {
        return super.equals(obj);
      }
      com.yorha.proto.Core.CSHeader other = (com.yorha.proto.Core.CSHeader) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (getType()
            != other.getType()) return false;
      }
      if (hasSeqId() != other.hasSeqId()) return false;
      if (hasSeqId()) {
        if (getSeqId()
            != other.getSeqId()) return false;
      }
      if (hasFlag() != other.hasFlag()) return false;
      if (hasFlag()) {
        if (getFlag()
            != other.getFlag()) return false;
      }
      if (hasCode() != other.hasCode()) return false;
      if (hasCode()) {
        if (!getCode()
            .equals(other.getCode())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType();
      }
      if (hasSeqId()) {
        hash = (37 * hash) + SEQID_FIELD_NUMBER;
        hash = (53 * hash) + getSeqId();
      }
      if (hasFlag()) {
        hash = (37 * hash) + FLAG_FIELD_NUMBER;
        hash = (53 * hash) + getFlag();
      }
      if (hasCode()) {
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Core.CSHeader parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.CSHeader parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.CSHeader parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Core.CSHeader parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Core.CSHeader prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CSHeader}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CSHeader)
        com.yorha.proto.Core.CSHeaderOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_CSHeader_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_CSHeader_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Core.CSHeader.class, com.yorha.proto.Core.CSHeader.Builder.class);
      }

      // Construct using com.yorha.proto.Core.CSHeader.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCodeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        seqId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        flag_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (codeBuilder_ == null) {
          code_ = null;
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Core.internal_static_com_yorha_proto_CSHeader_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Core.CSHeader getDefaultInstanceForType() {
        return com.yorha.proto.Core.CSHeader.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Core.CSHeader build() {
        com.yorha.proto.Core.CSHeader result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Core.CSHeader buildPartial() {
        com.yorha.proto.Core.CSHeader result = new com.yorha.proto.Core.CSHeader(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.seqId_ = seqId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.flag_ = flag_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (codeBuilder_ == null) {
            result.code_ = code_;
          } else {
            result.code_ = codeBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Core.CSHeader) {
          return mergeFrom((com.yorha.proto.Core.CSHeader)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Core.CSHeader other) {
        if (other == com.yorha.proto.Core.CSHeader.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasSeqId()) {
          setSeqId(other.getSeqId());
        }
        if (other.hasFlag()) {
          setFlag(other.getFlag());
        }
        if (other.hasCode()) {
          mergeCode(other.getCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Core.CSHeader parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Core.CSHeader) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>optional int32 type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int seqId_ ;
      /**
       * <code>optional int32 seqId = 2;</code>
       * @return Whether the seqId field is set.
       */
      @java.lang.Override
      public boolean hasSeqId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 seqId = 2;</code>
       * @return The seqId.
       */
      @java.lang.Override
      public int getSeqId() {
        return seqId_;
      }
      /**
       * <code>optional int32 seqId = 2;</code>
       * @param value The seqId to set.
       * @return This builder for chaining.
       */
      public Builder setSeqId(int value) {
        bitField0_ |= 0x00000002;
        seqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 seqId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeqId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        seqId_ = 0;
        onChanged();
        return this;
      }

      private int flag_ ;
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 4;</code>
       * @return Whether the flag field is set.
       */
      @java.lang.Override
      public boolean hasFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 4;</code>
       * @return The flag.
       */
      @java.lang.Override
      public int getFlag() {
        return flag_;
      }
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 4;</code>
       * @param value The flag to set.
       * @return This builder for chaining.
       */
      public Builder setFlag(int value) {
        bitField0_ |= 0x00000004;
        flag_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 第一位: 是否压缩; 第二位: 是否加密
       * </pre>
       *
       * <code>optional int32 flag = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        flag_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Core.Code code_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> codeBuilder_;
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       * @return Whether the code field is set.
       */
      public boolean hasCode() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       * @return The code.
       */
      public com.yorha.proto.Core.Code getCode() {
        if (codeBuilder_ == null) {
          return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        } else {
          return codeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       */
      public Builder setCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          code_ = value;
          onChanged();
        } else {
          codeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       */
      public Builder setCode(
          com.yorha.proto.Core.Code.Builder builderForValue) {
        if (codeBuilder_ == null) {
          code_ = builderForValue.build();
          onChanged();
        } else {
          codeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       */
      public Builder mergeCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              code_ != null &&
              code_ != com.yorha.proto.Core.Code.getDefaultInstance()) {
            code_ =
              com.yorha.proto.Core.Code.newBuilder(code_).mergeFrom(value).buildPartial();
          } else {
            code_ = value;
          }
          onChanged();
        } else {
          codeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       */
      public Builder clearCode() {
        if (codeBuilder_ == null) {
          code_ = null;
          onChanged();
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       */
      public com.yorha.proto.Core.Code.Builder getCodeBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCodeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       */
      public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
        if (codeBuilder_ != null) {
          return codeBuilder_.getMessageOrBuilder();
        } else {
          return code_ == null ?
              com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> 
          getCodeFieldBuilder() {
        if (codeBuilder_ == null) {
          codeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder>(
                  getCode(),
                  getParentForChildren(),
                  isClean());
          code_ = null;
        }
        return codeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CSHeader)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CSHeader)
    private static final com.yorha.proto.Core.CSHeader DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Core.CSHeader();
    }

    public static com.yorha.proto.Core.CSHeader getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CSHeader>
        PARSER = new com.google.protobuf.AbstractParser<CSHeader>() {
      @java.lang.Override
      public CSHeader parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CSHeader(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CSHeader> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CSHeader> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Core.CSHeader getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Code_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Code_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RpcMsg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RpcMsg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GameMsg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GameMsg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RpcHeader_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RpcHeader_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Disconnet_S2C_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Disconnet_S2C_Msg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Warn_S2C_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Warn_S2C_Msg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LogLoading_C2S_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LogLoading_C2S_Msg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LogLoading_S2C_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LogLoading_S2C_Msg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KeepAlive_C2S_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KeepAlive_C2S_Msg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KeepAlive_S2C_Msg_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KeepAlive_S2C_Msg_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CSHeader_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CSHeader_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\034ss_proto/gen/core/core.proto\022\017com.yorh" +
      "a.proto\"!\n\004Code\022\n\n\002id\030\001 \001(\005\022\r\n\005param\030\002 \003" +
      "(\t\"Z\n\006RpcMsg\022\014\n\004sync\030\001 \001(\010\022\r\n\005seqId\030\002 \001(" +
      "\003\022\017\n\007msgType\030\003 \001(\005\022\021\n\ttimestamp\030\004 \001(\003\022\017\n" +
      "\007content\030\005 \001(\t\"B\n\007GameMsg\022\014\n\004type\030\001 \001(\005\022" +
      "\014\n\004flag\030\002 \001(\005\022\017\n\007content\030\003 \001(\t\022\n\n\002id\030\004 \001" +
      "(\005\"\013\n\tRpcHeader\"8\n\021Disconnet_S2C_Msg\022#\n\004" +
      "code\030\001 \001(\0132\025.com.yorha.proto.Code\"3\n\014War" +
      "n_S2C_Msg\022#\n\004Code\030\001 \001(\0132\025.com.yorha.prot" +
      "o.Code\"\270\001\n\022LogLoading_C2S_Msg\022\013\n\003pid\030\001 \001" +
      "(\t\022\016\n\006userId\030\002 \001(\t\022\020\n\010userName\030\003 \001(\t\022\020\n\010" +
      "interval\030\004 \001(\005\022\023\n\013browserType\030\005 \001(\t\022\021\n\th" +
      "5Version\030\006 \001(\t\022\n\n\002os\030\007 \001(\t\022\022\n\nresolution" +
      "\030\010 \001(\t\022\031\n\021browserResolution\030\t \001(\t\"\024\n\022Log" +
      "Loading_S2C_Msg\"!\n\021KeepAlive_C2S_Msg\022\014\n\004" +
      "ping\030\001 \001(\003\"$\n\021KeepAlive_S2C_Msg\022\017\n\007curTi" +
      "me\030\001 \001(\003\"Z\n\010CSHeader\022\014\n\004type\030\001 \001(\005\022\r\n\005se" +
      "qId\030\002 \001(\005\022\014\n\004flag\030\004 \001(\005\022#\n\004code\030\005 \001(\0132\025." +
      "com.yorha.proto.CodeB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_Code_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Code_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Code_descriptor,
        new java.lang.String[] { "Id", "Param", });
    internal_static_com_yorha_proto_RpcMsg_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_RpcMsg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RpcMsg_descriptor,
        new java.lang.String[] { "Sync", "SeqId", "MsgType", "Timestamp", "Content", });
    internal_static_com_yorha_proto_GameMsg_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_GameMsg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GameMsg_descriptor,
        new java.lang.String[] { "Type", "Flag", "Content", "Id", });
    internal_static_com_yorha_proto_RpcHeader_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_RpcHeader_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RpcHeader_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Disconnet_S2C_Msg_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Disconnet_S2C_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Disconnet_S2C_Msg_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_com_yorha_proto_Warn_S2C_Msg_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Warn_S2C_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Warn_S2C_Msg_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_com_yorha_proto_LogLoading_C2S_Msg_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_LogLoading_C2S_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LogLoading_C2S_Msg_descriptor,
        new java.lang.String[] { "Pid", "UserId", "UserName", "Interval", "BrowserType", "H5Version", "Os", "Resolution", "BrowserResolution", });
    internal_static_com_yorha_proto_LogLoading_S2C_Msg_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_LogLoading_S2C_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LogLoading_S2C_Msg_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_KeepAlive_C2S_Msg_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_KeepAlive_C2S_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KeepAlive_C2S_Msg_descriptor,
        new java.lang.String[] { "Ping", });
    internal_static_com_yorha_proto_KeepAlive_S2C_Msg_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_KeepAlive_S2C_Msg_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KeepAlive_S2C_Msg_descriptor,
        new java.lang.String[] { "CurTime", });
    internal_static_com_yorha_proto_CSHeader_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_CSHeader_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CSHeader_descriptor,
        new java.lang.String[] { "Type", "SeqId", "Flag", "Code", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

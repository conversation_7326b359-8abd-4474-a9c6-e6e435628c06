// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_rank.proto

package com.yorha.proto;

public final class SsClanRank {
  private SsClanRank() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GetTopClanRankInfoAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetTopClanRankInfoAsk)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetTopClanRankInfoAsk}
   */
  public static final class GetTopClanRankInfoAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetTopClanRankInfoAsk)
      GetTopClanRankInfoAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetTopClanRankInfoAsk.newBuilder() to construct.
    private GetTopClanRankInfoAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetTopClanRankInfoAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetTopClanRankInfoAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetTopClanRankInfoAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk.class, com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk other = (com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetTopClanRankInfoAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetTopClanRankInfoAsk)
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk.class, com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk build() {
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk buildPartial() {
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk result = new com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk) {
          return mergeFrom((com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk other) {
        if (other == com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetTopClanRankInfoAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetTopClanRankInfoAsk)
    private static final com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk();
    }

    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetTopClanRankInfoAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetTopClanRankInfoAsk>() {
      @java.lang.Override
      public GetTopClanRankInfoAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetTopClanRankInfoAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetTopClanRankInfoAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetTopClanRankInfoAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanRank.GetTopClanRankInfoAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetTopClanRankInfoAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetTopClanRankInfoAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> 
        getDtoList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTO getDto(int index);
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    int getDtoCount();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index);

    /**
     * <pre>
     * 排行榜重置的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 rankResetTsMs = 2;</code>
     * @return Whether the rankResetTsMs field is set.
     */
    boolean hasRankResetTsMs();
    /**
     * <pre>
     * 排行榜重置的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 rankResetTsMs = 2;</code>
     * @return The rankResetTsMs.
     */
    long getRankResetTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetTopClanRankInfoAns}
   */
  public static final class GetTopClanRankInfoAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetTopClanRankInfoAns)
      GetTopClanRankInfoAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetTopClanRankInfoAns.newBuilder() to construct.
    private GetTopClanRankInfoAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetTopClanRankInfoAns() {
      dto_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetTopClanRankInfoAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetTopClanRankInfoAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>();
                mutable_bitField0_ |= 0x00000001;
              }
              dto_.add(
                  input.readMessage(com.yorha.proto.StructMsg.RankInfoDTO.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              rankResetTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          dto_ = java.util.Collections.unmodifiableList(dto_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanRank.GetTopClanRankInfoAns.class, com.yorha.proto.SsClanRank.GetTopClanRankInfoAns.Builder.class);
    }

    private int bitField0_;
    public static final int DTO_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_;
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public int getDtoCount() {
      return dto_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
      return dto_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index) {
      return dto_.get(index);
    }

    public static final int RANKRESETTSMS_FIELD_NUMBER = 2;
    private long rankResetTsMs_;
    /**
     * <pre>
     * 排行榜重置的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 rankResetTsMs = 2;</code>
     * @return Whether the rankResetTsMs field is set.
     */
    @java.lang.Override
    public boolean hasRankResetTsMs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 排行榜重置的时间戳(ms)
     * </pre>
     *
     * <code>optional int64 rankResetTsMs = 2;</code>
     * @return The rankResetTsMs.
     */
    @java.lang.Override
    public long getRankResetTsMs() {
      return rankResetTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < dto_.size(); i++) {
        output.writeMessage(1, dto_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(2, rankResetTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < dto_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, dto_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rankResetTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanRank.GetTopClanRankInfoAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanRank.GetTopClanRankInfoAns other = (com.yorha.proto.SsClanRank.GetTopClanRankInfoAns) obj;

      if (!getDtoList()
          .equals(other.getDtoList())) return false;
      if (hasRankResetTsMs() != other.hasRankResetTsMs()) return false;
      if (hasRankResetTsMs()) {
        if (getRankResetTsMs()
            != other.getRankResetTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDtoCount() > 0) {
        hash = (37 * hash) + DTO_FIELD_NUMBER;
        hash = (53 * hash) + getDtoList().hashCode();
      }
      if (hasRankResetTsMs()) {
        hash = (37 * hash) + RANKRESETTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRankResetTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanRank.GetTopClanRankInfoAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetTopClanRankInfoAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetTopClanRankInfoAns)
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanRank.GetTopClanRankInfoAns.class, com.yorha.proto.SsClanRank.GetTopClanRankInfoAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanRank.GetTopClanRankInfoAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          dtoBuilder_.clear();
        }
        rankResetTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetTopClanRankInfoAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetTopClanRankInfoAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanRank.GetTopClanRankInfoAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetTopClanRankInfoAns build() {
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetTopClanRankInfoAns buildPartial() {
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAns result = new com.yorha.proto.SsClanRank.GetTopClanRankInfoAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (dtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            dto_ = java.util.Collections.unmodifiableList(dto_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.dto_ = dto_;
        } else {
          result.dto_ = dtoBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rankResetTsMs_ = rankResetTsMs_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanRank.GetTopClanRankInfoAns) {
          return mergeFrom((com.yorha.proto.SsClanRank.GetTopClanRankInfoAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanRank.GetTopClanRankInfoAns other) {
        if (other == com.yorha.proto.SsClanRank.GetTopClanRankInfoAns.getDefaultInstance()) return this;
        if (dtoBuilder_ == null) {
          if (!other.dto_.isEmpty()) {
            if (dto_.isEmpty()) {
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureDtoIsMutable();
              dto_.addAll(other.dto_);
            }
            onChanged();
          }
        } else {
          if (!other.dto_.isEmpty()) {
            if (dtoBuilder_.isEmpty()) {
              dtoBuilder_.dispose();
              dtoBuilder_ = null;
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000001);
              dtoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDtoFieldBuilder() : null;
            } else {
              dtoBuilder_.addAllMessages(other.dto_);
            }
          }
        }
        if (other.hasRankResetTsMs()) {
          setRankResetTsMs(other.getRankResetTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanRank.GetTopClanRankInfoAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanRank.GetTopClanRankInfoAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_ =
        java.util.Collections.emptyList();
      private void ensureDtoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>(dto_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> dtoBuilder_;

      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
        if (dtoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dto_);
        } else {
          return dtoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public int getDtoCount() {
        if (dtoBuilder_ == null) {
          return dto_.size();
        } else {
          return dtoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);
        } else {
          return dtoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.set(index, value);
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.set(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(index, value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(
          com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder addAllDto(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.RankInfoDTO> values) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dto_);
          onChanged();
        } else {
          dtoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder clearDto() {
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          dtoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public Builder removeDto(int index) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.remove(index);
          onChanged();
        } else {
          dtoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder getDtoBuilder(
          int index) {
        return getDtoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
          int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);  } else {
          return dtoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
           getDtoOrBuilderList() {
        if (dtoBuilder_ != null) {
          return dtoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dto_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder() {
        return getDtoFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder(
          int index) {
        return getDtoFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 1;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO.Builder> 
           getDtoBuilderList() {
        return getDtoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
          getDtoFieldBuilder() {
        if (dtoBuilder_ == null) {
          dtoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder>(
                  dto_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          dto_ = null;
        }
        return dtoBuilder_;
      }

      private long rankResetTsMs_ ;
      /**
       * <pre>
       * 排行榜重置的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 rankResetTsMs = 2;</code>
       * @return Whether the rankResetTsMs field is set.
       */
      @java.lang.Override
      public boolean hasRankResetTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 排行榜重置的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 rankResetTsMs = 2;</code>
       * @return The rankResetTsMs.
       */
      @java.lang.Override
      public long getRankResetTsMs() {
        return rankResetTsMs_;
      }
      /**
       * <pre>
       * 排行榜重置的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 rankResetTsMs = 2;</code>
       * @param value The rankResetTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setRankResetTsMs(long value) {
        bitField0_ |= 0x00000002;
        rankResetTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜重置的时间戳(ms)
       * </pre>
       *
       * <code>optional int64 rankResetTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankResetTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rankResetTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetTopClanRankInfoAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetTopClanRankInfoAns)
    private static final com.yorha.proto.SsClanRank.GetTopClanRankInfoAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanRank.GetTopClanRankInfoAns();
    }

    public static com.yorha.proto.SsClanRank.GetTopClanRankInfoAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetTopClanRankInfoAns>
        PARSER = new com.google.protobuf.AbstractParser<GetTopClanRankInfoAns>() {
      @java.lang.Override
      public GetTopClanRankInfoAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetTopClanRankInfoAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetTopClanRankInfoAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetTopClanRankInfoAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanRank.GetTopClanRankInfoAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetClanRankPageInfoAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetClanRankPageInfoAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 rankId = 1;</code>
     * @return Whether the rankId field is set.
     */
    boolean hasRankId();
    /**
     * <code>optional int32 rankId = 1;</code>
     * @return The rankId.
     */
    int getRankId();

    /**
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <code>optional int64 memberId = 3;</code>
     * @return Whether the memberId field is set.
     */
    boolean hasMemberId();
    /**
     * <code>optional int64 memberId = 3;</code>
     * @return The memberId.
     */
    long getMemberId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetClanRankPageInfoAsk}
   */
  public static final class GetClanRankPageInfoAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetClanRankPageInfoAsk)
      GetClanRankPageInfoAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetClanRankPageInfoAsk.newBuilder() to construct.
    private GetClanRankPageInfoAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetClanRankPageInfoAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetClanRankPageInfoAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetClanRankPageInfoAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              rankId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              page_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              memberId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk.class, com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk.Builder.class);
    }

    private int bitField0_;
    public static final int RANKID_FIELD_NUMBER = 1;
    private int rankId_;
    /**
     * <code>optional int32 rankId = 1;</code>
     * @return Whether the rankId field is set.
     */
    @java.lang.Override
    public boolean hasRankId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 rankId = 1;</code>
     * @return The rankId.
     */
    @java.lang.Override
    public int getRankId() {
      return rankId_;
    }

    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_;
    /**
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int MEMBERID_FIELD_NUMBER = 3;
    private long memberId_;
    /**
     * <code>optional int64 memberId = 3;</code>
     * @return Whether the memberId field is set.
     */
    @java.lang.Override
    public boolean hasMemberId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 memberId = 3;</code>
     * @return The memberId.
     */
    @java.lang.Override
    public long getMemberId() {
      return memberId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, rankId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, page_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, memberId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rankId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, memberId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk other = (com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk) obj;

      if (hasRankId() != other.hasRankId()) return false;
      if (hasRankId()) {
        if (getRankId()
            != other.getRankId()) return false;
      }
      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (hasMemberId() != other.hasMemberId()) return false;
      if (hasMemberId()) {
        if (getMemberId()
            != other.getMemberId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRankId()) {
        hash = (37 * hash) + RANKID_FIELD_NUMBER;
        hash = (53 * hash) + getRankId();
      }
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      if (hasMemberId()) {
        hash = (37 * hash) + MEMBERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMemberId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetClanRankPageInfoAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetClanRankPageInfoAsk)
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk.class, com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rankId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        memberId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk build() {
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk buildPartial() {
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk result = new com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rankId_ = rankId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.memberId_ = memberId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk) {
          return mergeFrom((com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk other) {
        if (other == com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk.getDefaultInstance()) return this;
        if (other.hasRankId()) {
          setRankId(other.getRankId());
        }
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        if (other.hasMemberId()) {
          setMemberId(other.getMemberId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int rankId_ ;
      /**
       * <code>optional int32 rankId = 1;</code>
       * @return Whether the rankId field is set.
       */
      @java.lang.Override
      public boolean hasRankId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 rankId = 1;</code>
       * @return The rankId.
       */
      @java.lang.Override
      public int getRankId() {
        return rankId_;
      }
      /**
       * <code>optional int32 rankId = 1;</code>
       * @param value The rankId to set.
       * @return This builder for chaining.
       */
      public Builder setRankId(int value) {
        bitField0_ |= 0x00000001;
        rankId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 rankId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rankId_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <code>optional int32 page = 2;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000002;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        page_ = 0;
        onChanged();
        return this;
      }

      private long memberId_ ;
      /**
       * <code>optional int64 memberId = 3;</code>
       * @return Whether the memberId field is set.
       */
      @java.lang.Override
      public boolean hasMemberId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 memberId = 3;</code>
       * @return The memberId.
       */
      @java.lang.Override
      public long getMemberId() {
        return memberId_;
      }
      /**
       * <code>optional int64 memberId = 3;</code>
       * @param value The memberId to set.
       * @return This builder for chaining.
       */
      public Builder setMemberId(long value) {
        bitField0_ |= 0x00000004;
        memberId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 memberId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemberId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        memberId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetClanRankPageInfoAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetClanRankPageInfoAsk)
    private static final com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk();
    }

    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetClanRankPageInfoAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetClanRankPageInfoAsk>() {
      @java.lang.Override
      public GetClanRankPageInfoAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetClanRankPageInfoAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetClanRankPageInfoAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetClanRankPageInfoAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanRank.GetClanRankPageInfoAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetClanRankPageInfoAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetClanRankPageInfoAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 rankId = 1;</code>
     * @return Whether the rankId field is set.
     */
    boolean hasRankId();
    /**
     * <code>optional int32 rankId = 1;</code>
     * @return The rankId.
     */
    int getRankId();

    /**
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return Whether the total field is set.
     */
    boolean hasTotal();
    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return The total.
     */
    int getTotal();

    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> 
        getDtoList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTO getDto(int index);
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    int getDtoCount();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index);

    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return Whether the myRankInfoDTO field is set.
     */
    boolean hasMyRankInfoDTO();
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return The myRankInfoDTO.
     */
    com.yorha.proto.StructMsg.RankInfoDTO getMyRankInfoDTO();
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     */
    com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getMyRankInfoDTOOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetClanRankPageInfoAns}
   */
  public static final class GetClanRankPageInfoAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetClanRankPageInfoAns)
      GetClanRankPageInfoAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetClanRankPageInfoAns.newBuilder() to construct.
    private GetClanRankPageInfoAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetClanRankPageInfoAns() {
      dto_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetClanRankPageInfoAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetClanRankPageInfoAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              rankId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              page_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              total_ = input.readInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) != 0)) {
                dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>();
                mutable_bitField0_ |= 0x00000008;
              }
              dto_.add(
                  input.readMessage(com.yorha.proto.StructMsg.RankInfoDTO.PARSER, extensionRegistry));
              break;
            }
            case 42: {
              com.yorha.proto.StructMsg.RankInfoDTO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = myRankInfoDTO_.toBuilder();
              }
              myRankInfoDTO_ = input.readMessage(com.yorha.proto.StructMsg.RankInfoDTO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(myRankInfoDTO_);
                myRankInfoDTO_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) != 0)) {
          dto_ = java.util.Collections.unmodifiableList(dto_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanRank.GetClanRankPageInfoAns.class, com.yorha.proto.SsClanRank.GetClanRankPageInfoAns.Builder.class);
    }

    private int bitField0_;
    public static final int RANKID_FIELD_NUMBER = 1;
    private int rankId_;
    /**
     * <code>optional int32 rankId = 1;</code>
     * @return Whether the rankId field is set.
     */
    @java.lang.Override
    public boolean hasRankId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 rankId = 1;</code>
     * @return The rankId.
     */
    @java.lang.Override
    public int getRankId() {
      return rankId_;
    }

    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_;
    /**
     * <code>optional int32 page = 2;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int TOTAL_FIELD_NUMBER = 3;
    private int total_;
    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return Whether the total field is set.
     */
    @java.lang.Override
    public boolean hasTotal() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 total = 3;</code>
     * @return The total.
     */
    @java.lang.Override
    public int getTotal() {
      return total_;
    }

    public static final int DTO_FIELD_NUMBER = 4;
    private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_;
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
        getDtoOrBuilderList() {
      return dto_;
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public int getDtoCount() {
      return dto_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
      return dto_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
        int index) {
      return dto_.get(index);
    }

    public static final int MYRANKINFODTO_FIELD_NUMBER = 5;
    private com.yorha.proto.StructMsg.RankInfoDTO myRankInfoDTO_;
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return Whether the myRankInfoDTO field is set.
     */
    @java.lang.Override
    public boolean hasMyRankInfoDTO() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     * @return The myRankInfoDTO.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTO getMyRankInfoDTO() {
      return myRankInfoDTO_ == null ? com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
    }
    /**
     * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getMyRankInfoDTOOrBuilder() {
      return myRankInfoDTO_ == null ? com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, rankId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, page_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, total_);
      }
      for (int i = 0; i < dto_.size(); i++) {
        output.writeMessage(4, dto_.get(i));
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(5, getMyRankInfoDTO());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rankId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, total_);
      }
      for (int i = 0; i < dto_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, dto_.get(i));
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getMyRankInfoDTO());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanRank.GetClanRankPageInfoAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanRank.GetClanRankPageInfoAns other = (com.yorha.proto.SsClanRank.GetClanRankPageInfoAns) obj;

      if (hasRankId() != other.hasRankId()) return false;
      if (hasRankId()) {
        if (getRankId()
            != other.getRankId()) return false;
      }
      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (hasTotal() != other.hasTotal()) return false;
      if (hasTotal()) {
        if (getTotal()
            != other.getTotal()) return false;
      }
      if (!getDtoList()
          .equals(other.getDtoList())) return false;
      if (hasMyRankInfoDTO() != other.hasMyRankInfoDTO()) return false;
      if (hasMyRankInfoDTO()) {
        if (!getMyRankInfoDTO()
            .equals(other.getMyRankInfoDTO())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRankId()) {
        hash = (37 * hash) + RANKID_FIELD_NUMBER;
        hash = (53 * hash) + getRankId();
      }
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      if (hasTotal()) {
        hash = (37 * hash) + TOTAL_FIELD_NUMBER;
        hash = (53 * hash) + getTotal();
      }
      if (getDtoCount() > 0) {
        hash = (37 * hash) + DTO_FIELD_NUMBER;
        hash = (53 * hash) + getDtoList().hashCode();
      }
      if (hasMyRankInfoDTO()) {
        hash = (37 * hash) + MYRANKINFODTO_FIELD_NUMBER;
        hash = (53 * hash) + getMyRankInfoDTO().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanRank.GetClanRankPageInfoAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetClanRankPageInfoAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetClanRankPageInfoAns)
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanRank.GetClanRankPageInfoAns.class, com.yorha.proto.SsClanRank.GetClanRankPageInfoAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanRank.GetClanRankPageInfoAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDtoFieldBuilder();
          getMyRankInfoDTOFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rankId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        total_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          dtoBuilder_.clear();
        }
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTO_ = null;
        } else {
          myRankInfoDTOBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_GetClanRankPageInfoAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetClanRankPageInfoAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanRank.GetClanRankPageInfoAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetClanRankPageInfoAns build() {
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.GetClanRankPageInfoAns buildPartial() {
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAns result = new com.yorha.proto.SsClanRank.GetClanRankPageInfoAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rankId_ = rankId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.total_ = total_;
          to_bitField0_ |= 0x00000004;
        }
        if (dtoBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            dto_ = java.util.Collections.unmodifiableList(dto_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.dto_ = dto_;
        } else {
          result.dto_ = dtoBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (myRankInfoDTOBuilder_ == null) {
            result.myRankInfoDTO_ = myRankInfoDTO_;
          } else {
            result.myRankInfoDTO_ = myRankInfoDTOBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanRank.GetClanRankPageInfoAns) {
          return mergeFrom((com.yorha.proto.SsClanRank.GetClanRankPageInfoAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanRank.GetClanRankPageInfoAns other) {
        if (other == com.yorha.proto.SsClanRank.GetClanRankPageInfoAns.getDefaultInstance()) return this;
        if (other.hasRankId()) {
          setRankId(other.getRankId());
        }
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        if (other.hasTotal()) {
          setTotal(other.getTotal());
        }
        if (dtoBuilder_ == null) {
          if (!other.dto_.isEmpty()) {
            if (dto_.isEmpty()) {
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureDtoIsMutable();
              dto_.addAll(other.dto_);
            }
            onChanged();
          }
        } else {
          if (!other.dto_.isEmpty()) {
            if (dtoBuilder_.isEmpty()) {
              dtoBuilder_.dispose();
              dtoBuilder_ = null;
              dto_ = other.dto_;
              bitField0_ = (bitField0_ & ~0x00000008);
              dtoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDtoFieldBuilder() : null;
            } else {
              dtoBuilder_.addAllMessages(other.dto_);
            }
          }
        }
        if (other.hasMyRankInfoDTO()) {
          mergeMyRankInfoDTO(other.getMyRankInfoDTO());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanRank.GetClanRankPageInfoAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanRank.GetClanRankPageInfoAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int rankId_ ;
      /**
       * <code>optional int32 rankId = 1;</code>
       * @return Whether the rankId field is set.
       */
      @java.lang.Override
      public boolean hasRankId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 rankId = 1;</code>
       * @return The rankId.
       */
      @java.lang.Override
      public int getRankId() {
        return rankId_;
      }
      /**
       * <code>optional int32 rankId = 1;</code>
       * @param value The rankId to set.
       * @return This builder for chaining.
       */
      public Builder setRankId(int value) {
        bitField0_ |= 0x00000001;
        rankId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 rankId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rankId_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <code>optional int32 page = 2;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000002;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        page_ = 0;
        onChanged();
        return this;
      }

      private int total_ ;
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @return Whether the total field is set.
       */
      @java.lang.Override
      public boolean hasTotal() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @return The total.
       */
      @java.lang.Override
      public int getTotal() {
        return total_;
      }
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @param value The total to set.
       * @return This builder for chaining.
       */
      public Builder setTotal(int value) {
        bitField0_ |= 0x00000004;
        total_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 total = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotal() {
        bitField0_ = (bitField0_ & ~0x00000004);
        total_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> dto_ =
        java.util.Collections.emptyList();
      private void ensureDtoIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          dto_ = new java.util.ArrayList<com.yorha.proto.StructMsg.RankInfoDTO>(dto_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> dtoBuilder_;

      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO> getDtoList() {
        if (dtoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dto_);
        } else {
          return dtoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public int getDtoCount() {
        if (dtoBuilder_ == null) {
          return dto_.size();
        } else {
          return dtoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO getDto(int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);
        } else {
          return dtoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.set(index, value);
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder setDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.set(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDtoIsMutable();
          dto_.add(index, value);
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(
          com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addDto(
          int index, com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.add(index, builderForValue.build());
          onChanged();
        } else {
          dtoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder addAllDto(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.RankInfoDTO> values) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dto_);
          onChanged();
        } else {
          dtoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder clearDto() {
        if (dtoBuilder_ == null) {
          dto_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          dtoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public Builder removeDto(int index) {
        if (dtoBuilder_ == null) {
          ensureDtoIsMutable();
          dto_.remove(index);
          onChanged();
        } else {
          dtoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder getDtoBuilder(
          int index) {
        return getDtoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getDtoOrBuilder(
          int index) {
        if (dtoBuilder_ == null) {
          return dto_.get(index);  } else {
          return dtoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
           getDtoOrBuilderList() {
        if (dtoBuilder_ != null) {
          return dtoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dto_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder() {
        return getDtoFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder addDtoBuilder(
          int index) {
        return getDtoFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.RankInfoDTO dto = 4;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.RankInfoDTO.Builder> 
           getDtoBuilderList() {
        return getDtoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
          getDtoFieldBuilder() {
        if (dtoBuilder_ == null) {
          dtoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder>(
                  dto_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          dto_ = null;
        }
        return dtoBuilder_;
      }

      private com.yorha.proto.StructMsg.RankInfoDTO myRankInfoDTO_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> myRankInfoDTOBuilder_;
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       * @return Whether the myRankInfoDTO field is set.
       */
      public boolean hasMyRankInfoDTO() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       * @return The myRankInfoDTO.
       */
      public com.yorha.proto.StructMsg.RankInfoDTO getMyRankInfoDTO() {
        if (myRankInfoDTOBuilder_ == null) {
          return myRankInfoDTO_ == null ? com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
        } else {
          return myRankInfoDTOBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder setMyRankInfoDTO(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (myRankInfoDTOBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          myRankInfoDTO_ = value;
          onChanged();
        } else {
          myRankInfoDTOBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder setMyRankInfoDTO(
          com.yorha.proto.StructMsg.RankInfoDTO.Builder builderForValue) {
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTO_ = builderForValue.build();
          onChanged();
        } else {
          myRankInfoDTOBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder mergeMyRankInfoDTO(com.yorha.proto.StructMsg.RankInfoDTO value) {
        if (myRankInfoDTOBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              myRankInfoDTO_ != null &&
              myRankInfoDTO_ != com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance()) {
            myRankInfoDTO_ =
              com.yorha.proto.StructMsg.RankInfoDTO.newBuilder(myRankInfoDTO_).mergeFrom(value).buildPartial();
          } else {
            myRankInfoDTO_ = value;
          }
          onChanged();
        } else {
          myRankInfoDTOBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public Builder clearMyRankInfoDTO() {
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTO_ = null;
          onChanged();
        } else {
          myRankInfoDTOBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTO.Builder getMyRankInfoDTOBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getMyRankInfoDTOFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      public com.yorha.proto.StructMsg.RankInfoDTOOrBuilder getMyRankInfoDTOOrBuilder() {
        if (myRankInfoDTOBuilder_ != null) {
          return myRankInfoDTOBuilder_.getMessageOrBuilder();
        } else {
          return myRankInfoDTO_ == null ?
              com.yorha.proto.StructMsg.RankInfoDTO.getDefaultInstance() : myRankInfoDTO_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.RankInfoDTO myRankInfoDTO = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder> 
          getMyRankInfoDTOFieldBuilder() {
        if (myRankInfoDTOBuilder_ == null) {
          myRankInfoDTOBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.RankInfoDTO, com.yorha.proto.StructMsg.RankInfoDTO.Builder, com.yorha.proto.StructMsg.RankInfoDTOOrBuilder>(
                  getMyRankInfoDTO(),
                  getParentForChildren(),
                  isClean());
          myRankInfoDTO_ = null;
        }
        return myRankInfoDTOBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetClanRankPageInfoAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetClanRankPageInfoAns)
    private static final com.yorha.proto.SsClanRank.GetClanRankPageInfoAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanRank.GetClanRankPageInfoAns();
    }

    public static com.yorha.proto.SsClanRank.GetClanRankPageInfoAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetClanRankPageInfoAns>
        PARSER = new com.google.protobuf.AbstractParser<GetClanRankPageInfoAns>() {
      @java.lang.Override
      public GetClanRankPageInfoAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetClanRankPageInfoAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetClanRankPageInfoAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetClanRankPageInfoAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanRank.GetClanRankPageInfoAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpdateClanRankingCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.UpdateClanRankingCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 排行的类型
     * </pre>
     *
     * <code>optional int32 rankId = 1;</code>
     * @return Whether the rankId field is set.
     */
    boolean hasRankId();
    /**
     * <pre>
     * 排行的类型
     * </pre>
     *
     * <code>optional int32 rankId = 1;</code>
     * @return The rankId.
     */
    int getRankId();

    /**
     * <pre>
     * 个体id
     * </pre>
     *
     * <code>optional int64 memberId = 2;</code>
     * @return Whether the memberId field is set.
     */
    boolean hasMemberId();
    /**
     * <pre>
     * 个体id
     * </pre>
     *
     * <code>optional int64 memberId = 2;</code>
     * @return The memberId.
     */
    long getMemberId();

    /**
     * <pre>
     * 排行的分数
     * </pre>
     *
     * <code>optional int64 score = 3;</code>
     * @return Whether the score field is set.
     */
    boolean hasScore();
    /**
     * <pre>
     * 排行的分数
     * </pre>
     *
     * <code>optional int64 score = 3;</code>
     * @return The score.
     */
    long getScore();

    /**
     * <pre>
     * 是否增量更新
     * </pre>
     *
     * <code>optional bool increase = 4;</code>
     * @return Whether the increase field is set.
     */
    boolean hasIncrease();
    /**
     * <pre>
     * 是否增量更新
     * </pre>
     *
     * <code>optional bool increase = 4;</code>
     * @return The increase.
     */
    boolean getIncrease();
  }
  /**
   * Protobuf type {@code com.yorha.proto.UpdateClanRankingCmd}
   */
  public static final class UpdateClanRankingCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.UpdateClanRankingCmd)
      UpdateClanRankingCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpdateClanRankingCmd.newBuilder() to construct.
    private UpdateClanRankingCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpdateClanRankingCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpdateClanRankingCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UpdateClanRankingCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              rankId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              memberId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              score_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              increase_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_UpdateClanRankingCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_UpdateClanRankingCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanRank.UpdateClanRankingCmd.class, com.yorha.proto.SsClanRank.UpdateClanRankingCmd.Builder.class);
    }

    private int bitField0_;
    public static final int RANKID_FIELD_NUMBER = 1;
    private int rankId_;
    /**
     * <pre>
     * 排行的类型
     * </pre>
     *
     * <code>optional int32 rankId = 1;</code>
     * @return Whether the rankId field is set.
     */
    @java.lang.Override
    public boolean hasRankId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 排行的类型
     * </pre>
     *
     * <code>optional int32 rankId = 1;</code>
     * @return The rankId.
     */
    @java.lang.Override
    public int getRankId() {
      return rankId_;
    }

    public static final int MEMBERID_FIELD_NUMBER = 2;
    private long memberId_;
    /**
     * <pre>
     * 个体id
     * </pre>
     *
     * <code>optional int64 memberId = 2;</code>
     * @return Whether the memberId field is set.
     */
    @java.lang.Override
    public boolean hasMemberId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 个体id
     * </pre>
     *
     * <code>optional int64 memberId = 2;</code>
     * @return The memberId.
     */
    @java.lang.Override
    public long getMemberId() {
      return memberId_;
    }

    public static final int SCORE_FIELD_NUMBER = 3;
    private long score_;
    /**
     * <pre>
     * 排行的分数
     * </pre>
     *
     * <code>optional int64 score = 3;</code>
     * @return Whether the score field is set.
     */
    @java.lang.Override
    public boolean hasScore() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 排行的分数
     * </pre>
     *
     * <code>optional int64 score = 3;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }

    public static final int INCREASE_FIELD_NUMBER = 4;
    private boolean increase_;
    /**
     * <pre>
     * 是否增量更新
     * </pre>
     *
     * <code>optional bool increase = 4;</code>
     * @return Whether the increase field is set.
     */
    @java.lang.Override
    public boolean hasIncrease() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 是否增量更新
     * </pre>
     *
     * <code>optional bool increase = 4;</code>
     * @return The increase.
     */
    @java.lang.Override
    public boolean getIncrease() {
      return increase_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, rankId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, memberId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, score_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, increase_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rankId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, memberId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, score_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, increase_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanRank.UpdateClanRankingCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanRank.UpdateClanRankingCmd other = (com.yorha.proto.SsClanRank.UpdateClanRankingCmd) obj;

      if (hasRankId() != other.hasRankId()) return false;
      if (hasRankId()) {
        if (getRankId()
            != other.getRankId()) return false;
      }
      if (hasMemberId() != other.hasMemberId()) return false;
      if (hasMemberId()) {
        if (getMemberId()
            != other.getMemberId()) return false;
      }
      if (hasScore() != other.hasScore()) return false;
      if (hasScore()) {
        if (getScore()
            != other.getScore()) return false;
      }
      if (hasIncrease() != other.hasIncrease()) return false;
      if (hasIncrease()) {
        if (getIncrease()
            != other.getIncrease()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRankId()) {
        hash = (37 * hash) + RANKID_FIELD_NUMBER;
        hash = (53 * hash) + getRankId();
      }
      if (hasMemberId()) {
        hash = (37 * hash) + MEMBERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMemberId());
      }
      if (hasScore()) {
        hash = (37 * hash) + SCORE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getScore());
      }
      if (hasIncrease()) {
        hash = (37 * hash) + INCREASE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIncrease());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanRank.UpdateClanRankingCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.UpdateClanRankingCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.UpdateClanRankingCmd)
        com.yorha.proto.SsClanRank.UpdateClanRankingCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_UpdateClanRankingCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_UpdateClanRankingCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanRank.UpdateClanRankingCmd.class, com.yorha.proto.SsClanRank.UpdateClanRankingCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanRank.UpdateClanRankingCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rankId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        memberId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        score_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        increase_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanRank.internal_static_com_yorha_proto_UpdateClanRankingCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.UpdateClanRankingCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanRank.UpdateClanRankingCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.UpdateClanRankingCmd build() {
        com.yorha.proto.SsClanRank.UpdateClanRankingCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanRank.UpdateClanRankingCmd buildPartial() {
        com.yorha.proto.SsClanRank.UpdateClanRankingCmd result = new com.yorha.proto.SsClanRank.UpdateClanRankingCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rankId_ = rankId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.memberId_ = memberId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.score_ = score_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.increase_ = increase_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanRank.UpdateClanRankingCmd) {
          return mergeFrom((com.yorha.proto.SsClanRank.UpdateClanRankingCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanRank.UpdateClanRankingCmd other) {
        if (other == com.yorha.proto.SsClanRank.UpdateClanRankingCmd.getDefaultInstance()) return this;
        if (other.hasRankId()) {
          setRankId(other.getRankId());
        }
        if (other.hasMemberId()) {
          setMemberId(other.getMemberId());
        }
        if (other.hasScore()) {
          setScore(other.getScore());
        }
        if (other.hasIncrease()) {
          setIncrease(other.getIncrease());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanRank.UpdateClanRankingCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanRank.UpdateClanRankingCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int rankId_ ;
      /**
       * <pre>
       * 排行的类型
       * </pre>
       *
       * <code>optional int32 rankId = 1;</code>
       * @return Whether the rankId field is set.
       */
      @java.lang.Override
      public boolean hasRankId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 排行的类型
       * </pre>
       *
       * <code>optional int32 rankId = 1;</code>
       * @return The rankId.
       */
      @java.lang.Override
      public int getRankId() {
        return rankId_;
      }
      /**
       * <pre>
       * 排行的类型
       * </pre>
       *
       * <code>optional int32 rankId = 1;</code>
       * @param value The rankId to set.
       * @return This builder for chaining.
       */
      public Builder setRankId(int value) {
        bitField0_ |= 0x00000001;
        rankId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行的类型
       * </pre>
       *
       * <code>optional int32 rankId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rankId_ = 0;
        onChanged();
        return this;
      }

      private long memberId_ ;
      /**
       * <pre>
       * 个体id
       * </pre>
       *
       * <code>optional int64 memberId = 2;</code>
       * @return Whether the memberId field is set.
       */
      @java.lang.Override
      public boolean hasMemberId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 个体id
       * </pre>
       *
       * <code>optional int64 memberId = 2;</code>
       * @return The memberId.
       */
      @java.lang.Override
      public long getMemberId() {
        return memberId_;
      }
      /**
       * <pre>
       * 个体id
       * </pre>
       *
       * <code>optional int64 memberId = 2;</code>
       * @param value The memberId to set.
       * @return This builder for chaining.
       */
      public Builder setMemberId(long value) {
        bitField0_ |= 0x00000002;
        memberId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 个体id
       * </pre>
       *
       * <code>optional int64 memberId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemberId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        memberId_ = 0L;
        onChanged();
        return this;
      }

      private long score_ ;
      /**
       * <pre>
       * 排行的分数
       * </pre>
       *
       * <code>optional int64 score = 3;</code>
       * @return Whether the score field is set.
       */
      @java.lang.Override
      public boolean hasScore() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 排行的分数
       * </pre>
       *
       * <code>optional int64 score = 3;</code>
       * @return The score.
       */
      @java.lang.Override
      public long getScore() {
        return score_;
      }
      /**
       * <pre>
       * 排行的分数
       * </pre>
       *
       * <code>optional int64 score = 3;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(long value) {
        bitField0_ |= 0x00000004;
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行的分数
       * </pre>
       *
       * <code>optional int64 score = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000004);
        score_ = 0L;
        onChanged();
        return this;
      }

      private boolean increase_ ;
      /**
       * <pre>
       * 是否增量更新
       * </pre>
       *
       * <code>optional bool increase = 4;</code>
       * @return Whether the increase field is set.
       */
      @java.lang.Override
      public boolean hasIncrease() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 是否增量更新
       * </pre>
       *
       * <code>optional bool increase = 4;</code>
       * @return The increase.
       */
      @java.lang.Override
      public boolean getIncrease() {
        return increase_;
      }
      /**
       * <pre>
       * 是否增量更新
       * </pre>
       *
       * <code>optional bool increase = 4;</code>
       * @param value The increase to set.
       * @return This builder for chaining.
       */
      public Builder setIncrease(boolean value) {
        bitField0_ |= 0x00000008;
        increase_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否增量更新
       * </pre>
       *
       * <code>optional bool increase = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIncrease() {
        bitField0_ = (bitField0_ & ~0x00000008);
        increase_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.UpdateClanRankingCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.UpdateClanRankingCmd)
    private static final com.yorha.proto.SsClanRank.UpdateClanRankingCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanRank.UpdateClanRankingCmd();
    }

    public static com.yorha.proto.SsClanRank.UpdateClanRankingCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<UpdateClanRankingCmd>
        PARSER = new com.google.protobuf.AbstractParser<UpdateClanRankingCmd>() {
      @java.lang.Override
      public UpdateClanRankingCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UpdateClanRankingCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UpdateClanRankingCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpdateClanRankingCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanRank.UpdateClanRankingCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetTopClanRankInfoAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetTopClanRankInfoAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetTopClanRankInfoAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetTopClanRankInfoAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetClanRankPageInfoAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetClanRankPageInfoAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetClanRankPageInfoAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetClanRankPageInfoAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_UpdateClanRankingCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_UpdateClanRankingCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$ss_proto/gen/clan/ss_clan_rank.proto\022\017" +
      "com.yorha.proto\032$ss_proto/gen/common/str" +
      "uct_msg.proto\"\027\n\025GetTopClanRankInfoAsk\"Y" +
      "\n\025GetTopClanRankInfoAns\022)\n\003dto\030\001 \003(\0132\034.c" +
      "om.yorha.proto.RankInfoDTO\022\025\n\rrankResetT" +
      "sMs\030\002 \001(\003\"H\n\026GetClanRankPageInfoAsk\022\016\n\006r" +
      "ankId\030\001 \001(\005\022\014\n\004page\030\002 \001(\005\022\020\n\010memberId\030\003 " +
      "\001(\003\"\245\001\n\026GetClanRankPageInfoAns\022\016\n\006rankId" +
      "\030\001 \001(\005\022\014\n\004page\030\002 \001(\005\022\r\n\005total\030\003 \001(\005\022)\n\003d" +
      "to\030\004 \003(\0132\034.com.yorha.proto.RankInfoDTO\0223" +
      "\n\rmyRankInfoDTO\030\005 \001(\0132\034.com.yorha.proto." +
      "RankInfoDTO\"Y\n\024UpdateClanRankingCmd\022\016\n\006r" +
      "ankId\030\001 \001(\005\022\020\n\010memberId\030\002 \001(\003\022\r\n\005score\030\003" +
      " \001(\003\022\020\n\010increase\030\004 \001(\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_GetTopClanRankInfoAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_GetTopClanRankInfoAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetTopClanRankInfoAsk_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_GetTopClanRankInfoAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_GetTopClanRankInfoAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetTopClanRankInfoAns_descriptor,
        new java.lang.String[] { "Dto", "RankResetTsMs", });
    internal_static_com_yorha_proto_GetClanRankPageInfoAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_GetClanRankPageInfoAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetClanRankPageInfoAsk_descriptor,
        new java.lang.String[] { "RankId", "Page", "MemberId", });
    internal_static_com_yorha_proto_GetClanRankPageInfoAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_GetClanRankPageInfoAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetClanRankPageInfoAns_descriptor,
        new java.lang.String[] { "RankId", "Page", "Total", "Dto", "MyRankInfoDTO", });
    internal_static_com_yorha_proto_UpdateClanRankingCmd_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_UpdateClanRankingCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_UpdateClanRankingCmd_descriptor,
        new java.lang.String[] { "RankId", "MemberId", "Score", "Increase", });
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

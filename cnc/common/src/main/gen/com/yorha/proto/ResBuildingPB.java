// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/resBuilding/resBuildingPB.proto

package com.yorha.proto;

public final class ResBuildingPB {
  private ResBuildingPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ResBuildingEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ResBuildingEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 资源田状态  正常/采集中/战斗
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <pre>
     * 资源田状态  正常/采集中/战斗
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
     * @return The state.
     */
    com.yorha.proto.CommonEnum.ResourceBuildingState getState();

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 4;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 4;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 5;</code>
     * @return Whether the clanSimpleName field is set.
     */
    boolean hasClanSimpleName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 5;</code>
     * @return The clanSimpleName.
     */
    java.lang.String getClanSimpleName();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 5;</code>
     * @return The bytes for clanSimpleName.
     */
    com.google.protobuf.ByteString
        getClanSimpleNameBytes();

    /**
     * <pre>
     * 当前资源量
     * </pre>
     *
     * <code>optional int32 curNum = 6;</code>
     * @return Whether the curNum field is set.
     */
    boolean hasCurNum();
    /**
     * <pre>
     * 当前资源量
     * </pre>
     *
     * <code>optional int32 curNum = 6;</code>
     * @return The curNum.
     */
    int getCurNum();

    /**
     * <pre>
     * 采集情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
     * @return Whether the collect field is set.
     */
    boolean hasCollect();
    /**
     * <pre>
     * 采集情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
     * @return The collect.
     */
    com.yorha.proto.ResBuildingPB.CollectInfoPB getCollect();
    /**
     * <pre>
     * 采集情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
     */
    com.yorha.proto.ResBuildingPB.CollectInfoPBOrBuilder getCollectOrBuilder();

    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return Whether the arrow field is set.
     */
    boolean hasArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return The arrow.
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder();

    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
     * @return Whether the expression field is set.
     */
    boolean hasExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
     * @return The expression.
     */
    com.yorha.proto.StructPB.ExpressionPB getExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
     */
    com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ResBuildingEntityPB}
   */
  public static final class ResBuildingEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ResBuildingEntityPB)
      ResBuildingEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResBuildingEntityPB.newBuilder() to construct.
    private ResBuildingEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResBuildingEntityPB() {
      state_ = 0;
      clanSimpleName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResBuildingEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResBuildingEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              templateId_ = input.readInt32();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ResourceBuildingState value = com.yorha.proto.CommonEnum.ResourceBuildingState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                state_ = rawValue;
              }
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              clanId_ = input.readInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              clanSimpleName_ = bs;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              curNum_ = input.readInt32();
              break;
            }
            case 58: {
              com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = collect_.toBuilder();
              }
              collect_ = input.readMessage(com.yorha.proto.ResBuildingPB.CollectInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(collect_);
                collect_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 186: {
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000080) != 0)) {
                subBuilder = arrow_.toBuilder();
              }
              arrow_ = input.readMessage(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(arrow_);
                arrow_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000080;
              break;
            }
            case 194: {
              com.yorha.proto.StructPB.ExpressionPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) != 0)) {
                subBuilder = expression_.toBuilder();
              }
              expression_ = input.readMessage(com.yorha.proto.StructPB.ExpressionPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(expression_);
                expression_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_ResBuildingEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_ResBuildingEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.class, com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int TEMPLATEID_FIELD_NUMBER = 1;
    private int templateId_;
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 模型id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int STATE_FIELD_NUMBER = 2;
    private int state_;
    /**
     * <pre>
     * 资源田状态  正常/采集中/战斗
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override public boolean hasState() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 资源田状态  正常/采集中/战斗
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
     * @return The state.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ResourceBuildingState getState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ResourceBuildingState result = com.yorha.proto.CommonEnum.ResourceBuildingState.valueOf(state_);
      return result == null ? com.yorha.proto.CommonEnum.ResourceBuildingState.RBS_IDLE : result;
    }

    public static final int POINT_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int CLANID_FIELD_NUMBER = 4;
    private long clanId_;
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 4;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 4;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int CLANSIMPLENAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object clanSimpleName_;
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 5;</code>
     * @return Whether the clanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasClanSimpleName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 5;</code>
     * @return The clanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getClanSimpleName() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 5;</code>
     * @return The bytes for clanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSimpleNameBytes() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CURNUM_FIELD_NUMBER = 6;
    private int curNum_;
    /**
     * <pre>
     * 当前资源量
     * </pre>
     *
     * <code>optional int32 curNum = 6;</code>
     * @return Whether the curNum field is set.
     */
    @java.lang.Override
    public boolean hasCurNum() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 当前资源量
     * </pre>
     *
     * <code>optional int32 curNum = 6;</code>
     * @return The curNum.
     */
    @java.lang.Override
    public int getCurNum() {
      return curNum_;
    }

    public static final int COLLECT_FIELD_NUMBER = 7;
    private com.yorha.proto.ResBuildingPB.CollectInfoPB collect_;
    /**
     * <pre>
     * 采集情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
     * @return Whether the collect field is set.
     */
    @java.lang.Override
    public boolean hasCollect() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 采集情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
     * @return The collect.
     */
    @java.lang.Override
    public com.yorha.proto.ResBuildingPB.CollectInfoPB getCollect() {
      return collect_ == null ? com.yorha.proto.ResBuildingPB.CollectInfoPB.getDefaultInstance() : collect_;
    }
    /**
     * <pre>
     * 采集情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ResBuildingPB.CollectInfoPBOrBuilder getCollectOrBuilder() {
      return collect_ == null ? com.yorha.proto.ResBuildingPB.CollectInfoPB.getDefaultInstance() : collect_;
    }

    public static final int ARROW_FIELD_NUMBER = 23;
    private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return Whether the arrow field is set.
     */
    @java.lang.Override
    public boolean hasArrow() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     * @return The arrow.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }

    public static final int EXPRESSION_FIELD_NUMBER = 24;
    private com.yorha.proto.StructPB.ExpressionPB expression_;
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
     * @return Whether the expression field is set.
     */
    @java.lang.Override
    public boolean hasExpression() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
     * @return The expression.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPB getExpression() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, state_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, clanId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, curNum_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getCollect());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(23, getArrow());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeMessage(24, getExpression());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, state_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPoint());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, clanId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, curNum_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getCollect());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(23, getArrow());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(24, getExpression());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.ResBuildingPB.ResBuildingEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.ResBuildingPB.ResBuildingEntityPB other = (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) obj;

      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (state_ != other.state_) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasClanSimpleName() != other.hasClanSimpleName()) return false;
      if (hasClanSimpleName()) {
        if (!getClanSimpleName()
            .equals(other.getClanSimpleName())) return false;
      }
      if (hasCurNum() != other.hasCurNum()) return false;
      if (hasCurNum()) {
        if (getCurNum()
            != other.getCurNum()) return false;
      }
      if (hasCollect() != other.hasCollect()) return false;
      if (hasCollect()) {
        if (!getCollect()
            .equals(other.getCollect())) return false;
      }
      if (hasArrow() != other.hasArrow()) return false;
      if (hasArrow()) {
        if (!getArrow()
            .equals(other.getArrow())) return false;
      }
      if (hasExpression() != other.hasExpression()) return false;
      if (hasExpression()) {
        if (!getExpression()
            .equals(other.getExpression())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + state_;
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasClanSimpleName()) {
        hash = (37 * hash) + CLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSimpleName().hashCode();
      }
      if (hasCurNum()) {
        hash = (37 * hash) + CURNUM_FIELD_NUMBER;
        hash = (53 * hash) + getCurNum();
      }
      if (hasCollect()) {
        hash = (37 * hash) + COLLECT_FIELD_NUMBER;
        hash = (53 * hash) + getCollect().hashCode();
      }
      if (hasArrow()) {
        hash = (37 * hash) + ARROW_FIELD_NUMBER;
        hash = (53 * hash) + getArrow().hashCode();
      }
      if (hasExpression()) {
        hash = (37 * hash) + EXPRESSION_FIELD_NUMBER;
        hash = (53 * hash) + getExpression().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.ResBuildingPB.ResBuildingEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ResBuildingEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ResBuildingEntityPB)
        com.yorha.proto.ResBuildingPB.ResBuildingEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_ResBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_ResBuildingEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.class, com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getCollectFieldBuilder();
          getArrowFieldBuilder();
          getExpressionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        state_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        clanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        curNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        if (collectBuilder_ == null) {
          collect_ = null;
        } else {
          collectBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        if (arrowBuilder_ == null) {
          arrow_ = null;
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        if (expressionBuilder_ == null) {
          expression_ = null;
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_ResBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.ResBuildingEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.ResBuildingEntityPB build() {
        com.yorha.proto.ResBuildingPB.ResBuildingEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.ResBuildingEntityPB buildPartial() {
        com.yorha.proto.ResBuildingPB.ResBuildingEntityPB result = new com.yorha.proto.ResBuildingPB.ResBuildingEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.state_ = state_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.clanSimpleName_ = clanSimpleName_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.curNum_ = curNum_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (collectBuilder_ == null) {
            result.collect_ = collect_;
          } else {
            result.collect_ = collectBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          if (arrowBuilder_ == null) {
            result.arrow_ = arrow_;
          } else {
            result.arrow_ = arrowBuilder_.build();
          }
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          if (expressionBuilder_ == null) {
            result.expression_ = expression_;
          } else {
            result.expression_ = expressionBuilder_.build();
          }
          to_bitField0_ |= 0x00000100;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) {
          return mergeFrom((com.yorha.proto.ResBuildingPB.ResBuildingEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.ResBuildingPB.ResBuildingEntityPB other) {
        if (other == com.yorha.proto.ResBuildingPB.ResBuildingEntityPB.getDefaultInstance()) return this;
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasState()) {
          setState(other.getState());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasClanSimpleName()) {
          bitField0_ |= 0x00000010;
          clanSimpleName_ = other.clanSimpleName_;
          onChanged();
        }
        if (other.hasCurNum()) {
          setCurNum(other.getCurNum());
        }
        if (other.hasCollect()) {
          mergeCollect(other.getCollect());
        }
        if (other.hasArrow()) {
          mergeArrow(other.getArrow());
        }
        if (other.hasExpression()) {
          mergeExpression(other.getExpression());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.ResBuildingPB.ResBuildingEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.ResBuildingPB.ResBuildingEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int templateId_ ;
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000001;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模型id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private int state_ = 0;
      /**
       * <pre>
       * 资源田状态  正常/采集中/战斗
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override public boolean hasState() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 资源田状态  正常/采集中/战斗
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ResourceBuildingState getState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ResourceBuildingState result = com.yorha.proto.CommonEnum.ResourceBuildingState.valueOf(state_);
        return result == null ? com.yorha.proto.CommonEnum.ResourceBuildingState.RBS_IDLE : result;
      }
      /**
       * <pre>
       * 资源田状态  正常/采集中/战斗
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.yorha.proto.CommonEnum.ResourceBuildingState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 资源田状态  正常/采集中/战斗
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResourceBuildingState state = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        bitField0_ = (bitField0_ & ~0x00000002);
        state_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 4;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 4;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 4;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000008;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object clanSimpleName_ = "";
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 5;</code>
       * @return Whether the clanSimpleName field is set.
       */
      public boolean hasClanSimpleName() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 5;</code>
       * @return The clanSimpleName.
       */
      public java.lang.String getClanSimpleName() {
        java.lang.Object ref = clanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 5;</code>
       * @return The bytes for clanSimpleName.
       */
      public com.google.protobuf.ByteString
          getClanSimpleNameBytes() {
        java.lang.Object ref = clanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 5;</code>
       * @param value The clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        clanSimpleName_ = getDefaultInstance().getClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 5;</code>
       * @param value The bytes for clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }

      private int curNum_ ;
      /**
       * <pre>
       * 当前资源量
       * </pre>
       *
       * <code>optional int32 curNum = 6;</code>
       * @return Whether the curNum field is set.
       */
      @java.lang.Override
      public boolean hasCurNum() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 当前资源量
       * </pre>
       *
       * <code>optional int32 curNum = 6;</code>
       * @return The curNum.
       */
      @java.lang.Override
      public int getCurNum() {
        return curNum_;
      }
      /**
       * <pre>
       * 当前资源量
       * </pre>
       *
       * <code>optional int32 curNum = 6;</code>
       * @param value The curNum to set.
       * @return This builder for chaining.
       */
      public Builder setCurNum(int value) {
        bitField0_ |= 0x00000020;
        curNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前资源量
       * </pre>
       *
       * <code>optional int32 curNum = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurNum() {
        bitField0_ = (bitField0_ & ~0x00000020);
        curNum_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.ResBuildingPB.CollectInfoPB collect_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ResBuildingPB.CollectInfoPB, com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder, com.yorha.proto.ResBuildingPB.CollectInfoPBOrBuilder> collectBuilder_;
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       * @return Whether the collect field is set.
       */
      public boolean hasCollect() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       * @return The collect.
       */
      public com.yorha.proto.ResBuildingPB.CollectInfoPB getCollect() {
        if (collectBuilder_ == null) {
          return collect_ == null ? com.yorha.proto.ResBuildingPB.CollectInfoPB.getDefaultInstance() : collect_;
        } else {
          return collectBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       */
      public Builder setCollect(com.yorha.proto.ResBuildingPB.CollectInfoPB value) {
        if (collectBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          collect_ = value;
          onChanged();
        } else {
          collectBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       */
      public Builder setCollect(
          com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder builderForValue) {
        if (collectBuilder_ == null) {
          collect_ = builderForValue.build();
          onChanged();
        } else {
          collectBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       */
      public Builder mergeCollect(com.yorha.proto.ResBuildingPB.CollectInfoPB value) {
        if (collectBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              collect_ != null &&
              collect_ != com.yorha.proto.ResBuildingPB.CollectInfoPB.getDefaultInstance()) {
            collect_ =
              com.yorha.proto.ResBuildingPB.CollectInfoPB.newBuilder(collect_).mergeFrom(value).buildPartial();
          } else {
            collect_ = value;
          }
          onChanged();
        } else {
          collectBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       */
      public Builder clearCollect() {
        if (collectBuilder_ == null) {
          collect_ = null;
          onChanged();
        } else {
          collectBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       */
      public com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder getCollectBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getCollectFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       */
      public com.yorha.proto.ResBuildingPB.CollectInfoPBOrBuilder getCollectOrBuilder() {
        if (collectBuilder_ != null) {
          return collectBuilder_.getMessageOrBuilder();
        } else {
          return collect_ == null ?
              com.yorha.proto.ResBuildingPB.CollectInfoPB.getDefaultInstance() : collect_;
        }
      }
      /**
       * <pre>
       * 采集情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CollectInfoPB collect = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ResBuildingPB.CollectInfoPB, com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder, com.yorha.proto.ResBuildingPB.CollectInfoPBOrBuilder> 
          getCollectFieldBuilder() {
        if (collectBuilder_ == null) {
          collectBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ResBuildingPB.CollectInfoPB, com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder, com.yorha.proto.ResBuildingPB.CollectInfoPBOrBuilder>(
                  getCollect(),
                  getParentForChildren(),
                  isClean());
          collect_ = null;
        }
        return collectBuilder_;
      }

      private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> arrowBuilder_;
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       * @return Whether the arrow field is set.
       */
      public boolean hasArrow() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       * @return The arrow.
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
        if (arrowBuilder_ == null) {
          return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        } else {
          return arrowBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder setArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          arrow_ = value;
          onChanged();
        } else {
          arrowBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder setArrow(
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder builderForValue) {
        if (arrowBuilder_ == null) {
          arrow_ = builderForValue.build();
          onChanged();
        } else {
          arrowBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder mergeArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
              arrow_ != null &&
              arrow_ != com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance()) {
            arrow_ =
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.newBuilder(arrow_).mergeFrom(value).buildPartial();
          } else {
            arrow_ = value;
          }
          onChanged();
        } else {
          arrowBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public Builder clearArrow() {
        if (arrowBuilder_ == null) {
          arrow_ = null;
          onChanged();
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder getArrowBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getArrowFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
        if (arrowBuilder_ != null) {
          return arrowBuilder_.getMessageOrBuilder();
        } else {
          return arrow_ == null ?
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 23;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> 
          getArrowFieldBuilder() {
        if (arrowBuilder_ == null) {
          arrowBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder>(
                  getArrow(),
                  getParentForChildren(),
                  isClean());
          arrow_ = null;
        }
        return arrowBuilder_;
      }

      private com.yorha.proto.StructPB.ExpressionPB expression_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> expressionBuilder_;
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       * @return Whether the expression field is set.
       */
      public boolean hasExpression() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       * @return The expression.
       */
      public com.yorha.proto.StructPB.ExpressionPB getExpression() {
        if (expressionBuilder_ == null) {
          return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        } else {
          return expressionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       */
      public Builder setExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          expression_ = value;
          onChanged();
        } else {
          expressionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       */
      public Builder setExpression(
          com.yorha.proto.StructPB.ExpressionPB.Builder builderForValue) {
        if (expressionBuilder_ == null) {
          expression_ = builderForValue.build();
          onChanged();
        } else {
          expressionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       */
      public Builder mergeExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0) &&
              expression_ != null &&
              expression_ != com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance()) {
            expression_ =
              com.yorha.proto.StructPB.ExpressionPB.newBuilder(expression_).mergeFrom(value).buildPartial();
          } else {
            expression_ = value;
          }
          onChanged();
        } else {
          expressionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       */
      public Builder clearExpression() {
        if (expressionBuilder_ == null) {
          expression_ = null;
          onChanged();
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPB.Builder getExpressionBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getExpressionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
        if (expressionBuilder_ != null) {
          return expressionBuilder_.getMessageOrBuilder();
        } else {
          return expression_ == null ?
              com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 24;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> 
          getExpressionFieldBuilder() {
        if (expressionBuilder_ == null) {
          expressionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder>(
                  getExpression(),
                  getParentForChildren(),
                  isClean());
          expression_ = null;
        }
        return expressionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ResBuildingEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ResBuildingEntityPB)
    private static final com.yorha.proto.ResBuildingPB.ResBuildingEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.ResBuildingPB.ResBuildingEntityPB();
    }

    public static com.yorha.proto.ResBuildingPB.ResBuildingEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ResBuildingEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<ResBuildingEntityPB>() {
      @java.lang.Override
      public ResBuildingEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResBuildingEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResBuildingEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResBuildingEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.ResBuildingPB.ResBuildingEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CollectInfoPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CollectInfoPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 采集的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 采集的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 采集的军队id
     * </pre>
     *
     * <code>optional int64 armyId = 2;</code>
     * @return Whether the armyId field is set.
     */
    boolean hasArmyId();
    /**
     * <pre>
     * 采集的军队id
     * </pre>
     *
     * <code>optional int64 armyId = 2;</code>
     * @return The armyId.
     */
    long getArmyId();

    /**
     * <pre>
     * 采集玩家名字
     * </pre>
     *
     * <code>optional string playerName = 3;</code>
     * @return Whether the playerName field is set.
     */
    boolean hasPlayerName();
    /**
     * <pre>
     * 采集玩家名字
     * </pre>
     *
     * <code>optional string playerName = 3;</code>
     * @return The playerName.
     */
    java.lang.String getPlayerName();
    /**
     * <pre>
     * 采集玩家名字
     * </pre>
     *
     * <code>optional string playerName = 3;</code>
     * @return The bytes for playerName.
     */
    com.google.protobuf.ByteString
        getPlayerNameBytes();

    /**
     * <pre>
     * 采集玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 4;</code>
     * @return Whether the clanSimpleName field is set.
     */
    boolean hasClanSimpleName();
    /**
     * <pre>
     * 采集玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 4;</code>
     * @return The clanSimpleName.
     */
    java.lang.String getClanSimpleName();
    /**
     * <pre>
     * 采集玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 4;</code>
     * @return The bytes for clanSimpleName.
     */
    com.google.protobuf.ByteString
        getClanSimpleNameBytes();

    /**
     * <pre>
     * 采集结束时间戳
     * </pre>
     *
     * <code>optional int64 endTsMs = 5;</code>
     * @return Whether the endTsMs field is set.
     */
    boolean hasEndTsMs();
    /**
     * <pre>
     * 采集结束时间戳
     * </pre>
     *
     * <code>optional int64 endTsMs = 5;</code>
     * @return The endTsMs.
     */
    long getEndTsMs();

    /**
     * <pre>
     * 是否采空了
     * </pre>
     *
     * <code>optional bool isCollectOut = 6;</code>
     * @return Whether the isCollectOut field is set.
     */
    boolean hasIsCollectOut();
    /**
     * <pre>
     * 是否采空了
     * </pre>
     *
     * <code>optional bool isCollectOut = 6;</code>
     * @return The isCollectOut.
     */
    boolean getIsCollectOut();

    /**
     * <pre>
     * 采集开始时间戳
     * </pre>
     *
     * <code>optional int64 startTsMs = 7;</code>
     * @return Whether the startTsMs field is set.
     */
    boolean hasStartTsMs();
    /**
     * <pre>
     * 采集开始时间戳
     * </pre>
     *
     * <code>optional int64 startTsMs = 7;</code>
     * @return The startTsMs.
     */
    long getStartTsMs();

    /**
     * <pre>
     * 采集玩家联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 采集玩家联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 预计采集量
     * </pre>
     *
     * <code>optional int64 estimateNum = 9;</code>
     * @return Whether the estimateNum field is set.
     */
    boolean hasEstimateNum();
    /**
     * <pre>
     * 预计采集量
     * </pre>
     *
     * <code>optional int64 estimateNum = 9;</code>
     * @return The estimateNum.
     */
    long getEstimateNum();

    /**
     * <pre>
     * 采集的玩家所属阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
     * @return Whether the playerCamp field is set.
     */
    boolean hasPlayerCamp();
    /**
     * <pre>
     * 采集的玩家所属阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
     * @return The playerCamp.
     */
    com.yorha.proto.CommonEnum.Camp getPlayerCamp();

    /**
     * <pre>
     * 采集玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 11;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 采集玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 11;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CollectInfoPB}
   */
  public static final class CollectInfoPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CollectInfoPB)
      CollectInfoPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CollectInfoPB.newBuilder() to construct.
    private CollectInfoPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CollectInfoPB() {
      playerName_ = "";
      clanSimpleName_ = "";
      playerCamp_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CollectInfoPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CollectInfoPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              armyId_ = input.readInt64();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              playerName_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              clanSimpleName_ = bs;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              endTsMs_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              isCollectOut_ = input.readBool();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              startTsMs_ = input.readInt64();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              clanId_ = input.readInt64();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              estimateNum_ = input.readInt64();
              break;
            }
            case 80: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Camp value = com.yorha.proto.CommonEnum.Camp.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(10, rawValue);
              } else {
                bitField0_ |= 0x00000200;
                playerCamp_ = rawValue;
              }
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_CollectInfoPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_CollectInfoPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.ResBuildingPB.CollectInfoPB.class, com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 采集的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 采集的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ARMYID_FIELD_NUMBER = 2;
    private long armyId_;
    /**
     * <pre>
     * 采集的军队id
     * </pre>
     *
     * <code>optional int64 armyId = 2;</code>
     * @return Whether the armyId field is set.
     */
    @java.lang.Override
    public boolean hasArmyId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 采集的军队id
     * </pre>
     *
     * <code>optional int64 armyId = 2;</code>
     * @return The armyId.
     */
    @java.lang.Override
    public long getArmyId() {
      return armyId_;
    }

    public static final int PLAYERNAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object playerName_;
    /**
     * <pre>
     * 采集玩家名字
     * </pre>
     *
     * <code>optional string playerName = 3;</code>
     * @return Whether the playerName field is set.
     */
    @java.lang.Override
    public boolean hasPlayerName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 采集玩家名字
     * </pre>
     *
     * <code>optional string playerName = 3;</code>
     * @return The playerName.
     */
    @java.lang.Override
    public java.lang.String getPlayerName() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          playerName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 采集玩家名字
     * </pre>
     *
     * <code>optional string playerName = 3;</code>
     * @return The bytes for playerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPlayerNameBytes() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        playerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLANSIMPLENAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object clanSimpleName_;
    /**
     * <pre>
     * 采集玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 4;</code>
     * @return Whether the clanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasClanSimpleName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 采集玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 4;</code>
     * @return The clanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getClanSimpleName() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 采集玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 4;</code>
     * @return The bytes for clanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSimpleNameBytes() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENDTSMS_FIELD_NUMBER = 5;
    private long endTsMs_;
    /**
     * <pre>
     * 采集结束时间戳
     * </pre>
     *
     * <code>optional int64 endTsMs = 5;</code>
     * @return Whether the endTsMs field is set.
     */
    @java.lang.Override
    public boolean hasEndTsMs() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 采集结束时间戳
     * </pre>
     *
     * <code>optional int64 endTsMs = 5;</code>
     * @return The endTsMs.
     */
    @java.lang.Override
    public long getEndTsMs() {
      return endTsMs_;
    }

    public static final int ISCOLLECTOUT_FIELD_NUMBER = 6;
    private boolean isCollectOut_;
    /**
     * <pre>
     * 是否采空了
     * </pre>
     *
     * <code>optional bool isCollectOut = 6;</code>
     * @return Whether the isCollectOut field is set.
     */
    @java.lang.Override
    public boolean hasIsCollectOut() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 是否采空了
     * </pre>
     *
     * <code>optional bool isCollectOut = 6;</code>
     * @return The isCollectOut.
     */
    @java.lang.Override
    public boolean getIsCollectOut() {
      return isCollectOut_;
    }

    public static final int STARTTSMS_FIELD_NUMBER = 7;
    private long startTsMs_;
    /**
     * <pre>
     * 采集开始时间戳
     * </pre>
     *
     * <code>optional int64 startTsMs = 7;</code>
     * @return Whether the startTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStartTsMs() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 采集开始时间戳
     * </pre>
     *
     * <code>optional int64 startTsMs = 7;</code>
     * @return The startTsMs.
     */
    @java.lang.Override
    public long getStartTsMs() {
      return startTsMs_;
    }

    public static final int CLANID_FIELD_NUMBER = 8;
    private long clanId_;
    /**
     * <pre>
     * 采集玩家联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 采集玩家联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 8;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int ESTIMATENUM_FIELD_NUMBER = 9;
    private long estimateNum_;
    /**
     * <pre>
     * 预计采集量
     * </pre>
     *
     * <code>optional int64 estimateNum = 9;</code>
     * @return Whether the estimateNum field is set.
     */
    @java.lang.Override
    public boolean hasEstimateNum() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 预计采集量
     * </pre>
     *
     * <code>optional int64 estimateNum = 9;</code>
     * @return The estimateNum.
     */
    @java.lang.Override
    public long getEstimateNum() {
      return estimateNum_;
    }

    public static final int PLAYERCAMP_FIELD_NUMBER = 10;
    private int playerCamp_;
    /**
     * <pre>
     * 采集的玩家所属阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
     * @return Whether the playerCamp field is set.
     */
    @java.lang.Override public boolean hasPlayerCamp() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 采集的玩家所属阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
     * @return The playerCamp.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Camp getPlayerCamp() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(playerCamp_);
      return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
    }

    public static final int ZONEID_FIELD_NUMBER = 11;
    private int zoneId_;
    /**
     * <pre>
     * 采集玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 11;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 采集玩家的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 11;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, armyId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, playerName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, endTsMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBool(6, isCollectOut_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt64(7, startTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt64(8, clanId_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt64(9, estimateNum_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeEnum(10, playerCamp_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeInt32(11, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, armyId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, playerName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, endTsMs_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(6, isCollectOut_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, startTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, clanId_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, estimateNum_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(10, playerCamp_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.ResBuildingPB.CollectInfoPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.ResBuildingPB.CollectInfoPB other = (com.yorha.proto.ResBuildingPB.CollectInfoPB) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasArmyId() != other.hasArmyId()) return false;
      if (hasArmyId()) {
        if (getArmyId()
            != other.getArmyId()) return false;
      }
      if (hasPlayerName() != other.hasPlayerName()) return false;
      if (hasPlayerName()) {
        if (!getPlayerName()
            .equals(other.getPlayerName())) return false;
      }
      if (hasClanSimpleName() != other.hasClanSimpleName()) return false;
      if (hasClanSimpleName()) {
        if (!getClanSimpleName()
            .equals(other.getClanSimpleName())) return false;
      }
      if (hasEndTsMs() != other.hasEndTsMs()) return false;
      if (hasEndTsMs()) {
        if (getEndTsMs()
            != other.getEndTsMs()) return false;
      }
      if (hasIsCollectOut() != other.hasIsCollectOut()) return false;
      if (hasIsCollectOut()) {
        if (getIsCollectOut()
            != other.getIsCollectOut()) return false;
      }
      if (hasStartTsMs() != other.hasStartTsMs()) return false;
      if (hasStartTsMs()) {
        if (getStartTsMs()
            != other.getStartTsMs()) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasEstimateNum() != other.hasEstimateNum()) return false;
      if (hasEstimateNum()) {
        if (getEstimateNum()
            != other.getEstimateNum()) return false;
      }
      if (hasPlayerCamp() != other.hasPlayerCamp()) return false;
      if (hasPlayerCamp()) {
        if (playerCamp_ != other.playerCamp_) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasArmyId()) {
        hash = (37 * hash) + ARMYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getArmyId());
      }
      if (hasPlayerName()) {
        hash = (37 * hash) + PLAYERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerName().hashCode();
      }
      if (hasClanSimpleName()) {
        hash = (37 * hash) + CLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSimpleName().hashCode();
      }
      if (hasEndTsMs()) {
        hash = (37 * hash) + ENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEndTsMs());
      }
      if (hasIsCollectOut()) {
        hash = (37 * hash) + ISCOLLECTOUT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsCollectOut());
      }
      if (hasStartTsMs()) {
        hash = (37 * hash) + STARTTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStartTsMs());
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasEstimateNum()) {
        hash = (37 * hash) + ESTIMATENUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEstimateNum());
      }
      if (hasPlayerCamp()) {
        hash = (37 * hash) + PLAYERCAMP_FIELD_NUMBER;
        hash = (53 * hash) + playerCamp_;
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ResBuildingPB.CollectInfoPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.ResBuildingPB.CollectInfoPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CollectInfoPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CollectInfoPB)
        com.yorha.proto.ResBuildingPB.CollectInfoPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_CollectInfoPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_CollectInfoPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.ResBuildingPB.CollectInfoPB.class, com.yorha.proto.ResBuildingPB.CollectInfoPB.Builder.class);
      }

      // Construct using com.yorha.proto.ResBuildingPB.CollectInfoPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        armyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        clanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        endTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        isCollectOut_ = false;
        bitField0_ = (bitField0_ & ~0x00000020);
        startTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        estimateNum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        playerCamp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.ResBuildingPB.internal_static_com_yorha_proto_CollectInfoPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.CollectInfoPB getDefaultInstanceForType() {
        return com.yorha.proto.ResBuildingPB.CollectInfoPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.CollectInfoPB build() {
        com.yorha.proto.ResBuildingPB.CollectInfoPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.ResBuildingPB.CollectInfoPB buildPartial() {
        com.yorha.proto.ResBuildingPB.CollectInfoPB result = new com.yorha.proto.ResBuildingPB.CollectInfoPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.armyId_ = armyId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.playerName_ = playerName_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.clanSimpleName_ = clanSimpleName_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.endTsMs_ = endTsMs_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.isCollectOut_ = isCollectOut_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.startTsMs_ = startTsMs_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.estimateNum_ = estimateNum_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          to_bitField0_ |= 0x00000200;
        }
        result.playerCamp_ = playerCamp_;
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000400;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.ResBuildingPB.CollectInfoPB) {
          return mergeFrom((com.yorha.proto.ResBuildingPB.CollectInfoPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.ResBuildingPB.CollectInfoPB other) {
        if (other == com.yorha.proto.ResBuildingPB.CollectInfoPB.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasArmyId()) {
          setArmyId(other.getArmyId());
        }
        if (other.hasPlayerName()) {
          bitField0_ |= 0x00000004;
          playerName_ = other.playerName_;
          onChanged();
        }
        if (other.hasClanSimpleName()) {
          bitField0_ |= 0x00000008;
          clanSimpleName_ = other.clanSimpleName_;
          onChanged();
        }
        if (other.hasEndTsMs()) {
          setEndTsMs(other.getEndTsMs());
        }
        if (other.hasIsCollectOut()) {
          setIsCollectOut(other.getIsCollectOut());
        }
        if (other.hasStartTsMs()) {
          setStartTsMs(other.getStartTsMs());
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasEstimateNum()) {
          setEstimateNum(other.getEstimateNum());
        }
        if (other.hasPlayerCamp()) {
          setPlayerCamp(other.getPlayerCamp());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.ResBuildingPB.CollectInfoPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.ResBuildingPB.CollectInfoPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 采集的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 采集的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 采集的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long armyId_ ;
      /**
       * <pre>
       * 采集的军队id
       * </pre>
       *
       * <code>optional int64 armyId = 2;</code>
       * @return Whether the armyId field is set.
       */
      @java.lang.Override
      public boolean hasArmyId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 采集的军队id
       * </pre>
       *
       * <code>optional int64 armyId = 2;</code>
       * @return The armyId.
       */
      @java.lang.Override
      public long getArmyId() {
        return armyId_;
      }
      /**
       * <pre>
       * 采集的军队id
       * </pre>
       *
       * <code>optional int64 armyId = 2;</code>
       * @param value The armyId to set.
       * @return This builder for chaining.
       */
      public Builder setArmyId(long value) {
        bitField0_ |= 0x00000002;
        armyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集的军队id
       * </pre>
       *
       * <code>optional int64 armyId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearArmyId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        armyId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object playerName_ = "";
      /**
       * <pre>
       * 采集玩家名字
       * </pre>
       *
       * <code>optional string playerName = 3;</code>
       * @return Whether the playerName field is set.
       */
      public boolean hasPlayerName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 采集玩家名字
       * </pre>
       *
       * <code>optional string playerName = 3;</code>
       * @return The playerName.
       */
      public java.lang.String getPlayerName() {
        java.lang.Object ref = playerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            playerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 采集玩家名字
       * </pre>
       *
       * <code>optional string playerName = 3;</code>
       * @return The bytes for playerName.
       */
      public com.google.protobuf.ByteString
          getPlayerNameBytes() {
        java.lang.Object ref = playerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          playerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 采集玩家名字
       * </pre>
       *
       * <code>optional string playerName = 3;</code>
       * @param value The playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        playerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集玩家名字
       * </pre>
       *
       * <code>optional string playerName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerName_ = getDefaultInstance().getPlayerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集玩家名字
       * </pre>
       *
       * <code>optional string playerName = 3;</code>
       * @param value The bytes for playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        playerName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object clanSimpleName_ = "";
      /**
       * <pre>
       * 采集玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 4;</code>
       * @return Whether the clanSimpleName field is set.
       */
      public boolean hasClanSimpleName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 采集玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 4;</code>
       * @return The clanSimpleName.
       */
      public java.lang.String getClanSimpleName() {
        java.lang.Object ref = clanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 采集玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 4;</code>
       * @return The bytes for clanSimpleName.
       */
      public com.google.protobuf.ByteString
          getClanSimpleNameBytes() {
        java.lang.Object ref = clanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 采集玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 4;</code>
       * @param value The clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        clanSimpleName_ = getDefaultInstance().getClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 4;</code>
       * @param value The bytes for clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }

      private long endTsMs_ ;
      /**
       * <pre>
       * 采集结束时间戳
       * </pre>
       *
       * <code>optional int64 endTsMs = 5;</code>
       * @return Whether the endTsMs field is set.
       */
      @java.lang.Override
      public boolean hasEndTsMs() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 采集结束时间戳
       * </pre>
       *
       * <code>optional int64 endTsMs = 5;</code>
       * @return The endTsMs.
       */
      @java.lang.Override
      public long getEndTsMs() {
        return endTsMs_;
      }
      /**
       * <pre>
       * 采集结束时间戳
       * </pre>
       *
       * <code>optional int64 endTsMs = 5;</code>
       * @param value The endTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setEndTsMs(long value) {
        bitField0_ |= 0x00000010;
        endTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集结束时间戳
       * </pre>
       *
       * <code>optional int64 endTsMs = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000010);
        endTsMs_ = 0L;
        onChanged();
        return this;
      }

      private boolean isCollectOut_ ;
      /**
       * <pre>
       * 是否采空了
       * </pre>
       *
       * <code>optional bool isCollectOut = 6;</code>
       * @return Whether the isCollectOut field is set.
       */
      @java.lang.Override
      public boolean hasIsCollectOut() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 是否采空了
       * </pre>
       *
       * <code>optional bool isCollectOut = 6;</code>
       * @return The isCollectOut.
       */
      @java.lang.Override
      public boolean getIsCollectOut() {
        return isCollectOut_;
      }
      /**
       * <pre>
       * 是否采空了
       * </pre>
       *
       * <code>optional bool isCollectOut = 6;</code>
       * @param value The isCollectOut to set.
       * @return This builder for chaining.
       */
      public Builder setIsCollectOut(boolean value) {
        bitField0_ |= 0x00000020;
        isCollectOut_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否采空了
       * </pre>
       *
       * <code>optional bool isCollectOut = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsCollectOut() {
        bitField0_ = (bitField0_ & ~0x00000020);
        isCollectOut_ = false;
        onChanged();
        return this;
      }

      private long startTsMs_ ;
      /**
       * <pre>
       * 采集开始时间戳
       * </pre>
       *
       * <code>optional int64 startTsMs = 7;</code>
       * @return Whether the startTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStartTsMs() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 采集开始时间戳
       * </pre>
       *
       * <code>optional int64 startTsMs = 7;</code>
       * @return The startTsMs.
       */
      @java.lang.Override
      public long getStartTsMs() {
        return startTsMs_;
      }
      /**
       * <pre>
       * 采集开始时间戳
       * </pre>
       *
       * <code>optional int64 startTsMs = 7;</code>
       * @param value The startTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStartTsMs(long value) {
        bitField0_ |= 0x00000040;
        startTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集开始时间戳
       * </pre>
       *
       * <code>optional int64 startTsMs = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTsMs() {
        bitField0_ = (bitField0_ & ~0x00000040);
        startTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 采集玩家联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 采集玩家联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 采集玩家联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000080;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集玩家联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000080);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private long estimateNum_ ;
      /**
       * <pre>
       * 预计采集量
       * </pre>
       *
       * <code>optional int64 estimateNum = 9;</code>
       * @return Whether the estimateNum field is set.
       */
      @java.lang.Override
      public boolean hasEstimateNum() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 预计采集量
       * </pre>
       *
       * <code>optional int64 estimateNum = 9;</code>
       * @return The estimateNum.
       */
      @java.lang.Override
      public long getEstimateNum() {
        return estimateNum_;
      }
      /**
       * <pre>
       * 预计采集量
       * </pre>
       *
       * <code>optional int64 estimateNum = 9;</code>
       * @param value The estimateNum to set.
       * @return This builder for chaining.
       */
      public Builder setEstimateNum(long value) {
        bitField0_ |= 0x00000100;
        estimateNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 预计采集量
       * </pre>
       *
       * <code>optional int64 estimateNum = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearEstimateNum() {
        bitField0_ = (bitField0_ & ~0x00000100);
        estimateNum_ = 0L;
        onChanged();
        return this;
      }

      private int playerCamp_ = 0;
      /**
       * <pre>
       * 采集的玩家所属阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
       * @return Whether the playerCamp field is set.
       */
      @java.lang.Override public boolean hasPlayerCamp() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 采集的玩家所属阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
       * @return The playerCamp.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Camp getPlayerCamp() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(playerCamp_);
        return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
      }
      /**
       * <pre>
       * 采集的玩家所属阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
       * @param value The playerCamp to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerCamp(com.yorha.proto.CommonEnum.Camp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000200;
        playerCamp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集的玩家所属阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp playerCamp = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerCamp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        playerCamp_ = 0;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 采集玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 11;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 采集玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 11;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 采集玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 11;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000400;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集玩家的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000400);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CollectInfoPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CollectInfoPB)
    private static final com.yorha.proto.ResBuildingPB.CollectInfoPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.ResBuildingPB.CollectInfoPB();
    }

    public static com.yorha.proto.ResBuildingPB.CollectInfoPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CollectInfoPB>
        PARSER = new com.google.protobuf.AbstractParser<CollectInfoPB>() {
      @java.lang.Override
      public CollectInfoPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CollectInfoPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CollectInfoPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CollectInfoPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.ResBuildingPB.CollectInfoPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ResBuildingEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ResBuildingEntityPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CollectInfoPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CollectInfoPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,cs_proto/gen/resBuilding/resBuildingPB" +
      ".proto\022\017com.yorha.proto\032\"cs_proto/gen/co" +
      "mmon/structPB.proto\032%ss_proto/gen/common" +
      "/common_enum.proto\"\336\002\n\023ResBuildingEntity" +
      "PB\022\022\n\ntemplateId\030\001 \001(\005\0225\n\005state\030\002 \001(\0162&." +
      "com.yorha.proto.ResourceBuildingState\022\'\n" +
      "\005point\030\003 \001(\0132\030.com.yorha.proto.PointPB\022\016" +
      "\n\006clanId\030\004 \001(\003\022\026\n\016clanSimpleName\030\005 \001(\t\022\016" +
      "\n\006curNum\030\006 \001(\005\022/\n\007collect\030\007 \001(\0132\036.com.yo" +
      "rha.proto.CollectInfoPB\0227\n\005arrow\030\027 \001(\0132(" +
      ".com.yorha.proto.Int64ArmyArrowItemMapPB" +
      "\0221\n\nexpression\030\030 \001(\0132\035.com.yorha.proto.E" +
      "xpressionPB\"\367\001\n\rCollectInfoPB\022\020\n\010playerI" +
      "d\030\001 \001(\003\022\016\n\006armyId\030\002 \001(\003\022\022\n\nplayerName\030\003 " +
      "\001(\t\022\026\n\016clanSimpleName\030\004 \001(\t\022\017\n\007endTsMs\030\005" +
      " \001(\003\022\024\n\014isCollectOut\030\006 \001(\010\022\021\n\tstartTsMs\030" +
      "\007 \001(\003\022\016\n\006clanId\030\010 \001(\003\022\023\n\013estimateNum\030\t \001" +
      "(\003\022)\n\nplayerCamp\030\n \001(\0162\025.com.yorha.proto" +
      ".Camp\022\016\n\006zoneId\030\013 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_ResBuildingEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ResBuildingEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ResBuildingEntityPB_descriptor,
        new java.lang.String[] { "TemplateId", "State", "Point", "ClanId", "ClanSimpleName", "CurNum", "Collect", "Arrow", "Expression", });
    internal_static_com_yorha_proto_CollectInfoPB_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_CollectInfoPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CollectInfoPB_descriptor,
        new java.lang.String[] { "PlayerId", "ArmyId", "PlayerName", "ClanSimpleName", "EndTsMs", "IsCollectOut", "StartTsMs", "ClanId", "EstimateNum", "PlayerCamp", "ZoneId", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

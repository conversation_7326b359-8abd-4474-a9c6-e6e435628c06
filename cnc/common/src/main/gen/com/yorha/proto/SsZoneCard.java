// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/zoneCard/ss_zoneCard.proto

package com.yorha.proto;

public final class SsZoneCard {
  private SsZoneCard() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GetAllZoneInfoAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetAllZoneInfoAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否是超级白名单(可看到未对外,未到开服时间的服)
     * </pre>
     *
     * <code>optional bool isSuperWhite = 1;</code>
     * @return Whether the isSuperWhite field is set.
     */
    boolean hasIsSuperWhite();
    /**
     * <pre>
     * 是否是超级白名单(可看到未对外,未到开服时间的服)
     * </pre>
     *
     * <code>optional bool isSuperWhite = 1;</code>
     * @return The isSuperWhite.
     */
    boolean getIsSuperWhite();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetAllZoneInfoAsk}
   */
  public static final class GetAllZoneInfoAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetAllZoneInfoAsk)
      GetAllZoneInfoAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetAllZoneInfoAsk.newBuilder() to construct.
    private GetAllZoneInfoAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetAllZoneInfoAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetAllZoneInfoAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetAllZoneInfoAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isSuperWhite_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk.class, com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ISSUPERWHITE_FIELD_NUMBER = 1;
    private boolean isSuperWhite_;
    /**
     * <pre>
     * 是否是超级白名单(可看到未对外,未到开服时间的服)
     * </pre>
     *
     * <code>optional bool isSuperWhite = 1;</code>
     * @return Whether the isSuperWhite field is set.
     */
    @java.lang.Override
    public boolean hasIsSuperWhite() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 是否是超级白名单(可看到未对外,未到开服时间的服)
     * </pre>
     *
     * <code>optional bool isSuperWhite = 1;</code>
     * @return The isSuperWhite.
     */
    @java.lang.Override
    public boolean getIsSuperWhite() {
      return isSuperWhite_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isSuperWhite_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isSuperWhite_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk other = (com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk) obj;

      if (hasIsSuperWhite() != other.hasIsSuperWhite()) return false;
      if (hasIsSuperWhite()) {
        if (getIsSuperWhite()
            != other.getIsSuperWhite()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsSuperWhite()) {
        hash = (37 * hash) + ISSUPERWHITE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsSuperWhite());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetAllZoneInfoAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetAllZoneInfoAsk)
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk.class, com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isSuperWhite_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk build() {
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk buildPartial() {
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk result = new com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isSuperWhite_ = isSuperWhite_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk other) {
        if (other == com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk.getDefaultInstance()) return this;
        if (other.hasIsSuperWhite()) {
          setIsSuperWhite(other.getIsSuperWhite());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isSuperWhite_ ;
      /**
       * <pre>
       * 是否是超级白名单(可看到未对外,未到开服时间的服)
       * </pre>
       *
       * <code>optional bool isSuperWhite = 1;</code>
       * @return Whether the isSuperWhite field is set.
       */
      @java.lang.Override
      public boolean hasIsSuperWhite() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 是否是超级白名单(可看到未对外,未到开服时间的服)
       * </pre>
       *
       * <code>optional bool isSuperWhite = 1;</code>
       * @return The isSuperWhite.
       */
      @java.lang.Override
      public boolean getIsSuperWhite() {
        return isSuperWhite_;
      }
      /**
       * <pre>
       * 是否是超级白名单(可看到未对外,未到开服时间的服)
       * </pre>
       *
       * <code>optional bool isSuperWhite = 1;</code>
       * @param value The isSuperWhite to set.
       * @return This builder for chaining.
       */
      public Builder setIsSuperWhite(boolean value) {
        bitField0_ |= 0x00000001;
        isSuperWhite_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否是超级白名单(可看到未对外,未到开服时间的服)
       * </pre>
       *
       * <code>optional bool isSuperWhite = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSuperWhite() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isSuperWhite_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetAllZoneInfoAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetAllZoneInfoAsk)
    private static final com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk();
    }

    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetAllZoneInfoAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetAllZoneInfoAsk>() {
      @java.lang.Override
      public GetAllZoneInfoAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetAllZoneInfoAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetAllZoneInfoAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetAllZoneInfoAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetAllZoneInfoAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetAllZoneInfoAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetAllZoneInfoAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> 
        getZoneServerListList();
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    com.yorha.proto.CommonMsg.ZoneServerInfo getZoneServerList(int index);
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    int getZoneServerListCount();
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
        getZoneServerListOrBuilderList();
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder getZoneServerListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetAllZoneInfoAns}
   */
  public static final class GetAllZoneInfoAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetAllZoneInfoAns)
      GetAllZoneInfoAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetAllZoneInfoAns.newBuilder() to construct.
    private GetAllZoneInfoAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetAllZoneInfoAns() {
      zoneServerList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetAllZoneInfoAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetAllZoneInfoAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                zoneServerList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ZoneServerInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              zoneServerList_.add(
                  input.readMessage(com.yorha.proto.CommonMsg.ZoneServerInfo.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          zoneServerList_ = java.util.Collections.unmodifiableList(zoneServerList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetAllZoneInfoAns.class, com.yorha.proto.SsZoneCard.GetAllZoneInfoAns.Builder.class);
    }

    public static final int ZONESERVERLIST_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> zoneServerList_;
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> getZoneServerListList() {
      return zoneServerList_;
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
        getZoneServerListOrBuilderList() {
      return zoneServerList_;
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public int getZoneServerListCount() {
      return zoneServerList_.size();
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ZoneServerInfo getZoneServerList(int index) {
      return zoneServerList_.get(index);
    }
    /**
     * <pre>
     * 各服信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder getZoneServerListOrBuilder(
        int index) {
      return zoneServerList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < zoneServerList_.size(); i++) {
        output.writeMessage(1, zoneServerList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < zoneServerList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, zoneServerList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetAllZoneInfoAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetAllZoneInfoAns other = (com.yorha.proto.SsZoneCard.GetAllZoneInfoAns) obj;

      if (!getZoneServerListList()
          .equals(other.getZoneServerListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getZoneServerListCount() > 0) {
        hash = (37 * hash) + ZONESERVERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getZoneServerListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetAllZoneInfoAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetAllZoneInfoAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetAllZoneInfoAns)
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetAllZoneInfoAns.class, com.yorha.proto.SsZoneCard.GetAllZoneInfoAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetAllZoneInfoAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getZoneServerListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (zoneServerListBuilder_ == null) {
          zoneServerList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          zoneServerListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetAllZoneInfoAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetAllZoneInfoAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetAllZoneInfoAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetAllZoneInfoAns build() {
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetAllZoneInfoAns buildPartial() {
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAns result = new com.yorha.proto.SsZoneCard.GetAllZoneInfoAns(this);
        int from_bitField0_ = bitField0_;
        if (zoneServerListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            zoneServerList_ = java.util.Collections.unmodifiableList(zoneServerList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.zoneServerList_ = zoneServerList_;
        } else {
          result.zoneServerList_ = zoneServerListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetAllZoneInfoAns) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetAllZoneInfoAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetAllZoneInfoAns other) {
        if (other == com.yorha.proto.SsZoneCard.GetAllZoneInfoAns.getDefaultInstance()) return this;
        if (zoneServerListBuilder_ == null) {
          if (!other.zoneServerList_.isEmpty()) {
            if (zoneServerList_.isEmpty()) {
              zoneServerList_ = other.zoneServerList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureZoneServerListIsMutable();
              zoneServerList_.addAll(other.zoneServerList_);
            }
            onChanged();
          }
        } else {
          if (!other.zoneServerList_.isEmpty()) {
            if (zoneServerListBuilder_.isEmpty()) {
              zoneServerListBuilder_.dispose();
              zoneServerListBuilder_ = null;
              zoneServerList_ = other.zoneServerList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              zoneServerListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getZoneServerListFieldBuilder() : null;
            } else {
              zoneServerListBuilder_.addAllMessages(other.zoneServerList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetAllZoneInfoAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetAllZoneInfoAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> zoneServerList_ =
        java.util.Collections.emptyList();
      private void ensureZoneServerListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          zoneServerList_ = new java.util.ArrayList<com.yorha.proto.CommonMsg.ZoneServerInfo>(zoneServerList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ZoneServerInfo, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder, com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> zoneServerListBuilder_;

      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo> getZoneServerListList() {
        if (zoneServerListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(zoneServerList_);
        } else {
          return zoneServerListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public int getZoneServerListCount() {
        if (zoneServerListBuilder_ == null) {
          return zoneServerList_.size();
        } else {
          return zoneServerListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo getZoneServerList(int index) {
        if (zoneServerListBuilder_ == null) {
          return zoneServerList_.get(index);
        } else {
          return zoneServerListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder setZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo value) {
        if (zoneServerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureZoneServerListIsMutable();
          zoneServerList_.set(index, value);
          onChanged();
        } else {
          zoneServerListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder setZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder builderForValue) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.set(index, builderForValue.build());
          onChanged();
        } else {
          zoneServerListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(com.yorha.proto.CommonMsg.ZoneServerInfo value) {
        if (zoneServerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureZoneServerListIsMutable();
          zoneServerList_.add(value);
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo value) {
        if (zoneServerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureZoneServerListIsMutable();
          zoneServerList_.add(index, value);
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(
          com.yorha.proto.CommonMsg.ZoneServerInfo.Builder builderForValue) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.add(builderForValue.build());
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addZoneServerList(
          int index, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder builderForValue) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.add(index, builderForValue.build());
          onChanged();
        } else {
          zoneServerListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder addAllZoneServerList(
          java.lang.Iterable<? extends com.yorha.proto.CommonMsg.ZoneServerInfo> values) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, zoneServerList_);
          onChanged();
        } else {
          zoneServerListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder clearZoneServerList() {
        if (zoneServerListBuilder_ == null) {
          zoneServerList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          zoneServerListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public Builder removeZoneServerList(int index) {
        if (zoneServerListBuilder_ == null) {
          ensureZoneServerListIsMutable();
          zoneServerList_.remove(index);
          onChanged();
        } else {
          zoneServerListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo.Builder getZoneServerListBuilder(
          int index) {
        return getZoneServerListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder getZoneServerListOrBuilder(
          int index) {
        if (zoneServerListBuilder_ == null) {
          return zoneServerList_.get(index);  } else {
          return zoneServerListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
           getZoneServerListOrBuilderList() {
        if (zoneServerListBuilder_ != null) {
          return zoneServerListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(zoneServerList_);
        }
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo.Builder addZoneServerListBuilder() {
        return getZoneServerListFieldBuilder().addBuilder(
            com.yorha.proto.CommonMsg.ZoneServerInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ZoneServerInfo.Builder addZoneServerListBuilder(
          int index) {
        return getZoneServerListFieldBuilder().addBuilder(
            index, com.yorha.proto.CommonMsg.ZoneServerInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 各服信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.ZoneServerInfo zoneServerList = 1;</code>
       */
      public java.util.List<com.yorha.proto.CommonMsg.ZoneServerInfo.Builder> 
           getZoneServerListBuilderList() {
        return getZoneServerListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.CommonMsg.ZoneServerInfo, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder, com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder> 
          getZoneServerListFieldBuilder() {
        if (zoneServerListBuilder_ == null) {
          zoneServerListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.CommonMsg.ZoneServerInfo, com.yorha.proto.CommonMsg.ZoneServerInfo.Builder, com.yorha.proto.CommonMsg.ZoneServerInfoOrBuilder>(
                  zoneServerList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          zoneServerList_ = null;
        }
        return zoneServerListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetAllZoneInfoAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetAllZoneInfoAns)
    private static final com.yorha.proto.SsZoneCard.GetAllZoneInfoAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetAllZoneInfoAns();
    }

    public static com.yorha.proto.SsZoneCard.GetAllZoneInfoAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetAllZoneInfoAns>
        PARSER = new com.google.protobuf.AbstractParser<GetAllZoneInfoAns>() {
      @java.lang.Override
      public GetAllZoneInfoAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetAllZoneInfoAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetAllZoneInfoAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetAllZoneInfoAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetAllZoneInfoAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetMultiZoneStatusAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetMultiZoneStatusAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 多个zone
     * </pre>
     *
     * <code>repeated int32 zoneIds = 1;</code>
     * @return A list containing the zoneIds.
     */
    java.util.List<java.lang.Integer> getZoneIdsList();
    /**
     * <pre>
     * 多个zone
     * </pre>
     *
     * <code>repeated int32 zoneIds = 1;</code>
     * @return The count of zoneIds.
     */
    int getZoneIdsCount();
    /**
     * <pre>
     * 多个zone
     * </pre>
     *
     * <code>repeated int32 zoneIds = 1;</code>
     * @param index The index of the element to return.
     * @return The zoneIds at the given index.
     */
    int getZoneIds(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetMultiZoneStatusAsk}
   */
  public static final class GetMultiZoneStatusAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetMultiZoneStatusAsk)
      GetMultiZoneStatusAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetMultiZoneStatusAsk.newBuilder() to construct.
    private GetMultiZoneStatusAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetMultiZoneStatusAsk() {
      zoneIds_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetMultiZoneStatusAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetMultiZoneStatusAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                zoneIds_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              zoneIds_.addInt(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                zoneIds_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                zoneIds_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          zoneIds_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk.class, com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk.Builder.class);
    }

    public static final int ZONEIDS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.IntList zoneIds_;
    /**
     * <pre>
     * 多个zone
     * </pre>
     *
     * <code>repeated int32 zoneIds = 1;</code>
     * @return A list containing the zoneIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getZoneIdsList() {
      return zoneIds_;
    }
    /**
     * <pre>
     * 多个zone
     * </pre>
     *
     * <code>repeated int32 zoneIds = 1;</code>
     * @return The count of zoneIds.
     */
    public int getZoneIdsCount() {
      return zoneIds_.size();
    }
    /**
     * <pre>
     * 多个zone
     * </pre>
     *
     * <code>repeated int32 zoneIds = 1;</code>
     * @param index The index of the element to return.
     * @return The zoneIds at the given index.
     */
    public int getZoneIds(int index) {
      return zoneIds_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < zoneIds_.size(); i++) {
        output.writeInt32(1, zoneIds_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < zoneIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(zoneIds_.getInt(i));
        }
        size += dataSize;
        size += 1 * getZoneIdsList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk other = (com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk) obj;

      if (!getZoneIdsList()
          .equals(other.getZoneIdsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getZoneIdsCount() > 0) {
        hash = (37 * hash) + ZONEIDS_FIELD_NUMBER;
        hash = (53 * hash) + getZoneIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetMultiZoneStatusAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetMultiZoneStatusAsk)
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk.class, com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk build() {
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk buildPartial() {
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk result = new com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          zoneIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.zoneIds_ = zoneIds_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk other) {
        if (other == com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk.getDefaultInstance()) return this;
        if (!other.zoneIds_.isEmpty()) {
          if (zoneIds_.isEmpty()) {
            zoneIds_ = other.zoneIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureZoneIdsIsMutable();
            zoneIds_.addAll(other.zoneIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList zoneIds_ = emptyIntList();
      private void ensureZoneIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          zoneIds_ = mutableCopy(zoneIds_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       * 多个zone
       * </pre>
       *
       * <code>repeated int32 zoneIds = 1;</code>
       * @return A list containing the zoneIds.
       */
      public java.util.List<java.lang.Integer>
          getZoneIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(zoneIds_) : zoneIds_;
      }
      /**
       * <pre>
       * 多个zone
       * </pre>
       *
       * <code>repeated int32 zoneIds = 1;</code>
       * @return The count of zoneIds.
       */
      public int getZoneIdsCount() {
        return zoneIds_.size();
      }
      /**
       * <pre>
       * 多个zone
       * </pre>
       *
       * <code>repeated int32 zoneIds = 1;</code>
       * @param index The index of the element to return.
       * @return The zoneIds at the given index.
       */
      public int getZoneIds(int index) {
        return zoneIds_.getInt(index);
      }
      /**
       * <pre>
       * 多个zone
       * </pre>
       *
       * <code>repeated int32 zoneIds = 1;</code>
       * @param index The index to set the value at.
       * @param value The zoneIds to set.
       * @return This builder for chaining.
       */
      public Builder setZoneIds(
          int index, int value) {
        ensureZoneIdsIsMutable();
        zoneIds_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 多个zone
       * </pre>
       *
       * <code>repeated int32 zoneIds = 1;</code>
       * @param value The zoneIds to add.
       * @return This builder for chaining.
       */
      public Builder addZoneIds(int value) {
        ensureZoneIdsIsMutable();
        zoneIds_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 多个zone
       * </pre>
       *
       * <code>repeated int32 zoneIds = 1;</code>
       * @param values The zoneIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllZoneIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureZoneIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, zoneIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 多个zone
       * </pre>
       *
       * <code>repeated int32 zoneIds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneIds() {
        zoneIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetMultiZoneStatusAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetMultiZoneStatusAsk)
    private static final com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk();
    }

    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetMultiZoneStatusAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetMultiZoneStatusAsk>() {
      @java.lang.Override
      public GetMultiZoneStatusAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetMultiZoneStatusAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetMultiZoneStatusAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetMultiZoneStatusAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetMultiZoneStatusAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetMultiZoneStatusAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    int getServersStatusCount();
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    boolean containsServersStatus(
        int key);
    /**
     * Use {@link #getServersStatusMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
    getServersStatus();
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
    getServersStatusMap();
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */

    com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrDefault(
        int key,
        com.yorha.proto.StructMsg.ZoneStatus defaultValue);
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */

    com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetMultiZoneStatusAns}
   */
  public static final class GetMultiZoneStatusAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetMultiZoneStatusAns)
      GetMultiZoneStatusAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetMultiZoneStatusAns.newBuilder() to construct.
    private GetMultiZoneStatusAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetMultiZoneStatusAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetMultiZoneStatusAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetMultiZoneStatusAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                serversStatus_ = com.google.protobuf.MapField.newMapField(
                    ServersStatusDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
              serversStatus__ = input.readMessage(
                  ServersStatusDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              serversStatus_.getMutableMap().put(
                  serversStatus__.getKey(), serversStatus__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetServersStatus();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns.class, com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns.Builder.class);
    }

    public static final int SERVERSSTATUS_FIELD_NUMBER = 1;
    private static final class ServersStatusDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>newDefaultInstance(
                  com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAns_ServersStatusEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructMsg.ZoneStatus.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> serversStatus_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
    internalGetServersStatus() {
      if (serversStatus_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ServersStatusDefaultEntryHolder.defaultEntry);
      }
      return serversStatus_;
    }

    public int getServersStatusCount() {
      return internalGetServersStatus().getMap().size();
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */

    @java.lang.Override
    public boolean containsServersStatus(
        int key) {
      
      return internalGetServersStatus().getMap().containsKey(key);
    }
    /**
     * Use {@link #getServersStatusMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatus() {
      return getServersStatusMap();
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatusMap() {
      return internalGetServersStatus().getMap();
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrDefault(
        int key,
        com.yorha.proto.StructMsg.ZoneStatus defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
          internalGetServersStatus().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
          internalGetServersStatus().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetServersStatus(),
          ServersStatusDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> entry
           : internalGetServersStatus().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
        serversStatus__ = ServersStatusDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, serversStatus__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns other = (com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns) obj;

      if (!internalGetServersStatus().equals(
          other.internalGetServersStatus())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetServersStatus().getMap().isEmpty()) {
        hash = (37 * hash) + SERVERSSTATUS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetServersStatus().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetMultiZoneStatusAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetMultiZoneStatusAns)
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetServersStatus();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableServersStatus();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns.class, com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableServersStatus().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetMultiZoneStatusAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns build() {
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns buildPartial() {
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns result = new com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns(this);
        int from_bitField0_ = bitField0_;
        result.serversStatus_ = internalGetServersStatus();
        result.serversStatus_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns other) {
        if (other == com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns.getDefaultInstance()) return this;
        internalGetMutableServersStatus().mergeFrom(
            other.internalGetServersStatus());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> serversStatus_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
      internalGetServersStatus() {
        if (serversStatus_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ServersStatusDefaultEntryHolder.defaultEntry);
        }
        return serversStatus_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
      internalGetMutableServersStatus() {
        onChanged();;
        if (serversStatus_ == null) {
          serversStatus_ = com.google.protobuf.MapField.newMapField(
              ServersStatusDefaultEntryHolder.defaultEntry);
        }
        if (!serversStatus_.isMutable()) {
          serversStatus_ = serversStatus_.copy();
        }
        return serversStatus_;
      }

      public int getServersStatusCount() {
        return internalGetServersStatus().getMap().size();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */

      @java.lang.Override
      public boolean containsServersStatus(
          int key) {
        
        return internalGetServersStatus().getMap().containsKey(key);
      }
      /**
       * Use {@link #getServersStatusMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatus() {
        return getServersStatusMap();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatusMap() {
        return internalGetServersStatus().getMap();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrDefault(
          int key,
          com.yorha.proto.StructMsg.ZoneStatus defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
            internalGetServersStatus().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
            internalGetServersStatus().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearServersStatus() {
        internalGetMutableServersStatus().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */

      public Builder removeServersStatus(
          int key) {
        
        internalGetMutableServersStatus().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
      getMutableServersStatus() {
        return internalGetMutableServersStatus().getMutableMap();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      public Builder putServersStatus(
          int key,
          com.yorha.proto.StructMsg.ZoneStatus value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableServersStatus().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */

      public Builder putAllServersStatus(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> values) {
        internalGetMutableServersStatus().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetMultiZoneStatusAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetMultiZoneStatusAns)
    private static final com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns();
    }

    public static com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetMultiZoneStatusAns>
        PARSER = new com.google.protobuf.AbstractParser<GetMultiZoneStatusAns>() {
      @java.lang.Override
      public GetMultiZoneStatusAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetMultiZoneStatusAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetMultiZoneStatusAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetMultiZoneStatusAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetMultiZoneStatusAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetZonesUnderSeasonAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetZonesUnderSeasonAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return Whether the zoneSeason field is set.
     */
    boolean hasZoneSeason();
    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return The zoneSeason.
     */
    com.yorha.proto.CommonEnum.ZoneSeason getZoneSeason();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetZonesUnderSeasonAsk}
   */
  public static final class GetZonesUnderSeasonAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetZonesUnderSeasonAsk)
      GetZonesUnderSeasonAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetZonesUnderSeasonAsk.newBuilder() to construct.
    private GetZonesUnderSeasonAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetZonesUnderSeasonAsk() {
      zoneSeason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetZonesUnderSeasonAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetZonesUnderSeasonAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ZoneSeason value = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                zoneSeason_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk.class, com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ZONESEASON_FIELD_NUMBER = 1;
    private int zoneSeason_;
    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return Whether the zoneSeason field is set.
     */
    @java.lang.Override public boolean hasZoneSeason() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return The zoneSeason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ZoneSeason getZoneSeason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ZoneSeason result = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(zoneSeason_);
      return result == null ? com.yorha.proto.CommonEnum.ZoneSeason.ZS_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, zoneSeason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, zoneSeason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk other = (com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk) obj;

      if (hasZoneSeason() != other.hasZoneSeason()) return false;
      if (hasZoneSeason()) {
        if (zoneSeason_ != other.zoneSeason_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasZoneSeason()) {
        hash = (37 * hash) + ZONESEASON_FIELD_NUMBER;
        hash = (53 * hash) + zoneSeason_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetZonesUnderSeasonAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetZonesUnderSeasonAsk)
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk.class, com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneSeason_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk build() {
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk buildPartial() {
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk result = new com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.zoneSeason_ = zoneSeason_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk other) {
        if (other == com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk.getDefaultInstance()) return this;
        if (other.hasZoneSeason()) {
          setZoneSeason(other.getZoneSeason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int zoneSeason_ = 0;
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @return Whether the zoneSeason field is set.
       */
      @java.lang.Override public boolean hasZoneSeason() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @return The zoneSeason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ZoneSeason getZoneSeason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ZoneSeason result = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(zoneSeason_);
        return result == null ? com.yorha.proto.CommonEnum.ZoneSeason.ZS_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @param value The zoneSeason to set.
       * @return This builder for chaining.
       */
      public Builder setZoneSeason(com.yorha.proto.CommonEnum.ZoneSeason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        zoneSeason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneSeason() {
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneSeason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetZonesUnderSeasonAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetZonesUnderSeasonAsk)
    private static final com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk();
    }

    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetZonesUnderSeasonAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetZonesUnderSeasonAsk>() {
      @java.lang.Override
      public GetZonesUnderSeasonAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetZonesUnderSeasonAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetZonesUnderSeasonAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetZonesUnderSeasonAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetZonesUnderSeasonAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetZonesUnderSeasonAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    int getZoneIdsCount();
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    boolean containsZoneIds(
        int key);
    /**
     * Use {@link #getZoneIdsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getZoneIds();
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getZoneIdsMap();
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */

    int getZoneIdsOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */

    int getZoneIdsOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetZonesUnderSeasonAns}
   */
  public static final class GetZonesUnderSeasonAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetZonesUnderSeasonAns)
      GetZonesUnderSeasonAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetZonesUnderSeasonAns.newBuilder() to construct.
    private GetZonesUnderSeasonAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetZonesUnderSeasonAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetZonesUnderSeasonAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetZonesUnderSeasonAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                zoneIds_ = com.google.protobuf.MapField.newMapField(
                    ZoneIdsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              zoneIds__ = input.readMessage(
                  ZoneIdsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              zoneIds_.getMutableMap().put(
                  zoneIds__.getKey(), zoneIds__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetZoneIds();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns.class, com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns.Builder.class);
    }

    public static final int ZONEIDS_FIELD_NUMBER = 1;
    private static final class ZoneIdsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAns_ZoneIdsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> zoneIds_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetZoneIds() {
      if (zoneIds_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ZoneIdsDefaultEntryHolder.defaultEntry);
      }
      return zoneIds_;
    }

    public int getZoneIdsCount() {
      return internalGetZoneIds().getMap().size();
    }
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */

    @java.lang.Override
    public boolean containsZoneIds(
        int key) {
      
      return internalGetZoneIds().getMap().containsKey(key);
    }
    /**
     * Use {@link #getZoneIdsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIds() {
      return getZoneIdsMap();
    }
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIdsMap() {
      return internalGetZoneIds().getMap();
    }
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    @java.lang.Override

    public int getZoneIdsOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetZoneIds().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * key=zoneId, val=kvkZoneId
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    @java.lang.Override

    public int getZoneIdsOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetZoneIds().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetZoneIds(),
          ZoneIdsDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetZoneIds().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        zoneIds__ = ZoneIdsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, zoneIds__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns other = (com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns) obj;

      if (!internalGetZoneIds().equals(
          other.internalGetZoneIds())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetZoneIds().getMap().isEmpty()) {
        hash = (37 * hash) + ZONEIDS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetZoneIds().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetZonesUnderSeasonAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetZonesUnderSeasonAns)
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetZoneIds();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableZoneIds();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns.class, com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableZoneIds().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZonesUnderSeasonAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns build() {
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns buildPartial() {
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns result = new com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns(this);
        int from_bitField0_ = bitField0_;
        result.zoneIds_ = internalGetZoneIds();
        result.zoneIds_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns other) {
        if (other == com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns.getDefaultInstance()) return this;
        internalGetMutableZoneIds().mergeFrom(
            other.internalGetZoneIds());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> zoneIds_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetZoneIds() {
        if (zoneIds_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ZoneIdsDefaultEntryHolder.defaultEntry);
        }
        return zoneIds_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableZoneIds() {
        onChanged();;
        if (zoneIds_ == null) {
          zoneIds_ = com.google.protobuf.MapField.newMapField(
              ZoneIdsDefaultEntryHolder.defaultEntry);
        }
        if (!zoneIds_.isMutable()) {
          zoneIds_ = zoneIds_.copy();
        }
        return zoneIds_;
      }

      public int getZoneIdsCount() {
        return internalGetZoneIds().getMap().size();
      }
      /**
       * <pre>
       * key=zoneId, val=kvkZoneId
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */

      @java.lang.Override
      public boolean containsZoneIds(
          int key) {
        
        return internalGetZoneIds().getMap().containsKey(key);
      }
      /**
       * Use {@link #getZoneIdsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIds() {
        return getZoneIdsMap();
      }
      /**
       * <pre>
       * key=zoneId, val=kvkZoneId
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIdsMap() {
        return internalGetZoneIds().getMap();
      }
      /**
       * <pre>
       * key=zoneId, val=kvkZoneId
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      @java.lang.Override

      public int getZoneIdsOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetZoneIds().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * key=zoneId, val=kvkZoneId
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      @java.lang.Override

      public int getZoneIdsOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetZoneIds().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearZoneIds() {
        internalGetMutableZoneIds().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * key=zoneId, val=kvkZoneId
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */

      public Builder removeZoneIds(
          int key) {
        
        internalGetMutableZoneIds().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableZoneIds() {
        return internalGetMutableZoneIds().getMutableMap();
      }
      /**
       * <pre>
       * key=zoneId, val=kvkZoneId
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      public Builder putZoneIds(
          int key,
          int value) {
        
        
        internalGetMutableZoneIds().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * key=zoneId, val=kvkZoneId
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */

      public Builder putAllZoneIds(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableZoneIds().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetZonesUnderSeasonAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetZonesUnderSeasonAns)
    private static final com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns();
    }

    public static com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetZonesUnderSeasonAns>
        PARSER = new com.google.protobuf.AbstractParser<GetZonesUnderSeasonAns>() {
      @java.lang.Override
      public GetZonesUnderSeasonAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetZonesUnderSeasonAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetZonesUnderSeasonAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetZonesUnderSeasonAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetZonesUnderSeasonAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetZoneMileStoneAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetZoneMileStoneAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetZoneMileStoneAsk}
   */
  public static final class GetZoneMileStoneAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetZoneMileStoneAsk)
      GetZoneMileStoneAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetZoneMileStoneAsk.newBuilder() to construct.
    private GetZoneMileStoneAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetZoneMileStoneAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetZoneMileStoneAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetZoneMileStoneAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk.class, com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ZONEID_FIELD_NUMBER = 1;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 zoneId = 1;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk other = (com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk) obj;

      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetZoneMileStoneAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetZoneMileStoneAsk)
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk.class, com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk build() {
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk buildPartial() {
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk result = new com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk other) {
        if (other == com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk.getDefaultInstance()) return this;
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000001;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetZoneMileStoneAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetZoneMileStoneAsk)
    private static final com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk();
    }

    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetZoneMileStoneAsk>
        PARSER = new com.google.protobuf.AbstractParser<GetZoneMileStoneAsk>() {
      @java.lang.Override
      public GetZoneMileStoneAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetZoneMileStoneAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetZoneMileStoneAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetZoneMileStoneAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetZoneMileStoneAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetZoneMileStoneAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GetZoneMileStoneAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 里程碑id(-1==里程碑已结束)
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return Whether the mileStoneId field is set.
     */
    boolean hasMileStoneId();
    /**
     * <pre>
     * 里程碑id(-1==里程碑已结束)
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return The mileStoneId.
     */
    int getMileStoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GetZoneMileStoneAns}
   */
  public static final class GetZoneMileStoneAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GetZoneMileStoneAns)
      GetZoneMileStoneAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetZoneMileStoneAns.newBuilder() to construct.
    private GetZoneMileStoneAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetZoneMileStoneAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetZoneMileStoneAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetZoneMileStoneAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              mileStoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsZoneCard.GetZoneMileStoneAns.class, com.yorha.proto.SsZoneCard.GetZoneMileStoneAns.Builder.class);
    }

    private int bitField0_;
    public static final int MILESTONEID_FIELD_NUMBER = 1;
    private int mileStoneId_;
    /**
     * <pre>
     * 里程碑id(-1==里程碑已结束)
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return Whether the mileStoneId field is set.
     */
    @java.lang.Override
    public boolean hasMileStoneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 里程碑id(-1==里程碑已结束)
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return The mileStoneId.
     */
    @java.lang.Override
    public int getMileStoneId() {
      return mileStoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, mileStoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mileStoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsZoneCard.GetZoneMileStoneAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsZoneCard.GetZoneMileStoneAns other = (com.yorha.proto.SsZoneCard.GetZoneMileStoneAns) obj;

      if (hasMileStoneId() != other.hasMileStoneId()) return false;
      if (hasMileStoneId()) {
        if (getMileStoneId()
            != other.getMileStoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMileStoneId()) {
        hash = (37 * hash) + MILESTONEID_FIELD_NUMBER;
        hash = (53 * hash) + getMileStoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsZoneCard.GetZoneMileStoneAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GetZoneMileStoneAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GetZoneMileStoneAns)
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsZoneCard.GetZoneMileStoneAns.class, com.yorha.proto.SsZoneCard.GetZoneMileStoneAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsZoneCard.GetZoneMileStoneAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        mileStoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsZoneCard.internal_static_com_yorha_proto_GetZoneMileStoneAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZoneMileStoneAns getDefaultInstanceForType() {
        return com.yorha.proto.SsZoneCard.GetZoneMileStoneAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZoneMileStoneAns build() {
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsZoneCard.GetZoneMileStoneAns buildPartial() {
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAns result = new com.yorha.proto.SsZoneCard.GetZoneMileStoneAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.mileStoneId_ = mileStoneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsZoneCard.GetZoneMileStoneAns) {
          return mergeFrom((com.yorha.proto.SsZoneCard.GetZoneMileStoneAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsZoneCard.GetZoneMileStoneAns other) {
        if (other == com.yorha.proto.SsZoneCard.GetZoneMileStoneAns.getDefaultInstance()) return this;
        if (other.hasMileStoneId()) {
          setMileStoneId(other.getMileStoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsZoneCard.GetZoneMileStoneAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsZoneCard.GetZoneMileStoneAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int mileStoneId_ ;
      /**
       * <pre>
       * 里程碑id(-1==里程碑已结束)
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return Whether the mileStoneId field is set.
       */
      @java.lang.Override
      public boolean hasMileStoneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 里程碑id(-1==里程碑已结束)
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return The mileStoneId.
       */
      @java.lang.Override
      public int getMileStoneId() {
        return mileStoneId_;
      }
      /**
       * <pre>
       * 里程碑id(-1==里程碑已结束)
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @param value The mileStoneId to set.
       * @return This builder for chaining.
       */
      public Builder setMileStoneId(int value) {
        bitField0_ |= 0x00000001;
        mileStoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 里程碑id(-1==里程碑已结束)
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMileStoneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        mileStoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GetZoneMileStoneAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GetZoneMileStoneAns)
    private static final com.yorha.proto.SsZoneCard.GetZoneMileStoneAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsZoneCard.GetZoneMileStoneAns();
    }

    public static com.yorha.proto.SsZoneCard.GetZoneMileStoneAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GetZoneMileStoneAns>
        PARSER = new com.google.protobuf.AbstractParser<GetZoneMileStoneAns>() {
      @java.lang.Override
      public GetZoneMileStoneAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetZoneMileStoneAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetZoneMileStoneAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetZoneMileStoneAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsZoneCard.GetZoneMileStoneAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetAllZoneInfoAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetAllZoneInfoAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetAllZoneInfoAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetAllZoneInfoAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetMultiZoneStatusAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetMultiZoneStatusAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetMultiZoneStatusAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetMultiZoneStatusAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetMultiZoneStatusAns_ServersStatusEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetMultiZoneStatusAns_ServersStatusEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetZonesUnderSeasonAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetZonesUnderSeasonAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetZonesUnderSeasonAns_ZoneIdsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetZonesUnderSeasonAns_ZoneIdsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetZoneMileStoneAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetZoneMileStoneAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GetZoneMileStoneAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GetZoneMileStoneAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'ss_proto/gen/zoneCard/ss_zoneCard.prot" +
      "o\022\017com.yorha.proto\032%ss_proto/gen/common/" +
      "common_enum.proto\032$ss_proto/gen/common/c" +
      "ommon_msg.proto\032$ss_proto/gen/common/str" +
      "uct_msg.proto\")\n\021GetAllZoneInfoAsk\022\024\n\014is" +
      "SuperWhite\030\001 \001(\010\"L\n\021GetAllZoneInfoAns\0227\n" +
      "\016zoneServerList\030\001 \003(\0132\037.com.yorha.proto." +
      "ZoneServerInfo\"(\n\025GetMultiZoneStatusAsk\022" +
      "\017\n\007zoneIds\030\001 \003(\005\"\274\001\n\025GetMultiZoneStatusA" +
      "ns\022P\n\rserversStatus\030\001 \003(\01329.com.yorha.pr" +
      "oto.GetMultiZoneStatusAns.ServersStatusE" +
      "ntry\032Q\n\022ServersStatusEntry\022\013\n\003key\030\001 \001(\005\022" +
      "*\n\005value\030\002 \001(\0132\033.com.yorha.proto.ZoneSta" +
      "tus:\0028\001\"I\n\026GetZonesUnderSeasonAsk\022/\n\nzon" +
      "eSeason\030\001 \001(\0162\033.com.yorha.proto.ZoneSeas" +
      "on\"\217\001\n\026GetZonesUnderSeasonAns\022E\n\007zoneIds" +
      "\030\001 \003(\01324.com.yorha.proto.GetZonesUnderSe" +
      "asonAns.ZoneIdsEntry\032.\n\014ZoneIdsEntry\022\013\n\003" +
      "key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"%\n\023GetZoneM" +
      "ileStoneAsk\022\016\n\006zoneId\030\001 \001(\005\"*\n\023GetZoneMi" +
      "leStoneAns\022\023\n\013mileStoneId\030\001 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_GetAllZoneInfoAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_GetAllZoneInfoAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetAllZoneInfoAsk_descriptor,
        new java.lang.String[] { "IsSuperWhite", });
    internal_static_com_yorha_proto_GetAllZoneInfoAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_GetAllZoneInfoAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetAllZoneInfoAns_descriptor,
        new java.lang.String[] { "ZoneServerList", });
    internal_static_com_yorha_proto_GetMultiZoneStatusAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_GetMultiZoneStatusAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetMultiZoneStatusAsk_descriptor,
        new java.lang.String[] { "ZoneIds", });
    internal_static_com_yorha_proto_GetMultiZoneStatusAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_GetMultiZoneStatusAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetMultiZoneStatusAns_descriptor,
        new java.lang.String[] { "ServersStatus", });
    internal_static_com_yorha_proto_GetMultiZoneStatusAns_ServersStatusEntry_descriptor =
      internal_static_com_yorha_proto_GetMultiZoneStatusAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_GetMultiZoneStatusAns_ServersStatusEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetMultiZoneStatusAns_ServersStatusEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetZonesUnderSeasonAsk_descriptor,
        new java.lang.String[] { "ZoneSeason", });
    internal_static_com_yorha_proto_GetZonesUnderSeasonAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_GetZonesUnderSeasonAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetZonesUnderSeasonAns_descriptor,
        new java.lang.String[] { "ZoneIds", });
    internal_static_com_yorha_proto_GetZonesUnderSeasonAns_ZoneIdsEntry_descriptor =
      internal_static_com_yorha_proto_GetZonesUnderSeasonAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_GetZonesUnderSeasonAns_ZoneIdsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetZonesUnderSeasonAns_ZoneIdsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_GetZoneMileStoneAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_GetZoneMileStoneAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetZoneMileStoneAsk_descriptor,
        new java.lang.String[] { "ZoneId", });
    internal_static_com_yorha_proto_GetZoneMileStoneAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_GetZoneMileStoneAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GetZoneMileStoneAns_descriptor,
        new java.lang.String[] { "MileStoneId", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

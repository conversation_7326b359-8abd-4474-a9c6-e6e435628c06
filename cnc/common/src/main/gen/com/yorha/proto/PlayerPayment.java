// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_payment.proto

package com.yorha.proto;

public final class PlayerPayment {
  private PlayerPayment() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_JudgeRecharge_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_JudgeRecharge_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return Whether the chargeSdkId field is set.
     */
    boolean hasChargeSdkId();
    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return The chargeSdkId.
     */
    int getChargeSdkId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_JudgeRecharge_C2S}
   */
  public static final class Player_JudgeRecharge_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_JudgeRecharge_C2S)
      Player_JudgeRecharge_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_JudgeRecharge_C2S.newBuilder() to construct.
    private Player_JudgeRecharge_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_JudgeRecharge_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_JudgeRecharge_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_JudgeRecharge_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              chargeSdkId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S.class, com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CHARGESDKID_FIELD_NUMBER = 1;
    private int chargeSdkId_;
    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return Whether the chargeSdkId field is set.
     */
    @java.lang.Override
    public boolean hasChargeSdkId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return The chargeSdkId.
     */
    @java.lang.Override
    public int getChargeSdkId() {
      return chargeSdkId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, chargeSdkId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, chargeSdkId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S other = (com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S) obj;

      if (hasChargeSdkId() != other.hasChargeSdkId()) return false;
      if (hasChargeSdkId()) {
        if (getChargeSdkId()
            != other.getChargeSdkId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChargeSdkId()) {
        hash = (37 * hash) + CHARGESDKID_FIELD_NUMBER;
        hash = (53 * hash) + getChargeSdkId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_JudgeRecharge_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_JudgeRecharge_C2S)
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S.class, com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chargeSdkId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S build() {
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S buildPartial() {
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S result = new com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chargeSdkId_ = chargeSdkId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S other) {
        if (other == com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S.getDefaultInstance()) return this;
        if (other.hasChargeSdkId()) {
          setChargeSdkId(other.getChargeSdkId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int chargeSdkId_ ;
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @return Whether the chargeSdkId field is set.
       */
      @java.lang.Override
      public boolean hasChargeSdkId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @return The chargeSdkId.
       */
      @java.lang.Override
      public int getChargeSdkId() {
        return chargeSdkId_;
      }
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @param value The chargeSdkId to set.
       * @return This builder for chaining.
       */
      public Builder setChargeSdkId(int value) {
        bitField0_ |= 0x00000001;
        chargeSdkId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChargeSdkId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chargeSdkId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_JudgeRecharge_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_JudgeRecharge_C2S)
    private static final com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S();
    }

    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_JudgeRecharge_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_JudgeRecharge_C2S>() {
      @java.lang.Override
      public Player_JudgeRecharge_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_JudgeRecharge_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_JudgeRecharge_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_JudgeRecharge_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_JudgeRecharge_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_JudgeRecharge_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return Whether the chargeSdkId field is set.
     */
    boolean hasChargeSdkId();
    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return The chargeSdkId.
     */
    int getChargeSdkId();

    /**
     * <pre>
     * hope平台返回的指令流水号
     * </pre>
     *
     * <code>optional string traceId = 2;</code>
     * @return Whether the traceId field is set.
     */
    boolean hasTraceId();
    /**
     * <pre>
     * hope平台返回的指令流水号
     * </pre>
     *
     * <code>optional string traceId = 2;</code>
     * @return The traceId.
     */
    java.lang.String getTraceId();
    /**
     * <pre>
     * hope平台返回的指令流水号
     * </pre>
     *
     * <code>optional string traceId = 2;</code>
     * @return The bytes for traceId.
     */
    com.google.protobuf.ByteString
        getTraceIdBytes();

    /**
     * <pre>
     * hope平台指令
     * </pre>
     *
     * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
     * @return Whether the instruction field is set.
     */
    boolean hasInstruction();
    /**
     * <pre>
     * hope平台指令
     * </pre>
     *
     * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
     * @return The instruction.
     */
    com.yorha.proto.User.HopeInstruction getInstruction();
    /**
     * <pre>
     * hope平台指令
     * </pre>
     *
     * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
     */
    com.yorha.proto.User.HopeInstructionOrBuilder getInstructionOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_JudgeRecharge_S2C}
   */
  public static final class Player_JudgeRecharge_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_JudgeRecharge_S2C)
      Player_JudgeRecharge_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_JudgeRecharge_S2C.newBuilder() to construct.
    private Player_JudgeRecharge_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_JudgeRecharge_S2C() {
      traceId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_JudgeRecharge_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_JudgeRecharge_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              chargeSdkId_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              traceId_ = bs;
              break;
            }
            case 26: {
              com.yorha.proto.User.HopeInstruction.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = instruction_.toBuilder();
              }
              instruction_ = input.readMessage(com.yorha.proto.User.HopeInstruction.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(instruction_);
                instruction_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C.class, com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int CHARGESDKID_FIELD_NUMBER = 1;
    private int chargeSdkId_;
    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return Whether the chargeSdkId field is set.
     */
    @java.lang.Override
    public boolean hasChargeSdkId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 chargeSdkId = 1;</code>
     * @return The chargeSdkId.
     */
    @java.lang.Override
    public int getChargeSdkId() {
      return chargeSdkId_;
    }

    public static final int TRACEID_FIELD_NUMBER = 2;
    private volatile java.lang.Object traceId_;
    /**
     * <pre>
     * hope平台返回的指令流水号
     * </pre>
     *
     * <code>optional string traceId = 2;</code>
     * @return Whether the traceId field is set.
     */
    @java.lang.Override
    public boolean hasTraceId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * hope平台返回的指令流水号
     * </pre>
     *
     * <code>optional string traceId = 2;</code>
     * @return The traceId.
     */
    @java.lang.Override
    public java.lang.String getTraceId() {
      java.lang.Object ref = traceId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          traceId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * hope平台返回的指令流水号
     * </pre>
     *
     * <code>optional string traceId = 2;</code>
     * @return The bytes for traceId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTraceIdBytes() {
      java.lang.Object ref = traceId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        traceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INSTRUCTION_FIELD_NUMBER = 3;
    private com.yorha.proto.User.HopeInstruction instruction_;
    /**
     * <pre>
     * hope平台指令
     * </pre>
     *
     * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
     * @return Whether the instruction field is set.
     */
    @java.lang.Override
    public boolean hasInstruction() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * hope平台指令
     * </pre>
     *
     * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
     * @return The instruction.
     */
    @java.lang.Override
    public com.yorha.proto.User.HopeInstruction getInstruction() {
      return instruction_ == null ? com.yorha.proto.User.HopeInstruction.getDefaultInstance() : instruction_;
    }
    /**
     * <pre>
     * hope平台指令
     * </pre>
     *
     * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.User.HopeInstructionOrBuilder getInstructionOrBuilder() {
      return instruction_ == null ? com.yorha.proto.User.HopeInstruction.getDefaultInstance() : instruction_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, chargeSdkId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, traceId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getInstruction());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, chargeSdkId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, traceId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getInstruction());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C other = (com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C) obj;

      if (hasChargeSdkId() != other.hasChargeSdkId()) return false;
      if (hasChargeSdkId()) {
        if (getChargeSdkId()
            != other.getChargeSdkId()) return false;
      }
      if (hasTraceId() != other.hasTraceId()) return false;
      if (hasTraceId()) {
        if (!getTraceId()
            .equals(other.getTraceId())) return false;
      }
      if (hasInstruction() != other.hasInstruction()) return false;
      if (hasInstruction()) {
        if (!getInstruction()
            .equals(other.getInstruction())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChargeSdkId()) {
        hash = (37 * hash) + CHARGESDKID_FIELD_NUMBER;
        hash = (53 * hash) + getChargeSdkId();
      }
      if (hasTraceId()) {
        hash = (37 * hash) + TRACEID_FIELD_NUMBER;
        hash = (53 * hash) + getTraceId().hashCode();
      }
      if (hasInstruction()) {
        hash = (37 * hash) + INSTRUCTION_FIELD_NUMBER;
        hash = (53 * hash) + getInstruction().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_JudgeRecharge_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_JudgeRecharge_S2C)
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C.class, com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInstructionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chargeSdkId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        traceId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        if (instructionBuilder_ == null) {
          instruction_ = null;
        } else {
          instructionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C build() {
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C buildPartial() {
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C result = new com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chargeSdkId_ = chargeSdkId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.traceId_ = traceId_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (instructionBuilder_ == null) {
            result.instruction_ = instruction_;
          } else {
            result.instruction_ = instructionBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C other) {
        if (other == com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C.getDefaultInstance()) return this;
        if (other.hasChargeSdkId()) {
          setChargeSdkId(other.getChargeSdkId());
        }
        if (other.hasTraceId()) {
          bitField0_ |= 0x00000002;
          traceId_ = other.traceId_;
          onChanged();
        }
        if (other.hasInstruction()) {
          mergeInstruction(other.getInstruction());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int chargeSdkId_ ;
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @return Whether the chargeSdkId field is set.
       */
      @java.lang.Override
      public boolean hasChargeSdkId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @return The chargeSdkId.
       */
      @java.lang.Override
      public int getChargeSdkId() {
        return chargeSdkId_;
      }
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @param value The chargeSdkId to set.
       * @return This builder for chaining.
       */
      public Builder setChargeSdkId(int value) {
        bitField0_ |= 0x00000001;
        chargeSdkId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 chargeSdkId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChargeSdkId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chargeSdkId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object traceId_ = "";
      /**
       * <pre>
       * hope平台返回的指令流水号
       * </pre>
       *
       * <code>optional string traceId = 2;</code>
       * @return Whether the traceId field is set.
       */
      public boolean hasTraceId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * hope平台返回的指令流水号
       * </pre>
       *
       * <code>optional string traceId = 2;</code>
       * @return The traceId.
       */
      public java.lang.String getTraceId() {
        java.lang.Object ref = traceId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            traceId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * hope平台返回的指令流水号
       * </pre>
       *
       * <code>optional string traceId = 2;</code>
       * @return The bytes for traceId.
       */
      public com.google.protobuf.ByteString
          getTraceIdBytes() {
        java.lang.Object ref = traceId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          traceId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * hope平台返回的指令流水号
       * </pre>
       *
       * <code>optional string traceId = 2;</code>
       * @param value The traceId to set.
       * @return This builder for chaining.
       */
      public Builder setTraceId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        traceId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * hope平台返回的指令流水号
       * </pre>
       *
       * <code>optional string traceId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTraceId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        traceId_ = getDefaultInstance().getTraceId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * hope平台返回的指令流水号
       * </pre>
       *
       * <code>optional string traceId = 2;</code>
       * @param value The bytes for traceId to set.
       * @return This builder for chaining.
       */
      public Builder setTraceIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        traceId_ = value;
        onChanged();
        return this;
      }

      private com.yorha.proto.User.HopeInstruction instruction_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder> instructionBuilder_;
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       * @return Whether the instruction field is set.
       */
      public boolean hasInstruction() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       * @return The instruction.
       */
      public com.yorha.proto.User.HopeInstruction getInstruction() {
        if (instructionBuilder_ == null) {
          return instruction_ == null ? com.yorha.proto.User.HopeInstruction.getDefaultInstance() : instruction_;
        } else {
          return instructionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       */
      public Builder setInstruction(com.yorha.proto.User.HopeInstruction value) {
        if (instructionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          instruction_ = value;
          onChanged();
        } else {
          instructionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       */
      public Builder setInstruction(
          com.yorha.proto.User.HopeInstruction.Builder builderForValue) {
        if (instructionBuilder_ == null) {
          instruction_ = builderForValue.build();
          onChanged();
        } else {
          instructionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       */
      public Builder mergeInstruction(com.yorha.proto.User.HopeInstruction value) {
        if (instructionBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              instruction_ != null &&
              instruction_ != com.yorha.proto.User.HopeInstruction.getDefaultInstance()) {
            instruction_ =
              com.yorha.proto.User.HopeInstruction.newBuilder(instruction_).mergeFrom(value).buildPartial();
          } else {
            instruction_ = value;
          }
          onChanged();
        } else {
          instructionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       */
      public Builder clearInstruction() {
        if (instructionBuilder_ == null) {
          instruction_ = null;
          onChanged();
        } else {
          instructionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       */
      public com.yorha.proto.User.HopeInstruction.Builder getInstructionBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getInstructionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       */
      public com.yorha.proto.User.HopeInstructionOrBuilder getInstructionOrBuilder() {
        if (instructionBuilder_ != null) {
          return instructionBuilder_.getMessageOrBuilder();
        } else {
          return instruction_ == null ?
              com.yorha.proto.User.HopeInstruction.getDefaultInstance() : instruction_;
        }
      }
      /**
       * <pre>
       * hope平台指令
       * </pre>
       *
       * <code>optional .com.yorha.proto.HopeInstruction instruction = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder> 
          getInstructionFieldBuilder() {
        if (instructionBuilder_ == null) {
          instructionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.User.HopeInstruction, com.yorha.proto.User.HopeInstruction.Builder, com.yorha.proto.User.HopeInstructionOrBuilder>(
                  getInstruction(),
                  getParentForChildren(),
                  isClean());
          instruction_ = null;
        }
        return instructionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_JudgeRecharge_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_JudgeRecharge_S2C)
    private static final com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C();
    }

    public static com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_JudgeRecharge_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_JudgeRecharge_S2C>() {
      @java.lang.Override
      public Player_JudgeRecharge_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_JudgeRecharge_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_JudgeRecharge_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_JudgeRecharge_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_JudgeRecharge_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PaymentOver_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PaymentOver_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 对应支付表charge_base页签中的id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return Whether the chargeBaseId field is set.
     */
    boolean hasChargeBaseId();
    /**
     * <pre>
     * 对应支付表charge_base页签中的id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return The chargeBaseId.
     */
    int getChargeBaseId();

    /**
     * <pre>
     * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
     * </pre>
     *
     * <code>optional int32 ifDouble = 2;</code>
     * @return Whether the ifDouble field is set.
     */
    boolean hasIfDouble();
    /**
     * <pre>
     * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
     * </pre>
     *
     * <code>optional int32 ifDouble = 2;</code>
     * @return The ifDouble.
     */
    int getIfDouble();
  }
  /**
   * <pre>
   * 支付完成的回调接口，会和midas做一次货币同步；请求参数会用于qlog，暂时不会做其他逻辑
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_PaymentOver_C2S}
   */
  public static final class Player_PaymentOver_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PaymentOver_C2S)
      Player_PaymentOver_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PaymentOver_C2S.newBuilder() to construct.
    private Player_PaymentOver_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PaymentOver_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PaymentOver_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PaymentOver_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              chargeBaseId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ifDouble_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S.class, com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CHARGEBASEID_FIELD_NUMBER = 1;
    private int chargeBaseId_;
    /**
     * <pre>
     * 对应支付表charge_base页签中的id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return Whether the chargeBaseId field is set.
     */
    @java.lang.Override
    public boolean hasChargeBaseId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 对应支付表charge_base页签中的id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return The chargeBaseId.
     */
    @java.lang.Override
    public int getChargeBaseId() {
      return chargeBaseId_;
    }

    public static final int IFDOUBLE_FIELD_NUMBER = 2;
    private int ifDouble_;
    /**
     * <pre>
     * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
     * </pre>
     *
     * <code>optional int32 ifDouble = 2;</code>
     * @return Whether the ifDouble field is set.
     */
    @java.lang.Override
    public boolean hasIfDouble() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
     * </pre>
     *
     * <code>optional int32 ifDouble = 2;</code>
     * @return The ifDouble.
     */
    @java.lang.Override
    public int getIfDouble() {
      return ifDouble_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, chargeBaseId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, ifDouble_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, chargeBaseId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, ifDouble_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S other = (com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S) obj;

      if (hasChargeBaseId() != other.hasChargeBaseId()) return false;
      if (hasChargeBaseId()) {
        if (getChargeBaseId()
            != other.getChargeBaseId()) return false;
      }
      if (hasIfDouble() != other.hasIfDouble()) return false;
      if (hasIfDouble()) {
        if (getIfDouble()
            != other.getIfDouble()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChargeBaseId()) {
        hash = (37 * hash) + CHARGEBASEID_FIELD_NUMBER;
        hash = (53 * hash) + getChargeBaseId();
      }
      if (hasIfDouble()) {
        hash = (37 * hash) + IFDOUBLE_FIELD_NUMBER;
        hash = (53 * hash) + getIfDouble();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 支付完成的回调接口，会和midas做一次货币同步；请求参数会用于qlog，暂时不会做其他逻辑
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_PaymentOver_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PaymentOver_C2S)
        com.yorha.proto.PlayerPayment.Player_PaymentOver_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S.class, com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chargeBaseId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        ifDouble_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S build() {
        com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S buildPartial() {
        com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S result = new com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chargeBaseId_ = chargeBaseId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ifDouble_ = ifDouble_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S other) {
        if (other == com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S.getDefaultInstance()) return this;
        if (other.hasChargeBaseId()) {
          setChargeBaseId(other.getChargeBaseId());
        }
        if (other.hasIfDouble()) {
          setIfDouble(other.getIfDouble());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int chargeBaseId_ ;
      /**
       * <pre>
       * 对应支付表charge_base页签中的id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @return Whether the chargeBaseId field is set.
       */
      @java.lang.Override
      public boolean hasChargeBaseId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 对应支付表charge_base页签中的id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @return The chargeBaseId.
       */
      @java.lang.Override
      public int getChargeBaseId() {
        return chargeBaseId_;
      }
      /**
       * <pre>
       * 对应支付表charge_base页签中的id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @param value The chargeBaseId to set.
       * @return This builder for chaining.
       */
      public Builder setChargeBaseId(int value) {
        bitField0_ |= 0x00000001;
        chargeBaseId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 对应支付表charge_base页签中的id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChargeBaseId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chargeBaseId_ = 0;
        onChanged();
        return this;
      }

      private int ifDouble_ ;
      /**
       * <pre>
       * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
       * </pre>
       *
       * <code>optional int32 ifDouble = 2;</code>
       * @return Whether the ifDouble field is set.
       */
      @java.lang.Override
      public boolean hasIfDouble() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
       * </pre>
       *
       * <code>optional int32 ifDouble = 2;</code>
       * @return The ifDouble.
       */
      @java.lang.Override
      public int getIfDouble() {
        return ifDouble_;
      }
      /**
       * <pre>
       * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
       * </pre>
       *
       * <code>optional int32 ifDouble = 2;</code>
       * @param value The ifDouble to set.
       * @return This builder for chaining.
       */
      public Builder setIfDouble(int value) {
        bitField0_ |= 0x00000002;
        ifDouble_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否双倍黄金。（首次双倍:1  非首次双倍:2 其余情况:0）
       * </pre>
       *
       * <code>optional int32 ifDouble = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfDouble() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ifDouble_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PaymentOver_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PaymentOver_C2S)
    private static final com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S();
    }

    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PaymentOver_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_PaymentOver_C2S>() {
      @java.lang.Override
      public Player_PaymentOver_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PaymentOver_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PaymentOver_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PaymentOver_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_PaymentOver_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PaymentOver_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PaymentOver_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PaymentOver_S2C}
   */
  public static final class Player_PaymentOver_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PaymentOver_S2C)
      Player_PaymentOver_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PaymentOver_S2C.newBuilder() to construct.
    private Player_PaymentOver_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PaymentOver_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PaymentOver_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PaymentOver_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C.class, com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C other = (com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PaymentOver_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PaymentOver_S2C)
        com.yorha.proto.PlayerPayment.Player_PaymentOver_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C.class, com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_PaymentOver_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C build() {
        com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C buildPartial() {
        com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C result = new com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C other) {
        if (other == com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PaymentOver_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PaymentOver_S2C)
    private static final com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C();
    }

    public static com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PaymentOver_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_PaymentOver_S2C>() {
      @java.lang.Override
      public Player_PaymentOver_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PaymentOver_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PaymentOver_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PaymentOver_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_PaymentOver_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ApplyGoodsOrder_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ApplyGoodsOrder_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 goodsId = 1;</code>
     * @return Whether the goodsId field is set.
     */
    boolean hasGoodsId();
    /**
     * <code>optional int32 goodsId = 1;</code>
     * @return The goodsId.
     */
    int getGoodsId();

    /**
     * <pre>
     * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
     * </pre>
     *
     * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
     * @return Whether the orderParam field is set.
     */
    boolean hasOrderParam();
    /**
     * <pre>
     * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
     * </pre>
     *
     * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
     * @return The orderParam.
     */
    com.yorha.proto.PlayerPayment.GoodsOrderParam getOrderParam();
    /**
     * <pre>
     * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
     * </pre>
     *
     * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
     */
    com.yorha.proto.PlayerPayment.GoodsOrderParamOrBuilder getOrderParamOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ApplyGoodsOrder_C2S}
   */
  public static final class Player_ApplyGoodsOrder_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ApplyGoodsOrder_C2S)
      Player_ApplyGoodsOrder_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ApplyGoodsOrder_C2S.newBuilder() to construct.
    private Player_ApplyGoodsOrder_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ApplyGoodsOrder_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ApplyGoodsOrder_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ApplyGoodsOrder_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              goodsId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = orderParam_.toBuilder();
              }
              orderParam_ = input.readMessage(com.yorha.proto.PlayerPayment.GoodsOrderParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(orderParam_);
                orderParam_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S.class, com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int GOODSID_FIELD_NUMBER = 1;
    private int goodsId_;
    /**
     * <code>optional int32 goodsId = 1;</code>
     * @return Whether the goodsId field is set.
     */
    @java.lang.Override
    public boolean hasGoodsId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 goodsId = 1;</code>
     * @return The goodsId.
     */
    @java.lang.Override
    public int getGoodsId() {
      return goodsId_;
    }

    public static final int ORDERPARAM_FIELD_NUMBER = 2;
    private com.yorha.proto.PlayerPayment.GoodsOrderParam orderParam_;
    /**
     * <pre>
     * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
     * </pre>
     *
     * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
     * @return Whether the orderParam field is set.
     */
    @java.lang.Override
    public boolean hasOrderParam() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
     * </pre>
     *
     * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
     * @return The orderParam.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPayment.GoodsOrderParam getOrderParam() {
      return orderParam_ == null ? com.yorha.proto.PlayerPayment.GoodsOrderParam.getDefaultInstance() : orderParam_;
    }
    /**
     * <pre>
     * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
     * </pre>
     *
     * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPayment.GoodsOrderParamOrBuilder getOrderParamOrBuilder() {
      return orderParam_ == null ? com.yorha.proto.PlayerPayment.GoodsOrderParam.getDefaultInstance() : orderParam_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, goodsId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getOrderParam());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, goodsId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getOrderParam());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S other = (com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S) obj;

      if (hasGoodsId() != other.hasGoodsId()) return false;
      if (hasGoodsId()) {
        if (getGoodsId()
            != other.getGoodsId()) return false;
      }
      if (hasOrderParam() != other.hasOrderParam()) return false;
      if (hasOrderParam()) {
        if (!getOrderParam()
            .equals(other.getOrderParam())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGoodsId()) {
        hash = (37 * hash) + GOODSID_FIELD_NUMBER;
        hash = (53 * hash) + getGoodsId();
      }
      if (hasOrderParam()) {
        hash = (37 * hash) + ORDERPARAM_FIELD_NUMBER;
        hash = (53 * hash) + getOrderParam().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ApplyGoodsOrder_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ApplyGoodsOrder_C2S)
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S.class, com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getOrderParamFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        goodsId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (orderParamBuilder_ == null) {
          orderParam_ = null;
        } else {
          orderParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S build() {
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S buildPartial() {
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S result = new com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.goodsId_ = goodsId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (orderParamBuilder_ == null) {
            result.orderParam_ = orderParam_;
          } else {
            result.orderParam_ = orderParamBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S other) {
        if (other == com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S.getDefaultInstance()) return this;
        if (other.hasGoodsId()) {
          setGoodsId(other.getGoodsId());
        }
        if (other.hasOrderParam()) {
          mergeOrderParam(other.getOrderParam());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int goodsId_ ;
      /**
       * <code>optional int32 goodsId = 1;</code>
       * @return Whether the goodsId field is set.
       */
      @java.lang.Override
      public boolean hasGoodsId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 goodsId = 1;</code>
       * @return The goodsId.
       */
      @java.lang.Override
      public int getGoodsId() {
        return goodsId_;
      }
      /**
       * <code>optional int32 goodsId = 1;</code>
       * @param value The goodsId to set.
       * @return This builder for chaining.
       */
      public Builder setGoodsId(int value) {
        bitField0_ |= 0x00000001;
        goodsId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 goodsId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        goodsId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.PlayerPayment.GoodsOrderParam orderParam_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPayment.GoodsOrderParam, com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder, com.yorha.proto.PlayerPayment.GoodsOrderParamOrBuilder> orderParamBuilder_;
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       * @return Whether the orderParam field is set.
       */
      public boolean hasOrderParam() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       * @return The orderParam.
       */
      public com.yorha.proto.PlayerPayment.GoodsOrderParam getOrderParam() {
        if (orderParamBuilder_ == null) {
          return orderParam_ == null ? com.yorha.proto.PlayerPayment.GoodsOrderParam.getDefaultInstance() : orderParam_;
        } else {
          return orderParamBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       */
      public Builder setOrderParam(com.yorha.proto.PlayerPayment.GoodsOrderParam value) {
        if (orderParamBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          orderParam_ = value;
          onChanged();
        } else {
          orderParamBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       */
      public Builder setOrderParam(
          com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder builderForValue) {
        if (orderParamBuilder_ == null) {
          orderParam_ = builderForValue.build();
          onChanged();
        } else {
          orderParamBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       */
      public Builder mergeOrderParam(com.yorha.proto.PlayerPayment.GoodsOrderParam value) {
        if (orderParamBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              orderParam_ != null &&
              orderParam_ != com.yorha.proto.PlayerPayment.GoodsOrderParam.getDefaultInstance()) {
            orderParam_ =
              com.yorha.proto.PlayerPayment.GoodsOrderParam.newBuilder(orderParam_).mergeFrom(value).buildPartial();
          } else {
            orderParam_ = value;
          }
          onChanged();
        } else {
          orderParamBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       */
      public Builder clearOrderParam() {
        if (orderParamBuilder_ == null) {
          orderParam_ = null;
          onChanged();
        } else {
          orderParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       */
      public com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder getOrderParamBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getOrderParamFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       */
      public com.yorha.proto.PlayerPayment.GoodsOrderParamOrBuilder getOrderParamOrBuilder() {
        if (orderParamBuilder_ != null) {
          return orderParamBuilder_.getMessageOrBuilder();
        } else {
          return orderParam_ == null ?
              com.yorha.proto.PlayerPayment.GoodsOrderParam.getDefaultInstance() : orderParam_;
        }
      }
      /**
       * <pre>
       * 有些礼包购买需要补充一些参数，比如随活动开放的礼包需要传活动id等等
       * </pre>
       *
       * <code>optional .com.yorha.proto.GoodsOrderParam orderParam = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPayment.GoodsOrderParam, com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder, com.yorha.proto.PlayerPayment.GoodsOrderParamOrBuilder> 
          getOrderParamFieldBuilder() {
        if (orderParamBuilder_ == null) {
          orderParamBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerPayment.GoodsOrderParam, com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder, com.yorha.proto.PlayerPayment.GoodsOrderParamOrBuilder>(
                  getOrderParam(),
                  getParentForChildren(),
                  isClean());
          orderParam_ = null;
        }
        return orderParamBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ApplyGoodsOrder_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ApplyGoodsOrder_C2S)
    private static final com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S();
    }

    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ApplyGoodsOrder_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ApplyGoodsOrder_C2S>() {
      @java.lang.Override
      public Player_ApplyGoodsOrder_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ApplyGoodsOrder_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ApplyGoodsOrder_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ApplyGoodsOrder_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GoodsOrderParamOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.GoodsOrderParam)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 礼包链活动、双队列礼包都要填充这个
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
     * @return Whether the actGoodsParam field is set.
     */
    boolean hasActGoodsParam();
    /**
     * <pre>
     * 礼包链活动、双队列礼包都要填充这个
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
     * @return The actGoodsParam.
     */
    com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam getActGoodsParam();
    /**
     * <pre>
     * 礼包链活动、双队列礼包都要填充这个
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
     */
    com.yorha.proto.PlayerPayment.ActivityNormalGoodsParamOrBuilder getActGoodsParamOrBuilder();

    /**
     * <pre>
     * 糊脸礼包参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
     * @return Whether the triggerBundleParam field is set.
     */
    boolean hasTriggerBundleParam();
    /**
     * <pre>
     * 糊脸礼包参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
     * @return The triggerBundleParam.
     */
    com.yorha.proto.StructPB.TriggerBundleOrderParamPB getTriggerBundleParam();
    /**
     * <pre>
     * 糊脸礼包参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
     */
    com.yorha.proto.StructPB.TriggerBundleOrderParamPBOrBuilder getTriggerBundleParamOrBuilder();

    /**
     * <pre>
     * 失落礼包购买参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
     * @return Whether the resellTriggerBundleParam field is set.
     */
    boolean hasResellTriggerBundleParam();
    /**
     * <pre>
     * 失落礼包购买参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
     * @return The resellTriggerBundleParam.
     */
    com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB getResellTriggerBundleParam();
    /**
     * <pre>
     * 失落礼包购买参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
     */
    com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPBOrBuilder getResellTriggerBundleParamOrBuilder();

    /**
     * <pre>
     * 自选礼包相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
     * @return Whether the selectParam field is set.
     */
    boolean hasSelectParam();
    /**
     * <pre>
     * 自选礼包相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
     * @return The selectParam.
     */
    com.yorha.proto.StructPB.ActivitySelectGoodsParamPB getSelectParam();
    /**
     * <pre>
     * 自选礼包相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
     */
    com.yorha.proto.StructPB.ActivitySelectGoodsParamPBOrBuilder getSelectParamOrBuilder();

    /**
     * <pre>
     * 节日bp相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
     * @return Whether the fbpParam field is set.
     */
    boolean hasFbpParam();
    /**
     * <pre>
     * 节日bp相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
     * @return The fbpParam.
     */
    com.yorha.proto.StructPB.FestivalBpParamPB getFbpParam();
    /**
     * <pre>
     * 节日bp相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
     */
    com.yorha.proto.StructPB.FestivalBpParamPBOrBuilder getFbpParamOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.GoodsOrderParam}
   */
  public static final class GoodsOrderParam extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.GoodsOrderParam)
      GoodsOrderParamOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GoodsOrderParam.newBuilder() to construct.
    private GoodsOrderParam(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GoodsOrderParam() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GoodsOrderParam();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GoodsOrderParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = actGoodsParam_.toBuilder();
              }
              actGoodsParam_ = input.readMessage(com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(actGoodsParam_);
                actGoodsParam_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.yorha.proto.StructPB.TriggerBundleOrderParamPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = triggerBundleParam_.toBuilder();
              }
              triggerBundleParam_ = input.readMessage(com.yorha.proto.StructPB.TriggerBundleOrderParamPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(triggerBundleParam_);
                triggerBundleParam_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = resellTriggerBundleParam_.toBuilder();
              }
              resellTriggerBundleParam_ = input.readMessage(com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(resellTriggerBundleParam_);
                resellTriggerBundleParam_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 34: {
              com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = selectParam_.toBuilder();
              }
              selectParam_ = input.readMessage(com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(selectParam_);
                selectParam_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              com.yorha.proto.StructPB.FestivalBpParamPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = fbpParam_.toBuilder();
              }
              fbpParam_ = input.readMessage(com.yorha.proto.StructPB.FestivalBpParamPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(fbpParam_);
                fbpParam_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_GoodsOrderParam_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_GoodsOrderParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.GoodsOrderParam.class, com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder.class);
    }

    private int bitField0_;
    public static final int ACTGOODSPARAM_FIELD_NUMBER = 1;
    private com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam actGoodsParam_;
    /**
     * <pre>
     * 礼包链活动、双队列礼包都要填充这个
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
     * @return Whether the actGoodsParam field is set.
     */
    @java.lang.Override
    public boolean hasActGoodsParam() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 礼包链活动、双队列礼包都要填充这个
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
     * @return The actGoodsParam.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam getActGoodsParam() {
      return actGoodsParam_ == null ? com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.getDefaultInstance() : actGoodsParam_;
    }
    /**
     * <pre>
     * 礼包链活动、双队列礼包都要填充这个
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParamOrBuilder getActGoodsParamOrBuilder() {
      return actGoodsParam_ == null ? com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.getDefaultInstance() : actGoodsParam_;
    }

    public static final int TRIGGERBUNDLEPARAM_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPB.TriggerBundleOrderParamPB triggerBundleParam_;
    /**
     * <pre>
     * 糊脸礼包参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
     * @return Whether the triggerBundleParam field is set.
     */
    @java.lang.Override
    public boolean hasTriggerBundleParam() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 糊脸礼包参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
     * @return The triggerBundleParam.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.TriggerBundleOrderParamPB getTriggerBundleParam() {
      return triggerBundleParam_ == null ? com.yorha.proto.StructPB.TriggerBundleOrderParamPB.getDefaultInstance() : triggerBundleParam_;
    }
    /**
     * <pre>
     * 糊脸礼包参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.TriggerBundleOrderParamPBOrBuilder getTriggerBundleParamOrBuilder() {
      return triggerBundleParam_ == null ? com.yorha.proto.StructPB.TriggerBundleOrderParamPB.getDefaultInstance() : triggerBundleParam_;
    }

    public static final int RESELLTRIGGERBUNDLEPARAM_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB resellTriggerBundleParam_;
    /**
     * <pre>
     * 失落礼包购买参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
     * @return Whether the resellTriggerBundleParam field is set.
     */
    @java.lang.Override
    public boolean hasResellTriggerBundleParam() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 失落礼包购买参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
     * @return The resellTriggerBundleParam.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB getResellTriggerBundleParam() {
      return resellTriggerBundleParam_ == null ? com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.getDefaultInstance() : resellTriggerBundleParam_;
    }
    /**
     * <pre>
     * 失落礼包购买参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPBOrBuilder getResellTriggerBundleParamOrBuilder() {
      return resellTriggerBundleParam_ == null ? com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.getDefaultInstance() : resellTriggerBundleParam_;
    }

    public static final int SELECTPARAM_FIELD_NUMBER = 4;
    private com.yorha.proto.StructPB.ActivitySelectGoodsParamPB selectParam_;
    /**
     * <pre>
     * 自选礼包相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
     * @return Whether the selectParam field is set.
     */
    @java.lang.Override
    public boolean hasSelectParam() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 自选礼包相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
     * @return The selectParam.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ActivitySelectGoodsParamPB getSelectParam() {
      return selectParam_ == null ? com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.getDefaultInstance() : selectParam_;
    }
    /**
     * <pre>
     * 自选礼包相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ActivitySelectGoodsParamPBOrBuilder getSelectParamOrBuilder() {
      return selectParam_ == null ? com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.getDefaultInstance() : selectParam_;
    }

    public static final int FBPPARAM_FIELD_NUMBER = 5;
    private com.yorha.proto.StructPB.FestivalBpParamPB fbpParam_;
    /**
     * <pre>
     * 节日bp相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
     * @return Whether the fbpParam field is set.
     */
    @java.lang.Override
    public boolean hasFbpParam() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 节日bp相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
     * @return The fbpParam.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.FestivalBpParamPB getFbpParam() {
      return fbpParam_ == null ? com.yorha.proto.StructPB.FestivalBpParamPB.getDefaultInstance() : fbpParam_;
    }
    /**
     * <pre>
     * 节日bp相关参数
     * </pre>
     *
     * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.FestivalBpParamPBOrBuilder getFbpParamOrBuilder() {
      return fbpParam_ == null ? com.yorha.proto.StructPB.FestivalBpParamPB.getDefaultInstance() : fbpParam_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getActGoodsParam());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getTriggerBundleParam());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getResellTriggerBundleParam());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getSelectParam());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getFbpParam());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getActGoodsParam());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTriggerBundleParam());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getResellTriggerBundleParam());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getSelectParam());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getFbpParam());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.GoodsOrderParam)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.GoodsOrderParam other = (com.yorha.proto.PlayerPayment.GoodsOrderParam) obj;

      if (hasActGoodsParam() != other.hasActGoodsParam()) return false;
      if (hasActGoodsParam()) {
        if (!getActGoodsParam()
            .equals(other.getActGoodsParam())) return false;
      }
      if (hasTriggerBundleParam() != other.hasTriggerBundleParam()) return false;
      if (hasTriggerBundleParam()) {
        if (!getTriggerBundleParam()
            .equals(other.getTriggerBundleParam())) return false;
      }
      if (hasResellTriggerBundleParam() != other.hasResellTriggerBundleParam()) return false;
      if (hasResellTriggerBundleParam()) {
        if (!getResellTriggerBundleParam()
            .equals(other.getResellTriggerBundleParam())) return false;
      }
      if (hasSelectParam() != other.hasSelectParam()) return false;
      if (hasSelectParam()) {
        if (!getSelectParam()
            .equals(other.getSelectParam())) return false;
      }
      if (hasFbpParam() != other.hasFbpParam()) return false;
      if (hasFbpParam()) {
        if (!getFbpParam()
            .equals(other.getFbpParam())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActGoodsParam()) {
        hash = (37 * hash) + ACTGOODSPARAM_FIELD_NUMBER;
        hash = (53 * hash) + getActGoodsParam().hashCode();
      }
      if (hasTriggerBundleParam()) {
        hash = (37 * hash) + TRIGGERBUNDLEPARAM_FIELD_NUMBER;
        hash = (53 * hash) + getTriggerBundleParam().hashCode();
      }
      if (hasResellTriggerBundleParam()) {
        hash = (37 * hash) + RESELLTRIGGERBUNDLEPARAM_FIELD_NUMBER;
        hash = (53 * hash) + getResellTriggerBundleParam().hashCode();
      }
      if (hasSelectParam()) {
        hash = (37 * hash) + SELECTPARAM_FIELD_NUMBER;
        hash = (53 * hash) + getSelectParam().hashCode();
      }
      if (hasFbpParam()) {
        hash = (37 * hash) + FBPPARAM_FIELD_NUMBER;
        hash = (53 * hash) + getFbpParam().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.GoodsOrderParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.GoodsOrderParam prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.GoodsOrderParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.GoodsOrderParam)
        com.yorha.proto.PlayerPayment.GoodsOrderParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_GoodsOrderParam_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_GoodsOrderParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.GoodsOrderParam.class, com.yorha.proto.PlayerPayment.GoodsOrderParam.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.GoodsOrderParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getActGoodsParamFieldBuilder();
          getTriggerBundleParamFieldBuilder();
          getResellTriggerBundleParamFieldBuilder();
          getSelectParamFieldBuilder();
          getFbpParamFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (actGoodsParamBuilder_ == null) {
          actGoodsParam_ = null;
        } else {
          actGoodsParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (triggerBundleParamBuilder_ == null) {
          triggerBundleParam_ = null;
        } else {
          triggerBundleParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (resellTriggerBundleParamBuilder_ == null) {
          resellTriggerBundleParam_ = null;
        } else {
          resellTriggerBundleParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (selectParamBuilder_ == null) {
          selectParam_ = null;
        } else {
          selectParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (fbpParamBuilder_ == null) {
          fbpParam_ = null;
        } else {
          fbpParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_GoodsOrderParam_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.GoodsOrderParam getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.GoodsOrderParam.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.GoodsOrderParam build() {
        com.yorha.proto.PlayerPayment.GoodsOrderParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.GoodsOrderParam buildPartial() {
        com.yorha.proto.PlayerPayment.GoodsOrderParam result = new com.yorha.proto.PlayerPayment.GoodsOrderParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (actGoodsParamBuilder_ == null) {
            result.actGoodsParam_ = actGoodsParam_;
          } else {
            result.actGoodsParam_ = actGoodsParamBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (triggerBundleParamBuilder_ == null) {
            result.triggerBundleParam_ = triggerBundleParam_;
          } else {
            result.triggerBundleParam_ = triggerBundleParamBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (resellTriggerBundleParamBuilder_ == null) {
            result.resellTriggerBundleParam_ = resellTriggerBundleParam_;
          } else {
            result.resellTriggerBundleParam_ = resellTriggerBundleParamBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (selectParamBuilder_ == null) {
            result.selectParam_ = selectParam_;
          } else {
            result.selectParam_ = selectParamBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (fbpParamBuilder_ == null) {
            result.fbpParam_ = fbpParam_;
          } else {
            result.fbpParam_ = fbpParamBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.GoodsOrderParam) {
          return mergeFrom((com.yorha.proto.PlayerPayment.GoodsOrderParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.GoodsOrderParam other) {
        if (other == com.yorha.proto.PlayerPayment.GoodsOrderParam.getDefaultInstance()) return this;
        if (other.hasActGoodsParam()) {
          mergeActGoodsParam(other.getActGoodsParam());
        }
        if (other.hasTriggerBundleParam()) {
          mergeTriggerBundleParam(other.getTriggerBundleParam());
        }
        if (other.hasResellTriggerBundleParam()) {
          mergeResellTriggerBundleParam(other.getResellTriggerBundleParam());
        }
        if (other.hasSelectParam()) {
          mergeSelectParam(other.getSelectParam());
        }
        if (other.hasFbpParam()) {
          mergeFbpParam(other.getFbpParam());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.GoodsOrderParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.GoodsOrderParam) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam actGoodsParam_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParamOrBuilder> actGoodsParamBuilder_;
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       * @return Whether the actGoodsParam field is set.
       */
      public boolean hasActGoodsParam() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       * @return The actGoodsParam.
       */
      public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam getActGoodsParam() {
        if (actGoodsParamBuilder_ == null) {
          return actGoodsParam_ == null ? com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.getDefaultInstance() : actGoodsParam_;
        } else {
          return actGoodsParamBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       */
      public Builder setActGoodsParam(com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam value) {
        if (actGoodsParamBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          actGoodsParam_ = value;
          onChanged();
        } else {
          actGoodsParamBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       */
      public Builder setActGoodsParam(
          com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder builderForValue) {
        if (actGoodsParamBuilder_ == null) {
          actGoodsParam_ = builderForValue.build();
          onChanged();
        } else {
          actGoodsParamBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       */
      public Builder mergeActGoodsParam(com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam value) {
        if (actGoodsParamBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              actGoodsParam_ != null &&
              actGoodsParam_ != com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.getDefaultInstance()) {
            actGoodsParam_ =
              com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.newBuilder(actGoodsParam_).mergeFrom(value).buildPartial();
          } else {
            actGoodsParam_ = value;
          }
          onChanged();
        } else {
          actGoodsParamBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       */
      public Builder clearActGoodsParam() {
        if (actGoodsParamBuilder_ == null) {
          actGoodsParam_ = null;
          onChanged();
        } else {
          actGoodsParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       */
      public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder getActGoodsParamBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getActGoodsParamFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       */
      public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParamOrBuilder getActGoodsParamOrBuilder() {
        if (actGoodsParamBuilder_ != null) {
          return actGoodsParamBuilder_.getMessageOrBuilder();
        } else {
          return actGoodsParam_ == null ?
              com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.getDefaultInstance() : actGoodsParam_;
        }
      }
      /**
       * <pre>
       * 礼包链活动、双队列礼包都要填充这个
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivityNormalGoodsParam actGoodsParam = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParamOrBuilder> 
          getActGoodsParamFieldBuilder() {
        if (actGoodsParamBuilder_ == null) {
          actGoodsParamBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParamOrBuilder>(
                  getActGoodsParam(),
                  getParentForChildren(),
                  isClean());
          actGoodsParam_ = null;
        }
        return actGoodsParamBuilder_;
      }

      private com.yorha.proto.StructPB.TriggerBundleOrderParamPB triggerBundleParam_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.TriggerBundleOrderParamPB, com.yorha.proto.StructPB.TriggerBundleOrderParamPB.Builder, com.yorha.proto.StructPB.TriggerBundleOrderParamPBOrBuilder> triggerBundleParamBuilder_;
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       * @return Whether the triggerBundleParam field is set.
       */
      public boolean hasTriggerBundleParam() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       * @return The triggerBundleParam.
       */
      public com.yorha.proto.StructPB.TriggerBundleOrderParamPB getTriggerBundleParam() {
        if (triggerBundleParamBuilder_ == null) {
          return triggerBundleParam_ == null ? com.yorha.proto.StructPB.TriggerBundleOrderParamPB.getDefaultInstance() : triggerBundleParam_;
        } else {
          return triggerBundleParamBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       */
      public Builder setTriggerBundleParam(com.yorha.proto.StructPB.TriggerBundleOrderParamPB value) {
        if (triggerBundleParamBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          triggerBundleParam_ = value;
          onChanged();
        } else {
          triggerBundleParamBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       */
      public Builder setTriggerBundleParam(
          com.yorha.proto.StructPB.TriggerBundleOrderParamPB.Builder builderForValue) {
        if (triggerBundleParamBuilder_ == null) {
          triggerBundleParam_ = builderForValue.build();
          onChanged();
        } else {
          triggerBundleParamBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       */
      public Builder mergeTriggerBundleParam(com.yorha.proto.StructPB.TriggerBundleOrderParamPB value) {
        if (triggerBundleParamBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              triggerBundleParam_ != null &&
              triggerBundleParam_ != com.yorha.proto.StructPB.TriggerBundleOrderParamPB.getDefaultInstance()) {
            triggerBundleParam_ =
              com.yorha.proto.StructPB.TriggerBundleOrderParamPB.newBuilder(triggerBundleParam_).mergeFrom(value).buildPartial();
          } else {
            triggerBundleParam_ = value;
          }
          onChanged();
        } else {
          triggerBundleParamBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       */
      public Builder clearTriggerBundleParam() {
        if (triggerBundleParamBuilder_ == null) {
          triggerBundleParam_ = null;
          onChanged();
        } else {
          triggerBundleParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       */
      public com.yorha.proto.StructPB.TriggerBundleOrderParamPB.Builder getTriggerBundleParamBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getTriggerBundleParamFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       */
      public com.yorha.proto.StructPB.TriggerBundleOrderParamPBOrBuilder getTriggerBundleParamOrBuilder() {
        if (triggerBundleParamBuilder_ != null) {
          return triggerBundleParamBuilder_.getMessageOrBuilder();
        } else {
          return triggerBundleParam_ == null ?
              com.yorha.proto.StructPB.TriggerBundleOrderParamPB.getDefaultInstance() : triggerBundleParam_;
        }
      }
      /**
       * <pre>
       * 糊脸礼包参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.TriggerBundleOrderParamPB triggerBundleParam = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.TriggerBundleOrderParamPB, com.yorha.proto.StructPB.TriggerBundleOrderParamPB.Builder, com.yorha.proto.StructPB.TriggerBundleOrderParamPBOrBuilder> 
          getTriggerBundleParamFieldBuilder() {
        if (triggerBundleParamBuilder_ == null) {
          triggerBundleParamBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.TriggerBundleOrderParamPB, com.yorha.proto.StructPB.TriggerBundleOrderParamPB.Builder, com.yorha.proto.StructPB.TriggerBundleOrderParamPBOrBuilder>(
                  getTriggerBundleParam(),
                  getParentForChildren(),
                  isClean());
          triggerBundleParam_ = null;
        }
        return triggerBundleParamBuilder_;
      }

      private com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB resellTriggerBundleParam_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB, com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.Builder, com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPBOrBuilder> resellTriggerBundleParamBuilder_;
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       * @return Whether the resellTriggerBundleParam field is set.
       */
      public boolean hasResellTriggerBundleParam() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       * @return The resellTriggerBundleParam.
       */
      public com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB getResellTriggerBundleParam() {
        if (resellTriggerBundleParamBuilder_ == null) {
          return resellTriggerBundleParam_ == null ? com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.getDefaultInstance() : resellTriggerBundleParam_;
        } else {
          return resellTriggerBundleParamBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       */
      public Builder setResellTriggerBundleParam(com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB value) {
        if (resellTriggerBundleParamBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          resellTriggerBundleParam_ = value;
          onChanged();
        } else {
          resellTriggerBundleParamBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       */
      public Builder setResellTriggerBundleParam(
          com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.Builder builderForValue) {
        if (resellTriggerBundleParamBuilder_ == null) {
          resellTriggerBundleParam_ = builderForValue.build();
          onChanged();
        } else {
          resellTriggerBundleParamBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       */
      public Builder mergeResellTriggerBundleParam(com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB value) {
        if (resellTriggerBundleParamBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              resellTriggerBundleParam_ != null &&
              resellTriggerBundleParam_ != com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.getDefaultInstance()) {
            resellTriggerBundleParam_ =
              com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.newBuilder(resellTriggerBundleParam_).mergeFrom(value).buildPartial();
          } else {
            resellTriggerBundleParam_ = value;
          }
          onChanged();
        } else {
          resellTriggerBundleParamBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       */
      public Builder clearResellTriggerBundleParam() {
        if (resellTriggerBundleParamBuilder_ == null) {
          resellTriggerBundleParam_ = null;
          onChanged();
        } else {
          resellTriggerBundleParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       */
      public com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.Builder getResellTriggerBundleParamBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getResellTriggerBundleParamFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       */
      public com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPBOrBuilder getResellTriggerBundleParamOrBuilder() {
        if (resellTriggerBundleParamBuilder_ != null) {
          return resellTriggerBundleParamBuilder_.getMessageOrBuilder();
        } else {
          return resellTriggerBundleParam_ == null ?
              com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.getDefaultInstance() : resellTriggerBundleParam_;
        }
      }
      /**
       * <pre>
       * 失落礼包购买参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ResellTriggerBundleOrderParamPB resellTriggerBundleParam = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB, com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.Builder, com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPBOrBuilder> 
          getResellTriggerBundleParamFieldBuilder() {
        if (resellTriggerBundleParamBuilder_ == null) {
          resellTriggerBundleParamBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB, com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPB.Builder, com.yorha.proto.StructPB.ResellTriggerBundleOrderParamPBOrBuilder>(
                  getResellTriggerBundleParam(),
                  getParentForChildren(),
                  isClean());
          resellTriggerBundleParam_ = null;
        }
        return resellTriggerBundleParamBuilder_;
      }

      private com.yorha.proto.StructPB.ActivitySelectGoodsParamPB selectParam_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ActivitySelectGoodsParamPB, com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.Builder, com.yorha.proto.StructPB.ActivitySelectGoodsParamPBOrBuilder> selectParamBuilder_;
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       * @return Whether the selectParam field is set.
       */
      public boolean hasSelectParam() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       * @return The selectParam.
       */
      public com.yorha.proto.StructPB.ActivitySelectGoodsParamPB getSelectParam() {
        if (selectParamBuilder_ == null) {
          return selectParam_ == null ? com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.getDefaultInstance() : selectParam_;
        } else {
          return selectParamBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       */
      public Builder setSelectParam(com.yorha.proto.StructPB.ActivitySelectGoodsParamPB value) {
        if (selectParamBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          selectParam_ = value;
          onChanged();
        } else {
          selectParamBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       */
      public Builder setSelectParam(
          com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.Builder builderForValue) {
        if (selectParamBuilder_ == null) {
          selectParam_ = builderForValue.build();
          onChanged();
        } else {
          selectParamBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       */
      public Builder mergeSelectParam(com.yorha.proto.StructPB.ActivitySelectGoodsParamPB value) {
        if (selectParamBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              selectParam_ != null &&
              selectParam_ != com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.getDefaultInstance()) {
            selectParam_ =
              com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.newBuilder(selectParam_).mergeFrom(value).buildPartial();
          } else {
            selectParam_ = value;
          }
          onChanged();
        } else {
          selectParamBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       */
      public Builder clearSelectParam() {
        if (selectParamBuilder_ == null) {
          selectParam_ = null;
          onChanged();
        } else {
          selectParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       */
      public com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.Builder getSelectParamBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getSelectParamFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       */
      public com.yorha.proto.StructPB.ActivitySelectGoodsParamPBOrBuilder getSelectParamOrBuilder() {
        if (selectParamBuilder_ != null) {
          return selectParamBuilder_.getMessageOrBuilder();
        } else {
          return selectParam_ == null ?
              com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.getDefaultInstance() : selectParam_;
        }
      }
      /**
       * <pre>
       * 自选礼包相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.ActivitySelectGoodsParamPB selectParam = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ActivitySelectGoodsParamPB, com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.Builder, com.yorha.proto.StructPB.ActivitySelectGoodsParamPBOrBuilder> 
          getSelectParamFieldBuilder() {
        if (selectParamBuilder_ == null) {
          selectParamBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ActivitySelectGoodsParamPB, com.yorha.proto.StructPB.ActivitySelectGoodsParamPB.Builder, com.yorha.proto.StructPB.ActivitySelectGoodsParamPBOrBuilder>(
                  getSelectParam(),
                  getParentForChildren(),
                  isClean());
          selectParam_ = null;
        }
        return selectParamBuilder_;
      }

      private com.yorha.proto.StructPB.FestivalBpParamPB fbpParam_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.FestivalBpParamPB, com.yorha.proto.StructPB.FestivalBpParamPB.Builder, com.yorha.proto.StructPB.FestivalBpParamPBOrBuilder> fbpParamBuilder_;
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       * @return Whether the fbpParam field is set.
       */
      public boolean hasFbpParam() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       * @return The fbpParam.
       */
      public com.yorha.proto.StructPB.FestivalBpParamPB getFbpParam() {
        if (fbpParamBuilder_ == null) {
          return fbpParam_ == null ? com.yorha.proto.StructPB.FestivalBpParamPB.getDefaultInstance() : fbpParam_;
        } else {
          return fbpParamBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       */
      public Builder setFbpParam(com.yorha.proto.StructPB.FestivalBpParamPB value) {
        if (fbpParamBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fbpParam_ = value;
          onChanged();
        } else {
          fbpParamBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       */
      public Builder setFbpParam(
          com.yorha.proto.StructPB.FestivalBpParamPB.Builder builderForValue) {
        if (fbpParamBuilder_ == null) {
          fbpParam_ = builderForValue.build();
          onChanged();
        } else {
          fbpParamBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       */
      public Builder mergeFbpParam(com.yorha.proto.StructPB.FestivalBpParamPB value) {
        if (fbpParamBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              fbpParam_ != null &&
              fbpParam_ != com.yorha.proto.StructPB.FestivalBpParamPB.getDefaultInstance()) {
            fbpParam_ =
              com.yorha.proto.StructPB.FestivalBpParamPB.newBuilder(fbpParam_).mergeFrom(value).buildPartial();
          } else {
            fbpParam_ = value;
          }
          onChanged();
        } else {
          fbpParamBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       */
      public Builder clearFbpParam() {
        if (fbpParamBuilder_ == null) {
          fbpParam_ = null;
          onChanged();
        } else {
          fbpParamBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       */
      public com.yorha.proto.StructPB.FestivalBpParamPB.Builder getFbpParamBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getFbpParamFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       */
      public com.yorha.proto.StructPB.FestivalBpParamPBOrBuilder getFbpParamOrBuilder() {
        if (fbpParamBuilder_ != null) {
          return fbpParamBuilder_.getMessageOrBuilder();
        } else {
          return fbpParam_ == null ?
              com.yorha.proto.StructPB.FestivalBpParamPB.getDefaultInstance() : fbpParam_;
        }
      }
      /**
       * <pre>
       * 节日bp相关参数
       * </pre>
       *
       * <code>optional .com.yorha.proto.FestivalBpParamPB fbpParam = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.FestivalBpParamPB, com.yorha.proto.StructPB.FestivalBpParamPB.Builder, com.yorha.proto.StructPB.FestivalBpParamPBOrBuilder> 
          getFbpParamFieldBuilder() {
        if (fbpParamBuilder_ == null) {
          fbpParamBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.FestivalBpParamPB, com.yorha.proto.StructPB.FestivalBpParamPB.Builder, com.yorha.proto.StructPB.FestivalBpParamPBOrBuilder>(
                  getFbpParam(),
                  getParentForChildren(),
                  isClean());
          fbpParam_ = null;
        }
        return fbpParamBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.GoodsOrderParam)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.GoodsOrderParam)
    private static final com.yorha.proto.PlayerPayment.GoodsOrderParam DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.GoodsOrderParam();
    }

    public static com.yorha.proto.PlayerPayment.GoodsOrderParam getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<GoodsOrderParam>
        PARSER = new com.google.protobuf.AbstractParser<GoodsOrderParam>() {
      @java.lang.Override
      public GoodsOrderParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GoodsOrderParam(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GoodsOrderParam> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GoodsOrderParam> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.GoodsOrderParam getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ActivityNormalGoodsParamOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ActivityNormalGoodsParam)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 哪个活动
     * </pre>
     *
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    boolean hasActId();
    /**
     * <pre>
     * 哪个活动
     * </pre>
     *
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    int getActId();

    /**
     * <pre>
     * 哪个unit
     * </pre>
     *
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    boolean hasUnitId();
    /**
     * <pre>
     * 哪个unit
     * </pre>
     *
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    int getUnitId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ActivityNormalGoodsParam}
   */
  public static final class ActivityNormalGoodsParam extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ActivityNormalGoodsParam)
      ActivityNormalGoodsParamOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ActivityNormalGoodsParam.newBuilder() to construct.
    private ActivityNormalGoodsParam(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ActivityNormalGoodsParam() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ActivityNormalGoodsParam();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ActivityNormalGoodsParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              actId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              unitId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_ActivityNormalGoodsParam_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_ActivityNormalGoodsParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.class, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder.class);
    }

    private int bitField0_;
    public static final int ACTID_FIELD_NUMBER = 1;
    private int actId_;
    /**
     * <pre>
     * 哪个活动
     * </pre>
     *
     * <code>optional int32 actId = 1;</code>
     * @return Whether the actId field is set.
     */
    @java.lang.Override
    public boolean hasActId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 哪个活动
     * </pre>
     *
     * <code>optional int32 actId = 1;</code>
     * @return The actId.
     */
    @java.lang.Override
    public int getActId() {
      return actId_;
    }

    public static final int UNITID_FIELD_NUMBER = 2;
    private int unitId_;
    /**
     * <pre>
     * 哪个unit
     * </pre>
     *
     * <code>optional int32 unitId = 2;</code>
     * @return Whether the unitId field is set.
     */
    @java.lang.Override
    public boolean hasUnitId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 哪个unit
     * </pre>
     *
     * <code>optional int32 unitId = 2;</code>
     * @return The unitId.
     */
    @java.lang.Override
    public int getUnitId() {
      return unitId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, unitId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, actId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, unitId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam other = (com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam) obj;

      if (hasActId() != other.hasActId()) return false;
      if (hasActId()) {
        if (getActId()
            != other.getActId()) return false;
      }
      if (hasUnitId() != other.hasUnitId()) return false;
      if (hasUnitId()) {
        if (getUnitId()
            != other.getUnitId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActId()) {
        hash = (37 * hash) + ACTID_FIELD_NUMBER;
        hash = (53 * hash) + getActId();
      }
      if (hasUnitId()) {
        hash = (37 * hash) + UNITID_FIELD_NUMBER;
        hash = (53 * hash) + getUnitId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ActivityNormalGoodsParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ActivityNormalGoodsParam)
        com.yorha.proto.PlayerPayment.ActivityNormalGoodsParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_ActivityNormalGoodsParam_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_ActivityNormalGoodsParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.class, com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        unitId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_ActivityNormalGoodsParam_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam build() {
        com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam buildPartial() {
        com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam result = new com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.actId_ = actId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.unitId_ = unitId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam) {
          return mergeFrom((com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam other) {
        if (other == com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam.getDefaultInstance()) return this;
        if (other.hasActId()) {
          setActId(other.getActId());
        }
        if (other.hasUnitId()) {
          setUnitId(other.getUnitId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int actId_ ;
      /**
       * <pre>
       * 哪个活动
       * </pre>
       *
       * <code>optional int32 actId = 1;</code>
       * @return Whether the actId field is set.
       */
      @java.lang.Override
      public boolean hasActId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 哪个活动
       * </pre>
       *
       * <code>optional int32 actId = 1;</code>
       * @return The actId.
       */
      @java.lang.Override
      public int getActId() {
        return actId_;
      }
      /**
       * <pre>
       * 哪个活动
       * </pre>
       *
       * <code>optional int32 actId = 1;</code>
       * @param value The actId to set.
       * @return This builder for chaining.
       */
      public Builder setActId(int value) {
        bitField0_ |= 0x00000001;
        actId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 哪个活动
       * </pre>
       *
       * <code>optional int32 actId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        actId_ = 0;
        onChanged();
        return this;
      }

      private int unitId_ ;
      /**
       * <pre>
       * 哪个unit
       * </pre>
       *
       * <code>optional int32 unitId = 2;</code>
       * @return Whether the unitId field is set.
       */
      @java.lang.Override
      public boolean hasUnitId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 哪个unit
       * </pre>
       *
       * <code>optional int32 unitId = 2;</code>
       * @return The unitId.
       */
      @java.lang.Override
      public int getUnitId() {
        return unitId_;
      }
      /**
       * <pre>
       * 哪个unit
       * </pre>
       *
       * <code>optional int32 unitId = 2;</code>
       * @param value The unitId to set.
       * @return This builder for chaining.
       */
      public Builder setUnitId(int value) {
        bitField0_ |= 0x00000002;
        unitId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 哪个unit
       * </pre>
       *
       * <code>optional int32 unitId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        unitId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ActivityNormalGoodsParam)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ActivityNormalGoodsParam)
    private static final com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam();
    }

    public static com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ActivityNormalGoodsParam>
        PARSER = new com.google.protobuf.AbstractParser<ActivityNormalGoodsParam>() {
      @java.lang.Override
      public ActivityNormalGoodsParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ActivityNormalGoodsParam(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ActivityNormalGoodsParam> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ActivityNormalGoodsParam> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.ActivityNormalGoodsParam getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ApplyGoodsOrder_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ApplyGoodsOrder_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
     * </pre>
     *
     * <code>optional string orderToken = 1;</code>
     * @return Whether the orderToken field is set.
     */
    boolean hasOrderToken();
    /**
     * <pre>
     * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
     * </pre>
     *
     * <code>optional string orderToken = 1;</code>
     * @return The orderToken.
     */
    java.lang.String getOrderToken();
    /**
     * <pre>
     * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
     * </pre>
     *
     * <code>optional string orderToken = 1;</code>
     * @return The bytes for orderToken.
     */
    com.google.protobuf.ByteString
        getOrderTokenBytes();

    /**
     * <pre>
     * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
     * </pre>
     *
     * <code>optional bool orderFinished = 2;</code>
     * @return Whether the orderFinished field is set.
     */
    boolean hasOrderFinished();
    /**
     * <pre>
     * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
     * </pre>
     *
     * <code>optional bool orderFinished = 2;</code>
     * @return The orderFinished.
     */
    boolean getOrderFinished();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ApplyGoodsOrder_S2C}
   */
  public static final class Player_ApplyGoodsOrder_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ApplyGoodsOrder_S2C)
      Player_ApplyGoodsOrder_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ApplyGoodsOrder_S2C.newBuilder() to construct.
    private Player_ApplyGoodsOrder_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ApplyGoodsOrder_S2C() {
      orderToken_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ApplyGoodsOrder_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ApplyGoodsOrder_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              orderToken_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              orderFinished_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C.class, com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int ORDERTOKEN_FIELD_NUMBER = 1;
    private volatile java.lang.Object orderToken_;
    /**
     * <pre>
     * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
     * </pre>
     *
     * <code>optional string orderToken = 1;</code>
     * @return Whether the orderToken field is set.
     */
    @java.lang.Override
    public boolean hasOrderToken() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
     * </pre>
     *
     * <code>optional string orderToken = 1;</code>
     * @return The orderToken.
     */
    @java.lang.Override
    public java.lang.String getOrderToken() {
      java.lang.Object ref = orderToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          orderToken_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
     * </pre>
     *
     * <code>optional string orderToken = 1;</code>
     * @return The bytes for orderToken.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOrderTokenBytes() {
      java.lang.Object ref = orderToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ORDERFINISHED_FIELD_NUMBER = 2;
    private boolean orderFinished_;
    /**
     * <pre>
     * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
     * </pre>
     *
     * <code>optional bool orderFinished = 2;</code>
     * @return Whether the orderFinished field is set.
     */
    @java.lang.Override
    public boolean hasOrderFinished() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
     * </pre>
     *
     * <code>optional bool orderFinished = 2;</code>
     * @return The orderFinished.
     */
    @java.lang.Override
    public boolean getOrderFinished() {
      return orderFinished_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, orderToken_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, orderFinished_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, orderToken_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, orderFinished_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C other = (com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C) obj;

      if (hasOrderToken() != other.hasOrderToken()) return false;
      if (hasOrderToken()) {
        if (!getOrderToken()
            .equals(other.getOrderToken())) return false;
      }
      if (hasOrderFinished() != other.hasOrderFinished()) return false;
      if (hasOrderFinished()) {
        if (getOrderFinished()
            != other.getOrderFinished()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOrderToken()) {
        hash = (37 * hash) + ORDERTOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getOrderToken().hashCode();
      }
      if (hasOrderFinished()) {
        hash = (37 * hash) + ORDERFINISHED_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getOrderFinished());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ApplyGoodsOrder_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ApplyGoodsOrder_S2C)
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C.class, com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        orderToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        orderFinished_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C build() {
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C buildPartial() {
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C result = new com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.orderToken_ = orderToken_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.orderFinished_ = orderFinished_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C other) {
        if (other == com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C.getDefaultInstance()) return this;
        if (other.hasOrderToken()) {
          bitField0_ |= 0x00000001;
          orderToken_ = other.orderToken_;
          onChanged();
        }
        if (other.hasOrderFinished()) {
          setOrderFinished(other.getOrderFinished());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object orderToken_ = "";
      /**
       * <pre>
       * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
       * </pre>
       *
       * <code>optional string orderToken = 1;</code>
       * @return Whether the orderToken field is set.
       */
      public boolean hasOrderToken() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
       * </pre>
       *
       * <code>optional string orderToken = 1;</code>
       * @return The orderToken.
       */
      public java.lang.String getOrderToken() {
        java.lang.Object ref = orderToken_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            orderToken_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
       * </pre>
       *
       * <code>optional string orderToken = 1;</code>
       * @return The bytes for orderToken.
       */
      public com.google.protobuf.ByteString
          getOrderTokenBytes() {
        java.lang.Object ref = orderToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          orderToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
       * </pre>
       *
       * <code>optional string orderToken = 1;</code>
       * @param value The orderToken to set.
       * @return This builder for chaining.
       */
      public Builder setOrderToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        orderToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
       * </pre>
       *
       * <code>optional string orderToken = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrderToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        orderToken_ = getDefaultInstance().getOrderToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 服务器校验订单，签发凭据，客户端透传到midas，midas回调时透传，作为合法订单的校验辅助
       * </pre>
       *
       * <code>optional string orderToken = 1;</code>
       * @param value The bytes for orderToken to set.
       * @return This builder for chaining.
       */
      public Builder setOrderTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        orderToken_ = value;
        onChanged();
        return this;
      }

      private boolean orderFinished_ ;
      /**
       * <pre>
       * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
       * </pre>
       *
       * <code>optional bool orderFinished = 2;</code>
       * @return Whether the orderFinished field is set.
       */
      @java.lang.Override
      public boolean hasOrderFinished() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
       * </pre>
       *
       * <code>optional bool orderFinished = 2;</code>
       * @return The orderFinished.
       */
      @java.lang.Override
      public boolean getOrderFinished() {
        return orderFinished_;
      }
      /**
       * <pre>
       * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
       * </pre>
       *
       * <code>optional bool orderFinished = 2;</code>
       * @param value The orderFinished to set.
       * @return This builder for chaining.
       */
      public Builder setOrderFinished(boolean value) {
        bitField0_ |= 0x00000002;
        orderFinished_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家代币足够的话，会直接扣除代币，直接完成订单，或者测试环境下买礼包直接发货的也是会返回true
       * </pre>
       *
       * <code>optional bool orderFinished = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrderFinished() {
        bitField0_ = (bitField0_ & ~0x00000002);
        orderFinished_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ApplyGoodsOrder_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ApplyGoodsOrder_S2C)
    private static final com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C();
    }

    public static com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ApplyGoodsOrder_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ApplyGoodsOrder_S2C>() {
      @java.lang.Override
      public Player_ApplyGoodsOrder_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ApplyGoodsOrder_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ApplyGoodsOrder_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ApplyGoodsOrder_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_ApplyGoodsOrder_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FakeRecharge_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FakeRecharge_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 模拟直充的配置id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return Whether the chargeBaseId field is set.
     */
    boolean hasChargeBaseId();
    /**
     * <pre>
     * 模拟直充的配置id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return The chargeBaseId.
     */
    int getChargeBaseId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FakeRecharge_C2S}
   */
  public static final class Player_FakeRecharge_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FakeRecharge_C2S)
      Player_FakeRecharge_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FakeRecharge_C2S.newBuilder() to construct.
    private Player_FakeRecharge_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FakeRecharge_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FakeRecharge_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FakeRecharge_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              chargeBaseId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S.class, com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CHARGEBASEID_FIELD_NUMBER = 1;
    private int chargeBaseId_;
    /**
     * <pre>
     * 模拟直充的配置id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return Whether the chargeBaseId field is set.
     */
    @java.lang.Override
    public boolean hasChargeBaseId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 模拟直充的配置id
     * </pre>
     *
     * <code>optional int32 chargeBaseId = 1;</code>
     * @return The chargeBaseId.
     */
    @java.lang.Override
    public int getChargeBaseId() {
      return chargeBaseId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, chargeBaseId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, chargeBaseId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S other = (com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S) obj;

      if (hasChargeBaseId() != other.hasChargeBaseId()) return false;
      if (hasChargeBaseId()) {
        if (getChargeBaseId()
            != other.getChargeBaseId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChargeBaseId()) {
        hash = (37 * hash) + CHARGEBASEID_FIELD_NUMBER;
        hash = (53 * hash) + getChargeBaseId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FakeRecharge_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FakeRecharge_C2S)
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S.class, com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chargeBaseId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S build() {
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S buildPartial() {
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S result = new com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chargeBaseId_ = chargeBaseId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S other) {
        if (other == com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S.getDefaultInstance()) return this;
        if (other.hasChargeBaseId()) {
          setChargeBaseId(other.getChargeBaseId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int chargeBaseId_ ;
      /**
       * <pre>
       * 模拟直充的配置id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @return Whether the chargeBaseId field is set.
       */
      @java.lang.Override
      public boolean hasChargeBaseId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 模拟直充的配置id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @return The chargeBaseId.
       */
      @java.lang.Override
      public int getChargeBaseId() {
        return chargeBaseId_;
      }
      /**
       * <pre>
       * 模拟直充的配置id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @param value The chargeBaseId to set.
       * @return This builder for chaining.
       */
      public Builder setChargeBaseId(int value) {
        bitField0_ |= 0x00000001;
        chargeBaseId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模拟直充的配置id
       * </pre>
       *
       * <code>optional int32 chargeBaseId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChargeBaseId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chargeBaseId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FakeRecharge_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FakeRecharge_C2S)
    private static final com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S();
    }

    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FakeRecharge_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FakeRecharge_C2S>() {
      @java.lang.Override
      public Player_FakeRecharge_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FakeRecharge_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FakeRecharge_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FakeRecharge_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_FakeRecharge_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FakeRecharge_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FakeRecharge_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FakeRecharge_S2C}
   */
  public static final class Player_FakeRecharge_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FakeRecharge_S2C)
      Player_FakeRecharge_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FakeRecharge_S2C.newBuilder() to construct.
    private Player_FakeRecharge_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FakeRecharge_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FakeRecharge_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FakeRecharge_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C.class, com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C other = (com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FakeRecharge_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FakeRecharge_S2C)
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C.class, com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_FakeRecharge_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C build() {
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C buildPartial() {
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C result = new com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C other) {
        if (other == com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FakeRecharge_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FakeRecharge_S2C)
    private static final com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C();
    }

    public static com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FakeRecharge_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FakeRecharge_S2C>() {
      @java.lang.Override
      public Player_FakeRecharge_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FakeRecharge_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FakeRecharge_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FakeRecharge_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_FakeRecharge_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TriggerBundleShow_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TriggerBundleShow_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 bundleId = 1;</code>
     * @return Whether the bundleId field is set.
     */
    boolean hasBundleId();
    /**
     * <code>optional int64 bundleId = 1;</code>
     * @return The bundleId.
     */
    long getBundleId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TriggerBundleShow_C2S}
   */
  public static final class Player_TriggerBundleShow_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TriggerBundleShow_C2S)
      Player_TriggerBundleShow_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TriggerBundleShow_C2S.newBuilder() to construct.
    private Player_TriggerBundleShow_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TriggerBundleShow_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TriggerBundleShow_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TriggerBundleShow_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              bundleId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S.class, com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int BUNDLEID_FIELD_NUMBER = 1;
    private long bundleId_;
    /**
     * <code>optional int64 bundleId = 1;</code>
     * @return Whether the bundleId field is set.
     */
    @java.lang.Override
    public boolean hasBundleId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 bundleId = 1;</code>
     * @return The bundleId.
     */
    @java.lang.Override
    public long getBundleId() {
      return bundleId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, bundleId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, bundleId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S other = (com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S) obj;

      if (hasBundleId() != other.hasBundleId()) return false;
      if (hasBundleId()) {
        if (getBundleId()
            != other.getBundleId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBundleId()) {
        hash = (37 * hash) + BUNDLEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBundleId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TriggerBundleShow_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TriggerBundleShow_C2S)
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S.class, com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bundleId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S build() {
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S buildPartial() {
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S result = new com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.bundleId_ = bundleId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S other) {
        if (other == com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S.getDefaultInstance()) return this;
        if (other.hasBundleId()) {
          setBundleId(other.getBundleId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long bundleId_ ;
      /**
       * <code>optional int64 bundleId = 1;</code>
       * @return Whether the bundleId field is set.
       */
      @java.lang.Override
      public boolean hasBundleId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 bundleId = 1;</code>
       * @return The bundleId.
       */
      @java.lang.Override
      public long getBundleId() {
        return bundleId_;
      }
      /**
       * <code>optional int64 bundleId = 1;</code>
       * @param value The bundleId to set.
       * @return This builder for chaining.
       */
      public Builder setBundleId(long value) {
        bitField0_ |= 0x00000001;
        bundleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 bundleId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBundleId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        bundleId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TriggerBundleShow_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TriggerBundleShow_C2S)
    private static final com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S();
    }

    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TriggerBundleShow_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_TriggerBundleShow_C2S>() {
      @java.lang.Override
      public Player_TriggerBundleShow_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TriggerBundleShow_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TriggerBundleShow_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TriggerBundleShow_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TriggerBundleShow_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TriggerBundleShow_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TriggerBundleShow_S2C}
   */
  public static final class Player_TriggerBundleShow_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TriggerBundleShow_S2C)
      Player_TriggerBundleShow_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TriggerBundleShow_S2C.newBuilder() to construct.
    private Player_TriggerBundleShow_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TriggerBundleShow_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TriggerBundleShow_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TriggerBundleShow_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C.class, com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C other = (com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TriggerBundleShow_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TriggerBundleShow_S2C)
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C.class, com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerPayment.internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C build() {
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C buildPartial() {
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C result = new com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C) {
          return mergeFrom((com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C other) {
        if (other == com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TriggerBundleShow_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TriggerBundleShow_S2C)
    private static final com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C();
    }

    public static com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TriggerBundleShow_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_TriggerBundleShow_S2C>() {
      @java.lang.Override
      public Player_TriggerBundleShow_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TriggerBundleShow_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TriggerBundleShow_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TriggerBundleShow_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerPayment.Player_TriggerBundleShow_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PaymentOver_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PaymentOver_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PaymentOver_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PaymentOver_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_GoodsOrderParam_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_GoodsOrderParam_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ActivityNormalGoodsParam_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ActivityNormalGoodsParam_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FakeRecharge_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FakeRecharge_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FakeRecharge_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FakeRecharge_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/player/cs/player_payment." +
      "proto\022\017com.yorha.proto\032\"cs_proto/gen/com" +
      "mon/structPB.proto\032\036ss_proto/gen/common/" +
      "user.proto\"/\n\030Player_JudgeRecharge_C2S\022\023" +
      "\n\013chargeSdkId\030\001 \001(\005\"w\n\030Player_JudgeRecha" +
      "rge_S2C\022\023\n\013chargeSdkId\030\001 \001(\005\022\017\n\007traceId\030" +
      "\002 \001(\t\0225\n\013instruction\030\003 \001(\0132 .com.yorha.p" +
      "roto.HopeInstruction\"@\n\026Player_PaymentOv" +
      "er_C2S\022\024\n\014chargeBaseId\030\001 \001(\005\022\020\n\010ifDouble" +
      "\030\002 \001(\005\"\030\n\026Player_PaymentOver_S2C\"c\n\032Play" +
      "er_ApplyGoodsOrder_C2S\022\017\n\007goodsId\030\001 \001(\005\022" +
      "4\n\norderParam\030\002 \001(\0132 .com.yorha.proto.Go" +
      "odsOrderParam\"\347\002\n\017GoodsOrderParam\022@\n\ract" +
      "GoodsParam\030\001 \001(\0132).com.yorha.proto.Activ" +
      "ityNormalGoodsParam\022F\n\022triggerBundlePara" +
      "m\030\002 \001(\0132*.com.yorha.proto.TriggerBundleO" +
      "rderParamPB\022R\n\030resellTriggerBundleParam\030" +
      "\003 \001(\01320.com.yorha.proto.ResellTriggerBun" +
      "dleOrderParamPB\022@\n\013selectParam\030\004 \001(\0132+.c" +
      "om.yorha.proto.ActivitySelectGoodsParamP" +
      "B\0224\n\010fbpParam\030\005 \001(\0132\".com.yorha.proto.Fe" +
      "stivalBpParamPB\"9\n\030ActivityNormalGoodsPa" +
      "ram\022\r\n\005actId\030\001 \001(\005\022\016\n\006unitId\030\002 \001(\005\"G\n\032Pl" +
      "ayer_ApplyGoodsOrder_S2C\022\022\n\norderToken\030\001" +
      " \001(\t\022\025\n\rorderFinished\030\002 \001(\010\"/\n\027Player_Fa" +
      "keRecharge_C2S\022\024\n\014chargeBaseId\030\001 \001(\005\"\031\n\027" +
      "Player_FakeRecharge_S2C\"0\n\034Player_Trigge" +
      "rBundleShow_C2S\022\020\n\010bundleId\030\001 \001(\003\"\036\n\034Pla" +
      "yer_TriggerBundleShow_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.User.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_JudgeRecharge_C2S_descriptor,
        new java.lang.String[] { "ChargeSdkId", });
    internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_JudgeRecharge_S2C_descriptor,
        new java.lang.String[] { "ChargeSdkId", "TraceId", "Instruction", });
    internal_static_com_yorha_proto_Player_PaymentOver_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_PaymentOver_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PaymentOver_C2S_descriptor,
        new java.lang.String[] { "ChargeBaseId", "IfDouble", });
    internal_static_com_yorha_proto_Player_PaymentOver_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_PaymentOver_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PaymentOver_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ApplyGoodsOrder_C2S_descriptor,
        new java.lang.String[] { "GoodsId", "OrderParam", });
    internal_static_com_yorha_proto_GoodsOrderParam_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_GoodsOrderParam_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_GoodsOrderParam_descriptor,
        new java.lang.String[] { "ActGoodsParam", "TriggerBundleParam", "ResellTriggerBundleParam", "SelectParam", "FbpParam", });
    internal_static_com_yorha_proto_ActivityNormalGoodsParam_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_ActivityNormalGoodsParam_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ActivityNormalGoodsParam_descriptor,
        new java.lang.String[] { "ActId", "UnitId", });
    internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ApplyGoodsOrder_S2C_descriptor,
        new java.lang.String[] { "OrderToken", "OrderFinished", });
    internal_static_com_yorha_proto_Player_FakeRecharge_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_FakeRecharge_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FakeRecharge_C2S_descriptor,
        new java.lang.String[] { "ChargeBaseId", });
    internal_static_com_yorha_proto_Player_FakeRecharge_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_FakeRecharge_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FakeRecharge_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TriggerBundleShow_C2S_descriptor,
        new java.lang.String[] { "BundleId", });
    internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TriggerBundleShow_S2C_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.User.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

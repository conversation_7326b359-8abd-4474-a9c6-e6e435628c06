// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/node/ss_node.proto

package com.yorha.proto;

public final class SsNode {
  private SsNode() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * <pre>
   * 系统消息：类型
   * </pre>
   *
   * Protobuf enum {@code com.yorha.proto.NodeTaskType}
   */
  public enum NodeTaskType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 空
     * </pre>
     *
     * <code>NTT_NONE = 0;</code>
     */
    NTT_NONE(0),
    /**
     * <pre>
     * 广播string，前面废弃
     * </pre>
     *
     * <code>NTT_STRING_MSG = 8;</code>
     */
    NTT_STRING_MSG(8),
    ;

    /**
     * <pre>
     * 空
     * </pre>
     *
     * <code>NTT_NONE = 0;</code>
     */
    public static final int NTT_NONE_VALUE = 0;
    /**
     * <pre>
     * 广播string，前面废弃
     * </pre>
     *
     * <code>NTT_STRING_MSG = 8;</code>
     */
    public static final int NTT_STRING_MSG_VALUE = 8;


    public final int getNumber() {
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static NodeTaskType valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static NodeTaskType forNumber(int value) {
      switch (value) {
        case 0: return NTT_NONE;
        case 8: return NTT_STRING_MSG;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<NodeTaskType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        NodeTaskType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<NodeTaskType>() {
            public NodeTaskType findValueByNumber(int number) {
              return NodeTaskType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.yorha.proto.SsNode.getDescriptor().getEnumTypes().get(0);
    }

    private static final NodeTaskType[] VALUES = values();

    public static NodeTaskType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private NodeTaskType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:com.yorha.proto.NodeTaskType)
  }

  public interface NodeTaskCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.NodeTaskCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
     * @return Whether the nodeTaskType field is set.
     */
    boolean hasNodeTaskType();
    /**
     * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
     * @return The nodeTaskType.
     */
    com.yorha.proto.SsNode.NodeTaskType getNodeTaskType();

    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    boolean hasTaskId();
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    long getTaskId();

    /**
     * <pre>
     * 广播的string，前面废弃
     * </pre>
     *
     * <code>string stringMsg = 10;</code>
     * @return Whether the stringMsg field is set.
     */
    boolean hasStringMsg();
    /**
     * <pre>
     * 广播的string，前面废弃
     * </pre>
     *
     * <code>string stringMsg = 10;</code>
     * @return The stringMsg.
     */
    java.lang.String getStringMsg();
    /**
     * <pre>
     * 广播的string，前面废弃
     * </pre>
     *
     * <code>string stringMsg = 10;</code>
     * @return The bytes for stringMsg.
     */
    com.google.protobuf.ByteString
        getStringMsgBytes();

    public com.yorha.proto.SsNode.NodeTaskCmd.TaskCase getTaskCase();
  }
  /**
   * <pre>
   * 系统消息：包体
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.NodeTaskCmd}
   */
  public static final class NodeTaskCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.NodeTaskCmd)
      NodeTaskCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NodeTaskCmd.newBuilder() to construct.
    private NodeTaskCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NodeTaskCmd() {
      nodeTaskType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NodeTaskCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private NodeTaskCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.SsNode.NodeTaskType value = com.yorha.proto.SsNode.NodeTaskType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                nodeTaskType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              taskId_ = input.readInt64();
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              taskCase_ = 10;
              task_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsNode.internal_static_com_yorha_proto_NodeTaskCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsNode.internal_static_com_yorha_proto_NodeTaskCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsNode.NodeTaskCmd.class, com.yorha.proto.SsNode.NodeTaskCmd.Builder.class);
    }

    private int bitField0_;
    private int taskCase_ = 0;
    private java.lang.Object task_;
    public enum TaskCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      STRINGMSG(10),
      TASK_NOT_SET(0);
      private final int value;
      private TaskCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static TaskCase valueOf(int value) {
        return forNumber(value);
      }

      public static TaskCase forNumber(int value) {
        switch (value) {
          case 10: return STRINGMSG;
          case 0: return TASK_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public TaskCase
    getTaskCase() {
      return TaskCase.forNumber(
          taskCase_);
    }

    public static final int NODETASKTYPE_FIELD_NUMBER = 1;
    private int nodeTaskType_;
    /**
     * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
     * @return Whether the nodeTaskType field is set.
     */
    @java.lang.Override public boolean hasNodeTaskType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
     * @return The nodeTaskType.
     */
    @java.lang.Override public com.yorha.proto.SsNode.NodeTaskType getNodeTaskType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.SsNode.NodeTaskType result = com.yorha.proto.SsNode.NodeTaskType.valueOf(nodeTaskType_);
      return result == null ? com.yorha.proto.SsNode.NodeTaskType.NTT_NONE : result;
    }

    public static final int TASKID_FIELD_NUMBER = 2;
    private long taskId_;
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return Whether the taskId field is set.
     */
    @java.lang.Override
    public boolean hasTaskId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 taskId = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public long getTaskId() {
      return taskId_;
    }

    public static final int STRINGMSG_FIELD_NUMBER = 10;
    /**
     * <pre>
     * 广播的string，前面废弃
     * </pre>
     *
     * <code>string stringMsg = 10;</code>
     * @return Whether the stringMsg field is set.
     */
    public boolean hasStringMsg() {
      return taskCase_ == 10;
    }
    /**
     * <pre>
     * 广播的string，前面废弃
     * </pre>
     *
     * <code>string stringMsg = 10;</code>
     * @return The stringMsg.
     */
    public java.lang.String getStringMsg() {
      java.lang.Object ref = "";
      if (taskCase_ == 10) {
        ref = task_;
      }
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8() && (taskCase_ == 10)) {
          task_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 广播的string，前面废弃
     * </pre>
     *
     * <code>string stringMsg = 10;</code>
     * @return The bytes for stringMsg.
     */
    public com.google.protobuf.ByteString
        getStringMsgBytes() {
      java.lang.Object ref = "";
      if (taskCase_ == 10) {
        ref = task_;
      }
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        if (taskCase_ == 10) {
          task_ = b;
        }
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, nodeTaskType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, taskId_);
      }
      if (taskCase_ == 10) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, task_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, nodeTaskType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, taskId_);
      }
      if (taskCase_ == 10) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, task_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsNode.NodeTaskCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsNode.NodeTaskCmd other = (com.yorha.proto.SsNode.NodeTaskCmd) obj;

      if (hasNodeTaskType() != other.hasNodeTaskType()) return false;
      if (hasNodeTaskType()) {
        if (nodeTaskType_ != other.nodeTaskType_) return false;
      }
      if (hasTaskId() != other.hasTaskId()) return false;
      if (hasTaskId()) {
        if (getTaskId()
            != other.getTaskId()) return false;
      }
      if (!getTaskCase().equals(other.getTaskCase())) return false;
      switch (taskCase_) {
        case 10:
          if (!getStringMsg()
              .equals(other.getStringMsg())) return false;
          break;
        case 0:
        default:
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNodeTaskType()) {
        hash = (37 * hash) + NODETASKTYPE_FIELD_NUMBER;
        hash = (53 * hash) + nodeTaskType_;
      }
      if (hasTaskId()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTaskId());
      }
      switch (taskCase_) {
        case 10:
          hash = (37 * hash) + STRINGMSG_FIELD_NUMBER;
          hash = (53 * hash) + getStringMsg().hashCode();
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsNode.NodeTaskCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsNode.NodeTaskCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 系统消息：包体
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.NodeTaskCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.NodeTaskCmd)
        com.yorha.proto.SsNode.NodeTaskCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsNode.internal_static_com_yorha_proto_NodeTaskCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsNode.internal_static_com_yorha_proto_NodeTaskCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsNode.NodeTaskCmd.class, com.yorha.proto.SsNode.NodeTaskCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsNode.NodeTaskCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        nodeTaskType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        taskId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        taskCase_ = 0;
        task_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsNode.internal_static_com_yorha_proto_NodeTaskCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsNode.NodeTaskCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsNode.NodeTaskCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsNode.NodeTaskCmd build() {
        com.yorha.proto.SsNode.NodeTaskCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsNode.NodeTaskCmd buildPartial() {
        com.yorha.proto.SsNode.NodeTaskCmd result = new com.yorha.proto.SsNode.NodeTaskCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.nodeTaskType_ = nodeTaskType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.taskId_ = taskId_;
          to_bitField0_ |= 0x00000002;
        }
        if (taskCase_ == 10) {
          result.task_ = task_;
        }
        result.bitField0_ = to_bitField0_;
        result.taskCase_ = taskCase_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsNode.NodeTaskCmd) {
          return mergeFrom((com.yorha.proto.SsNode.NodeTaskCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsNode.NodeTaskCmd other) {
        if (other == com.yorha.proto.SsNode.NodeTaskCmd.getDefaultInstance()) return this;
        if (other.hasNodeTaskType()) {
          setNodeTaskType(other.getNodeTaskType());
        }
        if (other.hasTaskId()) {
          setTaskId(other.getTaskId());
        }
        switch (other.getTaskCase()) {
          case STRINGMSG: {
            taskCase_ = 10;
            task_ = other.task_;
            onChanged();
            break;
          }
          case TASK_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsNode.NodeTaskCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsNode.NodeTaskCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int taskCase_ = 0;
      private java.lang.Object task_;
      public TaskCase
          getTaskCase() {
        return TaskCase.forNumber(
            taskCase_);
      }

      public Builder clearTask() {
        taskCase_ = 0;
        task_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      private int nodeTaskType_ = 0;
      /**
       * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
       * @return Whether the nodeTaskType field is set.
       */
      @java.lang.Override public boolean hasNodeTaskType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
       * @return The nodeTaskType.
       */
      @java.lang.Override
      public com.yorha.proto.SsNode.NodeTaskType getNodeTaskType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.SsNode.NodeTaskType result = com.yorha.proto.SsNode.NodeTaskType.valueOf(nodeTaskType_);
        return result == null ? com.yorha.proto.SsNode.NodeTaskType.NTT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
       * @param value The nodeTaskType to set.
       * @return This builder for chaining.
       */
      public Builder setNodeTaskType(com.yorha.proto.SsNode.NodeTaskType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        nodeTaskType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NodeTaskType nodeTaskType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNodeTaskType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nodeTaskType_ = 0;
        onChanged();
        return this;
      }

      private long taskId_ ;
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return Whether the taskId field is set.
       */
      @java.lang.Override
      public boolean hasTaskId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public long getTaskId() {
        return taskId_;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(long value) {
        bitField0_ |= 0x00000002;
        taskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 taskId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        taskId_ = 0L;
        onChanged();
        return this;
      }

      /**
       * <pre>
       * 广播的string，前面废弃
       * </pre>
       *
       * <code>string stringMsg = 10;</code>
       * @return Whether the stringMsg field is set.
       */
      @java.lang.Override
      public boolean hasStringMsg() {
        return taskCase_ == 10;
      }
      /**
       * <pre>
       * 广播的string，前面废弃
       * </pre>
       *
       * <code>string stringMsg = 10;</code>
       * @return The stringMsg.
       */
      @java.lang.Override
      public java.lang.String getStringMsg() {
        java.lang.Object ref = "";
        if (taskCase_ == 10) {
          ref = task_;
        }
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (taskCase_ == 10) {
            if (bs.isValidUtf8()) {
              task_ = s;
            }
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 广播的string，前面废弃
       * </pre>
       *
       * <code>string stringMsg = 10;</code>
       * @return The bytes for stringMsg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getStringMsgBytes() {
        java.lang.Object ref = "";
        if (taskCase_ == 10) {
          ref = task_;
        }
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          if (taskCase_ == 10) {
            task_ = b;
          }
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 广播的string，前面废弃
       * </pre>
       *
       * <code>string stringMsg = 10;</code>
       * @param value The stringMsg to set.
       * @return This builder for chaining.
       */
      public Builder setStringMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  taskCase_ = 10;
        task_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广播的string，前面废弃
       * </pre>
       *
       * <code>string stringMsg = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearStringMsg() {
        if (taskCase_ == 10) {
          taskCase_ = 0;
          task_ = null;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 广播的string，前面废弃
       * </pre>
       *
       * <code>string stringMsg = 10;</code>
       * @param value The bytes for stringMsg to set.
       * @return This builder for chaining.
       */
      public Builder setStringMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  taskCase_ = 10;
        task_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.NodeTaskCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.NodeTaskCmd)
    private static final com.yorha.proto.SsNode.NodeTaskCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsNode.NodeTaskCmd();
    }

    public static com.yorha.proto.SsNode.NodeTaskCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<NodeTaskCmd>
        PARSER = new com.google.protobuf.AbstractParser<NodeTaskCmd>() {
      @java.lang.Override
      public NodeTaskCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new NodeTaskCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<NodeTaskCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NodeTaskCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsNode.NodeTaskCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_NodeTaskCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_NodeTaskCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037ss_proto/gen/node/ss_node.proto\022\017com.y" +
      "orha.proto\"o\n\013NodeTaskCmd\0223\n\014nodeTaskTyp" +
      "e\030\001 \001(\0162\035.com.yorha.proto.NodeTaskType\022\016" +
      "\n\006taskId\030\002 \001(\003\022\023\n\tstringMsg\030\n \001(\tH\000B\006\n\004t" +
      "ask*0\n\014NodeTaskType\022\014\n\010NTT_NONE\020\000\022\022\n\016NTT" +
      "_STRING_MSG\020\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_NodeTaskCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_NodeTaskCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_NodeTaskCmd_descriptor,
        new java.lang.String[] { "NodeTaskType", "TaskId", "StringMsg", "Task", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

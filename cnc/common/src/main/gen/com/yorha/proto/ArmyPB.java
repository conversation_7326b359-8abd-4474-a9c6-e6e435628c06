// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/army/armyPB.proto

package com.yorha.proto;

public final class ArmyPB {
  private ArmyPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ArmyEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ArmyEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
     * @return Whether the armyState field is set.
     */
    boolean hasArmyState();
    /**
     * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
     * @return The armyState.
     */
    com.yorha.proto.CommonEnum.ArmyState getArmyState();

    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return Whether the troop field is set.
     */
    boolean hasTroop();
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return The troop.
     */
    com.yorha.proto.StructPlayerPB.TroopPB getTroop();
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     */
    com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder();

    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return Whether the move field is set.
     */
    boolean hasMove();
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return The move.
     */
    com.yorha.proto.StructPB.MovePB getMove();
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     */
    com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder();

    /**
     * <code>optional int64 ownerId = 4;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <code>optional int64 ownerId = 4;</code>
     * @return The ownerId.
     */
    long getOwnerId();

    /**
     * <code>optional int32 formationId = 5;</code>
     * @return Whether the formationId field is set.
     */
    boolean hasFormationId();
    /**
     * <code>optional int32 formationId = 5;</code>
     * @return The formationId.
     */
    int getFormationId();

    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
     * @return Whether the battle field is set.
     */
    boolean hasBattle();
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
     * @return The battle.
     */
    com.yorha.proto.StructBattlePB.BattlePB getBattle();
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
     */
    com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder();

    /**
     * <pre>
     * buf列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
     * @return Whether the buff field is set.
     */
    boolean hasBuff();
    /**
     * <pre>
     * buf列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
     * @return The buff.
     */
    com.yorha.proto.StructBattlePB.Int32BuffMapPB getBuff();
    /**
     * <pre>
     * buf列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
     */
    com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder getBuffOrBuilder();

    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 8;</code>
     * @return Whether the camp field is set.
     */
    boolean hasCamp();
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 8;</code>
     * @return The camp.
     */
    com.yorha.proto.CommonEnum.Camp getCamp();

    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 9;</code>
     * @return Whether the clanSname field is set.
     */
    boolean hasClanSname();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 9;</code>
     * @return The clanSname.
     */
    java.lang.String getClanSname();
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 9;</code>
     * @return The bytes for clanSname.
     */
    com.google.protobuf.ByteString
        getClanSnameBytes();

    /**
     * <pre>
     * 状态进入时间
     * </pre>
     *
     * <code>optional int64 enterStateTs = 10;</code>
     * @return Whether the enterStateTs field is set.
     */
    boolean hasEnterStateTs();
    /**
     * <pre>
     * 状态进入时间
     * </pre>
     *
     * <code>optional int64 enterStateTs = 10;</code>
     * @return The enterStateTs.
     */
    long getEnterStateTs();

    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 11;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 11;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 集结所用血量分母
     * </pre>
     *
     * <code>optional int64 hpMax = 12;</code>
     * @return Whether the hpMax field is set.
     */
    boolean hasHpMax();
    /**
     * <pre>
     * 集结所用血量分母
     * </pre>
     *
     * <code>optional int64 hpMax = 12;</code>
     * @return The hpMax.
     */
    long getHpMax();

    /**
     * <pre>
     * 拾取信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
     * @return Whether the pickUpInfo field is set.
     */
    boolean hasPickUpInfo();
    /**
     * <pre>
     * 拾取信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
     * @return The pickUpInfo.
     */
    com.yorha.proto.StructPB.PickUpInfoPB getPickUpInfo();
    /**
     * <pre>
     * 拾取信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
     */
    com.yorha.proto.StructPB.PickUpInfoPBOrBuilder getPickUpInfoOrBuilder();

    /**
     * <pre>
     * 依附的对象id
     * </pre>
     *
     * <code>optional int64 attachId = 14;</code>
     * @return Whether the attachId field is set.
     */
    boolean hasAttachId();
    /**
     * <pre>
     * 依附的对象id
     * </pre>
     *
     * <code>optional int64 attachId = 14;</code>
     * @return The attachId.
     */
    long getAttachId();

    /**
     * <pre>
     * 依附状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
     * @return Whether the attachState field is set.
     */
    boolean hasAttachState();
    /**
     * <pre>
     * 依附状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
     * @return The attachState.
     */
    com.yorha.proto.CommonEnum.AttachState getAttachState();

    /**
     * <pre>
     * 运输机
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
     * @return Whether the transportPlane field is set.
     */
    boolean hasTransportPlane();
    /**
     * <pre>
     * 运输机
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
     * @return The transportPlane.
     */
    com.yorha.proto.StructPB.SceneTransportPlanePB getTransportPlane();
    /**
     * <pre>
     * 运输机
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
     */
    com.yorha.proto.StructPB.SceneTransportPlanePBOrBuilder getTransportPlaneOrBuilder();

    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
     * @return The cardHead.
     */
    com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead();
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
     */
    com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder();

    /**
     * <pre>
     * 联盟旗帜 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
     * @return Whether the clanFlag field is set.
     */
    boolean hasClanFlag();
    /**
     * <pre>
     * 联盟旗帜 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
     * @return The clanFlag.
     */
    com.yorha.proto.StructClanPB.ClanFlagInfoPB getClanFlag();
    /**
     * <pre>
     * 联盟旗帜 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
     */
    com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder getClanFlagOrBuilder();

    /**
     * <pre>
     * 小箭头 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
     * @return Whether the arrow field is set.
     */
    boolean hasArrow();
    /**
     * <pre>
     * 小箭头 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
     * @return The arrow.
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow();
    /**
     * <pre>
     * 小箭头 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
     */
    com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder();

    /**
     * <pre>
     * 模型圈半径
     * </pre>
     *
     * <code>optional int32 modelRadius = 21;</code>
     * @return Whether the modelRadius field is set.
     */
    boolean hasModelRadius();
    /**
     * <pre>
     * 模型圈半径
     * </pre>
     *
     * <code>optional int32 modelRadius = 21;</code>
     * @return The modelRadius.
     */
    int getModelRadius();

    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return Whether the expression field is set.
     */
    boolean hasExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return The expression.
     */
    com.yorha.proto.StructPB.ExpressionPB getExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     */
    com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder();

    /**
     * <pre>
     * 集结角色
     * </pre>
     *
     * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
     * @return Whether the rallyRole field is set.
     */
    boolean hasRallyRole();
    /**
     * <pre>
     * 集结角色
     * </pre>
     *
     * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
     * @return The rallyRole.
     */
    com.yorha.proto.CommonEnum.RallyArmyRoleType getRallyRole();

    /**
     * <pre>
     * 当前所处的集结id 0代表未参与集结
     * </pre>
     *
     * <code>optional int64 curRallyId = 25;</code>
     * @return Whether the curRallyId field is set.
     */
    boolean hasCurRallyId();
    /**
     * <pre>
     * 当前所处的集结id 0代表未参与集结
     * </pre>
     *
     * <code>optional int64 curRallyId = 25;</code>
     * @return The curRallyId.
     */
    long getCurRallyId();

    /**
     * <pre>
     * 当前所援助的城池 0代表未参与援助
     * </pre>
     *
     * <code>optional int64 curAssistTargetId = 26;</code>
     * @return Whether the curAssistTargetId field is set.
     */
    boolean hasCurAssistTargetId();
    /**
     * <pre>
     * 当前所援助的城池 0代表未参与援助
     * </pre>
     *
     * <code>optional int64 curAssistTargetId = 26;</code>
     * @return The curAssistTargetId.
     */
    long getCurAssistTargetId();

    /**
     * <pre>
     * 部队所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 27;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 部队所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 27;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ArmyEntityPB}
   */
  public static final class ArmyEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ArmyEntityPB)
      ArmyEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ArmyEntityPB.newBuilder() to construct.
    private ArmyEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ArmyEntityPB() {
      armyState_ = 0;
      camp_ = 0;
      clanSname_ = "";
      attachState_ = 0;
      rallyRole_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ArmyEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ArmyEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ArmyState value = com.yorha.proto.CommonEnum.ArmyState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                armyState_ = rawValue;
              }
              break;
            }
            case 18: {
              com.yorha.proto.StructPlayerPB.TroopPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = troop_.toBuilder();
              }
              troop_ = input.readMessage(com.yorha.proto.StructPlayerPB.TroopPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(troop_);
                troop_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.StructPB.MovePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = move_.toBuilder();
              }
              move_ = input.readMessage(com.yorha.proto.StructPB.MovePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(move_);
                move_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              ownerId_ = input.readInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              formationId_ = input.readInt32();
              break;
            }
            case 50: {
              com.yorha.proto.StructBattlePB.BattlePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000020) != 0)) {
                subBuilder = battle_.toBuilder();
              }
              battle_ = input.readMessage(com.yorha.proto.StructBattlePB.BattlePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battle_);
                battle_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000020;
              break;
            }
            case 58: {
              com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = buff_.toBuilder();
              }
              buff_ = input.readMessage(com.yorha.proto.StructBattlePB.Int32BuffMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buff_);
                buff_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 64: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Camp value = com.yorha.proto.CommonEnum.Camp.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(8, rawValue);
              } else {
                bitField0_ |= 0x00000080;
                camp_ = rawValue;
              }
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              clanSname_ = bs;
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              enterStateTs_ = input.readInt64();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              clanId_ = input.readInt64();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              hpMax_ = input.readInt64();
              break;
            }
            case 106: {
              com.yorha.proto.StructPB.PickUpInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00001000) != 0)) {
                subBuilder = pickUpInfo_.toBuilder();
              }
              pickUpInfo_ = input.readMessage(com.yorha.proto.StructPB.PickUpInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pickUpInfo_);
                pickUpInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00001000;
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              attachId_ = input.readInt64();
              break;
            }
            case 120: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.AttachState value = com.yorha.proto.CommonEnum.AttachState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(15, rawValue);
              } else {
                bitField0_ |= 0x00004000;
                attachState_ = rawValue;
              }
              break;
            }
            case 130: {
              com.yorha.proto.StructPB.SceneTransportPlanePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00008000) != 0)) {
                subBuilder = transportPlane_.toBuilder();
              }
              transportPlane_ = input.readMessage(com.yorha.proto.StructPB.SceneTransportPlanePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(transportPlane_);
                transportPlane_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00008000;
              break;
            }
            case 146: {
              com.yorha.proto.StructPB.PlayerCardHeadPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00010000) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.StructPB.PlayerCardHeadPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00010000;
              break;
            }
            case 154: {
              com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00020000) != 0)) {
                subBuilder = clanFlag_.toBuilder();
              }
              clanFlag_ = input.readMessage(com.yorha.proto.StructClanPB.ClanFlagInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(clanFlag_);
                clanFlag_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00020000;
              break;
            }
            case 162: {
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00040000) != 0)) {
                subBuilder = arrow_.toBuilder();
              }
              arrow_ = input.readMessage(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(arrow_);
                arrow_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00040000;
              break;
            }
            case 168: {
              bitField0_ |= 0x00080000;
              modelRadius_ = input.readInt32();
              break;
            }
            case 178: {
              com.yorha.proto.StructPB.ExpressionPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00100000) != 0)) {
                subBuilder = expression_.toBuilder();
              }
              expression_ = input.readMessage(com.yorha.proto.StructPB.ExpressionPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(expression_);
                expression_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00100000;
              break;
            }
            case 192: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.RallyArmyRoleType value = com.yorha.proto.CommonEnum.RallyArmyRoleType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(24, rawValue);
              } else {
                bitField0_ |= 0x00200000;
                rallyRole_ = rawValue;
              }
              break;
            }
            case 200: {
              bitField0_ |= 0x00400000;
              curRallyId_ = input.readInt64();
              break;
            }
            case 208: {
              bitField0_ |= 0x00800000;
              curAssistTargetId_ = input.readInt64();
              break;
            }
            case 216: {
              bitField0_ |= 0x01000000;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.ArmyPB.ArmyEntityPB.class, com.yorha.proto.ArmyPB.ArmyEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int ARMYSTATE_FIELD_NUMBER = 1;
    private int armyState_;
    /**
     * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
     * @return Whether the armyState field is set.
     */
    @java.lang.Override public boolean hasArmyState() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
     * @return The armyState.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ArmyState getArmyState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ArmyState result = com.yorha.proto.CommonEnum.ArmyState.valueOf(armyState_);
      return result == null ? com.yorha.proto.CommonEnum.ArmyState.AS_None : result;
    }

    public static final int TROOP_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPlayerPB.TroopPB troop_;
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return Whether the troop field is set.
     */
    @java.lang.Override
    public boolean hasTroop() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     * @return The troop.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }
    /**
     * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
      return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
    }

    public static final int MOVE_FIELD_NUMBER = 3;
    private com.yorha.proto.StructPB.MovePB move_;
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return Whether the move field is set.
     */
    @java.lang.Override
    public boolean hasMove() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     * @return The move.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MovePB getMove() {
      return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
    }
    /**
     * <code>optional .com.yorha.proto.MovePB move = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder() {
      return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
    }

    public static final int OWNERID_FIELD_NUMBER = 4;
    private long ownerId_;
    /**
     * <code>optional int64 ownerId = 4;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 ownerId = 4;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    public static final int FORMATIONID_FIELD_NUMBER = 5;
    private int formationId_;
    /**
     * <code>optional int32 formationId = 5;</code>
     * @return Whether the formationId field is set.
     */
    @java.lang.Override
    public boolean hasFormationId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 formationId = 5;</code>
     * @return The formationId.
     */
    @java.lang.Override
    public int getFormationId() {
      return formationId_;
    }

    public static final int BATTLE_FIELD_NUMBER = 6;
    private com.yorha.proto.StructBattlePB.BattlePB battle_;
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
     * @return Whether the battle field is set.
     */
    @java.lang.Override
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
     * @return The battle.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }
    /**
     * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
      return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
    }

    public static final int BUFF_FIELD_NUMBER = 7;
    private com.yorha.proto.StructBattlePB.Int32BuffMapPB buff_;
    /**
     * <pre>
     * buf列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
     * @return Whether the buff field is set.
     */
    @java.lang.Override
    public boolean hasBuff() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * buf列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
     * @return The buff.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.Int32BuffMapPB getBuff() {
      return buff_ == null ? com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
    }
    /**
     * <pre>
     * buf列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder getBuffOrBuilder() {
      return buff_ == null ? com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
    }

    public static final int CAMP_FIELD_NUMBER = 8;
    private int camp_;
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 8;</code>
     * @return Whether the camp field is set.
     */
    @java.lang.Override public boolean hasCamp() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 阵营
     * </pre>
     *
     * <code>optional .com.yorha.proto.Camp camp = 8;</code>
     * @return The camp.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Camp getCamp() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
      return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
    }

    public static final int CLANSNAME_FIELD_NUMBER = 9;
    private volatile java.lang.Object clanSname_;
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 9;</code>
     * @return Whether the clanSname field is set.
     */
    @java.lang.Override
    public boolean hasClanSname() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 9;</code>
     * @return The clanSname.
     */
    @java.lang.Override
    public java.lang.String getClanSname() {
      java.lang.Object ref = clanSname_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSname_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 联盟简称
     * </pre>
     *
     * <code>optional string clanSname = 9;</code>
     * @return The bytes for clanSname.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSnameBytes() {
      java.lang.Object ref = clanSname_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSname_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENTERSTATETS_FIELD_NUMBER = 10;
    private long enterStateTs_;
    /**
     * <pre>
     * 状态进入时间
     * </pre>
     *
     * <code>optional int64 enterStateTs = 10;</code>
     * @return Whether the enterStateTs field is set.
     */
    @java.lang.Override
    public boolean hasEnterStateTs() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 状态进入时间
     * </pre>
     *
     * <code>optional int64 enterStateTs = 10;</code>
     * @return The enterStateTs.
     */
    @java.lang.Override
    public long getEnterStateTs() {
      return enterStateTs_;
    }

    public static final int CLANID_FIELD_NUMBER = 11;
    private long clanId_;
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 11;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 联盟id
     * </pre>
     *
     * <code>optional int64 clanId = 11;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int HPMAX_FIELD_NUMBER = 12;
    private long hpMax_;
    /**
     * <pre>
     * 集结所用血量分母
     * </pre>
     *
     * <code>optional int64 hpMax = 12;</code>
     * @return Whether the hpMax field is set.
     */
    @java.lang.Override
    public boolean hasHpMax() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 集结所用血量分母
     * </pre>
     *
     * <code>optional int64 hpMax = 12;</code>
     * @return The hpMax.
     */
    @java.lang.Override
    public long getHpMax() {
      return hpMax_;
    }

    public static final int PICKUPINFO_FIELD_NUMBER = 13;
    private com.yorha.proto.StructPB.PickUpInfoPB pickUpInfo_;
    /**
     * <pre>
     * 拾取信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
     * @return Whether the pickUpInfo field is set.
     */
    @java.lang.Override
    public boolean hasPickUpInfo() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 拾取信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
     * @return The pickUpInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PickUpInfoPB getPickUpInfo() {
      return pickUpInfo_ == null ? com.yorha.proto.StructPB.PickUpInfoPB.getDefaultInstance() : pickUpInfo_;
    }
    /**
     * <pre>
     * 拾取信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PickUpInfoPBOrBuilder getPickUpInfoOrBuilder() {
      return pickUpInfo_ == null ? com.yorha.proto.StructPB.PickUpInfoPB.getDefaultInstance() : pickUpInfo_;
    }

    public static final int ATTACHID_FIELD_NUMBER = 14;
    private long attachId_;
    /**
     * <pre>
     * 依附的对象id
     * </pre>
     *
     * <code>optional int64 attachId = 14;</code>
     * @return Whether the attachId field is set.
     */
    @java.lang.Override
    public boolean hasAttachId() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 依附的对象id
     * </pre>
     *
     * <code>optional int64 attachId = 14;</code>
     * @return The attachId.
     */
    @java.lang.Override
    public long getAttachId() {
      return attachId_;
    }

    public static final int ATTACHSTATE_FIELD_NUMBER = 15;
    private int attachState_;
    /**
     * <pre>
     * 依附状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
     * @return Whether the attachState field is set.
     */
    @java.lang.Override public boolean hasAttachState() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 依附状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
     * @return The attachState.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.AttachState getAttachState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.AttachState result = com.yorha.proto.CommonEnum.AttachState.valueOf(attachState_);
      return result == null ? com.yorha.proto.CommonEnum.AttachState.AAS_NONE : result;
    }

    public static final int TRANSPORTPLANE_FIELD_NUMBER = 16;
    private com.yorha.proto.StructPB.SceneTransportPlanePB transportPlane_;
    /**
     * <pre>
     * 运输机
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
     * @return Whether the transportPlane field is set.
     */
    @java.lang.Override
    public boolean hasTransportPlane() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 运输机
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
     * @return The transportPlane.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.SceneTransportPlanePB getTransportPlane() {
      return transportPlane_ == null ? com.yorha.proto.StructPB.SceneTransportPlanePB.getDefaultInstance() : transportPlane_;
    }
    /**
     * <pre>
     * 运输机
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.SceneTransportPlanePBOrBuilder getTransportPlaneOrBuilder() {
      return transportPlane_ == null ? com.yorha.proto.StructPB.SceneTransportPlanePB.getDefaultInstance() : transportPlane_;
    }

    public static final int CARDHEAD_FIELD_NUMBER = 18;
    private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }
    /**
     * <pre>
     * 玩家铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
    }

    public static final int CLANFLAG_FIELD_NUMBER = 19;
    private com.yorha.proto.StructClanPB.ClanFlagInfoPB clanFlag_;
    /**
     * <pre>
     * 联盟旗帜 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
     * @return Whether the clanFlag field is set.
     */
    @java.lang.Override
    public boolean hasClanFlag() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * 联盟旗帜 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
     * @return The clanFlag.
     */
    @java.lang.Override
    public com.yorha.proto.StructClanPB.ClanFlagInfoPB getClanFlag() {
      return clanFlag_ == null ? com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : clanFlag_;
    }
    /**
     * <pre>
     * 联盟旗帜 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder getClanFlagOrBuilder() {
      return clanFlag_ == null ? com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : clanFlag_;
    }

    public static final int ARROW_FIELD_NUMBER = 20;
    private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
    /**
     * <pre>
     * 小箭头 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
     * @return Whether the arrow field is set.
     */
    @java.lang.Override
    public boolean hasArrow() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * 小箭头 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
     * @return The arrow.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }
    /**
     * <pre>
     * 小箭头 集结部队才有
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
      return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
    }

    public static final int MODELRADIUS_FIELD_NUMBER = 21;
    private int modelRadius_;
    /**
     * <pre>
     * 模型圈半径
     * </pre>
     *
     * <code>optional int32 modelRadius = 21;</code>
     * @return Whether the modelRadius field is set.
     */
    @java.lang.Override
    public boolean hasModelRadius() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 模型圈半径
     * </pre>
     *
     * <code>optional int32 modelRadius = 21;</code>
     * @return The modelRadius.
     */
    @java.lang.Override
    public int getModelRadius() {
      return modelRadius_;
    }

    public static final int EXPRESSION_FIELD_NUMBER = 22;
    private com.yorha.proto.StructPB.ExpressionPB expression_;
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return Whether the expression field is set.
     */
    @java.lang.Override
    public boolean hasExpression() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     * @return The expression.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPB getExpression() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
      return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
    }

    public static final int RALLYROLE_FIELD_NUMBER = 24;
    private int rallyRole_;
    /**
     * <pre>
     * 集结角色
     * </pre>
     *
     * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
     * @return Whether the rallyRole field is set.
     */
    @java.lang.Override public boolean hasRallyRole() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 集结角色
     * </pre>
     *
     * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
     * @return The rallyRole.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.RallyArmyRoleType getRallyRole() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.RallyArmyRoleType result = com.yorha.proto.CommonEnum.RallyArmyRoleType.valueOf(rallyRole_);
      return result == null ? com.yorha.proto.CommonEnum.RallyArmyRoleType.RART_Single : result;
    }

    public static final int CURRALLYID_FIELD_NUMBER = 25;
    private long curRallyId_;
    /**
     * <pre>
     * 当前所处的集结id 0代表未参与集结
     * </pre>
     *
     * <code>optional int64 curRallyId = 25;</code>
     * @return Whether the curRallyId field is set.
     */
    @java.lang.Override
    public boolean hasCurRallyId() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * 当前所处的集结id 0代表未参与集结
     * </pre>
     *
     * <code>optional int64 curRallyId = 25;</code>
     * @return The curRallyId.
     */
    @java.lang.Override
    public long getCurRallyId() {
      return curRallyId_;
    }

    public static final int CURASSISTTARGETID_FIELD_NUMBER = 26;
    private long curAssistTargetId_;
    /**
     * <pre>
     * 当前所援助的城池 0代表未参与援助
     * </pre>
     *
     * <code>optional int64 curAssistTargetId = 26;</code>
     * @return Whether the curAssistTargetId field is set.
     */
    @java.lang.Override
    public boolean hasCurAssistTargetId() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * 当前所援助的城池 0代表未参与援助
     * </pre>
     *
     * <code>optional int64 curAssistTargetId = 26;</code>
     * @return The curAssistTargetId.
     */
    @java.lang.Override
    public long getCurAssistTargetId() {
      return curAssistTargetId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 27;
    private int zoneId_;
    /**
     * <pre>
     * 部队所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 27;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <pre>
     * 部队所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 27;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, armyState_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getTroop());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getMove());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, ownerId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, formationId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(6, getBattle());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getBuff());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeEnum(8, camp_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, clanSname_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt64(10, enterStateTs_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeInt64(11, clanId_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt64(12, hpMax_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeMessage(13, getPickUpInfo());
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeInt64(14, attachId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeEnum(15, attachState_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeMessage(16, getTransportPlane());
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeMessage(18, getCardHead());
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeMessage(19, getClanFlag());
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeMessage(20, getArrow());
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeInt32(21, modelRadius_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeMessage(22, getExpression());
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeEnum(24, rallyRole_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeInt64(25, curRallyId_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeInt64(26, curAssistTargetId_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeInt32(27, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, armyState_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTroop());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getMove());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, ownerId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, formationId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getBattle());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getBuff());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(8, camp_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, clanSname_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, enterStateTs_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(11, clanId_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(12, hpMax_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, getPickUpInfo());
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(14, attachId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(15, attachState_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, getTransportPlane());
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(18, getCardHead());
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(19, getClanFlag());
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(20, getArrow());
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(21, modelRadius_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(22, getExpression());
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(24, rallyRole_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(25, curRallyId_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(26, curAssistTargetId_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(27, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.ArmyPB.ArmyEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.ArmyPB.ArmyEntityPB other = (com.yorha.proto.ArmyPB.ArmyEntityPB) obj;

      if (hasArmyState() != other.hasArmyState()) return false;
      if (hasArmyState()) {
        if (armyState_ != other.armyState_) return false;
      }
      if (hasTroop() != other.hasTroop()) return false;
      if (hasTroop()) {
        if (!getTroop()
            .equals(other.getTroop())) return false;
      }
      if (hasMove() != other.hasMove()) return false;
      if (hasMove()) {
        if (!getMove()
            .equals(other.getMove())) return false;
      }
      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (hasFormationId() != other.hasFormationId()) return false;
      if (hasFormationId()) {
        if (getFormationId()
            != other.getFormationId()) return false;
      }
      if (hasBattle() != other.hasBattle()) return false;
      if (hasBattle()) {
        if (!getBattle()
            .equals(other.getBattle())) return false;
      }
      if (hasBuff() != other.hasBuff()) return false;
      if (hasBuff()) {
        if (!getBuff()
            .equals(other.getBuff())) return false;
      }
      if (hasCamp() != other.hasCamp()) return false;
      if (hasCamp()) {
        if (camp_ != other.camp_) return false;
      }
      if (hasClanSname() != other.hasClanSname()) return false;
      if (hasClanSname()) {
        if (!getClanSname()
            .equals(other.getClanSname())) return false;
      }
      if (hasEnterStateTs() != other.hasEnterStateTs()) return false;
      if (hasEnterStateTs()) {
        if (getEnterStateTs()
            != other.getEnterStateTs()) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasHpMax() != other.hasHpMax()) return false;
      if (hasHpMax()) {
        if (getHpMax()
            != other.getHpMax()) return false;
      }
      if (hasPickUpInfo() != other.hasPickUpInfo()) return false;
      if (hasPickUpInfo()) {
        if (!getPickUpInfo()
            .equals(other.getPickUpInfo())) return false;
      }
      if (hasAttachId() != other.hasAttachId()) return false;
      if (hasAttachId()) {
        if (getAttachId()
            != other.getAttachId()) return false;
      }
      if (hasAttachState() != other.hasAttachState()) return false;
      if (hasAttachState()) {
        if (attachState_ != other.attachState_) return false;
      }
      if (hasTransportPlane() != other.hasTransportPlane()) return false;
      if (hasTransportPlane()) {
        if (!getTransportPlane()
            .equals(other.getTransportPlane())) return false;
      }
      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (hasClanFlag() != other.hasClanFlag()) return false;
      if (hasClanFlag()) {
        if (!getClanFlag()
            .equals(other.getClanFlag())) return false;
      }
      if (hasArrow() != other.hasArrow()) return false;
      if (hasArrow()) {
        if (!getArrow()
            .equals(other.getArrow())) return false;
      }
      if (hasModelRadius() != other.hasModelRadius()) return false;
      if (hasModelRadius()) {
        if (getModelRadius()
            != other.getModelRadius()) return false;
      }
      if (hasExpression() != other.hasExpression()) return false;
      if (hasExpression()) {
        if (!getExpression()
            .equals(other.getExpression())) return false;
      }
      if (hasRallyRole() != other.hasRallyRole()) return false;
      if (hasRallyRole()) {
        if (rallyRole_ != other.rallyRole_) return false;
      }
      if (hasCurRallyId() != other.hasCurRallyId()) return false;
      if (hasCurRallyId()) {
        if (getCurRallyId()
            != other.getCurRallyId()) return false;
      }
      if (hasCurAssistTargetId() != other.hasCurAssistTargetId()) return false;
      if (hasCurAssistTargetId()) {
        if (getCurAssistTargetId()
            != other.getCurAssistTargetId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasArmyState()) {
        hash = (37 * hash) + ARMYSTATE_FIELD_NUMBER;
        hash = (53 * hash) + armyState_;
      }
      if (hasTroop()) {
        hash = (37 * hash) + TROOP_FIELD_NUMBER;
        hash = (53 * hash) + getTroop().hashCode();
      }
      if (hasMove()) {
        hash = (37 * hash) + MOVE_FIELD_NUMBER;
        hash = (53 * hash) + getMove().hashCode();
      }
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      if (hasFormationId()) {
        hash = (37 * hash) + FORMATIONID_FIELD_NUMBER;
        hash = (53 * hash) + getFormationId();
      }
      if (hasBattle()) {
        hash = (37 * hash) + BATTLE_FIELD_NUMBER;
        hash = (53 * hash) + getBattle().hashCode();
      }
      if (hasBuff()) {
        hash = (37 * hash) + BUFF_FIELD_NUMBER;
        hash = (53 * hash) + getBuff().hashCode();
      }
      if (hasCamp()) {
        hash = (37 * hash) + CAMP_FIELD_NUMBER;
        hash = (53 * hash) + camp_;
      }
      if (hasClanSname()) {
        hash = (37 * hash) + CLANSNAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSname().hashCode();
      }
      if (hasEnterStateTs()) {
        hash = (37 * hash) + ENTERSTATETS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEnterStateTs());
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasHpMax()) {
        hash = (37 * hash) + HPMAX_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getHpMax());
      }
      if (hasPickUpInfo()) {
        hash = (37 * hash) + PICKUPINFO_FIELD_NUMBER;
        hash = (53 * hash) + getPickUpInfo().hashCode();
      }
      if (hasAttachId()) {
        hash = (37 * hash) + ATTACHID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getAttachId());
      }
      if (hasAttachState()) {
        hash = (37 * hash) + ATTACHSTATE_FIELD_NUMBER;
        hash = (53 * hash) + attachState_;
      }
      if (hasTransportPlane()) {
        hash = (37 * hash) + TRANSPORTPLANE_FIELD_NUMBER;
        hash = (53 * hash) + getTransportPlane().hashCode();
      }
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      if (hasClanFlag()) {
        hash = (37 * hash) + CLANFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getClanFlag().hashCode();
      }
      if (hasArrow()) {
        hash = (37 * hash) + ARROW_FIELD_NUMBER;
        hash = (53 * hash) + getArrow().hashCode();
      }
      if (hasModelRadius()) {
        hash = (37 * hash) + MODELRADIUS_FIELD_NUMBER;
        hash = (53 * hash) + getModelRadius();
      }
      if (hasExpression()) {
        hash = (37 * hash) + EXPRESSION_FIELD_NUMBER;
        hash = (53 * hash) + getExpression().hashCode();
      }
      if (hasRallyRole()) {
        hash = (37 * hash) + RALLYROLE_FIELD_NUMBER;
        hash = (53 * hash) + rallyRole_;
      }
      if (hasCurRallyId()) {
        hash = (37 * hash) + CURRALLYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCurRallyId());
      }
      if (hasCurAssistTargetId()) {
        hash = (37 * hash) + CURASSISTTARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCurAssistTargetId());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.ArmyPB.ArmyEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ArmyEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ArmyEntityPB)
        com.yorha.proto.ArmyPB.ArmyEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.ArmyPB.ArmyEntityPB.class, com.yorha.proto.ArmyPB.ArmyEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.ArmyPB.ArmyEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTroopFieldBuilder();
          getMoveFieldBuilder();
          getBattleFieldBuilder();
          getBuffFieldBuilder();
          getPickUpInfoFieldBuilder();
          getTransportPlaneFieldBuilder();
          getCardHeadFieldBuilder();
          getClanFlagFieldBuilder();
          getArrowFieldBuilder();
          getExpressionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        armyState_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (troopBuilder_ == null) {
          troop_ = null;
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (moveBuilder_ == null) {
          move_ = null;
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        formationId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        if (battleBuilder_ == null) {
          battle_ = null;
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (buffBuilder_ == null) {
          buff_ = null;
        } else {
          buffBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        camp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        clanSname_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        enterStateTs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000400);
        hpMax_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        if (pickUpInfoBuilder_ == null) {
          pickUpInfo_ = null;
        } else {
          pickUpInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00001000);
        attachId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00002000);
        attachState_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        if (transportPlaneBuilder_ == null) {
          transportPlane_ = null;
        } else {
          transportPlaneBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00008000);
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00010000);
        if (clanFlagBuilder_ == null) {
          clanFlag_ = null;
        } else {
          clanFlagBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00020000);
        if (arrowBuilder_ == null) {
          arrow_ = null;
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00040000);
        modelRadius_ = 0;
        bitField0_ = (bitField0_ & ~0x00080000);
        if (expressionBuilder_ == null) {
          expression_ = null;
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00100000);
        rallyRole_ = 0;
        bitField0_ = (bitField0_ & ~0x00200000);
        curRallyId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00400000);
        curAssistTargetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00800000);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x01000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyEntityPB build() {
        com.yorha.proto.ArmyPB.ArmyEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyEntityPB buildPartial() {
        com.yorha.proto.ArmyPB.ArmyEntityPB result = new com.yorha.proto.ArmyPB.ArmyEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.armyState_ = armyState_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (troopBuilder_ == null) {
            result.troop_ = troop_;
          } else {
            result.troop_ = troopBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (moveBuilder_ == null) {
            result.move_ = move_;
          } else {
            result.move_ = moveBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.formationId_ = formationId_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          if (battleBuilder_ == null) {
            result.battle_ = battle_;
          } else {
            result.battle_ = battleBuilder_.build();
          }
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (buffBuilder_ == null) {
            result.buff_ = buff_;
          } else {
            result.buff_ = buffBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.camp_ = camp_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          to_bitField0_ |= 0x00000100;
        }
        result.clanSname_ = clanSname_;
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.enterStateTs_ = enterStateTs_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.hpMax_ = hpMax_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          if (pickUpInfoBuilder_ == null) {
            result.pickUpInfo_ = pickUpInfo_;
          } else {
            result.pickUpInfo_ = pickUpInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.attachId_ = attachId_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          to_bitField0_ |= 0x00004000;
        }
        result.attachState_ = attachState_;
        if (((from_bitField0_ & 0x00008000) != 0)) {
          if (transportPlaneBuilder_ == null) {
            result.transportPlane_ = transportPlane_;
          } else {
            result.transportPlane_ = transportPlaneBuilder_.build();
          }
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          if (clanFlagBuilder_ == null) {
            result.clanFlag_ = clanFlag_;
          } else {
            result.clanFlag_ = clanFlagBuilder_.build();
          }
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          if (arrowBuilder_ == null) {
            result.arrow_ = arrow_;
          } else {
            result.arrow_ = arrowBuilder_.build();
          }
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.modelRadius_ = modelRadius_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          if (expressionBuilder_ == null) {
            result.expression_ = expression_;
          } else {
            result.expression_ = expressionBuilder_.build();
          }
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          to_bitField0_ |= 0x00200000;
        }
        result.rallyRole_ = rallyRole_;
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.curRallyId_ = curRallyId_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.curAssistTargetId_ = curAssistTargetId_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x01000000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.ArmyPB.ArmyEntityPB) {
          return mergeFrom((com.yorha.proto.ArmyPB.ArmyEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.ArmyPB.ArmyEntityPB other) {
        if (other == com.yorha.proto.ArmyPB.ArmyEntityPB.getDefaultInstance()) return this;
        if (other.hasArmyState()) {
          setArmyState(other.getArmyState());
        }
        if (other.hasTroop()) {
          mergeTroop(other.getTroop());
        }
        if (other.hasMove()) {
          mergeMove(other.getMove());
        }
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        if (other.hasFormationId()) {
          setFormationId(other.getFormationId());
        }
        if (other.hasBattle()) {
          mergeBattle(other.getBattle());
        }
        if (other.hasBuff()) {
          mergeBuff(other.getBuff());
        }
        if (other.hasCamp()) {
          setCamp(other.getCamp());
        }
        if (other.hasClanSname()) {
          bitField0_ |= 0x00000100;
          clanSname_ = other.clanSname_;
          onChanged();
        }
        if (other.hasEnterStateTs()) {
          setEnterStateTs(other.getEnterStateTs());
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasHpMax()) {
          setHpMax(other.getHpMax());
        }
        if (other.hasPickUpInfo()) {
          mergePickUpInfo(other.getPickUpInfo());
        }
        if (other.hasAttachId()) {
          setAttachId(other.getAttachId());
        }
        if (other.hasAttachState()) {
          setAttachState(other.getAttachState());
        }
        if (other.hasTransportPlane()) {
          mergeTransportPlane(other.getTransportPlane());
        }
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        if (other.hasClanFlag()) {
          mergeClanFlag(other.getClanFlag());
        }
        if (other.hasArrow()) {
          mergeArrow(other.getArrow());
        }
        if (other.hasModelRadius()) {
          setModelRadius(other.getModelRadius());
        }
        if (other.hasExpression()) {
          mergeExpression(other.getExpression());
        }
        if (other.hasRallyRole()) {
          setRallyRole(other.getRallyRole());
        }
        if (other.hasCurRallyId()) {
          setCurRallyId(other.getCurRallyId());
        }
        if (other.hasCurAssistTargetId()) {
          setCurAssistTargetId(other.getCurAssistTargetId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.ArmyPB.ArmyEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.ArmyPB.ArmyEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int armyState_ = 0;
      /**
       * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
       * @return Whether the armyState field is set.
       */
      @java.lang.Override public boolean hasArmyState() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
       * @return The armyState.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ArmyState getArmyState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ArmyState result = com.yorha.proto.CommonEnum.ArmyState.valueOf(armyState_);
        return result == null ? com.yorha.proto.CommonEnum.ArmyState.AS_None : result;
      }
      /**
       * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
       * @param value The armyState to set.
       * @return This builder for chaining.
       */
      public Builder setArmyState(com.yorha.proto.CommonEnum.ArmyState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        armyState_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ArmyState armyState = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearArmyState() {
        bitField0_ = (bitField0_ & ~0x00000001);
        armyState_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPlayerPB.TroopPB troop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> troopBuilder_;
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       * @return Whether the troop field is set.
       */
      public boolean hasTroop() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       * @return The troop.
       */
      public com.yorha.proto.StructPlayerPB.TroopPB getTroop() {
        if (troopBuilder_ == null) {
          return troop_ == null ? com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        } else {
          return troopBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder setTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          troop_ = value;
          onChanged();
        } else {
          troopBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder setTroop(
          com.yorha.proto.StructPlayerPB.TroopPB.Builder builderForValue) {
        if (troopBuilder_ == null) {
          troop_ = builderForValue.build();
          onChanged();
        } else {
          troopBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder mergeTroop(com.yorha.proto.StructPlayerPB.TroopPB value) {
        if (troopBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              troop_ != null &&
              troop_ != com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance()) {
            troop_ =
              com.yorha.proto.StructPlayerPB.TroopPB.newBuilder(troop_).mergeFrom(value).buildPartial();
          } else {
            troop_ = value;
          }
          onChanged();
        } else {
          troopBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public Builder clearTroop() {
        if (troopBuilder_ == null) {
          troop_ = null;
          onChanged();
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPB.Builder getTroopBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getTroopFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      public com.yorha.proto.StructPlayerPB.TroopPBOrBuilder getTroopOrBuilder() {
        if (troopBuilder_ != null) {
          return troopBuilder_.getMessageOrBuilder();
        } else {
          return troop_ == null ?
              com.yorha.proto.StructPlayerPB.TroopPB.getDefaultInstance() : troop_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.TroopPB troop = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder> 
          getTroopFieldBuilder() {
        if (troopBuilder_ == null) {
          troopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayerPB.TroopPB, com.yorha.proto.StructPlayerPB.TroopPB.Builder, com.yorha.proto.StructPlayerPB.TroopPBOrBuilder>(
                  getTroop(),
                  getParentForChildren(),
                  isClean());
          troop_ = null;
        }
        return troopBuilder_;
      }

      private com.yorha.proto.StructPB.MovePB move_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder> moveBuilder_;
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       * @return Whether the move field is set.
       */
      public boolean hasMove() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       * @return The move.
       */
      public com.yorha.proto.StructPB.MovePB getMove() {
        if (moveBuilder_ == null) {
          return move_ == null ? com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
        } else {
          return moveBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder setMove(com.yorha.proto.StructPB.MovePB value) {
        if (moveBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          move_ = value;
          onChanged();
        } else {
          moveBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder setMove(
          com.yorha.proto.StructPB.MovePB.Builder builderForValue) {
        if (moveBuilder_ == null) {
          move_ = builderForValue.build();
          onChanged();
        } else {
          moveBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder mergeMove(com.yorha.proto.StructPB.MovePB value) {
        if (moveBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              move_ != null &&
              move_ != com.yorha.proto.StructPB.MovePB.getDefaultInstance()) {
            move_ =
              com.yorha.proto.StructPB.MovePB.newBuilder(move_).mergeFrom(value).buildPartial();
          } else {
            move_ = value;
          }
          onChanged();
        } else {
          moveBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public Builder clearMove() {
        if (moveBuilder_ == null) {
          move_ = null;
          onChanged();
        } else {
          moveBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public com.yorha.proto.StructPB.MovePB.Builder getMoveBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getMoveFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      public com.yorha.proto.StructPB.MovePBOrBuilder getMoveOrBuilder() {
        if (moveBuilder_ != null) {
          return moveBuilder_.getMessageOrBuilder();
        } else {
          return move_ == null ?
              com.yorha.proto.StructPB.MovePB.getDefaultInstance() : move_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MovePB move = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder> 
          getMoveFieldBuilder() {
        if (moveBuilder_ == null) {
          moveBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.MovePB, com.yorha.proto.StructPB.MovePB.Builder, com.yorha.proto.StructPB.MovePBOrBuilder>(
                  getMove(),
                  getParentForChildren(),
                  isClean());
          move_ = null;
        }
        return moveBuilder_;
      }

      private long ownerId_ ;
      /**
       * <code>optional int64 ownerId = 4;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int64 ownerId = 4;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <code>optional int64 ownerId = 4;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000008;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ownerId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        ownerId_ = 0L;
        onChanged();
        return this;
      }

      private int formationId_ ;
      /**
       * <code>optional int32 formationId = 5;</code>
       * @return Whether the formationId field is set.
       */
      @java.lang.Override
      public boolean hasFormationId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 formationId = 5;</code>
       * @return The formationId.
       */
      @java.lang.Override
      public int getFormationId() {
        return formationId_;
      }
      /**
       * <code>optional int32 formationId = 5;</code>
       * @param value The formationId to set.
       * @return This builder for chaining.
       */
      public Builder setFormationId(int value) {
        bitField0_ |= 0x00000010;
        formationId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 formationId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearFormationId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        formationId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructBattlePB.BattlePB battle_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> battleBuilder_;
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       * @return Whether the battle field is set.
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       * @return The battle.
       */
      public com.yorha.proto.StructBattlePB.BattlePB getBattle() {
        if (battleBuilder_ == null) {
          return battle_ == null ? com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        } else {
          return battleBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       */
      public Builder setBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battle_ = value;
          onChanged();
        } else {
          battleBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       */
      public Builder setBattle(
          com.yorha.proto.StructBattlePB.BattlePB.Builder builderForValue) {
        if (battleBuilder_ == null) {
          battle_ = builderForValue.build();
          onChanged();
        } else {
          battleBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       */
      public Builder mergeBattle(com.yorha.proto.StructBattlePB.BattlePB value) {
        if (battleBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
              battle_ != null &&
              battle_ != com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance()) {
            battle_ =
              com.yorha.proto.StructBattlePB.BattlePB.newBuilder(battle_).mergeFrom(value).buildPartial();
          } else {
            battle_ = value;
          }
          onChanged();
        } else {
          battleBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       */
      public Builder clearBattle() {
        if (battleBuilder_ == null) {
          battle_ = null;
          onChanged();
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePB.Builder getBattleBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getBattleFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       */
      public com.yorha.proto.StructBattlePB.BattlePBOrBuilder getBattleOrBuilder() {
        if (battleBuilder_ != null) {
          return battleBuilder_.getMessageOrBuilder();
        } else {
          return battle_ == null ?
              com.yorha.proto.StructBattlePB.BattlePB.getDefaultInstance() : battle_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.BattlePB battle = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder> 
          getBattleFieldBuilder() {
        if (battleBuilder_ == null) {
          battleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.BattlePB, com.yorha.proto.StructBattlePB.BattlePB.Builder, com.yorha.proto.StructBattlePB.BattlePBOrBuilder>(
                  getBattle(),
                  getParentForChildren(),
                  isClean());
          battle_ = null;
        }
        return battleBuilder_;
      }

      private com.yorha.proto.StructBattlePB.Int32BuffMapPB buff_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.Int32BuffMapPB, com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder, com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder> buffBuilder_;
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       * @return Whether the buff field is set.
       */
      public boolean hasBuff() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       * @return The buff.
       */
      public com.yorha.proto.StructBattlePB.Int32BuffMapPB getBuff() {
        if (buffBuilder_ == null) {
          return buff_ == null ? com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
        } else {
          return buffBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       */
      public Builder setBuff(com.yorha.proto.StructBattlePB.Int32BuffMapPB value) {
        if (buffBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buff_ = value;
          onChanged();
        } else {
          buffBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       */
      public Builder setBuff(
          com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder builderForValue) {
        if (buffBuilder_ == null) {
          buff_ = builderForValue.build();
          onChanged();
        } else {
          buffBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       */
      public Builder mergeBuff(com.yorha.proto.StructBattlePB.Int32BuffMapPB value) {
        if (buffBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              buff_ != null &&
              buff_ != com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance()) {
            buff_ =
              com.yorha.proto.StructBattlePB.Int32BuffMapPB.newBuilder(buff_).mergeFrom(value).buildPartial();
          } else {
            buff_ = value;
          }
          onChanged();
        } else {
          buffBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       */
      public Builder clearBuff() {
        if (buffBuilder_ == null) {
          buff_ = null;
          onChanged();
        } else {
          buffBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder getBuffBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getBuffFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       */
      public com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder getBuffOrBuilder() {
        if (buffBuilder_ != null) {
          return buffBuilder_.getMessageOrBuilder();
        } else {
          return buff_ == null ?
              com.yorha.proto.StructBattlePB.Int32BuffMapPB.getDefaultInstance() : buff_;
        }
      }
      /**
       * <pre>
       * buf列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32BuffMapPB buff = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattlePB.Int32BuffMapPB, com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder, com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder> 
          getBuffFieldBuilder() {
        if (buffBuilder_ == null) {
          buffBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattlePB.Int32BuffMapPB, com.yorha.proto.StructBattlePB.Int32BuffMapPB.Builder, com.yorha.proto.StructBattlePB.Int32BuffMapPBOrBuilder>(
                  getBuff(),
                  getParentForChildren(),
                  isClean());
          buff_ = null;
        }
        return buffBuilder_;
      }

      private int camp_ = 0;
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 8;</code>
       * @return Whether the camp field is set.
       */
      @java.lang.Override public boolean hasCamp() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 8;</code>
       * @return The camp.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Camp getCamp() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Camp result = com.yorha.proto.CommonEnum.Camp.valueOf(camp_);
        return result == null ? com.yorha.proto.CommonEnum.Camp.C_NONE : result;
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 8;</code>
       * @param value The camp to set.
       * @return This builder for chaining.
       */
      public Builder setCamp(com.yorha.proto.CommonEnum.Camp value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000080;
        camp_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 阵营
       * </pre>
       *
       * <code>optional .com.yorha.proto.Camp camp = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearCamp() {
        bitField0_ = (bitField0_ & ~0x00000080);
        camp_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object clanSname_ = "";
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 9;</code>
       * @return Whether the clanSname field is set.
       */
      public boolean hasClanSname() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 9;</code>
       * @return The clanSname.
       */
      public java.lang.String getClanSname() {
        java.lang.Object ref = clanSname_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSname_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 9;</code>
       * @return The bytes for clanSname.
       */
      public com.google.protobuf.ByteString
          getClanSnameBytes() {
        java.lang.Object ref = clanSname_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSname_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 9;</code>
       * @param value The clanSname to set.
       * @return This builder for chaining.
       */
      public Builder setClanSname(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        clanSname_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSname() {
        bitField0_ = (bitField0_ & ~0x00000100);
        clanSname_ = getDefaultInstance().getClanSname();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟简称
       * </pre>
       *
       * <code>optional string clanSname = 9;</code>
       * @param value The bytes for clanSname to set.
       * @return This builder for chaining.
       */
      public Builder setClanSnameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        clanSname_ = value;
        onChanged();
        return this;
      }

      private long enterStateTs_ ;
      /**
       * <pre>
       * 状态进入时间
       * </pre>
       *
       * <code>optional int64 enterStateTs = 10;</code>
       * @return Whether the enterStateTs field is set.
       */
      @java.lang.Override
      public boolean hasEnterStateTs() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 状态进入时间
       * </pre>
       *
       * <code>optional int64 enterStateTs = 10;</code>
       * @return The enterStateTs.
       */
      @java.lang.Override
      public long getEnterStateTs() {
        return enterStateTs_;
      }
      /**
       * <pre>
       * 状态进入时间
       * </pre>
       *
       * <code>optional int64 enterStateTs = 10;</code>
       * @param value The enterStateTs to set.
       * @return This builder for chaining.
       */
      public Builder setEnterStateTs(long value) {
        bitField0_ |= 0x00000200;
        enterStateTs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态进入时间
       * </pre>
       *
       * <code>optional int64 enterStateTs = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnterStateTs() {
        bitField0_ = (bitField0_ & ~0x00000200);
        enterStateTs_ = 0L;
        onChanged();
        return this;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 11;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 11;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 11;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000400;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 联盟id
       * </pre>
       *
       * <code>optional int64 clanId = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000400);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private long hpMax_ ;
      /**
       * <pre>
       * 集结所用血量分母
       * </pre>
       *
       * <code>optional int64 hpMax = 12;</code>
       * @return Whether the hpMax field is set.
       */
      @java.lang.Override
      public boolean hasHpMax() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 集结所用血量分母
       * </pre>
       *
       * <code>optional int64 hpMax = 12;</code>
       * @return The hpMax.
       */
      @java.lang.Override
      public long getHpMax() {
        return hpMax_;
      }
      /**
       * <pre>
       * 集结所用血量分母
       * </pre>
       *
       * <code>optional int64 hpMax = 12;</code>
       * @param value The hpMax to set.
       * @return This builder for chaining.
       */
      public Builder setHpMax(long value) {
        bitField0_ |= 0x00000800;
        hpMax_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 集结所用血量分母
       * </pre>
       *
       * <code>optional int64 hpMax = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearHpMax() {
        bitField0_ = (bitField0_ & ~0x00000800);
        hpMax_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PickUpInfoPB pickUpInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PickUpInfoPB, com.yorha.proto.StructPB.PickUpInfoPB.Builder, com.yorha.proto.StructPB.PickUpInfoPBOrBuilder> pickUpInfoBuilder_;
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       * @return Whether the pickUpInfo field is set.
       */
      public boolean hasPickUpInfo() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       * @return The pickUpInfo.
       */
      public com.yorha.proto.StructPB.PickUpInfoPB getPickUpInfo() {
        if (pickUpInfoBuilder_ == null) {
          return pickUpInfo_ == null ? com.yorha.proto.StructPB.PickUpInfoPB.getDefaultInstance() : pickUpInfo_;
        } else {
          return pickUpInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       */
      public Builder setPickUpInfo(com.yorha.proto.StructPB.PickUpInfoPB value) {
        if (pickUpInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pickUpInfo_ = value;
          onChanged();
        } else {
          pickUpInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       */
      public Builder setPickUpInfo(
          com.yorha.proto.StructPB.PickUpInfoPB.Builder builderForValue) {
        if (pickUpInfoBuilder_ == null) {
          pickUpInfo_ = builderForValue.build();
          onChanged();
        } else {
          pickUpInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       */
      public Builder mergePickUpInfo(com.yorha.proto.StructPB.PickUpInfoPB value) {
        if (pickUpInfoBuilder_ == null) {
          if (((bitField0_ & 0x00001000) != 0) &&
              pickUpInfo_ != null &&
              pickUpInfo_ != com.yorha.proto.StructPB.PickUpInfoPB.getDefaultInstance()) {
            pickUpInfo_ =
              com.yorha.proto.StructPB.PickUpInfoPB.newBuilder(pickUpInfo_).mergeFrom(value).buildPartial();
          } else {
            pickUpInfo_ = value;
          }
          onChanged();
        } else {
          pickUpInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00001000;
        return this;
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       */
      public Builder clearPickUpInfo() {
        if (pickUpInfoBuilder_ == null) {
          pickUpInfo_ = null;
          onChanged();
        } else {
          pickUpInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00001000);
        return this;
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       */
      public com.yorha.proto.StructPB.PickUpInfoPB.Builder getPickUpInfoBuilder() {
        bitField0_ |= 0x00001000;
        onChanged();
        return getPickUpInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       */
      public com.yorha.proto.StructPB.PickUpInfoPBOrBuilder getPickUpInfoOrBuilder() {
        if (pickUpInfoBuilder_ != null) {
          return pickUpInfoBuilder_.getMessageOrBuilder();
        } else {
          return pickUpInfo_ == null ?
              com.yorha.proto.StructPB.PickUpInfoPB.getDefaultInstance() : pickUpInfo_;
        }
      }
      /**
       * <pre>
       * 拾取信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.PickUpInfoPB pickUpInfo = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PickUpInfoPB, com.yorha.proto.StructPB.PickUpInfoPB.Builder, com.yorha.proto.StructPB.PickUpInfoPBOrBuilder> 
          getPickUpInfoFieldBuilder() {
        if (pickUpInfoBuilder_ == null) {
          pickUpInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PickUpInfoPB, com.yorha.proto.StructPB.PickUpInfoPB.Builder, com.yorha.proto.StructPB.PickUpInfoPBOrBuilder>(
                  getPickUpInfo(),
                  getParentForChildren(),
                  isClean());
          pickUpInfo_ = null;
        }
        return pickUpInfoBuilder_;
      }

      private long attachId_ ;
      /**
       * <pre>
       * 依附的对象id
       * </pre>
       *
       * <code>optional int64 attachId = 14;</code>
       * @return Whether the attachId field is set.
       */
      @java.lang.Override
      public boolean hasAttachId() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 依附的对象id
       * </pre>
       *
       * <code>optional int64 attachId = 14;</code>
       * @return The attachId.
       */
      @java.lang.Override
      public long getAttachId() {
        return attachId_;
      }
      /**
       * <pre>
       * 依附的对象id
       * </pre>
       *
       * <code>optional int64 attachId = 14;</code>
       * @param value The attachId to set.
       * @return This builder for chaining.
       */
      public Builder setAttachId(long value) {
        bitField0_ |= 0x00002000;
        attachId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 依附的对象id
       * </pre>
       *
       * <code>optional int64 attachId = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttachId() {
        bitField0_ = (bitField0_ & ~0x00002000);
        attachId_ = 0L;
        onChanged();
        return this;
      }

      private int attachState_ = 0;
      /**
       * <pre>
       * 依附状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
       * @return Whether the attachState field is set.
       */
      @java.lang.Override public boolean hasAttachState() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 依附状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
       * @return The attachState.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.AttachState getAttachState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.AttachState result = com.yorha.proto.CommonEnum.AttachState.valueOf(attachState_);
        return result == null ? com.yorha.proto.CommonEnum.AttachState.AAS_NONE : result;
      }
      /**
       * <pre>
       * 依附状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
       * @param value The attachState to set.
       * @return This builder for chaining.
       */
      public Builder setAttachState(com.yorha.proto.CommonEnum.AttachState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00004000;
        attachState_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 依附状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.AttachState attachState = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttachState() {
        bitField0_ = (bitField0_ & ~0x00004000);
        attachState_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.SceneTransportPlanePB transportPlane_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.SceneTransportPlanePB, com.yorha.proto.StructPB.SceneTransportPlanePB.Builder, com.yorha.proto.StructPB.SceneTransportPlanePBOrBuilder> transportPlaneBuilder_;
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       * @return Whether the transportPlane field is set.
       */
      public boolean hasTransportPlane() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       * @return The transportPlane.
       */
      public com.yorha.proto.StructPB.SceneTransportPlanePB getTransportPlane() {
        if (transportPlaneBuilder_ == null) {
          return transportPlane_ == null ? com.yorha.proto.StructPB.SceneTransportPlanePB.getDefaultInstance() : transportPlane_;
        } else {
          return transportPlaneBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       */
      public Builder setTransportPlane(com.yorha.proto.StructPB.SceneTransportPlanePB value) {
        if (transportPlaneBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          transportPlane_ = value;
          onChanged();
        } else {
          transportPlaneBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       */
      public Builder setTransportPlane(
          com.yorha.proto.StructPB.SceneTransportPlanePB.Builder builderForValue) {
        if (transportPlaneBuilder_ == null) {
          transportPlane_ = builderForValue.build();
          onChanged();
        } else {
          transportPlaneBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       */
      public Builder mergeTransportPlane(com.yorha.proto.StructPB.SceneTransportPlanePB value) {
        if (transportPlaneBuilder_ == null) {
          if (((bitField0_ & 0x00008000) != 0) &&
              transportPlane_ != null &&
              transportPlane_ != com.yorha.proto.StructPB.SceneTransportPlanePB.getDefaultInstance()) {
            transportPlane_ =
              com.yorha.proto.StructPB.SceneTransportPlanePB.newBuilder(transportPlane_).mergeFrom(value).buildPartial();
          } else {
            transportPlane_ = value;
          }
          onChanged();
        } else {
          transportPlaneBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       */
      public Builder clearTransportPlane() {
        if (transportPlaneBuilder_ == null) {
          transportPlane_ = null;
          onChanged();
        } else {
          transportPlaneBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00008000);
        return this;
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       */
      public com.yorha.proto.StructPB.SceneTransportPlanePB.Builder getTransportPlaneBuilder() {
        bitField0_ |= 0x00008000;
        onChanged();
        return getTransportPlaneFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       */
      public com.yorha.proto.StructPB.SceneTransportPlanePBOrBuilder getTransportPlaneOrBuilder() {
        if (transportPlaneBuilder_ != null) {
          return transportPlaneBuilder_.getMessageOrBuilder();
        } else {
          return transportPlane_ == null ?
              com.yorha.proto.StructPB.SceneTransportPlanePB.getDefaultInstance() : transportPlane_;
        }
      }
      /**
       * <pre>
       * 运输机
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneTransportPlanePB transportPlane = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.SceneTransportPlanePB, com.yorha.proto.StructPB.SceneTransportPlanePB.Builder, com.yorha.proto.StructPB.SceneTransportPlanePBOrBuilder> 
          getTransportPlaneFieldBuilder() {
        if (transportPlaneBuilder_ == null) {
          transportPlaneBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.SceneTransportPlanePB, com.yorha.proto.StructPB.SceneTransportPlanePB.Builder, com.yorha.proto.StructPB.SceneTransportPlanePBOrBuilder>(
                  getTransportPlane(),
                  getParentForChildren(),
                  isClean());
          transportPlane_ = null;
        }
        return transportPlaneBuilder_;
      }

      private com.yorha.proto.StructPB.PlayerCardHeadPB cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> cardHeadBuilder_;
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       */
      public Builder setCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00010000;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.StructPB.PlayerCardHeadPB.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00010000;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.StructPB.PlayerCardHeadPB value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00010000) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.StructPB.PlayerCardHeadPB.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00010000;
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00010000);
        return this;
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPB.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00010000;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       */
      public com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.StructPB.PlayerCardHeadPB.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <pre>
       * 玩家铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHeadPB cardHead = 18;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PlayerCardHeadPB, com.yorha.proto.StructPB.PlayerCardHeadPB.Builder, com.yorha.proto.StructPB.PlayerCardHeadPBOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }

      private com.yorha.proto.StructClanPB.ClanFlagInfoPB clanFlag_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClanPB.ClanFlagInfoPB, com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder, com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder> clanFlagBuilder_;
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       * @return Whether the clanFlag field is set.
       */
      public boolean hasClanFlag() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       * @return The clanFlag.
       */
      public com.yorha.proto.StructClanPB.ClanFlagInfoPB getClanFlag() {
        if (clanFlagBuilder_ == null) {
          return clanFlag_ == null ? com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : clanFlag_;
        } else {
          return clanFlagBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       */
      public Builder setClanFlag(com.yorha.proto.StructClanPB.ClanFlagInfoPB value) {
        if (clanFlagBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          clanFlag_ = value;
          onChanged();
        } else {
          clanFlagBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00020000;
        return this;
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       */
      public Builder setClanFlag(
          com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder builderForValue) {
        if (clanFlagBuilder_ == null) {
          clanFlag_ = builderForValue.build();
          onChanged();
        } else {
          clanFlagBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00020000;
        return this;
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       */
      public Builder mergeClanFlag(com.yorha.proto.StructClanPB.ClanFlagInfoPB value) {
        if (clanFlagBuilder_ == null) {
          if (((bitField0_ & 0x00020000) != 0) &&
              clanFlag_ != null &&
              clanFlag_ != com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance()) {
            clanFlag_ =
              com.yorha.proto.StructClanPB.ClanFlagInfoPB.newBuilder(clanFlag_).mergeFrom(value).buildPartial();
          } else {
            clanFlag_ = value;
          }
          onChanged();
        } else {
          clanFlagBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00020000;
        return this;
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       */
      public Builder clearClanFlag() {
        if (clanFlagBuilder_ == null) {
          clanFlag_ = null;
          onChanged();
        } else {
          clanFlagBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00020000);
        return this;
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       */
      public com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder getClanFlagBuilder() {
        bitField0_ |= 0x00020000;
        onChanged();
        return getClanFlagFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       */
      public com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder getClanFlagOrBuilder() {
        if (clanFlagBuilder_ != null) {
          return clanFlagBuilder_.getMessageOrBuilder();
        } else {
          return clanFlag_ == null ?
              com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : clanFlag_;
        }
      }
      /**
       * <pre>
       * 联盟旗帜 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB clanFlag = 19;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClanPB.ClanFlagInfoPB, com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder, com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder> 
          getClanFlagFieldBuilder() {
        if (clanFlagBuilder_ == null) {
          clanFlagBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructClanPB.ClanFlagInfoPB, com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder, com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder>(
                  getClanFlag(),
                  getParentForChildren(),
                  isClean());
          clanFlag_ = null;
        }
        return clanFlagBuilder_;
      }

      private com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB arrow_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> arrowBuilder_;
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       * @return Whether the arrow field is set.
       */
      public boolean hasArrow() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       * @return The arrow.
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB getArrow() {
        if (arrowBuilder_ == null) {
          return arrow_ == null ? com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        } else {
          return arrowBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       */
      public Builder setArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          arrow_ = value;
          onChanged();
        } else {
          arrowBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00040000;
        return this;
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       */
      public Builder setArrow(
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder builderForValue) {
        if (arrowBuilder_ == null) {
          arrow_ = builderForValue.build();
          onChanged();
        } else {
          arrowBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00040000;
        return this;
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       */
      public Builder mergeArrow(com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB value) {
        if (arrowBuilder_ == null) {
          if (((bitField0_ & 0x00040000) != 0) &&
              arrow_ != null &&
              arrow_ != com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance()) {
            arrow_ =
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.newBuilder(arrow_).mergeFrom(value).buildPartial();
          } else {
            arrow_ = value;
          }
          onChanged();
        } else {
          arrowBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00040000;
        return this;
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       */
      public Builder clearArrow() {
        if (arrowBuilder_ == null) {
          arrow_ = null;
          onChanged();
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00040000);
        return this;
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder getArrowBuilder() {
        bitField0_ |= 0x00040000;
        onChanged();
        return getArrowFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       */
      public com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder getArrowOrBuilder() {
        if (arrowBuilder_ != null) {
          return arrowBuilder_.getMessageOrBuilder();
        } else {
          return arrow_ == null ?
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.getDefaultInstance() : arrow_;
        }
      }
      /**
       * <pre>
       * 小箭头 集结部队才有
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMapPB arrow = 20;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder> 
          getArrowFieldBuilder() {
        if (arrowBuilder_ == null) {
          arrowBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPB.Builder, com.yorha.proto.StructPB.Int64ArmyArrowItemMapPBOrBuilder>(
                  getArrow(),
                  getParentForChildren(),
                  isClean());
          arrow_ = null;
        }
        return arrowBuilder_;
      }

      private int modelRadius_ ;
      /**
       * <pre>
       * 模型圈半径
       * </pre>
       *
       * <code>optional int32 modelRadius = 21;</code>
       * @return Whether the modelRadius field is set.
       */
      @java.lang.Override
      public boolean hasModelRadius() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 模型圈半径
       * </pre>
       *
       * <code>optional int32 modelRadius = 21;</code>
       * @return The modelRadius.
       */
      @java.lang.Override
      public int getModelRadius() {
        return modelRadius_;
      }
      /**
       * <pre>
       * 模型圈半径
       * </pre>
       *
       * <code>optional int32 modelRadius = 21;</code>
       * @param value The modelRadius to set.
       * @return This builder for chaining.
       */
      public Builder setModelRadius(int value) {
        bitField0_ |= 0x00080000;
        modelRadius_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模型圈半径
       * </pre>
       *
       * <code>optional int32 modelRadius = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearModelRadius() {
        bitField0_ = (bitField0_ & ~0x00080000);
        modelRadius_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.ExpressionPB expression_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> expressionBuilder_;
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       * @return Whether the expression field is set.
       */
      public boolean hasExpression() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       * @return The expression.
       */
      public com.yorha.proto.StructPB.ExpressionPB getExpression() {
        if (expressionBuilder_ == null) {
          return expression_ == null ? com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        } else {
          return expressionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder setExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          expression_ = value;
          onChanged();
        } else {
          expressionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00100000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder setExpression(
          com.yorha.proto.StructPB.ExpressionPB.Builder builderForValue) {
        if (expressionBuilder_ == null) {
          expression_ = builderForValue.build();
          onChanged();
        } else {
          expressionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00100000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder mergeExpression(com.yorha.proto.StructPB.ExpressionPB value) {
        if (expressionBuilder_ == null) {
          if (((bitField0_ & 0x00100000) != 0) &&
              expression_ != null &&
              expression_ != com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance()) {
            expression_ =
              com.yorha.proto.StructPB.ExpressionPB.newBuilder(expression_).mergeFrom(value).buildPartial();
          } else {
            expression_ = value;
          }
          onChanged();
        } else {
          expressionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00100000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public Builder clearExpression() {
        if (expressionBuilder_ == null) {
          expression_ = null;
          onChanged();
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00100000);
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPB.Builder getExpressionBuilder() {
        bitField0_ |= 0x00100000;
        onChanged();
        return getExpressionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      public com.yorha.proto.StructPB.ExpressionPBOrBuilder getExpressionOrBuilder() {
        if (expressionBuilder_ != null) {
          return expressionBuilder_.getMessageOrBuilder();
        } else {
          return expression_ == null ?
              com.yorha.proto.StructPB.ExpressionPB.getDefaultInstance() : expression_;
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.ExpressionPB expression = 22;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder> 
          getExpressionFieldBuilder() {
        if (expressionBuilder_ == null) {
          expressionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ExpressionPB, com.yorha.proto.StructPB.ExpressionPB.Builder, com.yorha.proto.StructPB.ExpressionPBOrBuilder>(
                  getExpression(),
                  getParentForChildren(),
                  isClean());
          expression_ = null;
        }
        return expressionBuilder_;
      }

      private int rallyRole_ = 0;
      /**
       * <pre>
       * 集结角色
       * </pre>
       *
       * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
       * @return Whether the rallyRole field is set.
       */
      @java.lang.Override public boolean hasRallyRole() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 集结角色
       * </pre>
       *
       * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
       * @return The rallyRole.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.RallyArmyRoleType getRallyRole() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.RallyArmyRoleType result = com.yorha.proto.CommonEnum.RallyArmyRoleType.valueOf(rallyRole_);
        return result == null ? com.yorha.proto.CommonEnum.RallyArmyRoleType.RART_Single : result;
      }
      /**
       * <pre>
       * 集结角色
       * </pre>
       *
       * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
       * @param value The rallyRole to set.
       * @return This builder for chaining.
       */
      public Builder setRallyRole(com.yorha.proto.CommonEnum.RallyArmyRoleType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00200000;
        rallyRole_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 集结角色
       * </pre>
       *
       * <code>optional .com.yorha.proto.RallyArmyRoleType rallyRole = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearRallyRole() {
        bitField0_ = (bitField0_ & ~0x00200000);
        rallyRole_ = 0;
        onChanged();
        return this;
      }

      private long curRallyId_ ;
      /**
       * <pre>
       * 当前所处的集结id 0代表未参与集结
       * </pre>
       *
       * <code>optional int64 curRallyId = 25;</code>
       * @return Whether the curRallyId field is set.
       */
      @java.lang.Override
      public boolean hasCurRallyId() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 当前所处的集结id 0代表未参与集结
       * </pre>
       *
       * <code>optional int64 curRallyId = 25;</code>
       * @return The curRallyId.
       */
      @java.lang.Override
      public long getCurRallyId() {
        return curRallyId_;
      }
      /**
       * <pre>
       * 当前所处的集结id 0代表未参与集结
       * </pre>
       *
       * <code>optional int64 curRallyId = 25;</code>
       * @param value The curRallyId to set.
       * @return This builder for chaining.
       */
      public Builder setCurRallyId(long value) {
        bitField0_ |= 0x00400000;
        curRallyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前所处的集结id 0代表未参与集结
       * </pre>
       *
       * <code>optional int64 curRallyId = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurRallyId() {
        bitField0_ = (bitField0_ & ~0x00400000);
        curRallyId_ = 0L;
        onChanged();
        return this;
      }

      private long curAssistTargetId_ ;
      /**
       * <pre>
       * 当前所援助的城池 0代表未参与援助
       * </pre>
       *
       * <code>optional int64 curAssistTargetId = 26;</code>
       * @return Whether the curAssistTargetId field is set.
       */
      @java.lang.Override
      public boolean hasCurAssistTargetId() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 当前所援助的城池 0代表未参与援助
       * </pre>
       *
       * <code>optional int64 curAssistTargetId = 26;</code>
       * @return The curAssistTargetId.
       */
      @java.lang.Override
      public long getCurAssistTargetId() {
        return curAssistTargetId_;
      }
      /**
       * <pre>
       * 当前所援助的城池 0代表未参与援助
       * </pre>
       *
       * <code>optional int64 curAssistTargetId = 26;</code>
       * @param value The curAssistTargetId to set.
       * @return This builder for chaining.
       */
      public Builder setCurAssistTargetId(long value) {
        bitField0_ |= 0x00800000;
        curAssistTargetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前所援助的城池 0代表未参与援助
       * </pre>
       *
       * <code>optional int64 curAssistTargetId = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurAssistTargetId() {
        bitField0_ = (bitField0_ & ~0x00800000);
        curAssistTargetId_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 部队所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 27;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * 部队所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 27;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 部队所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 27;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x01000000;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 部队所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x01000000);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ArmyEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ArmyEntityPB)
    private static final com.yorha.proto.ArmyPB.ArmyEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.ArmyPB.ArmyEntityPB();
    }

    public static com.yorha.proto.ArmyPB.ArmyEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ArmyEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<ArmyEntityPB>() {
      @java.lang.Override
      public ArmyEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ArmyEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ArmyEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ArmyEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ArmyResourcesModelPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ArmyResourcesModelPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 掠夺的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
     * @return Whether the plunder field is set.
     */
    boolean hasPlunder();
    /**
     * <pre>
     * 掠夺的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
     * @return The plunder.
     */
    com.yorha.proto.StructPB.Int32CurrencyMapPB getPlunder();
    /**
     * <pre>
     * 掠夺的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
     */
    com.yorha.proto.StructPB.Int32CurrencyMapPBOrBuilder getPlunderOrBuilder();

    /**
     * <pre>
     * 采集的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
     * @return Whether the collect field is set.
     */
    boolean hasCollect();
    /**
     * <pre>
     * 采集的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
     * @return The collect.
     */
    com.yorha.proto.ArmyPB.ArmyCollectResourceListPB getCollect();
    /**
     * <pre>
     * 采集的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
     */
    com.yorha.proto.ArmyPB.ArmyCollectResourceListPBOrBuilder getCollectOrBuilder();

    /**
     * <pre>
     * 采集完的次数
     * </pre>
     *
     * <code>optional int32 outTimes = 3;</code>
     * @return Whether the outTimes field is set.
     */
    boolean hasOutTimes();
    /**
     * <pre>
     * 采集完的次数
     * </pre>
     *
     * <code>optional int32 outTimes = 3;</code>
     * @return The outTimes.
     */
    int getOutTimes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ArmyResourcesModelPB}
   */
  public static final class ArmyResourcesModelPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ArmyResourcesModelPB)
      ArmyResourcesModelPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ArmyResourcesModelPB.newBuilder() to construct.
    private ArmyResourcesModelPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ArmyResourcesModelPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ArmyResourcesModelPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ArmyResourcesModelPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.Int32CurrencyMapPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = plunder_.toBuilder();
              }
              plunder_ = input.readMessage(com.yorha.proto.StructPB.Int32CurrencyMapPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(plunder_);
                plunder_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = collect_.toBuilder();
              }
              collect_ = input.readMessage(com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(collect_);
                collect_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              outTimes_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyResourcesModelPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyResourcesModelPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.ArmyPB.ArmyResourcesModelPB.class, com.yorha.proto.ArmyPB.ArmyResourcesModelPB.Builder.class);
    }

    private int bitField0_;
    public static final int PLUNDER_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.Int32CurrencyMapPB plunder_;
    /**
     * <pre>
     * 掠夺的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
     * @return Whether the plunder field is set.
     */
    @java.lang.Override
    public boolean hasPlunder() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 掠夺的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
     * @return The plunder.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int32CurrencyMapPB getPlunder() {
      return plunder_ == null ? com.yorha.proto.StructPB.Int32CurrencyMapPB.getDefaultInstance() : plunder_;
    }
    /**
     * <pre>
     * 掠夺的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.Int32CurrencyMapPBOrBuilder getPlunderOrBuilder() {
      return plunder_ == null ? com.yorha.proto.StructPB.Int32CurrencyMapPB.getDefaultInstance() : plunder_;
    }

    public static final int COLLECT_FIELD_NUMBER = 2;
    private com.yorha.proto.ArmyPB.ArmyCollectResourceListPB collect_;
    /**
     * <pre>
     * 采集的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
     * @return Whether the collect field is set.
     */
    @java.lang.Override
    public boolean hasCollect() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 采集的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
     * @return The collect.
     */
    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyCollectResourceListPB getCollect() {
      return collect_ == null ? com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.getDefaultInstance() : collect_;
    }
    /**
     * <pre>
     * 采集的资源
     * </pre>
     *
     * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyCollectResourceListPBOrBuilder getCollectOrBuilder() {
      return collect_ == null ? com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.getDefaultInstance() : collect_;
    }

    public static final int OUTTIMES_FIELD_NUMBER = 3;
    private int outTimes_;
    /**
     * <pre>
     * 采集完的次数
     * </pre>
     *
     * <code>optional int32 outTimes = 3;</code>
     * @return Whether the outTimes field is set.
     */
    @java.lang.Override
    public boolean hasOutTimes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 采集完的次数
     * </pre>
     *
     * <code>optional int32 outTimes = 3;</code>
     * @return The outTimes.
     */
    @java.lang.Override
    public int getOutTimes() {
      return outTimes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPlunder());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getCollect());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, outTimes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPlunder());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCollect());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, outTimes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.ArmyPB.ArmyResourcesModelPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.ArmyPB.ArmyResourcesModelPB other = (com.yorha.proto.ArmyPB.ArmyResourcesModelPB) obj;

      if (hasPlunder() != other.hasPlunder()) return false;
      if (hasPlunder()) {
        if (!getPlunder()
            .equals(other.getPlunder())) return false;
      }
      if (hasCollect() != other.hasCollect()) return false;
      if (hasCollect()) {
        if (!getCollect()
            .equals(other.getCollect())) return false;
      }
      if (hasOutTimes() != other.hasOutTimes()) return false;
      if (hasOutTimes()) {
        if (getOutTimes()
            != other.getOutTimes()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlunder()) {
        hash = (37 * hash) + PLUNDER_FIELD_NUMBER;
        hash = (53 * hash) + getPlunder().hashCode();
      }
      if (hasCollect()) {
        hash = (37 * hash) + COLLECT_FIELD_NUMBER;
        hash = (53 * hash) + getCollect().hashCode();
      }
      if (hasOutTimes()) {
        hash = (37 * hash) + OUTTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getOutTimes();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.ArmyPB.ArmyResourcesModelPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ArmyResourcesModelPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ArmyResourcesModelPB)
        com.yorha.proto.ArmyPB.ArmyResourcesModelPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyResourcesModelPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyResourcesModelPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.ArmyPB.ArmyResourcesModelPB.class, com.yorha.proto.ArmyPB.ArmyResourcesModelPB.Builder.class);
      }

      // Construct using com.yorha.proto.ArmyPB.ArmyResourcesModelPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPlunderFieldBuilder();
          getCollectFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (plunderBuilder_ == null) {
          plunder_ = null;
        } else {
          plunderBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (collectBuilder_ == null) {
          collect_ = null;
        } else {
          collectBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        outTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyResourcesModelPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyResourcesModelPB getDefaultInstanceForType() {
        return com.yorha.proto.ArmyPB.ArmyResourcesModelPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyResourcesModelPB build() {
        com.yorha.proto.ArmyPB.ArmyResourcesModelPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyResourcesModelPB buildPartial() {
        com.yorha.proto.ArmyPB.ArmyResourcesModelPB result = new com.yorha.proto.ArmyPB.ArmyResourcesModelPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (plunderBuilder_ == null) {
            result.plunder_ = plunder_;
          } else {
            result.plunder_ = plunderBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (collectBuilder_ == null) {
            result.collect_ = collect_;
          } else {
            result.collect_ = collectBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.outTimes_ = outTimes_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.ArmyPB.ArmyResourcesModelPB) {
          return mergeFrom((com.yorha.proto.ArmyPB.ArmyResourcesModelPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.ArmyPB.ArmyResourcesModelPB other) {
        if (other == com.yorha.proto.ArmyPB.ArmyResourcesModelPB.getDefaultInstance()) return this;
        if (other.hasPlunder()) {
          mergePlunder(other.getPlunder());
        }
        if (other.hasCollect()) {
          mergeCollect(other.getCollect());
        }
        if (other.hasOutTimes()) {
          setOutTimes(other.getOutTimes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.ArmyPB.ArmyResourcesModelPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.ArmyPB.ArmyResourcesModelPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.Int32CurrencyMapPB plunder_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int32CurrencyMapPB, com.yorha.proto.StructPB.Int32CurrencyMapPB.Builder, com.yorha.proto.StructPB.Int32CurrencyMapPBOrBuilder> plunderBuilder_;
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       * @return Whether the plunder field is set.
       */
      public boolean hasPlunder() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       * @return The plunder.
       */
      public com.yorha.proto.StructPB.Int32CurrencyMapPB getPlunder() {
        if (plunderBuilder_ == null) {
          return plunder_ == null ? com.yorha.proto.StructPB.Int32CurrencyMapPB.getDefaultInstance() : plunder_;
        } else {
          return plunderBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       */
      public Builder setPlunder(com.yorha.proto.StructPB.Int32CurrencyMapPB value) {
        if (plunderBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          plunder_ = value;
          onChanged();
        } else {
          plunderBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       */
      public Builder setPlunder(
          com.yorha.proto.StructPB.Int32CurrencyMapPB.Builder builderForValue) {
        if (plunderBuilder_ == null) {
          plunder_ = builderForValue.build();
          onChanged();
        } else {
          plunderBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       */
      public Builder mergePlunder(com.yorha.proto.StructPB.Int32CurrencyMapPB value) {
        if (plunderBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              plunder_ != null &&
              plunder_ != com.yorha.proto.StructPB.Int32CurrencyMapPB.getDefaultInstance()) {
            plunder_ =
              com.yorha.proto.StructPB.Int32CurrencyMapPB.newBuilder(plunder_).mergeFrom(value).buildPartial();
          } else {
            plunder_ = value;
          }
          onChanged();
        } else {
          plunderBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       */
      public Builder clearPlunder() {
        if (plunderBuilder_ == null) {
          plunder_ = null;
          onChanged();
        } else {
          plunderBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       */
      public com.yorha.proto.StructPB.Int32CurrencyMapPB.Builder getPlunderBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPlunderFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       */
      public com.yorha.proto.StructPB.Int32CurrencyMapPBOrBuilder getPlunderOrBuilder() {
        if (plunderBuilder_ != null) {
          return plunderBuilder_.getMessageOrBuilder();
        } else {
          return plunder_ == null ?
              com.yorha.proto.StructPB.Int32CurrencyMapPB.getDefaultInstance() : plunder_;
        }
      }
      /**
       * <pre>
       * 掠夺的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32CurrencyMapPB plunder = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.Int32CurrencyMapPB, com.yorha.proto.StructPB.Int32CurrencyMapPB.Builder, com.yorha.proto.StructPB.Int32CurrencyMapPBOrBuilder> 
          getPlunderFieldBuilder() {
        if (plunderBuilder_ == null) {
          plunderBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.Int32CurrencyMapPB, com.yorha.proto.StructPB.Int32CurrencyMapPB.Builder, com.yorha.proto.StructPB.Int32CurrencyMapPBOrBuilder>(
                  getPlunder(),
                  getParentForChildren(),
                  isClean());
          plunder_ = null;
        }
        return plunderBuilder_;
      }

      private com.yorha.proto.ArmyPB.ArmyCollectResourceListPB collect_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ArmyPB.ArmyCollectResourceListPB, com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder, com.yorha.proto.ArmyPB.ArmyCollectResourceListPBOrBuilder> collectBuilder_;
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       * @return Whether the collect field is set.
       */
      public boolean hasCollect() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       * @return The collect.
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourceListPB getCollect() {
        if (collectBuilder_ == null) {
          return collect_ == null ? com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.getDefaultInstance() : collect_;
        } else {
          return collectBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       */
      public Builder setCollect(com.yorha.proto.ArmyPB.ArmyCollectResourceListPB value) {
        if (collectBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          collect_ = value;
          onChanged();
        } else {
          collectBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       */
      public Builder setCollect(
          com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder builderForValue) {
        if (collectBuilder_ == null) {
          collect_ = builderForValue.build();
          onChanged();
        } else {
          collectBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       */
      public Builder mergeCollect(com.yorha.proto.ArmyPB.ArmyCollectResourceListPB value) {
        if (collectBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              collect_ != null &&
              collect_ != com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.getDefaultInstance()) {
            collect_ =
              com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.newBuilder(collect_).mergeFrom(value).buildPartial();
          } else {
            collect_ = value;
          }
          onChanged();
        } else {
          collectBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       */
      public Builder clearCollect() {
        if (collectBuilder_ == null) {
          collect_ = null;
          onChanged();
        } else {
          collectBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder getCollectBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCollectFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourceListPBOrBuilder getCollectOrBuilder() {
        if (collectBuilder_ != null) {
          return collectBuilder_.getMessageOrBuilder();
        } else {
          return collect_ == null ?
              com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.getDefaultInstance() : collect_;
        }
      }
      /**
       * <pre>
       * 采集的资源
       * </pre>
       *
       * <code>optional .com.yorha.proto.ArmyCollectResourceListPB collect = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ArmyPB.ArmyCollectResourceListPB, com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder, com.yorha.proto.ArmyPB.ArmyCollectResourceListPBOrBuilder> 
          getCollectFieldBuilder() {
        if (collectBuilder_ == null) {
          collectBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ArmyPB.ArmyCollectResourceListPB, com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder, com.yorha.proto.ArmyPB.ArmyCollectResourceListPBOrBuilder>(
                  getCollect(),
                  getParentForChildren(),
                  isClean());
          collect_ = null;
        }
        return collectBuilder_;
      }

      private int outTimes_ ;
      /**
       * <pre>
       * 采集完的次数
       * </pre>
       *
       * <code>optional int32 outTimes = 3;</code>
       * @return Whether the outTimes field is set.
       */
      @java.lang.Override
      public boolean hasOutTimes() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 采集完的次数
       * </pre>
       *
       * <code>optional int32 outTimes = 3;</code>
       * @return The outTimes.
       */
      @java.lang.Override
      public int getOutTimes() {
        return outTimes_;
      }
      /**
       * <pre>
       * 采集完的次数
       * </pre>
       *
       * <code>optional int32 outTimes = 3;</code>
       * @param value The outTimes to set.
       * @return This builder for chaining.
       */
      public Builder setOutTimes(int value) {
        bitField0_ |= 0x00000004;
        outTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集完的次数
       * </pre>
       *
       * <code>optional int32 outTimes = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutTimes() {
        bitField0_ = (bitField0_ & ~0x00000004);
        outTimes_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ArmyResourcesModelPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ArmyResourcesModelPB)
    private static final com.yorha.proto.ArmyPB.ArmyResourcesModelPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.ArmyPB.ArmyResourcesModelPB();
    }

    public static com.yorha.proto.ArmyPB.ArmyResourcesModelPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ArmyResourcesModelPB>
        PARSER = new com.google.protobuf.AbstractParser<ArmyResourcesModelPB>() {
      @java.lang.Override
      public ArmyResourcesModelPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ArmyResourcesModelPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ArmyResourcesModelPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ArmyResourcesModelPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyResourcesModelPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ArmyCollectResourcePBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ArmyCollectResourcePB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 唯一标识 同一个资源田采多次 合并
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return Whether the id field is set.
     */
    boolean hasId();
    /**
     * <pre>
     * 唯一标识 同一个资源田采多次 合并
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return The id.
     */
    long getId();

    /**
     * <pre>
     * 采集的资源田配表id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 采集的资源田配表id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 资源总量
     * </pre>
     *
     * <code>optional int64 num = 3;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <pre>
     * 资源总量
     * </pre>
     *
     * <code>optional int64 num = 3;</code>
     * @return The num.
     */
    long getNum();

    /**
     * <pre>
     * 采集完成时间戳
     * </pre>
     *
     * <code>optional int64 collectEndTsMs = 4;</code>
     * @return Whether the collectEndTsMs field is set.
     */
    boolean hasCollectEndTsMs();
    /**
     * <pre>
     * 采集完成时间戳
     * </pre>
     *
     * <code>optional int64 collectEndTsMs = 4;</code>
     * @return The collectEndTsMs.
     */
    long getCollectEndTsMs();

    /**
     * <code>optional .com.yorha.proto.PointPB point = 5;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <code>optional .com.yorha.proto.PointPB point = 5;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <code>optional .com.yorha.proto.PointPB point = 5;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 采集完次数（废弃）
     * </pre>
     *
     * <code>optional int32 outTimes = 6;</code>
     * @return Whether the outTimes field is set.
     */
    boolean hasOutTimes();
    /**
     * <pre>
     * 采集完次数（废弃）
     * </pre>
     *
     * <code>optional int32 outTimes = 6;</code>
     * @return The outTimes.
     */
    int getOutTimes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ArmyCollectResourcePB}
   */
  public static final class ArmyCollectResourcePB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ArmyCollectResourcePB)
      ArmyCollectResourcePBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ArmyCollectResourcePB.newBuilder() to construct.
    private ArmyCollectResourcePB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ArmyCollectResourcePB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ArmyCollectResourcePB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ArmyCollectResourcePB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              templateId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              num_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              collectEndTsMs_ = input.readInt64();
              break;
            }
            case 42: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              outTimes_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourcePB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourcePB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.ArmyPB.ArmyCollectResourcePB.class, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder.class);
    }

    private int bitField0_;
    public static final int ID_FIELD_NUMBER = 1;
    private long id_;
    /**
     * <pre>
     * 唯一标识 同一个资源田采多次 合并
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return Whether the id field is set.
     */
    @java.lang.Override
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 唯一标识 同一个资源田采多次 合并
     * </pre>
     *
     * <code>optional int64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 2;
    private int templateId_;
    /**
     * <pre>
     * 采集的资源田配表id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 采集的资源田配表id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int NUM_FIELD_NUMBER = 3;
    private long num_;
    /**
     * <pre>
     * 资源总量
     * </pre>
     *
     * <code>optional int64 num = 3;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 资源总量
     * </pre>
     *
     * <code>optional int64 num = 3;</code>
     * @return The num.
     */
    @java.lang.Override
    public long getNum() {
      return num_;
    }

    public static final int COLLECTENDTSMS_FIELD_NUMBER = 4;
    private long collectEndTsMs_;
    /**
     * <pre>
     * 采集完成时间戳
     * </pre>
     *
     * <code>optional int64 collectEndTsMs = 4;</code>
     * @return Whether the collectEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasCollectEndTsMs() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 采集完成时间戳
     * </pre>
     *
     * <code>optional int64 collectEndTsMs = 4;</code>
     * @return The collectEndTsMs.
     */
    @java.lang.Override
    public long getCollectEndTsMs() {
      return collectEndTsMs_;
    }

    public static final int POINT_FIELD_NUMBER = 5;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <code>optional .com.yorha.proto.PointPB point = 5;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.PointPB point = 5;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <code>optional .com.yorha.proto.PointPB point = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int OUTTIMES_FIELD_NUMBER = 6;
    private int outTimes_;
    /**
     * <pre>
     * 采集完次数（废弃）
     * </pre>
     *
     * <code>optional int32 outTimes = 6;</code>
     * @return Whether the outTimes field is set.
     */
    @java.lang.Override
    public boolean hasOutTimes() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 采集完次数（废弃）
     * </pre>
     *
     * <code>optional int32 outTimes = 6;</code>
     * @return The outTimes.
     */
    @java.lang.Override
    public int getOutTimes() {
      return outTimes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, templateId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, num_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, collectEndTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getPoint());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, outTimes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, templateId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, num_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, collectEndTsMs_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getPoint());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, outTimes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.ArmyPB.ArmyCollectResourcePB)) {
        return super.equals(obj);
      }
      com.yorha.proto.ArmyPB.ArmyCollectResourcePB other = (com.yorha.proto.ArmyPB.ArmyCollectResourcePB) obj;

      if (hasId() != other.hasId()) return false;
      if (hasId()) {
        if (getId()
            != other.getId()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (hasCollectEndTsMs() != other.hasCollectEndTsMs()) return false;
      if (hasCollectEndTsMs()) {
        if (getCollectEndTsMs()
            != other.getCollectEndTsMs()) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasOutTimes() != other.hasOutTimes()) return false;
      if (hasOutTimes()) {
        if (getOutTimes()
            != other.getOutTimes()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasId()) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getId());
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNum());
      }
      if (hasCollectEndTsMs()) {
        hash = (37 * hash) + COLLECTENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCollectEndTsMs());
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasOutTimes()) {
        hash = (37 * hash) + OUTTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getOutTimes();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.ArmyPB.ArmyCollectResourcePB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ArmyCollectResourcePB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ArmyCollectResourcePB)
        com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourcePB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourcePB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.ArmyPB.ArmyCollectResourcePB.class, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder.class);
      }

      // Construct using com.yorha.proto.ArmyPB.ArmyCollectResourcePB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        collectEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        outTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourcePB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePB getDefaultInstanceForType() {
        return com.yorha.proto.ArmyPB.ArmyCollectResourcePB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePB build() {
        com.yorha.proto.ArmyPB.ArmyCollectResourcePB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePB buildPartial() {
        com.yorha.proto.ArmyPB.ArmyCollectResourcePB result = new com.yorha.proto.ArmyPB.ArmyCollectResourcePB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.collectEndTsMs_ = collectEndTsMs_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.outTimes_ = outTimes_;
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.ArmyPB.ArmyCollectResourcePB) {
          return mergeFrom((com.yorha.proto.ArmyPB.ArmyCollectResourcePB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.ArmyPB.ArmyCollectResourcePB other) {
        if (other == com.yorha.proto.ArmyPB.ArmyCollectResourcePB.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        if (other.hasCollectEndTsMs()) {
          setCollectEndTsMs(other.getCollectEndTsMs());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasOutTimes()) {
          setOutTimes(other.getOutTimes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.ArmyPB.ArmyCollectResourcePB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.ArmyPB.ArmyCollectResourcePB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long id_ ;
      /**
       * <pre>
       * 唯一标识 同一个资源田采多次 合并
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @return Whether the id field is set.
       */
      @java.lang.Override
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 唯一标识 同一个资源田采多次 合并
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public long getId() {
        return id_;
      }
      /**
       * <pre>
       * 唯一标识 同一个资源田采多次 合并
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(long value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 唯一标识 同一个资源田采多次 合并
       * </pre>
       *
       * <code>optional int64 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 采集的资源田配表id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 采集的资源田配表id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 采集的资源田配表id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000002;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集的资源田配表id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private long num_ ;
      /**
       * <pre>
       * 资源总量
       * </pre>
       *
       * <code>optional int64 num = 3;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 资源总量
       * </pre>
       *
       * <code>optional int64 num = 3;</code>
       * @return The num.
       */
      @java.lang.Override
      public long getNum() {
        return num_;
      }
      /**
       * <pre>
       * 资源总量
       * </pre>
       *
       * <code>optional int64 num = 3;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(long value) {
        bitField0_ |= 0x00000004;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 资源总量
       * </pre>
       *
       * <code>optional int64 num = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        num_ = 0L;
        onChanged();
        return this;
      }

      private long collectEndTsMs_ ;
      /**
       * <pre>
       * 采集完成时间戳
       * </pre>
       *
       * <code>optional int64 collectEndTsMs = 4;</code>
       * @return Whether the collectEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasCollectEndTsMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 采集完成时间戳
       * </pre>
       *
       * <code>optional int64 collectEndTsMs = 4;</code>
       * @return The collectEndTsMs.
       */
      @java.lang.Override
      public long getCollectEndTsMs() {
        return collectEndTsMs_;
      }
      /**
       * <pre>
       * 采集完成时间戳
       * </pre>
       *
       * <code>optional int64 collectEndTsMs = 4;</code>
       * @param value The collectEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setCollectEndTsMs(long value) {
        bitField0_ |= 0x00000008;
        collectEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集完成时间戳
       * </pre>
       *
       * <code>optional int64 collectEndTsMs = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCollectEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        collectEndTsMs_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.PointPB point = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private int outTimes_ ;
      /**
       * <pre>
       * 采集完次数（废弃）
       * </pre>
       *
       * <code>optional int32 outTimes = 6;</code>
       * @return Whether the outTimes field is set.
       */
      @java.lang.Override
      public boolean hasOutTimes() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 采集完次数（废弃）
       * </pre>
       *
       * <code>optional int32 outTimes = 6;</code>
       * @return The outTimes.
       */
      @java.lang.Override
      public int getOutTimes() {
        return outTimes_;
      }
      /**
       * <pre>
       * 采集完次数（废弃）
       * </pre>
       *
       * <code>optional int32 outTimes = 6;</code>
       * @param value The outTimes to set.
       * @return This builder for chaining.
       */
      public Builder setOutTimes(int value) {
        bitField0_ |= 0x00000020;
        outTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 采集完次数（废弃）
       * </pre>
       *
       * <code>optional int32 outTimes = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutTimes() {
        bitField0_ = (bitField0_ & ~0x00000020);
        outTimes_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ArmyCollectResourcePB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ArmyCollectResourcePB)
    private static final com.yorha.proto.ArmyPB.ArmyCollectResourcePB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.ArmyPB.ArmyCollectResourcePB();
    }

    public static com.yorha.proto.ArmyPB.ArmyCollectResourcePB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ArmyCollectResourcePB>
        PARSER = new com.google.protobuf.AbstractParser<ArmyCollectResourcePB>() {
      @java.lang.Override
      public ArmyCollectResourcePB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ArmyCollectResourcePB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ArmyCollectResourcePB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ArmyCollectResourcePB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyCollectResourcePB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ArmyCollectResourceListPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ArmyCollectResourceListPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    java.util.List<com.yorha.proto.ArmyPB.ArmyCollectResourcePB> 
        getDatasList();
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    com.yorha.proto.ArmyPB.ArmyCollectResourcePB getDatas(int index);
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    int getDatasCount();
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder> 
        getDatasOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder getDatasOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.ArmyCollectResourceListPB}
   */
  public static final class ArmyCollectResourceListPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ArmyCollectResourceListPB)
      ArmyCollectResourceListPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ArmyCollectResourceListPB.newBuilder() to construct.
    private ArmyCollectResourceListPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ArmyCollectResourceListPB() {
      datas_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ArmyCollectResourceListPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ArmyCollectResourceListPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = new java.util.ArrayList<com.yorha.proto.ArmyPB.ArmyCollectResourcePB>();
                mutable_bitField0_ |= 0x00000001;
              }
              datas_.add(
                  input.readMessage(com.yorha.proto.ArmyPB.ArmyCollectResourcePB.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          datas_ = java.util.Collections.unmodifiableList(datas_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourceListPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourceListPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.class, com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder.class);
    }

    public static final int DATAS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.ArmyPB.ArmyCollectResourcePB> datas_;
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.ArmyPB.ArmyCollectResourcePB> getDatasList() {
      return datas_;
    }
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder> 
        getDatasOrBuilderList() {
      return datas_;
    }
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    @java.lang.Override
    public int getDatasCount() {
      return datas_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyCollectResourcePB getDatas(int index) {
      return datas_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder getDatasOrBuilder(
        int index) {
      return datas_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < datas_.size(); i++) {
        output.writeMessage(1, datas_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < datas_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, datas_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.ArmyPB.ArmyCollectResourceListPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.ArmyPB.ArmyCollectResourceListPB other = (com.yorha.proto.ArmyPB.ArmyCollectResourceListPB) obj;

      if (!getDatasList()
          .equals(other.getDatasList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatasCount() > 0) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + getDatasList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.ArmyPB.ArmyCollectResourceListPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ArmyCollectResourceListPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ArmyCollectResourceListPB)
        com.yorha.proto.ArmyPB.ArmyCollectResourceListPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourceListPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourceListPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.class, com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.Builder.class);
      }

      // Construct using com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDatasFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (datasBuilder_ == null) {
          datas_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          datasBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.ArmyPB.internal_static_com_yorha_proto_ArmyCollectResourceListPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyCollectResourceListPB getDefaultInstanceForType() {
        return com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyCollectResourceListPB build() {
        com.yorha.proto.ArmyPB.ArmyCollectResourceListPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.ArmyPB.ArmyCollectResourceListPB buildPartial() {
        com.yorha.proto.ArmyPB.ArmyCollectResourceListPB result = new com.yorha.proto.ArmyPB.ArmyCollectResourceListPB(this);
        int from_bitField0_ = bitField0_;
        if (datasBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            datas_ = java.util.Collections.unmodifiableList(datas_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.datas_ = datas_;
        } else {
          result.datas_ = datasBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.ArmyPB.ArmyCollectResourceListPB) {
          return mergeFrom((com.yorha.proto.ArmyPB.ArmyCollectResourceListPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.ArmyPB.ArmyCollectResourceListPB other) {
        if (other == com.yorha.proto.ArmyPB.ArmyCollectResourceListPB.getDefaultInstance()) return this;
        if (datasBuilder_ == null) {
          if (!other.datas_.isEmpty()) {
            if (datas_.isEmpty()) {
              datas_ = other.datas_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureDatasIsMutable();
              datas_.addAll(other.datas_);
            }
            onChanged();
          }
        } else {
          if (!other.datas_.isEmpty()) {
            if (datasBuilder_.isEmpty()) {
              datasBuilder_.dispose();
              datasBuilder_ = null;
              datas_ = other.datas_;
              bitField0_ = (bitField0_ & ~0x00000001);
              datasBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDatasFieldBuilder() : null;
            } else {
              datasBuilder_.addAllMessages(other.datas_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.ArmyPB.ArmyCollectResourceListPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.ArmyPB.ArmyCollectResourceListPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.ArmyPB.ArmyCollectResourcePB> datas_ =
        java.util.Collections.emptyList();
      private void ensureDatasIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          datas_ = new java.util.ArrayList<com.yorha.proto.ArmyPB.ArmyCollectResourcePB>(datas_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.ArmyPB.ArmyCollectResourcePB, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder, com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder> datasBuilder_;

      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public java.util.List<com.yorha.proto.ArmyPB.ArmyCollectResourcePB> getDatasList() {
        if (datasBuilder_ == null) {
          return java.util.Collections.unmodifiableList(datas_);
        } else {
          return datasBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public int getDatasCount() {
        if (datasBuilder_ == null) {
          return datas_.size();
        } else {
          return datasBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePB getDatas(int index) {
        if (datasBuilder_ == null) {
          return datas_.get(index);
        } else {
          return datasBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder setDatas(
          int index, com.yorha.proto.ArmyPB.ArmyCollectResourcePB value) {
        if (datasBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDatasIsMutable();
          datas_.set(index, value);
          onChanged();
        } else {
          datasBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder setDatas(
          int index, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder builderForValue) {
        if (datasBuilder_ == null) {
          ensureDatasIsMutable();
          datas_.set(index, builderForValue.build());
          onChanged();
        } else {
          datasBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder addDatas(com.yorha.proto.ArmyPB.ArmyCollectResourcePB value) {
        if (datasBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDatasIsMutable();
          datas_.add(value);
          onChanged();
        } else {
          datasBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder addDatas(
          int index, com.yorha.proto.ArmyPB.ArmyCollectResourcePB value) {
        if (datasBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDatasIsMutable();
          datas_.add(index, value);
          onChanged();
        } else {
          datasBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder addDatas(
          com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder builderForValue) {
        if (datasBuilder_ == null) {
          ensureDatasIsMutable();
          datas_.add(builderForValue.build());
          onChanged();
        } else {
          datasBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder addDatas(
          int index, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder builderForValue) {
        if (datasBuilder_ == null) {
          ensureDatasIsMutable();
          datas_.add(index, builderForValue.build());
          onChanged();
        } else {
          datasBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder addAllDatas(
          java.lang.Iterable<? extends com.yorha.proto.ArmyPB.ArmyCollectResourcePB> values) {
        if (datasBuilder_ == null) {
          ensureDatasIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, datas_);
          onChanged();
        } else {
          datasBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder clearDatas() {
        if (datasBuilder_ == null) {
          datas_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          datasBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public Builder removeDatas(int index) {
        if (datasBuilder_ == null) {
          ensureDatasIsMutable();
          datas_.remove(index);
          onChanged();
        } else {
          datasBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder getDatasBuilder(
          int index) {
        return getDatasFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder getDatasOrBuilder(
          int index) {
        if (datasBuilder_ == null) {
          return datas_.get(index);  } else {
          return datasBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder> 
           getDatasOrBuilderList() {
        if (datasBuilder_ != null) {
          return datasBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(datas_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder addDatasBuilder() {
        return getDatasFieldBuilder().addBuilder(
            com.yorha.proto.ArmyPB.ArmyCollectResourcePB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder addDatasBuilder(
          int index) {
        return getDatasFieldBuilder().addBuilder(
            index, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.ArmyCollectResourcePB datas = 1;</code>
       */
      public java.util.List<com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder> 
           getDatasBuilderList() {
        return getDatasFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.ArmyPB.ArmyCollectResourcePB, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder, com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder> 
          getDatasFieldBuilder() {
        if (datasBuilder_ == null) {
          datasBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.ArmyPB.ArmyCollectResourcePB, com.yorha.proto.ArmyPB.ArmyCollectResourcePB.Builder, com.yorha.proto.ArmyPB.ArmyCollectResourcePBOrBuilder>(
                  datas_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          datas_ = null;
        }
        return datasBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ArmyCollectResourceListPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ArmyCollectResourceListPB)
    private static final com.yorha.proto.ArmyPB.ArmyCollectResourceListPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.ArmyPB.ArmyCollectResourceListPB();
    }

    public static com.yorha.proto.ArmyPB.ArmyCollectResourceListPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ArmyCollectResourceListPB>
        PARSER = new com.google.protobuf.AbstractParser<ArmyCollectResourceListPB>() {
      @java.lang.Override
      public ArmyCollectResourceListPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ArmyCollectResourceListPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ArmyCollectResourceListPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ArmyCollectResourceListPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.ArmyPB.ArmyCollectResourceListPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ArmyEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ArmyEntityPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ArmyResourcesModelPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ArmyResourcesModelPB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ArmyCollectResourcePB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ArmyCollectResourcePB_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ArmyCollectResourceListPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ArmyCollectResourceListPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\036cs_proto/gen/army/armyPB.proto\022\017com.yo" +
      "rha.proto\032\"cs_proto/gen/common/structPB." +
      "proto\032)cs_proto/gen/common/struct_battle" +
      "PB.proto\032\'cs_proto/gen/common/struct_cla" +
      "nPB.proto\032)cs_proto/gen/common/struct_pl" +
      "ayerPB.proto\032%ss_proto/gen/common/common" +
      "_enum.proto\"\221\007\n\014ArmyEntityPB\022-\n\tarmyStat" +
      "e\030\001 \001(\0162\032.com.yorha.proto.ArmyState\022\'\n\005t" +
      "roop\030\002 \001(\0132\030.com.yorha.proto.TroopPB\022%\n\004" +
      "move\030\003 \001(\0132\027.com.yorha.proto.MovePB\022\017\n\007o" +
      "wnerId\030\004 \001(\003\022\023\n\013formationId\030\005 \001(\005\022)\n\006bat" +
      "tle\030\006 \001(\0132\031.com.yorha.proto.BattlePB\022-\n\004" +
      "buff\030\007 \001(\0132\037.com.yorha.proto.Int32BuffMa" +
      "pPB\022#\n\004camp\030\010 \001(\0162\025.com.yorha.proto.Camp" +
      "\022\021\n\tclanSname\030\t \001(\t\022\024\n\014enterStateTs\030\n \001(" +
      "\003\022\016\n\006clanId\030\013 \001(\003\022\r\n\005hpMax\030\014 \001(\003\0221\n\npick" +
      "UpInfo\030\r \001(\0132\035.com.yorha.proto.PickUpInf" +
      "oPB\022\020\n\010attachId\030\016 \001(\003\0221\n\013attachState\030\017 \001" +
      "(\0162\034.com.yorha.proto.AttachState\022>\n\016tran" +
      "sportPlane\030\020 \001(\0132&.com.yorha.proto.Scene" +
      "TransportPlanePB\0223\n\010cardHead\030\022 \001(\0132!.com" +
      ".yorha.proto.PlayerCardHeadPB\0221\n\010clanFla" +
      "g\030\023 \001(\0132\037.com.yorha.proto.ClanFlagInfoPB" +
      "\0227\n\005arrow\030\024 \001(\0132(.com.yorha.proto.Int64A" +
      "rmyArrowItemMapPB\022\023\n\013modelRadius\030\025 \001(\005\0221" +
      "\n\nexpression\030\026 \001(\0132\035.com.yorha.proto.Exp" +
      "ressionPB\0225\n\trallyRole\030\030 \001(\0162\".com.yorha" +
      ".proto.RallyArmyRoleType\022\022\n\ncurRallyId\030\031" +
      " \001(\003\022\031\n\021curAssistTargetId\030\032 \001(\003\022\016\n\006zoneI" +
      "d\030\033 \001(\005\"\233\001\n\024ArmyResourcesModelPB\0224\n\007plun" +
      "der\030\001 \001(\0132#.com.yorha.proto.Int32Currenc" +
      "yMapPB\022;\n\007collect\030\002 \001(\0132*.com.yorha.prot" +
      "o.ArmyCollectResourceListPB\022\020\n\010outTimes\030" +
      "\003 \001(\005\"\227\001\n\025ArmyCollectResourcePB\022\n\n\002id\030\001 " +
      "\001(\003\022\022\n\ntemplateId\030\002 \001(\005\022\013\n\003num\030\003 \001(\003\022\026\n\016" +
      "collectEndTsMs\030\004 \001(\003\022\'\n\005point\030\005 \001(\0132\030.co" +
      "m.yorha.proto.PointPB\022\020\n\010outTimes\030\006 \001(\005\"" +
      "R\n\031ArmyCollectResourceListPB\0225\n\005datas\030\001 " +
      "\003(\0132&.com.yorha.proto.ArmyCollectResourc" +
      "ePBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.StructBattlePB.getDescriptor(),
          com.yorha.proto.StructClanPB.getDescriptor(),
          com.yorha.proto.StructPlayerPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_ArmyEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ArmyEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ArmyEntityPB_descriptor,
        new java.lang.String[] { "ArmyState", "Troop", "Move", "OwnerId", "FormationId", "Battle", "Buff", "Camp", "ClanSname", "EnterStateTs", "ClanId", "HpMax", "PickUpInfo", "AttachId", "AttachState", "TransportPlane", "CardHead", "ClanFlag", "Arrow", "ModelRadius", "Expression", "RallyRole", "CurRallyId", "CurAssistTargetId", "ZoneId", });
    internal_static_com_yorha_proto_ArmyResourcesModelPB_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_ArmyResourcesModelPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ArmyResourcesModelPB_descriptor,
        new java.lang.String[] { "Plunder", "Collect", "OutTimes", });
    internal_static_com_yorha_proto_ArmyCollectResourcePB_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ArmyCollectResourcePB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ArmyCollectResourcePB_descriptor,
        new java.lang.String[] { "Id", "TemplateId", "Num", "CollectEndTsMs", "Point", "OutTimes", });
    internal_static_com_yorha_proto_ArmyCollectResourceListPB_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_ArmyCollectResourceListPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ArmyCollectResourceListPB_descriptor,
        new java.lang.String[] { "Datas", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.StructBattlePB.getDescriptor();
    com.yorha.proto.StructClanPB.getDescriptor();
    com.yorha.proto.StructPlayerPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

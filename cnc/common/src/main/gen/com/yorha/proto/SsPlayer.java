// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player.proto

package com.yorha.proto;

public final class SsPlayer {
  private SsPlayer() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ApplyDataPatchCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ApplyDataPatchCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    boolean hasVersion();
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    int getVersion();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ApplyDataPatchCmd}
   */
  public static final class ApplyDataPatchCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ApplyDataPatchCmd)
      ApplyDataPatchCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ApplyDataPatchCmd.newBuilder() to construct.
    private ApplyDataPatchCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ApplyDataPatchCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ApplyDataPatchCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ApplyDataPatchCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              version_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_ApplyDataPatchCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_ApplyDataPatchCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayer.ApplyDataPatchCmd.class, com.yorha.proto.SsPlayer.ApplyDataPatchCmd.Builder.class);
    }

    private int bitField0_;
    public static final int VERSION_FIELD_NUMBER = 1;
    private int version_;
    /**
     * <code>optional int32 version = 1;</code>
     * @return Whether the version field is set.
     */
    @java.lang.Override
    public boolean hasVersion() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 version = 1;</code>
     * @return The version.
     */
    @java.lang.Override
    public int getVersion() {
      return version_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, version_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, version_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayer.ApplyDataPatchCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayer.ApplyDataPatchCmd other = (com.yorha.proto.SsPlayer.ApplyDataPatchCmd) obj;

      if (hasVersion() != other.hasVersion()) return false;
      if (hasVersion()) {
        if (getVersion()
            != other.getVersion()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasVersion()) {
        hash = (37 * hash) + VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getVersion();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayer.ApplyDataPatchCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ApplyDataPatchCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ApplyDataPatchCmd)
        com.yorha.proto.SsPlayer.ApplyDataPatchCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_ApplyDataPatchCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_ApplyDataPatchCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayer.ApplyDataPatchCmd.class, com.yorha.proto.SsPlayer.ApplyDataPatchCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayer.ApplyDataPatchCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        version_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_ApplyDataPatchCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.ApplyDataPatchCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayer.ApplyDataPatchCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.ApplyDataPatchCmd build() {
        com.yorha.proto.SsPlayer.ApplyDataPatchCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.ApplyDataPatchCmd buildPartial() {
        com.yorha.proto.SsPlayer.ApplyDataPatchCmd result = new com.yorha.proto.SsPlayer.ApplyDataPatchCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.version_ = version_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayer.ApplyDataPatchCmd) {
          return mergeFrom((com.yorha.proto.SsPlayer.ApplyDataPatchCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayer.ApplyDataPatchCmd other) {
        if (other == com.yorha.proto.SsPlayer.ApplyDataPatchCmd.getDefaultInstance()) return this;
        if (other.hasVersion()) {
          setVersion(other.getVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayer.ApplyDataPatchCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayer.ApplyDataPatchCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int version_ ;
      /**
       * <code>optional int32 version = 1;</code>
       * @return Whether the version field is set.
       */
      @java.lang.Override
      public boolean hasVersion() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return The version.
       */
      @java.lang.Override
      public int getVersion() {
        return version_;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(int value) {
        bitField0_ |= 0x00000001;
        version_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 version = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000001);
        version_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ApplyDataPatchCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ApplyDataPatchCmd)
    private static final com.yorha.proto.SsPlayer.ApplyDataPatchCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayer.ApplyDataPatchCmd();
    }

    public static com.yorha.proto.SsPlayer.ApplyDataPatchCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ApplyDataPatchCmd>
        PARSER = new com.google.protobuf.AbstractParser<ApplyDataPatchCmd>() {
      @java.lang.Override
      public ApplyDataPatchCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ApplyDataPatchCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ApplyDataPatchCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ApplyDataPatchCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayer.ApplyDataPatchCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LoginPlayerCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LoginPlayerCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
     * @return Whether the sessionParamInfo field is set.
     */
    boolean hasSessionParamInfo();
    /**
     * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
     * @return The sessionParamInfo.
     */
    com.yorha.proto.CommonMsg.SessionParamInfo getSessionParamInfo();
    /**
     * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
     */
    com.yorha.proto.CommonMsg.SessionParamInfoOrBuilder getSessionParamInfoOrBuilder();

    /**
     * <pre>
     * 是否开启新手
     * </pre>
     *
     * <code>optional bool debugStartNewbie = 3;</code>
     * @return Whether the debugStartNewbie field is set.
     */
    boolean hasDebugStartNewbie();
    /**
     * <pre>
     * 是否开启新手
     * </pre>
     *
     * <code>optional bool debugStartNewbie = 3;</code>
     * @return The debugStartNewbie.
     */
    boolean getDebugStartNewbie();

    /**
     * <pre>
     * 真实的客户端语言 只用于导量规则中对语言的限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
     * @return Whether the actualLanguage field is set.
     */
    boolean hasActualLanguage();
    /**
     * <pre>
     * 真实的客户端语言 只用于导量规则中对语言的限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
     * @return The actualLanguage.
     */
    com.yorha.proto.CommonEnum.Language getActualLanguage();

    /**
     * <pre>
     * midas的神奇pf值
     * </pre>
     *
     * <code>optional string midasPf = 5;</code>
     * @return Whether the midasPf field is set.
     */
    boolean hasMidasPf();
    /**
     * <pre>
     * midas的神奇pf值
     * </pre>
     *
     * <code>optional string midasPf = 5;</code>
     * @return The midasPf.
     */
    java.lang.String getMidasPf();
    /**
     * <pre>
     * midas的神奇pf值
     * </pre>
     *
     * <code>optional string midasPf = 5;</code>
     * @return The bytes for midasPf.
     */
    com.google.protobuf.ByteString
        getMidasPfBytes();

    /**
     * <pre>
     * intl SDK接收推送的token
     * </pre>
     *
     * <code>optional string intlNtfToken = 6;</code>
     * @return Whether the intlNtfToken field is set.
     */
    boolean hasIntlNtfToken();
    /**
     * <pre>
     * intl SDK接收推送的token
     * </pre>
     *
     * <code>optional string intlNtfToken = 6;</code>
     * @return The intlNtfToken.
     */
    java.lang.String getIntlNtfToken();
    /**
     * <pre>
     * intl SDK接收推送的token
     * </pre>
     *
     * <code>optional string intlNtfToken = 6;</code>
     * @return The bytes for intlNtfToken.
     */
    com.google.protobuf.ByteString
        getIntlNtfTokenBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.LoginPlayerCmd}
   */
  public static final class LoginPlayerCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LoginPlayerCmd)
      LoginPlayerCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LoginPlayerCmd.newBuilder() to construct.
    private LoginPlayerCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LoginPlayerCmd() {
      openId_ = "";
      actualLanguage_ = 0;
      midasPf_ = "";
      intlNtfToken_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LoginPlayerCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LoginPlayerCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              openId_ = bs;
              break;
            }
            case 18: {
              com.yorha.proto.CommonMsg.SessionParamInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = sessionParamInfo_.toBuilder();
              }
              sessionParamInfo_ = input.readMessage(com.yorha.proto.CommonMsg.SessionParamInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sessionParamInfo_);
                sessionParamInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              debugStartNewbie_ = input.readBool();
              break;
            }
            case 32: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Language value = com.yorha.proto.CommonEnum.Language.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(4, rawValue);
              } else {
                bitField0_ |= 0x00000008;
                actualLanguage_ = rawValue;
              }
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              midasPf_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              intlNtfToken_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_LoginPlayerCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_LoginPlayerCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayer.LoginPlayerCmd.class, com.yorha.proto.SsPlayer.LoginPlayerCmd.Builder.class);
    }

    private int bitField0_;
    public static final int OPENID_FIELD_NUMBER = 1;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SESSIONPARAMINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.CommonMsg.SessionParamInfo sessionParamInfo_;
    /**
     * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
     * @return Whether the sessionParamInfo field is set.
     */
    @java.lang.Override
    public boolean hasSessionParamInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
     * @return The sessionParamInfo.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.SessionParamInfo getSessionParamInfo() {
      return sessionParamInfo_ == null ? com.yorha.proto.CommonMsg.SessionParamInfo.getDefaultInstance() : sessionParamInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.SessionParamInfoOrBuilder getSessionParamInfoOrBuilder() {
      return sessionParamInfo_ == null ? com.yorha.proto.CommonMsg.SessionParamInfo.getDefaultInstance() : sessionParamInfo_;
    }

    public static final int DEBUGSTARTNEWBIE_FIELD_NUMBER = 3;
    private boolean debugStartNewbie_;
    /**
     * <pre>
     * 是否开启新手
     * </pre>
     *
     * <code>optional bool debugStartNewbie = 3;</code>
     * @return Whether the debugStartNewbie field is set.
     */
    @java.lang.Override
    public boolean hasDebugStartNewbie() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 是否开启新手
     * </pre>
     *
     * <code>optional bool debugStartNewbie = 3;</code>
     * @return The debugStartNewbie.
     */
    @java.lang.Override
    public boolean getDebugStartNewbie() {
      return debugStartNewbie_;
    }

    public static final int ACTUALLANGUAGE_FIELD_NUMBER = 4;
    private int actualLanguage_;
    /**
     * <pre>
     * 真实的客户端语言 只用于导量规则中对语言的限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
     * @return Whether the actualLanguage field is set.
     */
    @java.lang.Override public boolean hasActualLanguage() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 真实的客户端语言 只用于导量规则中对语言的限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
     * @return The actualLanguage.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Language getActualLanguage() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Language result = com.yorha.proto.CommonEnum.Language.valueOf(actualLanguage_);
      return result == null ? com.yorha.proto.CommonEnum.Language.L_NONE : result;
    }

    public static final int MIDASPF_FIELD_NUMBER = 5;
    private volatile java.lang.Object midasPf_;
    /**
     * <pre>
     * midas的神奇pf值
     * </pre>
     *
     * <code>optional string midasPf = 5;</code>
     * @return Whether the midasPf field is set.
     */
    @java.lang.Override
    public boolean hasMidasPf() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * midas的神奇pf值
     * </pre>
     *
     * <code>optional string midasPf = 5;</code>
     * @return The midasPf.
     */
    @java.lang.Override
    public java.lang.String getMidasPf() {
      java.lang.Object ref = midasPf_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          midasPf_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * midas的神奇pf值
     * </pre>
     *
     * <code>optional string midasPf = 5;</code>
     * @return The bytes for midasPf.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMidasPfBytes() {
      java.lang.Object ref = midasPf_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        midasPf_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int INTLNTFTOKEN_FIELD_NUMBER = 6;
    private volatile java.lang.Object intlNtfToken_;
    /**
     * <pre>
     * intl SDK接收推送的token
     * </pre>
     *
     * <code>optional string intlNtfToken = 6;</code>
     * @return Whether the intlNtfToken field is set.
     */
    @java.lang.Override
    public boolean hasIntlNtfToken() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * intl SDK接收推送的token
     * </pre>
     *
     * <code>optional string intlNtfToken = 6;</code>
     * @return The intlNtfToken.
     */
    @java.lang.Override
    public java.lang.String getIntlNtfToken() {
      java.lang.Object ref = intlNtfToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          intlNtfToken_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * intl SDK接收推送的token
     * </pre>
     *
     * <code>optional string intlNtfToken = 6;</code>
     * @return The bytes for intlNtfToken.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIntlNtfTokenBytes() {
      java.lang.Object ref = intlNtfToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        intlNtfToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getSessionParamInfo());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, debugStartNewbie_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeEnum(4, actualLanguage_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, midasPf_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, intlNtfToken_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSessionParamInfo());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, debugStartNewbie_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, actualLanguage_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, midasPf_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, intlNtfToken_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayer.LoginPlayerCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayer.LoginPlayerCmd other = (com.yorha.proto.SsPlayer.LoginPlayerCmd) obj;

      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasSessionParamInfo() != other.hasSessionParamInfo()) return false;
      if (hasSessionParamInfo()) {
        if (!getSessionParamInfo()
            .equals(other.getSessionParamInfo())) return false;
      }
      if (hasDebugStartNewbie() != other.hasDebugStartNewbie()) return false;
      if (hasDebugStartNewbie()) {
        if (getDebugStartNewbie()
            != other.getDebugStartNewbie()) return false;
      }
      if (hasActualLanguage() != other.hasActualLanguage()) return false;
      if (hasActualLanguage()) {
        if (actualLanguage_ != other.actualLanguage_) return false;
      }
      if (hasMidasPf() != other.hasMidasPf()) return false;
      if (hasMidasPf()) {
        if (!getMidasPf()
            .equals(other.getMidasPf())) return false;
      }
      if (hasIntlNtfToken() != other.hasIntlNtfToken()) return false;
      if (hasIntlNtfToken()) {
        if (!getIntlNtfToken()
            .equals(other.getIntlNtfToken())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasSessionParamInfo()) {
        hash = (37 * hash) + SESSIONPARAMINFO_FIELD_NUMBER;
        hash = (53 * hash) + getSessionParamInfo().hashCode();
      }
      if (hasDebugStartNewbie()) {
        hash = (37 * hash) + DEBUGSTARTNEWBIE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getDebugStartNewbie());
      }
      if (hasActualLanguage()) {
        hash = (37 * hash) + ACTUALLANGUAGE_FIELD_NUMBER;
        hash = (53 * hash) + actualLanguage_;
      }
      if (hasMidasPf()) {
        hash = (37 * hash) + MIDASPF_FIELD_NUMBER;
        hash = (53 * hash) + getMidasPf().hashCode();
      }
      if (hasIntlNtfToken()) {
        hash = (37 * hash) + INTLNTFTOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getIntlNtfToken().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.LoginPlayerCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayer.LoginPlayerCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.LoginPlayerCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LoginPlayerCmd)
        com.yorha.proto.SsPlayer.LoginPlayerCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_LoginPlayerCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_LoginPlayerCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayer.LoginPlayerCmd.class, com.yorha.proto.SsPlayer.LoginPlayerCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayer.LoginPlayerCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSessionParamInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        if (sessionParamInfoBuilder_ == null) {
          sessionParamInfo_ = null;
        } else {
          sessionParamInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        debugStartNewbie_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        actualLanguage_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        midasPf_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        intlNtfToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_LoginPlayerCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.LoginPlayerCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayer.LoginPlayerCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.LoginPlayerCmd build() {
        com.yorha.proto.SsPlayer.LoginPlayerCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.LoginPlayerCmd buildPartial() {
        com.yorha.proto.SsPlayer.LoginPlayerCmd result = new com.yorha.proto.SsPlayer.LoginPlayerCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (sessionParamInfoBuilder_ == null) {
            result.sessionParamInfo_ = sessionParamInfo_;
          } else {
            result.sessionParamInfo_ = sessionParamInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.debugStartNewbie_ = debugStartNewbie_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.actualLanguage_ = actualLanguage_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.midasPf_ = midasPf_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.intlNtfToken_ = intlNtfToken_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayer.LoginPlayerCmd) {
          return mergeFrom((com.yorha.proto.SsPlayer.LoginPlayerCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayer.LoginPlayerCmd other) {
        if (other == com.yorha.proto.SsPlayer.LoginPlayerCmd.getDefaultInstance()) return this;
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000001;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasSessionParamInfo()) {
          mergeSessionParamInfo(other.getSessionParamInfo());
        }
        if (other.hasDebugStartNewbie()) {
          setDebugStartNewbie(other.getDebugStartNewbie());
        }
        if (other.hasActualLanguage()) {
          setActualLanguage(other.getActualLanguage());
        }
        if (other.hasMidasPf()) {
          bitField0_ |= 0x00000010;
          midasPf_ = other.midasPf_;
          onChanged();
        }
        if (other.hasIntlNtfToken()) {
          bitField0_ |= 0x00000020;
          intlNtfToken_ = other.intlNtfToken_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayer.LoginPlayerCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayer.LoginPlayerCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 1;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.SessionParamInfo sessionParamInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.SessionParamInfo, com.yorha.proto.CommonMsg.SessionParamInfo.Builder, com.yorha.proto.CommonMsg.SessionParamInfoOrBuilder> sessionParamInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       * @return Whether the sessionParamInfo field is set.
       */
      public boolean hasSessionParamInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       * @return The sessionParamInfo.
       */
      public com.yorha.proto.CommonMsg.SessionParamInfo getSessionParamInfo() {
        if (sessionParamInfoBuilder_ == null) {
          return sessionParamInfo_ == null ? com.yorha.proto.CommonMsg.SessionParamInfo.getDefaultInstance() : sessionParamInfo_;
        } else {
          return sessionParamInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       */
      public Builder setSessionParamInfo(com.yorha.proto.CommonMsg.SessionParamInfo value) {
        if (sessionParamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sessionParamInfo_ = value;
          onChanged();
        } else {
          sessionParamInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       */
      public Builder setSessionParamInfo(
          com.yorha.proto.CommonMsg.SessionParamInfo.Builder builderForValue) {
        if (sessionParamInfoBuilder_ == null) {
          sessionParamInfo_ = builderForValue.build();
          onChanged();
        } else {
          sessionParamInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       */
      public Builder mergeSessionParamInfo(com.yorha.proto.CommonMsg.SessionParamInfo value) {
        if (sessionParamInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              sessionParamInfo_ != null &&
              sessionParamInfo_ != com.yorha.proto.CommonMsg.SessionParamInfo.getDefaultInstance()) {
            sessionParamInfo_ =
              com.yorha.proto.CommonMsg.SessionParamInfo.newBuilder(sessionParamInfo_).mergeFrom(value).buildPartial();
          } else {
            sessionParamInfo_ = value;
          }
          onChanged();
        } else {
          sessionParamInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       */
      public Builder clearSessionParamInfo() {
        if (sessionParamInfoBuilder_ == null) {
          sessionParamInfo_ = null;
          onChanged();
        } else {
          sessionParamInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       */
      public com.yorha.proto.CommonMsg.SessionParamInfo.Builder getSessionParamInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSessionParamInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       */
      public com.yorha.proto.CommonMsg.SessionParamInfoOrBuilder getSessionParamInfoOrBuilder() {
        if (sessionParamInfoBuilder_ != null) {
          return sessionParamInfoBuilder_.getMessageOrBuilder();
        } else {
          return sessionParamInfo_ == null ?
              com.yorha.proto.CommonMsg.SessionParamInfo.getDefaultInstance() : sessionParamInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.SessionParamInfo sessionParamInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.SessionParamInfo, com.yorha.proto.CommonMsg.SessionParamInfo.Builder, com.yorha.proto.CommonMsg.SessionParamInfoOrBuilder> 
          getSessionParamInfoFieldBuilder() {
        if (sessionParamInfoBuilder_ == null) {
          sessionParamInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.SessionParamInfo, com.yorha.proto.CommonMsg.SessionParamInfo.Builder, com.yorha.proto.CommonMsg.SessionParamInfoOrBuilder>(
                  getSessionParamInfo(),
                  getParentForChildren(),
                  isClean());
          sessionParamInfo_ = null;
        }
        return sessionParamInfoBuilder_;
      }

      private boolean debugStartNewbie_ ;
      /**
       * <pre>
       * 是否开启新手
       * </pre>
       *
       * <code>optional bool debugStartNewbie = 3;</code>
       * @return Whether the debugStartNewbie field is set.
       */
      @java.lang.Override
      public boolean hasDebugStartNewbie() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 是否开启新手
       * </pre>
       *
       * <code>optional bool debugStartNewbie = 3;</code>
       * @return The debugStartNewbie.
       */
      @java.lang.Override
      public boolean getDebugStartNewbie() {
        return debugStartNewbie_;
      }
      /**
       * <pre>
       * 是否开启新手
       * </pre>
       *
       * <code>optional bool debugStartNewbie = 3;</code>
       * @param value The debugStartNewbie to set.
       * @return This builder for chaining.
       */
      public Builder setDebugStartNewbie(boolean value) {
        bitField0_ |= 0x00000004;
        debugStartNewbie_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否开启新手
       * </pre>
       *
       * <code>optional bool debugStartNewbie = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDebugStartNewbie() {
        bitField0_ = (bitField0_ & ~0x00000004);
        debugStartNewbie_ = false;
        onChanged();
        return this;
      }

      private int actualLanguage_ = 0;
      /**
       * <pre>
       * 真实的客户端语言 只用于导量规则中对语言的限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
       * @return Whether the actualLanguage field is set.
       */
      @java.lang.Override public boolean hasActualLanguage() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 真实的客户端语言 只用于导量规则中对语言的限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
       * @return The actualLanguage.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Language getActualLanguage() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Language result = com.yorha.proto.CommonEnum.Language.valueOf(actualLanguage_);
        return result == null ? com.yorha.proto.CommonEnum.Language.L_NONE : result;
      }
      /**
       * <pre>
       * 真实的客户端语言 只用于导量规则中对语言的限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
       * @param value The actualLanguage to set.
       * @return This builder for chaining.
       */
      public Builder setActualLanguage(com.yorha.proto.CommonEnum.Language value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000008;
        actualLanguage_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 真实的客户端语言 只用于导量规则中对语言的限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.Language actualLanguage = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearActualLanguage() {
        bitField0_ = (bitField0_ & ~0x00000008);
        actualLanguage_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object midasPf_ = "";
      /**
       * <pre>
       * midas的神奇pf值
       * </pre>
       *
       * <code>optional string midasPf = 5;</code>
       * @return Whether the midasPf field is set.
       */
      public boolean hasMidasPf() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * midas的神奇pf值
       * </pre>
       *
       * <code>optional string midasPf = 5;</code>
       * @return The midasPf.
       */
      public java.lang.String getMidasPf() {
        java.lang.Object ref = midasPf_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            midasPf_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * midas的神奇pf值
       * </pre>
       *
       * <code>optional string midasPf = 5;</code>
       * @return The bytes for midasPf.
       */
      public com.google.protobuf.ByteString
          getMidasPfBytes() {
        java.lang.Object ref = midasPf_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          midasPf_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * midas的神奇pf值
       * </pre>
       *
       * <code>optional string midasPf = 5;</code>
       * @param value The midasPf to set.
       * @return This builder for chaining.
       */
      public Builder setMidasPf(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        midasPf_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * midas的神奇pf值
       * </pre>
       *
       * <code>optional string midasPf = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearMidasPf() {
        bitField0_ = (bitField0_ & ~0x00000010);
        midasPf_ = getDefaultInstance().getMidasPf();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * midas的神奇pf值
       * </pre>
       *
       * <code>optional string midasPf = 5;</code>
       * @param value The bytes for midasPf to set.
       * @return This builder for chaining.
       */
      public Builder setMidasPfBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        midasPf_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object intlNtfToken_ = "";
      /**
       * <pre>
       * intl SDK接收推送的token
       * </pre>
       *
       * <code>optional string intlNtfToken = 6;</code>
       * @return Whether the intlNtfToken field is set.
       */
      public boolean hasIntlNtfToken() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * intl SDK接收推送的token
       * </pre>
       *
       * <code>optional string intlNtfToken = 6;</code>
       * @return The intlNtfToken.
       */
      public java.lang.String getIntlNtfToken() {
        java.lang.Object ref = intlNtfToken_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            intlNtfToken_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * intl SDK接收推送的token
       * </pre>
       *
       * <code>optional string intlNtfToken = 6;</code>
       * @return The bytes for intlNtfToken.
       */
      public com.google.protobuf.ByteString
          getIntlNtfTokenBytes() {
        java.lang.Object ref = intlNtfToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          intlNtfToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * intl SDK接收推送的token
       * </pre>
       *
       * <code>optional string intlNtfToken = 6;</code>
       * @param value The intlNtfToken to set.
       * @return This builder for chaining.
       */
      public Builder setIntlNtfToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        intlNtfToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * intl SDK接收推送的token
       * </pre>
       *
       * <code>optional string intlNtfToken = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIntlNtfToken() {
        bitField0_ = (bitField0_ & ~0x00000020);
        intlNtfToken_ = getDefaultInstance().getIntlNtfToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * intl SDK接收推送的token
       * </pre>
       *
       * <code>optional string intlNtfToken = 6;</code>
       * @param value The bytes for intlNtfToken to set.
       * @return This builder for chaining.
       */
      public Builder setIntlNtfTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        intlNtfToken_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LoginPlayerCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LoginPlayerCmd)
    private static final com.yorha.proto.SsPlayer.LoginPlayerCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayer.LoginPlayerCmd();
    }

    public static com.yorha.proto.SsPlayer.LoginPlayerCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LoginPlayerCmd>
        PARSER = new com.google.protobuf.AbstractParser<LoginPlayerCmd>() {
      @java.lang.Override
      public LoginPlayerCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LoginPlayerCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LoginPlayerCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LoginPlayerCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayer.LoginPlayerCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnPlayerDisconnectCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnPlayerDisconnectCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return Whether the closeReason field is set.
     */
    boolean hasCloseReason();
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return The closeReason.
     */
    com.yorha.proto.CommonEnum.SessionCloseReason getCloseReason();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnPlayerDisconnectCmd}
   */
  public static final class OnPlayerDisconnectCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnPlayerDisconnectCmd)
      OnPlayerDisconnectCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnPlayerDisconnectCmd.newBuilder() to construct.
    private OnPlayerDisconnectCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnPlayerDisconnectCmd() {
      closeReason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnPlayerDisconnectCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnPlayerDisconnectCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SessionCloseReason value = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                closeReason_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_OnPlayerDisconnectCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_OnPlayerDisconnectCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd.class, com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CLOSEREASON_FIELD_NUMBER = 1;
    private int closeReason_;
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return Whether the closeReason field is set.
     */
    @java.lang.Override public boolean hasCloseReason() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return The closeReason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SessionCloseReason getCloseReason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SessionCloseReason result = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(closeReason_);
      return result == null ? com.yorha.proto.CommonEnum.SessionCloseReason.SCR_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, closeReason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, closeReason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd other = (com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd) obj;

      if (hasCloseReason() != other.hasCloseReason()) return false;
      if (hasCloseReason()) {
        if (closeReason_ != other.closeReason_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCloseReason()) {
        hash = (37 * hash) + CLOSEREASON_FIELD_NUMBER;
        hash = (53 * hash) + closeReason_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnPlayerDisconnectCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnPlayerDisconnectCmd)
        com.yorha.proto.SsPlayer.OnPlayerDisconnectCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_OnPlayerDisconnectCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_OnPlayerDisconnectCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd.class, com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        closeReason_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_OnPlayerDisconnectCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd build() {
        com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd buildPartial() {
        com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd result = new com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.closeReason_ = closeReason_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd) {
          return mergeFrom((com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd other) {
        if (other == com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd.getDefaultInstance()) return this;
        if (other.hasCloseReason()) {
          setCloseReason(other.getCloseReason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int closeReason_ = 0;
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @return Whether the closeReason field is set.
       */
      @java.lang.Override public boolean hasCloseReason() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @return The closeReason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SessionCloseReason getCloseReason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SessionCloseReason result = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(closeReason_);
        return result == null ? com.yorha.proto.CommonEnum.SessionCloseReason.SCR_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @param value The closeReason to set.
       * @return This builder for chaining.
       */
      public Builder setCloseReason(com.yorha.proto.CommonEnum.SessionCloseReason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        closeReason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCloseReason() {
        bitField0_ = (bitField0_ & ~0x00000001);
        closeReason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnPlayerDisconnectCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnPlayerDisconnectCmd)
    private static final com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd();
    }

    public static com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnPlayerDisconnectCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnPlayerDisconnectCmd>() {
      @java.lang.Override
      public OnPlayerDisconnectCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnPlayerDisconnectCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnPlayerDisconnectCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnPlayerDisconnectCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayer.OnPlayerDisconnectCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RecvClientMsgCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.RecvClientMsgCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>optional int32 seqId = 2;</code>
     * @return Whether the seqId field is set.
     */
    boolean hasSeqId();
    /**
     * <code>optional int32 seqId = 2;</code>
     * @return The seqId.
     */
    int getSeqId();

    /**
     * <code>optional bytes msgBytes = 3;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 3;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.RecvClientMsgCmd}
   */
  public static final class RecvClientMsgCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.RecvClientMsgCmd)
      RecvClientMsgCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RecvClientMsgCmd.newBuilder() to construct.
    private RecvClientMsgCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RecvClientMsgCmd() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RecvClientMsgCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RecvClientMsgCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              msgType_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              seqId_ = input.readInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_RecvClientMsgCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_RecvClientMsgCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayer.RecvClientMsgCmd.class, com.yorha.proto.SsPlayer.RecvClientMsgCmd.Builder.class);
    }

    private int bitField0_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int SEQID_FIELD_NUMBER = 2;
    private int seqId_;
    /**
     * <code>optional int32 seqId = 2;</code>
     * @return Whether the seqId field is set.
     */
    @java.lang.Override
    public boolean hasSeqId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 seqId = 2;</code>
     * @return The seqId.
     */
    @java.lang.Override
    public int getSeqId() {
      return seqId_;
    }

    public static final int MSGBYTES_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 3;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 3;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBytes(3, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayer.RecvClientMsgCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayer.RecvClientMsgCmd other = (com.yorha.proto.SsPlayer.RecvClientMsgCmd) obj;

      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (hasSeqId() != other.hasSeqId()) return false;
      if (hasSeqId()) {
        if (getSeqId()
            != other.getSeqId()) return false;
      }
      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasSeqId()) {
        hash = (37 * hash) + SEQID_FIELD_NUMBER;
        hash = (53 * hash) + getSeqId();
      }
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayer.RecvClientMsgCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.RecvClientMsgCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.RecvClientMsgCmd)
        com.yorha.proto.SsPlayer.RecvClientMsgCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_RecvClientMsgCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_RecvClientMsgCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayer.RecvClientMsgCmd.class, com.yorha.proto.SsPlayer.RecvClientMsgCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayer.RecvClientMsgCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        seqId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_RecvClientMsgCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.RecvClientMsgCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayer.RecvClientMsgCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.RecvClientMsgCmd build() {
        com.yorha.proto.SsPlayer.RecvClientMsgCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.RecvClientMsgCmd buildPartial() {
        com.yorha.proto.SsPlayer.RecvClientMsgCmd result = new com.yorha.proto.SsPlayer.RecvClientMsgCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.seqId_ = seqId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayer.RecvClientMsgCmd) {
          return mergeFrom((com.yorha.proto.SsPlayer.RecvClientMsgCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayer.RecvClientMsgCmd other) {
        if (other == com.yorha.proto.SsPlayer.RecvClientMsgCmd.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasSeqId()) {
          setSeqId(other.getSeqId());
        }
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayer.RecvClientMsgCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayer.RecvClientMsgCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgType_ ;
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private int seqId_ ;
      /**
       * <code>optional int32 seqId = 2;</code>
       * @return Whether the seqId field is set.
       */
      @java.lang.Override
      public boolean hasSeqId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 seqId = 2;</code>
       * @return The seqId.
       */
      @java.lang.Override
      public int getSeqId() {
        return seqId_;
      }
      /**
       * <code>optional int32 seqId = 2;</code>
       * @param value The seqId to set.
       * @return This builder for chaining.
       */
      public Builder setSeqId(int value) {
        bitField0_ |= 0x00000002;
        seqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 seqId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeqId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        seqId_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 3;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 3;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 3;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000004);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.RecvClientMsgCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.RecvClientMsgCmd)
    private static final com.yorha.proto.SsPlayer.RecvClientMsgCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayer.RecvClientMsgCmd();
    }

    public static com.yorha.proto.SsPlayer.RecvClientMsgCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<RecvClientMsgCmd>
        PARSER = new com.google.protobuf.AbstractParser<RecvClientMsgCmd>() {
      @java.lang.Override
      public RecvClientMsgCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RecvClientMsgCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RecvClientMsgCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RecvClientMsgCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayer.RecvClientMsgCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KickOffPlayerCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KickOffPlayerCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return Whether the closeReason field is set.
     */
    boolean hasCloseReason();
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return The closeReason.
     */
    com.yorha.proto.CommonEnum.SessionCloseReason getCloseReason();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KickOffPlayerCmd}
   */
  public static final class KickOffPlayerCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KickOffPlayerCmd)
      KickOffPlayerCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KickOffPlayerCmd.newBuilder() to construct.
    private KickOffPlayerCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KickOffPlayerCmd() {
      closeReason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KickOffPlayerCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KickOffPlayerCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SessionCloseReason value = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                closeReason_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_KickOffPlayerCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_KickOffPlayerCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayer.KickOffPlayerCmd.class, com.yorha.proto.SsPlayer.KickOffPlayerCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CLOSEREASON_FIELD_NUMBER = 1;
    private int closeReason_;
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return Whether the closeReason field is set.
     */
    @java.lang.Override public boolean hasCloseReason() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
     * @return The closeReason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SessionCloseReason getCloseReason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SessionCloseReason result = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(closeReason_);
      return result == null ? com.yorha.proto.CommonEnum.SessionCloseReason.SCR_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, closeReason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, closeReason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayer.KickOffPlayerCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayer.KickOffPlayerCmd other = (com.yorha.proto.SsPlayer.KickOffPlayerCmd) obj;

      if (hasCloseReason() != other.hasCloseReason()) return false;
      if (hasCloseReason()) {
        if (closeReason_ != other.closeReason_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCloseReason()) {
        hash = (37 * hash) + CLOSEREASON_FIELD_NUMBER;
        hash = (53 * hash) + closeReason_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayer.KickOffPlayerCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KickOffPlayerCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KickOffPlayerCmd)
        com.yorha.proto.SsPlayer.KickOffPlayerCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_KickOffPlayerCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_KickOffPlayerCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayer.KickOffPlayerCmd.class, com.yorha.proto.SsPlayer.KickOffPlayerCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayer.KickOffPlayerCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        closeReason_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayer.internal_static_com_yorha_proto_KickOffPlayerCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.KickOffPlayerCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayer.KickOffPlayerCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.KickOffPlayerCmd build() {
        com.yorha.proto.SsPlayer.KickOffPlayerCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayer.KickOffPlayerCmd buildPartial() {
        com.yorha.proto.SsPlayer.KickOffPlayerCmd result = new com.yorha.proto.SsPlayer.KickOffPlayerCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.closeReason_ = closeReason_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayer.KickOffPlayerCmd) {
          return mergeFrom((com.yorha.proto.SsPlayer.KickOffPlayerCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayer.KickOffPlayerCmd other) {
        if (other == com.yorha.proto.SsPlayer.KickOffPlayerCmd.getDefaultInstance()) return this;
        if (other.hasCloseReason()) {
          setCloseReason(other.getCloseReason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayer.KickOffPlayerCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayer.KickOffPlayerCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int closeReason_ = 0;
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @return Whether the closeReason field is set.
       */
      @java.lang.Override public boolean hasCloseReason() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @return The closeReason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SessionCloseReason getCloseReason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SessionCloseReason result = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(closeReason_);
        return result == null ? com.yorha.proto.CommonEnum.SessionCloseReason.SCR_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @param value The closeReason to set.
       * @return This builder for chaining.
       */
      public Builder setCloseReason(com.yorha.proto.CommonEnum.SessionCloseReason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        closeReason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason closeReason = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCloseReason() {
        bitField0_ = (bitField0_ & ~0x00000001);
        closeReason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KickOffPlayerCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KickOffPlayerCmd)
    private static final com.yorha.proto.SsPlayer.KickOffPlayerCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayer.KickOffPlayerCmd();
    }

    public static com.yorha.proto.SsPlayer.KickOffPlayerCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KickOffPlayerCmd>
        PARSER = new com.google.protobuf.AbstractParser<KickOffPlayerCmd>() {
      @java.lang.Override
      public KickOffPlayerCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KickOffPlayerCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KickOffPlayerCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KickOffPlayerCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayer.KickOffPlayerCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ApplyDataPatchCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ApplyDataPatchCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LoginPlayerCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LoginPlayerCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnPlayerDisconnectCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnPlayerDisconnectCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_RecvClientMsgCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_RecvClientMsgCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KickOffPlayerCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KickOffPlayerCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&ss_proto/gen/player/ss/ss_player.proto" +
      "\022\017com.yorha.proto\032%ss_proto/gen/common/c" +
      "ommon_enum.proto\032$ss_proto/gen/common/co" +
      "mmon_msg.proto\"$\n\021ApplyDataPatchCmd\022\017\n\007v" +
      "ersion\030\001 \001(\005\"\321\001\n\016LoginPlayerCmd\022\016\n\006openI" +
      "d\030\001 \001(\t\022;\n\020sessionParamInfo\030\002 \001(\0132!.com." +
      "yorha.proto.SessionParamInfo\022\030\n\020debugSta" +
      "rtNewbie\030\003 \001(\010\0221\n\016actualLanguage\030\004 \001(\0162\031" +
      ".com.yorha.proto.Language\022\017\n\007midasPf\030\005 \001" +
      "(\t\022\024\n\014intlNtfToken\030\006 \001(\t\"Q\n\025OnPlayerDisc" +
      "onnectCmd\0228\n\013closeReason\030\001 \001(\0162#.com.yor" +
      "ha.proto.SessionCloseReason\"D\n\020RecvClien" +
      "tMsgCmd\022\017\n\007msgType\030\001 \001(\005\022\r\n\005seqId\030\002 \001(\005\022" +
      "\020\n\010msgBytes\030\003 \001(\014\"L\n\020KickOffPlayerCmd\0228\n" +
      "\013closeReason\030\001 \001(\0162#.com.yorha.proto.Ses" +
      "sionCloseReasonB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_ApplyDataPatchCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ApplyDataPatchCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ApplyDataPatchCmd_descriptor,
        new java.lang.String[] { "Version", });
    internal_static_com_yorha_proto_LoginPlayerCmd_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_LoginPlayerCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LoginPlayerCmd_descriptor,
        new java.lang.String[] { "OpenId", "SessionParamInfo", "DebugStartNewbie", "ActualLanguage", "MidasPf", "IntlNtfToken", });
    internal_static_com_yorha_proto_OnPlayerDisconnectCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_OnPlayerDisconnectCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnPlayerDisconnectCmd_descriptor,
        new java.lang.String[] { "CloseReason", });
    internal_static_com_yorha_proto_RecvClientMsgCmd_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_RecvClientMsgCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_RecvClientMsgCmd_descriptor,
        new java.lang.String[] { "MsgType", "SeqId", "MsgBytes", });
    internal_static_com_yorha_proto_KickOffPlayerCmd_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_KickOffPlayerCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KickOffPlayerCmd_descriptor,
        new java.lang.String[] { "CloseReason", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_milestone.proto

package com.yorha.proto;

public final class PlayerMilestone {
  private PlayerMilestone() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_TakeMileStoneReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TakeMileStoneReward_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 里程碑Id
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return Whether the mileStoneId field is set.
     */
    boolean hasMileStoneId();
    /**
     * <pre>
     * 里程碑Id
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return The mileStoneId.
     */
    int getMileStoneId();
  }
  /**
   * <pre>
   * 领取里程碑奖励
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_TakeMileStoneReward_C2S}
   */
  public static final class Player_TakeMileStoneReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TakeMileStoneReward_C2S)
      Player_TakeMileStoneReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TakeMileStoneReward_C2S.newBuilder() to construct.
    private Player_TakeMileStoneReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TakeMileStoneReward_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TakeMileStoneReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TakeMileStoneReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              mileStoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S.class, com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int MILESTONEID_FIELD_NUMBER = 1;
    private int mileStoneId_;
    /**
     * <pre>
     * 里程碑Id
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return Whether the mileStoneId field is set.
     */
    @java.lang.Override
    public boolean hasMileStoneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 里程碑Id
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return The mileStoneId.
     */
    @java.lang.Override
    public int getMileStoneId() {
      return mileStoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, mileStoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mileStoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S other = (com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S) obj;

      if (hasMileStoneId() != other.hasMileStoneId()) return false;
      if (hasMileStoneId()) {
        if (getMileStoneId()
            != other.getMileStoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMileStoneId()) {
        hash = (37 * hash) + MILESTONEID_FIELD_NUMBER;
        hash = (53 * hash) + getMileStoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 领取里程碑奖励
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_TakeMileStoneReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TakeMileStoneReward_C2S)
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S.class, com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        mileStoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S build() {
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S buildPartial() {
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S result = new com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.mileStoneId_ = mileStoneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S other) {
        if (other == com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S.getDefaultInstance()) return this;
        if (other.hasMileStoneId()) {
          setMileStoneId(other.getMileStoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int mileStoneId_ ;
      /**
       * <pre>
       * 里程碑Id
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return Whether the mileStoneId field is set.
       */
      @java.lang.Override
      public boolean hasMileStoneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 里程碑Id
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return The mileStoneId.
       */
      @java.lang.Override
      public int getMileStoneId() {
        return mileStoneId_;
      }
      /**
       * <pre>
       * 里程碑Id
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @param value The mileStoneId to set.
       * @return This builder for chaining.
       */
      public Builder setMileStoneId(int value) {
        bitField0_ |= 0x00000001;
        mileStoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 里程碑Id
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMileStoneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        mileStoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TakeMileStoneReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TakeMileStoneReward_C2S)
    private static final com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S();
    }

    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TakeMileStoneReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_TakeMileStoneReward_C2S>() {
      @java.lang.Override
      public Player_TakeMileStoneReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TakeMileStoneReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TakeMileStoneReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TakeMileStoneReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_TakeMileStoneReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_TakeMileStoneReward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return Whether the rewardInfo field is set.
     */
    boolean hasRewardInfo();
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return The rewardInfo.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo();
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_TakeMileStoneReward_S2C}
   */
  public static final class Player_TakeMileStoneReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_TakeMileStoneReward_S2C)
      Player_TakeMileStoneReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_TakeMileStoneReward_S2C.newBuilder() to construct.
    private Player_TakeMileStoneReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_TakeMileStoneReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_TakeMileStoneReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_TakeMileStoneReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = rewardInfo_.toBuilder();
              }
              rewardInfo_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(rewardInfo_);
                rewardInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C.class, com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARDINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB rewardInfo_;
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return Whether the rewardInfo field is set.
     */
    @java.lang.Override
    public boolean hasRewardInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     * @return The rewardInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo() {
      return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
    }
    /**
     * <pre>
     * 奖励信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder() {
      return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getRewardInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getRewardInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C other = (com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C) obj;

      if (hasRewardInfo() != other.hasRewardInfo()) return false;
      if (hasRewardInfo()) {
        if (!getRewardInfo()
            .equals(other.getRewardInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRewardInfo()) {
        hash = (37 * hash) + REWARDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRewardInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_TakeMileStoneReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_TakeMileStoneReward_S2C)
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C.class, com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = null;
        } else {
          rewardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C build() {
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C buildPartial() {
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C result = new com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardInfoBuilder_ == null) {
            result.rewardInfo_ = rewardInfo_;
          } else {
            result.rewardInfo_ = rewardInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C other) {
        if (other == com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C.getDefaultInstance()) return this;
        if (other.hasRewardInfo()) {
          mergeRewardInfo(other.getRewardInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB rewardInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardInfoBuilder_;
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       * @return Whether the rewardInfo field is set.
       */
      public boolean hasRewardInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       * @return The rewardInfo.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getRewardInfo() {
        if (rewardInfoBuilder_ == null) {
          return rewardInfo_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
        } else {
          return rewardInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder setRewardInfo(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          rewardInfo_ = value;
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder setRewardInfo(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = builderForValue.build();
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder mergeRewardInfo(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              rewardInfo_ != null &&
              rewardInfo_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            rewardInfo_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(rewardInfo_).mergeFrom(value).buildPartial();
          } else {
            rewardInfo_ = value;
          }
          onChanged();
        } else {
          rewardInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public Builder clearRewardInfo() {
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = null;
          onChanged();
        } else {
          rewardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardInfoOrBuilder() {
        if (rewardInfoBuilder_ != null) {
          return rewardInfoBuilder_.getMessageOrBuilder();
        } else {
          return rewardInfo_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : rewardInfo_;
        }
      }
      /**
       * <pre>
       * 奖励信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.YoAssetPackagePB rewardInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardInfoFieldBuilder() {
        if (rewardInfoBuilder_ == null) {
          rewardInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getRewardInfo(),
                  getParentForChildren(),
                  isClean());
          rewardInfo_ = null;
        }
        return rewardInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_TakeMileStoneReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_TakeMileStoneReward_S2C)
    private static final com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C();
    }

    public static com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_TakeMileStoneReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_TakeMileStoneReward_S2C>() {
      @java.lang.Override
      public Player_TakeMileStoneReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_TakeMileStoneReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_TakeMileStoneReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_TakeMileStoneReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMilestone.Player_TakeMileStoneReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetMileStoneHistory_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetMileStoneHistory_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   * 获取里程碑数据
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_GetMileStoneHistory_C2S}
   */
  public static final class Player_GetMileStoneHistory_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetMileStoneHistory_C2S)
      Player_GetMileStoneHistory_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetMileStoneHistory_C2S.newBuilder() to construct.
    private Player_GetMileStoneHistory_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetMileStoneHistory_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetMileStoneHistory_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetMileStoneHistory_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S other = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 获取里程碑数据
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_GetMileStoneHistory_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetMileStoneHistory_C2S)
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S build() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S buildPartial() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S result = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S) {
          return mergeFrom((com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S other) {
        if (other == com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetMileStoneHistory_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetMileStoneHistory_C2S)
    private static final com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S();
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetMileStoneHistory_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetMileStoneHistory_C2S>() {
      @java.lang.Override
      public Player_GetMileStoneHistory_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetMileStoneHistory_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetMileStoneHistory_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetMileStoneHistory_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetMileStoneHistory_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetMileStoneHistory_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */
    int getMileStoneDataCount();
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */
    boolean containsMileStoneData(
        int key);
    /**
     * Use {@link #getMileStoneDataMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
    getMileStoneData();
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
    getMileStoneDataMap();
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */

    com.yorha.proto.CommonMsg.MileStoneData getMileStoneDataOrDefault(
        int key,
        com.yorha.proto.CommonMsg.MileStoneData defaultValue);
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */

    com.yorha.proto.CommonMsg.MileStoneData getMileStoneDataOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetMileStoneHistory_S2C}
   */
  public static final class Player_GetMileStoneHistory_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetMileStoneHistory_S2C)
      Player_GetMileStoneHistory_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetMileStoneHistory_S2C.newBuilder() to construct.
    private Player_GetMileStoneHistory_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetMileStoneHistory_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetMileStoneHistory_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetMileStoneHistory_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                mileStoneData_ = com.google.protobuf.MapField.newMapField(
                    MileStoneDataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
              mileStoneData__ = input.readMessage(
                  MileStoneDataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              mileStoneData_.getMutableMap().put(
                  mileStoneData__.getKey(), mileStoneData__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetMileStoneData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C.Builder.class);
    }

    public static final int MILESTONEDATA_FIELD_NUMBER = 1;
    private static final class MileStoneDataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>newDefaultInstance(
                  com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_MileStoneDataEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.CommonMsg.MileStoneData.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> mileStoneData_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
    internalGetMileStoneData() {
      if (mileStoneData_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            MileStoneDataDefaultEntryHolder.defaultEntry);
      }
      return mileStoneData_;
    }

    public int getMileStoneDataCount() {
      return internalGetMileStoneData().getMap().size();
    }
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */

    @java.lang.Override
    public boolean containsMileStoneData(
        int key) {
      
      return internalGetMileStoneData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getMileStoneDataMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> getMileStoneData() {
      return getMileStoneDataMap();
    }
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> getMileStoneDataMap() {
      return internalGetMileStoneData().getMap();
    }
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.MileStoneData getMileStoneDataOrDefault(
        int key,
        com.yorha.proto.CommonMsg.MileStoneData defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> map =
          internalGetMileStoneData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 里程碑数据
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.MileStoneData getMileStoneDataOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> map =
          internalGetMileStoneData().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetMileStoneData(),
          MileStoneDataDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> entry
           : internalGetMileStoneData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
        mileStoneData__ = MileStoneDataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, mileStoneData__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C other = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C) obj;

      if (!internalGetMileStoneData().equals(
          other.internalGetMileStoneData())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetMileStoneData().getMap().isEmpty()) {
        hash = (37 * hash) + MILESTONEDATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetMileStoneData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetMileStoneHistory_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetMileStoneHistory_S2C)
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMileStoneData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableMileStoneData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableMileStoneData().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C build() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C buildPartial() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C result = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C(this);
        int from_bitField0_ = bitField0_;
        result.mileStoneData_ = internalGetMileStoneData();
        result.mileStoneData_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C) {
          return mergeFrom((com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C other) {
        if (other == com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C.getDefaultInstance()) return this;
        internalGetMutableMileStoneData().mergeFrom(
            other.internalGetMileStoneData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> mileStoneData_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
      internalGetMileStoneData() {
        if (mileStoneData_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              MileStoneDataDefaultEntryHolder.defaultEntry);
        }
        return mileStoneData_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
      internalGetMutableMileStoneData() {
        onChanged();;
        if (mileStoneData_ == null) {
          mileStoneData_ = com.google.protobuf.MapField.newMapField(
              MileStoneDataDefaultEntryHolder.defaultEntry);
        }
        if (!mileStoneData_.isMutable()) {
          mileStoneData_ = mileStoneData_.copy();
        }
        return mileStoneData_;
      }

      public int getMileStoneDataCount() {
        return internalGetMileStoneData().getMap().size();
      }
      /**
       * <pre>
       * 里程碑数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
       */

      @java.lang.Override
      public boolean containsMileStoneData(
          int key) {
        
        return internalGetMileStoneData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getMileStoneDataMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> getMileStoneData() {
        return getMileStoneDataMap();
      }
      /**
       * <pre>
       * 里程碑数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> getMileStoneDataMap() {
        return internalGetMileStoneData().getMap();
      }
      /**
       * <pre>
       * 里程碑数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.MileStoneData getMileStoneDataOrDefault(
          int key,
          com.yorha.proto.CommonMsg.MileStoneData defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> map =
            internalGetMileStoneData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 里程碑数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.MileStoneData getMileStoneDataOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> map =
            internalGetMileStoneData().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearMileStoneData() {
        internalGetMutableMileStoneData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 里程碑数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
       */

      public Builder removeMileStoneData(
          int key) {
        
        internalGetMutableMileStoneData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData>
      getMutableMileStoneData() {
        return internalGetMutableMileStoneData().getMutableMap();
      }
      /**
       * <pre>
       * 里程碑数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
       */
      public Builder putMileStoneData(
          int key,
          com.yorha.proto.CommonMsg.MileStoneData value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableMileStoneData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 里程碑数据
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneData&gt; mileStoneData = 1;</code>
       */

      public Builder putAllMileStoneData(
          java.util.Map<java.lang.Integer, com.yorha.proto.CommonMsg.MileStoneData> values) {
        internalGetMutableMileStoneData().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetMileStoneHistory_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetMileStoneHistory_S2C)
    private static final com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C();
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetMileStoneHistory_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetMileStoneHistory_S2C>() {
      @java.lang.Override
      public Player_GetMileStoneHistory_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetMileStoneHistory_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetMileStoneHistory_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetMileStoneHistory_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMilestone.Player_GetMileStoneHistory_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetMileStoneRank_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetMileStoneRank_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 里程碑
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return Whether the mileStoneId field is set.
     */
    boolean hasMileStoneId();
    /**
     * <pre>
     * 里程碑
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return The mileStoneId.
     */
    int getMileStoneId();
  }
  /**
   * <pre>
   * 获取里程碑排行信息
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_GetMileStoneRank_C2S}
   */
  public static final class Player_GetMileStoneRank_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetMileStoneRank_C2S)
      Player_GetMileStoneRank_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetMileStoneRank_C2S.newBuilder() to construct.
    private Player_GetMileStoneRank_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetMileStoneRank_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetMileStoneRank_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetMileStoneRank_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              mileStoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int MILESTONEID_FIELD_NUMBER = 1;
    private int mileStoneId_;
    /**
     * <pre>
     * 里程碑
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return Whether the mileStoneId field is set.
     */
    @java.lang.Override
    public boolean hasMileStoneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 里程碑
     * </pre>
     *
     * <code>optional int32 mileStoneId = 1;</code>
     * @return The mileStoneId.
     */
    @java.lang.Override
    public int getMileStoneId() {
      return mileStoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, mileStoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mileStoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S other = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S) obj;

      if (hasMileStoneId() != other.hasMileStoneId()) return false;
      if (hasMileStoneId()) {
        if (getMileStoneId()
            != other.getMileStoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMileStoneId()) {
        hash = (37 * hash) + MILESTONEID_FIELD_NUMBER;
        hash = (53 * hash) + getMileStoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 获取里程碑排行信息
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_GetMileStoneRank_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetMileStoneRank_C2S)
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        mileStoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S build() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S buildPartial() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S result = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.mileStoneId_ = mileStoneId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S) {
          return mergeFrom((com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S other) {
        if (other == com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S.getDefaultInstance()) return this;
        if (other.hasMileStoneId()) {
          setMileStoneId(other.getMileStoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int mileStoneId_ ;
      /**
       * <pre>
       * 里程碑
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return Whether the mileStoneId field is set.
       */
      @java.lang.Override
      public boolean hasMileStoneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 里程碑
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return The mileStoneId.
       */
      @java.lang.Override
      public int getMileStoneId() {
        return mileStoneId_;
      }
      /**
       * <pre>
       * 里程碑
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @param value The mileStoneId to set.
       * @return This builder for chaining.
       */
      public Builder setMileStoneId(int value) {
        bitField0_ |= 0x00000001;
        mileStoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 里程碑
       * </pre>
       *
       * <code>optional int32 mileStoneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMileStoneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        mileStoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetMileStoneRank_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetMileStoneRank_C2S)
    private static final com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S();
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetMileStoneRank_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetMileStoneRank_C2S>() {
      @java.lang.Override
      public Player_GetMileStoneRank_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetMileStoneRank_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetMileStoneRank_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetMileStoneRank_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetMileStoneRank_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetMileStoneRank_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */
    int getRankInfoCount();
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */
    boolean containsRankInfo(
        long key);
    /**
     * Use {@link #getRankInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
    getRankInfo();
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
    getRankInfoMap();
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */

    com.yorha.proto.StructPB.MileStoneClanInfoPB getRankInfoOrDefault(
        long key,
        com.yorha.proto.StructPB.MileStoneClanInfoPB defaultValue);
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */

    com.yorha.proto.StructPB.MileStoneClanInfoPB getRankInfoOrThrow(
        long key);

    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */
    int getZoneRankInfoCount();
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */
    boolean containsZoneRankInfo(
        int key);
    /**
     * Use {@link #getZoneRankInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
    getZoneRankInfo();
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
    getZoneRankInfoMap();
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */

    com.yorha.proto.StructPB.MileStoneZoneInfoPB getZoneRankInfoOrDefault(
        int key,
        com.yorha.proto.StructPB.MileStoneZoneInfoPB defaultValue);
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */

    com.yorha.proto.StructPB.MileStoneZoneInfoPB getZoneRankInfoOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetMileStoneRank_S2C}
   */
  public static final class Player_GetMileStoneRank_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetMileStoneRank_S2C)
      Player_GetMileStoneRank_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetMileStoneRank_S2C.newBuilder() to construct.
    private Player_GetMileStoneRank_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetMileStoneRank_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetMileStoneRank_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetMileStoneRank_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rankInfo_ = com.google.protobuf.MapField.newMapField(
                    RankInfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
              rankInfo__ = input.readMessage(
                  RankInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              rankInfo_.getMutableMap().put(
                  rankInfo__.getKey(), rankInfo__.getValue());
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                zoneRankInfo_ = com.google.protobuf.MapField.newMapField(
                    ZoneRankInfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
              zoneRankInfo__ = input.readMessage(
                  ZoneRankInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              zoneRankInfo_.getMutableMap().put(
                  zoneRankInfo__.getKey(), zoneRankInfo__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetRankInfo();
        case 2:
          return internalGetZoneRankInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C.Builder.class);
    }

    public static final int RANKINFO_FIELD_NUMBER = 1;
    private static final class RankInfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>newDefaultInstance(
                  com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_RankInfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructPB.MileStoneClanInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> rankInfo_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
    internalGetRankInfo() {
      if (rankInfo_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            RankInfoDefaultEntryHolder.defaultEntry);
      }
      return rankInfo_;
    }

    public int getRankInfoCount() {
      return internalGetRankInfo().getMap().size();
    }
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */

    @java.lang.Override
    public boolean containsRankInfo(
        long key) {
      
      return internalGetRankInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getRankInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> getRankInfo() {
      return getRankInfoMap();
    }
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> getRankInfoMap() {
      return internalGetRankInfo().getMap();
    }
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.MileStoneClanInfoPB getRankInfoOrDefault(
        long key,
        com.yorha.proto.StructPB.MileStoneClanInfoPB defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> map =
          internalGetRankInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 联盟Id2联盟详情
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.MileStoneClanInfoPB getRankInfoOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> map =
          internalGetRankInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int ZONERANKINFO_FIELD_NUMBER = 2;
    private static final class ZoneRankInfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>newDefaultInstance(
                  com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_ZoneRankInfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructPB.MileStoneZoneInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> zoneRankInfo_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
    internalGetZoneRankInfo() {
      if (zoneRankInfo_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ZoneRankInfoDefaultEntryHolder.defaultEntry);
      }
      return zoneRankInfo_;
    }

    public int getZoneRankInfoCount() {
      return internalGetZoneRankInfo().getMap().size();
    }
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */

    @java.lang.Override
    public boolean containsZoneRankInfo(
        int key) {
      
      return internalGetZoneRankInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getZoneRankInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> getZoneRankInfo() {
      return getZoneRankInfoMap();
    }
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> getZoneRankInfoMap() {
      return internalGetZoneRankInfo().getMap();
    }
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.MileStoneZoneInfoPB getZoneRankInfoOrDefault(
        int key,
        com.yorha.proto.StructPB.MileStoneZoneInfoPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> map =
          internalGetZoneRankInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 王国Id2王国详情
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructPB.MileStoneZoneInfoPB getZoneRankInfoOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> map =
          internalGetZoneRankInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetRankInfo(),
          RankInfoDefaultEntryHolder.defaultEntry,
          1);
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetZoneRankInfo(),
          ZoneRankInfoDefaultEntryHolder.defaultEntry,
          2);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> entry
           : internalGetRankInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
        rankInfo__ = RankInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, rankInfo__);
      }
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> entry
           : internalGetZoneRankInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
        zoneRankInfo__ = ZoneRankInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, zoneRankInfo__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C other = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C) obj;

      if (!internalGetRankInfo().equals(
          other.internalGetRankInfo())) return false;
      if (!internalGetZoneRankInfo().equals(
          other.internalGetZoneRankInfo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetRankInfo().getMap().isEmpty()) {
        hash = (37 * hash) + RANKINFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetRankInfo().hashCode();
      }
      if (!internalGetZoneRankInfo().getMap().isEmpty()) {
        hash = (37 * hash) + ZONERANKINFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetZoneRankInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetMileStoneRank_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetMileStoneRank_S2C)
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetRankInfo();
          case 2:
            return internalGetZoneRankInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableRankInfo();
          case 2:
            return internalGetMutableZoneRankInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C.class, com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableRankInfo().clear();
        internalGetMutableZoneRankInfo().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMilestone.internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C build() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C buildPartial() {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C result = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C(this);
        int from_bitField0_ = bitField0_;
        result.rankInfo_ = internalGetRankInfo();
        result.rankInfo_.makeImmutable();
        result.zoneRankInfo_ = internalGetZoneRankInfo();
        result.zoneRankInfo_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C) {
          return mergeFrom((com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C other) {
        if (other == com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C.getDefaultInstance()) return this;
        internalGetMutableRankInfo().mergeFrom(
            other.internalGetRankInfo());
        internalGetMutableZoneRankInfo().mergeFrom(
            other.internalGetZoneRankInfo());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> rankInfo_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
      internalGetRankInfo() {
        if (rankInfo_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              RankInfoDefaultEntryHolder.defaultEntry);
        }
        return rankInfo_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
      internalGetMutableRankInfo() {
        onChanged();;
        if (rankInfo_ == null) {
          rankInfo_ = com.google.protobuf.MapField.newMapField(
              RankInfoDefaultEntryHolder.defaultEntry);
        }
        if (!rankInfo_.isMutable()) {
          rankInfo_ = rankInfo_.copy();
        }
        return rankInfo_;
      }

      public int getRankInfoCount() {
        return internalGetRankInfo().getMap().size();
      }
      /**
       * <pre>
       * 联盟Id2联盟详情
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
       */

      @java.lang.Override
      public boolean containsRankInfo(
          long key) {
        
        return internalGetRankInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getRankInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> getRankInfo() {
        return getRankInfoMap();
      }
      /**
       * <pre>
       * 联盟Id2联盟详情
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> getRankInfoMap() {
        return internalGetRankInfo().getMap();
      }
      /**
       * <pre>
       * 联盟Id2联盟详情
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.MileStoneClanInfoPB getRankInfoOrDefault(
          long key,
          com.yorha.proto.StructPB.MileStoneClanInfoPB defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> map =
            internalGetRankInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 联盟Id2联盟详情
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.MileStoneClanInfoPB getRankInfoOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> map =
            internalGetRankInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearRankInfo() {
        internalGetMutableRankInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 联盟Id2联盟详情
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
       */

      public Builder removeRankInfo(
          long key) {
        
        internalGetMutableRankInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB>
      getMutableRankInfo() {
        return internalGetMutableRankInfo().getMutableMap();
      }
      /**
       * <pre>
       * 联盟Id2联盟详情
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
       */
      public Builder putRankInfo(
          long key,
          com.yorha.proto.StructPB.MileStoneClanInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableRankInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 联盟Id2联盟详情
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.MileStoneClanInfoPB&gt; rankInfo = 1;</code>
       */

      public Builder putAllRankInfo(
          java.util.Map<java.lang.Long, com.yorha.proto.StructPB.MileStoneClanInfoPB> values) {
        internalGetMutableRankInfo().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> zoneRankInfo_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
      internalGetZoneRankInfo() {
        if (zoneRankInfo_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ZoneRankInfoDefaultEntryHolder.defaultEntry);
        }
        return zoneRankInfo_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
      internalGetMutableZoneRankInfo() {
        onChanged();;
        if (zoneRankInfo_ == null) {
          zoneRankInfo_ = com.google.protobuf.MapField.newMapField(
              ZoneRankInfoDefaultEntryHolder.defaultEntry);
        }
        if (!zoneRankInfo_.isMutable()) {
          zoneRankInfo_ = zoneRankInfo_.copy();
        }
        return zoneRankInfo_;
      }

      public int getZoneRankInfoCount() {
        return internalGetZoneRankInfo().getMap().size();
      }
      /**
       * <pre>
       * 王国Id2王国详情
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
       */

      @java.lang.Override
      public boolean containsZoneRankInfo(
          int key) {
        
        return internalGetZoneRankInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getZoneRankInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> getZoneRankInfo() {
        return getZoneRankInfoMap();
      }
      /**
       * <pre>
       * 王国Id2王国详情
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> getZoneRankInfoMap() {
        return internalGetZoneRankInfo().getMap();
      }
      /**
       * <pre>
       * 王国Id2王国详情
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.MileStoneZoneInfoPB getZoneRankInfoOrDefault(
          int key,
          com.yorha.proto.StructPB.MileStoneZoneInfoPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> map =
            internalGetZoneRankInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 王国Id2王国详情
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructPB.MileStoneZoneInfoPB getZoneRankInfoOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> map =
            internalGetZoneRankInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearZoneRankInfo() {
        internalGetMutableZoneRankInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 王国Id2王国详情
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
       */

      public Builder removeZoneRankInfo(
          int key) {
        
        internalGetMutableZoneRankInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB>
      getMutableZoneRankInfo() {
        return internalGetMutableZoneRankInfo().getMutableMap();
      }
      /**
       * <pre>
       * 王国Id2王国详情
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
       */
      public Builder putZoneRankInfo(
          int key,
          com.yorha.proto.StructPB.MileStoneZoneInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableZoneRankInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 王国Id2王国详情
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.MileStoneZoneInfoPB&gt; zoneRankInfo = 2;</code>
       */

      public Builder putAllZoneRankInfo(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructPB.MileStoneZoneInfoPB> values) {
        internalGetMutableZoneRankInfo().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetMileStoneRank_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetMileStoneRank_S2C)
    private static final com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C();
    }

    public static com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetMileStoneRank_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetMileStoneRank_S2C>() {
      @java.lang.Override
      public Player_GetMileStoneRank_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetMileStoneRank_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetMileStoneRank_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetMileStoneRank_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMilestone.Player_GetMileStoneRank_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_MileStoneDataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_MileStoneDataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_RankInfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_RankInfoEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_ZoneRankInfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_ZoneRankInfoEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-ss_proto/gen/player/cs/player_mileston" +
      "e.proto\022\017com.yorha.proto\032\"cs_proto/gen/c" +
      "ommon/structPB.proto\032$ss_proto/gen/commo" +
      "n/common_msg.proto\"5\n\036Player_TakeMileSto" +
      "neReward_C2S\022\023\n\013mileStoneId\030\001 \001(\005\"W\n\036Pla" +
      "yer_TakeMileStoneReward_S2C\0225\n\nrewardInf" +
      "o\030\001 \001(\0132!.com.yorha.proto.YoAssetPackage" +
      "PB\" \n\036Player_GetMileStoneHistory_C2S\"\321\001\n" +
      "\036Player_GetMileStoneHistory_S2C\022Y\n\rmileS" +
      "toneData\030\001 \003(\0132B.com.yorha.proto.Player_" +
      "GetMileStoneHistory_S2C.MileStoneDataEnt" +
      "ry\032T\n\022MileStoneDataEntry\022\013\n\003key\030\001 \001(\005\022-\n" +
      "\005value\030\002 \001(\0132\036.com.yorha.proto.MileStone" +
      "Data:\0028\001\"2\n\033Player_GetMileStoneRank_C2S\022" +
      "\023\n\013mileStoneId\030\001 \001(\005\"\363\002\n\033Player_GetMileS" +
      "toneRank_S2C\022L\n\010rankInfo\030\001 \003(\0132:.com.yor" +
      "ha.proto.Player_GetMileStoneRank_S2C.Ran" +
      "kInfoEntry\022T\n\014zoneRankInfo\030\002 \003(\0132>.com.y" +
      "orha.proto.Player_GetMileStoneRank_S2C.Z" +
      "oneRankInfoEntry\032U\n\rRankInfoEntry\022\013\n\003key" +
      "\030\001 \001(\003\0223\n\005value\030\002 \001(\0132$.com.yorha.proto." +
      "MileStoneClanInfoPB:\0028\001\032Y\n\021ZoneRankInfoE" +
      "ntry\022\013\n\003key\030\001 \001(\005\0223\n\005value\030\002 \001(\0132$.com.y" +
      "orha.proto.MileStoneZoneInfoPB:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TakeMileStoneReward_C2S_descriptor,
        new java.lang.String[] { "MileStoneId", });
    internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_TakeMileStoneReward_S2C_descriptor,
        new java.lang.String[] { "RewardInfo", });
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetMileStoneHistory_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_descriptor,
        new java.lang.String[] { "MileStoneData", });
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_MileStoneDataEntry_descriptor =
      internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_MileStoneDataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetMileStoneHistory_S2C_MileStoneDataEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetMileStoneRank_C2S_descriptor,
        new java.lang.String[] { "MileStoneId", });
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor,
        new java.lang.String[] { "RankInfo", "ZoneRankInfo", });
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_RankInfoEntry_descriptor =
      internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_RankInfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_RankInfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_ZoneRankInfoEntry_descriptor =
      internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_descriptor.getNestedTypes().get(1);
    internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_ZoneRankInfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetMileStoneRank_S2C_ZoneRankInfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

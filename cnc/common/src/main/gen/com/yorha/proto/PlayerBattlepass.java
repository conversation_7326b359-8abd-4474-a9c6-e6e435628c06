// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_battlepass.proto

package com.yorha.proto;

public final class PlayerBattlepass {
  private PlayerBattlepass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_ObtainTaskExp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainTaskExp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return Whether the bpTaskId field is set.
     */
    boolean hasBpTaskId();
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return The bpTaskId.
     */
    int getBpTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainTaskExp_C2S}
   */
  public static final class Player_ObtainTaskExp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainTaskExp_C2S)
      Player_ObtainTaskExp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainTaskExp_C2S.newBuilder() to construct.
    private Player_ObtainTaskExp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainTaskExp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainTaskExp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainTaskExp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              bpTaskId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int BPTASKID_FIELD_NUMBER = 1;
    private int bpTaskId_;
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return Whether the bpTaskId field is set.
     */
    @java.lang.Override
    public boolean hasBpTaskId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return The bpTaskId.
     */
    @java.lang.Override
    public int getBpTaskId() {
      return bpTaskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, bpTaskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, bpTaskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S other = (com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S) obj;

      if (hasBpTaskId() != other.hasBpTaskId()) return false;
      if (hasBpTaskId()) {
        if (getBpTaskId()
            != other.getBpTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBpTaskId()) {
        hash = (37 * hash) + BPTASKID_FIELD_NUMBER;
        hash = (53 * hash) + getBpTaskId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainTaskExp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainTaskExp_C2S)
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bpTaskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S result = new com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.bpTaskId_ = bpTaskId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S.getDefaultInstance()) return this;
        if (other.hasBpTaskId()) {
          setBpTaskId(other.getBpTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int bpTaskId_ ;
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @return Whether the bpTaskId field is set.
       */
      @java.lang.Override
      public boolean hasBpTaskId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @return The bpTaskId.
       */
      @java.lang.Override
      public int getBpTaskId() {
        return bpTaskId_;
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @param value The bpTaskId to set.
       * @return This builder for chaining.
       */
      public Builder setBpTaskId(int value) {
        bitField0_ |= 0x00000001;
        bpTaskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBpTaskId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        bpTaskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainTaskExp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainTaskExp_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainTaskExp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainTaskExp_C2S>() {
      @java.lang.Override
      public Player_ObtainTaskExp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainTaskExp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainTaskExp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainTaskExp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainTaskExp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainTaskExp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainTaskExp_S2C}
   */
  public static final class Player_ObtainTaskExp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainTaskExp_S2C)
      Player_ObtainTaskExp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainTaskExp_S2C.newBuilder() to construct.
    private Player_ObtainTaskExp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainTaskExp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainTaskExp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainTaskExp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C other = (com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainTaskExp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainTaskExp_S2C)
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C result = new com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainTaskExp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainTaskExp_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainTaskExp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainTaskExp_S2C>() {
      @java.lang.Override
      public Player_ObtainTaskExp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainTaskExp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainTaskExp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainTaskExp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainTaskExp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainExpBox_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainExpBox_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainExpBox_C2S}
   */
  public static final class Player_ObtainExpBox_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainExpBox_C2S)
      Player_ObtainExpBox_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainExpBox_C2S.newBuilder() to construct.
    private Player_ObtainExpBox_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainExpBox_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainExpBox_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainExpBox_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S other = (com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainExpBox_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainExpBox_C2S)
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S result = new com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainExpBox_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainExpBox_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainExpBox_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainExpBox_C2S>() {
      @java.lang.Override
      public Player_ObtainExpBox_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainExpBox_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainExpBox_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainExpBox_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainExpBox_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainExpBox_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainExpBox_S2C}
   */
  public static final class Player_ObtainExpBox_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainExpBox_S2C)
      Player_ObtainExpBox_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainExpBox_S2C.newBuilder() to construct.
    private Player_ObtainExpBox_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainExpBox_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainExpBox_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainExpBox_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C other = (com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainExpBox_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainExpBox_S2C)
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C result = new com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainExpBox_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainExpBox_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainExpBox_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainExpBox_S2C>() {
      @java.lang.Override
      public Player_ObtainExpBox_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainExpBox_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainExpBox_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainExpBox_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainExpBox_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainLevelReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainLevelReward_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return Whether the isAll field is set.
     */
    boolean hasIsAll();
    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return The isAll.
     */
    boolean getIsAll();

    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return Whether the level field is set.
     */
    boolean hasLevel();
    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return Whether the extraReward field is set.
     */
    boolean hasExtraReward();
    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return The extraReward.
     */
    boolean getExtraReward();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainLevelReward_C2S}
   */
  public static final class Player_ObtainLevelReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainLevelReward_C2S)
      Player_ObtainLevelReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainLevelReward_C2S.newBuilder() to construct.
    private Player_ObtainLevelReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainLevelReward_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainLevelReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainLevelReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isAll_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              level_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              extraReward_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ISALL_FIELD_NUMBER = 1;
    private boolean isAll_;
    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return Whether the isAll field is set.
     */
    @java.lang.Override
    public boolean hasIsAll() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return The isAll.
     */
    @java.lang.Override
    public boolean getIsAll() {
      return isAll_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int EXTRAREWARD_FIELD_NUMBER = 3;
    private boolean extraReward_;
    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return Whether the extraReward field is set.
     */
    @java.lang.Override
    public boolean hasExtraReward() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return The extraReward.
     */
    @java.lang.Override
    public boolean getExtraReward() {
      return extraReward_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isAll_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, level_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, extraReward_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isAll_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, extraReward_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S other = (com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S) obj;

      if (hasIsAll() != other.hasIsAll()) return false;
      if (hasIsAll()) {
        if (getIsAll()
            != other.getIsAll()) return false;
      }
      if (hasLevel() != other.hasLevel()) return false;
      if (hasLevel()) {
        if (getLevel()
            != other.getLevel()) return false;
      }
      if (hasExtraReward() != other.hasExtraReward()) return false;
      if (hasExtraReward()) {
        if (getExtraReward()
            != other.getExtraReward()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsAll()) {
        hash = (37 * hash) + ISALL_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsAll());
      }
      if (hasLevel()) {
        hash = (37 * hash) + LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getLevel();
      }
      if (hasExtraReward()) {
        hash = (37 * hash) + EXTRAREWARD_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getExtraReward());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainLevelReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainLevelReward_C2S)
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isAll_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        extraReward_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S result = new com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isAll_ = isAll_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.level_ = level_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.extraReward_ = extraReward_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S.getDefaultInstance()) return this;
        if (other.hasIsAll()) {
          setIsAll(other.getIsAll());
        }
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        if (other.hasExtraReward()) {
          setExtraReward(other.getExtraReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isAll_ ;
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @return Whether the isAll field is set.
       */
      @java.lang.Override
      public boolean hasIsAll() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @return The isAll.
       */
      @java.lang.Override
      public boolean getIsAll() {
        return isAll_;
      }
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @param value The isAll to set.
       * @return This builder for chaining.
       */
      public Builder setIsAll(boolean value) {
        bitField0_ |= 0x00000001;
        isAll_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsAll() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isAll_ = false;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @return Whether the level field is set.
       */
      @java.lang.Override
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000002;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        level_ = 0;
        onChanged();
        return this;
      }

      private boolean extraReward_ ;
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @return Whether the extraReward field is set.
       */
      @java.lang.Override
      public boolean hasExtraReward() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @return The extraReward.
       */
      @java.lang.Override
      public boolean getExtraReward() {
        return extraReward_;
      }
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @param value The extraReward to set.
       * @return This builder for chaining.
       */
      public Builder setExtraReward(boolean value) {
        bitField0_ |= 0x00000004;
        extraReward_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraReward() {
        bitField0_ = (bitField0_ & ~0x00000004);
        extraReward_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainLevelReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainLevelReward_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainLevelReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainLevelReward_C2S>() {
      @java.lang.Override
      public Player_ObtainLevelReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainLevelReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainLevelReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainLevelReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainLevelReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainLevelReward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    boolean hasReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainLevelReward_S2C}
   */
  public static final class Player_ObtainLevelReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainLevelReward_S2C)
      Player_ObtainLevelReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainLevelReward_S2C.newBuilder() to construct.
    private Player_ObtainLevelReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainLevelReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainLevelReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainLevelReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = reward_.toBuilder();
              }
              reward_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(reward_);
                reward_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARD_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    @java.lang.Override
    public boolean hasReward() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getReward());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getReward());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C other = (com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C) obj;

      if (hasReward() != other.hasReward()) return false;
      if (hasReward()) {
        if (!getReward()
            .equals(other.getReward())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReward()) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getReward().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainLevelReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainLevelReward_S2C)
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardBuilder_ == null) {
          reward_ = null;
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C result = new com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardBuilder_ == null) {
            result.reward_ = reward_;
          } else {
            result.reward_ = rewardBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C.getDefaultInstance()) return this;
        if (other.hasReward()) {
          mergeReward(other.getReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardBuilder_;
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return Whether the reward field is set.
       */
      public boolean hasReward() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return The reward.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
        if (rewardBuilder_ == null) {
          return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        } else {
          return rewardBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          reward_ = value;
          onChanged();
        } else {
          rewardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          reward_ = builderForValue.build();
          onChanged();
        } else {
          rewardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder mergeReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              reward_ != null &&
              reward_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            reward_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(reward_).mergeFrom(value).buildPartial();
          } else {
            reward_ = value;
          }
          onChanged();
        } else {
          rewardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = null;
          onChanged();
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilder();
        } else {
          return reward_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getReward(),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainLevelReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainLevelReward_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainLevelReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainLevelReward_S2C>() {
      @java.lang.Override
      public Player_ObtainLevelReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainLevelReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainLevelReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainLevelReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainLevelReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PurchaseBpLevel_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PurchaseBpLevel_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return Whether the level field is set.
     */
    boolean hasLevel();
    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return The level.
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PurchaseBpLevel_C2S}
   */
  public static final class Player_PurchaseBpLevel_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PurchaseBpLevel_C2S)
      Player_PurchaseBpLevel_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PurchaseBpLevel_C2S.newBuilder() to construct.
    private Player_PurchaseBpLevel_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PurchaseBpLevel_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PurchaseBpLevel_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PurchaseBpLevel_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              level_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int LEVEL_FIELD_NUMBER = 1;
    private int level_;
    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, level_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S other = (com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S) obj;

      if (hasLevel() != other.hasLevel()) return false;
      if (hasLevel()) {
        if (getLevel()
            != other.getLevel()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLevel()) {
        hash = (37 * hash) + LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getLevel();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PurchaseBpLevel_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PurchaseBpLevel_C2S)
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S result = new com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.level_ = level_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S.getDefaultInstance()) return this;
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int level_ ;
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @return Whether the level field is set.
       */
      @java.lang.Override
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000001;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        level_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PurchaseBpLevel_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PurchaseBpLevel_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PurchaseBpLevel_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_PurchaseBpLevel_C2S>() {
      @java.lang.Override
      public Player_PurchaseBpLevel_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PurchaseBpLevel_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PurchaseBpLevel_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PurchaseBpLevel_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PurchaseBpLevel_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PurchaseBpLevel_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PurchaseBpLevel_S2C}
   */
  public static final class Player_PurchaseBpLevel_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PurchaseBpLevel_S2C)
      Player_PurchaseBpLevel_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PurchaseBpLevel_S2C.newBuilder() to construct.
    private Player_PurchaseBpLevel_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PurchaseBpLevel_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PurchaseBpLevel_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PurchaseBpLevel_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C other = (com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PurchaseBpLevel_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PurchaseBpLevel_S2C)
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C result = new com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PurchaseBpLevel_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PurchaseBpLevel_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PurchaseBpLevel_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_PurchaseBpLevel_S2C>() {
      @java.lang.Override
      public Player_PurchaseBpLevel_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PurchaseBpLevel_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PurchaseBpLevel_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PurchaseBpLevel_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_PurchaseBpLevel_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainSeasonTaskExp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainSeasonTaskExp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return Whether the bpTaskId field is set.
     */
    boolean hasBpTaskId();
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return The bpTaskId.
     */
    int getBpTaskId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonTaskExp_C2S}
   */
  public static final class Player_ObtainSeasonTaskExp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainSeasonTaskExp_C2S)
      Player_ObtainSeasonTaskExp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainSeasonTaskExp_C2S.newBuilder() to construct.
    private Player_ObtainSeasonTaskExp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainSeasonTaskExp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainSeasonTaskExp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainSeasonTaskExp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              bpTaskId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int BPTASKID_FIELD_NUMBER = 1;
    private int bpTaskId_;
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return Whether the bpTaskId field is set.
     */
    @java.lang.Override
    public boolean hasBpTaskId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 任务id
     * </pre>
     *
     * <code>optional int32 bpTaskId = 1;</code>
     * @return The bpTaskId.
     */
    @java.lang.Override
    public int getBpTaskId() {
      return bpTaskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, bpTaskId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, bpTaskId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S other = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S) obj;

      if (hasBpTaskId() != other.hasBpTaskId()) return false;
      if (hasBpTaskId()) {
        if (getBpTaskId()
            != other.getBpTaskId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBpTaskId()) {
        hash = (37 * hash) + BPTASKID_FIELD_NUMBER;
        hash = (53 * hash) + getBpTaskId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonTaskExp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainSeasonTaskExp_C2S)
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bpTaskId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S result = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.bpTaskId_ = bpTaskId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.getDefaultInstance()) return this;
        if (other.hasBpTaskId()) {
          setBpTaskId(other.getBpTaskId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int bpTaskId_ ;
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @return Whether the bpTaskId field is set.
       */
      @java.lang.Override
      public boolean hasBpTaskId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @return The bpTaskId.
       */
      @java.lang.Override
      public int getBpTaskId() {
        return bpTaskId_;
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @param value The bpTaskId to set.
       * @return This builder for chaining.
       */
      public Builder setBpTaskId(int value) {
        bitField0_ |= 0x00000001;
        bpTaskId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务id
       * </pre>
       *
       * <code>optional int32 bpTaskId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBpTaskId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        bpTaskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainSeasonTaskExp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainSeasonTaskExp_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainSeasonTaskExp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainSeasonTaskExp_C2S>() {
      @java.lang.Override
      public Player_ObtainSeasonTaskExp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainSeasonTaskExp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainSeasonTaskExp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainSeasonTaskExp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainSeasonTaskExp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainSeasonTaskExp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonTaskExp_S2C}
   */
  public static final class Player_ObtainSeasonTaskExp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainSeasonTaskExp_S2C)
      Player_ObtainSeasonTaskExp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainSeasonTaskExp_S2C.newBuilder() to construct.
    private Player_ObtainSeasonTaskExp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainSeasonTaskExp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainSeasonTaskExp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainSeasonTaskExp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C other = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonTaskExp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainSeasonTaskExp_S2C)
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C result = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainSeasonTaskExp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainSeasonTaskExp_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainSeasonTaskExp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainSeasonTaskExp_S2C>() {
      @java.lang.Override
      public Player_ObtainSeasonTaskExp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainSeasonTaskExp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainSeasonTaskExp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainSeasonTaskExp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainSeasonExpBox_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainSeasonExpBox_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonExpBox_C2S}
   */
  public static final class Player_ObtainSeasonExpBox_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainSeasonExpBox_C2S)
      Player_ObtainSeasonExpBox_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainSeasonExpBox_C2S.newBuilder() to construct.
    private Player_ObtainSeasonExpBox_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainSeasonExpBox_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainSeasonExpBox_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainSeasonExpBox_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S other = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonExpBox_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainSeasonExpBox_C2S)
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S result = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainSeasonExpBox_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainSeasonExpBox_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainSeasonExpBox_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainSeasonExpBox_C2S>() {
      @java.lang.Override
      public Player_ObtainSeasonExpBox_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainSeasonExpBox_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainSeasonExpBox_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainSeasonExpBox_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainSeasonExpBox_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainSeasonExpBox_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonExpBox_S2C}
   */
  public static final class Player_ObtainSeasonExpBox_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainSeasonExpBox_S2C)
      Player_ObtainSeasonExpBox_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainSeasonExpBox_S2C.newBuilder() to construct.
    private Player_ObtainSeasonExpBox_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainSeasonExpBox_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainSeasonExpBox_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainSeasonExpBox_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C other = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonExpBox_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainSeasonExpBox_S2C)
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C result = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainSeasonExpBox_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainSeasonExpBox_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainSeasonExpBox_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainSeasonExpBox_S2C>() {
      @java.lang.Override
      public Player_ObtainSeasonExpBox_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainSeasonExpBox_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainSeasonExpBox_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainSeasonExpBox_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonExpBox_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainSeasonLevelReward_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainSeasonLevelReward_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return Whether the isAll field is set.
     */
    boolean hasIsAll();
    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return The isAll.
     */
    boolean getIsAll();

    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return Whether the level field is set.
     */
    boolean hasLevel();
    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return Whether the extraReward field is set.
     */
    boolean hasExtraReward();
    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return The extraReward.
     */
    boolean getExtraReward();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonLevelReward_C2S}
   */
  public static final class Player_ObtainSeasonLevelReward_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainSeasonLevelReward_C2S)
      Player_ObtainSeasonLevelReward_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainSeasonLevelReward_C2S.newBuilder() to construct.
    private Player_ObtainSeasonLevelReward_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainSeasonLevelReward_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainSeasonLevelReward_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainSeasonLevelReward_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isAll_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              level_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              extraReward_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ISALL_FIELD_NUMBER = 1;
    private boolean isAll_;
    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return Whether the isAll field is set.
     */
    @java.lang.Override
    public boolean hasIsAll() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 是否全部领取
     * </pre>
     *
     * <code>optional bool isAll = 1;</code>
     * @return The isAll.
     */
    @java.lang.Override
    public boolean getIsAll() {
      return isAll_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 领取单个等级奖励
     * </pre>
     *
     * <code>optional int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int EXTRAREWARD_FIELD_NUMBER = 3;
    private boolean extraReward_;
    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return Whether the extraReward field is set.
     */
    @java.lang.Override
    public boolean hasExtraReward() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 额外奖励(50级之后的黄金奖励)
     * </pre>
     *
     * <code>optional bool extraReward = 3;</code>
     * @return The extraReward.
     */
    @java.lang.Override
    public boolean getExtraReward() {
      return extraReward_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isAll_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, level_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBool(3, extraReward_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isAll_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, extraReward_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S other = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S) obj;

      if (hasIsAll() != other.hasIsAll()) return false;
      if (hasIsAll()) {
        if (getIsAll()
            != other.getIsAll()) return false;
      }
      if (hasLevel() != other.hasLevel()) return false;
      if (hasLevel()) {
        if (getLevel()
            != other.getLevel()) return false;
      }
      if (hasExtraReward() != other.hasExtraReward()) return false;
      if (hasExtraReward()) {
        if (getExtraReward()
            != other.getExtraReward()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsAll()) {
        hash = (37 * hash) + ISALL_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsAll());
      }
      if (hasLevel()) {
        hash = (37 * hash) + LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getLevel();
      }
      if (hasExtraReward()) {
        hash = (37 * hash) + EXTRAREWARD_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getExtraReward());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonLevelReward_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainSeasonLevelReward_C2S)
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isAll_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        extraReward_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S result = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isAll_ = isAll_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.level_ = level_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.extraReward_ = extraReward_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.getDefaultInstance()) return this;
        if (other.hasIsAll()) {
          setIsAll(other.getIsAll());
        }
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        if (other.hasExtraReward()) {
          setExtraReward(other.getExtraReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isAll_ ;
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @return Whether the isAll field is set.
       */
      @java.lang.Override
      public boolean hasIsAll() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @return The isAll.
       */
      @java.lang.Override
      public boolean getIsAll() {
        return isAll_;
      }
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @param value The isAll to set.
       * @return This builder for chaining.
       */
      public Builder setIsAll(boolean value) {
        bitField0_ |= 0x00000001;
        isAll_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否全部领取
       * </pre>
       *
       * <code>optional bool isAll = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsAll() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isAll_ = false;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @return Whether the level field is set.
       */
      @java.lang.Override
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000002;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领取单个等级奖励
       * </pre>
       *
       * <code>optional int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        level_ = 0;
        onChanged();
        return this;
      }

      private boolean extraReward_ ;
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @return Whether the extraReward field is set.
       */
      @java.lang.Override
      public boolean hasExtraReward() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @return The extraReward.
       */
      @java.lang.Override
      public boolean getExtraReward() {
        return extraReward_;
      }
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @param value The extraReward to set.
       * @return This builder for chaining.
       */
      public Builder setExtraReward(boolean value) {
        bitField0_ |= 0x00000004;
        extraReward_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 额外奖励(50级之后的黄金奖励)
       * </pre>
       *
       * <code>optional bool extraReward = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraReward() {
        bitField0_ = (bitField0_ & ~0x00000004);
        extraReward_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainSeasonLevelReward_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainSeasonLevelReward_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainSeasonLevelReward_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainSeasonLevelReward_C2S>() {
      @java.lang.Override
      public Player_ObtainSeasonLevelReward_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainSeasonLevelReward_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainSeasonLevelReward_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainSeasonLevelReward_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ObtainSeasonLevelReward_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ObtainSeasonLevelReward_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    boolean hasReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    com.yorha.proto.StructPB.YoAssetPackagePB getReward();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonLevelReward_S2C}
   */
  public static final class Player_ObtainSeasonLevelReward_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ObtainSeasonLevelReward_S2C)
      Player_ObtainSeasonLevelReward_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ObtainSeasonLevelReward_S2C.newBuilder() to construct.
    private Player_ObtainSeasonLevelReward_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ObtainSeasonLevelReward_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ObtainSeasonLevelReward_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ObtainSeasonLevelReward_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPB.YoAssetPackagePB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = reward_.toBuilder();
              }
              reward_ = input.readMessage(com.yorha.proto.StructPB.YoAssetPackagePB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(reward_);
                reward_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int REWARD_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return Whether the reward field is set.
     */
    @java.lang.Override
    public boolean hasReward() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     * @return The reward.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
      return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getReward());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getReward());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C other = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C) obj;

      if (hasReward() != other.hasReward()) return false;
      if (hasReward()) {
        if (!getReward()
            .equals(other.getReward())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReward()) {
        hash = (37 * hash) + REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getReward().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ObtainSeasonLevelReward_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ObtainSeasonLevelReward_S2C)
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.class, com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardBuilder_ == null) {
          reward_ = null;
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C result = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (rewardBuilder_ == null) {
            result.reward_ = reward_;
          } else {
            result.reward_ = rewardBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.getDefaultInstance()) return this;
        if (other.hasReward()) {
          mergeReward(other.getReward());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPB.YoAssetPackagePB reward_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> rewardBuilder_;
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return Whether the reward field is set.
       */
      public boolean hasReward() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       * @return The reward.
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB getReward() {
        if (rewardBuilder_ == null) {
          return reward_ == null ? com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        } else {
          return rewardBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          reward_ = value;
          onChanged();
        } else {
          rewardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder setReward(
          com.yorha.proto.StructPB.YoAssetPackagePB.Builder builderForValue) {
        if (rewardBuilder_ == null) {
          reward_ = builderForValue.build();
          onChanged();
        } else {
          rewardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder mergeReward(com.yorha.proto.StructPB.YoAssetPackagePB value) {
        if (rewardBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              reward_ != null &&
              reward_ != com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance()) {
            reward_ =
              com.yorha.proto.StructPB.YoAssetPackagePB.newBuilder(reward_).mergeFrom(value).buildPartial();
          } else {
            reward_ = value;
          }
          onChanged();
        } else {
          rewardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public Builder clearReward() {
        if (rewardBuilder_ == null) {
          reward_ = null;
          onChanged();
        } else {
          rewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePB.Builder getRewardBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRewardFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      public com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder getRewardOrBuilder() {
        if (rewardBuilder_ != null) {
          return rewardBuilder_.getMessageOrBuilder();
        } else {
          return reward_ == null ?
              com.yorha.proto.StructPB.YoAssetPackagePB.getDefaultInstance() : reward_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackagePB reward = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder> 
          getRewardFieldBuilder() {
        if (rewardBuilder_ == null) {
          rewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.YoAssetPackagePB, com.yorha.proto.StructPB.YoAssetPackagePB.Builder, com.yorha.proto.StructPB.YoAssetPackagePBOrBuilder>(
                  getReward(),
                  getParentForChildren(),
                  isClean());
          reward_ = null;
        }
        return rewardBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ObtainSeasonLevelReward_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ObtainSeasonLevelReward_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ObtainSeasonLevelReward_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ObtainSeasonLevelReward_S2C>() {
      @java.lang.Override
      public Player_ObtainSeasonLevelReward_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ObtainSeasonLevelReward_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ObtainSeasonLevelReward_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ObtainSeasonLevelReward_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PurchaseSeasonBpLevel_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PurchaseSeasonBpLevel_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return Whether the level field is set.
     */
    boolean hasLevel();
    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return The level.
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PurchaseSeasonBpLevel_C2S}
   */
  public static final class Player_PurchaseSeasonBpLevel_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PurchaseSeasonBpLevel_C2S)
      Player_PurchaseSeasonBpLevel_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PurchaseSeasonBpLevel_C2S.newBuilder() to construct.
    private Player_PurchaseSeasonBpLevel_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PurchaseSeasonBpLevel_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PurchaseSeasonBpLevel_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PurchaseSeasonBpLevel_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              level_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int LEVEL_FIELD_NUMBER = 1;
    private int level_;
    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return Whether the level field is set.
     */
    @java.lang.Override
    public boolean hasLevel() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 购买多少等级
     * </pre>
     *
     * <code>optional int32 level = 1;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, level_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S other = (com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S) obj;

      if (hasLevel() != other.hasLevel()) return false;
      if (hasLevel()) {
        if (getLevel()
            != other.getLevel()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLevel()) {
        hash = (37 * hash) + LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getLevel();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PurchaseSeasonBpLevel_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PurchaseSeasonBpLevel_C2S)
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        level_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S build() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S result = new com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.level_ = level_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.getDefaultInstance()) return this;
        if (other.hasLevel()) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int level_ ;
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @return Whether the level field is set.
       */
      @java.lang.Override
      public boolean hasLevel() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        bitField0_ |= 0x00000001;
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 购买多少等级
       * </pre>
       *
       * <code>optional int32 level = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        level_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PurchaseSeasonBpLevel_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PurchaseSeasonBpLevel_C2S)
    private static final com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PurchaseSeasonBpLevel_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_PurchaseSeasonBpLevel_C2S>() {
      @java.lang.Override
      public Player_PurchaseSeasonBpLevel_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PurchaseSeasonBpLevel_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PurchaseSeasonBpLevel_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PurchaseSeasonBpLevel_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_PurchaseSeasonBpLevel_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_PurchaseSeasonBpLevel_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_PurchaseSeasonBpLevel_S2C}
   */
  public static final class Player_PurchaseSeasonBpLevel_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_PurchaseSeasonBpLevel_S2C)
      Player_PurchaseSeasonBpLevel_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_PurchaseSeasonBpLevel_S2C.newBuilder() to construct.
    private Player_PurchaseSeasonBpLevel_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_PurchaseSeasonBpLevel_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_PurchaseSeasonBpLevel_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_PurchaseSeasonBpLevel_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C other = (com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_PurchaseSeasonBpLevel_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_PurchaseSeasonBpLevel_S2C)
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.class, com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerBattlepass.internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C build() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C buildPartial() {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C result = new com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C) {
          return mergeFrom((com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C other) {
        if (other == com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_PurchaseSeasonBpLevel_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_PurchaseSeasonBpLevel_S2C)
    private static final com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C();
    }

    public static com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_PurchaseSeasonBpLevel_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_PurchaseSeasonBpLevel_S2C>() {
      @java.lang.Override
      public Player_PurchaseSeasonBpLevel_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_PurchaseSeasonBpLevel_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_PurchaseSeasonBpLevel_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_PurchaseSeasonBpLevel_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/player/cs/player_battlepa" +
      "ss.proto\022\017com.yorha.proto\032\"cs_proto/gen/" +
      "common/structPB.proto\",\n\030Player_ObtainTa" +
      "skExp_C2S\022\020\n\010bpTaskId\030\001 \001(\005\"\032\n\030Player_Ob" +
      "tainTaskExp_S2C\"\031\n\027Player_ObtainExpBox_C" +
      "2S\"\031\n\027Player_ObtainExpBox_S2C\"Q\n\034Player_" +
      "ObtainLevelReward_C2S\022\r\n\005isAll\030\001 \001(\010\022\r\n\005" +
      "level\030\002 \001(\005\022\023\n\013extraReward\030\003 \001(\010\"Q\n\034Play" +
      "er_ObtainLevelReward_S2C\0221\n\006reward\030\001 \001(\013" +
      "2!.com.yorha.proto.YoAssetPackagePB\"+\n\032P" +
      "layer_PurchaseBpLevel_C2S\022\r\n\005level\030\001 \001(\005" +
      "\"\034\n\032Player_PurchaseBpLevel_S2C\"2\n\036Player" +
      "_ObtainSeasonTaskExp_C2S\022\020\n\010bpTaskId\030\001 \001" +
      "(\005\" \n\036Player_ObtainSeasonTaskExp_S2C\"\037\n\035" +
      "Player_ObtainSeasonExpBox_C2S\"\037\n\035Player_" +
      "ObtainSeasonExpBox_S2C\"W\n\"Player_ObtainS" +
      "easonLevelReward_C2S\022\r\n\005isAll\030\001 \001(\010\022\r\n\005l" +
      "evel\030\002 \001(\005\022\023\n\013extraReward\030\003 \001(\010\"W\n\"Playe" +
      "r_ObtainSeasonLevelReward_S2C\0221\n\006reward\030" +
      "\001 \001(\0132!.com.yorha.proto.YoAssetPackagePB" +
      "\"1\n Player_PurchaseSeasonBpLevel_C2S\022\r\n\005" +
      "level\030\001 \001(\005\"\"\n Player_PurchaseSeasonBpLe" +
      "vel_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainTaskExp_C2S_descriptor,
        new java.lang.String[] { "BpTaskId", });
    internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainTaskExp_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainExpBox_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainExpBox_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainLevelReward_C2S_descriptor,
        new java.lang.String[] { "IsAll", "Level", "ExtraReward", });
    internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainLevelReward_S2C_descriptor,
        new java.lang.String[] { "Reward", });
    internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PurchaseBpLevel_C2S_descriptor,
        new java.lang.String[] { "Level", });
    internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PurchaseBpLevel_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_C2S_descriptor,
        new java.lang.String[] { "BpTaskId", });
    internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainSeasonTaskExp_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainSeasonExpBox_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_C2S_descriptor,
        new java.lang.String[] { "IsAll", "Level", "ExtraReward", });
    internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ObtainSeasonLevelReward_S2C_descriptor,
        new java.lang.String[] { "Reward", });
    internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_C2S_descriptor,
        new java.lang.String[] { "Level", });
    internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_PurchaseSeasonBpLevel_S2C_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.StructPB.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

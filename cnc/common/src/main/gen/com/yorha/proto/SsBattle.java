// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/battle/ss_battle.proto

package com.yorha.proto;

public final class SsBattle {
  private SsBattle() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CheckBattleReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckBattleReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
     * @return Whether the battleInfo field is set.
     */
    boolean hasBattleInfo();
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
     * @return The battleInfo.
     */
    com.yorha.proto.RtsBattle.RTSBattleInfo getBattleInfo();
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
     */
    com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder getBattleInfoOrBuilder();

    /**
     * <pre>
     * 操作数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
     * @return Whether the battleRecord field is set.
     */
    boolean hasBattleRecord();
    /**
     * <pre>
     * 操作数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
     * @return The battleRecord.
     */
    com.yorha.proto.RtsBattle.RTSBattleRecord getBattleRecord();
    /**
     * <pre>
     * 操作数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
     */
    com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder getBattleRecordOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckBattleReq}
   */
  public static final class CheckBattleReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckBattleReq)
      CheckBattleReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckBattleReq.newBuilder() to construct.
    private CheckBattleReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckBattleReq() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckBattleReq();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckBattleReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.RtsBattle.RTSBattleInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = battleInfo_.toBuilder();
              }
              battleInfo_ = input.readMessage(com.yorha.proto.RtsBattle.RTSBattleInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battleInfo_);
                battleInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.yorha.proto.RtsBattle.RTSBattleRecord.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = battleRecord_.toBuilder();
              }
              battleRecord_ = input.readMessage(com.yorha.proto.RtsBattle.RTSBattleRecord.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battleRecord_);
                battleRecord_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsBattle.CheckBattleReq.class, com.yorha.proto.SsBattle.CheckBattleReq.Builder.class);
    }

    private int bitField0_;
    public static final int BATTLEINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.RtsBattle.RTSBattleInfo battleInfo_;
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
     * @return Whether the battleInfo field is set.
     */
    @java.lang.Override
    public boolean hasBattleInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
     * @return The battleInfo.
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleInfo getBattleInfo() {
      return battleInfo_ == null ? com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : battleInfo_;
    }
    /**
     * <pre>
     * 战斗信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder getBattleInfoOrBuilder() {
      return battleInfo_ == null ? com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : battleInfo_;
    }

    public static final int BATTLERECORD_FIELD_NUMBER = 2;
    private com.yorha.proto.RtsBattle.RTSBattleRecord battleRecord_;
    /**
     * <pre>
     * 操作数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
     * @return Whether the battleRecord field is set.
     */
    @java.lang.Override
    public boolean hasBattleRecord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 操作数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
     * @return The battleRecord.
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleRecord getBattleRecord() {
      return battleRecord_ == null ? com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : battleRecord_;
    }
    /**
     * <pre>
     * 操作数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder getBattleRecordOrBuilder() {
      return battleRecord_ == null ? com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : battleRecord_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getBattleInfo());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getBattleRecord());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getBattleInfo());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getBattleRecord());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsBattle.CheckBattleReq)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsBattle.CheckBattleReq other = (com.yorha.proto.SsBattle.CheckBattleReq) obj;

      if (hasBattleInfo() != other.hasBattleInfo()) return false;
      if (hasBattleInfo()) {
        if (!getBattleInfo()
            .equals(other.getBattleInfo())) return false;
      }
      if (hasBattleRecord() != other.hasBattleRecord()) return false;
      if (hasBattleRecord()) {
        if (!getBattleRecord()
            .equals(other.getBattleRecord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBattleInfo()) {
        hash = (37 * hash) + BATTLEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getBattleInfo().hashCode();
      }
      if (hasBattleRecord()) {
        hash = (37 * hash) + BATTLERECORD_FIELD_NUMBER;
        hash = (53 * hash) + getBattleRecord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsBattle.CheckBattleReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsBattle.CheckBattleReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckBattleReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckBattleReq)
        com.yorha.proto.SsBattle.CheckBattleReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsBattle.CheckBattleReq.class, com.yorha.proto.SsBattle.CheckBattleReq.Builder.class);
      }

      // Construct using com.yorha.proto.SsBattle.CheckBattleReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getBattleInfoFieldBuilder();
          getBattleRecordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (battleInfoBuilder_ == null) {
          battleInfo_ = null;
        } else {
          battleInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (battleRecordBuilder_ == null) {
          battleRecord_ = null;
        } else {
          battleRecordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleReq_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsBattle.CheckBattleReq getDefaultInstanceForType() {
        return com.yorha.proto.SsBattle.CheckBattleReq.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsBattle.CheckBattleReq build() {
        com.yorha.proto.SsBattle.CheckBattleReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsBattle.CheckBattleReq buildPartial() {
        com.yorha.proto.SsBattle.CheckBattleReq result = new com.yorha.proto.SsBattle.CheckBattleReq(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (battleInfoBuilder_ == null) {
            result.battleInfo_ = battleInfo_;
          } else {
            result.battleInfo_ = battleInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (battleRecordBuilder_ == null) {
            result.battleRecord_ = battleRecord_;
          } else {
            result.battleRecord_ = battleRecordBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsBattle.CheckBattleReq) {
          return mergeFrom((com.yorha.proto.SsBattle.CheckBattleReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsBattle.CheckBattleReq other) {
        if (other == com.yorha.proto.SsBattle.CheckBattleReq.getDefaultInstance()) return this;
        if (other.hasBattleInfo()) {
          mergeBattleInfo(other.getBattleInfo());
        }
        if (other.hasBattleRecord()) {
          mergeBattleRecord(other.getBattleRecord());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsBattle.CheckBattleReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsBattle.CheckBattleReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.RtsBattle.RTSBattleInfo battleInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleInfo, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder, com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder> battleInfoBuilder_;
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       * @return Whether the battleInfo field is set.
       */
      public boolean hasBattleInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       * @return The battleInfo.
       */
      public com.yorha.proto.RtsBattle.RTSBattleInfo getBattleInfo() {
        if (battleInfoBuilder_ == null) {
          return battleInfo_ == null ? com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : battleInfo_;
        } else {
          return battleInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       */
      public Builder setBattleInfo(com.yorha.proto.RtsBattle.RTSBattleInfo value) {
        if (battleInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battleInfo_ = value;
          onChanged();
        } else {
          battleInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       */
      public Builder setBattleInfo(
          com.yorha.proto.RtsBattle.RTSBattleInfo.Builder builderForValue) {
        if (battleInfoBuilder_ == null) {
          battleInfo_ = builderForValue.build();
          onChanged();
        } else {
          battleInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       */
      public Builder mergeBattleInfo(com.yorha.proto.RtsBattle.RTSBattleInfo value) {
        if (battleInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              battleInfo_ != null &&
              battleInfo_ != com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance()) {
            battleInfo_ =
              com.yorha.proto.RtsBattle.RTSBattleInfo.newBuilder(battleInfo_).mergeFrom(value).buildPartial();
          } else {
            battleInfo_ = value;
          }
          onChanged();
        } else {
          battleInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       */
      public Builder clearBattleInfo() {
        if (battleInfoBuilder_ == null) {
          battleInfo_ = null;
          onChanged();
        } else {
          battleInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleInfo.Builder getBattleInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getBattleInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder getBattleInfoOrBuilder() {
        if (battleInfoBuilder_ != null) {
          return battleInfoBuilder_.getMessageOrBuilder();
        } else {
          return battleInfo_ == null ?
              com.yorha.proto.RtsBattle.RTSBattleInfo.getDefaultInstance() : battleInfo_;
        }
      }
      /**
       * <pre>
       * 战斗信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleInfo battleInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleInfo, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder, com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder> 
          getBattleInfoFieldBuilder() {
        if (battleInfoBuilder_ == null) {
          battleInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSBattleInfo, com.yorha.proto.RtsBattle.RTSBattleInfo.Builder, com.yorha.proto.RtsBattle.RTSBattleInfoOrBuilder>(
                  getBattleInfo(),
                  getParentForChildren(),
                  isClean());
          battleInfo_ = null;
        }
        return battleInfoBuilder_;
      }

      private com.yorha.proto.RtsBattle.RTSBattleRecord battleRecord_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleRecord, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder, com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder> battleRecordBuilder_;
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       * @return Whether the battleRecord field is set.
       */
      public boolean hasBattleRecord() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       * @return The battleRecord.
       */
      public com.yorha.proto.RtsBattle.RTSBattleRecord getBattleRecord() {
        if (battleRecordBuilder_ == null) {
          return battleRecord_ == null ? com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : battleRecord_;
        } else {
          return battleRecordBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       */
      public Builder setBattleRecord(com.yorha.proto.RtsBattle.RTSBattleRecord value) {
        if (battleRecordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battleRecord_ = value;
          onChanged();
        } else {
          battleRecordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       */
      public Builder setBattleRecord(
          com.yorha.proto.RtsBattle.RTSBattleRecord.Builder builderForValue) {
        if (battleRecordBuilder_ == null) {
          battleRecord_ = builderForValue.build();
          onChanged();
        } else {
          battleRecordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       */
      public Builder mergeBattleRecord(com.yorha.proto.RtsBattle.RTSBattleRecord value) {
        if (battleRecordBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              battleRecord_ != null &&
              battleRecord_ != com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance()) {
            battleRecord_ =
              com.yorha.proto.RtsBattle.RTSBattleRecord.newBuilder(battleRecord_).mergeFrom(value).buildPartial();
          } else {
            battleRecord_ = value;
          }
          onChanged();
        } else {
          battleRecordBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       */
      public Builder clearBattleRecord() {
        if (battleRecordBuilder_ == null) {
          battleRecord_ = null;
          onChanged();
        } else {
          battleRecordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleRecord.Builder getBattleRecordBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getBattleRecordFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       */
      public com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder getBattleRecordOrBuilder() {
        if (battleRecordBuilder_ != null) {
          return battleRecordBuilder_.getMessageOrBuilder();
        } else {
          return battleRecord_ == null ?
              com.yorha.proto.RtsBattle.RTSBattleRecord.getDefaultInstance() : battleRecord_;
        }
      }
      /**
       * <pre>
       * 操作数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.RTSBattleRecord battleRecord = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.RtsBattle.RTSBattleRecord, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder, com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder> 
          getBattleRecordFieldBuilder() {
        if (battleRecordBuilder_ == null) {
          battleRecordBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.RtsBattle.RTSBattleRecord, com.yorha.proto.RtsBattle.RTSBattleRecord.Builder, com.yorha.proto.RtsBattle.RTSBattleRecordOrBuilder>(
                  getBattleRecord(),
                  getParentForChildren(),
                  isClean());
          battleRecord_ = null;
        }
        return battleRecordBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckBattleReq)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckBattleReq)
    private static final com.yorha.proto.SsBattle.CheckBattleReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsBattle.CheckBattleReq();
    }

    public static com.yorha.proto.SsBattle.CheckBattleReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckBattleReq>
        PARSER = new com.google.protobuf.AbstractParser<CheckBattleReq>() {
      @java.lang.Override
      public CheckBattleReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckBattleReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckBattleReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckBattleReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsBattle.CheckBattleReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckBattleRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckBattleResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 结果
     * </pre>
     *
     * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
     * @return Whether the result field is set.
     */
    boolean hasResult();
    /**
     * <pre>
     * 结果
     * </pre>
     *
     * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
     * @return The result.
     */
    com.yorha.proto.CommonMsg.MissionMapResultInfo getResult();
    /**
     * <pre>
     * 结果
     * </pre>
     *
     * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
     */
    com.yorha.proto.CommonMsg.MissionMapResultInfoOrBuilder getResultOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckBattleResp}
   */
  public static final class CheckBattleResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckBattleResp)
      CheckBattleRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckBattleResp.newBuilder() to construct.
    private CheckBattleResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckBattleResp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckBattleResp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckBattleResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.MissionMapResultInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = result_.toBuilder();
              }
              result_ = input.readMessage(com.yorha.proto.CommonMsg.MissionMapResultInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(result_);
                result_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsBattle.CheckBattleResp.class, com.yorha.proto.SsBattle.CheckBattleResp.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.MissionMapResultInfo result_;
    /**
     * <pre>
     * 结果
     * </pre>
     *
     * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
     * @return Whether the result field is set.
     */
    @java.lang.Override
    public boolean hasResult() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 结果
     * </pre>
     *
     * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.MissionMapResultInfo getResult() {
      return result_ == null ? com.yorha.proto.CommonMsg.MissionMapResultInfo.getDefaultInstance() : result_;
    }
    /**
     * <pre>
     * 结果
     * </pre>
     *
     * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.MissionMapResultInfoOrBuilder getResultOrBuilder() {
      return result_ == null ? com.yorha.proto.CommonMsg.MissionMapResultInfo.getDefaultInstance() : result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getResult());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getResult());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsBattle.CheckBattleResp)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsBattle.CheckBattleResp other = (com.yorha.proto.SsBattle.CheckBattleResp) obj;

      if (hasResult() != other.hasResult()) return false;
      if (hasResult()) {
        if (!getResult()
            .equals(other.getResult())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsBattle.CheckBattleResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsBattle.CheckBattleResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckBattleResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckBattleResp)
        com.yorha.proto.SsBattle.CheckBattleRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleResp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsBattle.CheckBattleResp.class, com.yorha.proto.SsBattle.CheckBattleResp.Builder.class);
      }

      // Construct using com.yorha.proto.SsBattle.CheckBattleResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getResultFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (resultBuilder_ == null) {
          result_ = null;
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsBattle.internal_static_com_yorha_proto_CheckBattleResp_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsBattle.CheckBattleResp getDefaultInstanceForType() {
        return com.yorha.proto.SsBattle.CheckBattleResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsBattle.CheckBattleResp build() {
        com.yorha.proto.SsBattle.CheckBattleResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsBattle.CheckBattleResp buildPartial() {
        com.yorha.proto.SsBattle.CheckBattleResp result = new com.yorha.proto.SsBattle.CheckBattleResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (resultBuilder_ == null) {
            result.result_ = result_;
          } else {
            result.result_ = resultBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsBattle.CheckBattleResp) {
          return mergeFrom((com.yorha.proto.SsBattle.CheckBattleResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsBattle.CheckBattleResp other) {
        if (other == com.yorha.proto.SsBattle.CheckBattleResp.getDefaultInstance()) return this;
        if (other.hasResult()) {
          mergeResult(other.getResult());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsBattle.CheckBattleResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsBattle.CheckBattleResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.MissionMapResultInfo result_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.MissionMapResultInfo, com.yorha.proto.CommonMsg.MissionMapResultInfo.Builder, com.yorha.proto.CommonMsg.MissionMapResultInfoOrBuilder> resultBuilder_;
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       * @return Whether the result field is set.
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       * @return The result.
       */
      public com.yorha.proto.CommonMsg.MissionMapResultInfo getResult() {
        if (resultBuilder_ == null) {
          return result_ == null ? com.yorha.proto.CommonMsg.MissionMapResultInfo.getDefaultInstance() : result_;
        } else {
          return resultBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       */
      public Builder setResult(com.yorha.proto.CommonMsg.MissionMapResultInfo value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          result_ = value;
          onChanged();
        } else {
          resultBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       */
      public Builder setResult(
          com.yorha.proto.CommonMsg.MissionMapResultInfo.Builder builderForValue) {
        if (resultBuilder_ == null) {
          result_ = builderForValue.build();
          onChanged();
        } else {
          resultBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       */
      public Builder mergeResult(com.yorha.proto.CommonMsg.MissionMapResultInfo value) {
        if (resultBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              result_ != null &&
              result_ != com.yorha.proto.CommonMsg.MissionMapResultInfo.getDefaultInstance()) {
            result_ =
              com.yorha.proto.CommonMsg.MissionMapResultInfo.newBuilder(result_).mergeFrom(value).buildPartial();
          } else {
            result_ = value;
          }
          onChanged();
        } else {
          resultBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = null;
          onChanged();
        } else {
          resultBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       */
      public com.yorha.proto.CommonMsg.MissionMapResultInfo.Builder getResultBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getResultFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       */
      public com.yorha.proto.CommonMsg.MissionMapResultInfoOrBuilder getResultOrBuilder() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilder();
        } else {
          return result_ == null ?
              com.yorha.proto.CommonMsg.MissionMapResultInfo.getDefaultInstance() : result_;
        }
      }
      /**
       * <pre>
       * 结果
       * </pre>
       *
       * <code>optional .com.yorha.proto.MissionMapResultInfo result = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.MissionMapResultInfo, com.yorha.proto.CommonMsg.MissionMapResultInfo.Builder, com.yorha.proto.CommonMsg.MissionMapResultInfoOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.MissionMapResultInfo, com.yorha.proto.CommonMsg.MissionMapResultInfo.Builder, com.yorha.proto.CommonMsg.MissionMapResultInfoOrBuilder>(
                  getResult(),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckBattleResp)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckBattleResp)
    private static final com.yorha.proto.SsBattle.CheckBattleResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsBattle.CheckBattleResp();
    }

    public static com.yorha.proto.SsBattle.CheckBattleResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckBattleResp>
        PARSER = new com.google.protobuf.AbstractParser<CheckBattleResp>() {
      @java.lang.Override
      public CheckBattleResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckBattleResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckBattleResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckBattleResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsBattle.CheckBattleResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckBattleReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckBattleReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckBattleResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckBattleResp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n#ss_proto/gen/battle/ss_battle.proto\022\017c" +
      "om.yorha.proto\032$ss_proto/gen/common/comm" +
      "on_msg.proto\032$ss_proto/gen/common/rts_ba" +
      "ttle.proto\"|\n\016CheckBattleReq\0222\n\nbattleIn" +
      "fo\030\001 \001(\0132\036.com.yorha.proto.RTSBattleInfo" +
      "\0226\n\014battleRecord\030\002 \001(\0132 .com.yorha.proto" +
      ".RTSBattleRecord\"H\n\017CheckBattleResp\0225\n\006r" +
      "esult\030\001 \001(\0132%.com.yorha.proto.MissionMap" +
      "ResultInfoB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
          com.yorha.proto.RtsBattle.getDescriptor(),
        });
    internal_static_com_yorha_proto_CheckBattleReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_CheckBattleReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckBattleReq_descriptor,
        new java.lang.String[] { "BattleInfo", "BattleRecord", });
    internal_static_com_yorha_proto_CheckBattleResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_CheckBattleResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckBattleResp_descriptor,
        new java.lang.String[] { "Result", });
    com.yorha.proto.CommonMsg.getDescriptor();
    com.yorha.proto.RtsBattle.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
